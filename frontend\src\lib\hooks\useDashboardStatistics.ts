import { useQuery } from '@tanstack/react-query';
import { toast } from '@/lib/useToast';
import { QueryNames } from './QueryConstants';

export interface DashboardStatistics {
  totalApprovalRequests: number;
  pendingApprovals: number;
  rejectedRequests: number;
  scheduledVesselsToday: number;
  lastUpdated: string;
}

export const useDashboardStatistics = () => {
  return useQuery<DashboardStatistics, Error>({
    queryKey: [QueryNames.GetDashboardStatistics],
    queryFn: async (): Promise<DashboardStatistics> => {
      try {
        const response = await fetch('/api/dashboard/statistics', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return {
          totalApprovalRequests: data.totalApprovalRequests || 0,
          pendingApprovals: data.pendingApprovals || 0,
          rejectedRequests: data.rejectedRequests || 0,
          scheduledVesselsToday: data.scheduledVesselsToday || 0,
          lastUpdated: data.lastUpdated || new Date().toISOString(),
        };
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading dashboard statistics';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Dashboard Statistics API Error:', error);
        toast({
          title: 'Error loading dashboard statistics',
          description: message,
          variant: 'destructive',
        });
        
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};
