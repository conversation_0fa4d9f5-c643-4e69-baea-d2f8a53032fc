import { router } from '@inertiajs/react';

interface TokenStatus {
  has_valid_token: boolean;
  refresh_token_expired: boolean;
}

class OidcTokenManager {
  private refreshInterval: NodeJS.Timeout | null = null;
  private activityTimeout: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private lastRefreshAttempt = 0;
  private isRefreshing = false;

  constructor() {
    this.init();
  }

  private init() {
    if (this.isInitialized) return;

    console.log('Initializing OIDC Token Manager');

    // Start automatic refresh
    this.startAutomaticRefresh();

    // Setup activity listeners
    this.setupActivityListeners();

    // Initial token check
    this.checkTokenStatus().then(status => {
      if (status?.refresh_token_expired) {
        console.log('Refresh token expired, redirecting to login');
        this.redirectToLogin();
      }
    });

    this.isInitialized = true;
  }

  private startAutomaticRefresh() {
    // Clear existing interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    // Set up periodic refresh every 15 minutes (less aggressive)
    this.refreshInterval = setInterval(() => {
      this.refreshTokenSilently();
    }, 15 * 60 * 1000); // 15 minutes
  }

  private setupActivityListeners() {
    // Reduce the number of events that trigger refresh to avoid excessive requests
    const events = ['click', 'keypress'];

    const handleActivity = () => {
      // Debounce the activity with a longer delay
      if (this.activityTimeout) {
        clearTimeout(this.activityTimeout);
      }

      this.activityTimeout = setTimeout(() => {
        this.refreshTokenSilently();
      }, 30000); // Wait 30 seconds after last activity
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Handle visibility change - only refresh if page was hidden for more than 5 minutes
    let lastHiddenTime = 0;
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        lastHiddenTime = Date.now();
      } else if (lastHiddenTime > 0 && Date.now() - lastHiddenTime > 300000) {
        // Only refresh if page was hidden for more than 5 minutes
        this.refreshTokenSilently();
      }
    });
  }

  private async refreshTokenSilently(): Promise<boolean> {
    // Prevent multiple simultaneous refresh attempts
    if (this.isRefreshing) {
      console.log('Token refresh already in progress, skipping');
      return false;
    }

    // Rate limiting: don't refresh more than once per minute
    const now = Date.now();
    if (now - this.lastRefreshAttempt < 60000) {
      console.log('Token refresh rate limited, skipping');
      return false;
    }

    this.isRefreshing = true;
    this.lastRefreshAttempt = now;

    try {
      // First check if we actually need to refresh
      const status = await this.checkTokenStatus();
      if (status?.has_valid_token) {
        console.log('Token is still valid, no refresh needed');
        return true;
      }

      if (status?.refresh_token_expired) {
        console.log('Refresh token expired, redirecting to login');
        this.redirectToLogin();
        return false;
      }

      const response = await fetch('/api/token/refresh', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        console.log('Silent token refresh successful');
        return true;
      } else if (response.status === 401) {
        console.log('Token refresh failed, redirecting to login');
        this.redirectToLogin();
        return false;
      }

      return false;
    } catch (error) {
      console.error('Error during silent token refresh:', error);
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  private async checkTokenStatus(): Promise<TokenStatus | null> {
    try {
      const response = await fetch('/api/token/status', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        return await response.json();
      }

      return null;
    } catch (error) {
      console.error('Error checking token status:', error);
      return null;
    }
  }

  private redirectToLogin() {
    const currentUrl = encodeURIComponent(window.location.href);
    router.visit(`/Account/Login?returnUrl=${currentUrl}`);
  }

  // Public method for manual token refresh
  public async tryRefreshToken(): Promise<boolean> {
    return await this.refreshTokenSilently();
  }

  // Public method for enhanced fetch
  public async fetchWithTokenRefresh(url: string, options: RequestInit = {}): Promise<Response> {
    try {
      const response = await fetch(url, {
        ...options,
        credentials: 'include',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
          ...options.headers,
        },
      });

      if (response.status === 401) {
        const refreshSuccess = await this.refreshTokenSilently();
        if (refreshSuccess) {
          // Retry the request
          return await fetch(url, {
            ...options,
            credentials: 'include',
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              'Accept': 'application/json',
              ...options.headers,
            },
          });
        } else {
          this.redirectToLogin();
          throw new Error('Authentication required');
        }
      }

      return response;
    } catch (error) {
      console.error('Fetch error:', error);
      throw error;
    }
  }

  // Cleanup method
  public destroy() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    if (this.activityTimeout) {
      clearTimeout(this.activityTimeout);
    }
  }
}

// Create global instance
const oidcManager = new OidcTokenManager();

// Export for use in components
export { oidcManager };

// Make it available globally for debugging
declare global {
  interface Window {
    oidcManager: OidcTokenManager;
  }
}

window.oidcManager = oidcManager; 