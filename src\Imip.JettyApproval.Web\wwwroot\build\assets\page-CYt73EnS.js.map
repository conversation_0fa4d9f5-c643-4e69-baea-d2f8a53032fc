{"version": 3, "file": "page-CYt73EnS.js", "sources": ["../../../../../frontend/src/components/approval/approval-history-table.tsx", "../../../../../frontend/src/pages/approval/history/page.tsx"], "sourcesContent": ["import type { ApprovalStageDto } from '@/client';\r\nimport { postApiApprovalStagesFilterList } from '@/client/sdk.gen';\r\nimport { Button } from '@/components/ui/button';\r\nimport { DataGrid } from '@/components/ui/data-grid';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { router } from '@inertiajs/react';\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\nimport { ArrowUpRight } from 'lucide-react';\r\nimport React from \"react\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ApprovalHistoryTable: React.FC = () => {\r\n  const { toast } = useToast();\r\n  const { t } = useTranslation();\r\n\r\n  // Define columns with translations\r\n  const approvalHistoryColumns: ColumnDef<ApprovalStageDto>[] = [\r\n    {\r\n      accessorKey: \"vessel.vesselName\",\r\n      header: t('table.vesselName'),\r\n      cell: (info: { getValue: () => unknown }) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.voyage\",\r\n      header: t('table.voyage'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.vesselType\",\r\n      header: t('table.vesselType'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.vesselArrival\",\r\n      header: t('table.arrival'),\r\n      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.destinationPort\",\r\n      header: t('table.destinationPort'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: t('table.status'),\r\n      cell: (info: { getValue: () => unknown }) => {\r\n        const status = info.getValue() as string | number;\r\n        let statusText: string;\r\n        let variant: \"warning\" | \"success\" | \"destructive\" | \"secondary\" | \"primary\";\r\n\r\n        switch (status) {\r\n          case 0:\r\n            statusText = t('table.statusPending');\r\n            variant = \"warning\";\r\n            break;\r\n          case 1:\r\n            statusText = t('table.statusApproved');\r\n            variant = \"success\";\r\n            break;\r\n          case 2:\r\n            statusText = t('table.statusRejected');\r\n            variant = \"destructive\";\r\n            break;\r\n          case 3:\r\n            statusText = t('table.statusCancelled');\r\n            variant = \"secondary\";\r\n            break;\r\n          default:\r\n            statusText = t('table.statusUnknown');\r\n            variant = \"primary\";\r\n            break;\r\n        }\r\n\r\n        return <Badge variant={variant} size=\"sm\">{statusText}</Badge>;\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"requesterUserName\",\r\n      header: t('table.requester'),\r\n      cell: (info: { getValue: () => unknown }) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"approverUserName\",\r\n      header: t('table.approver'),\r\n      cell: (info: { getValue: () => unknown }) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"requestDate\",\r\n      header: t('table.requestDate'),\r\n      cell: (info: { getValue: () => unknown }) => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : \"-\",\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: t('table.actions'),\r\n      cell: ({ row }) => {\r\n        const vessel = row.original.vessel;\r\n        return (\r\n          <div className=\"flex space-x-1\">\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  onClick={() => {\r\n                    if (vessel && vessel.id) {\r\n                      if (vessel.vesselType === 'Import') {\r\n                        router.visit(`/import/edit/${vessel.id}`);\r\n                      } else if (vessel.vesselType === 'Export') {\r\n                        router.visit(`/export/edit/${vessel.id}`);\r\n                      } else {\r\n                        router.visit(`/local/edit/${vessel.id}`);\r\n                      }\r\n                    }\r\n                  }}\r\n                  aria-label=\"Details\"\r\n                >\r\n                  <ArrowUpRight className=\"w-5 h-5\" />\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>{t('table.viewDetails')}</TooltipContent>\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <DataGrid\r\n        columns={approvalHistoryColumns}\r\n        title={t('datagrid.approvalHistory')}\r\n        queryKey={[\"approval-history\"]}\r\n        manualSorting={true}\r\n        manualFiltering={true}\r\n        autoSizeColumns={true}\r\n        queryFn={async ({ pageIndex, pageSize }) => {\r\n          try {\r\n            const response = await postApiApprovalStagesFilterList({\r\n              body: {\r\n                maxResultCount: pageSize,\r\n                skipCount: pageIndex * pageSize,\r\n                // Add filterGroup or sorting as needed for history\r\n              }\r\n            });\r\n            if (!response || !response.data) {\r\n              throw new Error(\"Failed to fetch approval history\");\r\n            }\r\n            return {\r\n              items: response.data.items || [],\r\n              totalCount: response.data.totalCount || 0,\r\n            };\r\n          } catch (err) {\r\n            console.error(\"Error fetching approval history:\", err);\r\n            toast({\r\n              title: \"Error\",\r\n              description: \"Failed to load approval history\",\r\n              variant: \"destructive\",\r\n            });\r\n            return { items: [], totalCount: 0 };\r\n          }\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApprovalHistoryTable; ", "import ApprovalHistoryTable from \"@/components/approval/approval-history-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport React from \"react\";\r\n\r\nconst ApprovalListPage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <ApprovalHistoryTable />\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ApprovalListPage;"], "names": ["ApprovalHistoryTable", "toast", "useToast", "useTranslation", "approvalHistoryColumns", "info", "status", "statusText", "variant", "jsx", "Badge", "row", "vessel", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON>", "router", "ArrowUpRight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataGrid", "pageIndex", "pageSize", "response", "postApiApprovalStagesFilterList", "ApprovalListPage", "AppLayout"], "mappings": "8rBAaA,MAAMA,EAAiC,IAAM,CACrC,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAAE,CAAE,EAAIC,EAAe,EAGvBC,EAAwD,CAC5D,CACE,YAAa,oBACb,OAAQ,EAAE,kBAAkB,EAC5B,KAAOC,GAAsCA,EAAK,YAAc,GAClE,EACA,CACE,YAAa,gBACb,OAAQ,EAAE,cAAc,EACxB,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,oBACb,OAAQ,EAAE,kBAAkB,EAC5B,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,uBACb,OAAQ,EAAE,eAAe,EACzB,KAAMA,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,eAAA,EAAmB,GACzF,EACA,CACE,YAAa,yBACb,OAAQ,EAAE,uBAAuB,EACjC,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,SACb,OAAQ,EAAE,cAAc,EACxB,KAAOA,GAAsC,CACrC,MAAAC,EAASD,EAAK,SAAS,EACzB,IAAAE,EACAC,EAEJ,OAAQF,EAAQ,CACd,IAAK,GACHC,EAAa,EAAE,qBAAqB,EAC1BC,EAAA,UACV,MACF,IAAK,GACHD,EAAa,EAAE,sBAAsB,EAC3BC,EAAA,UACV,MACF,IAAK,GACHD,EAAa,EAAE,sBAAsB,EAC3BC,EAAA,cACV,MACF,IAAK,GACHD,EAAa,EAAE,uBAAuB,EAC5BC,EAAA,YACV,MACF,QACED,EAAa,EAAE,qBAAqB,EAC1BC,EAAA,UACV,KAAA,CAGJ,OAAQC,EAAA,IAAAC,EAAA,CAAM,QAAAF,EAAkB,KAAK,KAAM,SAAWD,EAAA,CAAA,CAE1D,EACA,CACE,YAAa,oBACb,OAAQ,EAAE,iBAAiB,EAC3B,KAAOF,GAAsCA,EAAK,YAAc,GAClE,EACA,CACE,YAAa,mBACb,OAAQ,EAAE,gBAAgB,EAC1B,KAAOA,GAAsCA,EAAK,YAAc,GAClE,EACA,CACE,YAAa,cACb,OAAQ,EAAE,mBAAmB,EAC7B,KAAOA,GAAsCA,EAAK,SAAS,EAAI,IAAI,KAAKA,EAAK,UAAoB,EAAE,qBAAuB,GAC5H,EACA,CACE,GAAI,UACJ,OAAQ,EAAE,eAAe,EACzB,KAAM,CAAC,CAAE,IAAAM,KAAU,CACX,MAAAC,EAASD,EAAI,SAAS,OAC5B,OACGF,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,gBAACI,EACC,CAAA,SAAA,CAACJ,EAAAA,IAAAK,EAAA,CAAe,QAAO,GACrB,SAAAL,EAAA,IAACM,EAAA,CACC,QAAQ,QACR,KAAK,OACL,QAAS,IAAM,CACTH,GAAUA,EAAO,KACfA,EAAO,aAAe,SACxBI,EAAO,MAAM,gBAAgBJ,EAAO,EAAE,EAAE,EAC/BA,EAAO,aAAe,SAC/BI,EAAO,MAAM,gBAAgBJ,EAAO,EAAE,EAAE,EAExCI,EAAO,MAAM,eAAeJ,EAAO,EAAE,EAAE,EAG7C,EACA,aAAW,UAEX,SAAAH,EAAAA,IAACQ,EAAa,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,EAEtC,EACCR,EAAAA,IAAAS,EAAA,CAAgB,SAAE,EAAA,mBAAmB,CAAE,CAAA,CAAA,CAAA,CAC1C,CACF,CAAA,CAAA,CAEJ,CAEJ,EAGE,OAAAT,EAAAA,IAAC,MAAI,CAAA,UAAU,qEACb,SAAAA,EAAA,IAACU,EAAA,CACC,QAASf,EACT,MAAO,EAAE,0BAA0B,EACnC,SAAU,CAAC,kBAAkB,EAC7B,cAAe,GACf,gBAAiB,GACjB,gBAAiB,GACjB,QAAS,MAAO,CAAE,UAAAgB,EAAW,SAAAC,KAAe,CACtC,GAAA,CACI,MAAAC,EAAW,MAAMC,EAAgC,CACrD,KAAM,CACJ,eAAgBF,EAChB,UAAWD,EAAYC,CAAA,CAEzB,CACD,EACD,GAAI,CAACC,GAAY,CAACA,EAAS,KACnB,MAAA,IAAI,MAAM,kCAAkC,EAE7C,MAAA,CACL,MAAOA,EAAS,KAAK,OAAS,CAAC,EAC/B,WAAYA,EAAS,KAAK,YAAc,CAC1C,OACY,CAEN,OAAArB,EAAA,CACJ,MAAO,QACP,YAAa,kCACb,QAAS,aAAA,CACV,EACM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACF,CAAA,EAEJ,CAEJ,ECnKMuB,EAA6B,IAE/Bf,EAAAA,IAACgB,GACC,SAAChB,EAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAA,EAAAA,IAACT,EAAqB,CAAA,CAAA,CAAA,CACxB,CACF,CAAA"}