import { ekbProxyService } from "@/services/ekbProxyService";
import { useExportColumns } from "@/components/jetty/custom-area-jetty.columns";
import { DataGrid } from "@/components/ui/data-grid";
import AppLayout from "@/layouts/app-layout";
import { buildApiPayloadVessel } from "@/lib/queryHelper/buildApiPayloadVessel";
import { Head, router } from "@inertiajs/react";
import React from "react";
import { useTranslation } from "react-i18next";
import { ContentCard } from "@/components/layout/content-card";


const DEFAULT_SORTING = [{ id: 'docNum', desc: true }];
const CustomAreaJettyContent: React.FC = () => {
  const { t } = useTranslation();
  const exportColumns = useExportColumns();
  return (
    <ContentCard>
      <DataGrid
        columns={exportColumns}
        title={t("datagrid.exportVessel")}
        queryKey={["export-vessel-list"]}
        manualSorting={true}
        manualFiltering={true}
        autoSizeColumns={true} // Enable auto-sizing columns
        onCreate={() => router.visit('/export/create')}
        queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {
          const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter, vesselType: 'export' });
          const res = await ekbProxyService.filterExportVessels(payload);
          return {
            items: res.data?.items ?? [],
            totalCount: res.data?.totalCount ?? 0,
          };
        }}
      />
    </ContentCard>
  );
};

const ExportVesselPage: React.FC = () => {
  return (
    <AppLayout>
      <Head title={`Export Vessel`} />
      <div className="flex flex-col space-y-4 p-4">
        <CustomAreaJettyContent />
      </div>
    </AppLayout>
  );
};

export default ExportVesselPage;