import React, { useState, useEffect, useRef } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import DatePickerReact from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import type { DateValue } from 'react-aria-components';

import { HotTable } from '@handsontable/react-wrapper';
import type { HotTableRef } from '@handsontable/react-wrapper';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-main.min.css';
import Handsontable from 'handsontable';

registerAllModules();

type ParameterType = 'text' | 'date' | 'select';

interface ReportParameter {
  name: string;
  label: string;
  type: ParameterType;
  options?: { value: string; label: string }[];
}

interface ReportDefinition {
  id: string;
  name: string;
  parameters: ReportParameter[];
  mockDataGenerator: (params: Record<string, string | DateValue | null>) => Handsontable.CellValue[][];
  columns: Handsontable.ColumnSettings[];
}

const mockReports: ReportDefinition[] = [
  {
    id: 'jetty_schedule_report',
    name: 'Jetty Schedule Report',
    parameters: [
      { name: 'startDate', label: 'Start Date', type: 'date' },
      { name: 'endDate', label: 'End Date', type: 'date' },
      {
        name: 'jetty', label: 'Jetty', type: 'select', options: [
          { value: 'all', label: 'All Jetties' },
          { value: 'jetty_a', label: 'Jetty A' },
          { value: 'jetty_b', label: 'Jetty B' },
          { value: 'jetty_c', label: 'Jetty C' }
        ]
      },
      {
        name: 'status', label: 'Status', type: 'select', options: [
          { value: 'all', label: 'All Status' },
          { value: 'scheduled', label: 'Scheduled' },
          { value: 'in_progress', label: 'In Progress' },
          { value: 'completed', label: 'Completed' }
        ]
      }
    ],
    mockDataGenerator: (params) => {
      console.log('Generating data for Jetty Schedule Report with params:', params);
      const data = [];
      for (let i = 0; i < 10; i++) {
        data.push([
          `Vessel_${i + 1}`,
          `2024-03-${10 + i}`,
          `2024-03-${12 + i}`,
          params.jetty === 'all' ? `Jetty ${String.fromCharCode(65 + (i % 3))}` : params.jetty,
          ['scheduled', 'in_progress', 'completed'][i % 3],
          `Cargo Type ${i % 3 + 1}`,
          `${Math.floor(Math.random() * 1000)} tons`
        ]);
      }
      return data;
    },
    columns: [
      { data: 0, title: 'Vessel Name' },
      { data: 1, title: 'Arrival Date' },
      { data: 2, title: 'Departure Date' },
      { data: 3, title: 'Jetty' },
      { data: 4, title: 'Status' },
      { data: 5, title: 'Cargo Type' },
      { data: 6, title: 'Cargo Volume' }
    ],
  },
  {
    id: 'bounded_zone_report',
    name: 'Bounded Zone Report',
    parameters: [
      { name: 'date', label: 'Date', type: 'date' },
      {
        name: 'zone', label: 'Zone', type: 'select', options: [
          { value: 'all', label: 'All Zones' },
          { value: 'zone_a', label: 'Zone A' },
          { value: 'zone_b', label: 'Zone B' },
          { value: 'zone_c', label: 'Zone C' }
        ]
      },
      {
        name: 'activity', label: 'Activity Type', type: 'select', options: [
          { value: 'all', label: 'All Activities' },
          { value: 'loading', label: 'Loading' },
          { value: 'unloading', label: 'Unloading' },
          { value: 'storage', label: 'Storage' }
        ]
      }
    ],
    mockDataGenerator: (params) => {
      console.log('Generating data for Bounded Zone Report with params:', params);
      const data = [];
      for (let i = 0; i < 10; i++) {
        data.push([
          params.zone === 'all' ? `Zone ${String.fromCharCode(65 + (i % 3))}` : params.zone,
          ['loading', 'unloading', 'storage'][i % 3],
          `Cargo_${i + 1}`,
          `${Math.floor(Math.random() * 500)} tons`,
          `Vessel_${i + 1}`,
          `Operator_${i + 1}`,
          params.date ? params.date.toString() : '2024-03-15'
        ]);
      }
      return data;
    },
    columns: [
      { data: 0, title: 'Zone' },
      { data: 1, title: 'Activity Type' },
      { data: 2, title: 'Cargo ID' },
      { data: 3, title: 'Volume' },
      { data: 4, title: 'Vessel' },
      { data: 5, title: 'Operator' },
      { data: 6, title: 'Date' }
    ],
  }
];

const ReportPage: React.FC = () => {
  const [selectedReportId, setSelectedReportId] = useState<string>('');
  const [selectedReport, setSelectedReport] = useState<ReportDefinition | null>(null);
  const [parameterValues, setParameterValues] = useState<Record<string, string | DateValue | null>>({});
  const [reportData, setReportData] = useState<Handsontable.CellValue[][]>([]);
  const hotRef = useRef<HotTableRef>(null);

  useEffect(() => {
    if (selectedReportId) {
      const report = mockReports.find(r => r.id === selectedReportId);
      setSelectedReport(report || null);
      const initialParams: Record<string, string | DateValue | null> = {};
      report?.parameters.forEach(param => {
        if (param.type === 'date') {
          initialParams[param.name] = null;
        } else {
          initialParams[param.name] = '';
        }
      });
      setParameterValues(initialParams);
      setReportData([]); // Clear previous report data
    } else {
      setSelectedReport(null);
      setParameterValues({});
      setReportData([]);
    }
  }, [selectedReportId]);

  const handleParameterChange = (name: string, value: string | DateValue | null) => {
    setParameterValues(prev => ({ ...prev, [name]: value }));
  };

  const handleRunReport = () => {
    if (selectedReport) {
      const data = selectedReport.mockDataGenerator(parameterValues);
      setReportData(data);
      if (hotRef.current?.hotInstance) {
        hotRef.current.hotInstance.loadData(data);
      }
    }
  };

  return (
    <AppLayout>
      <div className="flex flex-col space-y-2 p-2">
        <Card>
          <CardHeader>
            <CardTitle>Report Selection and Parameters</CardTitle>
          </CardHeader>
          <CardContent className="p-3">
            <div className="mb-4">
              <Label htmlFor="report-select" className="text-sm mb-1">Select Report</Label>
              <Select onValueChange={setSelectedReportId} value={selectedReportId}>
                <SelectTrigger className="w-[240px]">
                  <SelectValue placeholder="Select a report" />
                </SelectTrigger>
                <SelectContent>
                  {mockReports.map(report => (
                    <SelectItem key={report.id} value={report.id}>
                      {report.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedReport && (
              <>
                <h4 className="text-lg font-semibold mb-3">{selectedReport.name} Parameters</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-2">
                  {selectedReport.parameters.map(param => (
                    <div key={param.name}>
                      <Label htmlFor={param.name} className="text-sm mb-1">{param.label}</Label>
                      {param.type === 'text' && (
                        <Input
                          id={param.name}
                          value={parameterValues[param.name] as string || ''}
                          onChange={(e) => handleParameterChange(param.name, e.target.value)}
                          className="w-[240px]"
                        />
                      )}
                      {param.type === 'date' && (
                        <DatePickerReact
                          value={parameterValues[param.name] as DateValue || null}
                          onChange={(date) => handleParameterChange(param.name, date)}

                        />
                      )}
                      {param.type === 'select' && (
                        <Select
                          value={parameterValues[param.name] as string || ''}
                          onValueChange={(value) => handleParameterChange(param.name, value)}
                        >
                          <SelectTrigger className="w-[240px]">
                            <SelectValue placeholder={`Select ${param.label}`} />
                          </SelectTrigger>
                          <SelectContent>
                            {param.options?.map(option => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                  ))}
                </div>
                <Button onClick={handleRunReport} className="mt-4">Run Report</Button>
              </>
            )}
          </CardContent>
        </Card>

        {reportData.length > 0 && selectedReport && (
          <Card>
            <CardHeader>
              <CardTitle>{selectedReport.name} Results</CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              <HotTable
                ref={hotRef}
                themeName="ht-theme-main"
                data={reportData}
                columns={selectedReport.columns}
                colHeaders={selectedReport.columns.map(col => col.title as string)}
                rowHeaders={true}
                height="auto"
                width="auto"
                stretchH="all"
                licenseKey="non-commercial-and-evaluation"
              />
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
};

export default ReportPage;
