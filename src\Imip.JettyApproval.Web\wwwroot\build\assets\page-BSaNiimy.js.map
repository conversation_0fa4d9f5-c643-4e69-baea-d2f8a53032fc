{"version": 3, "file": "page-BSaNiimy.js", "sources": ["../../../../../frontend/src/components/approval-template/approval-template-approvers-tab.tsx", "../../../../../frontend/src/components/approval-template/approval-template-criterias-tab.tsx", "../../../../../frontend/src/components/approval-template/approval-template-dialog.tsx", "../../../../../frontend/src/components/approval-template/approval-template-table.tsx", "../../../../../frontend/src/pages/master/approval-template/page.tsx"], "sourcesContent": ["import type { PagedResultDtoOfExtendedIdentityUserDto } from \"@/client/types.gen\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport type { MultiSelectOption } from \"@/components/ui/multi-select\";\r\nimport { MultiSelect } from \"@/components/ui/multi-select\";\r\nimport type { UseMutationResult } from \"@tanstack/react-query\";\r\nimport { Trash2 } from \"lucide-react\";\r\nimport React from \"react\";\r\nimport type { Control, FieldArrayWithId, UseFieldArrayAppend, UseFormSetValue, UseFormWatch } from \"react-hook-form\";\r\nimport type { FormValues } from \"./approval-template-dialog\";\r\n\r\ntype ApproversTabProps = {\r\n  fields: FieldArrayWithId<FormValues, \"approvers\", \"id\">[];\r\n  append: UseFieldArrayAppend<FormValues, \"approvers\">;\r\n  remove: (index: number) => void;\r\n  control: Control<FormValues>;\r\n  setValue: UseFormSetValue<FormValues>;\r\n  watch: UseFormWatch<FormValues>;\r\n  searchValue: string;\r\n  setSearchValue: (val: string) => void;\r\n  userOptions: MultiSelectOption[];\r\n  setUserOptions: (opts: MultiSelectOption[]) => void;\r\n  userSearchMutation: UseMutationResult<PagedResultDtoOfExtendedIdentityUserDto, Error, string>;\r\n};\r\n\r\nconst ApproversTab: React.FC<ApproversTabProps> = ({ fields, append, remove, control, setValue, watch, searchValue, setSearchValue, userOptions, setUserOptions, userSearchMutation }) => {\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center mb-2\">\r\n        <div className=\"font-semibold\">Approvers</div>\r\n        <Button type=\"button\" onClick={() => append({ approvalId: null, approverId: \"\", sequence: fields.length + 1, status: undefined, id: \"00000000-0000-0000-0000-000000000000\" })} size=\"sm\">\r\n          Add Approver\r\n        </Button>\r\n      </div>\r\n      <div className=\"space-y-2\">\r\n        {fields.length === 0 && <div className=\"text-muted-foreground text-sm\">No approvers added.</div>}\r\n        {fields.map((field, idx) => {\r\n          const currentApproverId = watch(`approvers.${idx}.approverId`);\r\n          const selectedOption = userOptions.find(opt => opt.value === currentApproverId);\r\n\r\n          // Create enhanced options that include selected user even if not in main options\r\n          const enhancedOptions = currentApproverId && !selectedOption\r\n            ? [{\r\n                value: currentApproverId,\r\n                label: `Loading user... (${currentApproverId.substring(0, 8)}...)`\r\n              }, ...userOptions]\r\n            : [...userOptions];\r\n\r\n\r\n\r\n          return (\r\n            <div key={field.id} className=\"flex flex-col md:flex-row items-center gap-2 border rounded p-2\">\r\n              <span className=\"font-semibold text-sm\">Approver #{idx + 1}</span>\r\n              <input type=\"hidden\" {...control.register(`approvers.${idx}.id`)} />\r\n              <input type=\"hidden\" {...control.register(`approvers.${idx}.approvalId`)} />\r\n              <div className=\"w-110\">\r\n                <MultiSelect\r\n                  options={enhancedOptions}\r\n                  value={currentApproverId ? [currentApproverId] : []}\r\n                  onChange={vals => setValue(`approvers.${idx}.approverId`, vals[0] ?? \"\", { shouldDirty: true })}\r\n                  placeholder=\"Select user...\"\r\n                  mode=\"single\"\r\n                  maxHeight={220}\r\n                  searchValue={searchValue}\r\n                onSearchValueChange={val => {\r\n                  setSearchValue(val);\r\n                  // Debounce search to avoid too many API calls\r\n                  if ((val.length >= 2 || val.length === 0) && !userSearchMutation.isPending) {\r\n                    userSearchMutation.mutate(val, {\r\n                      onSuccess: users => {\r\n                        const newOptions = (users.items ?? []).map(u => ({\r\n                          value: u.id ?? \"\",\r\n                          label: u.name || u.userName || u.email || \"(no name)\"\r\n                        }));\r\n\r\n                        // Simply replace with new options for now (selected users are loaded separately)\r\n                        setUserOptions(newOptions);\r\n                      }\r\n                    });\r\n                  }\r\n                }}\r\n              />\r\n            </div>\r\n            <Input\r\n              type=\"number\"\r\n              {...control.register(`approvers.${idx}.sequence`)}\r\n              placeholder=\"Sequence\"\r\n              className=\"w-24\"\r\n            />\r\n            {/* <Input\r\n              {...control.register(`approvers.${idx}.status`)}\r\n              placeholder=\"Status\"\r\n              className=\"w-24\"\r\n            /> */}\r\n            <Button type=\"button\" variant=\"ghost\" size=\"icon\" onClick={() => remove(idx)} aria-label=\"Remove Approver\">\r\n              <Trash2 className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n        );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApproversTab; ", "import { Button } from \"@/components/ui/button\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { Trash2 } from \"lucide-react\";\r\nimport React from \"react\";\r\nimport type { FieldArrayWithId, UseFieldArrayAppend } from \"react-hook-form\";\r\nimport { Controller, useFormContext } from \"react-hook-form\";\r\nimport type { FormValues } from \"./approval-template-dialog\";\r\n\r\ntype CriteriasTabProps = {\r\n  fields: FieldArrayWithId<FormValues, \"criterias\", \"id\">[];\r\n  append: UseFieldArrayAppend<FormValues, \"criterias\">;\r\n  remove: (index: number) => void;\r\n};\r\n\r\nconst DOCUMENT_TYPE_OPTIONS = [\r\n  { value: \"Import\", label: \"Import\" },\r\n  { value: \"Export\", label: \"Export\" },\r\n  { value: \"Local\", label: \"Local\" },\r\n];\r\n\r\nconst CriteriasTab: React.FC<CriteriasTabProps> = ({ fields, append, remove }) => {\r\n  const { control } = useFormContext();\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center mb-2\">\r\n        <div className=\"font-semibold\">Criterias</div>\r\n        <Button type=\"button\" onClick={() => append({ approvalId: null, documentType: undefined, id: \"00000000-0000-0000-0000-000000000000\" })} size=\"sm\">\r\n          Add Criteria\r\n        </Button>\r\n      </div>\r\n      <div className=\"space-y-2\">\r\n        {fields.length === 0 && <div className=\"text-muted-foreground text-sm\">No criterias added.</div>}\r\n        {fields.map((field, idx) => (\r\n          <div key={field.id} className=\"flex flex-col md:flex-row items-center gap-2 border rounded p-2\">\r\n            <span className=\"font-semibold text-sm\">Criteria #{idx + 1}</span>\r\n            <input type=\"hidden\" {...control.register(`criterias.${idx}.id`)} />\r\n            <input type=\"hidden\" {...control.register(`criterias.${idx}.approvalId`)} />\r\n            <Controller\r\n              name={`criterias.${idx}.documentType`}\r\n              control={control}\r\n              render={({ field }) => (\r\n                <Select value={field.value || \"\"} onValueChange={field.onChange}>\r\n                  <SelectTrigger className=\"w-100\">\r\n                    <SelectValue placeholder=\"Select type\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {DOCUMENT_TYPE_OPTIONS.map(opt => (\r\n                      <SelectItem key={opt.value} value={opt.value}>\r\n                        {opt.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              )}\r\n            />\r\n            <Button type=\"button\" variant=\"ghost\" size=\"icon\" onClick={() => remove(idx)} aria-label=\"Remove Criteria\">\r\n              <Trash2 className=\"w-4 h-4\" />\r\n            </Button>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CriteriasTab; ", "import { postApiIdentityServerUserQuery, postApiIdjasApprovalTemplate, putApiIdjasApprovalTemplateById } from \"@/client/sdk.gen\";\r\nimport type { ApprovalTemplateDto, CreateUpdateApprovalApproverDto, CreateUpdateApprovalCriteriaDto, CreateUpdateApprovalStageDto, FilterOperator, LogicalOperator, PagedResultDtoOfExtendedIdentityUserDto, RemoteServiceErrorResponse } from \"@/client/types.gen\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport type { MultiSelectOption } from \"@/components/ui/multi-select\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useToast } from \"@/lib/useToast\";\r\nimport { useMutation } from \"@tanstack/react-query\";\r\nimport React, { useEffect } from \"react\";\r\nimport { FormProvider, useFieldArray, useForm } from \"react-hook-form\";\r\nimport ApproversTab from \"./approval-template-approvers-tab\";\r\nimport CriteriasTab from \"./approval-template-criterias-tab\";\r\n\r\nexport interface ApprovalTemplateDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  template: ApprovalTemplateDto | null;\r\n}\r\n\r\nexport type FormValues = {\r\n  name: string;\r\n  description?: string;\r\n  code?: string;\r\n  approvers: CreateUpdateApprovalApproverDto[];\r\n  criterias: CreateUpdateApprovalCriteriaDto[];\r\n  stages: CreateUpdateApprovalStageDto[];\r\n};\r\n\r\nconst ApprovalTemplateDialog: React.FC<ApprovalTemplateDialogProps> = ({ open, onOpenChange, template }) => {\r\n  // Transform template data for editing\r\n  const transformTemplateForEdit = (template: ApprovalTemplateDto | null): FormValues => {\r\n    if (!template) {\r\n      return {\r\n        name: \"\",\r\n        description: \"\",\r\n        code: \"\",\r\n        approvers: [],\r\n        criterias: [],\r\n        stages: [],\r\n      };\r\n    }\r\n\r\n    // Transform ApprovalApproverDto[] to CreateUpdateApprovalApproverDto[]\r\n    const approvers: CreateUpdateApprovalApproverDto[] = (template.approvers || [])\r\n      .filter(approver => approver.approverId) // Only include approvers with valid approverId\r\n      .map(approver => ({\r\n        id: approver.id || null,\r\n        approvalId: approver.approvalId || null,\r\n        approverId: approver.approverId!, // Use non-null assertion since we filtered above\r\n        sequence: approver.sequence || 0,\r\n        status: approver.status || null,\r\n      }));\r\n\r\n    // Transform ApprovalCriteriaDto[] to CreateUpdateApprovalCriteriaDto[]\r\n    const criterias: CreateUpdateApprovalCriteriaDto[] = (template.criterias || []).map(criteria => ({\r\n      id: criteria.id || null,\r\n      approvalId: criteria.approvalId || null,\r\n      documentType: criteria.documentType || null,\r\n    }));\r\n\r\n    return {\r\n      name: template.name || \"\",\r\n      description: template.description || \"\",\r\n      code: template.code || \"\",\r\n      approvers,\r\n      criterias,\r\n      stages: [], // TODO: Transform stages when implemented\r\n    };\r\n  };\r\n\r\n  const methods = useForm<FormValues>({\r\n    defaultValues: transformTemplateForEdit(template),\r\n  });\r\n  const { register, handleSubmit, reset, control, formState: { errors, isDirty, isSubmitting }, setValue, watch } = methods;\r\n  const { toast } = useToast()\r\n\r\n  // Field arrays for tabs\r\n  const approversArray = useFieldArray({ control, name: \"approvers\" });\r\n  const criteriasArray = useFieldArray({ control, name: \"criterias\" });\r\n  // const stagesArray = useFieldArray({ control, name: \"stages\" });\r\n\r\n  // Reset form when dialog opens/closes or template changes\r\n  useEffect(() => {\r\n    const formData = transformTemplateForEdit(template);\r\n    reset(formData);\r\n\r\n    // Clear search state when dialog closes\r\n    if (!open) {\r\n      setSearchValue(\"\");\r\n      setUserOptions([]);\r\n      setInitialUsersLoaded(false);\r\n      loadingInitialUsers.current = false;\r\n      loadingSelectedUsers.current = false;\r\n      loadedTemplateId.current = null;\r\n    }\r\n  }, [template, open, reset]);\r\n\r\n  // Mutation for create\r\n  const createMutation = useMutation({\r\n    mutationFn: async (values: FormValues) => {\r\n      return postApiIdjasApprovalTemplate({ body: values });\r\n    },\r\n    onSuccess: () => {\r\n      onOpenChange(false);\r\n      reset();\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err.error?.message || 'Error',\r\n        description: err.error?.details || 'Failed to create approval template.',\r\n        variant: 'error',\r\n      })\r\n    }\r\n  });\r\n\r\n  // Mutation for update\r\n  const updateMutation = useMutation({\r\n    mutationFn: async (values: FormValues) => {\r\n      if (!template?.id) throw new Error(\"No template id\");\r\n      return putApiIdjasApprovalTemplateById({\r\n        path: { id: template.id },\r\n        body: values,\r\n      });\r\n    },\r\n    onSuccess: () => {\r\n      onOpenChange(false);\r\n      reset();\r\n    },\r\n  });\r\n\r\n  const onSubmit = (values: FormValues) => {\r\n    // Remove approvalId from approvers and criterias before submit\r\n    const cleanValues: FormValues = {\r\n      ...values,\r\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n      approvers: values.approvers.map(({ approvalId, ...rest }) => rest),\r\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n      criterias: values.criterias.map(({ approvalId, ...rest }) => rest),\r\n      stages: values.stages,\r\n    };\r\n    if (template?.id) {\r\n      updateMutation.mutate(cleanValues);\r\n    } else {\r\n      createMutation.mutate(cleanValues);\r\n    }\r\n  };\r\n\r\n  const isLoading = isSubmitting || createMutation.isPending || updateMutation.isPending;\r\n  const error = createMutation.error || updateMutation.error;\r\n\r\n  const [searchValue, setSearchValue] = React.useState(\"\");\r\n  const [userOptions, setUserOptions] = React.useState<MultiSelectOption[]>([]);\r\n  const [initialUsersLoaded, setInitialUsersLoaded] = React.useState(false);\r\n  const loadingInitialUsers = React.useRef(false);\r\n  const loadingSelectedUsers = React.useRef(false);\r\n  const loadedTemplateId = React.useRef<string | null>(null);\r\n  const userSearchMutation = useMutation<PagedResultDtoOfExtendedIdentityUserDto, Error, string>({\r\n    mutationFn: async (keyword: string) => {\r\n      const filterGroup = keyword\r\n        ? {\r\n            operator: \"Or\" as LogicalOperator,\r\n            conditions: [\r\n              { fieldName: \"name\", operator: \"Contains\" as FilterOperator, value: keyword },\r\n              { fieldName: \"userName\", operator: \"Contains\" as FilterOperator, value: keyword },\r\n              { fieldName: \"email\", operator: \"Contains\" as FilterOperator, value: keyword },\r\n            ],\r\n          }\r\n        : undefined;\r\n      const res = await postApiIdentityServerUserQuery({\r\n        body: {\r\n          maxResultCount: 20,\r\n          skipCount: 0,\r\n          filterGroup,\r\n        },\r\n      });\r\n      return res?.data ?? { items: [], totalCount: 0 };\r\n    },\r\n  });\r\n\r\n  // Load selected users when editing\r\n  const loadSelectedUsers = React.useCallback(async (approverIds: string[]) => {\r\n    if (approverIds.length === 0 || loadingSelectedUsers.current) return;\r\n\r\n    loadingSelectedUsers.current = true;\r\n    try {\r\n      const filterGroup = {\r\n        operator: \"Or\" as LogicalOperator,\r\n        conditions: approverIds.map(id => ({\r\n          fieldName: \"id\",\r\n          operator: \"Equal\" as FilterOperator,\r\n          value: id,\r\n        })),\r\n      };\r\n\r\n      const res = await postApiIdentityServerUserQuery({\r\n        body: {\r\n          maxResultCount: 100,\r\n          skipCount: 0,\r\n          filterGroup,\r\n        },\r\n      });\r\n\r\n      const response = res?.data ?? { items: [], totalCount: 0 };\r\n\r\n      const selectedUserOptions = (response.items ?? []).map(user => ({\r\n        value: user.id ?? \"\",\r\n        label: user.name || user.userName || user.email || \"(no name)\",\r\n      }));\r\n\r\n      setUserOptions(prev => {\r\n        // Merge with existing options, avoiding duplicates\r\n        const existingValues = new Set(prev.map(opt => opt.value));\r\n        const newOptions = selectedUserOptions.filter(opt => !existingValues.has(opt.value));\r\n        return [...prev, ...newOptions];\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to load selected users:\", error);\r\n    } finally {\r\n      loadingSelectedUsers.current = false;\r\n    }\r\n  }, []);\r\n\r\n  // Load selected users when template changes (for edit mode)\r\n  React.useEffect(() => {\r\n    if (open && template?.approvers && template.approvers.length > 0) {\r\n      // Only load if we haven't loaded this template's users yet\r\n      if (loadedTemplateId.current !== template.id) {\r\n        const approverIds = template.approvers\r\n          .map(approver => approver.approverId)\r\n          .filter(Boolean) as string[];\r\n\r\n        if (approverIds.length > 0) {\r\n          loadedTemplateId.current = template.id || null;\r\n          loadSelectedUsers(approverIds);\r\n        }\r\n      }\r\n    }\r\n  }, [open, template]); // Removed loadSelectedUsers from dependencies\r\n\r\n  // Load initial users on first open (for better UX)\r\n  React.useEffect(() => {\r\n    if (open && !initialUsersLoaded && userOptions.length === 0 && !loadingInitialUsers.current) {\r\n      loadingInitialUsers.current = true;\r\n      setInitialUsersLoaded(true);\r\n      userSearchMutation.mutate(\"\", {\r\n        onSuccess: users => {\r\n          setUserOptions((users.items ?? []).map(u => ({\r\n            value: u.id ?? \"\",\r\n            label: u.name || u.userName || u.email || \"(no name)\"\r\n          })));\r\n          loadingInitialUsers.current = false;\r\n        },\r\n        onError: () => {\r\n          setInitialUsersLoaded(false); // Reset on error to allow retry\r\n          loadingInitialUsers.current = false;\r\n        }\r\n      });\r\n    }\r\n  }, [open, initialUsersLoaded, userOptions.length]);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent size=\"2xl\">\r\n        <DialogHeader>\r\n          <DialogTitle>Approval Template</DialogTitle>\r\n          <DialogDescription>\r\n            Fill in the details for the approval template.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <FormProvider {...methods}>\r\n          <form onSubmit={handleSubmit(onSubmit)}>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\r\n              <div>\r\n                <label className=\"block text-sm font-medium mb-1\">Name <span className=\"text-destructive\">*</span></label>\r\n                <Input\r\n                  {...register(\"name\", { required: \"Name is required\" })}\r\n                  className=\"input input-bordered w-full\"\r\n                  autoFocus\r\n                  aria-invalid={!!errors.name}\r\n                />\r\n                {errors.name && <div className=\"text-xs text-destructive mt-1\">{errors.name.message}</div>}\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-sm font-medium mb-1\">Code</label>\r\n                <Input\r\n                  {...register(\"code\")}\r\n                  className=\"input input-bordered w-full\"\r\n                />\r\n              </div>\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"block text-sm font-medium mb-1\">Description</label>\r\n                <Textarea\r\n                  {...register(\"description\")}\r\n                  className=\"input input-bordered w-full min-h-[60px]\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <Tabs defaultValue=\"approvers\" className=\"w-full\">\r\n              <TabsList className=\"mb-2\">\r\n                <TabsTrigger value=\"approvers\">Approvers</TabsTrigger>\r\n                <TabsTrigger value=\"criterias\">Criterias</TabsTrigger>\r\n                {/* <TabsTrigger value=\"stages\">Stages</TabsTrigger> */}\r\n              </TabsList>\r\n              <TabsContent value=\"approvers\">\r\n                <ApproversTab\r\n                  {...approversArray}\r\n                  control={control}\r\n                  setValue={setValue}\r\n                  watch={watch}\r\n                  searchValue={searchValue}\r\n                  setSearchValue={setSearchValue}\r\n                  userOptions={userOptions}\r\n                  setUserOptions={setUserOptions}\r\n                  userSearchMutation={userSearchMutation}\r\n                />\r\n              </TabsContent>\r\n              <TabsContent value=\"criterias\">\r\n                <CriteriasTab {...criteriasArray} />\r\n              </TabsContent>\r\n              {/* <TabsContent value=\"stages\">\r\n                <StagesTab {...stagesArray} />\r\n              </TabsContent> */}\r\n            </Tabs>\r\n            {error && <div className=\"text-xs text-destructive mt-2\">{String(error instanceof Error ? error.message : error)}</div>}\r\n            <DialogFooter className=\"mt-6\">\r\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" className=\"ml-2\" disabled={isLoading || !isDirty}>\r\n                {isLoading ? \"Saving...\" : \"Save\"}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </FormProvider>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default ApprovalTemplateDialog; ", "import { postApiIdjasApprovalTemplateFilterList } from \"@/client/sdk.gen\";\r\nimport type { ApprovalTemplateDto, FilterCondition, FilterGroup, FilterOperator, LogicalOperator } from \"@/client/types.gen\";\r\nimport { DataTable } from \"@/components/data-table/DataTable\";\r\nimport FilterSortBar, { type SortDirection } from \"@/components/filter-sort-bar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport TableSkeleton from \"@/components/ui/table-skeleton\";\r\nimport { useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { type ColumnDef, type PaginationState } from \"@tanstack/react-table\";\r\nimport { Pencil, Plus, RefreshCw } from \"lucide-react\";\r\nimport React, { useMemo, useState } from \"react\";\r\nimport ApprovalTemplateDialog from \"./approval-template-dialog\";\r\n\r\nconst FILTER_FIELDS = [\r\n  { value: \"name\", label: \"Name\" },\r\n  { value: \"description\", label: \"Description\" },\r\n  { value: \"code\", label: \"Code\" },\r\n  // Add more fields as needed\r\n];\r\nconst FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [\r\n  { value: \"Equals\", label: \"Equals\" },\r\n  { value: \"Contains\", label: \"Contains\" },\r\n  { value: \"NotEquals\", label: \"Not Equals\" },\r\n  { value: \"GreaterThan\", label: \">\" },\r\n  { value: \"LessThan\", label: \"<\" },\r\n];\r\n\r\nconst ApprovalTemplateTable: React.FC = () => {\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  });\r\n  const [filters, setFilters] = useState<FilterCondition[]>([]);\r\n  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [editingTemplate, setEditingTemplate] = useState<ApprovalTemplateDto | null>(null);\r\n  const queryClient = useQueryClient();\r\n\r\n  // Reset to first page when filters or sorts change\r\n  React.useEffect(() => {\r\n    setPagination((prev) => ({ ...prev, pageIndex: 0 }));\r\n  }, [filters, sorts]);\r\n\r\n  // Build filter group for backend\r\n  const filterGroup: FilterGroup | undefined = useMemo(() => {\r\n    if (!filters.length) return undefined;\r\n    return {\r\n      operator: \"And\" as LogicalOperator,\r\n      conditions: filters,\r\n    };\r\n  }, [filters]);\r\n\r\n  // Build sorting string for backend\r\n  const sortingStr = useMemo(() => {\r\n    if (!sorts.length) return undefined;\r\n    return sorts.map(s => `${s.field} ${s.direction}`).join(\", \");\r\n  }, [sorts]);\r\n\r\n  // React Query for data\r\n  const {\r\n    data: queryData,\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n    isFetching,\r\n  } = useQuery({\r\n    queryKey: [\r\n      \"approval-templates\",\r\n      pagination.pageIndex,\r\n      pagination.pageSize,\r\n      sortingStr,\r\n      filterGroup,\r\n    ],\r\n    queryFn: async () => {\r\n      const res = await postApiIdjasApprovalTemplateFilterList({\r\n        body: {\r\n          page: pagination.pageIndex + 1,\r\n          maxResultCount: pagination.pageSize,\r\n          sorting: sortingStr,\r\n          filterGroup,\r\n        },\r\n      });\r\n      // Type guard for paged result\r\n      function isPagedResult(obj: unknown): obj is { items: ApprovalTemplateDto[]; totalCount: number } {\r\n        return !!obj && typeof obj === 'object' && 'items' in obj && 'totalCount' in obj;\r\n      }\r\n      const data = res?.data ?? res;\r\n      if (isPagedResult(data)) {\r\n        return {\r\n          items: data.items ?? [],\r\n          totalCount: data.totalCount ?? 0,\r\n        };\r\n      }\r\n      return { items: [], totalCount: 0 };\r\n    },\r\n  });\r\n\r\n  const data = queryData ?? { items: [], totalCount: 0 };\r\n\r\n  // Refresh data\r\n  const handleRefresh = async () => {\r\n    setIsRefreshing(true);\r\n    await queryClient.invalidateQueries({ queryKey: [\r\n      \"approval-templates\",\r\n      pagination.pageIndex,\r\n      pagination.pageSize,\r\n      sortingStr,\r\n      filterGroup,\r\n    ] });\r\n    setIsRefreshing(false);\r\n  };\r\n\r\n  // New Approval Template button\r\n  const handleNew = () => {\r\n    setEditingTemplate(null);\r\n    setDialogOpen(true);\r\n  };\r\n\r\n  // Edit button handler\r\n  const handleEdit = (template: ApprovalTemplateDto) => {\r\n    setEditingTemplate(template);\r\n    setDialogOpen(true);\r\n  };\r\n\r\n  // Table columns (must be inside component to access handleEdit)\r\n  const columns: ColumnDef<ApprovalTemplateDto>[] = [\r\n    {\r\n      accessorKey: \"name\",\r\n      header: \"Name\",\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"description\",\r\n      header: \"Description\",\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"code\",\r\n      header: \"Code\",\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      id: \"edit\",\r\n      header: \"\",\r\n      cell: ({ row }) => (\r\n        <Button\r\n          onClick={() => handleEdit(row.original)}\r\n          aria-label=\"Edit Approval Template\"\r\n          tabIndex={0}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"ml-2 h-8 w-8\"\r\n        >\r\n          <Pencil className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n        </Button>\r\n      ),\r\n      enableSorting: false,\r\n      enableColumnFilter: false,\r\n    },\r\n    // Add more columns as needed\r\n  ];\r\n\r\n  // Error UI\r\n  if (error) {\r\n    return (\r\n      <div className=\"bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center\">\r\n        <div className=\"mx-auto h-12 w-12 text-destructive mb-4\">!</div>\r\n        <h3 className=\"font-semibold text-destructive mb-2\">Error loading data</h3>\r\n        <p className=\"text-sm text-muted-foreground mb-4\">{String((error as Error).message)}</p>\r\n        <Button onClick={handleRefresh} variant=\"destructive\">Retry</Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <div className=\"text-xl font-bold px-2 pt-2 pb-1\">Approval Template List</div>\r\n      <FilterSortBar\r\n        filterFields={FILTER_FIELDS}\r\n        operators={FILTER_OPERATORS}\r\n        filters={filters}\r\n        sorts={sorts}\r\n        onFiltersChange={setFilters}\r\n        onSortsChange={setSorts}\r\n      >\r\n        <div className=\"ml-auto flex items-center gap-2\">\r\n          <Button\r\n            onClick={handleRefresh}\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"h-10 w-10\"\r\n            disabled={isLoading || isRefreshing || isFetching}\r\n          >\r\n            <RefreshCw className={`h-4 w-4 ${isRefreshing || isFetching ? 'animate-spin' : ''}`} />\r\n          </Button>\r\n          <Button\r\n            onClick={handleNew}\r\n          >\r\n            <Plus className=\"h-5 w-5\" /> New Approval Template\r\n          </Button>\r\n        </div>\r\n      </FilterSortBar>\r\n      {isLoading ? (\r\n        <TableSkeleton columns={columns} />\r\n      ) : (\r\n        <DataTable\r\n          title=\"\"\r\n          columns={columns}\r\n          data={data.items}\r\n          totalCount={data.totalCount}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={setPagination}\r\n          hideDefaultFilterbar={true}\r\n          enableRowSelection={false}\r\n          manualSorting={true}\r\n        />\r\n      )}\r\n      <ApprovalTemplateDialog\r\n        open={dialogOpen}\r\n        onOpenChange={(open) => {\r\n          setDialogOpen(open);\r\n          if (!open) refetch(); // Refresh data on close\r\n        }}\r\n        template={editingTemplate}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApprovalTemplateTable; ", "import ApprovalTemplateTable from \"@/components/approval-template/approval-template-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport React from \"react\";\r\n\r\nconst ApprovalTemplatePage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <ApprovalTemplateTable />\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ApprovalTemplatePage; "], "names": ["ApproversTab", "fields", "append", "remove", "control", "setValue", "watch", "searchValue", "setSearchValue", "userOptions", "setUserOptions", "userSearchMutation", "jsxs", "jsx", "<PERSON><PERSON>", "field", "idx", "currentApproverId", "selectedOption", "opt", "enhancedOptions", "MultiSelect", "vals", "val", "users", "newOptions", "u", "Input", "Trash2", "DOCUMENT_TYPE_OPTIONS", "CriteriasTab", "useFormContext", "Controller", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "ApprovalTemplateDialog", "open", "onOpenChange", "template", "transformTemplateForEdit", "approvers", "approver", "criterias", "criteria", "methods", "useForm", "register", "handleSubmit", "reset", "errors", "isDirty", "isSubmitting", "toast", "useToast", "approvers<PERSON><PERSON><PERSON>", "useFieldArray", "criteriasArray", "useEffect", "formData", "setInitialUsersLoaded", "loadingInitialUsers", "loadingSelectedUsers", "loadedTemplateId", "createMutation", "useMutation", "values", "postApiIdjasApprovalTemplate", "err", "updateMutation", "putApiIdjasApprovalTemplateById", "onSubmit", "cleanValues", "approvalId", "rest", "isLoading", "error", "React", "initialUsersLoaded", "keyword", "postApiIdentityServerUserQuery", "loadSelectedUsers", "approverIds", "filterGroup", "id", "selectedUserOptions", "user", "prev", "existingValues", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "FormProvider", "Textarea", "Tabs", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>er", "FILTER_FIELDS", "FILTER_OPERATORS", "ApprovalTemplateTable", "pagination", "setPagination", "useState", "filters", "setFilters", "sorts", "setSorts", "isRefreshing", "setIsRefreshing", "dialogOpen", "setDialogOpen", "editingTemplate", "setEditingTemplate", "queryClient", "useQueryClient", "useMemo", "sortingStr", "s", "queryData", "refetch", "isFetching", "useQuery", "res", "postApiIdjasApprovalTemplateFilterList", "isPagedResult", "obj", "data", "handleRefresh", "handleNew", "handleEdit", "columns", "info", "row", "Pencil", "FilterSortBar", "RefreshCw", "Plus", "TableSkeleton", "DataTable", "ApprovalTemplatePage", "AppLayout"], "mappings": "4sCAyBA,MAAMA,GAA4C,CAAC,CAAE,OAAAC,EAAQ,OAAAC,EAAQ,OAAAC,EAAQ,QAAAC,EAAS,SAAAC,EAAU,MAAAC,EAAO,YAAAC,EAAa,eAAAC,EAAgB,YAAAC,EAAa,eAAAC,EAAgB,mBAAAC,YAE5J,MACC,CAAA,SAAA,CAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAS,YAAA,EACxCA,EAAAA,IAACC,EAAO,CAAA,KAAK,SAAS,QAAS,IAAMZ,EAAO,CAAE,WAAY,KAAM,WAAY,GAAI,SAAUD,EAAO,OAAS,EAAG,OAAQ,OAAW,GAAI,sCAAwC,CAAA,EAAG,KAAK,KAAK,SAEzL,cAAA,CAAA,CAAA,EACF,EACAW,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAAX,EAAO,SAAW,GAAKY,MAAC,MAAI,CAAA,UAAU,gCAAgC,SAAmB,sBAAA,EACzFZ,EAAO,IAAI,CAACc,EAAOC,IAAQ,CAC1B,MAAMC,EAAoBX,EAAM,aAAaU,CAAG,aAAa,EACvDE,EAAiBT,EAAY,KAAYU,GAAAA,EAAI,QAAUF,CAAiB,EAGxEG,EAAkBH,GAAqB,CAACC,EAC1C,CAAC,CACC,MAAOD,EACP,MAAO,oBAAoBA,EAAkB,UAAU,EAAG,CAAC,CAAC,QAC3D,GAAGR,CAAW,EACjB,CAAC,GAAGA,CAAW,EAKjB,OAAAG,EAAA,KAAC,MAAmB,CAAA,UAAU,kEAC5B,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,wBAAwB,SAAA,CAAA,aAAWI,EAAM,CAAA,EAAE,EAC3DH,EAAAA,IAAC,QAAM,CAAA,KAAK,SAAU,GAAGT,EAAQ,SAAS,aAAaY,CAAG,KAAK,CAAG,CAAA,EAClEH,EAAAA,IAAC,QAAM,CAAA,KAAK,SAAU,GAAGT,EAAQ,SAAS,aAAaY,CAAG,aAAa,CAAG,CAAA,EAC1EH,EAAAA,IAAC,MAAI,CAAA,UAAU,QACb,SAAAA,EAAA,IAACQ,GAAA,CACC,QAASD,EACT,MAAOH,EAAoB,CAACA,CAAiB,EAAI,CAAC,EAClD,SAAUK,GAAQjB,EAAS,aAAaW,CAAG,cAAeM,EAAK,CAAC,GAAK,GAAI,CAAE,YAAa,GAAM,EAC9F,YAAY,iBACZ,KAAK,SACL,UAAW,IACX,YAAAf,EACF,oBAA4BgB,GAAA,CAC1Bf,EAAee,CAAG,GAEbA,EAAI,QAAU,GAAKA,EAAI,SAAW,IAAM,CAACZ,EAAmB,WAC/DA,EAAmB,OAAOY,EAAK,CAC7B,UAAoBC,GAAA,CAClB,MAAMC,GAAcD,EAAM,OAAS,CAAA,GAAI,IAAUE,IAAA,CAC/C,MAAOA,EAAE,IAAM,GACf,MAAOA,EAAE,MAAQA,EAAE,UAAYA,EAAE,OAAS,WAAA,EAC1C,EAGFhB,EAAee,CAAU,CAAA,CAC3B,CACD,CACH,CACF,CAAA,EAEJ,EACAZ,EAAA,IAACc,EAAA,CACC,KAAK,SACJ,GAAGvB,EAAQ,SAAS,aAAaY,CAAG,WAAW,EAChD,YAAY,WACZ,UAAU,MAAA,CACZ,QAMCF,EAAO,CAAA,KAAK,SAAS,QAAQ,QAAQ,KAAK,OAAO,QAAS,IAAMX,EAAOa,CAAG,EAAG,aAAW,kBACvF,eAACY,EAAO,CAAA,UAAU,UAAU,CAC9B,CAAA,CAAA,CAAA,EA7CUb,EAAM,EA8ClB,CAED,CAAA,CAAA,CACH,CAAA,CAAA,EACF,ECvFEc,GAAwB,CAC5B,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,QAAS,MAAO,OAAQ,CACnC,EAEMC,GAA4C,CAAC,CAAE,OAAA7B,EAAQ,OAAAC,EAAQ,OAAAC,KAAa,CAC1E,KAAA,CAAE,QAAAC,CAAQ,EAAI2B,GAAe,EAEnC,cACG,MACC,CAAA,SAAA,CAACnB,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gBAAgB,SAAS,YAAA,QACvCC,EAAO,CAAA,KAAK,SAAS,QAAS,IAAMZ,EAAO,CAAE,WAAY,KAAM,aAAc,OAAW,GAAI,sCAAwC,CAAA,EAAG,KAAK,KAAK,SAElJ,cAAA,CAAA,CAAA,EACF,EACAU,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CAAAX,EAAO,SAAW,GAAKY,MAAC,MAAI,CAAA,UAAU,gCAAgC,SAAmB,sBAAA,EACzFZ,EAAO,IAAI,CAACc,EAAOC,IACjBJ,OAAA,MAAA,CAAmB,UAAU,kEAC5B,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,wBAAwB,SAAA,CAAA,aAAWI,EAAM,CAAA,EAAE,EAC3DH,EAAAA,IAAC,QAAM,CAAA,KAAK,SAAU,GAAGT,EAAQ,SAAS,aAAaY,CAAG,KAAK,CAAG,CAAA,EAClEH,EAAAA,IAAC,QAAM,CAAA,KAAK,SAAU,GAAGT,EAAQ,SAAS,aAAaY,CAAG,aAAa,CAAG,CAAA,EAC1EH,EAAA,IAACmB,GAAA,CACC,KAAM,aAAahB,CAAG,gBACtB,QAAAZ,EACA,OAAQ,CAAC,CAAE,MAAAW,KACTH,EAAAA,KAACqB,GAAO,CAAA,MAAOlB,EAAM,OAAS,GAAI,cAAeA,EAAM,SACrD,SAAA,CAAAF,EAAAA,IAACqB,IAAc,UAAU,QACvB,eAACC,GAAY,CAAA,YAAY,cAAc,CACzC,CAAA,EACCtB,MAAAuB,GAAA,CACE,SAAsBP,GAAA,OACpBhB,EAAAA,IAAAwB,GAAA,CAA2B,MAAOlB,EAAI,MACpC,SAAIA,EAAA,OADUA,EAAI,KAErB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CAEJ,QACCL,EAAO,CAAA,KAAK,SAAS,QAAQ,QAAQ,KAAK,OAAO,QAAS,IAAMX,EAAOa,CAAG,EAAG,aAAW,kBACvF,eAACY,EAAO,CAAA,UAAU,UAAU,CAC9B,CAAA,CAAA,CAxBQ,EAAAb,EAAM,EAyBhB,CACD,CAAA,CACH,CAAA,CAAA,EACF,CAEJ,EClCMuB,GAAgE,CAAC,CAAE,KAAAC,EAAM,aAAAC,EAAc,SAAAC,KAAe,CAEpG,MAAAC,EAA4BD,GAAqD,CACrF,GAAI,CAACA,EACI,MAAA,CACL,KAAM,GACN,YAAa,GACb,KAAM,GACN,UAAW,CAAC,EACZ,UAAW,CAAC,EACZ,OAAQ,CAAA,CACV,EAII,MAAAE,GAAgDF,EAAS,WAAa,CAAC,GAC1E,OAAOG,GAAYA,EAAS,UAAU,EACtC,IAAiBA,IAAA,CAChB,GAAIA,EAAS,IAAM,KACnB,WAAYA,EAAS,YAAc,KACnC,WAAYA,EAAS,WACrB,SAAUA,EAAS,UAAY,EAC/B,OAAQA,EAAS,QAAU,IAAA,EAC3B,EAGEC,GAAgDJ,EAAS,WAAa,CAAA,GAAI,IAAiBK,IAAA,CAC/F,GAAIA,EAAS,IAAM,KACnB,WAAYA,EAAS,YAAc,KACnC,aAAcA,EAAS,cAAgB,IAAA,EACvC,EAEK,MAAA,CACL,KAAML,EAAS,MAAQ,GACvB,YAAaA,EAAS,aAAe,GACrC,KAAMA,EAAS,MAAQ,GACvB,UAAAE,EACA,UAAAE,EACA,OAAQ,CAAA,CACV,CACF,EAEME,EAAUC,GAAoB,CAClC,cAAeN,EAAyBD,CAAQ,CAAA,CACjD,EACK,CAAE,SAAAQ,EAAU,aAAAC,EAAc,MAAAC,EAAO,QAAA/C,EAAS,UAAW,CAAE,OAAAgD,EAAQ,QAAAC,EAAS,aAAAC,CAAA,EAAgB,SAAAjD,EAAU,MAAAC,CAAU,EAAAyC,EAC5G,CAAE,MAAAQ,CAAM,EAAIC,GAAS,EAGrBC,EAAiBC,EAAc,CAAE,QAAAtD,EAAS,KAAM,YAAa,EAC7DuD,EAAiBD,EAAc,CAAE,QAAAtD,EAAS,KAAM,YAAa,EAInEwD,EAAAA,UAAU,IAAM,CACR,MAAAC,EAAWnB,EAAyBD,CAAQ,EAClDU,EAAMU,CAAQ,EAGTtB,IACH/B,EAAe,EAAE,EACjBE,EAAe,CAAA,CAAE,EACjBoD,EAAsB,EAAK,EAC3BC,EAAoB,QAAU,GAC9BC,EAAqB,QAAU,GAC/BC,EAAiB,QAAU,KAE5B,EAAA,CAACxB,EAAUF,EAAMY,CAAK,CAAC,EAG1B,MAAMe,EAAiBC,EAAY,CACjC,WAAY,MAAOC,GACVC,GAA6B,CAAE,KAAMD,EAAQ,EAEtD,UAAW,IAAM,CACf5B,EAAa,EAAK,EACZW,EAAA,CACR,EACA,QAAUmB,GAAoC,CACtCf,EAAA,CACJ,MAAOe,EAAI,OAAO,SAAW,QAC7B,YAAaA,EAAI,OAAO,SAAW,sCACnC,QAAS,OAAA,CACV,CAAA,CACH,CACD,EAGKC,EAAiBJ,EAAY,CACjC,WAAY,MAAOC,GAAuB,CACxC,GAAI,CAAC3B,GAAU,GAAU,MAAA,IAAI,MAAM,gBAAgB,EACnD,OAAO+B,GAAgC,CACrC,KAAM,CAAE,GAAI/B,EAAS,EAAG,EACxB,KAAM2B,CAAA,CACP,CACH,EACA,UAAW,IAAM,CACf5B,EAAa,EAAK,EACZW,EAAA,CAAA,CACR,CACD,EAEKsB,EAAYL,GAAuB,CAEvC,MAAMM,EAA0B,CAC9B,GAAGN,EAEH,UAAWA,EAAO,UAAU,IAAI,CAAC,CAAE,WAAAO,EAAY,GAAGC,CAAK,IAAMA,CAAI,EAEjE,UAAWR,EAAO,UAAU,IAAI,CAAC,CAAE,WAAAO,EAAY,GAAGC,CAAK,IAAMA,CAAI,EACjE,OAAQR,EAAO,MACjB,EACI3B,GAAU,GACZ8B,EAAe,OAAOG,CAAW,EAEjCR,EAAe,OAAOQ,CAAW,CAErC,EAEMG,EAAYvB,GAAgBY,EAAe,WAAaK,EAAe,UACvEO,EAAQZ,EAAe,OAASK,EAAe,MAE/C,CAAChE,EAAaC,CAAc,EAAIuE,EAAM,SAAS,EAAE,EACjD,CAACtE,EAAaC,CAAc,EAAIqE,EAAM,SAA8B,CAAA,CAAE,EACtE,CAACC,EAAoBlB,CAAqB,EAAIiB,EAAM,SAAS,EAAK,EAClEhB,EAAsBgB,EAAM,OAAO,EAAK,EACxCf,EAAuBe,EAAM,OAAO,EAAK,EACzCd,EAAmBc,EAAM,OAAsB,IAAI,EACnDpE,EAAqBwD,EAAoE,CAC7F,WAAY,MAAOc,IAWL,MAAMC,EAA+B,CAC/C,KAAM,CACJ,eAAgB,GAChB,UAAW,EACX,YAdgBD,EAChB,CACE,SAAU,KACV,WAAY,CACV,CAAE,UAAW,OAAQ,SAAU,WAA8B,MAAOA,CAAQ,EAC5E,CAAE,UAAW,WAAY,SAAU,WAA8B,MAAOA,CAAQ,EAChF,CAAE,UAAW,QAAS,SAAU,WAA8B,MAAOA,CAAQ,CAAA,CAC/E,EAEF,MAKA,CACF,CACD,IACW,MAAQ,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,CACjD,CACD,EAGKE,EAAoBJ,EAAM,YAAY,MAAOK,GAA0B,CAC3E,GAAI,EAAAA,EAAY,SAAW,GAAKpB,EAAqB,SAErD,CAAAA,EAAqB,QAAU,GAC3B,GAAA,CACF,MAAMqB,EAAc,CAClB,SAAU,KACV,WAAYD,EAAY,IAAWE,IAAA,CACjC,UAAW,KACX,SAAU,QACV,MAAOA,CAAA,EACP,CACJ,EAYMC,KAVM,MAAML,EAA+B,CAC/C,KAAM,CACJ,eAAgB,IAChB,UAAW,EACX,YAAAG,CAAA,CACF,CACD,IAEqB,MAAQ,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,GAEnB,OAAS,CAAA,GAAI,IAAaG,IAAA,CAC9D,MAAOA,EAAK,IAAM,GAClB,MAAOA,EAAK,MAAQA,EAAK,UAAYA,EAAK,OAAS,WAAA,EACnD,EAEF9E,EAAuB+E,GAAA,CAEf,MAAAC,EAAiB,IAAI,IAAID,EAAK,IAAWtE,GAAAA,EAAI,KAAK,CAAC,EACnDM,EAAa8D,EAAoB,OAAOpE,GAAO,CAACuE,EAAe,IAAIvE,EAAI,KAAK,CAAC,EACnF,MAAO,CAAC,GAAGsE,EAAM,GAAGhE,CAAU,CAAA,CAC/B,OACa,CAAA,QAEd,CACAuC,EAAqB,QAAU,EAAA,EAEnC,EAAG,EAAE,EAGLe,OAAAA,EAAM,UAAU,IAAM,CACpB,GAAIxC,GAAQE,GAAU,WAAaA,EAAS,UAAU,OAAS,GAEzDwB,EAAiB,UAAYxB,EAAS,GAAI,CACtC,MAAA2C,EAAc3C,EAAS,UAC1B,OAAgBG,EAAS,UAAU,EACnC,OAAO,OAAO,EAEbwC,EAAY,OAAS,IACNnB,EAAA,QAAUxB,EAAS,IAAM,KAC1C0C,EAAkBC,CAAW,EAC/B,CAEJ,EACC,CAAC7C,EAAME,CAAQ,CAAC,EAGnBsC,EAAM,UAAU,IAAM,CAChBxC,GAAQ,CAACyC,GAAsBvE,EAAY,SAAW,GAAK,CAACsD,EAAoB,UAClFA,EAAoB,QAAU,GAC9BD,EAAsB,EAAI,EAC1BnD,EAAmB,OAAO,GAAI,CAC5B,UAAoBa,GAAA,CAClBd,GAAgBc,EAAM,OAAS,CAAA,GAAI,IAAUE,IAAA,CAC3C,MAAOA,EAAE,IAAM,GACf,MAAOA,EAAE,MAAQA,EAAE,UAAYA,EAAE,OAAS,aAC1C,CAAC,EACHqC,EAAoB,QAAU,EAChC,EACA,QAAS,IAAM,CACbD,EAAsB,EAAK,EAC3BC,EAAoB,QAAU,EAAA,CAChC,CACD,IAEF,CAACxB,EAAMyC,EAAoBvE,EAAY,MAAM,CAAC,QAG9CkF,GAAO,CAAA,KAAApD,EAAY,aAAAC,EAClB,SAAC5B,EAAA,KAAAgF,GAAA,CAAc,KAAK,MAClB,SAAA,CAAAhF,OAACiF,GACC,CAAA,SAAA,CAAAhF,EAAAA,IAACiF,IAAY,SAAiB,mBAAA,CAAA,EAC9BjF,EAAAA,IAACkF,IAAkB,SAEnB,gDAAA,CAAA,CAAA,EACF,EACAlF,EAAAA,IAACmF,IAAc,GAAGjD,EAChB,gBAAC,OAAK,CAAA,SAAUG,EAAauB,CAAQ,EACnC,SAAA,CAAC7D,EAAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,iCAAiC,SAAA,CAAA,QAAMC,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAAC,GAAA,CAAA,CAAA,EAAO,EAClGA,EAAA,IAACc,EAAA,CACE,GAAGsB,EAAS,OAAQ,CAAE,SAAU,mBAAoB,EACrD,UAAU,8BACV,UAAS,GACT,eAAc,CAAC,CAACG,EAAO,IAAA,CACzB,EACCA,EAAO,MAASvC,MAAA,MAAA,CAAI,UAAU,gCAAiC,SAAAuC,EAAO,KAAK,OAAQ,CAAA,CAAA,EACtF,SACC,MACC,CAAA,SAAA,CAACvC,EAAA,IAAA,QAAA,CAAM,UAAU,iCAAiC,SAAI,OAAA,EACtDA,EAAA,IAACc,EAAA,CACE,GAAGsB,EAAS,MAAM,EACnB,UAAU,6BAAA,CAAA,CACZ,EACF,EACArC,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,iCAAiC,SAAW,cAAA,EAC7DA,EAAA,IAACoF,GAAA,CACE,GAAGhD,EAAS,aAAa,EAC1B,UAAU,0CAAA,CAAA,CACZ,CACF,CAAA,CAAA,EACF,EACCrC,EAAA,KAAAsF,GAAA,CAAK,aAAa,YAAY,UAAU,SACvC,SAAA,CAACtF,EAAAA,KAAAuF,GAAA,CAAS,UAAU,OAClB,SAAA,CAACtF,EAAA,IAAAuF,EAAA,CAAY,MAAM,YAAY,SAAS,YAAA,EACvCvF,EAAA,IAAAuF,EAAA,CAAY,MAAM,YAAY,SAAS,WAAA,CAAA,CAAA,EAE1C,EACAvF,EAAAA,IAACwF,EAAY,CAAA,MAAM,YACjB,SAAAxF,EAAA,IAACb,GAAA,CACE,GAAGyD,EACJ,QAAArD,EACA,SAAAC,EACA,MAAAC,EACA,YAAAC,EACA,eAAAC,EACA,YAAAC,EACA,eAAAC,EACA,mBAAAC,CAAA,CAAA,EAEJ,EACAE,EAAAA,IAACwF,GAAY,MAAM,YACjB,eAACvE,GAAc,CAAA,GAAG6B,EAAgB,CACpC,CAAA,CAAA,EAIF,EACCmB,GAAUjE,EAAAA,IAAA,MAAA,CAAI,UAAU,gCAAiC,SAAO,OAAAiE,aAAiB,MAAQA,EAAM,QAAUA,CAAK,CAAE,CAAA,EACjHlE,EAAAA,KAAC0F,GAAa,CAAA,UAAU,OACtB,SAAA,CAACzF,EAAAA,IAAAC,EAAA,CAAO,KAAK,SAAS,QAAQ,UAAU,QAAS,IAAM0B,EAAa,EAAK,EAAG,SAE5E,QAAA,CAAA,EACC3B,EAAAA,IAAAC,EAAA,CAAO,KAAK,SAAS,UAAU,OAAO,SAAU+D,GAAa,CAACxB,EAC5D,SAAYwB,EAAA,YAAc,MAC7B,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECvUM0B,GAAgB,CACpB,CAAE,MAAO,OAAQ,MAAO,MAAO,EAC/B,CAAE,MAAO,cAAe,MAAO,aAAc,EAC7C,CAAE,MAAO,OAAQ,MAAO,MAAO,CAEjC,EACMC,GAA+D,CACnE,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,WAAY,MAAO,UAAW,EACvC,CAAE,MAAO,YAAa,MAAO,YAAa,EAC1C,CAAE,MAAO,cAAe,MAAO,GAAI,EACnC,CAAE,MAAO,WAAY,MAAO,GAAI,CAClC,EAEMC,GAAkC,IAAM,CAC5C,KAAM,CAACC,EAAYC,CAAa,EAAIC,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EACK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAA4B,CAAA,CAAE,EACtD,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAwD,CAAA,CAAE,EAC9E,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAK,EAChD,CAACO,EAAYC,CAAa,EAAIR,EAAAA,SAAS,EAAK,EAC5C,CAACS,EAAiBC,CAAkB,EAAIV,EAAAA,SAAqC,IAAI,EACjFW,EAAcC,EAAe,EAGnCzC,EAAM,UAAU,IAAM,CACpB4B,EAAelB,IAAU,CAAE,GAAGA,EAAM,UAAW,GAAI,CAAA,EAClD,CAACoB,EAASE,CAAK,CAAC,EAGb,MAAA1B,EAAuCoC,EAAAA,QAAQ,IAAM,CACrD,GAACZ,EAAQ,OACN,MAAA,CACL,SAAU,MACV,WAAYA,CACd,CAAA,EACC,CAACA,CAAO,CAAC,EAGNa,EAAaD,EAAAA,QAAQ,IAAM,CAC3B,GAACV,EAAM,OACX,OAAOA,EAAM,IAASY,GAAA,GAAGA,EAAE,KAAK,IAAIA,EAAE,SAAS,EAAE,EAAE,KAAK,IAAI,CAAA,EAC3D,CAACZ,CAAK,CAAC,EAGJ,CACJ,KAAMa,EACN,UAAA/C,EACA,MAAAC,EACA,QAAA+C,EACA,WAAAC,GACEC,GAAS,CACX,SAAU,CACR,qBACArB,EAAW,UACXA,EAAW,SACXgB,EACArC,CACF,EACA,QAAS,SAAY,CACb,MAAA2C,EAAM,MAAMC,GAAuC,CACvD,KAAM,CACJ,KAAMvB,EAAW,UAAY,EAC7B,eAAgBA,EAAW,SAC3B,QAASgB,EACT,YAAArC,CAAA,CACF,CACD,EAED,SAAS6C,EAAcC,EAA2E,CACzF,MAAA,CAAC,CAACA,GAAO,OAAOA,GAAQ,UAAY,UAAWA,GAAO,eAAgBA,CAAA,CAEzEC,MAAAA,EAAOJ,GAAK,MAAQA,EACtB,OAAAE,EAAcE,CAAI,EACb,CACL,MAAOA,EAAK,OAAS,CAAC,EACtB,WAAYA,EAAK,YAAc,CACjC,EAEK,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACD,EAEKA,EAAOR,GAAa,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,EAG/CS,EAAgB,SAAY,CAChCnB,EAAgB,EAAI,EACd,MAAAK,EAAY,kBAAkB,CAAE,SAAU,CAC9C,qBACAb,EAAW,UACXA,EAAW,SACXgB,EACArC,CAAA,EACC,EACH6B,EAAgB,EAAK,CACvB,EAGMoB,EAAY,IAAM,CACtBhB,EAAmB,IAAI,EACvBF,EAAc,EAAI,CACpB,EAGMmB,EAAc9F,GAAkC,CACpD6E,EAAmB7E,CAAQ,EAC3B2E,EAAc,EAAI,CACpB,EAGMoB,EAA4C,CAChD,CACE,YAAa,OACb,OAAQ,OACR,KAAMC,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,cACb,OAAQ,cACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,OACb,OAAQ,OACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,GAAI,OACJ,OAAQ,GACR,KAAM,CAAC,CAAE,IAAAC,CAAA,IACP7H,EAAA,IAACC,EAAA,CACC,QAAS,IAAMyH,EAAWG,EAAI,QAAQ,EACtC,aAAW,yBACX,SAAU,EACV,QAAQ,UACR,KAAK,OACL,UAAU,eAEV,SAAC7H,EAAA,IAAA8H,GAAA,CAAO,UAAU,UAAU,cAAY,MAAO,CAAA,CAAA,CACjD,EAEF,cAAe,GACf,mBAAoB,EAAA,CAGxB,EAGA,OAAI7D,EAEAlE,EAAA,KAAC,MAAI,CAAA,UAAU,4EACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA0C,SAAC,IAAA,EACzDA,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAkB,qBAAA,QACrE,IAAE,CAAA,UAAU,qCAAsC,SAAQ,OAAAiE,EAAgB,OAAO,EAAE,QACnFhE,EAAO,CAAA,QAASuH,EAAe,QAAQ,cAAc,SAAK,OAAA,CAAA,CAAA,EAC7D,EAKFzH,EAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,mCAAmC,SAAsB,yBAAA,EACxEA,EAAA,IAAC+H,GAAA,CACC,aAAcrC,GACd,UAAWC,GACX,QAAAK,EACA,MAAAE,EACA,gBAAiBD,EACjB,cAAeE,EAEf,SAAApG,EAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAC,EAAA,IAACC,EAAA,CACC,QAASuH,EACT,QAAQ,UACR,KAAK,OACL,UAAU,YACV,SAAUxD,GAAaoC,GAAgBa,EAEvC,SAAAjH,EAAA,IAACgI,IAAU,UAAW,WAAW5B,GAAgBa,EAAa,eAAiB,EAAE,EAAI,CAAA,CAAA,CACvF,EACAlH,EAAA,KAACE,EAAA,CACC,QAASwH,EAET,SAAA,CAACzH,EAAAA,IAAAiI,GAAA,CAAK,UAAU,SAAU,CAAA,EAAE,wBAAA,CAAA,CAAA,CAC9B,CACF,CAAA,CAAA,CACF,EACCjE,EACChE,EAAAA,IAACkI,GAAc,CAAA,QAAAP,CAAA,CAAkB,EAEjC3H,EAAA,IAACmI,GAAA,CACC,MAAM,GACN,QAAAR,EACA,KAAMJ,EAAK,MACX,WAAYA,EAAK,WACjB,UAAAvD,EACA,iBAAkB,GAClB,SAAU6B,EAAW,SACrB,mBAAoBC,EACpB,qBAAsB,GACtB,mBAAoB,GACpB,cAAe,EAAA,CACjB,EAEF9F,EAAA,IAACyB,GAAA,CACC,KAAM6E,EACN,aAAe5E,GAAS,CACtB6E,EAAc7E,CAAI,EACbA,GAAcsF,EAAA,CACrB,EACA,SAAUR,CAAA,CAAA,CACZ,EACF,CAEJ,ECjOM4B,GAAiC,IAElCpI,EAAAA,IAAAqI,GAAA,CACC,SAACrI,EAAAA,IAAA4F,GAAA,CAAsB,CAAA,EACzB"}