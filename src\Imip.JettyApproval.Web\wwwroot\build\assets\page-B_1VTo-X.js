import{j as t}from"./vendor-6tJeyfYI.js";import{A as l,F as u,M as d}from"./app-layout-rNt37hVL.js";import{u as x,b as c}from"./buildApiPayloadVessel-BEaNS5GF.js";import{D as y}from"./data-grid-DZ2U-5jU.js";import{$ as f}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./badge-DWaCYvGm.js";import"./arrow-up-right-DyuQRH0Y.js";import"./checkbox-D1loOtZt.js";import"./dialog-BmEXyFlW.js";import"./input-DlXlkYlT.js";import"./popover-ChFN9yvN.js";import"./table-BKSoE52x.js";import"./useDebounce-B2N8e_3P.js";import"./index-CaiFFM4D.js";import"./TableSkeleton-CIQBoxBh.js";import"./skeleton-DAOxGMKm.js";import"./plus-PD53KOti.js";import"./arrow-up-DDQ17ADi.js";import"./chevron-left-DJFXm33k.js";const j=[{id:"docNum",desc:!0}],C=()=>{const{t:r}=u(),s=x();return t.jsx("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:t.jsx(y,{columns:s,title:r("datagrid.nonCustomArea.exportVessel"),queryKey:["non-custom-export-vessel-list"],manualSorting:!0,manualFiltering:!0,queryFn:async({pageIndex:e,pageSize:a,sorting:i=j,filters:m,globalFilter:p})=>{const n=c({pageIndex:e,pageSize:a,sorting:i,filters:m,globalFilter:p,vesselType:"export",isCustomArea:!1}),o=await d.filterExportVessels(n);return{items:o.data?.items??[],totalCount:o.data?.totalCount??0}}})})},I=()=>t.jsxs(l,{children:[t.jsx(f,{title:"Export Vessel"}),t.jsx("div",{className:"flex flex-col space-y-4 p-4",children:t.jsx(C,{})})]});export{I as default};
//# sourceMappingURL=page-B_1VTo-X.js.map
