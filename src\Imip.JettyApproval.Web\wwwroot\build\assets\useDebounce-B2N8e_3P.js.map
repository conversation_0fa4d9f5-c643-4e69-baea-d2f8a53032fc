{"version": 3, "file": "useDebounce-B2N8e_3P.js", "sources": ["../../../../../frontend/src/lib/hooks/useDebounce.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\n\n/**\n * Custom hook that debounces a value\n * @param value - The value to debounce\n * @param delay - The delay in milliseconds\n * @returns The debounced value\n */\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value)\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value)\n    }, delay)\n\n    return () => {\n      clearTimeout(handler)\n    }\n  }, [value, delay])\n\n  return debouncedValue\n}\n"], "names": ["useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "useState", "useEffect", "handler"], "mappings": "yCAQgB,SAAAA,EAAeC,EAAUC,EAAkB,CACzD,KAAM,CAACC,EAAgBC,CAAiB,EAAIC,EAAAA,SAAYJ,CAAK,EAE7DK,OAAAA,EAAAA,UAAU,IAAM,CACR,MAAAC,EAAU,WAAW,IAAM,CAC/BH,EAAkBH,CAAK,GACtBC,CAAK,EAER,MAAO,IAAM,CACX,aAAaK,CAAO,CACtB,CAAA,EACC,CAACN,EAAOC,CAAK,CAAC,EAEVC,CACT"}