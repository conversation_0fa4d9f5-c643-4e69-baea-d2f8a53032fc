import { ekbProxyService } from "@/services/ekbProxyService";
import { useImportColumns } from "@/components/jetty/custom-area-jetty.columns";
import { DataGrid } from "@/components/ui/data-grid";
import AppLayout from "@/layouts/app-layout";
import { buildApiPayloadVessel } from "@/lib/queryHelper/buildApiPayloadVessel";
import { Head } from "@inertiajs/react";
import React from "react";
import { useTranslation } from "react-i18next";
import { ContentCard } from "@/components/layout/content-card";


const DEFAULT_SORTING = [{ id: 'docNum', desc: true }];
const CustomAreaJettyContent: React.FC = () => {
  const { t } = useTranslation();
  const importColumns = useImportColumns()
  return (
    <ContentCard>
      <DataGrid
        columns={importColumns}
        title={t("datagrid.nonCustomArea.importVessel")}
        queryKey={["import-vessel-list"]}
        manualSorting={true}
        manualFiltering={true}
        enableNewButton={false}
        queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {
          const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter, isCustomArea: false });
          const res = await ekbProxyService.filterImportVessels(payload);
          return {
            items: res.data?.items ?? [],
            totalCount: res.data?.totalCount ?? 0,
          };
        }}
      />
    </ContentCard>
  );
};

const ImportVesselPage: React.FC = () => {
  return (
    <AppLayout>
      <Head title={`Import Vessel`} />
      <div className="flex flex-col space-y-4 p-4">
        <CustomAreaJettyContent />
      </div>
    </AppLayout>
  );
};

export default ImportVesselPage;