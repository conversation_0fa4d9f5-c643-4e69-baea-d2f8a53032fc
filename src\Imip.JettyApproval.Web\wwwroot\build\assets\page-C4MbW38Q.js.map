{"version": 3, "file": "page-C4MbW38Q.js", "sources": ["../../../../../frontend/src/pages/import/edit/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { CreateUpdateImportVesselDto, CreateUpdateVesselItemDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { ImportVesselFormWithData } from '@/components/jetty/vessel/import/import-vessel-form';\r\nimport type { ImportVesselHeaderForm } from '@/components/jetty/vessel/import/import-vessel-header-schema';\r\nimport type { ImportVesselItemForm } from '@/components/jetty/vessel/import/import-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { toDatetimeLocalString } from '@/lib/utils/date-convert';\r\nimport { Head, usePage } from '@inertiajs/react';\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ImportVesselEditPage = () => {\r\n  const { t } = useTranslation();\r\n  const { props } = usePage();\r\n  const { toast } = useToast();\r\n  const id = typeof props.id === 'string' ? props.id : undefined;\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    data: vesselData,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: ['import-vessel', id],\r\n    queryFn: async () => {\r\n      if (!id) return null;\r\n      const response = await ekbProxyService.getImportVesselWithItems(id);\r\n      return response.data;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n\r\n  const initialHeader: Partial<CreateUpdateImportVesselDto> = vesselData\r\n    ? {\r\n      docNum: vesselData.docNum ?? 0,\r\n      voyage: vesselData.voyage ?? '',\r\n      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? ''),\r\n      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? ''),\r\n      vesselId: vesselData.vesselId ?? '',\r\n      jettyId: vesselData.jettyId ?? '',\r\n      portOriginId: vesselData.portOriginId ?? '',\r\n      destinationPortId: vesselData.destinationPortId ?? '',\r\n      postingDate: vesselData.postingDate ?? '',\r\n      asideDate: toDatetimeLocalString(vesselData.asideDate ?? ''),\r\n      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? ''),\r\n      deleted: vesselData.deleted ?? '',\r\n      docType: vesselData.docType ?? '',\r\n      isChange: vesselData.isChange ?? '',\r\n      isLocked: vesselData.isLocked ?? '',\r\n      createdBy: vesselData.createdBy ?? '',\r\n      docStatus: vesselData.docStatus ?? '',\r\n      statusBms: vesselData.statusBms ?? '',\r\n      transType: vesselData.transType ?? '',\r\n      concurrencyStamp: vesselData.concurrencyStamp ?? '',\r\n    }\r\n    : {\r\n      deleted: '',\r\n      docType: '',\r\n      isChange: '',\r\n      isLocked: '',\r\n      createdBy: '',\r\n      docStatus: '',\r\n      statusBms: '',\r\n      transType: '',\r\n      concurrencyStamp: '',\r\n    };\r\n\r\n  const initialItems: CreateUpdateVesselItemDto[] = vesselData?.items\r\n    ? vesselData.items.map(item => {\r\n      let unitWeight: string | null | undefined = null;\r\n      if (item.unitWeight != null) {\r\n        if (typeof item.unitWeight === 'string') {\r\n          unitWeight = item.unitWeight !== '' ? item.unitWeight : null;\r\n        } else {\r\n          unitWeight = String(item.unitWeight);\r\n        }\r\n      }\r\n      let grossWeight: number | null = null;\r\n      if (item.grossWeight != null) {\r\n        if (typeof item.grossWeight === 'string') {\r\n          grossWeight = item.grossWeight !== '' ? Number(item.grossWeight) : null;\r\n        } else {\r\n          grossWeight = item.grossWeight;\r\n        }\r\n      }\r\n      return {\r\n        itemName: item.itemName ?? null,\r\n        itemQty: item.itemQty ?? 0,\r\n        unitQty: item.unitQty ?? null,\r\n        remarks: item.remarks ?? null,\r\n        ajuNo: item.ajuNo ?? null,\r\n        regDate: item.regDate ? item.regDate : null,\r\n        regNo: item.regNo ?? null,\r\n        grossWeight,\r\n        unitWeight,\r\n        shippingInstructionNo: item.shippingInstructionNo ?? null,\r\n        shippingInstructionDate: item.shippingInstructionDate ?? null,\r\n        letterNo: item.letterNo ?? null,\r\n        letterDate: item.letterDate ?? null,\r\n        status: item.status ?? null,\r\n        regType: item.regType ?? null,\r\n        attachments: item.attachments ?? [],\r\n        tenant: item.tenantName ?? null,\r\n        tenantId: item.tenantId ?? '',\r\n        businessPartner: item.businessPartner?.name ?? null,\r\n        businessPartnerId: item.businessPartnerId ?? null,\r\n        concurrencyStamp: item.concurrencyStamp ?? undefined,\r\n        id: item.id ?? undefined,\r\n      };\r\n    })\r\n    : [];\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: ImportVesselHeaderForm; items: ImportVesselItemForm[] }) => {\r\n      if (!id) throw new Error('No ID provided');\r\n      const response = await ekbProxyService.updateImportVessel(id, {\r\n        ...header,\r\n        docNum: Number(header.docNum),\r\n        deleted: header.deleted ?? '', // or your default\r\n        docType: header.docType ?? '',\r\n        isChange: header.isChange ?? '',\r\n        isLocked: header.isLocked ?? '',\r\n        createdBy: header.createdBy ?? '',\r\n        docStatus: header.docStatus ?? 'Open',\r\n        statusBms: header.statusBms ?? '',\r\n        transType: header.transType ?? '',\r\n        concurrencyStamp: header.concurrencyStamp ?? '',\r\n        items: items.map(item => ({\r\n          ...item,\r\n          regDate: item.regDate ? item.regDate : null,\r\n          tenantId: item.tenantId || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n        })),\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: () => {\r\n      toast({ title: 'Success', description: 'Import vessel updated.', variant: 'success' });\r\n      queryClient.invalidateQueries({ queryKey: ['import-vessel', id] });\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  if (isLoading) return <div>Loading...</div>;\r\n  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;\r\n\r\n  return (\r\n    <ImportVesselFormWithData\r\n      mode=\"edit\"\r\n      title={t('pages.vessel.edit.import')}\r\n      initialHeader={initialHeader}\r\n      initialItems={initialItems}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n      queryClient={queryClient}\r\n      showAddLineButton={false}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function ImportVesselEdit() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.edit.import')} />\r\n      <ImportVesselEditPage />\r\n    </AppLayout>\r\n  );\r\n} "], "names": ["ImportVesselEditPage", "t", "useTranslation", "props", "usePage", "toast", "useToast", "id", "queryClient", "useQueryClient", "vesselData", "isLoading", "isError", "error", "useQuery", "ekbProxyService", "initialHeader", "toDatetimeLocalString", "initialItems", "item", "unitWeight", "grossWeight", "mutation", "useMutation", "header", "items", "response", "err", "handleSubmit", "jsx", "jsxs", "ImportVesselFormWithData", "ImportVesselEdit", "AppLayout", "Head"], "mappings": "+yBAYA,MAAMA,EAAuB,IAAM,CAC3B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAQ,EACpB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrBC,EAAK,OAAOJ,EAAM,IAAO,SAAWA,EAAM,GAAK,OAC/CK,EAAcC,EAAe,EAE7B,CACJ,KAAMC,EACN,UAAAC,EACA,QAAAC,EACA,MAAAC,GACEC,EAAS,CACX,SAAU,CAAC,gBAAiBP,CAAE,EAC9B,QAAS,SACFA,GACY,MAAMQ,EAAgB,yBAAyBR,CAAE,GAClD,KAFA,KAIlB,QAAS,CAAC,CAACA,CAAA,CACZ,EAEKS,EAAsDN,EACxD,CACA,OAAQA,EAAW,QAAU,EAC7B,OAAQA,EAAW,QAAU,GAC7B,cAAeO,EAAsBP,EAAW,eAAiB,EAAE,EACnE,gBAAiBO,EAAsBP,EAAW,iBAAmB,EAAE,EACvE,SAAUA,EAAW,UAAY,GACjC,QAASA,EAAW,SAAW,GAC/B,aAAcA,EAAW,cAAgB,GACzC,kBAAmBA,EAAW,mBAAqB,GACnD,YAAaA,EAAW,aAAe,GACvC,UAAWO,EAAsBP,EAAW,WAAa,EAAE,EAC3D,WAAYO,EAAsBP,EAAW,YAAc,EAAE,EAC7D,QAASA,EAAW,SAAW,GAC/B,QAASA,EAAW,SAAW,GAC/B,SAAUA,EAAW,UAAY,GACjC,SAAUA,EAAW,UAAY,GACjC,UAAWA,EAAW,WAAa,GACnC,UAAWA,EAAW,WAAa,GACnC,UAAWA,EAAW,WAAa,GACnC,UAAWA,EAAW,WAAa,GACnC,iBAAkBA,EAAW,kBAAoB,EAAA,EAEjD,CACA,QAAS,GACT,QAAS,GACT,SAAU,GACV,SAAU,GACV,UAAW,GACX,UAAW,GACX,UAAW,GACX,UAAW,GACX,iBAAkB,EACpB,EAEIQ,EAA4CR,GAAY,MAC1DA,EAAW,MAAM,IAAYS,GAAA,CAC7B,IAAIC,EAAwC,KACxCD,EAAK,YAAc,OACjB,OAAOA,EAAK,YAAe,SAC7BC,EAAaD,EAAK,aAAe,GAAKA,EAAK,WAAa,KAE3CC,EAAA,OAAOD,EAAK,UAAU,GAGvC,IAAIE,EAA6B,KAC7B,OAAAF,EAAK,aAAe,OAClB,OAAOA,EAAK,aAAgB,SAC9BE,EAAcF,EAAK,cAAgB,GAAK,OAAOA,EAAK,WAAW,EAAI,KAEnEE,EAAcF,EAAK,aAGhB,CACL,SAAUA,EAAK,UAAY,KAC3B,QAASA,EAAK,SAAW,EACzB,QAASA,EAAK,SAAW,KACzB,QAASA,EAAK,SAAW,KACzB,MAAOA,EAAK,OAAS,KACrB,QAASA,EAAK,QAAUA,EAAK,QAAU,KACvC,MAAOA,EAAK,OAAS,KACrB,YAAAE,EACA,WAAAD,EACA,sBAAuBD,EAAK,uBAAyB,KACrD,wBAAyBA,EAAK,yBAA2B,KACzD,SAAUA,EAAK,UAAY,KAC3B,WAAYA,EAAK,YAAc,KAC/B,OAAQA,EAAK,QAAU,KACvB,QAASA,EAAK,SAAW,KACzB,YAAaA,EAAK,aAAe,CAAC,EAClC,OAAQA,EAAK,YAAc,KAC3B,SAAUA,EAAK,UAAY,GAC3B,gBAAiBA,EAAK,iBAAiB,MAAQ,KAC/C,kBAAmBA,EAAK,mBAAqB,KAC7C,iBAAkBA,EAAK,kBAAoB,OAC3C,GAAIA,EAAK,IAAM,MACjB,CACD,CAAA,EACC,CAAC,EAECG,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA+E,CAC1G,GAAI,CAAClB,EAAU,MAAA,IAAI,MAAM,gBAAgB,EACzC,MAAMmB,EAAW,MAAMX,EAAgB,mBAAmBR,EAAI,CAC5D,GAAGiB,EACH,OAAQ,OAAOA,EAAO,MAAM,EAC5B,QAASA,EAAO,SAAW,GAC3B,QAASA,EAAO,SAAW,GAC3B,SAAUA,EAAO,UAAY,GAC7B,SAAUA,EAAO,UAAY,GAC7B,UAAWA,EAAO,WAAa,GAC/B,UAAWA,EAAO,WAAa,OAC/B,UAAWA,EAAO,WAAa,GAC/B,UAAWA,EAAO,WAAa,GAC/B,iBAAkBA,EAAO,kBAAoB,GAC7C,MAAOC,EAAM,IAAaN,IAAA,CACxB,GAAGA,EACH,QAASA,EAAK,QAAUA,EAAK,QAAU,KACvC,SAAUA,EAAK,UAAY,GAC3B,kBAAmBA,EAAK,mBAAqB,EAAA,EAC7C,CAAA,CACH,EACD,GAAIO,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAW,IAAM,CACfrB,EAAM,CAAE,MAAO,UAAW,YAAa,yBAA0B,QAAS,UAAW,EACrFG,EAAY,kBAAkB,CAAE,SAAU,CAAC,gBAAiBD,CAAE,EAAG,CACnE,EACA,QAAUoB,GAAoC,CACtCtB,EAAA,CACJ,MAAOsB,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOJ,EAAgCC,IAAkC,CAC5F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAEA,OAAId,EAAmBkB,EAAA,IAAA,MAAA,CAAI,SAAU,aAAA,EACjCjB,EAAgBkB,EAAAA,KAAC,MAAI,CAAA,SAAA,CAAA,uBAAqBjB,aAAiB,MAAQA,EAAM,QAAU,eAAA,EAAgB,EAGrGgB,EAAA,IAACE,EAAA,CACC,KAAK,OACL,MAAO9B,EAAE,0BAA0B,EACnC,cAAAe,EACA,aAAAE,EACA,SAAUU,EACV,aAAcN,EAAS,UACvB,YAAAd,EACA,kBAAmB,EAAA,CACrB,CAEJ,EAEA,SAAwBwB,GAAmB,CACnC,KAAA,CAAE,EAAA/B,CAAE,EAAIC,EAAe,EAC7B,cACG+B,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACK,EAAK,CAAA,MAAOjC,EAAE,0BAA0B,CAAG,CAAA,QAC3CD,EAAqB,CAAA,CAAA,CAAA,EACxB,CAEJ"}