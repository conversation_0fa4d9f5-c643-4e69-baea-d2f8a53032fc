import{j as t}from"./vendor-6tJeyfYI.js";import{A as n,F as u,M as d}from"./app-layout-rNt37hVL.js";import{a as c,b as x}from"./buildApiPayloadVessel-BEaNS5GF.js";import{D as f}from"./data-grid-DZ2U-5jU.js";import{$ as y}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./badge-DWaCYvGm.js";import"./arrow-up-right-DyuQRH0Y.js";import"./checkbox-D1loOtZt.js";import"./dialog-BmEXyFlW.js";import"./input-DlXlkYlT.js";import"./popover-ChFN9yvN.js";import"./table-BKSoE52x.js";import"./useDebounce-B2N8e_3P.js";import"./index-CaiFFM4D.js";import"./TableSkeleton-CIQBoxBh.js";import"./skeleton-DAOxGMKm.js";import"./plus-PD53KOti.js";import"./arrow-up-DDQ17ADi.js";import"./chevron-left-DJFXm33k.js";const j=[{id:"docNum",desc:!0}],C=()=>{const{t:o}=u(),s=c();return t.jsx("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:t.jsx(f,{columns:s,title:o("datagrid.nonCustomArea.importVessel"),queryKey:["import-vessel-list"],manualSorting:!0,manualFiltering:!0,queryFn:async({pageIndex:e,pageSize:a,sorting:i=j,filters:m,globalFilter:p})=>{const l=x({pageIndex:e,pageSize:a,sorting:i,filters:m,globalFilter:p,isCustomArea:!1}),r=await d.filterImportVessels(l);return{items:r.data?.items??[],totalCount:r.data?.totalCount??0}}})})},k=()=>t.jsxs(n,{children:[t.jsx(y,{title:"Import Vessel"}),t.jsx("div",{className:"flex flex-col space-y-4 p-4",children:t.jsx(C,{})})]});export{k as default};
//# sourceMappingURL=page-BUBQVA0E.js.map
