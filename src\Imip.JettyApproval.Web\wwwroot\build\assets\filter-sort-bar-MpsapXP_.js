import{a as m,j as e}from"./vendor-6tJeyfYI.js";import{B as _}from"./badge-DWaCYvGm.js";import{k as Q,B as n,X as p,S as o,c as u,d as h,e as x,f as c,W as R,G as H,H as U,J as q}from"./app-layout-rNt37hVL.js";import{I as A}from"./input-DlXlkYlT.js";import{P as F,a as z,b as k}from"./popover-ChFN9yvN.js";import{P as Y}from"./plus-PD53KOti.js";import{A as Z,a as $,b as ee}from"./arrow-up-DDQ17ADi.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],ae=Q("funnel",le),oe=({filterFields:v,operators:V,filters:f,sorts:g,onFiltersChange:P,onSortsChange:D,children:T})=>{const[G,N]=m.useState(!1),[J,w]=m.useState(!1),[s,t]=m.useState({}),[i,j]=m.useState({}),[b,S]=m.useState(null),[y,C]=m.useState(null),[E,I]=m.useState(""),O=v.filter(l=>l.label.toLowerCase().includes(E.toLowerCase())),B=()=>{if(s.fieldName&&s.operator&&s.value!==void 0&&s.value!==""){if(b!==null){const l=[...f];l[b]={fieldName:s.fieldName,operator:s.operator,value:s.value},P(l),S(null)}else P([...f,{fieldName:s.fieldName,operator:s.operator,value:s.value}]);t({}),N(!1),I("")}},M=l=>{P(f.filter((r,a)=>a!==l)),b===l&&S(null)},W=l=>{S(l),N(!1),t(f[l]),N(!1)},L=()=>{if(i.field&&i.direction){if(y!==null){const l=[...g];l[y]={field:i.field,direction:i.direction},D(l),C(null)}else D([...g,{field:i.field,direction:i.direction}]);j({}),w(!1)}},X=l=>{D(g.filter((r,a)=>a!==l)),y===l&&C(null)},K=l=>{C(l),w(!1),j(g[l]),w(!1)};return e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2 mb-2 px-2 py-2 bg-muted/30 rounded-t-lg",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[f.map((l,r)=>e.jsxs(F,{open:b===r,onOpenChange:a=>a?W(r):S(null),children:[e.jsx(z,{asChild:!0,children:e.jsxs(_,{className:"flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-full cursor-pointer",children:[e.jsx(ae,{className:"h-4 w-4"}),e.jsxs("span",{children:[v.find(a=>a.value===l.fieldName)?.label||l.fieldName," ",V.find(a=>a.value===l.operator)?.label||l.operator,' "',String(l.value),'"']}),e.jsx(n,{size:"icon",variant:"ghost",onClick:a=>{a.stopPropagation(),M(r)},className:"ml-1 h-5 w-5",children:e.jsx(p,{className:"h-4 w-4"})})]})}),e.jsxs(k,{className:"w-80 p-4 flex flex-col gap-3",children:[e.jsx(A,{placeholder:"Search field...",value:E,onChange:a=>I(a.target.value),className:"mb-2"}),e.jsxs(o,{value:s.fieldName||"",onValueChange:a=>t(d=>({...d,fieldName:a})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Field"})}),e.jsx(x,{children:O.map(a=>e.jsx(c,{value:a.value,children:a.label},a.value))})]}),e.jsxs(o,{value:s.operator||"",onValueChange:a=>t(d=>({...d,operator:a})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Operator"})}),e.jsx(x,{children:V.map(a=>e.jsx(c,{value:a.value,children:a.label},a.value))})]}),e.jsx(A,{className:"w-full",placeholder:"Value",value:typeof s.value=="string"?s.value:s.value===void 0?"":String(s.value),onChange:a=>t(d=>({...d,value:a.target.value}))}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(n,{onClick:B,size:"sm",className:"bg-primary text-primary-foreground",children:"Save"}),e.jsx(n,{onClick:()=>S(null),size:"icon",variant:"ghost",children:e.jsx(p,{className:"h-4 w-4"})})]})]})]},r)),e.jsx(R,{children:e.jsxs(F,{open:G,onOpenChange:N,children:[e.jsxs(H,{children:[e.jsx(U,{asChild:!0,children:e.jsx(z,{asChild:!0,children:e.jsx(n,{size:"icon",variant:"secondary",className:"rounded-full",children:e.jsx(Y,{className:"h-4 w-4"})})})}),e.jsx(q,{children:"Add Filter"})]}),e.jsxs(k,{className:"w-80 p-4 flex flex-col gap-3",children:[e.jsx(A,{placeholder:"Search field...",value:E,onChange:l=>I(l.target.value),className:"mb-2"}),e.jsxs(o,{value:s.fieldName||"",onValueChange:l=>t(r=>({...r,fieldName:l})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Field"})}),e.jsx(x,{children:O.map(l=>e.jsx(c,{value:l.value,children:l.label},l.value))})]}),e.jsxs(o,{value:s.operator||"",onValueChange:l=>t(r=>({...r,operator:l})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Operator"})}),e.jsx(x,{children:V.map(l=>e.jsx(c,{value:l.value,children:l.label},l.value))})]}),e.jsx(A,{className:"w-full",placeholder:"Value",value:typeof s.value=="string"?s.value:s.value===void 0?"":String(s.value),onChange:l=>t(r=>({...r,value:l.target.value}))}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(n,{onClick:B,size:"sm",className:"bg-primary text-primary-foreground",children:"Add"}),e.jsx(n,{onClick:()=>N(!1),size:"icon",variant:"ghost",children:e.jsx(p,{className:"h-4 w-4"})})]})]})]})})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[g.map((l,r)=>e.jsxs(F,{open:y===r,onOpenChange:a=>a?K(r):C(null),children:[e.jsx(z,{asChild:!0,children:e.jsxs(_,{className:"flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-full cursor-pointer",children:[v.find(a=>a.value===l.field)?.label||l.field,l.direction==="ASC"?e.jsx(Z,{className:"h-4 w-4 ml-1"}):e.jsx($,{className:"h-4 w-4 ml-1"}),e.jsx(n,{size:"icon",variant:"ghost",onClick:a=>{a.stopPropagation(),X(r)},className:"ml-1 h-5 w-5",children:e.jsx(p,{className:"h-4 w-4"})})]})}),e.jsxs(k,{className:"w-72 p-4 flex flex-col gap-3",children:[e.jsxs(o,{value:i.field||"",onValueChange:a=>j(d=>({...d,field:a})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Field"})}),e.jsx(x,{children:v.map(a=>e.jsx(c,{value:a.value,children:a.label},a.value))})]}),e.jsxs(o,{value:i.direction||"",onValueChange:a=>j(d=>({...d,direction:a})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Direction"})}),e.jsxs(x,{children:[e.jsx(c,{value:"ASC",children:"Ascending"}),e.jsx(c,{value:"DESC",children:"Descending"})]})]}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(n,{onClick:L,size:"sm",className:"bg-primary text-primary-foreground",children:"Save"}),e.jsx(n,{onClick:()=>C(null),size:"icon",variant:"ghost",children:e.jsx(p,{className:"h-4 w-4"})})]})]})]},r)),e.jsx(R,{children:e.jsxs(F,{open:J,onOpenChange:w,children:[e.jsxs(H,{children:[e.jsx(U,{asChild:!0,children:e.jsx(z,{asChild:!0,children:e.jsx(n,{size:"icon",variant:"secondary",className:"rounded-full",children:e.jsx(ee,{className:"h-4 w-4"})})})}),e.jsx(q,{children:"Add Sort"})]}),e.jsxs(k,{className:"w-72 p-4 flex flex-col gap-3",children:[e.jsxs(o,{value:i.field||"",onValueChange:l=>j(r=>({...r,field:l})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Field"})}),e.jsx(x,{children:v.map(l=>e.jsx(c,{value:l.value,children:l.label},l.value))})]}),e.jsxs(o,{value:i.direction||"",onValueChange:l=>j(r=>({...r,direction:l})),children:[e.jsx(u,{className:"w-full",children:e.jsx(h,{placeholder:"Direction"})}),e.jsxs(x,{children:[e.jsx(c,{value:"ASC",children:"Ascending"}),e.jsx(c,{value:"DESC",children:"Descending"})]})]}),e.jsxs("div",{className:"flex gap-2 justify-end",children:[e.jsx(n,{onClick:L,size:"sm",className:"bg-primary text-primary-foreground",children:"Add"}),e.jsx(n,{onClick:()=>w(!1),size:"icon",variant:"ghost",children:e.jsx(p,{className:"h-4 w-4"})})]})]})]})})]}),T&&e.jsx("div",{className:"ml-auto flex items-center",children:T})]})};export{oe as F};
//# sourceMappingURL=filter-sort-bar-MpsapXP_.js.map
