import{j as e}from"./vendor-6tJeyfYI.js";import{l}from"./app-layout-rNt37hVL.js";function s({className:t,...a}){return e.jsx("div",{"data-slot":"table-wrapper",className:"relative w-full overflow-auto",children:e.jsx("table",{"data-slot":"table",className:l("w-full caption-bottom text-foreground text-sm",t),...a})})}function d({className:t,...a}){return e.jsx("thead",{"data-slot":"table-header",className:l("[&_tr]:border-b",t),...a})}function n({className:t,...a}){return e.jsx("tbody",{"data-slot":"table-body",className:l("[&_tr:last-child]:border-0",t),...a})}function b({className:t,...a}){return e.jsx("tr",{"data-slot":"table-row",className:l("border-b transition-colors [&:has(td):hover]:bg-muted/50 data-[state=selected]:bg-muted",t),...a})}function c({className:t,...a}){return e.jsx("th",{"data-slot":"table-head",className:l("h-12 px-4 text-left rtl:text-right align-middle font-normal text-muted-foreground [&:has([role=checkbox])]:pe-0",t),...a})}function i({className:t,...a}){return e.jsx("td",{"data-slot":"table-cell",className:l("p-4 align-middle [&:has([role=checkbox])]:pe-0",t),...a})}export{s as T,d as a,b,c,n as d,i as e};
//# sourceMappingURL=table-BKSoE52x.js.map
