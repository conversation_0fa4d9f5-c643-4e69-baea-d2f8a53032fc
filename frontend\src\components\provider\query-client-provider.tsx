import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import * as React from 'react';

interface ReactQueryProvidersProps {
  children: React.ReactNode
}

function ReactQueryProviders({ children }: ReactQueryProvidersProps) {
  // Use useState to ensure a single QueryClient instance per provider
  const [queryClient] = React.useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 10 * 1000, // 10s
        refetchOnWindowFocus: true,
      },
    },
  }));

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

export default ReactQueryProviders
