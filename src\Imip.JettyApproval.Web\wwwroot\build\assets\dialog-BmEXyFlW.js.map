{"version": 3, "file": "dialog-BmEXyFlW.js", "sources": ["../../../../../frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { cn } from '@/lib/utils';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { X } from 'lucide-react';\nimport { Dialog as DialogPrimitive } from 'radix-ui';\nimport * as React from 'react';\n\nconst dialogContentVariants = cva(\n  'flex flex-col fixed outline-0 z-50 border border-border bg-background p-6 shadow-lg shadow-black/5 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg',\n  {\n    variants: {\n      size: {\n        sm: \"sm:max-w-sm\",\n        md: \"sm:max-w-md\",\n        lg: \"sm:max-w-lg\",\n        xl: \"sm:max-w-xl\",\n        \"2xl\": \"sm:max-w-2xl\",\n        \"3xl\": \"sm:max-w-3xl\",\n        \"4xl\": \"sm:max-w-4xl\",\n        \"5xl\": \"sm:max-w-5xl\",\n        \"6xl\": \"sm:max-w-6xl\",\n        \"7xl\": \"sm:max-w-7xl\",\n        full: \"sm:max-w-full\",\n      },\n      variant: {\n        default: 'left-[50%] top-[50%] max-w-lg translate-x-[-50%] translate-y-[-50%] w-full',\n        fullscreen: 'inset-5',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: \"lg\",\n    },\n  },\n);\n\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\n}\n\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\n}\n\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return (\n    <DialogPrimitive.Portal\n      data-slot=\"dialog-portal\"\n      {...props}\n    />\n  );\n}\n\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\n}\n\nfunction DialogOverlay({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        'fixed inset-0 z-50 bg-black/30 [backdrop-filter:blur(4px)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction DialogContent({\n  className,\n  children,\n  close = true,\n  overlay = true,\n  variant,\n  size,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> &\n  VariantProps<typeof dialogContentVariants> & {\n    close?: boolean;\n    overlay?: boolean;\n  }) {\n  return (\n    <DialogPortal>\n      {overlay && <DialogOverlay />}\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(dialogContentVariants({ variant, size }), className)}\n        {...props}\n      >\n        {children}\n        {close && (\n          <DialogClose className=\"cursor-pointer outline-0 absolute end-5 top-5 rounded-sm opacity-60 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n            <X className=\"size-4\" />\n            <span className=\"sr-only\">Close</span>\n          </DialogClose>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  );\n}\n\nexport default DialogContent;\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    data-slot=\"dialog-header\"\n    className={cn('flex flex-col space-y-1 text-center sm:text-start mb-5', className)}\n    {...props}\n  />\n);\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    data-slot=\"dialog-footer\"\n    className={cn('flex flex-col-reverse sm:flex-row sm:justify-end pt-5 sm:space-x-2.5', className)}\n    {...props}\n  />\n);\n\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn('text-lg font-semibold leading-none tracking-tight', className)}\n      {...props}\n    />\n  );\n}\n\nconst DialogBody = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div data-slot=\"dialog-body\" className={cn('grow', className)} {...props} />\n);\n\nfunction DialogDescription({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Dialog,\n  DialogBody,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger\n};\n\n"], "names": ["dialogContentVariants", "cva", "Dialog", "props", "DialogPrimitive.Root", "DialogTrigger", "DialogPrimitive.Trigger", "DialogPortal", "jsx", "DialogPrimitive.Portal", "DialogClose", "DialogPrimitive.Close", "DialogOverlay", "className", "DialogPrimitive.Overlay", "cn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "close", "overlay", "variant", "size", "jsxs", "DialogPrimitive.Content", "X", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogPrimitive.Title", "DialogDescription", "DialogPrimitive.Description"], "mappings": "2LAQA,MAAMA,EAAwBC,EAC5B,qTACA,CACE,SAAU,CACR,KAAM,CACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,GAAI,cACJ,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,MAAO,eACP,KAAM,eACR,EACA,QAAS,CACP,QAAS,6EACT,WAAY,SAAA,CAEhB,EACA,gBAAiB,CACf,QAAS,UACT,KAAM,IAAA,CACR,CAEJ,EAEA,SAASC,EAAO,CAAE,GAAGC,GAA4D,CAC/E,aAAQC,EAAA,CAAqB,YAAU,SAAU,GAAGD,EAAO,CAC7D,CAEA,SAASE,EAAc,CAAE,GAAGF,GAA+D,CACzF,aAAQG,EAAA,CAAwB,YAAU,iBAAkB,GAAGH,EAAO,CACxE,CAEA,SAASI,EAAa,CAAE,GAAGJ,GAA8D,CAErF,OAAAK,EAAA,IAACC,EAAA,CACC,YAAU,gBACT,GAAGN,CAAA,CACN,CAEJ,CAEA,SAASO,EAAY,CAAE,GAAGP,GAA6D,CACrF,aAAQQ,EAAA,CAAsB,YAAU,eAAgB,GAAGR,EAAO,CACpE,CAEA,SAASS,EAAc,CAAE,UAAAC,EAAW,GAAGV,GAA+D,CAElG,OAAAK,EAAA,IAACM,EAAA,CACC,YAAU,iBACV,UAAWC,EACT,qLACAF,CACF,EACC,GAAGV,CAAA,CACN,CAEJ,CAEA,SAASa,EAAc,CACrB,UAAAH,EACA,SAAAI,EACA,MAAAC,EAAQ,GACR,QAAAC,EAAU,GACV,QAAAC,EACA,KAAAC,EACA,GAAGlB,CACL,EAIK,CACH,cACGI,EACE,CAAA,SAAA,CAAAY,SAAYP,EAAc,EAAA,EAC3BU,EAAA,KAACC,EAAA,CACC,YAAU,iBACV,UAAWR,EAAGf,EAAsB,CAAE,QAAAoB,EAAS,KAAAC,CAAK,CAAC,EAAGR,CAAS,EAChE,GAAGV,EAEH,SAAA,CAAAc,EACAC,GACCI,EAAA,KAACZ,EAAY,CAAA,UAAU,wPACrB,SAAA,CAACF,EAAAA,IAAAgB,EAAA,CAAE,UAAU,QAAS,CAAA,EACrBhB,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAK,OAAA,CAAA,CAAA,CACjC,CAAA,CAAA,CAAA,CAAA,CAEJ,EACF,CAEJ,CAIA,MAAMiB,EAAe,CAAC,CAAE,UAAAZ,EAAW,GAAGV,CACpC,IAAAK,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWO,EAAG,yDAA0DF,CAAS,EAChF,GAAGV,CAAA,CACN,EAGIuB,EAAe,CAAC,CAAE,UAAAb,EAAW,GAAGV,CACpC,IAAAK,EAAA,IAAC,MAAA,CACC,YAAU,gBACV,UAAWO,EAAG,uEAAwEF,CAAS,EAC9F,GAAGV,CAAA,CACN,EAGF,SAASwB,EAAY,CAAE,UAAAd,EAAW,GAAGV,GAA6D,CAE9F,OAAAK,EAAA,IAACoB,EAAA,CACC,YAAU,eACV,UAAWb,EAAG,oDAAqDF,CAAS,EAC3E,GAAGV,CAAA,CACN,CAEJ,CAMA,SAAS0B,EAAkB,CAAE,UAAAhB,EAAW,GAAGV,GAAmE,CAE1G,OAAAK,EAAA,IAACsB,EAAA,CACC,YAAU,qBACV,UAAWf,EAAG,gCAAiCF,CAAS,EACvD,GAAGV,CAAA,CACN,CAEJ"}