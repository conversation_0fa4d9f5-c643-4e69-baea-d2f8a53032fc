import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuItem, DropdownMenuLabel } from '@/components/ui/dropdown-menu';
import { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Jetty {
  id: string;
  location: string;
  jettyName: string;
  boundedZone: 'Yes' | 'No';
}

const mockJettyData: Jetty[] = Array(10).fill(null).map((_, i) => ({
  id: `jetty_${i + 1}`,
  location: `Fatufia ${i % 2 === 0 ? 'A' : 'B'}`,
  jettyName: `F${i + 1}`,
  boundedZone: i % 3 === 0 ? 'Yes' : 'No',
}));

export default function ManageJetty() {
  const [filter, setFilter] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [visibleColumns, setVisibleColumns] = useState<Set<keyof Jetty>>(
    new Set(Object.keys(mockJettyData[0]) as (keyof Jetty)[])
  );
  const [isManageJettyDialogOpen, setIsManageJettyDialogOpen] = useState(false);
  const [currentJetty, setCurrentJetty] = useState<Jetty | null>(null);
  const [jettyLocation, setJettyLocation] = useState('');
  const [jettyName, setJettyName] = useState('');
  const [boundedZone, setBoundedZone] = useState<'Yes' | 'No'>('No');

  const filteredData = mockJettyData.filter(jetty =>
    Object.values(jetty).some(value =>
      value.toString().toLowerCase().includes(filter.toLowerCase())
    )
  );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(filteredData.map(row => row.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(id);
    } else {
      newSelection.delete(id);
    }
    setSelectedRows(newSelection);
  };

  const handleToggleColumn = (columnKey: keyof Jetty, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const handleCreateNewJettyClick = () => {
    setCurrentJetty(null);
    setJettyLocation('');
    setJettyName('');
    setBoundedZone('No');
    setIsManageJettyDialogOpen(true);
  };

  const handleUpdateJettyClick = (jetty: Jetty) => {
    setCurrentJetty(jetty);
    setJettyLocation(jetty.location);
    setJettyName(jetty.jettyName);
    setBoundedZone(jetty.boundedZone);
    setIsManageJettyDialogOpen(true);
  };

  const handleDeleteJetty = (id: string) => {
    console.log(`Deleting jetty with ID: ${id}`);
    // Implement actual delete logic here
  };

  const handleSaveJetty = () => {
    if (currentJetty) {
      console.log(`Updating jetty: ${currentJetty.id}, Location: ${jettyLocation}, Jetty: ${jettyName}, Bounded Zone: ${boundedZone}`);
      // Implement actual update logic here
    } else {
      console.log(`Creating new jetty: Location: ${jettyLocation}, Jetty: ${jettyName}, Bounded Zone: ${boundedZone}`);
      // Implement actual create logic here
    }
    setIsManageJettyDialogOpen(false);
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Manage Jetty</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <Input
                placeholder="Filter lines..."
                value={filter}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
                className="max-w-sm"
              />
              <div className="flex space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline">
                      Columns <IconChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {Object.keys(mockJettyData[0]).map((key) => (
                      <DropdownMenuCheckboxItem
                        key={key}
                        className="capitalize"
                        checked={visibleColumns.has(key as keyof Jetty)}
                        onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof Jetty, checked === true)}
                      >
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </DropdownMenuCheckboxItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button onClick={handleCreateNewJettyClick}>Create New Jetty</Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]">
                      <Checkbox
                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}
                        onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}
                      />
                    </TableHead>
                    {Object.keys(mockJettyData[0]).map((key) => (visibleColumns.has(key as keyof Jetty) && key !== 'id' &&
                      <TableHead key={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </TableHead>
                    ))}
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((jetty) => (
                    <TableRow key={jetty.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRows.has(jetty.id)}
                          onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(jetty.id, checked === true)}
                        />
                      </TableCell>
                      {Object.entries(jetty).map(([key, value]) => (visibleColumns.has(key as keyof Jetty) && key !== 'id' &&
                        <TableCell key={key}>{value}</TableCell>
                      ))}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <IconDotsVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleUpdateJettyClick(jetty)}>
                              Update
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteJetty(jetty.id)}>
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                {selectedRows.size} of {filteredData.length} row(s) selected.
              </div>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Previous</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Manage Jetty Dialog */}
      <Dialog open={isManageJettyDialogOpen} onOpenChange={setIsManageJettyDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{currentJetty ? 'Update Jetty' : 'Create New Jetty'}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">Location</Label>
              <Input
                id="location"
                value={jettyLocation}
                onChange={(e) => setJettyLocation(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="jettyName" className="text-right">Jetty</Label>
              <Input
                id="jettyName"
                value={jettyName}
                onChange={(e) => setJettyName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="boundedZone" className="text-right">Bounded Z</Label>
              <Select onValueChange={(value: 'Yes' | 'No') => setBoundedZone(value)} value={boundedZone}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select Bounded Zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSaveJetty} className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AppLayout>
  );
}
