import{u as te,a as s,h as ae,f as se,j as r}from"./vendor-6tJeyfYI.js";import{u as re,a as oe,B as ne,g as ie,A as ue}from"./app-layout-rNt37hVL.js";import{f as u,c as K,r as de,a as le,b as ce,A as pe}from"./handsontable-renderer-bnhdgeQj.js";import{D as me}from"./DocumentPreviewDialog-C1lfiEeE.js";import{H as ge,r as ve}from"./ht-theme-main.min-DuylQxQp.js";import{q as ye,$ as fe}from"./App-DnhJzTNn.js";/* empty css                         */import"./radix-e4nK4mWk.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";import"./useJettyDataWithFilter-CK58-c0U.js";import"./filter-sort-bar-MpsapXP_.js";import"./plus-PD53KOti.js";import"./arrow-up-DDQ17ADi.js";import"./dialog-BmEXyFlW.js";import"./table-BKSoE52x.js";import"./TableSkeleton-CIQBoxBh.js";import"./skeleton-DAOxGMKm.js";ve();const De=()=>{const{props:g}=ye(),o=typeof g.id=="string"?g.id:void 0,{toast:v}=re(),d=te(),Q=s.useRef(null),[y,f]=s.useState(""),[l,D]=s.useState(""),[h,N]=s.useState(""),[w,S]=s.useState(""),[C,j]=s.useState(""),[x,b]=s.useState(""),[P,A]=s.useState(""),[O,E]=s.useState(""),[R,q]=s.useState(""),[T,I]=s.useState(""),[B,M]=s.useState(""),[H,V]=s.useState(""),[n,F]=s.useState([]),[L,z]=s.useState(null),[W,J]=s.useState(!1),[$,U]=s.useState(""),[c,X]=s.useState(new Map),G=e=>{U(e),J(!0)},p=(e,a)=>{X(ee=>{const m=new Map(ee);return a?m.set(e,!0):m.delete(e),m})},{data:t,isLoading:Y,error:k}=ae({queryKey:["jetty-request",o],enabled:!!o,queryFn:async()=>{if(!o)throw new Error("No ID provided");const{data:e,error:a}=await ie({path:{id:o}});if(a)throw new Error(a.error?.message||"Failed to load application");return e},retry:1,retryDelay:1e3});s.useEffect(()=>{t&&(f(String(t.docNum??"")),D(t.vesselType??""),N(t.voyage??""),S(t.jetty??""),j(u(t.arrivalDate)),b(u(t.departureDate)),A(u(t.asideDate)),E(u(t.castOfDate)),q(t.postDate?t.postDate.split("T")[0]:""),I(t.portOrigin??""),M(t.destinationPort??""),V(t.barge??""),z({id:void 0,docEntry:void 0,vesselName:t.vesselName??"",voyage:t.voyage??"",vesselArrival:t.arrivalDate??"",vesselDeparture:t.departureDate??"",vesselType:t.vesselType??"",items:void 0,cargo:void 0,barge:void 0,jetty:t.jetty?{id:t.jetty,name:t.jetty}:void 0,portOrigin:void 0,destinationPort:void 0,berthingDate:void 0,anchorageDate:void 0,unloadingDate:void 0,finishUnloadingDate:void 0,grtWeight:void 0,agentName:void 0}),F((t.items||[]).map(e=>({tenantName:e.tenantName??"",itemName:e.itemName??"",quantity:String(e.qty??""),uom:e.uoM??"",remark:e.notes??"",status:e.status??"Draft",letterNo:e.letterNo??"",letterDate:e.letterDate??"",id:e.id,preview:"",submit:"",delete:""}))))},[t]);const Z=K.map(e=>e.data==="id"?{...e,renderer:de(n,G,c,p)}:e.data==="submit"?{...e,renderer:le(n,l,c,p,d,o??"")}:e.data==="delete"?{...e,renderer:ce(n,F,c,p,d,o??"")}:e),i=se({mutationFn:async e=>{if(!o)throw new Error("No ID provided");const{error:a}=await oe({path:{id:o},body:e});if(a)throw a},onSuccess:()=>{v({title:"Success",description:"Application updated.",variant:"success"}),d.invalidateQueries({queryKey:["jetty-request",o]})},onError:e=>{v({title:e instanceof Error?e.message:e?.error?.message||"Error",description:e instanceof Error?void 0:e?.error?.details,variant:"destructive"})}}),_=()=>{if(i.isPending)return;const e={docNum:Number(y),vesselType:l,vesselName:L?.vesselName??"",voyage:h,jetty:w,arrivalDate:C,departureDate:x,asideDate:P,castOfDate:O,postDate:R,portOrigin:T,destinationPort:B,barge:H,referenceId:t?.referenceId,items:n.map(a=>({tenantName:a.tenantName,itemName:a.itemName,qty:Number(a.quantity)||0,uoM:a.uom,id:a.id,notes:a.remark,letterNo:a.letterNo,letterDate:a.letterDate,status:a.status==="Draft"?0:a.status==="Open"?1:a.status==="Submit"?2:a.status==="Approve"?3:a.status==="Reject"?4:0}))};i.mutate(e)};return Y?r.jsx("div",{className:"p-8",children:"Loading..."}):k?r.jsx("div",{className:"p-8 text-red-500",children:k.message}):r.jsxs("div",{className:"container mx-auto",children:[r.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4",children:[r.jsx(pe,{docNum:y,vesselType:l,vessel:L,voyage:h,jetty:w,arrivalDate:C,departureDate:x,asideDate:P,castOfDate:O,postDate:R,portOrigin:T,destinationPort:B,barge:H,onDocNumChange:f,onVesselTypeChange:D,onVesselChange:z,onVoyageChange:N,onJettyChange:S,onArrivalDateChange:j,onDepartureDateChange:b,onAsideDateChange:A,onCastOfDateChange:E,onPostDateChange:q,onPortOriginChange:I,onDestinationPortChange:M,onBargeChange:V,title:"Edit Application"}),r.jsx("div",{style:{maxWidth:"100%",overflowX:"auto"},className:"mb-8",children:r.jsx(ge,{ref:Q,themeName:"ht-theme-main",data:n,columns:Z,colHeaders:K.map(e=>e.title),rowHeaders:!0,height:"50vh",rowHeights:27,currentRowClassName:"currentRow",currentColClassName:"currentCol",licenseKey:"non-commercial-and-evaluation",stretchH:"all",contextMenu:!0,manualColumnResize:!0,manualRowResize:!0,autoColumnSize:!1,autoRowSize:!1,startRows:1,viewportRowRenderingOffset:1e3,viewportColumnRenderingOffset:100,dropdownMenu:!0,filters:!0,colWidths:80,width:"100%",persistentState:!0})}),r.jsx("div",{className:"flex justify-end",children:r.jsx(ne,{onClick:_,disabled:i.isPending,className:"px-6 py-2 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed",children:i.isPending?"Saving...":"Save Changes"})})]}),r.jsx(me,{isOpen:W,onOpenChange:J,documentSrc:$})]})},ke=()=>r.jsxs(ue,{children:[r.jsx(fe,{title:"Edit Application"}),r.jsx(De,{})]});export{ke as default};
//# sourceMappingURL=page-DK8DZOYP.js.map
