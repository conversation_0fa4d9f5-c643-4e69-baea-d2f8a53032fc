import * as z from 'zod';
import type { CreateUpdateVesselItemDto } from '@/clientEkb/types.gen';

export const importVesselItemSchema = z.object({
  itemName: z.string().nullable().optional(),
  itemQty: z.number().nullable().optional(),
  unitQty: z.string().nullable().optional(),
  remarks: z.string().nullable().optional(),
  tenant: z.string().nullable().optional(),
  tenantId: z.string().nullable().optional(),
  businessPartner: z.string().nullable().optional(),
  businessPartnerId: z.string().nullable().optional(),
  // Add more fields as needed from CreateUpdateVesselItemDto
});

export type ImportVesselItemForm = z.infer<typeof importVesselItemSchema> & Partial<CreateUpdateVesselItemDto>; 