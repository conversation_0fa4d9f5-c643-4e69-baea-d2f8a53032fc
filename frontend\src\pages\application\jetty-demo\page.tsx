import { JettySelector } from "@/components/applications/jetty-selector";
import AppLayout from "@/layouts/app-layout";
import { useState } from "react";

const JettyDemoPage: React.FC = () => {
  const [selectedJetty, setSelectedJetty] = useState<string>('');

  return (
    <AppLayout>
      <div className="flex flex-col space-y-6 p-6">
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">Jetty Selector Demo</h1>
          <p className="text-muted-foreground">
            This demonstrates the custom JettySelector component with debounced filtering.
            Try typing in the search box to filter jetties from the database.
          </p>
        </div>

        <div className="max-w-md space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Jetty</label>
            <JettySelector
              value={selectedJetty}
              onValueChange={setSelectedJetty}
              placeholder="Search and select a jetty..."
            />
          </div>

          {selectedJetty && (
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-medium mb-2">Selected Jetty:</h3>
              <p className="text-sm">{selectedJetty}</p>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default JettyDemoPage; 