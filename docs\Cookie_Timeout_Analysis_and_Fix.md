# Cookie Timeout Analysis and Fix

## 🔍 Problem Analysis

You were experiencing authentication redirect failures after approximately 1 hour of inactivity. The analysis revealed that while your main authentication cookies were set to 8 hours, there was a critical mismatch in the **EkbApiToken** cookie duration.

## 🚨 Root Cause Identified

### **EkbApiToken Cookie - 1 Hour Expiration**
The `EkbApiCookieMiddleware` was setting the EKB API token cookie to expire after only **1 hour**:

```csharp
// BEFORE (Problem)
Expires = DateTimeOffset.UtcNow.AddHours(1), // Only 1 hour!
```

This caused authentication failures when:
1. User stays inactive for more than 1 hour
2. Frontend tries to make API calls to EKB services
3. EkbApiToken cookie has expired
4. Redirect attempts fail due to token mismatch

## 🛠️ Solutions Implemented

### **1. Extended EkbApiToken Duration**
**File**: `src/Imip.JettyApproval.Web/Middleware/EkbdApiCookieMiddleware.cs`

```csharp
// AFTER (Fixed)
Expires = DateTimeOffset.UtcNow.AddHours(8), // Match authentication cookie duration
```

**Impact**: EKB API token now matches the 8-hour authentication cookie duration.

### **2. Improved Language Cookie Duration**
**File**: `frontend/src/components/layout/language-switcher.tsx`

```typescript
// BEFORE
max-age=31536000  // 1 year (excessive)

// AFTER  
max-age=2592000   // 30 days (more reasonable)
```

**Impact**: Reduced unnecessary long-term storage and improved privacy.

### **3. Enhanced Token Refresh Error Handling**
**File**: `src/Imip.JettyApproval.Web/Middleware/TokenRefreshMiddleware.cs`

**Improvements**:
- Better AJAX request detection
- Proper JSON error responses for API calls
- Improved redirect URL handling with return URL preservation
- Added proper using statements

```csharp
// Check if this is an AJAX request
if (context.Request.Headers.ContainsKey("X-Requested-With") && 
    context.Request.Headers["X-Requested-With"] == "XMLHttpRequest")
{
    // Return 401 for AJAX requests
    context.Response.StatusCode = 401;
    context.Response.ContentType = "application/json";
    await context.Response.WriteAsync("{\"error\":\"Authentication required\",\"redirectUrl\":\"/Account/Login\"}");
    return;
}

// Token refresh failed, redirect to login for regular requests
var returnUrl = context.Request.Path + context.Request.QueryString;
context.Response.Redirect($"/Account/Login?returnUrl={Uri.EscapeDataString(returnUrl)}");
```

## 📊 Current Cookie Configuration Summary

| Cookie Type | Duration | Purpose | Sliding Expiration |
|-------------|----------|---------|-------------------|
| Authentication Cookie | 8 hours | Main user session | ✅ Yes |
| Session Cookie | 8 hours | Server-side session | ✅ Yes |
| EkbApiToken | 8 hours | EKB API access | ❌ No |
| Antiforgery Cookie | 2 hours | CSRF protection | ❌ No |
| Language Cookie | 30 days | UI language preference | ❌ No |

## 🔄 Token Refresh Strategy

The application maintains a multi-layered token refresh approach:

1. **Background Refresh**: Every 5 minutes via JavaScript
2. **Activity-Based Refresh**: On user interaction (mouse, keyboard, scroll)
3. **Visibility-Based Refresh**: When page becomes visible
4. **Error-Based Refresh**: On 401 responses
5. **Middleware Refresh**: Server-side token validation and refresh

## ✅ Expected Results

After these changes:
- ✅ Users can stay logged in for up to 8 hours without issues
- ✅ EKB API calls will work consistently throughout the session
- ✅ Better error handling for authentication failures
- ✅ Improved redirect behavior with return URL preservation
- ✅ More reasonable language preference storage

## 🧪 Testing Recommendations

1. **Test 1-Hour Inactivity**: Leave the application idle for 1+ hours and verify functionality
2. **Test EKB API Calls**: Ensure EKB-related features work after extended inactivity
3. **Test AJAX Requests**: Verify proper error handling for expired sessions
4. **Test Language Switching**: Confirm language preferences persist appropriately

## 🔧 Additional Monitoring

Consider adding logging to track:
- Cookie expiration events
- Token refresh success/failure rates
- Authentication redirect patterns
- EKB API token usage patterns

## 📝 Notes

- The main authentication cookie still uses sliding expiration, so active users will stay logged in
- EKB API token is now aligned with the main session duration
- Language preferences are stored for a more reasonable 30-day period
- Enhanced error handling provides better user experience during authentication issues
