{"version": 3, "file": "scroll-area-DuGBN-Ug.js", "sources": ["../../../../../frontend/node_modules/.pnpm/@radix-ui+react-scroll-area_b5d13464c21f88022505288dd3198025/node_modules/@radix-ui/react-scroll-area/dist/index.mjs", "../../../../../frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\n\n// src/scroll-area.tsx\nimport * as React2 from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { Presence } from \"@radix-ui/react-presence\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport { useCallbackRef } from \"@radix-ui/react-use-callback-ref\";\nimport { useDirection } from \"@radix-ui/react-direction\";\nimport { useLayoutEffect } from \"@radix-ui/react-use-layout-effect\";\nimport { clamp } from \"@radix-ui/number\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\n\n// src/use-state-machine.ts\nimport * as React from \"react\";\nfunction useStateMachine(initialState, machine) {\n  return React.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/scroll-area.tsx\nimport { Fragment, jsx, jsxs } from \"react/jsx-runtime\";\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = createContextScope(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = React2.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeScrollArea,\n      type = \"hover\",\n      dir,\n      scrollHideDelay = 600,\n      ...scrollAreaProps\n    } = props;\n    const [scrollArea, setScrollArea] = React2.useState(null);\n    const [viewport, setViewport] = React2.useState(null);\n    const [content, setContent] = React2.useState(null);\n    const [scrollbarX, setScrollbarX] = React2.useState(null);\n    const [scrollbarY, setScrollbarY] = React2.useState(null);\n    const [cornerWidth, setCornerWidth] = React2.useState(0);\n    const [cornerHeight, setCornerHeight] = React2.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = React2.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = React2.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setScrollArea(node));\n    const direction = useDirection(dir);\n    return /* @__PURE__ */ jsx(\n      ScrollAreaProvider,\n      {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n              position: \"relative\",\n              // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n              [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n              [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n              ...props.style\n            }\n          }\n        )\n      }\n    );\n  }\n);\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = React2.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ jsxs(Fragment, { children: [\n      /* @__PURE__ */ jsx(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          \"data-radix-scroll-area-viewport\": \"\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */\n            overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n            overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n            ...props.style\n          },\n          children: /* @__PURE__ */ jsx(\"div\", { ref: context.onContentChange, style: { minWidth: \"100%\", display: \"table\" }, children })\n        }\n      )\n    ] });\n  }\n);\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    React2.useEffect(() => {\n      isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n      return () => {\n        isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n      };\n    }, [isHorizontal, onScrollbarXEnabledChange, onScrollbarYEnabledChange]);\n    return context.type === \"hover\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarHover, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"scroll\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarScroll, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"auto\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarAuto, { ...scrollbarProps, ref: forwardedRef, forceMount }) : context.type === \"always\" ? /* @__PURE__ */ jsx(ScrollAreaScrollbarVisible, { ...scrollbarProps, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [visible, setVisible] = React2.useState(false);\n  React2.useEffect(() => {\n    const scrollArea = context.scrollArea;\n    let hideTimer = 0;\n    if (scrollArea) {\n      const handlePointerEnter = () => {\n        window.clearTimeout(hideTimer);\n        setVisible(true);\n      };\n      const handlePointerLeave = () => {\n        hideTimer = window.setTimeout(() => setVisible(false), context.scrollHideDelay);\n      };\n      scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n      scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n      return () => {\n        window.clearTimeout(hideTimer);\n        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n      };\n    }\n  }, [context.scrollArea, context.scrollHideDelay]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarAuto,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarScroll = React2.forwardRef((props, forwardedRef) => {\n  const { forceMount, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const debounceScrollEnd = useDebounceCallback(() => send(\"SCROLL_END\"), 100);\n  const [state, send] = useStateMachine(\"hidden\", {\n    hidden: {\n      SCROLL: \"scrolling\"\n    },\n    scrolling: {\n      SCROLL_END: \"idle\",\n      POINTER_ENTER: \"interacting\"\n    },\n    interacting: {\n      SCROLL: \"interacting\",\n      POINTER_LEAVE: \"idle\"\n    },\n    idle: {\n      HIDE: \"hidden\",\n      SCROLL: \"scrolling\",\n      POINTER_ENTER: \"interacting\"\n    }\n  });\n  React2.useEffect(() => {\n    if (state === \"idle\") {\n      const hideTimer = window.setTimeout(() => send(\"HIDE\"), context.scrollHideDelay);\n      return () => window.clearTimeout(hideTimer);\n    }\n  }, [state, context.scrollHideDelay, send]);\n  React2.useEffect(() => {\n    const viewport = context.viewport;\n    const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n    if (viewport) {\n      let prevScrollPos = viewport[scrollDirection];\n      const handleScroll = () => {\n        const scrollPos = viewport[scrollDirection];\n        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n        if (hasScrollInDirectionChanged) {\n          send(\"SCROLL\");\n          debounceScrollEnd();\n        }\n        prevScrollPos = scrollPos;\n      };\n      viewport.addEventListener(\"scroll\", handleScroll);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll);\n    }\n  }, [context.viewport, isHorizontal, send, debounceScrollEnd]);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || state !== \"hidden\", children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n      ...scrollbarProps,\n      ref: forwardedRef,\n      onPointerEnter: composeEventHandlers(props.onPointerEnter, () => send(\"POINTER_ENTER\")),\n      onPointerLeave: composeEventHandlers(props.onPointerLeave, () => send(\"POINTER_LEAVE\"))\n    }\n  ) });\n});\nvar ScrollAreaScrollbarAuto = React2.forwardRef((props, forwardedRef) => {\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const { forceMount, ...scrollbarProps } = props;\n  const [visible, setVisible] = React2.useState(false);\n  const isHorizontal = props.orientation === \"horizontal\";\n  const handleResize = useDebounceCallback(() => {\n    if (context.viewport) {\n      const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n      const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n      setVisible(isHorizontal ? isOverflowX : isOverflowY);\n    }\n  }, 10);\n  useResizeObserver(context.viewport, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(Presence, { present: forceMount || visible, children: /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarVisible,\n    {\n      \"data-state\": visible ? \"visible\" : \"hidden\",\n      ...scrollbarProps,\n      ref: forwardedRef\n    }\n  ) });\n});\nvar ScrollAreaScrollbarVisible = React2.forwardRef((props, forwardedRef) => {\n  const { orientation = \"vertical\", ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const thumbRef = React2.useRef(null);\n  const pointerOffsetRef = React2.useRef(0);\n  const [sizes, setSizes] = React2.useState({\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }\n  });\n  const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n  const commonProps = {\n    ...scrollbarProps,\n    sizes,\n    onSizesChange: setSizes,\n    hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n    onThumbChange: (thumb) => thumbRef.current = thumb,\n    onThumbPointerUp: () => pointerOffsetRef.current = 0,\n    onThumbPointerDown: (pointerPos) => pointerOffsetRef.current = pointerPos\n  };\n  function getScrollPosition(pointerPos, dir) {\n    return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n  }\n  if (orientation === \"horizontal\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarX,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollLeft;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n            thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollLeft = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) {\n            context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n          }\n        }\n      }\n    );\n  }\n  if (orientation === \"vertical\") {\n    return /* @__PURE__ */ jsx(\n      ScrollAreaScrollbarY,\n      {\n        ...commonProps,\n        ref: forwardedRef,\n        onThumbPositionChange: () => {\n          if (context.viewport && thumbRef.current) {\n            const scrollPos = context.viewport.scrollTop;\n            const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n            thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n          }\n        },\n        onWheelScroll: (scrollPos) => {\n          if (context.viewport) context.viewport.scrollTop = scrollPos;\n        },\n        onDragScroll: (pointerPos) => {\n          if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n        }\n      }\n    );\n  }\n  return null;\n});\nvar ScrollAreaScrollbarX = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarXChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"horizontal\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        bottom: 0,\n        left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n        [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.x),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.x),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollLeft + event.deltaX;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollWidth,\n            viewport: context.viewport.offsetWidth,\n            scrollbar: {\n              size: ref.current.clientWidth,\n              paddingStart: toInt(computedStyle.paddingLeft),\n              paddingEnd: toInt(computedStyle.paddingRight)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar ScrollAreaScrollbarY = React2.forwardRef((props, forwardedRef) => {\n  const { sizes, onSizesChange, ...scrollbarProps } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n  const [computedStyle, setComputedStyle] = React2.useState();\n  const ref = React2.useRef(null);\n  const composeRefs = useComposedRefs(forwardedRef, ref, context.onScrollbarYChange);\n  React2.useEffect(() => {\n    if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n  }, [ref]);\n  return /* @__PURE__ */ jsx(\n    ScrollAreaScrollbarImpl,\n    {\n      \"data-orientation\": \"vertical\",\n      ...scrollbarProps,\n      ref: composeRefs,\n      sizes,\n      style: {\n        top: 0,\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: \"var(--radix-scroll-area-corner-height)\",\n        [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n        ...props.style\n      },\n      onThumbPointerDown: (pointerPos) => props.onThumbPointerDown(pointerPos.y),\n      onDragScroll: (pointerPos) => props.onDragScroll(pointerPos.y),\n      onWheelScroll: (event, maxScrollPos) => {\n        if (context.viewport) {\n          const scrollPos = context.viewport.scrollTop + event.deltaY;\n          props.onWheelScroll(scrollPos);\n          if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n            event.preventDefault();\n          }\n        }\n      },\n      onResize: () => {\n        if (ref.current && context.viewport && computedStyle) {\n          onSizesChange({\n            content: context.viewport.scrollHeight,\n            viewport: context.viewport.offsetHeight,\n            scrollbar: {\n              size: ref.current.clientHeight,\n              paddingStart: toInt(computedStyle.paddingTop),\n              paddingEnd: toInt(computedStyle.paddingBottom)\n            }\n          });\n        }\n      }\n    }\n  );\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = React2.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeScrollArea,\n    sizes,\n    hasThumb,\n    onThumbChange,\n    onThumbPointerUp,\n    onThumbPointerDown,\n    onThumbPositionChange,\n    onDragScroll,\n    onWheelScroll,\n    onResize,\n    ...scrollbarProps\n  } = props;\n  const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n  const [scrollbar, setScrollbar] = React2.useState(null);\n  const composeRefs = useComposedRefs(forwardedRef, (node) => setScrollbar(node));\n  const rectRef = React2.useRef(null);\n  const prevWebkitUserSelectRef = React2.useRef(\"\");\n  const viewport = context.viewport;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const handleWheelScroll = useCallbackRef(onWheelScroll);\n  const handleThumbPositionChange = useCallbackRef(onThumbPositionChange);\n  const handleResize = useDebounceCallback(onResize, 10);\n  function handleDragScroll(event) {\n    if (rectRef.current) {\n      const x = event.clientX - rectRef.current.left;\n      const y = event.clientY - rectRef.current.top;\n      onDragScroll({ x, y });\n    }\n  }\n  React2.useEffect(() => {\n    const handleWheel = (event) => {\n      const element = event.target;\n      const isScrollbarWheel = scrollbar?.contains(element);\n      if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n    };\n    document.addEventListener(\"wheel\", handleWheel, { passive: false });\n    return () => document.removeEventListener(\"wheel\", handleWheel, { passive: false });\n  }, [viewport, scrollbar, maxScrollPos, handleWheelScroll]);\n  React2.useEffect(handleThumbPositionChange, [sizes, handleThumbPositionChange]);\n  useResizeObserver(scrollbar, handleResize);\n  useResizeObserver(context.content, handleResize);\n  return /* @__PURE__ */ jsx(\n    ScrollbarProvider,\n    {\n      scope: __scopeScrollArea,\n      scrollbar,\n      hasThumb,\n      onThumbChange: useCallbackRef(onThumbChange),\n      onThumbPointerUp: useCallbackRef(onThumbPointerUp),\n      onThumbPositionChange: handleThumbPositionChange,\n      onThumbPointerDown: useCallbackRef(onThumbPointerDown),\n      children: /* @__PURE__ */ jsx(\n        Primitive.div,\n        {\n          ...scrollbarProps,\n          ref: composeRefs,\n          style: { position: \"absolute\", ...scrollbarProps.style },\n          onPointerDown: composeEventHandlers(props.onPointerDown, (event) => {\n            const mainPointer = 0;\n            if (event.button === mainPointer) {\n              const element = event.target;\n              element.setPointerCapture(event.pointerId);\n              rectRef.current = scrollbar.getBoundingClientRect();\n              prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n              document.body.style.webkitUserSelect = \"none\";\n              if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n              handleDragScroll(event);\n            }\n          }),\n          onPointerMove: composeEventHandlers(props.onPointerMove, handleDragScroll),\n          onPointerUp: composeEventHandlers(props.onPointerUp, (event) => {\n            const element = event.target;\n            if (element.hasPointerCapture(event.pointerId)) {\n              element.releasePointerCapture(event.pointerId);\n            }\n            document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n            if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n            rectRef.current = null;\n          })\n        }\n      )\n    }\n  );\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ jsx(Presence, { present: forceMount || scrollbarContext.hasThumb, children: /* @__PURE__ */ jsx(ScrollAreaThumbImpl, { ref: forwardedRef, ...thumbProps }) });\n  }\n);\nvar ScrollAreaThumbImpl = React2.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = useComposedRefs(\n      forwardedRef,\n      (node) => scrollbarContext.onThumbChange(node)\n    );\n    const removeUnlinkedScrollListenerRef = React2.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(() => {\n      if (removeUnlinkedScrollListenerRef.current) {\n        removeUnlinkedScrollListenerRef.current();\n        removeUnlinkedScrollListenerRef.current = void 0;\n      }\n    }, 100);\n    React2.useEffect(() => {\n      const viewport = scrollAreaContext.viewport;\n      if (viewport) {\n        const handleScroll = () => {\n          debounceScrollEnd();\n          if (!removeUnlinkedScrollListenerRef.current) {\n            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n            removeUnlinkedScrollListenerRef.current = listener;\n            onThumbPositionChange();\n          }\n        };\n        onThumbPositionChange();\n        viewport.addEventListener(\"scroll\", handleScroll);\n        return () => viewport.removeEventListener(\"scroll\", handleScroll);\n      }\n    }, [scrollAreaContext.viewport, debounceScrollEnd, onThumbPositionChange]);\n    return /* @__PURE__ */ jsx(\n      Primitive.div,\n      {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n          width: \"var(--radix-scroll-area-thumb-width)\",\n          height: \"var(--radix-scroll-area-thumb-height)\",\n          ...style\n        },\n        onPointerDownCapture: composeEventHandlers(props.onPointerDownCapture, (event) => {\n          const thumb = event.target;\n          const thumbRect = thumb.getBoundingClientRect();\n          const x = event.clientX - thumbRect.left;\n          const y = event.clientY - thumbRect.top;\n          scrollbarContext.onThumbPointerDown({ x, y });\n        }),\n        onPointerUp: composeEventHandlers(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n      }\n    );\n  }\n);\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = React2.forwardRef(\n  (props, forwardedRef) => {\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ jsx(ScrollAreaCornerImpl, { ...props, ref: forwardedRef }) : null;\n  }\n);\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = React2.forwardRef((props, forwardedRef) => {\n  const { __scopeScrollArea, ...cornerProps } = props;\n  const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n  const [width, setWidth] = React2.useState(0);\n  const [height, setHeight] = React2.useState(0);\n  const hasSize = Boolean(width && height);\n  useResizeObserver(context.scrollbarX, () => {\n    const height2 = context.scrollbarX?.offsetHeight || 0;\n    context.onCornerHeightChange(height2);\n    setHeight(height2);\n  });\n  useResizeObserver(context.scrollbarY, () => {\n    const width2 = context.scrollbarY?.offsetWidth || 0;\n    context.onCornerWidthChange(width2);\n    setWidth(width2);\n  });\n  return hasSize ? /* @__PURE__ */ jsx(\n    Primitive.div,\n    {\n      ...cornerProps,\n      ref: forwardedRef,\n      style: {\n        width,\n        height,\n        position: \"absolute\",\n        right: context.dir === \"ltr\" ? 0 : void 0,\n        left: context.dir === \"rtl\" ? 0 : void 0,\n        bottom: 0,\n        ...props.style\n      }\n    }\n  ) : null;\n});\nfunction toInt(value) {\n  return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n  const ratio = viewportSize / contentSize;\n  return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);\n  return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = () => {\n}) => {\n  let prevPosition = { left: node.scrollLeft, top: node.scrollTop };\n  let rAF = 0;\n  (function loop() {\n    const position = { left: node.scrollLeft, top: node.scrollTop };\n    const isHorizontalScroll = prevPosition.left !== position.left;\n    const isVerticalScroll = prevPosition.top !== position.top;\n    if (isHorizontalScroll || isVerticalScroll) handler();\n    prevPosition = position;\n    rAF = window.requestAnimationFrame(loop);\n  })();\n  return () => window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n  const handleCallback = useCallbackRef(callback);\n  const debounceTimerRef = React2.useRef(0);\n  React2.useEffect(() => () => window.clearTimeout(debounceTimerRef.current), []);\n  return React2.useCallback(() => {\n    window.clearTimeout(debounceTimerRef.current);\n    debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n  }, [handleCallback, delay]);\n}\nfunction useResizeObserver(element, onResize) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\nexport {\n  Corner,\n  Root,\n  ScrollArea,\n  ScrollAreaCorner,\n  ScrollAreaScrollbar,\n  ScrollAreaThumb,\n  ScrollAreaViewport,\n  Scrollbar,\n  Thumb,\n  Viewport,\n  createScrollAreaScope\n};\n//# sourceMappingURL=index.mjs.map\n", "\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot=\"scroll-area\"\r\n      className={cn(\"relative\", className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot=\"scroll-area-viewport\"\r\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  )\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = \"vertical\",\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot=\"scroll-area-scrollbar\"\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"flex touch-none p-px transition-colors select-none\",\r\n        orientation === \"vertical\" &&\r\n          \"h-full w-2.5 border-l border-l-transparent\",\r\n        orientation === \"horizontal\" &&\r\n          \"h-2.5 flex-col border-t border-t-transparent\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot=\"scroll-area-thumb\"\r\n        className=\"bg-border relative flex-1 rounded-full\"\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  )\r\n}\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": ["useStateMachine", "initialState", "machine", "React.useReducer", "state", "event", "SCROLL_AREA_NAME", "createScrollAreaContext", "createScrollAreaScope", "createContextScope", "ScrollAreaProvider", "useScrollAreaContext", "ScrollArea", "React2.forwardRef", "props", "forwardedRef", "__scopeScrollArea", "type", "dir", "scrollHideDelay", "scrollAreaProps", "scrollArea", "setScrollArea", "React2.useState", "viewport", "setViewport", "content", "<PERSON><PERSON><PERSON><PERSON>", "scrollbarX", "setScrollbarX", "scrollbarY", "setScrollbarY", "cornerWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cornerHeight", "setCornerHeight", "scrollbarXEnabled", "setScrollbarXEnabled", "scrollbarYEnabled", "setScrollbarYEnabled", "composedRefs", "useComposedRefs", "node", "direction", "useDirection", "jsx", "Primitive", "VIEWPORT_NAME", "ScrollAreaViewport", "children", "nonce", "viewportProps", "context", "ref", "React2.useRef", "jsxs", "Fragment", "SCROLLBAR_NAME", "ScrollAreaScrollbar", "forceMount", "scrollbarProps", "onScrollbarXEnabledChange", "onScrollbarYEnabledChange", "isHorizontal", "React2.useEffect", "ScrollAreaScrollbarHover", "ScrollAreaScrollbarScroll", "ScrollAreaScrollbarAuto", "ScrollAreaScrollbarVisible", "visible", "setVisible", "hide<PERSON><PERSON>r", "handlePointerEnter", "handlePointerLeave", "Presence", "debounceScrollEnd", "useDebounceCallback", "send", "scrollDirection", "prevScrollPos", "handleScroll", "scrollPos", "composeEventHandlers", "handleResize", "isOverflowX", "isOverflowY", "useResizeObserver", "orientation", "thumbRef", "pointerOffsetRef", "sizes", "setSizes", "thumbRatio", "getThumbRatio", "commonProps", "thumb", "pointerPos", "getScrollPosition", "getScrollPositionFromPointer", "ScrollAreaScrollbarX", "offset", "getThumbOffsetFromScroll", "ScrollAreaScrollbarY", "onSizesChange", "computedStyle", "setComputedStyle", "composeRefs", "ScrollAreaScrollbarImpl", "getThumbSize", "maxScrollPos", "isScrollingWithinScrollbarBounds", "toInt", "ScrollbarProvider", "useScrollbarContext", "<PERSON><PERSON><PERSON>b", "onThumbChange", "onThumbPointerUp", "onThumbPointerDown", "onThumbPositionChange", "onDragScroll", "onWheelScroll", "onResize", "scrollbar", "setScrollbar", "rectRef", "prevWebkitUserSelectRef", "handleWheelScroll", "useCallbackRef", "handleThumbPositionChange", "handleDragScroll", "x", "y", "handleWheel", "element", "THUMB_NAME", "ScrollAreaThumb", "thumbProps", "scrollbarContext", "ScrollAreaThumbImpl", "style", "scrollAreaContext", "composedRef", "removeUnlinkedScrollListenerRef", "listener", "addUnlinkedScrollListener", "thumbRect", "CORNER_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasBothScrollbarsVisible", "ScrollAreaCornerImpl", "cornerProps", "width", "<PERSON><PERSON><PERSON><PERSON>", "height", "setHeight", "hasSize", "height2", "width2", "value", "viewportSize", "contentSize", "ratio", "scrollbarPadding", "thumbSize", "pointerOffset", "thumbSizePx", "thumbCenter", "thumbOffsetFromEnd", "minPointerPos", "maxPointer<PERSON>os", "scrollRange", "linearScale", "maxThumbPos", "scrollClampRange", "scrollWithoutMomentum", "clamp", "input", "output", "handler", "prevPosition", "rAF", "loop", "position", "isHorizontalScroll", "isVerticalScroll", "callback", "delay", "handleCallback", "debounceTimerRef", "React2.useCallback", "useLayoutEffect", "resizeObserver", "Root", "Viewport", "Corner", "className", "ScrollAreaPrimitive.Root", "cn", "ScrollAreaPrimitive.Viewport", "<PERSON><PERSON>Bar", "ScrollAreaPrimitive.Corner", "ScrollAreaPrimitive.ScrollAreaScrollbar", "ScrollAreaPrimitive.ScrollAreaThumb"], "mappings": "mMAgBA,SAASA,GAAgBC,EAAcC,EAAS,CAC9C,OAAOC,EAAgB,WAAC,CAACC,EAAOC,IACZH,EAAQE,CAAK,EAAEC,CAAK,GAClBD,EACnBH,CAAY,CACjB,CAIA,IAAIK,EAAmB,aACnB,CAACC,EAAyBC,EAAqB,EAAIC,GAAmBH,CAAgB,EACtF,CAACI,GAAoBC,CAAoB,EAAIJ,EAAwBD,CAAgB,EACrFM,EAAaC,EAAiB,WAChC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CACJ,kBAAAC,EACA,KAAAC,EAAO,QACP,IAAAC,EACA,gBAAAC,EAAkB,IAClB,GAAGC,CACT,EAAQN,EACE,CAACO,EAAYC,CAAa,EAAIC,EAAAA,SAAgB,IAAI,EAClD,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAAgB,IAAI,EAC9C,CAACG,EAASC,CAAU,EAAIJ,EAAAA,SAAgB,IAAI,EAC5C,CAACK,EAAYC,CAAa,EAAIN,EAAAA,SAAgB,IAAI,EAClD,CAACO,EAAYC,CAAa,EAAIR,EAAAA,SAAgB,IAAI,EAClD,CAACS,EAAaC,CAAc,EAAIV,EAAAA,SAAgB,CAAC,EACjD,CAACW,EAAcC,CAAe,EAAIZ,EAAAA,SAAgB,CAAC,EACnD,CAACa,EAAmBC,CAAoB,EAAId,EAAAA,SAAgB,EAAK,EACjE,CAACe,EAAmBC,CAAoB,EAAIhB,EAAAA,SAAgB,EAAK,EACjEiB,EAAeC,EAAgB1B,EAAe2B,GAASpB,EAAcoB,CAAI,CAAC,EAC1EC,EAAYC,GAAa1B,CAAG,EAClC,OAAuB2B,EAAG,IACxBnC,GACA,CACE,MAAOM,EACP,KAAAC,EACA,IAAK0B,EACL,gBAAAxB,EACA,WAAAE,EACA,SAAAG,EACA,iBAAkBC,EAClB,QAAAC,EACA,gBAAiBC,EACjB,WAAAC,EACA,mBAAoBC,EACpB,kBAAAO,EACA,0BAA2BC,EAC3B,WAAAP,EACA,mBAAoBC,EACpB,kBAAAO,EACA,0BAA2BC,EAC3B,oBAAqBN,EACrB,qBAAsBE,EACtB,SAA0BU,EAAG,IAC3BC,EAAU,IACV,CACE,IAAKH,EACL,GAAGvB,EACH,IAAKoB,EACL,MAAO,CACL,SAAU,WAET,mCAAqCR,EAAc,KACnD,oCAAsCE,EAAe,KACtD,GAAGpB,EAAM,KACvB,CACA,CACA,CACA,CACK,CACL,CACA,EACAF,EAAW,YAAcN,EACzB,IAAIyC,EAAgB,qBAChBC,EAAqBnC,EAAiB,WACxC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CAAE,kBAAAC,EAAmB,SAAAiC,EAAU,MAAAC,EAAO,GAAGC,CAAe,EAAGrC,EAC3DsC,EAAUzC,EAAqBoC,EAAe/B,CAAiB,EAC/DqC,EAAMC,EAAa,OAAC,IAAI,EACxBd,EAAeC,EAAgB1B,EAAcsC,EAAKD,EAAQ,gBAAgB,EAChF,OAAuBG,EAAI,KAACC,WAAU,CAAE,SAAU,CAChCX,EAAG,IACjB,QACA,CACE,wBAAyB,CACvB,OAAQ,qLACT,EACD,MAAAK,CACV,CACO,EACeL,EAAG,IACjBC,EAAU,IACV,CACE,kCAAmC,GACnC,GAAGK,EACH,IAAKX,EACL,MAAO,CAYL,UAAWY,EAAQ,kBAAoB,SAAW,SAClD,UAAWA,EAAQ,kBAAoB,SAAW,SAClD,GAAGtC,EAAM,KACV,EACD,SAA0B+B,EAAAA,IAAI,MAAO,CAAE,IAAKO,EAAQ,gBAAiB,MAAO,CAAE,SAAU,OAAQ,QAAS,OAAO,EAAI,SAAAH,CAAU,CAAA,CACxI,CACA,CACA,EAAO,CACP,CACA,EACAD,EAAmB,YAAcD,EACjC,IAAIU,EAAiB,sBACjBC,EAAsB7C,EAAiB,WACzC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CAAE,WAAA4C,EAAY,GAAGC,CAAc,EAAK9C,EACpCsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE,CAAE,0BAAA+C,EAA2B,0BAAAC,CAAyB,EAAKV,EAC3DW,EAAejD,EAAM,cAAgB,aAC3CkD,OAAAA,EAAAA,UAAiB,KACfD,EAAeF,EAA0B,EAAI,EAAIC,EAA0B,EAAI,EACxE,IAAM,CACXC,EAAeF,EAA0B,EAAK,EAAIC,EAA0B,EAAK,CAClF,GACA,CAACC,EAAcF,EAA2BC,CAAyB,CAAC,EAChEV,EAAQ,OAAS,QAA0BP,EAAG,IAACoB,GAA0B,CAAE,GAAGL,EAAgB,IAAK7C,EAAc,WAAA4C,CAAY,CAAA,EAAIP,EAAQ,OAAS,SAA2BP,EAAG,IAACqB,GAA2B,CAAE,GAAGN,EAAgB,IAAK7C,EAAc,WAAA4C,CAAU,CAAE,EAAIP,EAAQ,OAAS,OAAyBP,MAAIsB,EAAyB,CAAE,GAAGP,EAAgB,IAAK7C,EAAc,WAAA4C,CAAY,CAAA,EAAIP,EAAQ,OAAS,SAA2BP,EAAAA,IAAIuB,EAA4B,CAAE,GAAGR,EAAgB,IAAK7C,CAAc,CAAA,EAAI,IACtgB,CACA,EACA2C,EAAoB,YAAcD,EAClC,IAAIQ,GAA2BpD,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACxE,KAAM,CAAE,WAAA4C,EAAY,GAAGC,CAAc,EAAK9C,EACpCsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE,CAACuD,EAASC,CAAU,EAAI/C,EAAAA,SAAgB,EAAK,EACnDyC,OAAAA,EAAAA,UAAiB,IAAM,CACrB,MAAM3C,EAAa+B,EAAQ,WAC3B,IAAImB,EAAY,EAChB,GAAIlD,EAAY,CACd,MAAMmD,EAAqB,IAAM,CAC/B,OAAO,aAAaD,CAAS,EAC7BD,EAAW,EAAI,CAChB,EACKG,EAAqB,IAAM,CAC/BF,EAAY,OAAO,WAAW,IAAMD,EAAW,EAAK,EAAGlB,EAAQ,eAAe,CAC/E,EACD,OAAA/B,EAAW,iBAAiB,eAAgBmD,CAAkB,EAC9DnD,EAAW,iBAAiB,eAAgBoD,CAAkB,EACvD,IAAM,CACX,OAAO,aAAaF,CAAS,EAC7BlD,EAAW,oBAAoB,eAAgBmD,CAAkB,EACjEnD,EAAW,oBAAoB,eAAgBoD,CAAkB,CAClE,CACP,CACG,EAAE,CAACrB,EAAQ,WAAYA,EAAQ,eAAe,CAAC,EACzBP,EAAAA,IAAI6B,EAAU,CAAE,QAASf,GAAcU,EAAS,SAA0BxB,EAAG,IAClGsB,EACA,CACE,aAAcE,EAAU,UAAY,SACpC,GAAGT,EACH,IAAK7C,CACX,CACA,EAAK,CACL,CAAC,EACGmD,GAA4BrD,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACzE,KAAM,CAAE,WAAA4C,EAAY,GAAGC,CAAc,EAAK9C,EACpCsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtEiD,EAAejD,EAAM,cAAgB,aACrC6D,EAAoBC,EAAoB,IAAMC,EAAK,YAAY,EAAG,GAAG,EACrE,CAACzE,EAAOyE,CAAI,EAAI7E,GAAgB,SAAU,CAC9C,OAAQ,CACN,OAAQ,WACT,EACD,UAAW,CACT,WAAY,OACZ,cAAe,aAChB,EACD,YAAa,CACX,OAAQ,cACR,cAAe,MAChB,EACD,KAAM,CACJ,KAAM,SACN,OAAQ,YACR,cAAe,aACrB,CACA,CAAG,EACDgE,OAAAA,EAAAA,UAAiB,IAAM,CACrB,GAAI5D,IAAU,OAAQ,CACpB,MAAMmE,EAAY,OAAO,WAAW,IAAMM,EAAK,MAAM,EAAGzB,EAAQ,eAAe,EAC/E,MAAO,IAAM,OAAO,aAAamB,CAAS,CAChD,CACG,EAAE,CAACnE,EAAOgD,EAAQ,gBAAiByB,CAAI,CAAC,EACzCb,EAAAA,UAAiB,IAAM,CACrB,MAAMxC,EAAW4B,EAAQ,SACnB0B,EAAkBf,EAAe,aAAe,YACtD,GAAIvC,EAAU,CACZ,IAAIuD,EAAgBvD,EAASsD,CAAe,EAC5C,MAAME,EAAe,IAAM,CACzB,MAAMC,EAAYzD,EAASsD,CAAe,EACNC,IAAkBE,IAEpDJ,EAAK,QAAQ,EACbF,EAAmB,GAErBI,EAAgBE,CACjB,EACD,OAAAzD,EAAS,iBAAiB,SAAUwD,CAAY,EACzC,IAAMxD,EAAS,oBAAoB,SAAUwD,CAAY,CACtE,CACA,EAAK,CAAC5B,EAAQ,SAAUW,EAAcc,EAAMF,CAAiB,CAAC,EACrC9B,EAAG,IAAC6B,EAAU,CAAE,QAASf,GAAcvD,IAAU,SAAU,SAA0ByC,EAAG,IAC7GuB,EACA,CACE,aAAchE,IAAU,SAAW,SAAW,UAC9C,GAAGwD,EACH,IAAK7C,EACL,eAAgBmE,EAAqBpE,EAAM,eAAgB,IAAM+D,EAAK,eAAe,CAAC,EACtF,eAAgBK,EAAqBpE,EAAM,eAAgB,IAAM+D,EAAK,eAAe,CAAC,CAC5F,CACA,EAAK,CACL,CAAC,EACGV,EAA0BtD,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACvE,MAAMqC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE,CAAE,WAAA6C,EAAY,GAAGC,CAAc,EAAK9C,EACpC,CAACuD,EAASC,CAAU,EAAI/C,EAAAA,SAAgB,EAAK,EAC7CwC,EAAejD,EAAM,cAAgB,aACrCqE,EAAeP,EAAoB,IAAM,CAC7C,GAAIxB,EAAQ,SAAU,CACpB,MAAMgC,EAAchC,EAAQ,SAAS,YAAcA,EAAQ,SAAS,YAC9DiC,EAAcjC,EAAQ,SAAS,aAAeA,EAAQ,SAAS,aACrEkB,EAAWP,EAAeqB,EAAcC,CAAW,CACzD,CACG,EAAE,EAAE,EACL,OAAAC,EAAkBlC,EAAQ,SAAU+B,CAAY,EAChDG,EAAkBlC,EAAQ,QAAS+B,CAAY,EACxBtC,EAAAA,IAAI6B,EAAU,CAAE,QAASf,GAAcU,EAAS,SAA0BxB,EAAG,IAClGuB,EACA,CACE,aAAcC,EAAU,UAAY,SACpC,GAAGT,EACH,IAAK7C,CACX,CACA,EAAK,CACL,CAAC,EACGqD,EAA6BvD,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CAC1E,KAAM,CAAE,YAAAwE,EAAc,WAAY,GAAG3B,CAAgB,EAAG9C,EAClDsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE0E,EAAWlC,EAAa,OAAC,IAAI,EAC7BmC,EAAmBnC,EAAa,OAAC,CAAC,EAClC,CAACoC,EAAOC,CAAQ,EAAIpE,WAAgB,CACxC,QAAS,EACT,SAAU,EACV,UAAW,CAAE,KAAM,EAAG,aAAc,EAAG,WAAY,CAAC,CACxD,CAAG,EACKqE,EAAaC,GAAcH,EAAM,SAAUA,EAAM,OAAO,EACxDI,EAAc,CAClB,GAAGlC,EACH,MAAA8B,EACA,cAAeC,EACf,SAAkBC,EAAa,GAAKA,EAAa,EACjD,cAAgBG,GAAUP,EAAS,QAAUO,EAC7C,iBAAkB,IAAMN,EAAiB,QAAU,EACnD,mBAAqBO,GAAeP,EAAiB,QAAUO,CAChE,EACD,SAASC,EAAkBD,EAAY9E,EAAK,CAC1C,OAAOgF,GAA6BF,EAAYP,EAAiB,QAASC,EAAOxE,CAAG,CACxF,CACE,OAAIqE,IAAgB,aACK1C,EAAG,IACxBsD,GACA,CACE,GAAGL,EACH,IAAK/E,EACL,sBAAuB,IAAM,CAC3B,GAAIqC,EAAQ,UAAYoC,EAAS,QAAS,CACxC,MAAMP,EAAY7B,EAAQ,SAAS,WAC7BgD,EAASC,EAAyBpB,EAAWS,EAAOtC,EAAQ,GAAG,EACrEoC,EAAS,QAAQ,MAAM,UAAY,eAAeY,CAAM,WACpE,CACS,EACD,cAAgBnB,GAAc,CACxB7B,EAAQ,WAAUA,EAAQ,SAAS,WAAa6B,EACrD,EACD,aAAee,GAAe,CACxB5C,EAAQ,WACVA,EAAQ,SAAS,WAAa6C,EAAkBD,EAAY5C,EAAQ,GAAG,EAEnF,CACA,CACK,EAECmC,IAAgB,WACK1C,EAAG,IACxByD,GACA,CACE,GAAGR,EACH,IAAK/E,EACL,sBAAuB,IAAM,CAC3B,GAAIqC,EAAQ,UAAYoC,EAAS,QAAS,CACxC,MAAMP,EAAY7B,EAAQ,SAAS,UAC7BgD,EAASC,EAAyBpB,EAAWS,CAAK,EACxDF,EAAS,QAAQ,MAAM,UAAY,kBAAkBY,CAAM,QACvE,CACS,EACD,cAAgBnB,GAAc,CACxB7B,EAAQ,WAAUA,EAAQ,SAAS,UAAY6B,EACpD,EACD,aAAee,GAAe,CACxB5C,EAAQ,WAAUA,EAAQ,SAAS,UAAY6C,EAAkBD,CAAU,EACzF,CACA,CACK,EAEI,IACT,CAAC,EACGG,GAAuBtF,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACpE,KAAM,CAAE,MAAA2E,EAAO,cAAAa,EAAe,GAAG3C,CAAgB,EAAG9C,EAC9CsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE,CAAC0F,EAAeC,CAAgB,EAAIlF,WAAiB,EACrD8B,EAAMC,EAAa,OAAC,IAAI,EACxBoD,EAAcjE,EAAgB1B,EAAcsC,EAAKD,EAAQ,kBAAkB,EACjFY,OAAAA,EAAAA,UAAiB,IAAM,CACjBX,EAAI,SAASoD,EAAiB,iBAAiBpD,EAAI,OAAO,CAAC,CACnE,EAAK,CAACA,CAAG,CAAC,EACeR,EAAG,IACxB8D,GACA,CACE,mBAAoB,aACpB,GAAG/C,EACH,IAAK8C,EACL,MAAAhB,EACA,MAAO,CACL,OAAQ,EACR,KAAMtC,EAAQ,MAAQ,MAAQ,wCAA0C,EACxE,MAAOA,EAAQ,MAAQ,MAAQ,wCAA0C,EACxE,kCAAoCwD,EAAalB,CAAK,EAAI,KAC3D,GAAG5E,EAAM,KACV,EACD,mBAAqBkF,GAAelF,EAAM,mBAAmBkF,EAAW,CAAC,EACzE,aAAeA,GAAelF,EAAM,aAAakF,EAAW,CAAC,EAC7D,cAAe,CAAC3F,EAAOwG,IAAiB,CACtC,GAAIzD,EAAQ,SAAU,CACpB,MAAM6B,EAAY7B,EAAQ,SAAS,WAAa/C,EAAM,OACtDS,EAAM,cAAcmE,CAAS,EACzB6B,GAAiC7B,EAAW4B,CAAY,GAC1DxG,EAAM,eAAgB,CAElC,CACO,EACD,SAAU,IAAM,CACVgD,EAAI,SAAWD,EAAQ,UAAYoD,GACrCD,EAAc,CACZ,QAASnD,EAAQ,SAAS,YAC1B,SAAUA,EAAQ,SAAS,YAC3B,UAAW,CACT,KAAMC,EAAI,QAAQ,YAClB,aAAc0D,EAAMP,EAAc,WAAW,EAC7C,WAAYO,EAAMP,EAAc,YAAY,CAC1D,CACA,CAAW,CAEX,CACA,CACG,CACH,CAAC,EACGF,GAAuBzF,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACpE,KAAM,CAAE,MAAA2E,EAAO,cAAAa,EAAe,GAAG3C,CAAgB,EAAG9C,EAC9CsC,EAAUzC,EAAqB8C,EAAgB3C,EAAM,iBAAiB,EACtE,CAAC0F,EAAeC,CAAgB,EAAIlF,WAAiB,EACrD8B,EAAMC,EAAa,OAAC,IAAI,EACxBoD,EAAcjE,EAAgB1B,EAAcsC,EAAKD,EAAQ,kBAAkB,EACjFY,OAAAA,EAAAA,UAAiB,IAAM,CACjBX,EAAI,SAASoD,EAAiB,iBAAiBpD,EAAI,OAAO,CAAC,CACnE,EAAK,CAACA,CAAG,CAAC,EACeR,EAAG,IACxB8D,GACA,CACE,mBAAoB,WACpB,GAAG/C,EACH,IAAK8C,EACL,MAAAhB,EACA,MAAO,CACL,IAAK,EACL,MAAOtC,EAAQ,MAAQ,MAAQ,EAAI,OACnC,KAAMA,EAAQ,MAAQ,MAAQ,EAAI,OAClC,OAAQ,yCACP,mCAAqCwD,EAAalB,CAAK,EAAI,KAC5D,GAAG5E,EAAM,KACV,EACD,mBAAqBkF,GAAelF,EAAM,mBAAmBkF,EAAW,CAAC,EACzE,aAAeA,GAAelF,EAAM,aAAakF,EAAW,CAAC,EAC7D,cAAe,CAAC3F,EAAOwG,IAAiB,CACtC,GAAIzD,EAAQ,SAAU,CACpB,MAAM6B,EAAY7B,EAAQ,SAAS,UAAY/C,EAAM,OACrDS,EAAM,cAAcmE,CAAS,EACzB6B,GAAiC7B,EAAW4B,CAAY,GAC1DxG,EAAM,eAAgB,CAElC,CACO,EACD,SAAU,IAAM,CACVgD,EAAI,SAAWD,EAAQ,UAAYoD,GACrCD,EAAc,CACZ,QAASnD,EAAQ,SAAS,aAC1B,SAAUA,EAAQ,SAAS,aAC3B,UAAW,CACT,KAAMC,EAAI,QAAQ,aAClB,aAAc0D,EAAMP,EAAc,UAAU,EAC5C,WAAYO,EAAMP,EAAc,aAAa,CAC3D,CACA,CAAW,CAEX,CACA,CACG,CACH,CAAC,EACG,CAACQ,GAAmBC,CAAmB,EAAI1G,EAAwBkD,CAAc,EACjFkD,GAA0B9F,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACvE,KAAM,CACJ,kBAAAC,EACA,MAAA0E,EACA,SAAAwB,EACA,cAAAC,EACA,iBAAAC,EACA,mBAAAC,EACA,sBAAAC,EACA,aAAAC,EACA,cAAAC,EACA,SAAAC,EACA,GAAG7D,CACP,EAAM9C,EACEsC,EAAUzC,EAAqB8C,EAAgBzC,CAAiB,EAChE,CAAC0G,EAAWC,CAAY,EAAIpG,EAAAA,SAAgB,IAAI,EAChDmF,EAAcjE,EAAgB1B,EAAe2B,GAASiF,EAAajF,CAAI,CAAC,EACxEkF,EAAUtE,EAAa,OAAC,IAAI,EAC5BuE,EAA0BvE,EAAa,OAAC,EAAE,EAC1C9B,EAAW4B,EAAQ,SACnByD,EAAenB,EAAM,QAAUA,EAAM,SACrCoC,EAAoBC,EAAeP,CAAa,EAChDQ,EAA4BD,EAAeT,CAAqB,EAChEnC,EAAeP,EAAoB6C,EAAU,EAAE,EACrD,SAASQ,EAAiB5H,EAAO,CAC/B,GAAIuH,EAAQ,QAAS,CACnB,MAAMM,EAAI7H,EAAM,QAAUuH,EAAQ,QAAQ,KACpCO,EAAI9H,EAAM,QAAUuH,EAAQ,QAAQ,IAC1CL,EAAa,CAAE,EAAAW,EAAG,EAAAC,EAAG,CAC3B,CACA,CACEnE,OAAAA,EAAAA,UAAiB,IAAM,CACrB,MAAMoE,EAAe/H,GAAU,CAC7B,MAAMgI,EAAUhI,EAAM,OACGqH,GAAW,SAASW,CAAO,GAC9BP,EAAkBzH,EAAOwG,CAAY,CAC5D,EACD,gBAAS,iBAAiB,QAASuB,EAAa,CAAE,QAAS,GAAO,EAC3D,IAAM,SAAS,oBAAoB,QAASA,EAAa,CAAE,QAAS,GAAO,CACnF,EAAE,CAAC5G,EAAUkG,EAAWb,EAAciB,CAAiB,CAAC,EACzD9D,EAAAA,UAAiBgE,EAA2B,CAACtC,EAAOsC,CAAyB,CAAC,EAC9E1C,EAAkBoC,EAAWvC,CAAY,EACzCG,EAAkBlC,EAAQ,QAAS+B,CAAY,EACxBtC,EAAG,IACxBmE,GACA,CACE,MAAOhG,EACP,UAAA0G,EACA,SAAAR,EACA,cAAea,EAAeZ,CAAa,EAC3C,iBAAkBY,EAAeX,CAAgB,EACjD,sBAAuBY,EACvB,mBAAoBD,EAAeV,CAAkB,EACrD,SAA0BxE,EAAG,IAC3BC,EAAU,IACV,CACE,GAAGc,EACH,IAAK8C,EACL,MAAO,CAAE,SAAU,WAAY,GAAG9C,EAAe,KAAO,EACxD,cAAesB,EAAqBpE,EAAM,cAAgBT,GAAU,CAE9DA,EAAM,SAAW,IACHA,EAAM,OACd,kBAAkBA,EAAM,SAAS,EACzCuH,EAAQ,QAAUF,EAAU,sBAAuB,EACnDG,EAAwB,QAAU,SAAS,KAAK,MAAM,iBACtD,SAAS,KAAK,MAAM,iBAAmB,OACnCzE,EAAQ,WAAUA,EAAQ,SAAS,MAAM,eAAiB,QAC9D6E,EAAiB5H,CAAK,EAEpC,CAAW,EACD,cAAe6E,EAAqBpE,EAAM,cAAemH,CAAgB,EACzE,YAAa/C,EAAqBpE,EAAM,YAAcT,GAAU,CAC9D,MAAMgI,EAAUhI,EAAM,OAClBgI,EAAQ,kBAAkBhI,EAAM,SAAS,GAC3CgI,EAAQ,sBAAsBhI,EAAM,SAAS,EAE/C,SAAS,KAAK,MAAM,iBAAmBwH,EAAwB,QAC3DzE,EAAQ,WAAUA,EAAQ,SAAS,MAAM,eAAiB,IAC9DwE,EAAQ,QAAU,IACnB,CAAA,CACX,CACA,CACA,CACG,CACH,CAAC,EACGU,EAAa,kBACbC,GAAkB1H,EAAiB,WACrC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CAAE,WAAA4C,EAAY,GAAG6E,CAAU,EAAK1H,EAChC2H,EAAmBxB,EAAoBqB,EAAYxH,EAAM,iBAAiB,EAChF,OAAuB+B,EAAAA,IAAI6B,EAAU,CAAE,QAASf,GAAc8E,EAAiB,SAAU,SAA0B5F,EAAAA,IAAI6F,GAAqB,CAAE,IAAK3H,EAAc,GAAGyH,CAAY,CAAA,EAAG,CACvL,CACA,EACIE,GAAsB7H,EAAiB,WACzC,CAACC,EAAOC,IAAiB,CACvB,KAAM,CAAE,kBAAAC,EAAmB,MAAA2H,EAAO,GAAGH,CAAY,EAAG1H,EAC9C8H,EAAoBjI,EAAqB2H,EAAYtH,CAAiB,EACtEyH,EAAmBxB,EAAoBqB,EAAYtH,CAAiB,EACpE,CAAE,sBAAAsG,CAAqB,EAAKmB,EAC5BI,EAAcpG,EAClB1B,EACC2B,GAAS+F,EAAiB,cAAc/F,CAAI,CAC9C,EACKoG,EAAkCxF,EAAa,OAAC,MAAM,EACtDqB,EAAoBC,EAAoB,IAAM,CAC9CkE,EAAgC,UAClCA,EAAgC,QAAS,EACzCA,EAAgC,QAAU,OAE7C,EAAE,GAAG,EACN9E,OAAAA,EAAAA,UAAiB,IAAM,CACrB,MAAMxC,EAAWoH,EAAkB,SACnC,GAAIpH,EAAU,CACZ,MAAMwD,EAAe,IAAM,CAEzB,GADAL,EAAmB,EACf,CAACmE,EAAgC,QAAS,CAC5C,MAAMC,EAAWC,GAA0BxH,EAAU8F,CAAqB,EAC1EwB,EAAgC,QAAUC,EAC1CzB,EAAuB,CACnC,CACS,EACD,OAAAA,EAAuB,EACvB9F,EAAS,iBAAiB,SAAUwD,CAAY,EACzC,IAAMxD,EAAS,oBAAoB,SAAUwD,CAAY,CACxE,CACK,EAAE,CAAC4D,EAAkB,SAAUjE,EAAmB2C,CAAqB,CAAC,EAClDzE,EAAG,IACxBC,EAAU,IACV,CACE,aAAc2F,EAAiB,SAAW,UAAY,SACtD,GAAGD,EACH,IAAKK,EACL,MAAO,CACL,MAAO,uCACP,OAAQ,wCACR,GAAGF,CACJ,EACD,qBAAsBzD,EAAqBpE,EAAM,qBAAuBT,GAAU,CAEhF,MAAM4I,EADQ5I,EAAM,OACI,sBAAuB,EACzC6H,EAAI7H,EAAM,QAAU4I,EAAU,KAC9Bd,EAAI9H,EAAM,QAAU4I,EAAU,IACpCR,EAAiB,mBAAmB,CAAE,EAAAP,EAAG,EAAAC,CAAC,CAAE,CACtD,CAAS,EACD,YAAajD,EAAqBpE,EAAM,YAAa2H,EAAiB,gBAAgB,CAC9F,CACK,CACL,CACA,EACAF,GAAgB,YAAcD,EAC9B,IAAIY,EAAc,mBACdC,GAAmBtI,EAAiB,WACtC,CAACC,EAAOC,IAAiB,CACvB,MAAMqC,EAAUzC,EAAqBuI,EAAapI,EAAM,iBAAiB,EACnEsI,EAA2B,GAAQhG,EAAQ,YAAcA,EAAQ,YAEvE,OADkBA,EAAQ,OAAS,UAAYgG,EACZvG,EAAG,IAACwG,GAAsB,CAAE,GAAGvI,EAAO,IAAKC,CAAc,CAAA,EAAI,IACpG,CACA,EACAoI,GAAiB,YAAcD,EAC/B,IAAIG,GAAuBxI,EAAiB,WAAC,CAACC,EAAOC,IAAiB,CACpE,KAAM,CAAE,kBAAAC,EAAmB,GAAGsI,CAAW,EAAKxI,EACxCsC,EAAUzC,EAAqBuI,EAAalI,CAAiB,EAC7D,CAACuI,EAAOC,CAAQ,EAAIjI,EAAAA,SAAgB,CAAC,EACrC,CAACkI,EAAQC,CAAS,EAAInI,EAAAA,SAAgB,CAAC,EACvCoI,EAAU,GAAQJ,GAASE,GACjC,OAAAnE,EAAkBlC,EAAQ,WAAY,IAAM,CAC1C,MAAMwG,EAAUxG,EAAQ,YAAY,cAAgB,EACpDA,EAAQ,qBAAqBwG,CAAO,EACpCF,EAAUE,CAAO,CACrB,CAAG,EACDtE,EAAkBlC,EAAQ,WAAY,IAAM,CAC1C,MAAMyG,EAASzG,EAAQ,YAAY,aAAe,EAClDA,EAAQ,oBAAoByG,CAAM,EAClCL,EAASK,CAAM,CACnB,CAAG,EACMF,EAA0B9G,EAAG,IAClCC,EAAU,IACV,CACE,GAAGwG,EACH,IAAKvI,EACL,MAAO,CACL,MAAAwI,EACA,OAAAE,EACA,SAAU,WACV,MAAOrG,EAAQ,MAAQ,MAAQ,EAAI,OACnC,KAAMA,EAAQ,MAAQ,MAAQ,EAAI,OAClC,OAAQ,EACR,GAAGtC,EAAM,KACjB,CACA,CACA,EAAM,IACN,CAAC,EACD,SAASiG,EAAM+C,EAAO,CACpB,OAAOA,EAAQ,SAASA,EAAO,EAAE,EAAI,CACvC,CACA,SAASjE,GAAckE,EAAcC,EAAa,CAChD,MAAMC,EAAQF,EAAeC,EAC7B,OAAO,MAAMC,CAAK,EAAI,EAAIA,CAC5B,CACA,SAASrD,EAAalB,EAAO,CAC3B,MAAMuE,EAAQpE,GAAcH,EAAM,SAAUA,EAAM,OAAO,EACnDwE,EAAmBxE,EAAM,UAAU,aAAeA,EAAM,UAAU,WAClEyE,GAAazE,EAAM,UAAU,KAAOwE,GAAoBD,EAC9D,OAAO,KAAK,IAAIE,EAAW,EAAE,CAC/B,CACA,SAASjE,GAA6BF,EAAYoE,EAAe1E,EAAOxE,EAAM,MAAO,CACnF,MAAMmJ,EAAczD,EAAalB,CAAK,EAChC4E,EAAcD,EAAc,EAC5BjE,EAASgE,GAAiBE,EAC1BC,EAAqBF,EAAcjE,EACnCoE,EAAgB9E,EAAM,UAAU,aAAeU,EAC/CqE,EAAgB/E,EAAM,UAAU,KAAOA,EAAM,UAAU,WAAa6E,EACpE1D,EAAenB,EAAM,QAAUA,EAAM,SACrCgF,EAAcxJ,IAAQ,MAAQ,CAAC,EAAG2F,CAAY,EAAI,CAACA,EAAe,GAAI,CAAC,EAE7E,OADoB8D,GAAY,CAACH,EAAeC,CAAa,EAAGC,CAAW,EACxD1E,CAAU,CAC/B,CACA,SAASK,EAAyBpB,EAAWS,EAAOxE,EAAM,MAAO,CAC/D,MAAMmJ,EAAczD,EAAalB,CAAK,EAChCwE,EAAmBxE,EAAM,UAAU,aAAeA,EAAM,UAAU,WAClEgC,EAAYhC,EAAM,UAAU,KAAOwE,EACnCrD,EAAenB,EAAM,QAAUA,EAAM,SACrCkF,EAAclD,EAAY2C,EAC1BQ,EAAmB3J,IAAQ,MAAQ,CAAC,EAAG2F,CAAY,EAAI,CAACA,EAAe,GAAI,CAAC,EAC5EiE,EAAwBC,GAAM9F,EAAW4F,CAAgB,EAE/D,OADoBF,GAAY,CAAC,EAAG9D,CAAY,EAAG,CAAC,EAAG+D,CAAW,CAAC,EAChDE,CAAqB,CAC1C,CACA,SAASH,GAAYK,EAAOC,EAAQ,CAClC,OAAQnB,GAAU,CAChB,GAAIkB,EAAM,CAAC,IAAMA,EAAM,CAAC,GAAKC,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAG,OAAOA,EAAO,CAAC,EACrE,MAAMhB,GAASgB,EAAO,CAAC,EAAIA,EAAO,CAAC,IAAMD,EAAM,CAAC,EAAIA,EAAM,CAAC,GAC3D,OAAOC,EAAO,CAAC,EAAIhB,GAASH,EAAQkB,EAAM,CAAC,EAC5C,CACH,CACA,SAASlE,GAAiC7B,EAAW4B,EAAc,CACjE,OAAO5B,EAAY,GAAKA,EAAY4B,CACtC,CACA,IAAImC,GAA4B,CAACtG,EAAMwI,EAAU,IAAM,CACvD,IAAM,CACJ,IAAIC,EAAe,CAAE,KAAMzI,EAAK,WAAY,IAAKA,EAAK,SAAW,EAC7D0I,EAAM,EACV,OAAC,SAASC,GAAO,CACf,MAAMC,EAAW,CAAE,KAAM5I,EAAK,WAAY,IAAKA,EAAK,SAAW,EACzD6I,EAAqBJ,EAAa,OAASG,EAAS,KACpDE,EAAmBL,EAAa,MAAQG,EAAS,KACnDC,GAAsBC,IAAkBN,EAAS,EACrDC,EAAeG,EACfF,EAAM,OAAO,sBAAsBC,CAAI,CAC3C,EAAM,EACG,IAAM,OAAO,qBAAqBD,CAAG,CAC9C,EACA,SAASxG,EAAoB6G,EAAUC,EAAO,CAC5C,MAAMC,EAAiB5D,EAAe0D,CAAQ,EACxCG,EAAmBtI,EAAa,OAAC,CAAC,EACxCU,OAAAA,EAAgB,UAAC,IAAM,IAAM,OAAO,aAAa4H,EAAiB,OAAO,EAAG,EAAE,EACvEC,EAAkB,YAAC,IAAM,CAC9B,OAAO,aAAaD,EAAiB,OAAO,EAC5CA,EAAiB,QAAU,OAAO,WAAWD,EAAgBD,CAAK,CACtE,EAAK,CAACC,EAAgBD,CAAK,CAAC,CAC5B,CACA,SAASpG,EAAkB+C,EAASZ,EAAU,CAC5C,MAAMtC,EAAe4C,EAAeN,CAAQ,EAC5CqE,GAAgB,IAAM,CACpB,IAAIV,EAAM,EACV,GAAI/C,EAAS,CACX,MAAM0D,EAAiB,IAAI,eAAe,IAAM,CAC9C,qBAAqBX,CAAG,EACxBA,EAAM,OAAO,sBAAsBjG,CAAY,CACvD,CAAO,EACD,OAAA4G,EAAe,QAAQ1D,CAAO,EACvB,IAAM,CACX,OAAO,qBAAqB+C,CAAG,EAC/BW,EAAe,UAAU1D,CAAO,CACjC,CACP,CACA,EAAK,CAACA,EAASlD,CAAY,CAAC,CAC5B,CACA,IAAI6G,GAAOpL,EACPqL,GAAWjJ,EAGXkJ,GAAS/C,GC7sBb,SAASvI,GAAW,CAClB,UAAAuL,EACA,SAAAlJ,EACA,GAAGnC,CACL,EAA0D,CAEtD,OAAAyC,EAAA,KAAC6I,GAAA,CACC,YAAU,cACV,UAAWC,EAAG,WAAYF,CAAS,EAClC,GAAGrL,EAEJ,SAAA,CAAA+B,EAAA,IAACyJ,GAAA,CACC,YAAU,uBACV,UAAU,qJAET,SAAArJ,CAAA,CACH,QACCsJ,GAAU,EAAA,EACX1J,EAAA,IAAC2J,GAAA,CAA2B,CAAA,CAAA,CAAA,CAC9B,CAEJ,CAEA,SAASD,GAAU,CACjB,UAAAJ,EACA,YAAA5G,EAAc,WACd,GAAGzE,CACL,EAAyE,CAErE,OAAA+B,EAAA,IAAC4J,EAAA,CACC,YAAU,wBACV,YAAAlH,EACA,UAAW8G,EACT,qDACA9G,IAAgB,YACd,6CACFA,IAAgB,cACd,+CACF4G,CACF,EACC,GAAGrL,EAEJ,SAAA+B,EAAA,IAAC6J,GAAA,CACC,YAAU,oBACV,UAAU,wCAAA,CAAA,CACZ,CACF,CAEJ", "x_google_ignoreList": [0]}