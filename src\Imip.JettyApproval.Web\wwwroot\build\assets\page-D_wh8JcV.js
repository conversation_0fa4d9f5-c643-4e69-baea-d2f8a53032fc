import{h as N,j as e,a as o}from"./vendor-6tJeyfYI.js";import{D}from"./DataTable-CDIoPElA.js";import{F as w}from"./filter-sort-bar-MpsapXP_.js";import{n as S,t as R,E as C,T as E,B as u,R as A,A as q}from"./app-layout-rNt37hVL.js";import{T as F}from"./table-skeleton-CE69MDqJ.js";import{m as h,$ as L}from"./App-DnhJzTNn.js";import{P as T}from"./plus-PD53KOti.js";import{P as J}from"./pencil-BTZ0_LzS.js";import"./table-BKSoE52x.js";import"./input-DlXlkYlT.js";import"./useDebounce-BdjXjarW.js";import"./index-X4QX0AQ3.js";import"./popover-ChFN9yvN.js";import"./radix-e4nK4mWk.js";import"./tiny-invariant-CopsF_GD.js";import"./checkbox-D1loOtZt.js";import"./index-CaiFFM4D.js";import"./badge-DWaCYvGm.js";import"./arrow-up-DDQ17ADi.js";import"./skeleton-DAOxGMKm.js";const k=(t,r,a,i)=>N({queryKey:["jetty-requests",t,r,JSON.stringify(a),i],queryFn:async()=>{const s={skipCount:t*r,maxResultCount:r,sorting:i,filterGroup:a};try{return(await S({body:s})).data||{items:[],totalCount:0}}catch(l){let n="Unknown error occurred while loading Jetty Requests";return typeof l=="object"&&l&&"message"in l&&typeof l.message=="string"&&(n=l.message??n),R({title:"Error loading Jetty Requests",description:n,variant:"destructive"}),{items:[],totalCount:0}}},retry:1,retryDelay:1e3}),P=[{value:"docNum",label:"Document Number"},{value:"vesselName",label:"Vessel Name"},{value:"jetty",label:"Jetty"},{value:"arrivalDate",label:"Arrival Date"},{value:"departureDate",label:"Departure Date"}],V=[{value:"Equals",label:"Equals"},{value:"Contains",label:"Contains"},{value:"NotEquals",label:"Not Equals"},{value:"GreaterThan",label:">"},{value:"LessThan",label:"<"}],g=[{accessorKey:"docNum",header:"Document Number",cell:t=>t.getValue()??"-"},{accessorKey:"vesselName",header:"Vessel Name",cell:t=>t.getValue()??"-"},{accessorKey:"jetty",header:"Jetty",cell:t=>t.getValue()??"-"},{accessorKey:"arrivalDate",header:"Arrival Date",cell:t=>t.getValue()?new Date(t.getValue()).toLocaleDateString():"-"},{accessorKey:"departureDate",header:"Departure Date",cell:t=>t.getValue()?new Date(t.getValue()).toLocaleDateString():"-"},{id:"edit",header:"",cell:({row:t})=>{const r=t.original.id,a=()=>h.visit(`/application/${r}/edit`),i=s=>{(s.key==="Enter"||s.key===" ")&&a()};return e.jsx(u,{onClick:a,onKeyDown:i,"aria-label":"Edit Application",tabIndex:0,variant:"outline",size:"icon",className:"ml-2 h-8 w-8",children:e.jsx(J,{className:"w-4 h-4","aria-hidden":"true"})})},enableSorting:!1,enableColumnFilter:!1}],I=()=>{const[t,r]=o.useState({pageIndex:0,pageSize:10}),[a,i]=o.useState([]),[s,l]=o.useState([]),[n,m]=o.useState(!1);o.useEffect(()=>{r(c=>({...c,pageIndex:0}))},[a,s]);const y=o.useMemo(()=>{if(a.length)return{operator:"And",conditions:a}},[a]),f=o.useMemo(()=>{if(s.length)return s.map(c=>`${c.field} ${c.direction}`).join(", ")},[s]),{data:p,isLoading:d,error:x,refetch:b}=k(t.pageIndex,t.pageSize,y,f),v=()=>h.visit("/application/create"),j=async()=>{m(!0);try{await b()}finally{m(!1)}};return x?e.jsxs("div",{className:"bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center",children:[e.jsx(E,{className:"mx-auto h-12 w-12 text-destructive mb-4"}),e.jsx("h3",{className:"font-semibold text-destructive mb-2",children:"Error loading data"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:String(x.message)}),e.jsx(u,{onClick:()=>window.location.reload(),variant:"destructive",children:"Retry"})]}):e.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:[e.jsx("div",{className:"text-xl font-bold px-2 pt-2 pb-1",children:"Jetty Application List"}),e.jsx(w,{filterFields:P,operators:V,filters:a,sorts:s,onFiltersChange:i,onSortsChange:l,children:e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsx(u,{onClick:j,variant:"outline",size:"icon",className:"h-10 w-10",disabled:d||n,children:e.jsx(A,{className:`h-4 w-4 ${n?"animate-spin":""}`})}),e.jsxs(u,{onClick:v,className:"flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base",size:"lg",children:[e.jsx(T,{className:"h-5 w-5"})," New Application"]})]})}),d?e.jsx(F,{columns:g}):e.jsx(D,{title:"",columns:g,data:p?.items??[],totalCount:p?.totalCount??0,isLoading:d,manualPagination:!0,pageSize:t.pageSize,onPaginationChange:r,hideDefaultFilterbar:!0,enableRowSelection:!1,manualSorting:!0})]})},K=()=>e.jsx(C,{children:e.jsx(I,{})}),oe=()=>e.jsxs(q,{children:[e.jsx(L,{title:"Application List"}),e.jsx("div",{className:"flex flex-col space-y-4 p-4",children:e.jsx(K,{})})]});export{oe as default};
//# sourceMappingURL=page-D_wh8JcV.js.map
