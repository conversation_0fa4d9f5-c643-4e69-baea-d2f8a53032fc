import{j as r}from"./vendor-6tJeyfYI.js";import{A as o}from"./app-layout-rNt37hVL.js";import{O as t}from"./overview-CTW9zS8n.js";import{$ as i}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./badge-DWaCYvGm.js";import"./skeleton-DAOxGMKm.js";import"./tiny-invariant-CopsF_GD.js";import"./table-BKSoE52x.js";function n(){return r.jsxs(o,{children:[r.jsx(i,{title:"Dashboard"}),r.jsx(t,{})]})}export{n as default};
//# sourceMappingURL=home-DMcLnTAt.js.map
