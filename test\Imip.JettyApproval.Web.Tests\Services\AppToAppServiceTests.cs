using System.Text.Json;
using Xunit;

namespace Imip.JettyApproval.Web.Tests.Services
{
    public class AppToAppServiceTests
    {
        [Theory]
        [InlineData("{\"id\": 01, \"value\": 0123}", "{\"id\": 1, \"value\": 123}")]
        [InlineData("{\"numbers\": [01, 02, 03]}", "{\"numbers\": [1, 2, 3]}")]
        [InlineData("{\"docNum\": \"25080001\"}", "{\"docNum\": \"25080001\"}")]  // String values should not be changed
        [InlineData("{\"id\": 10, \"value\": 123}", "{\"id\": 10, \"value\": 123}")]  // Valid numbers should not be changed
        [InlineData("{\"count\": 0}", "{\"count\": 0}")]    // Zero should not be changed
        [InlineData("\"25080001\"", "\"25080001\"")]  // Standalone string should not be changed
        public void SanitizeJsonNumbers_ShouldRemoveLeadingZeros(string input, string expected)
        {
            // This test uses reflection to access the private method
            var method = typeof(Imip.JettyApproval.Web.Services.AppToAppService)
                .GetMethod("SanitizeJsonNumbers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

            var result = (string)method.Invoke(null, new object[] { input });

            Assert.Equal(expected, result);
        }

        [Fact]
        public void SanitizeJsonNumbers_ShouldHandleComplexJson()
        {
            var input = @"{
                ""id"": 01,
                ""name"": ""test"",
                ""values"": [01, 02, 03],
                ""nested"": {
                    ""count"": 0123,
                    ""description"": ""value with 01 in string""
                }
            }";

            var expected = @"{
                ""id"": 1,
                ""name"": ""test"",
                ""values"": [1, 2, 3],
                ""nested"": {
                    ""count"": 123,
                    ""description"": ""value with 01 in string""
                }
            }";

            var method = typeof(Imip.JettyApproval.Web.Services.AppToAppService)
                .GetMethod("SanitizeJsonNumbers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

            var result = (string)method.Invoke(null, new object[] { input });

            // Parse both to ensure they're valid JSON and equivalent
            var resultJson = JsonSerializer.Deserialize<JsonElement>(result);
            var expectedJson = JsonSerializer.Deserialize<JsonElement>(expected);

            Assert.Equal(expectedJson.ToString(), resultJson.ToString());
        }

        [Fact]
        public void SanitizeJsonNumbers_ShouldHandleNullOrEmpty()
        {
            var method = typeof(Imip.JettyApproval.Web.Services.AppToAppService)
                .GetMethod("SanitizeJsonNumbers", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);

            var nullResult = (string)method.Invoke(null, new object[] { null });
            var emptyResult = (string)method.Invoke(null, new object[] { "" });

            Assert.Null(nullResult);
            Assert.Equal("", emptyResult);
        }
    }
}
