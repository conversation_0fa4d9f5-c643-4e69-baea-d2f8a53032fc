import type { CargoDto, FilterCondition, FilterGroup, QueryParametersDto } from "@/clientEkb/types.gen";
import { DataGrid } from "@/components/ui/data-grid";
import ErrorBoundary from "@/components/ui/error-boundary";
import { type ColumnFiltersState, type SortingState } from "@tanstack/react-table";
import React from "react";
import { cargoColumns } from "./cargo-columns";
import CargoDialog from "./CargoDialog";
import ekbProxyService from "@/services/ekbProxyService";
import { useTranslation } from "react-i18next";
import { ContentCard } from "@/components/layout/content-card";

const CARGO_QUERY_KEY = ["cargo-list"];

const ManageCargoTableContent: React.FC = () => {
  const { t } = useTranslation();
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editData, setEditData] = React.useState<CargoDto | undefined>(undefined);

  // DataGrid queryFn: adapts DataGrid's params to API
  const queryFn = async ({ pageIndex, pageSize, sorting, filters, globalFilter }: { pageIndex: number; pageSize: number; sorting?: SortingState; filters?: ColumnFiltersState; globalFilter?: string }) => {
    // Convert sorting array to string if present
    let sortingStr: string | undefined = undefined;
    if (Array.isArray(sorting) && sorting.length > 0) {
      sortingStr = sorting
        .map((s) => `${s.id} ${s.desc ? "desc" : "asc"}`)
        .join(", ");
    }

    // Build filterGroup for global and column filters
    const conditions: FilterCondition[] = [];
    if (globalFilter) {
      conditions.push({
        fieldName: "name",
        operator: "Contains",
        value: globalFilter,
      });
      // Add more fields for global search if needed
    }
    if (Array.isArray(filters)) {
      for (const filter of filters) {
        if (filter.value) {
          conditions.push({
            fieldName: filter.id,
            operator: "Contains",
            value: filter.value,
          });
        }
      }
    }
    const filterGroup: FilterGroup | undefined = conditions.length > 0 ? { operator: "And", conditions } : undefined;

    // Call the API directly
    const payload: QueryParametersDto & { sorting?: string } = {
      page: pageIndex + 1,
      maxResultCount: pageSize,
      ...(sortingStr ? { sorting: sortingStr } : {}),
      ...(filterGroup ? { filterGroup } : {}),
    };
    const response = await ekbProxyService.filterCargoList(payload);

    const data = response?.data;
    return {
      items: data?.items ?? [],
      totalCount: data?.totalCount ?? 0,
    };
  };

  // Handler for create
  const handleCreate = () => {
    setEditData(undefined);
    setDialogOpen(true);
  };

  // Handler for edit (to be passed to columns)
  const handleEdit = (row: CargoDto) => {
    setEditData(row);
    setDialogOpen(true);
  };

  return (
    <ContentCard>
      <DataGrid
        columns={cargoColumns(handleEdit)}
        title={t('menu.manageCargo')}
        queryFn={queryFn}
        queryKey={CARGO_QUERY_KEY}
        rowIdAccessor={row => String(row.id || row.docEntry || row.name || 'row-' + JSON.stringify(row))}
        enableRowSelection={true}
        defaultPageSize={10}
        manualSorting={true}
        manualFiltering={true}
        onCreate={handleCreate}
        createModalContent={dialogOpen ? (
          <CargoDialog
            open={dialogOpen}
            onClose={() => setDialogOpen(false)}
            initialData={editData}
            queryKey={CARGO_QUERY_KEY}
          />
        ) : null}
      />
    </ContentCard>
  );
};

const ManageCargoTable: React.FC = () => {
  return (
    <ErrorBoundary>
      <ManageCargoTableContent />
    </ErrorBoundary>
  );
};

export default ManageCargoTable; 