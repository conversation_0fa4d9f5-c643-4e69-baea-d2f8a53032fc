import{j as e,a as S,$ as v,f as U,u as Z,h as ee}from"./vendor-6tJeyfYI.js";import{B as f,S as se,c as ae,d as te,e as re,f as ie,u as ne,P as le,Q as oe,U as G,R as ce,V as de,A as ue}from"./app-layout-rNt37hVL.js";import{D as pe}from"./DataTable-CDIoPElA.js";import{F as me}from"./filter-sort-bar-MpsapXP_.js";import{T as he}from"./table-skeleton-CE69MDqJ.js";import{D as xe,b as ve,c as fe,d as ge,f as je,e as be}from"./dialog-BmEXyFlW.js";import{I as M}from"./input-DlXlkYlT.js";import{d as H,T as Ne,a as Ce,b as K,c as Q}from"./tabs-Dk-TLCdA.js";import{T as Se}from"./textarea-DwrdARTr.js";import{b as ye,C as Te,u as Ie,c as _,F as we}from"./index.esm-BubGICDC.js";import{M as Ae}from"./multi-select-Dsa7V91B.js";import{P as Ee}from"./plus-PD53KOti.js";import{P as Fe}from"./pencil-BTZ0_LzS.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";import"./table-BKSoE52x.js";import"./useDebounce-BdjXjarW.js";import"./index-X4QX0AQ3.js";import"./popover-ChFN9yvN.js";import"./tiny-invariant-CopsF_GD.js";import"./checkbox-D1loOtZt.js";import"./index-CaiFFM4D.js";import"./badge-DWaCYvGm.js";import"./arrow-up-DDQ17ADi.js";import"./skeleton-DAOxGMKm.js";import"./command-BPGQPJw5.js";import"./scroll-area-DuGBN-Ug.js";const De=({fields:t,append:m,remove:r,control:d,setValue:u,watch:o,searchValue:g,setSearchValue:c,userOptions:j,setUserOptions:b,userSearchMutation:E})=>e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("div",{className:"font-semibold",children:"Approvers"}),e.jsx(f,{type:"button",onClick:()=>m({approvalId:null,approverId:"",sequence:t.length+1,status:void 0,id:"00000000-0000-0000-0000-000000000000"}),size:"sm",children:"Add Approver"})]}),e.jsxs("div",{className:"space-y-2",children:[t.length===0&&e.jsx("div",{className:"text-muted-foreground text-sm",children:"No approvers added."}),t.map((F,h)=>{const p=o(`approvers.${h}.approverId`),T=j.find(l=>l.value===p),R=p&&!T?[{value:p,label:`Loading user... (${p.substring(0,8)}...)`},...j]:[...j];return e.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-2 border rounded p-2",children:[e.jsxs("span",{className:"font-semibold text-sm",children:["Approver #",h+1]}),e.jsx("input",{type:"hidden",...d.register(`approvers.${h}.id`)}),e.jsx("input",{type:"hidden",...d.register(`approvers.${h}.approvalId`)}),e.jsx("div",{className:"w-110",children:e.jsx(Ae,{options:R,value:p?[p]:[],onChange:l=>u(`approvers.${h}.approverId`,l[0]??"",{shouldDirty:!0}),placeholder:"Select user...",mode:"single",maxHeight:220,searchValue:g,onSearchValueChange:l=>{c(l),(l.length>=2||l.length===0)&&!E.isPending&&E.mutate(l,{onSuccess:y=>{const I=(y.items??[]).map(N=>({value:N.id??"",label:N.name||N.userName||N.email||"(no name)"}));b(I)}})}})}),e.jsx(M,{type:"number",...d.register(`approvers.${h}.sequence`),placeholder:"Sequence",className:"w-24"}),e.jsx(f,{type:"button",variant:"ghost",size:"icon",onClick:()=>r(h),"aria-label":"Remove Approver",children:e.jsx(H,{className:"w-4 h-4"})})]},F.id)})]})]}),Re=[{value:"Import",label:"Import"},{value:"Export",label:"Export"},{value:"Local",label:"Local"}],qe=({fields:t,append:m,remove:r})=>{const{control:d}=ye();return e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("div",{className:"font-semibold",children:"Criterias"}),e.jsx(f,{type:"button",onClick:()=>m({approvalId:null,documentType:void 0,id:"00000000-0000-0000-0000-000000000000"}),size:"sm",children:"Add Criteria"})]}),e.jsxs("div",{className:"space-y-2",children:[t.length===0&&e.jsx("div",{className:"text-muted-foreground text-sm",children:"No criterias added."}),t.map((u,o)=>e.jsxs("div",{className:"flex flex-col md:flex-row items-center gap-2 border rounded p-2",children:[e.jsxs("span",{className:"font-semibold text-sm",children:["Criteria #",o+1]}),e.jsx("input",{type:"hidden",...d.register(`criterias.${o}.id`)}),e.jsx("input",{type:"hidden",...d.register(`criterias.${o}.approvalId`)}),e.jsx(Te,{name:`criterias.${o}.documentType`,control:d,render:({field:g})=>e.jsxs(se,{value:g.value||"",onValueChange:g.onChange,children:[e.jsx(ae,{className:"w-100",children:e.jsx(te,{placeholder:"Select type"})}),e.jsx(re,{children:Re.map(c=>e.jsx(ie,{value:c.value,children:c.label},c.value))})]})}),e.jsx(f,{type:"button",variant:"ghost",size:"icon",onClick:()=>r(o),"aria-label":"Remove Criteria",children:e.jsx(H,{className:"w-4 h-4"})})]},u.id))]})]})},Pe=({open:t,onOpenChange:m,template:r})=>{const d=s=>{if(!s)return{name:"",description:"",code:"",approvers:[],criterias:[],stages:[]};const n=(s.approvers||[]).filter(i=>i.approverId).map(i=>({id:i.id||null,approvalId:i.approvalId||null,approverId:i.approverId,sequence:i.sequence||0,status:i.status||null})),P=(s.criterias||[]).map(i=>({id:i.id||null,approvalId:i.approvalId||null,documentType:i.documentType||null}));return{name:s.name||"",description:s.description||"",code:s.code||"",approvers:n,criterias:P,stages:[]}},u=Ie({defaultValues:d(r)}),{register:o,handleSubmit:g,reset:c,control:j,formState:{errors:b,isDirty:E,isSubmitting:F},setValue:h,watch:p}=u,{toast:T}=ne(),R=_({control:j,name:"approvers"}),l=_({control:j,name:"criterias"});S.useEffect(()=>{const s=d(r);c(s),t||(L(""),a([]),A(!1),x.current=!1,$.current=!1,z.current=null)},[r,t,c]);const y=U({mutationFn:async s=>le({body:s}),onSuccess:()=>{m(!1),c()},onError:s=>{T({title:s.error?.message||"Error",description:s.error?.details||"Failed to create approval template.",variant:"error"})}}),I=U({mutationFn:async s=>{if(!r?.id)throw new Error("No template id");return oe({path:{id:r.id},body:s})},onSuccess:()=>{m(!1),c()}}),N=s=>{const n={...s,approvers:s.approvers.map(({approvalId:P,...i})=>i),criterias:s.criterias.map(({approvalId:P,...i})=>i),stages:s.stages};r?.id?I.mutate(n):y.mutate(n)},q=F||y.isPending||I.isPending,w=y.error||I.error,[k,L]=v.useState(""),[D,a]=v.useState([]),[V,A]=v.useState(!1),x=v.useRef(!1),$=v.useRef(!1),z=v.useRef(null),B=U({mutationFn:async s=>(await G({body:{maxResultCount:20,skipCount:0,filterGroup:s?{operator:"Or",conditions:[{fieldName:"name",operator:"Contains",value:s},{fieldName:"userName",operator:"Contains",value:s},{fieldName:"email",operator:"Contains",value:s}]}:void 0}}))?.data??{items:[],totalCount:0}}),Y=v.useCallback(async s=>{if(!(s.length===0||$.current)){$.current=!0;try{const n={operator:"Or",conditions:s.map(C=>({fieldName:"id",operator:"Equal",value:C}))},J=(((await G({body:{maxResultCount:100,skipCount:0,filterGroup:n}}))?.data??{items:[],totalCount:0}).items??[]).map(C=>({value:C.id??"",label:C.name||C.userName||C.email||"(no name)"}));a(C=>{const W=new Set(C.map(O=>O.value)),X=J.filter(O=>!W.has(O.value));return[...C,...X]})}catch{}finally{$.current=!1}}},[]);return v.useEffect(()=>{if(t&&r?.approvers&&r.approvers.length>0&&z.current!==r.id){const s=r.approvers.map(n=>n.approverId).filter(Boolean);s.length>0&&(z.current=r.id||null,Y(s))}},[t,r]),v.useEffect(()=>{t&&!V&&D.length===0&&!x.current&&(x.current=!0,A(!0),B.mutate("",{onSuccess:s=>{a((s.items??[]).map(n=>({value:n.id??"",label:n.name||n.userName||n.email||"(no name)"}))),x.current=!1},onError:()=>{A(!1),x.current=!1}}))},[t,V,D.length]),e.jsx(xe,{open:t,onOpenChange:m,children:e.jsxs(ve,{size:"2xl",children:[e.jsxs(fe,{children:[e.jsx(ge,{children:"Approval Template"}),e.jsx(je,{children:"Fill in the details for the approval template."})]}),e.jsx(we,{...u,children:e.jsxs("form",{onSubmit:g(N),children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium mb-1",children:["Name ",e.jsx("span",{className:"text-destructive",children:"*"})]}),e.jsx(M,{...o("name",{required:"Name is required"}),className:"input input-bordered w-full",autoFocus:!0,"aria-invalid":!!b.name}),b.name&&e.jsx("div",{className:"text-xs text-destructive mt-1",children:b.name.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Code"}),e.jsx(M,{...o("code"),className:"input input-bordered w-full"})]}),e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Description"}),e.jsx(Se,{...o("description"),className:"input input-bordered w-full min-h-[60px]"})]})]}),e.jsxs(Ne,{defaultValue:"approvers",className:"w-full",children:[e.jsxs(Ce,{className:"mb-2",children:[e.jsx(K,{value:"approvers",children:"Approvers"}),e.jsx(K,{value:"criterias",children:"Criterias"})]}),e.jsx(Q,{value:"approvers",children:e.jsx(De,{...R,control:j,setValue:h,watch:p,searchValue:k,setSearchValue:L,userOptions:D,setUserOptions:a,userSearchMutation:B})}),e.jsx(Q,{value:"criterias",children:e.jsx(qe,{...l})})]}),w&&e.jsx("div",{className:"text-xs text-destructive mt-2",children:String(w instanceof Error?w.message:w)}),e.jsxs(be,{className:"mt-6",children:[e.jsx(f,{type:"button",variant:"outline",onClick:()=>m(!1),children:"Cancel"}),e.jsx(f,{type:"submit",className:"ml-2",disabled:q||!E,children:q?"Saving...":"Save"})]})]})})]})})},Le=[{value:"name",label:"Name"},{value:"description",label:"Description"},{value:"code",label:"Code"}],Ve=[{value:"Equals",label:"Equals"},{value:"Contains",label:"Contains"},{value:"NotEquals",label:"Not Equals"},{value:"GreaterThan",label:">"},{value:"LessThan",label:"<"}],$e=()=>{const[t,m]=S.useState({pageIndex:0,pageSize:10}),[r,d]=S.useState([]),[u,o]=S.useState([]),[g,c]=S.useState(!1),[j,b]=S.useState(!1),[E,F]=S.useState(null),h=Z();v.useEffect(()=>{m(a=>({...a,pageIndex:0}))},[r,u]);const p=S.useMemo(()=>{if(r.length)return{operator:"And",conditions:r}},[r]),T=S.useMemo(()=>{if(u.length)return u.map(a=>`${a.field} ${a.direction}`).join(", ")},[u]),{data:R,isLoading:l,error:y,refetch:I,isFetching:N}=ee({queryKey:["approval-templates",t.pageIndex,t.pageSize,T,p],queryFn:async()=>{const a=await de({body:{page:t.pageIndex+1,maxResultCount:t.pageSize,sorting:T,filterGroup:p}});function V(x){return!!x&&typeof x=="object"&&"items"in x&&"totalCount"in x}const A=a?.data??a;return V(A)?{items:A.items??[],totalCount:A.totalCount??0}:{items:[],totalCount:0}}}),q=R??{items:[],totalCount:0},w=async()=>{c(!0),await h.invalidateQueries({queryKey:["approval-templates",t.pageIndex,t.pageSize,T,p]}),c(!1)},k=()=>{F(null),b(!0)},L=a=>{F(a),b(!0)},D=[{accessorKey:"name",header:"Name",cell:a=>a.getValue()??"-"},{accessorKey:"description",header:"Description",cell:a=>a.getValue()??"-"},{accessorKey:"code",header:"Code",cell:a=>a.getValue()??"-"},{id:"edit",header:"",cell:({row:a})=>e.jsx(f,{onClick:()=>L(a.original),"aria-label":"Edit Approval Template",tabIndex:0,variant:"outline",size:"icon",className:"ml-2 h-8 w-8",children:e.jsx(Fe,{className:"w-4 h-4","aria-hidden":"true"})}),enableSorting:!1,enableColumnFilter:!1}];return y?e.jsxs("div",{className:"bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-destructive mb-4",children:"!"}),e.jsx("h3",{className:"font-semibold text-destructive mb-2",children:"Error loading data"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:String(y.message)}),e.jsx(f,{onClick:w,variant:"destructive",children:"Retry"})]}):e.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:[e.jsx("div",{className:"text-xl font-bold px-2 pt-2 pb-1",children:"Approval Template List"}),e.jsx(me,{filterFields:Le,operators:Ve,filters:r,sorts:u,onFiltersChange:d,onSortsChange:o,children:e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsx(f,{onClick:w,variant:"outline",size:"icon",className:"h-10 w-10",disabled:l||g||N,children:e.jsx(ce,{className:`h-4 w-4 ${g||N?"animate-spin":""}`})}),e.jsxs(f,{onClick:k,children:[e.jsx(Ee,{className:"h-5 w-5"})," New Approval Template"]})]})}),l?e.jsx(he,{columns:D}):e.jsx(pe,{title:"",columns:D,data:q.items,totalCount:q.totalCount,isLoading:l,manualPagination:!0,pageSize:t.pageSize,onPaginationChange:m,hideDefaultFilterbar:!0,enableRowSelection:!1,manualSorting:!0}),e.jsx(Pe,{open:j,onOpenChange:a=>{b(a),a||I()},template:E})]})},us=()=>e.jsx(ue,{children:e.jsx($e,{})});export{us as default};
//# sourceMappingURL=page-BSaNiimy.js.map
