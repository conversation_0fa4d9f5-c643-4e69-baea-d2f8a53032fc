import{j as r}from"./vendor-6tJeyfYI.js";import{S as t}from"./scroll-area-DuGBN-Ug.js";import{A as i}from"./app-layout-rNt37hVL.js";import{O as o}from"./overview-CTW9zS8n.js";import{$ as m}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./badge-DWaCYvGm.js";import"./skeleton-DAOxGMKm.js";import"./tiny-invariant-CopsF_GD.js";import"./table-BKSoE52x.js";function a({children:e,scrollable:s=!0}){return r.jsx(r.Fragment,{children:s?r.jsx(t,{className:"h-[calc(100dvh-52px)]",children:r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):r.jsx("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}function v(){return r.jsxs(i,{policy:"AbpIdentity.Users",children:[r.jsx(m,{title:"Users"}),r.jsx(a,{children:r.jsx(o,{})})]})}export{v as default};
//# sourceMappingURL=users-C9dW9sio.js.map
