{"version": 3, "file": "page-DPhoTYx-.js", "sources": ["../../../../../frontend/src/pages/master/manage-jetty/page.tsx"], "sourcesContent": ["import ManageJettyTable from '@/components/app/jetty/list';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Manage Jetty\" />\r\n      <ManageJettyTable />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["OverViewLayout", "AppLayout", "jsx", "Head", "ManageJettyTable"], "mappings": "suBAGA,SAAwBA,GAAiB,CACvC,cACGC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAK,MAAM,cAAe,CAAA,QAC1BC,EAAiB,CAAA,CAAA,CAAA,EACpB,CAEJ"}