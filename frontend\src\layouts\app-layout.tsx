'use client';

import ThemeProvider from "@/components/provider/theme-provider";
import ErrorBoundary from "@/components/ui/error-boundary";
import { Toaster } from "@/components/ui/toaster";
import type { IAppLayoutProps } from "@/lib/interfaces/IAppLayoutPros";
import { cn } from "@/lib/utils";
import Cookies from "js-cookie";
import { StrictMode, useEffect, useRef, useState } from "react";
import { usePage } from "@inertiajs/react";
import { ActiveThemeProvider, useThemeConfig } from "../components/active-theme";
import { PolicyGuard } from "../components/auth/policy-guard";
import AppSidebar from "../components/layout/app-sidebar";
import Header from "../components/layout/header";
import { SidebarInset, SidebarProvider } from "../components/ui/sidebar";

function AppLayoutContent({ children, policy, message }: IAppLayoutProps) {
  const { url } = usePage();
  const { activeTheme } = useThemeConfig();
  const isScaled = activeTheme.endsWith('-scaled');

  const previousUrl = useRef(url);

  // Calculate default sidebar state based on current route and cookie
  const getDefaultOpen = () => {
    const cookieValue = Cookies.get("sidebar_state");
    const isRootPage = url === "/";

    // If no cookie exists, use route-based defaults
    if (cookieValue === undefined) {
      return isRootPage; // true for root, false for others
    }

    // If we're on root page, respect the cookie
    if (isRootPage) {
      return cookieValue === "true";
    }

    // For non-root pages, default to closed
    return false;
  };

  const [sidebarKey, setSidebarKey] = useState(0);

  // Handle route changes
  useEffect(() => {
    if (previousUrl.current !== url) {
      const isRootPage = url === "/";
      const cookieValue = Cookies.get("sidebar_state");

      // For non-root pages, if cookie is true, set it to false
      if (!isRootPage && cookieValue === "true") {
        Cookies.set("sidebar_state", "false", { path: "/" });
      }

      // Force SidebarProvider to remount with new defaultOpen value
      setSidebarKey(prev => prev + 1);

      previousUrl.current = url;
    }
  }, [url]);

  return (
    <div
      className={cn(
        'bg-background font-sans antialiased',
        `theme-${activeTheme}`,
        isScaled ? 'theme-scaled' : ''
      )}
    >
      <Toaster />
      <SidebarProvider
        key={sidebarKey}
        defaultOpen={getDefaultOpen()}
      >
        <AppSidebar />
        <SidebarInset>
          <Header />
          <div className="flex flex-1 flex-col overflow-auto">
            <div className="@container/main flex flex-1 flex-col gap-2 bg-muted/60 dark:bg-transparent">
              <div className="flex flex-col gap-4 py-2 px-2 md:gap-4 md:py-4 md:px-4">
                {policy !== undefined ? (
                  <PolicyGuard policy={policy} message={message}>
                    {children}
                  </PolicyGuard>
                ) : children}
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </div>
  );
}

export default function AppLayout(props: IAppLayoutProps) {
  return (
    <StrictMode>
      <div id="portal-root"></div>
      <ErrorBoundary>
        {/* <ReactQueryProviders> */}
        <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
          <ActiveThemeProvider>
            <Toaster />
            <AppLayoutContent {...props} />
          </ActiveThemeProvider>
        </ThemeProvider>
        {/* </ReactQueryProviders> */}
      </ErrorBoundary>
    </StrictMode>
  );
}