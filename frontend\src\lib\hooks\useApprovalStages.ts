import { useQuery } from '@tanstack/react-query';
import { postApiIdjasApprovalStageFilterList } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterGroup, QueryParametersDto, PagedResultDtoOfApprovalStageDto } from '@/client/types.gen';

export const useApprovalStages = (
  pageIndex: number,
  pageSize: number,
  filterGroup?: FilterGroup,
  sorting?: string
) => {
  return useQuery<PagedResultDtoOfApprovalStageDto, Error>({
    queryKey: ['approval-stages', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],
    queryFn: async (): Promise<PagedResultDtoOfApprovalStageDto> => {
      const payload: QueryParametersDto = {
        skipCount: pageIndex * pageSize,
        maxResultCount: pageSize,
        sorting,
        filterGroup,
      };
      
      try {
        const response = await postApiIdjasApprovalStageFilterList({ body: payload });
        return response.data || { items: [], totalCount: 0 };
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Approval Stages';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('ApprovalStages API Error:', error);
        toast({
          title: 'Error loading Approval Stages',
          description: message,
          variant: 'destructive',
        });
        
        // Return empty result instead of throwing
        return { items: [], totalCount: 0 };
      }
    },
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
  });
}; 