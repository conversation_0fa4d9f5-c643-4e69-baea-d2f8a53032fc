import{j as o,u as S,h as N,f as T}from"./vendor-6tJeyfYI.js";import{F as y,A as W,u as b,M as m}from"./app-layout-rNt37hVL.js";import{I as j}from"./import-vessel-form-CB_uPBh9.js";import{t as i}from"./date-convert-rY_-W0p4.js";import{$ as E,q as B}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */import"./attachment-dialog-D7s9nIdd.js";import"./dialog-BmEXyFlW.js";import"./table-BKSoE52x.js";import"./tabs-Dk-TLCdA.js";const P=()=>{const{t:u}=y(),{props:l}=B(),{toast:c}=b(),s=typeof l.id=="string"?l.id:void 0,d=S(),{data:e,isLoading:I,isError:v,error:p}=N({queryKey:["import-vessel",s],queryFn:async()=>s?(await m.getImportVesselWithItems(s)).data:null,enabled:!!s}),f=e?{docNum:e.docNum??0,voyage:e.voyage??"",vesselArrival:i(e.vesselArrival??""),vesselDeparture:i(e.vesselDeparture??""),vesselId:e.vesselId??"",jettyId:e.jettyId??"",portOriginId:e.portOriginId??"",destinationPortId:e.destinationPortId??"",postingDate:e.postingDate??"",asideDate:i(e.asideDate??""),castOfDate:i(e.castOfDate??""),deleted:e.deleted??"",docType:e.docType??"",isChange:e.isChange??"",isLocked:e.isLocked??"",createdBy:e.createdBy??"",docStatus:e.docStatus??"",statusBms:e.statusBms??"",transType:e.transType??"",concurrencyStamp:e.concurrencyStamp??""}:{deleted:"",docType:"",isChange:"",isLocked:"",createdBy:"",docStatus:"",statusBms:"",transType:"",concurrencyStamp:""},h=e?.items?e.items.map(t=>{let r=null;t.unitWeight!=null&&(typeof t.unitWeight=="string"?r=t.unitWeight!==""?t.unitWeight:null:r=String(t.unitWeight));let n=null;return t.grossWeight!=null&&(typeof t.grossWeight=="string"?n=t.grossWeight!==""?Number(t.grossWeight):null:n=t.grossWeight),{itemName:t.itemName??null,itemQty:t.itemQty??0,unitQty:t.unitQty??null,remarks:t.remarks??null,ajuNo:t.ajuNo??null,regDate:t.regDate?t.regDate:null,regNo:t.regNo??null,grossWeight:n,unitWeight:r,shippingInstructionNo:t.shippingInstructionNo??null,shippingInstructionDate:t.shippingInstructionDate??null,letterNo:t.letterNo??null,letterDate:t.letterDate??null,status:t.status??null,regType:t.regType??null,attachments:t.attachments??[],tenant:t.tenantName??null,tenantId:t.tenantId??"",businessPartner:t.businessPartner?.name??null,businessPartnerId:t.businessPartnerId??null,concurrencyStamp:t.concurrencyStamp??void 0,id:t.id??void 0}}):[],g=T({mutationFn:async({header:t,items:r})=>{if(!s)throw new Error("No ID provided");const n=await m.updateImportVessel(s,{...t,docNum:Number(t.docNum),deleted:t.deleted??"",docType:t.docType??"",isChange:t.isChange??"",isLocked:t.isLocked??"",createdBy:t.createdBy??"",docStatus:t.docStatus??"Open",statusBms:t.statusBms??"",transType:t.transType??"",concurrencyStamp:t.concurrencyStamp??"",items:r.map(a=>({...a,regDate:a.regDate?a.regDate:null,tenantId:a.tenantId||"",businessPartnerId:a.businessPartnerId||""}))});if(n.error)throw new Error(n.error);return n.data},onSuccess:()=>{c({title:"Success",description:"Import vessel updated.",variant:"success"}),d.invalidateQueries({queryKey:["import-vessel",s]})},onError:t=>{c({title:t instanceof Error?t.message:t?.error?.message||"Error",description:t instanceof Error?void 0:t?.error?.details,variant:"destructive"})}}),D=async(t,r)=>{await g.mutateAsync({header:t,items:r})};return I?o.jsx("div",{children:"Loading..."}):v?o.jsxs("div",{children:["Error loading data: ",p instanceof Error?p.message:"Unknown error"]}):o.jsx(j,{mode:"edit",title:u("pages.vessel.edit.import"),initialHeader:f,initialItems:h,onSubmit:D,isSubmitting:g.isPending,queryClient:d,showAddLineButton:!1})};function Z(){const{t:u}=y();return o.jsxs(W,{children:[o.jsx(E,{title:u("pages.vessel.edit.import")}),o.jsx(P,{})]})}export{Z as default};
//# sourceMappingURL=page-C4MbW38Q.js.map
