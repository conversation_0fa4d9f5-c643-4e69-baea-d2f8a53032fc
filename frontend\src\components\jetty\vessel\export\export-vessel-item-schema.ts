import * as z from 'zod';

export const exportVesselItemSchema = z.object({
  itemName: z.string().optional(),
  itemQty: z.number().optional(),
  unitQty: z.string().optional(),
  remarks: z.string().optional(),
  tenant: z.string().optional(),
  tenantId: z.string().optional(),
  businessPartner: z.string().optional(),
  businessPartnerId: z.string().optional(),
  concurrencyStamp: z.string().optional(),
  shippingInstructionDate: z.string().optional(),
  letterDate: z.string().optional(),
  regDate: z.string().optional(),
  // Add more fields as needed from CreateUpdateVesselItemDto
});

export type ExportVesselItemForm = z.infer<typeof exportVesselItemSchema>; 