{"version": 3, "file": "table-BKSoE52x.js", "sources": ["../../../../../frontend/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nfunction Table({ className, ...props }: React.HTMLAttributes<HTMLTableElement>) {\n  return (\n    <div data-slot=\"table-wrapper\" className=\"relative w-full overflow-auto\">\n      <table data-slot=\"table\" className={cn('w-full caption-bottom text-foreground text-sm', className)} {...props} />\n    </div>\n  );\n}\n\nfunction TableHeader({ className, ...props }: React.HTMLAttributes<HTMLTableSectionElement>) {\n  return <thead data-slot=\"table-header\" className={cn('[&_tr]:border-b', className)} {...props} />;\n}\n\nfunction TableBody({ className, ...props }: React.HTMLAttributes<HTMLTableSectionElement>) {\n  return <tbody data-slot=\"table-body\" className={cn('[&_tr:last-child]:border-0', className)} {...props} />;\n}\n\nfunction TableFooter({ className, ...props }: React.HTMLAttributes<HTMLTableSectionElement>) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn('border-t bg-muted/50 font-medium last:[&>tr]:border-b-0', className)}\n      {...props}\n    />\n  );\n}\n\nfunction TableRow({ className, ...props }: React.HTMLAttributes<HTMLTableRowElement>) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        'border-b transition-colors [&:has(td):hover]:bg-muted/50 data-[state=selected]:bg-muted',\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction TableHead({ className, ...props }: React.ThHTMLAttributes<HTMLTableCellElement>) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        'h-12 px-4 text-left rtl:text-right align-middle font-normal text-muted-foreground [&:has([role=checkbox])]:pe-0',\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction TableCell({ className, ...props }: React.TdHTMLAttributes<HTMLTableCellElement>) {\n  return (\n    <td data-slot=\"table-cell\" className={cn('p-4 align-middle [&:has([role=checkbox])]:pe-0', className)} {...props} />\n  );\n}\n\nfunction TableCaption({ className, ...props }: React.HTMLAttributes<HTMLTableCaptionElement>) {\n  return (\n    <caption data-slot=\"table-caption\" className={cn('mt-4 text-sm text-muted-foreground', className)} {...props} />\n  );\n}\n\nexport { Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRow };\n"], "names": ["Table", "className", "props", "jsx", "cn", "TableHeader", "TableBody", "TableRow", "TableHead", "TableCell"], "mappings": "iFAKA,SAASA,EAAM,CAAE,UAAAC,EAAW,GAAGC,GAAiD,CAC9E,aACG,MAAI,CAAA,YAAU,gBAAgB,UAAU,gCACvC,SAACC,MAAA,QAAA,CAAM,YAAU,QAAQ,UAAWC,EAAG,gDAAiDH,CAAS,EAAI,GAAGC,CAAO,CAAA,EACjH,CAEJ,CAEA,SAASG,EAAY,CAAE,UAAAJ,EAAW,GAAGC,GAAwD,CACpF,OAAAC,MAAC,QAAM,CAAA,YAAU,eAAe,UAAWC,EAAG,kBAAmBH,CAAS,EAAI,GAAGC,CAAO,CAAA,CACjG,CAEA,SAASI,EAAU,CAAE,UAAAL,EAAW,GAAGC,GAAwD,CAClF,OAAAC,MAAC,QAAM,CAAA,YAAU,aAAa,UAAWC,EAAG,6BAA8BH,CAAS,EAAI,GAAGC,CAAO,CAAA,CAC1G,CAYA,SAASK,EAAS,CAAE,UAAAN,EAAW,GAAGC,GAAoD,CAElF,OAAAC,EAAA,IAAC,KAAA,CACC,YAAU,YACV,UAAWC,EACT,0FACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASM,EAAU,CAAE,UAAAP,EAAW,GAAGC,GAAuD,CAEtF,OAAAC,EAAA,IAAC,KAAA,CACC,YAAU,aACV,UAAWC,EACT,kHACAH,CACF,EACC,GAAGC,CAAA,CACN,CAEJ,CAEA,SAASO,EAAU,CAAE,UAAAR,EAAW,GAAGC,GAAuD,CAEtF,OAAAC,MAAC,KAAG,CAAA,YAAU,aAAa,UAAWC,EAAG,iDAAkDH,CAAS,EAAI,GAAGC,CAAO,CAAA,CAEtH"}