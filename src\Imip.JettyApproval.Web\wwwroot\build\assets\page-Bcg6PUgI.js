import{a as m,j as e}from"./vendor-6tJeyfYI.js";import{A as k,o as A,q as V,r as I,s as $,D as u,v as p,B as c,w,x as z,I as M,y as O,z as C}from"./app-layout-rNt37hVL.js";import{C as g}from"./checkbox-D1loOtZt.js";import{I as L}from"./input-DlXlkYlT.js";import{T as R,a as B,b,c as x,d as y,e as j}from"./table-BKSoE52x.js";import{I as H}from"./IconChevronDown-DtNUJLVx.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";const d=Array(10).fill(null).map((o,t)=>({id:`docked_${t+1}`,vesselName:`MV. SEAWORTHY ${t+1}`,jetty:`Jetty ${t%3===0?"A":t%3===1?"B":"C"}`,arrivalDateTime:`2025-05-${10+t} 08:00`,departureDateTime:`2025-05-${12+t} 17:00`,status:t%2===0?"Loading":"Unloading",cargo:t%2===0?"Containers":"Bulk Grain",agent:`Agent Corp ${t+1}`}));function P(){const[o,t]=m.useState(""),[i,h]=m.useState(new Set),[r,f]=m.useState(new Set(Object.keys(d[0]))),l=d.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(o.toLowerCase()))),N=s=>{h(s?new Set(l.map(a=>a.id)):new Set)},D=(s,a)=>{const n=new Set(i);a?n.add(s):n.delete(s),h(n)},S=(s,a)=>{const n=new Set(r);a?n.add(s):n.delete(s),f(n)},v=s=>{},T=s=>{};return e.jsx(k,{children:e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(A,{children:[e.jsx(V,{children:e.jsx(I,{className:"text-2xl font-bold",children:"Docked Vessels"})}),e.jsxs($,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(L,{placeholder:"Filter vessels...",value:o,onChange:s=>t(s.target.value),className:"max-w-sm"}),e.jsxs(u,{children:[e.jsx(p,{asChild:!0,children:e.jsxs(c,{variant:"outline",className:"ml-auto",children:["Columns ",e.jsx(H,{className:"ml-2 h-4 w-4"})]})}),e.jsx(w,{align:"end",children:Object.keys(d[0]).map(s=>e.jsx(z,{className:"capitalize",checked:r.has(s),onCheckedChange:a=>S(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(R,{children:[e.jsx(B,{children:e.jsxs(b,{children:[e.jsx(x,{className:"w-[30px]",children:e.jsx(g,{checked:i.size===l.length&&l.length>0,onCheckedChange:s=>N(s===!0)})}),Object.keys(d[0]).map(s=>r.has(s)&&s!=="id"&&e.jsx(x,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(x,{className:"text-right",children:"Actions"})]})}),e.jsx(y,{children:l.map(s=>e.jsxs(b,{children:[e.jsx(j,{children:e.jsx(g,{checked:i.has(s.id),onCheckedChange:a=>D(s.id,a===!0)})}),Object.entries(s).map(([a,n])=>r.has(a)&&a!=="id"&&e.jsx(j,{children:n},a)),e.jsx(j,{className:"text-right",children:e.jsxs(u,{children:[e.jsx(p,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(M,{className:"h-4 w-4"})]})}),e.jsxs(w,{align:"end",children:[e.jsx(O,{children:"Actions"}),e.jsx(C,{onClick:()=>v(s.id),children:"View Details"}),e.jsx(C,{onClick:()=>T(s.id),children:"Update Status"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[i.size," of ",l.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})})})}export{P as default};
//# sourceMappingURL=page-Bcg6PUgI.js.map
