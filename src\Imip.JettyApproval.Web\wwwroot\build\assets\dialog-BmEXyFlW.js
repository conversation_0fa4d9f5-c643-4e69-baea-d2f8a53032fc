import{j as t}from"./vendor-6tJeyfYI.js";import{l as s,X as d,$ as x}from"./app-layout-rNt37hVL.js";import{a as m,c,a0 as u,a1 as g,b as f,O as p,_ as b,af as j}from"./radix-e4nK4mWk.js";const w=x("flex flex-col fixed outline-0 z-50 border border-border bg-background p-6 shadow-lg shadow-black/5 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg",{variants:{size:{sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl","2xl":"sm:max-w-2xl","3xl":"sm:max-w-3xl","4xl":"sm:max-w-4xl","5xl":"sm:max-w-5xl","6xl":"sm:max-w-6xl","7xl":"sm:max-w-7xl",full:"sm:max-w-full"},variant:{default:"left-[50%] top-[50%] max-w-lg translate-x-[-50%] translate-y-[-50%] w-full",fullscreen:"inset-5"}},defaultVariants:{variant:"default",size:"lg"}});function k({...a}){return t.jsx(m,{"data-slot":"dialog",...a})}function C({...a}){return t.jsx(j,{"data-slot":"dialog-trigger",...a})}function D({...a}){return t.jsx(f,{"data-slot":"dialog-portal",...a})}function v({...a}){return t.jsx(b,{"data-slot":"dialog-close",...a})}function h({className:a,...e}){return t.jsx(p,{"data-slot":"dialog-overlay",className:s("fixed inset-0 z-50 bg-black/30 [backdrop-filter:blur(4px)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...e})}function T({className:a,children:e,close:o=!0,overlay:l=!0,variant:n,size:i,...r}){return t.jsxs(D,{children:[l&&t.jsx(h,{}),t.jsxs(c,{"data-slot":"dialog-content",className:s(w({variant:n,size:i}),a),...r,children:[e,o&&t.jsxs(v,{className:"cursor-pointer outline-0 absolute end-5 top-5 rounded-sm opacity-60 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(d,{className:"size-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}const O=({className:a,...e})=>t.jsx("div",{"data-slot":"dialog-header",className:s("flex flex-col space-y-1 text-center sm:text-start mb-5",a),...e}),P=({className:a,...e})=>t.jsx("div",{"data-slot":"dialog-footer",className:s("flex flex-col-reverse sm:flex-row sm:justify-end pt-5 sm:space-x-2.5",a),...e});function R({className:a,...e}){return t.jsx(u,{"data-slot":"dialog-title",className:s("text-lg font-semibold leading-none tracking-tight",a),...e})}function V({className:a,...e}){return t.jsx(g,{"data-slot":"dialog-description",className:s("text-sm text-muted-foreground",a),...e})}export{k as D,C as a,T as b,O as c,R as d,P as e,V as f,v as g};
//# sourceMappingURL=dialog-BmEXyFlW.js.map
