@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Default theme styles (used when no data-theme-preset is set or when 'default' is selected).
These serve as the fallback; there is no separate default.css file. */
:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.3211 0 0);
  --chart-2: oklch(0.4495 0 0);
  --chart-3: oklch(0.5693 0 0);
  --chart-4: oklch(0.6830 0 0);
  --chart-5: oklch(0.7921 0 0);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.9521 0 0);
  --chart-2: oklch(0.8576 0 0);
  --chart-3: oklch(0.7572 0 0);
  --chart-4: oklch(0.6534 0 0);
  --chart-5: oklch(0.5452 0 0);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer utilities {
  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-2xs {
    box-shadow: var(--shadow-2xs);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-xs {
    box-shadow: var(--shadow-xs);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-sm {
    box-shadow: var(--shadow-sm);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow {
    box-shadow: var(--shadow);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-md {
    box-shadow: var(--shadow-md);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-lg {
    box-shadow: var(--shadow-lg);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-xl {
    box-shadow: var(--shadow-xl);
  }

  [data-theme-preset]:not([data-theme-preset="default"]) .shadow-2xl {
    box-shadow: var(--shadow-2xl);
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground overscroll-none;
  }
}

.disable-transitions * {
  transition: none !important;
}

/** Custom Scrollbar **/
@layer base {
  ::-webkit-scrollbar {
    width: 5px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--input);
    border-radius: 5px;
  }

  * {
    scrollbar-width: thin;
    scrollbar-color: var(--input) transparent;
  }
}

/** Custom Container **/
@utility container {
  margin-inline: auto;
  padding-inline: 1.5rem;

  @media (width >=--theme(--breakpoint-sm)) {
    max-width: none;
  }

  @media (width >=1440px) {
    padding-inline: 2rem;
    max-width: 1440px;
  }
}

/** Smooth scroll **/
html {
  scroll-behavior: smooth;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

.handsontable.listbox {
  width: 400px !important;
  height: 300px !important;
}

.ht-theme-main,
.ht-theme-main-dark,
.ht-theme-main-dark-auto {
  --ht-font-size: 12px !important;
  --ht-line-height: 20px !important;
  --ht-font-weight: 400 !important;
  --ht-gap-size: 2px !important;
  --ht-icon-size: 15px !important;
  --ht-table-transition: 0.15s !important;
  --ht-accent-color: #388E3C !important;
  --ht-cell-horizontal-padding: 8px !important;
  --ht-cell-vertical-padding: 3px !important;
  --ht-cell-editor-border-width: 2px !important;
  --ht-cell-editor-border-color: #388E3C !important;
  --ht-cell-selection-border-color: #388E3C !important;
  --ht-cell-selection-background-color: #66BB6A !important;
  --ht-cell-autofill-size: 6px !important;
  --ht-cell-autofill-border-width: 1px !important;
  --ht-cell-autofill-border-radius: 4px !important;
  --ht-cell-autofill-background-color: #388E3C !important;
  --ht-cell-mobile-handle-size: 12px !important;
  --ht-cell-mobile-handle-border-width: 1px !important;
  --ht-cell-mobile-handle-border-radius: 6px !important;
  --ht-cell-mobile-handle-border-color: #388E3C !important;
  --ht-move-indicator-color: #388E3C !important;
  --ht-checkbox-size: 16px !important;
  --ht-checkbox-border-radius: 4px !important;
  --ht-checkbox-focus-ring-color: #388E3C !important;
  --ht-checkbox-checked-background-color: #388E3C !important;
  --ht-checkbox-checked-focus-background-color: #388E3C !important;
  --ht-header-font-weight: 400 !important;
  --ht-header-foreground-color: #222 !important;
  --ht-header-active-border-color: #43A047 !important;
  --ht-header-active-background-color: #388E3C !important;
  --ht-header-highlighted-shadow-size: 0 !important;
  --ht-header-row-active-background-color: #388E3C !important;
  --ht-icon-active-button-border-color: #43A047 !important;
  --ht-icon-active-button-background-color: #388E3C !important;
  --ht-icon-active-button-hover-border-color: #43A047 !important;
  --ht-icon-active-button-hover-background-color: #43A047 !important;
  --ht-button-border-radius: 4px !important;
  --ht-button-horizontal-padding: 12px !important;
  --ht-button-vertical-padding: 6px !important;
  --ht-primary-button-background-color: #388E3C !important;
  --ht-primary-button-focus-background-color: #388E3C !important;
  --ht-comments-textarea-horizontal-padding: 8px !important;
  --ht-comments-textarea-vertical-padding: 4px !important;
  --ht-comments-indicator-size: 6px !important;
  --ht-comments-indicator-color: #388E3C !important;
  --ht-comments-textarea-focus-border-width: 1px !important;
  --ht-comments-textarea-focus-border-color: #388E3C !important;
  --ht-license-horizontal-padding: 16px !important;
  --ht-license-vertical-padding: 8px !important;
  --ht-link-color: #388E3C !important;
  --ht-input-border-width: 1px !important;
  --ht-input-border-radius: 4px !important;
  --ht-input-horizontal-padding: 12px !important;
  --ht-input-vertical-padding: 6px !important;
  --ht-input-focus-border-color: #388E3C !important;
}

.ht-theme-horizon,
.ht-theme-horizon-dark,
.ht-theme-horizon-dark-auto {
  --ht-font-size: 12px !important;
  --ht-line-height: 20px !important;
  --ht-font-weight: 400;
  --ht-gap-size: 2px !important;
  --ht-icon-size: 14px !important;
  --ht-cell-horizontal-padding: 8px !important;
  --ht-cell-vertical-padding: 6px !important;
}

.ht_clone_top {
  z-index: 9 !important;
}

.ht_clone_inline_start {
  z-index: 9 !important;
}

.ht_clone_bottom {
  z-index: 9 !important;
}

.ht_clone_top_inline_start_corner {
  z-index: 9 !important;
}