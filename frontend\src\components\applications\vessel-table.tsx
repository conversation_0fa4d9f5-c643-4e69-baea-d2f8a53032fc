import type { FilterCondition, FilterGroup, FilterOperator, LogicalOperator, VesselHeaderDto } from "@/client/types.gen";
import FilterSortBar, { type SortDirection } from "@/components/filter-sort-bar";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useVesselData } from "@/lib/hooks/useVesselData";
import React, { useEffect, useMemo, useState } from "react";
import { TableSkeleton } from "../ui/TableSkeleton";

interface VesselTableProps {
  vesselType?: string;
  selectedVessel: VesselHeaderDto | null;
  onVesselSelect: (vessel: VesselHeaderDto | null) => void;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const FILTER_FIELDS = [
  { value: "docNum", label: "Document Number" },
  { value: "vesselName", label: "Vessel Name" },
  { value: "voyage", label: "Voyage" },
  { value: "arrival", label: "Arrival Date" },
  { value: "departure", label: "Departure Date" },
];

const FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [
  { value: "Equals", label: "Equals" },
  { value: "Contains", label: "Contains" },
  { value: "NotEquals", label: "Not Equals" },
  { value: "GreaterThan", label: ">" },
  { value: "LessThan", label: "<" },
];

const VesselTableContent: React.FC<VesselTableProps> = ({
  vesselType,
  selectedVessel,
  onVesselSelect,
  trigger,
  open,
  onOpenChange
}) => {
  // Remove internal dialog state if controlled
  const isControlled = typeof open === 'boolean' && typeof onOpenChange === 'function';
  const [internalDialogOpen, setInternalDialogOpen] = useState(false);
  const isDialogOpen = isControlled ? open! : internalDialogOpen;
  const setIsDialogOpen = isControlled ? onOpenChange! : setInternalDialogOpen;
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);
  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);

  // Build filter group for backend
  const filterGroup: FilterGroup | undefined = useMemo(() => {
    if (!filters.length) return undefined;
    return {
      operator: "And" as LogicalOperator,
      conditions: filters,
    };
  }, [filters]);

  // Fetch vessel data from API with filterGroup
  const { mutate, data: vesselResponse, isPending: isLoading, error } = useVesselData();

  // Trigger the mutation when vesselType or filterGroup changes
  useEffect(() => {
    if (vesselType) {
      mutate({ vesselType, filterGroup });
    }
  }, [vesselType, filterGroup, mutate]);

  // Extract vessels array and total count from the response
  const vessels = vesselResponse?.items ?? [];
  const totalCount = vesselResponse?.totalCount ?? 0;

  // Handle vessel selection
  const handleVesselSelect = () => {
    if (selectedRowIndex !== null && vessels[selectedRowIndex]) {
      onVesselSelect(vessels[selectedRowIndex]);
    } else {
      onVesselSelect(null);
    }
    setIsDialogOpen(false);
  };

  // Handle row selection with radio button
  const handleRowSelect = (index: number) => {
    setSelectedRowIndex(index);
  };

  const defaultTrigger = (
    <Button variant="outline" className="w-full justify-start font-normal">
      {selectedVessel ? selectedVessel.vesselName : "Select Vessel"}
    </Button>
  );

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      {/* Only render DialogTrigger if trigger is provided and not null */}
      {trigger !== null && (
        <DialogTrigger asChild>
          {trigger || defaultTrigger}
        </DialogTrigger>
      )}
      <DialogContent className="min-w-[1000px] w-auto max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Vessel {vesselType && `(${vesselType})`}</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <TableSkeleton
            rowCount={10}
            columnCount={4}
            hasTitle={true}
            hasSearch={true}
            hasFilters={true}
            hasPagination={true}
            hasActions={true}
          />
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-destructive">
            Error loading vessels
          </div>
        ) : vessels.length === 0 ? (
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            {vesselType ? `No vessels found for ${vesselType}` : 'Please select a vessel type first'}
          </div>
        ) : (
          <>
            <FilterSortBar
              filterFields={FILTER_FIELDS}
              operators={FILTER_OPERATORS}
              filters={filters}
              sorts={sorts}
              onFiltersChange={setFilters}
              onSortsChange={setSorts}
            />
            <div className="flex-grow overflow-hidden">
              <div className="relative overflow-hidden overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-y border-gray-200 dark:border-gray-800">
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs w-12">
                        Select
                      </TableHead>
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs">
                        Document Number
                      </TableHead>
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs">
                        Vessel Name
                      </TableHead>
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs">
                        Voyage
                      </TableHead>
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs">
                        Arrival Date
                      </TableHead>
                      <TableHead className="whitespace-nowrap py-1 text-sm sm:text-xs">
                        Departure Date
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {vessels.map((vessel, index) => (
                      <TableRow
                        key={vessel.id || index}
                        className="group select-none hover:bg-gray-50 dark:hover:bg-gray-900 cursor-pointer"
                        onClick={() => handleRowSelect(index)}
                      >
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400 w-12">
                          <input
                            type="radio"
                            name="vessel-selection"
                            checked={selectedRowIndex === index}
                            onChange={() => handleRowSelect(index)}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                          />
                        </TableCell>
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400">
                          {vessel.docEntry ?? "-"}
                        </TableCell>
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400">
                          {vessel.vesselName ?? "-"}
                        </TableCell>
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400">
                          {vessel.voyage ?? "-"}
                        </TableCell>
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400">
                          {vessel.vesselArrival ? new Date(vessel.vesselArrival).toLocaleDateString() : "-"}
                        </TableCell>
                        <TableCell className="relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400">
                          {vessel.vesselDeparture ? new Date(vessel.vesselDeparture).toLocaleDateString() : "-"}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                {totalCount > 0 && `Showing ${vessels.length} of ${totalCount} vessels`}
              </div>
              <Button onClick={handleVesselSelect} disabled={selectedRowIndex === null}>
                Select Vessel
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default VesselTableContent; 