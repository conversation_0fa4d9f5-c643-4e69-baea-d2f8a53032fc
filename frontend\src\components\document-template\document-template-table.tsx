import { postApiIdjasDocument<PERSON><PERSON>er<PERSON>ist, postApiIdjasDocumentTemplatesUpload } from "@/client/sdk.gen";
import type { DocumentTemplateDto, DocumentType } from "@/client/types.gen";
import { DataTable } from "@/components/data-table/DataTable";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { CellContext, ColumnDef, PaginationState, Row } from "@tanstack/react-table";
import { Pencil, Plus, RefreshCw } from "lucide-react";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { ContentCard } from "../layout/content-card";

const DOCUMENT_TYPE_OPTIONS: { value: DocumentType; label: string }[] = [
  { value: 1, label: "Invoice" },
  { value: 2, label: "Report" },
  { value: 3, label: "Contract" },
  { value: 4, label: "Letter" },
  { value: 5, label: "Certificate" },
  { value: 6, label: "RegistrationCard" },
  { value: 99, label: "Other" },
];

type DialogFormValues = {
  Name: string;
  DocumentType: DocumentType;
  Description?: string;
  IsDefault?: boolean;
  File: File | null;
};

const DocumentTemplateDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initial?: Partial<DocumentTemplateDto>;
  onSuccess: () => void;
}> = ({ open, onOpenChange, initial, onSuccess }) => {
  const { register, handleSubmit, reset, setValue, watch, control, formState: { errors, isSubmitting } } = useForm<Omit<DialogFormValues, "File">>({
    defaultValues: {
      Name: initial?.name ?? "",
      DocumentType: initial?.documentType ?? 1,
      Description: initial?.description ?? "",
      IsDefault: initial?.isDefault ?? false,
    },
  });
  const [file, setFile] = React.useState<File | null>(null);

  React.useEffect(() => {
    reset({
      Name: initial?.name ?? "",
      DocumentType: initial?.documentType ?? 1,
      Description: initial?.description ?? "",
      IsDefault: initial?.isDefault ?? false,
    });
    setFile(null);
  }, [initial, open, reset]);

  const mutation = useMutation({
    mutationFn: async (values: Omit<DialogFormValues, "File">) => {
      if (!file) throw new Error("No file selected");
      return postApiIdjasDocumentTemplatesUpload({
        body: {
          Name: values.Name,
          DocumentType: values.DocumentType,
          Description: values.Description,
          IsDefault: values.IsDefault,
          File: file,
        },
      });
    },
    onSuccess: () => {
      onSuccess();
      onOpenChange(false);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      if (!selectedFile.name.toLowerCase().endsWith('.docx') && !selectedFile.name.toLowerCase().endsWith('.pdf')) {
        e.target.value = '';
        setFile(null);
        return;
      }
      setFile(selectedFile);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{initial ? "Edit" : "Create"} Document Template</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit((values) => mutation.mutate(values))} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Name *</label>
            <Input {...register("Name", { required: "Name is required" })} className="w-full" />
            {errors.Name && <div className="text-xs text-destructive mt-1">{errors.Name.message}</div>}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Document Type *</label>
            <Controller
              name="DocumentType"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <Select
                  value={field.value ? String(field.value) : ""}
                  onValueChange={val => field.onChange(Number(val))}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select document type" />
                  </SelectTrigger>
                  <SelectContent>
                    {DOCUMENT_TYPE_OPTIONS.map(opt => (
                      <SelectItem key={opt.value} value={String(opt.value)}>
                        {opt.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <Textarea {...register("Description")} className="w-full min-h-[60px]" />
          </div>
          <div className="flex items-center gap-2">
            <Checkbox {...register("IsDefault")}
              id="isDefault"
              checked={!!watch("IsDefault")}
              onCheckedChange={val => setValue("IsDefault", !!val)}
            />
            <label htmlFor="isDefault" className="text-sm">Is Default</label>
          </div>
          <div>
            <Label htmlFor="templateFile" className="text-left">File *</Label>
            <Input
              id="templateFile"
              type="file"
              accept=".docx,.pdf"
              onChange={handleFileChange}
              disabled={mutation.isPending || isSubmitting}
            />
            {file && (
              <p className="text-sm text-muted-foreground">Selected file: {file.name}</p>
            )}
          </div>
          {mutation.error && <div className="text-xs text-destructive mt-2">{String(mutation.error instanceof Error ? mutation.error.message : mutation.error)}</div>}
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => { onOpenChange(false); setFile(null); }}>Cancel</Button>
            <Button type="submit" className="ml-2" disabled={isSubmitting || mutation.isPending}>{mutation.isPending ? "Saving..." : "Save"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

const DocumentTemplateTable: React.FC = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editing, setEditing] = useState<DocumentTemplateDto | null>(null);
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Table columns (moved here for setEditing/setDialogOpen scope)
  const columns: ColumnDef<DocumentTemplateDto>[] = [
    {
      accessorKey: "id",
      header: "Id",
      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? "-",
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? "-",
    },
    {
      accessorKey: "documentType",
      header: "Type",
      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? "-",
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? "-",
    },
    {
      accessorKey: "isDefault",
      header: "Default",
      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ? "Yes" : "No",
    },
    {
      id: "edit",
      header: "",
      cell: ({ row }: { row: Row<DocumentTemplateDto> }) => (
        <Button
          onClick={() => {
            setEditing(row.original);
            setDialogOpen(true);
          }}
          aria-label="Edit Document Template"
          tabIndex={0}
          variant="outline"
          size="icon"
          className="ml-2 h-8 w-8"
        >
          <Pencil className="w-4 h-4" aria-hidden="true" />
        </Button>
      ),
      enableSorting: false,
      enableColumnFilter: false,
    },
  ];

  // Query for document templates
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["document-templates", pagination],
    queryFn: async () => {
      const res = await postApiIdjasDocumentFilterList({
        body: {
          page: pagination.pageIndex + 1,
          maxResultCount: pagination.pageSize,
          skipCount: pagination.pageIndex * pagination.pageSize,
        },
      });
      // Always return an object with items and totalCount
      if (res && typeof res === 'object' && 'items' in res && 'totalCount' in res) return res;
      if (res && typeof res === 'object' && 'data' in res && res.data) return res.data;
      return { items: [], totalCount: 0 };
    },
  });

  const tableData: DocumentTemplateDto[] = Array.isArray(data?.items) ? data.items : [];
  const totalCount = typeof data?.totalCount === 'number' ? data.totalCount : 0;

  // Refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <ContentCard>
      <div className="text-xl font-bold px-2 pt-2 pb-1">Document Template List</div>
      <div className="flex justify-end mb-2 gap-2">
        <Button
          onClick={handleRefresh}
          variant="outline"
          size="icon"
          className="h-10 w-10"
          disabled={isLoading || isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
        </Button>
        <Button
          onClick={() => { setEditing(null); setDialogOpen(true); }}
        >
          <Plus className="h-3 w-3" /> New Document Template
        </Button>
      </div>
      <DataTable
        title=""
        columns={columns}
        data={tableData}
        totalCount={totalCount}
        isLoading={isLoading}
        manualPagination={true}
        pageSize={pagination.pageSize}
        onPaginationChange={setPagination}
        hideDefaultFilterbar={true}
        enableRowSelection={false}
        manualSorting={true}
      />
      <DocumentTemplateDialog
        open={dialogOpen}
        onOpenChange={(open) => {
          setDialogOpen(open);
          if (!open) setEditing(null);
        }}
        initial={editing ?? undefined}
        onSuccess={() => refetch()}
      />
    </ContentCard>
  );
};

export default DocumentTemplateTable; 