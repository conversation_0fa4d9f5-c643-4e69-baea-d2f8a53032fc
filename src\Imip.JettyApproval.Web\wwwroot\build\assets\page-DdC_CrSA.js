import{j as s,u as p,f as u}from"./vendor-6tJeyfYI.js";import{F as c,A as d,u as y,M as S}from"./app-layout-rNt37hVL.js";import{L as g}from"./local-vessel-form-B3V1uWrj.js";import{$ as v,m as f}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./DocumentPreviewDialog-C1lfiEeE.js";import"./dialog-BmEXyFlW.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */const I=()=>{const{t:r}=c(),{toast:n}=y(),m=p(),a=u({mutationFn:async({header:t,items:i})=>{const o=await S.createLocalVessel({...t,docType:"Local",vesselId:t.vesselId?String(t.vesselId):"",jettyId:t.jettyId?String(t.jettyId):"",concurrencyStamp:t.concurrencyStamp?String(t.concurrencyStamp):"",items:i.map(e=>({...e,createdBy:"",docType:"",isScan:"",isOriginal:"",isActive:!0,isDeleted:!1,isSend:"",isFeOri:"",isFeSend:"",isChange:"",isFeChange:"",isFeActive:"",deleted:"",isUrgent:"",tenantId:e.tenantId||"",businessPartnerId:e.businessPartnerId||"",concurrencyStamp:e.concurrencyStamp||""}))});if(o.error)throw new Error(o.error);return o.data},onSuccess:t=>{n({title:"Success",description:"Local vessel created.",variant:"success"}),t&&t.id&&f.visit(`/local/edit/${t.id}`)},onError:t=>{n({title:t instanceof Error?t.message:t?.error?.message||"Error",description:t instanceof Error?void 0:t?.error?.details,variant:"destructive"})}}),l=async(t,i)=>{await a.mutateAsync({header:t,items:i})};return s.jsx(g,{mode:"create",title:r("pages.vessel.create.local"),initialHeader:{},initialItems:[],onSubmit:l,isSubmitting:a.isPending,queryClient:m,jettyList:[]})};function H(){const{t:r}=c();return s.jsxs(d,{children:[s.jsx(v,{title:r("pages.vessel.create.local")}),s.jsx(I,{})]})}export{H as default};
//# sourceMappingURL=page-DdC_CrSA.js.map
