'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useDebounce } from "@/components/ui/useDebounce"
import { useJettyDataWithFilter } from "@/lib/hooks/useJettyDataWithFilter"
import { cn } from "@/lib/utils"
import { Check, ChevronsUpDown, Loader2 } from "lucide-react"
import * as React from 'react'
import { useEffect } from 'react'

interface JettySelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxHeight?: number;
}

export const JettySelector: React.FC<JettySelectorProps> = ({
  value,
  onValueChange,
  placeholder = 'Select jetty...',
  className,
  disabled = false,
  maxHeight = 300,
}) => {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)

  // Debounce the search value to avoid too many API calls
  const debouncedSearchValue = useDebounce(searchValue, 300)

  // Fetch jetty data with filter
  const { mutate: fetchJettyData, data: jettyData = [], isPending: isLoading, error } = useJettyDataWithFilter()

  // Fetch selected jetty data if we have a value but it's not in current results
  const { mutate: fetchSelectedJetty, data: selectedJettyFromApi = [] } = useJettyDataWithFilter()

  // Trigger the mutation when search value changes
  useEffect(() => {
    if (open) {
      fetchJettyData({
        maxResultCount: 20,
        skipCount: 0,
        filterGroup: debouncedSearchValue.trim() ? {
          operator: 'And',
          conditions: [{
            fieldName: 'name',
            operator: 'Contains',
            value: debouncedSearchValue.trim()
          }]
        } : undefined
      });
    }
  }, [debouncedSearchValue, open, fetchJettyData]);

  // Trigger the mutation for selected jetty when value changes
  useEffect(() => {
    if (value && open) {
      fetchSelectedJetty({
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [{
            fieldName: 'id',
            operator: 'Equals',
            value: value
          }]
        }
      });
    }
  }, [value, open, fetchSelectedJetty]);

  // Handle wheel events for scrolling
  const handleWheel = React.useCallback((e: WheelEvent) => {
    if (scrollAreaRef.current) {
      const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollableElement) {
        e.preventDefault();
        scrollableElement.scrollTop += e.deltaY;
      }
    }
  }, []);

  // Add wheel event listener when dropdown is open
  React.useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (open && scrollArea) {
      scrollArea.addEventListener('wheel', handleWheel, { passive: false });
      return () => {
        scrollArea.removeEventListener('wheel', handleWheel);
      };
    }
  }, [open, handleWheel]);

  // Get selected jetty data - check current results first, then fallback to API fetch
  const selectedJetty = React.useMemo(() => {
    // First check if selected jetty is in current filtered results
    const fromCurrentResults = jettyData.find(jetty => jetty.id === value || jetty.name === value);
    if (fromCurrentResults) return fromCurrentResults;
    
    // If not in current results, check the API fetch for the selected value
    if (value && selectedJettyFromApi.length > 0) {
      return selectedJettyFromApi.find(jetty => jetty.id === value || jetty.name === value);
    }
    
    return null;
  }, [jettyData, value, selectedJettyFromApi])

  // Handle selection
  const handleSelect = React.useCallback((jettyId: string) => {
    onValueChange(jettyId)
    setOpen(false)
    setSearchValue('') // Clear search when selection is made
  }, [onValueChange])

  // Handle search input change
  const handleSearchChange = React.useCallback((search: string) => {
    setSearchValue(search)
  }, [])

  // Display value for the trigger button
  const displayValue = selectedJetty ? selectedJetty.name || selectedJetty.alias || selectedJetty.id : placeholder

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between min-h-6 h-auto py-2",
            !value && "text-muted-foreground",
            className
          )}
          onClick={() => setOpen(!open)}
          disabled={disabled}
        >
          <span className="truncate">{displayValue}</span>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden"
        align="start"
        sideOffset={5}
      >
        <Command
          shouldFilter={false}
          className="max-h-full"
        >
          <CommandInput
            placeholder="Search jetties..."
            value={searchValue}
            onValueChange={handleSearchChange}
            className="h-9"
          />
          <CommandEmpty>
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </div>
            ) : error ? (
              <div className="text-center py-4 text-destructive">
                Error loading jetties
              </div>
            ) : (
              "No jetties found"
            )}
          </CommandEmpty>
          <ScrollArea
            className="overflow-hidden h-full"
            style={{ height: `${maxHeight - 40}px`, maxHeight: `${maxHeight - 40}px` }}
            ref={scrollAreaRef}
          >
            <CommandGroup>
              {jettyData.map((jetty) => {
                const isSelected = value === jetty.id || value === jetty.name
                const displayName = jetty.name || jetty.alias || jetty.id || 'Unknown'
                const jettyValue = jetty.id || jetty.name || ''
                
                return (
                  <CommandItem
                    key={jettyValue}
                    value={jettyValue}
                    onSelect={() => handleSelect(jettyValue)}
                    className={cn(
                      "flex items-center gap-2",
                      isSelected ? "bg-muted" : ""
                    )}
                  >
                    <div className={cn(
                      "flex h-4 w-4 items-center justify-center rounded-sm border",
                      isSelected
                        ? "bg-primary border-primary text-primary-foreground"
                        : "opacity-50"
                    )}>
                      {isSelected && <Check className="h-3 w-3" />}
                    </div>
                    <div className="flex flex-col">
                      <span className="font-medium">{displayName}</span>
                      {jetty.port && (
                        <span className="text-xs text-muted-foreground">
                          Port: {jetty.port}
                        </span>
                      )}
                    </div>
                  </CommandItem>
                )
              })}
            </CommandGroup>
          </ScrollArea>
        </Command>
      </PopoverContent>
    </Popover>
  )
} 