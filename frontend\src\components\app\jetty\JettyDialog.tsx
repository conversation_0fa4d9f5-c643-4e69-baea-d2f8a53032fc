import { postApiEkb<PERSON>etty, putApiEkbJettyById } from "@/clientEkb/sdk.gen";
import type { JettyDto } from "@/clientEkb/types.gen";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Divider } from "@/components/ui/divider";
import { FormField } from "@/components/ui/FormField";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

export type JettyDialogProps = {
  open: boolean;
  onClose: () => void;
  initialData?: Partial<JettyDto>;
  queryKey: unknown[];
};

const JettyDialog: React.FC<JettyDialogProps> = ({ open, onClose, initialData, queryKey }) => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const isEdit = Boolean(initialData && initialData.id);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<Partial<JettyDto>>({
    defaultValues: {
      name: initialData?.name ?? "",
      alias: initialData?.alias ?? "",
      max: initialData?.max ?? 0,
      port: initialData?.port ?? "",
      isCustomArea: initialData?.isCustomArea ?? false,
      docEntry: initialData?.docEntry ?? 0,
      deleted: initialData?.deleted ?? "",
      createdBy: initialData?.createdBy ?? 0,
      updatedBy: initialData?.updatedBy ?? 0,
    },
  });

  React.useEffect(() => {
    reset({
      name: initialData?.name ?? "",
      alias: initialData?.alias ?? "",
      max: initialData?.max ?? 0,
      port: initialData?.port ?? "",
      isCustomArea: initialData?.isCustomArea ?? false,
      docEntry: initialData?.docEntry ?? 0,
      deleted: initialData?.deleted ?? "",
      createdBy: initialData?.createdBy ?? 0,
      updatedBy: initialData?.updatedBy ?? 0,
    });
  }, [initialData, open, reset]);

  const createMutation = useMutation({
    mutationFn: async (values: Partial<JettyDto>) => {
      const payload = {
        ...values,
        name: values.name ?? "",
        alias: values.alias ?? "",
        max: values.max ?? 0,
        port: values.port ?? "",
        isCustomArea: values.isCustomArea ?? false,
        deleted: values.deleted ?? "",
        createdBy: values.createdBy ?? 0,
        updatedBy: values.updatedBy ?? 0,
        docEntry: values.docEntry ?? 0,
      };
      return postApiEkbJetty({ body: payload });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
  });

  const updateMutation = useMutation({
    mutationFn: async (values: Partial<JettyDto>) => {
      if (!initialData?.id) throw new Error("No id");
      const payload = {
        ...values,
        name: values.name ?? "",
        alias: values.alias ?? "",
        max: values.max ?? 0,
        port: values.port ?? "",
        isCustomArea: values.isCustomArea ?? false,
        deleted: values.deleted ?? "",
        createdBy: values.createdBy ?? 0,
        updatedBy: values.updatedBy ?? 0,
        docEntry: values.docEntry ?? 0,
      };
      return putApiEkbJettyById({ path: { id: initialData.id }, body: payload });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
  });

  const onSubmit = (values: Partial<JettyDto>) => {
    if (isEdit) {
      updateMutation.mutate(values);
    } else {
      createMutation.mutate(values);
    }
  };

  const isCustomAreaValue = watch("isCustomArea");

  return (
    <Dialog open={open} onOpenChange={v => { if (!v) onClose(); }}>
      <DialogContent size="md">
        <DialogHeader>
          <DialogTitle>{isEdit ? "Edit Jetty" : "Create Jetty"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <FormField label={t('form.labels.name')} labelWidth='120px'>
            <Input
              {...register("name", { required: "Name is required" })}
              aria-invalid={!!errors.name}
              aria-label="Jetty Name"
              autoFocus
            />
            {errors.name && <span className="text-red-500 text-xs">{errors.name.message as string}</span>}
          </FormField>
          <FormField label={t('form.labels.alias')} labelWidth='120px'>
            <Input
              {...register("alias", { required: "Alias is required" })}
              aria-invalid={!!errors.alias}
              aria-label="Alias"
            />
            {errors.alias && <span className="text-red-500 text-xs">{errors.alias.message as string}</span>}
          </FormField>
          <FormField label={t('form.labels.max')} labelWidth='120px'>
            <Input
              type="number"
              {...register("max", {
                required: "Max is required",
                valueAsNumber: true,
                validate: v => !isNaN(v as number) || "Max must be a number",
              })}
              aria-invalid={!!errors.max}
              aria-label="Max"
            />
            {errors.max && <span className="text-red-500 text-xs">{errors.max.message as string}</span>}
          </FormField>
          <FormField label={t('form.labels.port')} labelWidth='120px'>
            <Select
              value={watch("port") || ""}
              onValueChange={value => setValue("port", value, { shouldValidate: true })}
            >
              <SelectTrigger aria-label="Port" aria-invalid={!!errors.port}>
                <SelectValue placeholder="Select Port" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Labota">Labota</SelectItem>
                <SelectItem value="Fatufia">Fatufia</SelectItem>
              </SelectContent>
            </Select>
            {errors.port && <span className="text-red-500 text-xs">{errors.port.message as string}</span>}
          </FormField>
          <FormField label={t('form.labels.customArea')} labelWidth='120px'>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={!!isCustomAreaValue}
                onCheckedChange={checked => setValue("isCustomArea", !!checked, { shouldValidate: true })}
                aria-label="Is Custom Area"
              />
              <span>Is Custom Area</span>
            </div>
            {errors.isCustomArea && <span className="text-red-500 text-xs">{errors.isCustomArea.message as string}</span>}
          </FormField>
          <Divider className="my-2" />
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting || createMutation.isPending || updateMutation.isPending}>
              {isEdit ? "Save Changes" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default JettyDialog; 