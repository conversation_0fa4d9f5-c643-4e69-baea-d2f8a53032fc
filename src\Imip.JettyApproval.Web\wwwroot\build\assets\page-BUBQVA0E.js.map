{"version": 3, "file": "page-BUBQVA0E.js", "sources": ["../../../../../frontend/src/pages/non-custom-area/import/page.tsx"], "sourcesContent": ["import { ekbProxyService } from \"@/services/ekbProxyService\";\r\nimport { useImportColumns } from \"@/components/jetty/custom-area-jetty.columns\";\r\nimport { DataGrid } from \"@/components/ui/data-grid\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport { buildApiPayloadVessel } from \"@/lib/queryHelper/buildApiPayloadVessel\";\r\nimport { Head } from \"@inertiajs/react\";\r\nimport React from \"react\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\n\r\nconst DEFAULT_SORTING = [{ id: 'docNum', desc: true }];\r\nconst CustomAreaJettyContent: React.FC = () => {\r\n  const { t } = useTranslation();\r\n  const importColumns = useImportColumns()\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <DataGrid\r\n        columns={importColumns}\r\n        title={t(\"datagrid.nonCustomArea.importVessel\")}\r\n        queryKey={[\"import-vessel-list\"]}\r\n        manualSorting={true}\r\n        manualFiltering={true}\r\n        queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {\r\n          const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter, isCustomArea: false });\r\n          const res = await ekbProxyService.filterImportVessels(payload);\r\n          return {\r\n            items: res.data?.items ?? [],\r\n            totalCount: res.data?.totalCount ?? 0,\r\n          };\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ImportVesselPage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <Head title={`Import Vessel`} />\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <CustomAreaJettyContent />\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ImportVesselPage;"], "names": ["DEFAULT_SORTING", "CustomAreaJettyContent", "t", "useTranslation", "importColumns", "useImportColumns", "jsx", "DataGrid", "pageIndex", "pageSize", "sorting", "filters", "globalFilter", "payload", "buildApiPayloadVessel", "res", "ekbProxyService", "ImportVesselPage", "AppLayout", "Head"], "mappings": "ksBAUA,MAAMA,EAAkB,CAAC,CAAE,GAAI,SAAU,KAAM,GAAM,EAC/CC,EAAmC,IAAM,CACvC,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvBC,EAAgBC,EAAiB,EAErC,OAAAC,EAAAA,IAAC,MAAI,CAAA,UAAU,qEACb,SAAAA,EAAA,IAACC,EAAA,CACC,QAASH,EACT,MAAOF,EAAE,qCAAqC,EAC9C,SAAU,CAAC,oBAAoB,EAC/B,cAAe,GACf,gBAAiB,GACjB,QAAS,MAAO,CAAE,UAAAM,EAAW,SAAAC,EAAU,QAAAC,EAAUV,EAAiB,QAAAW,EAAS,aAAAC,KAAmB,CACtF,MAAAC,EAAUC,EAAsB,CAAE,UAAAN,EAAW,SAAAC,EAAU,QAAAC,EAAS,QAAAC,EAAS,aAAAC,EAAc,aAAc,GAAO,EAC5GG,EAAM,MAAMC,EAAgB,oBAAoBH,CAAO,EACtD,MAAA,CACL,MAAOE,EAAI,MAAM,OAAS,CAAC,EAC3B,WAAYA,EAAI,MAAM,YAAc,CACtC,CAAA,CACF,CAAA,EAEJ,CAEJ,EAEME,EAA6B,WAE9BC,EACC,CAAA,SAAA,CAACZ,EAAAA,IAAAa,EAAA,CAAK,MAAO,eAAiB,CAAA,QAC7B,MAAI,CAAA,UAAU,8BACb,SAAAb,EAAA,IAACL,IAAuB,CAC1B,CAAA,CAAA,EACF"}