{"version": 3, "file": "page-C-xPS9EJ.js", "sources": ["../../../../../frontend/src/pages/master/report/page.tsx"], "sourcesContent": ["import { EventCalendar } from '@/components/event-calendar';\r\nimport { type CalendarEvent } from '@/components/event-calendar/types';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useEffect, useState } from 'react';\r\n\r\ninterface JettyData {\r\n  name: string;\r\n  events: CalendarEvent[];\r\n}\r\n\r\nconst mockJettySchedules: JettyData[] = [\r\n  {\r\n    name: 'Jetty A',\r\n    events: [\r\n      { id: '1', title: 'Vessel A - Docking', start: new Date(2025, 4, 10, 8, 0), end: new Date(2025, 4, 10, 12, 0), color: 'sky', location: 'Jetty A' },\r\n      { id: '2', title: 'Maintenance', start: new Date(2025, 4, 11, 14, 0), end: new Date(2025, 4, 11, 17, 0), color: 'rose', location: 'Jetty A' },\r\n      { id: '6', title: 'Annual Inspection', start: new Date(2025, 4, 15), end: new Date(2025, 4, 16), allDay: true, color: 'orange', location: 'Jetty A' },\r\n      { id: '7', title: 'Vessel D - Loading', start: new Date(2025, 4, 18, 9, 30), end: new Date(2025, 4, 18, 16, 0), color: 'emerald', location: 'Jetty A' },\r\n    ],\r\n  },\r\n  {\r\n    name: 'Jetty B',\r\n    events: [\r\n      { id: '3', title: 'Vessel B - Loading', start: new Date(2025, 4, 12, 9, 0), end: new Date(2025, 4, 12, 18, 0), color: 'amber', location: 'Jetty B' },\r\n      { id: '4', title: 'Inspection', start: new Date(2025, 4, 13, 10, 0), end: new Date(2025, 4, 13, 11, 0), color: 'violet', location: 'Jetty B' },\r\n      { id: '8', title: 'Emergency Repair', start: new Date(2025, 4, 20, 7, 0), end: new Date(2025, 4, 20, 20, 0), color: 'rose', location: 'Jetty B' },\r\n      { id: '9', title: 'Vessel E - Unloading', start: new Date(2025, 4, 22, 11, 0), end: new Date(2025, 4, 23, 10, 0), color: 'sky', location: 'Jetty B' },\r\n    ],\r\n  },\r\n  {\r\n    name: 'Jetty C',\r\n    events: [\r\n      { id: '5', title: 'Vessel C - Unloading', start: new Date(2025, 4, 14, 7, 0), end: new Date(2025, 4, 14, 16, 0), color: 'emerald', location: 'Jetty C' },\r\n      { id: '10', title: 'Routine Cleaning', start: new Date(2025, 4, 25, 8, 0), end: new Date(2025, 4, 25, 12, 0), color: 'violet', location: 'Jetty C' },\r\n      { id: '11', title: 'Vessel F - Docking', start: new Date(2025, 4, 28, 6, 0), end: new Date(2025, 4, 29, 14, 0), color: 'amber', location: 'Jetty C' },\r\n    ],\r\n  },\r\n];\r\n\r\nexport default function JettySchedule() {\r\n  const [selectedJetty, setSelectedJetty] = useState<string | null>(mockJettySchedules[0]?.name || null);\r\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\r\n\r\n  const handleEventAdd = (event: CalendarEvent) => {\r\n    setEvents((prevEvents) => [...prevEvents, event]);\r\n  };\r\n\r\n  const handleEventUpdate = (updatedEvent: CalendarEvent) => {\r\n    setEvents((prevEvents) =>\r\n      prevEvents.map((event) =>\r\n        event.id === updatedEvent.id ? updatedEvent : event\r\n      )\r\n    );\r\n  };\r\n\r\n  const handleEventDelete = (eventId: string) => {\r\n    setEvents((prevEvents) => prevEvents.filter((event) => event.id !== eventId));\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (selectedJetty) {\r\n      const jettyData = mockJettySchedules.find(jetty => jetty.name === selectedJetty);\r\n      setEvents(jettyData ? jettyData.events : []);\r\n    } else {\r\n      setEvents([]);\r\n    }\r\n  }, [selectedJetty]);\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"container mx-auto p-4\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-2xl font-bold\">Jetty Schedule</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"mb-4 flex items-center gap-4\">\r\n              <label htmlFor=\"jetty-select\" className=\"font-medium\">Select Jetty:</label>\r\n              <Select onValueChange={setSelectedJetty} value={selectedJetty || ''}>\r\n                <SelectTrigger id=\"jetty-select\" className=\"w-[200px]\">\r\n                  <SelectValue placeholder=\"Select a jetty\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {mockJettySchedules.map((jetty) => (\r\n                    <SelectItem key={jetty.name} value={jetty.name}>\r\n                      {jetty.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n            <EventCalendar\r\n              events={events}\r\n              initialView=\"month\"\r\n              onEventAdd={handleEventAdd}\r\n              onEventUpdate={handleEventUpdate}\r\n              onEventDelete={handleEventDelete}\r\n            />\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["mockJettySchedules", "JettySchedule", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedJetty", "useState", "events", "setEvents", "handleEventAdd", "event", "prevEvents", "handleEventUpdate", "updatedEvent", "handleEventDelete", "eventId", "useEffect", "jettyData", "jetty", "AppLayout", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "EventCalendar"], "mappings": "khBAYA,MAAMA,EAAkC,CACtC,CACE,KAAM,UACN,OAAQ,CACN,CAAE,GAAI,IAAK,MAAO,qBAAsB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,MAAO,SAAU,SAAU,EACjJ,CAAE,GAAI,IAAK,MAAO,cAAe,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,OAAQ,SAAU,SAAU,EAC5I,CAAE,GAAI,IAAK,MAAO,oBAAqB,MAAO,IAAI,KAAK,KAAM,EAAG,EAAE,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,EAAE,EAAG,OAAQ,GAAM,MAAO,SAAU,SAAU,SAAU,EACpJ,CAAE,GAAI,IAAK,MAAO,qBAAsB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,EAAE,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,UAAW,SAAU,SAAU,CAAA,CAE1J,EACA,CACE,KAAM,UACN,OAAQ,CACN,CAAE,GAAI,IAAK,MAAO,qBAAsB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,QAAS,SAAU,SAAU,EACnJ,CAAE,GAAI,IAAK,MAAO,aAAc,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,SAAU,SAAU,SAAU,EAC7I,CAAE,GAAI,IAAK,MAAO,mBAAoB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,OAAQ,SAAU,SAAU,EAChJ,CAAE,GAAI,IAAK,MAAO,uBAAwB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,MAAO,SAAU,SAAU,CAAA,CAExJ,EACA,CACE,KAAM,UACN,OAAQ,CACN,CAAE,GAAI,IAAK,MAAO,uBAAwB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,UAAW,SAAU,SAAU,EACvJ,CAAE,GAAI,KAAM,MAAO,mBAAoB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,SAAU,SAAU,SAAU,EACnJ,CAAE,GAAI,KAAM,MAAO,qBAAsB,MAAO,IAAI,KAAK,KAAM,EAAG,GAAI,EAAG,CAAC,EAAG,IAAK,IAAI,KAAK,KAAM,EAAG,GAAI,GAAI,CAAC,EAAG,MAAO,QAAS,SAAU,SAAU,CAAA,CACtJ,CAEJ,EAEA,SAAwBC,GAAgB,CAChC,KAAA,CAACC,EAAeC,CAAgB,EAAIC,EAAAA,SAAwBJ,EAAmB,CAAC,GAAG,MAAQ,IAAI,EAC/F,CAACK,EAAQC,CAAS,EAAIF,EAAAA,SAA0B,CAAA,CAAE,EAElDG,EAAkBC,GAAyB,CAC/CF,EAAWG,GAAe,CAAC,GAAGA,EAAYD,CAAK,CAAC,CAClD,EAEME,EAAqBC,GAAgC,CACzDL,EAAWG,GACTA,EAAW,IAAKD,GACdA,EAAM,KAAOG,EAAa,GAAKA,EAAeH,CAAA,CAElD,CACF,EAEMI,EAAqBC,GAAoB,CACnCP,EAACG,GAAeA,EAAW,OAAQD,GAAUA,EAAM,KAAOK,CAAO,CAAC,CAC9E,EAEAC,OAAAA,EAAAA,UAAU,IAAM,CACd,GAAIZ,EAAe,CACjB,MAAMa,EAAYf,EAAmB,KAAcgB,GAAAA,EAAM,OAASd,CAAa,EAC/EI,EAAUS,EAAYA,EAAU,OAAS,CAAA,CAAE,CAAA,MAE3CT,EAAU,CAAA,CAAE,CACd,EACC,CAACJ,CAAa,CAAC,QAGfe,EACC,CAAA,SAAAC,EAAA,IAAC,OAAI,UAAU,wBACb,gBAACC,EACC,CAAA,SAAA,CAAAD,EAAAA,IAACE,GACC,SAACF,EAAA,IAAAG,EAAA,CAAU,UAAU,qBAAqB,0BAAc,CAC1D,CAAA,SACCC,EACC,CAAA,SAAA,CAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,+BACb,SAAA,CAAAL,MAAC,QAAM,CAAA,QAAQ,eAAe,UAAU,cAAc,SAAa,gBAAA,SAClEM,EAAO,CAAA,cAAerB,EAAkB,MAAOD,GAAiB,GAC/D,SAAA,CAACgB,EAAAA,IAAAO,EAAA,CAAc,GAAG,eAAe,UAAU,YACzC,SAACP,EAAAA,IAAAQ,EAAA,CAAY,YAAY,gBAAA,CAAiB,CAC5C,CAAA,QACCC,EACE,CAAA,SAAA3B,EAAmB,IAAKgB,GACtBE,EAAAA,IAAAU,EAAA,CAA4B,MAAOZ,EAAM,KACvC,SAAMA,EAAA,MADQA,EAAM,IAEvB,CACD,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACAE,EAAA,IAACW,EAAA,CACC,OAAAxB,EACA,YAAY,QACZ,WAAYE,EACZ,cAAeG,EACf,cAAeE,CAAA,CAAA,CACjB,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,CAEJ"}