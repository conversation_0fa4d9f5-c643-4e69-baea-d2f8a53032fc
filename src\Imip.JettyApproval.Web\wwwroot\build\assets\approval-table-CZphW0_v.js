import{a as T,j as t,u as C}from"./vendor-6tJeyfYI.js";import{k as w,u as k,B as y,M as g,F as V,G as I,H as f,J as A,K as B}from"./app-layout-rNt37hVL.js";import{D as F}from"./data-grid-DZ2U-5jU.js";import{B as O}from"./badge-DWaCYvGm.js";import{m as N}from"./App-DnhJzTNn.js";import{D as P,b as q,c as E,d as R,f as W,e as L}from"./dialog-BmEXyFlW.js";import{T as K}from"./textarea-DwrdARTr.js";import{A as U}from"./arrow-up-right-DyuQRH0Y.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]],z=w("circle-check",$);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Q=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],_=w("circle-x",Q),G=({approvalId:x,isOpen:D,onClose:r,action:o,vessel:i,onSuccess:b,queryClient:j})=>{const{toast:v}=k(),[m,a]=T.useState(""),[n,s]=T.useState(!1),l=async()=>{s(!0);try{const c=o==="approve"?`/api/idjas/approval/approve/${x}`:`/api/idjas/approval/reject/${x}`,S=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:m})});if(!S.ok){const u=await S.json();throw new Error(u.error||`Failed to ${o} approval`)}if(i?.id&&i?.vesselType)try{const u=o==="approve"?"Approved":"Rejected";switch(i.vesselType){case"Import":{const p=await g.getImportVessel(i.id);if(p.data){const e=p.data,h={createdBy:e.createdBy||"",isLocked:e.isLocked||"N",isChange:e.isChange||"N",transType:e.transType||"",docType:e.docType||"",deleted:e.deleted||"N",docStatus:u,statusBms:e.statusBms||"",concurrencyStamp:e.concurrencyStamp||"",docNum:e.docNum,bp:e.bp,vesselName:e.vesselName,shipment:e.shipment,shipmentNo:e.shipmentNo,vesselArrival:e.vesselArrival,updatedBy:e.updatedBy,createdAt:e.createdAt,updatedAt:e.updatedAt,postingDate:e.postingDate,color:e.color,flags:e.flags,remarks:e.remarks,status:e.status,bcType:e.bcType,portOrigin:e.portOrigin,emailToPpjk:e.emailToPpjk,matchKey:e.matchKey,voyage:e.voyage,grossWeight:e.grossWeight,vesselFlag:e.vesselFlag,vesselDeparture:e.vesselDeparture,vesselStatus:e.vesselStatus,jetty:e.jetty,destinationPort:e.destinationPort,berthingDate:e.berthingDate,anchorageDate:e.anchorageDate,type:e.type,jettyUpdate:e.jettyUpdate,reportDate:e.reportDate,unloadingDate:e.unloadingDate,finishUnloadingDate:e.finishUnloadingDate,grtWeight:e.grtWeight,invoiceStatus:e.invoiceStatus,agentId:e.agentId,agentName:e.agentName,surveyorId:e.surveyorId,tradingId:e.tradingId,jettyId:e.jettyId,vesselId:e.vesselId,masterAgentId:e.masterAgentId,masterTradingId:e.masterTradingId,masterSurveyorId:e.masterSurveyorId};await g.updateImportVessel(i.id,h)}else throw new Error("Failed to fetch existing import vessel data");break}case"Export":{const p=await g.getExportVessel(i.id);if(p.data){const e=p.data,h={docNum:e.docNum||"",postingDate:e.postingDate,vesselName:e.vesselName,vesselArrival:e.vesselArrival,voyage:e.voyage||"",shipment:e.shipment||"",vesselQty:e.vesselQty,portOrigin:e.portOrigin||"",deleted:e.deleted||"N",updatedBy:e.updatedBy,docStatus:u,statusBms:e.statusBms||"",docType:e.docType||"",concurrencyStamp:e.concurrencyStamp||"",vesselDeparture:e.vesselDeparture,destinationPort:e.destinationPort,remarks:e.remarks,grossWeight:e.grossWeight,vesselFlag:e.vesselFlag,vesselStatus:e.vesselStatus,jetty:e.jetty,berthingDate:e.berthingDate,anchorageDate:e.anchorageDate,reportDate:e.reportDate,unloadingDate:e.unloadingDate,finishUnloadingDate:e.finishUnloadingDate,deadWeight:e.deadWeight,grtWeight:e.grtWeight,invoiceStatus:e.invoiceStatus,agentId:e.agentId,agentName:e.agentName,surveyorId:e.surveyorId,tradingId:e.tradingId,jettyId:e.jettyId,vesselId:e.vesselId,masterAgentId:e.masterAgentId,masterTradingId:e.masterTradingId,masterSurveyorId:e.masterSurveyorId};await g.updateExportVessel(i.id,h)}else throw new Error("Failed to fetch existing export vessel data");break}case"LocalIn":case"LocalOut":{const p=await g.getLocalVessel(i.id);if(p.data){const e=p.data,h={docNum:e.docNum||"",deleted:e.deleted||"N",shipment:e.shipment||"",statusBms:e.statusBms||"",transType:e.transType||"",portOrigin:e.portOrigin||"",vesselType:e.vesselType||"Local",destinationPort:e.destinationPort||"",concurrencyStamp:e.concurrencyStamp||"",docType:"Local",docStatus:u,postingDate:e.postingDate,vesselName:e.vesselName,tongkang:e.tongkang,vesselArrival:e.vesselArrival,vesselDeparture:e.vesselDeparture,vesselQty:e.vesselQty,remark:e.remark,updatedBy:e.updatedBy,voyage:e.voyage,grossWeight:e.grossWeight,jetty:e.jetty,status:e.status,beratTugboat:e.beratTugboat,berthingDate:e.berthingDate,anchorageDate:e.anchorageDate,reportDate:e.reportDate,unloadingDate:e.unloadingDate,finishUnloadingDate:e.finishUnloadingDate,grtWeight:e.grtWeight,invoiceStatus:e.invoiceStatus,agentId:e.agentId,agentName:e.agentName,grtVessel:e.grtVessel,surveyorId:e.surveyorId,tradingId:e.tradingId,jettyId:e.jettyId,vesselId:e.vesselId,bargeId:e.bargeId,masterAgentId:e.masterAgentId,masterTradingId:e.masterTradingId,masterSurveyorId:e.masterSurveyorId};await g.updateLocalVessel(i.id,h)}else throw new Error("Failed to fetch existing local vessel data");break}default:}}catch{v({title:"Warning",description:"Approval processed but vessel status update failed",variant:"destructive"})}v({title:"Success",description:`Approval ${o}d successfully`,variant:"default"}),await j.invalidateQueries({queryKey:["approval-stages"]}),b?.(),r(),a("")}catch(c){v({title:"Error",description:c instanceof Error?c.message:`Failed to ${o} approval`,variant:"destructive"})}finally{s(!1)}},d=()=>{n||(r(),a(""))};return t.jsx(P,{open:D,onOpenChange:d,children:t.jsxs(q,{className:"sm:max-w-[425px]",children:[t.jsxs(E,{children:[t.jsxs(R,{children:[o==="approve"?"Approve":"Reject"," Request"]}),t.jsx(W,{children:o==="approve"?"Are you sure you want to approve this request? You can add optional notes below.":"Please provide a reason for rejecting this request."})]}),t.jsx("div",{className:"grid gap-4 py-4",children:t.jsxs("div",{className:"grid gap-2",children:[t.jsx("label",{htmlFor:"notes",className:"text-sm font-medium",children:o==="approve"?"Notes (Optional)":"Rejection Reason"}),t.jsx(K,{id:"notes",placeholder:o==="approve"?"Add any additional notes...":"Please explain why this request is being rejected...",value:m,onChange:c=>a(c.target.value),rows:4,disabled:n})]})}),t.jsxs(L,{children:[t.jsx(y,{type:"button",variant:"outline",onClick:d,disabled:n,children:"Cancel"}),t.jsx(y,{type:"button",variant:o==="approve"?"default":"destructive",onClick:l,disabled:n||o==="reject"&&!m.trim(),children:n?"Processing...":o==="approve"?"Approve":"Reject"})]})]})})},ae=()=>{const{toast:x}=k(),D=C(),{t:r}=V(),[o,i]=T.useState({isOpen:!1,approvalId:"",action:"approve"}),b=a=>{i(a)},j=()=>{i(a=>({...a,isOpen:!1}))},v=()=>{j()},m=[{accessorKey:"vessel.vesselName",header:r("table.vesselName"),cell:a=>a.getValue()??"-"},{accessorKey:"vessel.voyage",header:r("table.voyage"),cell:a=>a.getValue()??"-"},{accessorKey:"vessel.vesselType",header:r("table.vesselType"),cell:a=>a.getValue()??"-"},{accessorKey:"vessel.vesselArrival",header:r("table.arrival"),cell:a=>a.getValue()?new Date(a.getValue()).toLocaleString():"-"},{accessorKey:"vessel.destinationPort",header:r("table.destinationPort"),cell:a=>a.getValue()??"-"},{accessorKey:"requestDate",header:r("table.requestDate"),cell:a=>a.getValue()?new Date(a.getValue()).toLocaleDateString():"-"},{accessorKey:"status",header:r("table.status"),cell:a=>{const n=a.getValue();let s,l;switch(n){case 0:s=r("table.statusPending"),l="warning";break;case 1:s=r("table.statusApproved"),l="success";break;case 2:s=r("table.statusRejected"),l="destructive";break;case 3:s=r("table.statusCancelled"),l="secondary";break;default:s=r("table.statusUnknown"),l="primary";break}return t.jsx(O,{variant:l,size:"sm",children:s})}},{accessorKey:"requesterUserName",header:r("table.requester"),cell:a=>a.getValue()??"-"},{id:"actions",header:r("table.actions"),cell:({row:a,table:n})=>{const s=a.original,l=s.status===0,d=s.vessel,c=n.options.meta;return t.jsxs("div",{className:"flex space-x-1",children:[l&&t.jsxs(t.Fragment,{children:[t.jsxs(I,{children:[t.jsx(f,{asChild:!0,children:t.jsx(y,{variant:"ghost",size:"icon",className:"text-green-600 hover:text-green-700",onClick:()=>{c.setApprovalAction({isOpen:!0,approvalId:s.id||"",action:"approve",vessel:s.vessel})},"aria-label":"Approve",children:t.jsx(z,{className:"w-5 h-5"})})}),t.jsx(A,{children:r("table.approveRequest")})]}),t.jsxs(I,{children:[t.jsx(f,{asChild:!0,children:t.jsx(y,{variant:"ghost",size:"icon",className:"text-red-600 hover:text-red-700",onClick:()=>{c.setApprovalAction({isOpen:!0,approvalId:s.id||"",action:"reject",vessel:s.vessel})},"aria-label":"Reject",children:t.jsx(_,{className:"w-5 h-5"})})}),t.jsx(A,{children:r("table.rejectRequest")})]})]}),t.jsxs(I,{children:[t.jsx(f,{asChild:!0,children:t.jsx(y,{variant:"ghost",size:"icon",onClick:()=>{d&&d.id&&(d.vesselType==="Import"?N.visit(`/import/edit/${d.id}`):d.vesselType==="Export"?N.visit(`/export/edit/${d.id}`):N.visit(`/local/edit/${d.id}`))},"aria-label":"Details",children:t.jsx(U,{className:"w-5 h-5"})})}),t.jsx(A,{children:r("table.viewDetails")})]})]})}}];return t.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:[t.jsx(F,{columns:m,title:r("datagrid.pendingApprovals"),queryKey:["approval-stages"],manualSorting:!0,manualFiltering:!0,autoSizeColumns:!0,meta:{setApprovalAction:b},queryFn:async({pageIndex:a,pageSize:n})=>{try{const s=await B({body:{maxResultCount:n,skipCount:a*n,filterGroup:{operator:"And",conditions:[{fieldName:"status",operator:"Equals",value:"0"}]}}});if(!s||!s.data)throw new Error("Failed to fetch approvals");return{items:s.data.items||[],totalCount:s.data.totalCount||0}}catch{return x({title:"Error",description:"Failed to load approval list",variant:"destructive"}),{items:[],totalCount:0}}}}),t.jsx(G,{approvalId:o.approvalId,isOpen:o.isOpen,onClose:j,action:o.action,vessel:o.vessel,onSuccess:v,queryClient:D})]})};export{ae as A};
//# sourceMappingURL=approval-table-CZphW0_v.js.map
