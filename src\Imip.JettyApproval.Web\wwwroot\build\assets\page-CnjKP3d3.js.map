{"version": 3, "file": "page-CnjKP3d3.js", "sources": ["../../../../../frontend/src/pages/application/page.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';\r\nimport React, { useState } from 'react';\r\n\r\ninterface ApplicationRequest {\r\n  id: string;\r\n  vesselName: string;\r\n  arrivalDate: string;\r\n  departureDate: string;\r\n  itemName: string;\r\n  requestBy: string;\r\n}\r\n\r\nconst mockApplicationData: ApplicationRequest[] = Array(10).fill(null).map((_, i) => ({\r\n  id: `app_${i + 1}`,\r\n  vesselName: `MV. GOLDEN ACE V. 00${i + 1}`,\r\n  arrivalDate: '2025-05-01',\r\n  departureDate: '2025-05-02',\r\n  itemName: 'STEEL PRODUCT 10MT',\r\n  requestBy: 'USER1',\r\n}));\r\n\r\nexport default function ApplicationList() {\r\n  const [filter, setFilter] = useState('');\r\n  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());\r\n  const [visibleColumns, setVisibleColumns] = useState<Set<keyof ApplicationRequest>>(\r\n    new Set(Object.keys(mockApplicationData[0]) as (keyof ApplicationRequest)[])\r\n  );\r\n\r\n  const filteredData = mockApplicationData.filter(app =>\r\n    Object.values(app).some(value =>\r\n      value.toString().toLowerCase().includes(filter.toLowerCase())\r\n    )\r\n  );\r\n\r\n  const handleSelectAll = (checked: boolean) => {\r\n    if (checked) {\r\n      setSelectedRows(new Set(filteredData.map(row => row.id)));\r\n    } else {\r\n      setSelectedRows(new Set());\r\n    }\r\n  };\r\n\r\n  const handleRowSelect = (id: string, checked: boolean) => {\r\n    const newSelection = new Set(selectedRows);\r\n    if (checked) {\r\n      newSelection.add(id);\r\n    } else {\r\n      newSelection.delete(id);\r\n    }\r\n    setSelectedRows(newSelection);\r\n  };\r\n\r\n  const handleToggleColumn = (columnKey: keyof ApplicationRequest, checked: boolean) => {\r\n    const newVisibleColumns = new Set(visibleColumns);\r\n    if (checked) {\r\n      newVisibleColumns.add(columnKey);\r\n    } else {\r\n      newVisibleColumns.delete(columnKey);\r\n    }\r\n    setVisibleColumns(newVisibleColumns);\r\n  };\r\n\r\n  const handlePreview = (id: string) => {\r\n    console.log(`Preview document for ID: ${id}`);\r\n    // TODO: Integrate DocumentPreviewDialog here\r\n  };\r\n\r\n  const handleApprove = (id: string) => {\r\n    console.log(`Approve application with ID: ${id}`);\r\n  };\r\n\r\n  const handleReject = (id: string) => {\r\n    console.log(`Reject application with ID: ${id}`);\r\n  };\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"container mx-auto p-4\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-2xl font-bold\">Approval Requests</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <Input\r\n                placeholder=\"Filter lines...\"\r\n                value={filter}\r\n                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}\r\n                className=\"max-w-sm\"\r\n              />\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\" className=\"ml-auto\">\r\n                    Columns <IconChevronDown className=\"ml-2 h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\">\r\n                  {Object.keys(mockApplicationData[0]).map((key) => (\r\n                    <DropdownMenuCheckboxItem\r\n                      key={key}\r\n                      className=\"capitalize\"\r\n                      checked={visibleColumns.has(key as keyof ApplicationRequest)}\r\n                      onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof ApplicationRequest, checked === true)}\r\n                    >\r\n                      {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                    </DropdownMenuCheckboxItem>\r\n                  ))}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n\r\n            <div className=\"rounded-md border\">\r\n              <Table>\r\n                <TableHeader>\r\n                  <TableRow>\r\n                    <TableHead className=\"w-[30px]\">\r\n                      <Checkbox\r\n                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}\r\n                        onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}\r\n                      />\r\n                    </TableHead>\r\n                    {Object.keys(mockApplicationData[0]).map((key) => (visibleColumns.has(key as keyof ApplicationRequest) && key !== 'id' &&\r\n                      <TableHead key={key} className=\"capitalize\">\r\n                        {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                      </TableHead>\r\n                    ))}\r\n                    <TableHead className=\"text-right\">Actions</TableHead>\r\n                  </TableRow>\r\n                </TableHeader>\r\n                <TableBody>\r\n                  {filteredData.map((application) => (\r\n                    <TableRow key={application.id}>\r\n                      <TableCell>\r\n                        <Checkbox\r\n                          checked={selectedRows.has(application.id)}\r\n                          onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(application.id, checked === true)}\r\n                        />\r\n                      </TableCell>\r\n                      {Object.entries(application).map(([key, value]) => (visibleColumns.has(key as keyof ApplicationRequest) && key !== 'id' &&\r\n                        <TableCell key={key}>{value}</TableCell>\r\n                      ))}\r\n                      <TableCell className=\"text-right\">\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                              <span className=\"sr-only\">Open menu</span>\r\n                              <IconDotsVertical className=\"h-4 w-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent align=\"end\">\r\n                            <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                            <DropdownMenuItem onClick={() => handlePreview(application.id)}>\r\n                              Preview\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem onClick={() => handleApprove(application.id)}>\r\n                              Approve\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem onClick={() => handleReject(application.id)}>\r\n                              Reject\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <div className=\"text-sm text-gray-500\">\r\n                {selectedRows.size} of {filteredData.length} row(s) selected.\r\n              </div>\r\n              <div className=\"space-x-2\">\r\n                <Button variant=\"outline\" size=\"sm\">Previous</Button>\r\n                <Button variant=\"outline\" size=\"sm\">Next</Button>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["mockApplicationData", "_", "i", "ApplicationList", "filter", "setFilter", "useState", "selectedRows", "setSelectedRows", "visibleColumns", "setVisibleColumns", "filteredData", "app", "value", "handleSelectAll", "checked", "row", "handleRowSelect", "id", "newSelection", "handleToggleColumn", "column<PERSON>ey", "newVisibleColumns", "handlePreview", "handleApprove", "handleReject", "AppLayout", "jsx", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "Input", "e", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "IconChevronDown", "DropdownMenuContent", "key", "DropdownMenuCheckboxItem", "Table", "TableHeader", "TableRow", "TableHead", "Checkbox", "TableBody", "application", "TableCell", "IconDotsVertical", "DropdownMenuLabel", "DropdownMenuItem"], "mappings": "kbAmBA,MAAMA,EAA4C,MAAM,EAAE,EAAE,KAAK,IAAI,EAAE,IAAI,CAACC,EAAGC,KAAO,CACpF,GAAI,OAAOA,EAAI,CAAC,GAChB,WAAY,uBAAuBA,EAAI,CAAC,GACxC,YAAa,aACb,cAAe,aACf,SAAU,qBACV,UAAW,OACb,EAAE,EAEF,SAAwBC,GAAkB,CACxC,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,EAAE,EACjC,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAsB,IAAI,GAAK,EACjE,CAACG,EAAgBC,CAAiB,EAAIJ,EAAA,SAC1C,IAAI,IAAI,OAAO,KAAKN,EAAoB,CAAC,CAAC,CAAiC,CAC7E,EAEMW,EAAeX,EAAoB,OACvCY,GAAA,OAAO,OAAOA,CAAG,EAAE,KAAKC,GACtBA,EAAM,SAAS,EAAE,cAAc,SAAST,EAAO,YAAa,CAAA,CAAA,CAEhE,EAEMU,EAAmBC,GAAqB,CAE1BP,EADdO,EACc,IAAI,IAAIJ,EAAa,OAAWK,EAAI,EAAE,CAAC,EAEvC,IAAI,GAFoC,CAI5D,EAEMC,EAAkB,CAACC,EAAYH,IAAqB,CAClD,MAAAI,EAAe,IAAI,IAAIZ,CAAY,EACrCQ,EACFI,EAAa,IAAID,CAAE,EAEnBC,EAAa,OAAOD,CAAE,EAExBV,EAAgBW,CAAY,CAC9B,EAEMC,EAAqB,CAACC,EAAqCN,IAAqB,CAC9E,MAAAO,EAAoB,IAAI,IAAIb,CAAc,EAC5CM,EACFO,EAAkB,IAAID,CAAS,EAE/BC,EAAkB,OAAOD,CAAS,EAEpCX,EAAkBY,CAAiB,CACrC,EAEMC,EAAiBL,GAAe,CAGtC,EAEMM,EAAiBN,GAAe,CAEtC,EAEMO,EAAgBP,GAAe,CAErC,EAEA,aACGQ,EACC,CAAA,SAAAC,EAAA,IAAC,OAAI,UAAU,wBACb,gBAACC,EACC,CAAA,SAAA,CAAAD,EAAAA,IAACE,GACC,SAACF,EAAA,IAAAG,EAAA,CAAU,UAAU,qBAAqB,6BAAiB,CAC7D,CAAA,SACCC,EACC,CAAA,SAAA,CAACC,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAL,EAAA,IAACM,EAAA,CACC,YAAY,kBACZ,MAAO7B,EACP,SAAW8B,GAA2C7B,EAAU6B,EAAE,OAAO,KAAK,EAC9E,UAAU,UAAA,CACZ,SACCC,EACC,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GAC1B,SAAAJ,EAAAA,KAACK,GAAO,QAAQ,UAAU,UAAU,UAAU,SAAA,CAAA,WACpCV,EAAAA,IAACW,EAAgB,CAAA,UAAU,cAAe,CAAA,CAAA,CAAA,CACpD,CACF,CAAA,EACCX,EAAAA,IAAAY,EAAA,CAAoB,MAAM,MACxB,SAAO,OAAA,KAAKvC,EAAoB,CAAC,CAAC,EAAE,IAAKwC,GACxCb,EAAA,IAACc,EAAA,CAEC,UAAU,aACV,QAAShC,EAAe,IAAI+B,CAA+B,EAC3D,gBAAkBzB,GAAuCK,EAAmBoB,EAAiCzB,IAAY,EAAI,EAE5H,SAAIyB,EAAA,QAAQ,WAAY,KAAK,EAAE,KAAK,CAAA,EALhCA,CAAA,CAOR,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECb,MAAA,MAAA,CAAI,UAAU,oBACb,gBAACe,EACC,CAAA,SAAA,CAACf,EAAA,IAAAgB,EAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,EAAA,CAAU,UAAU,WACnB,SAAAlB,EAAA,IAACmB,EAAA,CACC,QAASvC,EAAa,OAASI,EAAa,QAAUA,EAAa,OAAS,EAC5E,gBAAkBI,GAAuCD,EAAgBC,IAAY,EAAI,CAAA,CAAA,EAE7F,EACC,OAAO,KAAKf,EAAoB,CAAC,CAAC,EAAE,IAAKwC,GAAS/B,EAAe,IAAI+B,CAA+B,GAAKA,IAAQ,MAC/Gb,MAAAkB,EAAA,CAAoB,UAAU,aAC5B,SAAIL,EAAA,QAAQ,WAAY,KAAK,EAAE,MADlB,EAAAA,CAEhB,CACD,EACAb,EAAA,IAAAkB,EAAA,CAAU,UAAU,aAAa,SAAO,SAAA,CAAA,CAAA,CAAA,CAC3C,CACF,CAAA,QACCE,EACE,CAAA,SAAApC,EAAa,IAAKqC,UAChBJ,EACC,CAAA,SAAA,CAAAjB,MAACsB,EACC,CAAA,SAAAtB,EAAA,IAACmB,EAAA,CACC,QAASvC,EAAa,IAAIyC,EAAY,EAAE,EACxC,gBAAkBjC,GAAuCE,EAAgB+B,EAAY,GAAIjC,IAAY,EAAI,CAAA,CAAA,EAE7G,EACC,OAAO,QAAQiC,CAAW,EAAE,IAAI,CAAC,CAACR,EAAK3B,CAAK,IAAOJ,EAAe,IAAI+B,CAA+B,GAAKA,IAAQ,YAChHS,EAAqB,CAAA,SAAApC,GAAN2B,CAAY,CAC7B,EACAb,MAAAsB,EAAA,CAAU,UAAU,aACnB,gBAACd,EACC,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GAC1B,SAAAJ,EAAAA,KAACK,GAAO,QAAQ,QAAQ,UAAU,cAChC,SAAA,CAACV,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,YAAA,EACnCA,EAAAA,IAACuB,EAAiB,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,CACxC,CACF,CAAA,EACAlB,EAAAA,KAACO,EAAoB,CAAA,MAAM,MACzB,SAAA,CAAAZ,EAAAA,IAACwB,GAAkB,SAAO,SAAA,CAAA,EAC1BxB,MAACyB,GAAiB,QAAS,IAAM7B,EAAcyB,EAAY,EAAE,EAAG,SAEhE,UAAA,EACArB,MAACyB,GAAiB,QAAS,IAAM5B,EAAcwB,EAAY,EAAE,EAAG,SAEhE,UAAA,EACArB,MAACyB,GAAiB,QAAS,IAAM3B,EAAauB,EAAY,EAAE,EAAG,SAE/D,QAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,GA/BaA,EAAY,EAgC3B,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAhB,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACZ,SAAA,CAAazB,EAAA,KAAK,OAAKI,EAAa,OAAO,mBAAA,EAC9C,EACAqB,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAL,MAACU,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAQ,WAAA,QAC3CA,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAI,MAAA,CAAA,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EACF,CAEJ"}