import * as z from 'zod';

export const exportVesselHeaderSchema = z.object({
  docNum: z.string().optional(),
  vesselId: z.string().min(1, 'Vessel Name is required'),
  postingDate: z.string().min(1, 'Posting Date is required'),
  voyage: z.string().optional(),
  vesselArrival: z.string().optional(),
  vesselDeparture: z.string().optional(),
  portOriginId: z.string().optional(),
  destinationPortId: z.string().optional(),
  concurrencyStamp: z.string().optional(),
  docStatus: z.string().optional(),
  jettyId: z.string().min(1, 'Jetty is required'),
  // Add more fields as needed from CreateUpdateExportVesselDto
  asideDate: z.string().optional(),
  castOfDate: z.string().optional(),
});

export type ExportVesselHeaderForm = z.infer<typeof exportVesselHeaderSchema>; 