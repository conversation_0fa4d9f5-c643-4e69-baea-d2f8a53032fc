import{a as j,h as C,j as e,$ as y,f as S}from"./vendor-6tJeyfYI.js";import{B as x,R as v,Y as T,S as I,c as w,d as F,e as P,f as k,L as E,Z as R,A as V}from"./app-layout-rNt37hVL.js";import{D as L}from"./DataTable-CDIoPElA.js";import{C as z}from"./checkbox-D1loOtZt.js";import{D as A,b as K,c as q,d as O,e as $}from"./dialog-BmEXyFlW.js";import{I as N}from"./input-DlXlkYlT.js";import{T as U}from"./textarea-DwrdARTr.js";import{u as Y,C as B}from"./index.esm-BubGICDC.js";import{P as M}from"./plus-PD53KOti.js";import{P as W}from"./pencil-BTZ0_LzS.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";import"./table-BKSoE52x.js";import"./useDebounce-BdjXjarW.js";import"./index-X4QX0AQ3.js";import"./popover-ChFN9yvN.js";import"./tiny-invariant-CopsF_GD.js";import"./index-CaiFFM4D.js";const _=[{value:1,label:"Invoice"},{value:2,label:"Report"},{value:3,label:"Contract"},{value:4,label:"Letter"},{value:5,label:"Certificate"},{value:6,label:"RegistrationCard"},{value:99,label:"Other"}],H=({open:h,onOpenChange:i,initial:s,onSuccess:u})=>{const{register:l,handleSubmit:b,reset:d,setValue:g,watch:D,control:o,formState:{errors:p,isSubmitting:f}}=Y({defaultValues:{Name:s?.name??"",DocumentType:s?.documentType??1,Description:s?.description??"",IsDefault:s?.isDefault??!1}}),[c,m]=y.useState(null);y.useEffect(()=>{d({Name:s?.name??"",DocumentType:s?.documentType??1,Description:s?.description??"",IsDefault:s?.isDefault??!1}),m(null)},[s,h,d]);const n=S({mutationFn:async a=>{if(!c)throw new Error("No file selected");return T({body:{Name:a.Name,DocumentType:a.DocumentType,Description:a.Description,IsDefault:a.IsDefault,File:c}})},onSuccess:()=>{u(),i(!1)}}),t=a=>{if(a.target.files&&a.target.files.length>0){const r=a.target.files[0];if(!r.name.toLowerCase().endsWith(".docx")&&!r.name.toLowerCase().endsWith(".pdf")){a.target.value="",m(null);return}m(r)}};return e.jsx(A,{open:h,onOpenChange:i,children:e.jsxs(K,{children:[e.jsx(q,{children:e.jsxs(O,{children:[s?"Edit":"Create"," Document Template"]})}),e.jsxs("form",{onSubmit:b(a=>n.mutate(a)),className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Name *"}),e.jsx(N,{...l("Name",{required:"Name is required"}),className:"w-full"}),p.Name&&e.jsx("div",{className:"text-xs text-destructive mt-1",children:p.Name.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Document Type *"}),e.jsx(B,{name:"DocumentType",control:o,rules:{required:!0},render:({field:a})=>e.jsxs(I,{value:a.value?String(a.value):"",onValueChange:r=>a.onChange(Number(r)),children:[e.jsx(w,{className:"w-full",children:e.jsx(F,{placeholder:"Select document type"})}),e.jsx(P,{children:_.map(r=>e.jsx(k,{value:String(r.value),children:r.label},r.value))})]})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Description"}),e.jsx(U,{...l("Description"),className:"w-full min-h-[60px]"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{...l("IsDefault"),id:"isDefault",checked:!!D("IsDefault"),onCheckedChange:a=>g("IsDefault",!!a)}),e.jsx("label",{htmlFor:"isDefault",className:"text-sm",children:"Is Default"})]}),e.jsxs("div",{children:[e.jsx(E,{htmlFor:"templateFile",className:"text-left",children:"File *"}),e.jsx(N,{id:"templateFile",type:"file",accept:".docx,.pdf",onChange:t,disabled:n.isPending||f}),c&&e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Selected file: ",c.name]})]}),n.error&&e.jsx("div",{className:"text-xs text-destructive mt-2",children:String(n.error instanceof Error?n.error.message:n.error)}),e.jsxs($,{children:[e.jsx(x,{type:"button",variant:"outline",onClick:()=>{i(!1),m(null)},children:"Cancel"}),e.jsx(x,{type:"submit",className:"ml-2",disabled:f||n.isPending,children:n.isPending?"Saving...":"Save"})]})]})]})})},Q=()=>{const[h,i]=j.useState(!1),[s,u]=j.useState(null),[l,b]=j.useState({pageIndex:0,pageSize:10}),[d,g]=j.useState(!1),D=[{accessorKey:"id",header:"Id",cell:t=>t.getValue()??"-"},{accessorKey:"name",header:"Name",cell:t=>t.getValue()??"-"},{accessorKey:"documentType",header:"Type",cell:t=>t.getValue()??"-"},{accessorKey:"description",header:"Description",cell:t=>t.getValue()??"-"},{accessorKey:"isDefault",header:"Default",cell:t=>t.getValue()?"Yes":"No"},{id:"edit",header:"",cell:({row:t})=>e.jsx(x,{onClick:()=>{u(t.original),i(!0)},"aria-label":"Edit Document Template",tabIndex:0,variant:"outline",size:"icon",className:"ml-2 h-8 w-8",children:e.jsx(W,{className:"w-4 h-4","aria-hidden":"true"})}),enableSorting:!1,enableColumnFilter:!1}],{data:o,isLoading:p,refetch:f}=C({queryKey:["document-templates",l],queryFn:async()=>{const t=await R({body:{page:l.pageIndex+1,maxResultCount:l.pageSize,skipCount:l.pageIndex*l.pageSize}});return t&&typeof t=="object"&&"items"in t&&"totalCount"in t?t:t&&typeof t=="object"&&"data"in t&&t.data?t.data:{items:[],totalCount:0}}}),c=Array.isArray(o?.items)?o.items:[],m=typeof o?.totalCount=="number"?o.totalCount:0,n=async()=>{g(!0);try{await f()}finally{g(!1)}};return e.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:[e.jsx("div",{className:"text-xl font-bold px-2 pt-2 pb-1",children:"Document Template List"}),e.jsxs("div",{className:"flex justify-end mb-2 gap-2",children:[e.jsx(x,{onClick:n,variant:"outline",size:"icon",className:"h-10 w-10",disabled:p||d,children:e.jsx(v,{className:`h-4 w-4 ${d?"animate-spin":""}`})}),e.jsxs(x,{onClick:()=>{u(null),i(!0)},children:[e.jsx(M,{className:"h-3 w-3"})," New Document Template"]})]}),e.jsx(L,{title:"",columns:D,data:c,totalCount:m,isLoading:p,manualPagination:!0,pageSize:l.pageSize,onPaginationChange:b,hideDefaultFilterbar:!0,enableRowSelection:!1,manualSorting:!0}),e.jsx(H,{open:h,onOpenChange:t=>{i(t),t||u(null)},initial:s??void 0,onSuccess:()=>f()})]})},fe=()=>e.jsx(V,{children:e.jsx(Q,{})});export{fe as default};
//# sourceMappingURL=page-B_8j8ARA.js.map
