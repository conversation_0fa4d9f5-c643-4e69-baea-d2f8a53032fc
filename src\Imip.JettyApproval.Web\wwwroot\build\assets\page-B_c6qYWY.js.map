{"version": 3, "file": "page-B_c6qYWY.js", "sources": ["../../../../../frontend/src/pages/jetty/page.tsx"], "sourcesContent": ["import ApprovalTable from \"@/components/approval/approval-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport { Head } from '@inertiajs/react';\r\nimport React from \"react\";\r\n\r\nconst JettyPage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Approval Management\" />\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <ApprovalTable />\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default JettyPage;"], "names": ["JettyPage", "AppLayout", "jsx", "Head", "ApprovalTable"], "mappings": "yrBAKA,MAAMA,EAAsB,WAEvBC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAK,MAAM,qBAAsB,CAAA,QACjC,MAAI,CAAA,UAAU,8BACb,SAAAD,EAAA,IAACE,IAAc,CACjB,CAAA,CAAA,EACF"}