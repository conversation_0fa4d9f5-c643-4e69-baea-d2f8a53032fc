{"version": 3, "file": "home-DMcLnTAt.js", "sources": ["../../../../../frontend/src/pages/home.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport AppLayout from '../layouts/app-layout';\r\nimport OverViewPage from '../components/dashboard/overview';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Dashboard\" />\r\n\r\n      {/* <PageContainer>\r\n      </PageContainer> */}\r\n      <OverViewPage />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["OverViewLayout", "AppLayout", "jsx", "Head", "OverViewPage"], "mappings": "+TAKA,SAAwBA,GAAiB,CACvC,cACGC,EACC,CAAA,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAK,MAAM,WAAY,CAAA,QAIvBC,EAAa,CAAA,CAAA,CAAA,EAChB,CAEJ"}