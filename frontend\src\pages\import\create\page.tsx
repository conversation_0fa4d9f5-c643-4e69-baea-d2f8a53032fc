import { ekbProxyService } from '@/services/ekbProxyService';
import type { CreateUpdateImportVesselDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { ImportVesselFormWithData } from '@/components/jetty/vessel/import/import-vessel-form';
import type { ImportVesselHeaderForm } from '@/components/jetty/vessel/import/import-vessel-header-schema';
import type { ImportVesselItemForm } from '@/components/jetty/vessel/import/import-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { Head, router } from '@inertiajs/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const ImportVesselCreatePage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: docNumData, isLoading } = useQuery({
    queryKey: ['import-vessel-next-docnum'],
    queryFn: async () => {
      const response = await ekbProxyService.generateNextImportVesselDocNum();
      return response.data;
    },
  });

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: ImportVesselHeaderForm; items: ImportVesselItemForm[] }) => {
      const response = await ekbProxyService.createImportVessel({
        ...header,
        docNum: Number(header.docNum),
        items: items.map(item => ({
          ...item,
          createdBy: '',
          docType: '',
          isScan: '',
          isOriginal: '',
          isActive: true,
          isDeleted: false,
          isSend: '',
          isFeOri: '',
          isFeSend: '',
          isChange: '',
          isFeChange: '',
          isFeActive: '',
          deleted: '',
          isUrgent: '',
          tenantId: item.tenantId || '',
          businessPartnerId: item.businessPartnerId || '',
        })),
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: (data) => {
      toast({ title: 'Success', description: 'Import vessel created.', variant: 'success' });
      if (data && data.id) {
        router.visit(`/jetty/vessel/import/edit/${data.id}`);
      }
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <ImportVesselFormWithData
      mode="create"
      title={t('pages.vessel.create.import')}
      initialHeader={{ docNum: Number(docNumData) } as Partial<CreateUpdateImportVesselDto>}
      initialItems={[]}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
      queryClient={queryClient}
      showAddLineButton={false}
    />
  );
};

export default function ImportVesselCreate() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.create.import')} />
      <ImportVesselCreatePage />
    </AppLayout>
  );
} 