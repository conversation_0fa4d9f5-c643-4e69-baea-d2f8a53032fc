import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { createRoot } from "react-dom/client";
import './i18n';
import './index.css';
// import './lib/oidc-init'; // Initialize OIDC token manager
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';
import { client } from "./client/client.gen";
import { client as clientEkb } from "./clientEkb/client.gen";
import { getCookie } from "./lib/utils/cookie";
import './theme.css';

// Declare global variable for TypeScript
declare global {
  interface Window {
    __RequestVerificationToken?: string;
  }
}

const appName = window.document.getElementsByTagName("title")[0]?.innerText || "Inertia";

// Custom fetch function with desired options
const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  return fetch(input, {
    credentials: 'include',
    duplex: 'half',
    ...init,
  } as RequestInit);
};

client.interceptors.request.use((request) => {
  const csrfToken = window.__RequestVerificationToken;
  request.headers.append("X-CSRF-TOKEN", csrfToken as string)
  request.headers.append("X-Requested-With", "XMLHttpRequest")
  return request
})

// clientEkb.interceptors.request.use((request) => {
//   const csrfToken = window.__RequestVerificationToken;
//   request.headers.append("X-CSRF-TOKEN", csrfToken as string)
//   request.headers.append("X-Requested-With", "XMLHttpRequest")
//   return request
// })

client.setConfig({
  throwOnError: true,
  baseUrl: "/",
  fetch: customFetch
})

const rawBaseUrl = getCookie("ekbUrl");
const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : "https://ekb-dev.imip.co.id";

const customFetchEkb = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const token = getCookie("EkbApiToken") ?? "";
  // console.log("base url", ekbBaseUrl)
  // If input is a relative path, prepend ekbBaseUrl
  let url = input;
  if (typeof input === "string" && input.startsWith("/")) {
    url = ekbBaseUrl.replace(/\/$/, "") + input;
  }
  const mergedHeaders = {
    'Content-Type': 'application/json',
    ...(init?.headers || {}),
    Authorization: token ? `Bearer ${token}` : undefined,
  };
  return fetch(url, {
    credentials: 'include',
    duplex: 'half',
    ...init,
    headers: mergedHeaders,
  } as RequestInit);
};

clientEkb.setConfig({
  throwOnError: true,
  baseUrl: ekbBaseUrl,
  fetch: customFetchEkb
})


createInertiaApp({
  title: (title) => `${title} - ${appName}`,
  resolve: (name) => resolvePageComponent(`./pages/${name}.tsx`, import.meta.glob("./pages/**/*.tsx")),
  setup({ el, App, props }) {
    const root = createRoot(el);
    function AppWithQueryClient() {
      const [queryClient] = useState(() => new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 10 * 1000, // 10s
            refetchOnWindowFocus: true,
          },
        },
      }));
      return (
        <QueryClientProvider client={queryClient}>
          <App {...props} />
        </QueryClientProvider>
      );
    }
    root.render(<AppWithQueryClient />);
  },
  progress: {
    color: '#4B5563',
  },
}).catch(console.error); 