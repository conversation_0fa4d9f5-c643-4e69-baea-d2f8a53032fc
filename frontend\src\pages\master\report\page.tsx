import { EventCalendar } from '@/components/event-calendar';
import { type CalendarEvent } from '@/components/event-calendar/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { useEffect, useState } from 'react';

interface JettyData {
  name: string;
  events: CalendarEvent[];
}

const mockJettySchedules: JettyData[] = [
  {
    name: 'Jetty A',
    events: [
      { id: '1', title: 'Vessel A - Docking', start: new Date(2025, 4, 10, 8, 0), end: new Date(2025, 4, 10, 12, 0), color: 'sky', location: 'Jetty A' },
      { id: '2', title: 'Maintenance', start: new Date(2025, 4, 11, 14, 0), end: new Date(2025, 4, 11, 17, 0), color: 'rose', location: 'Jetty A' },
      { id: '6', title: 'Annual Inspection', start: new Date(2025, 4, 15), end: new Date(2025, 4, 16), allDay: true, color: 'orange', location: 'Jetty A' },
      { id: '7', title: 'Vessel D - Loading', start: new Date(2025, 4, 18, 9, 30), end: new Date(2025, 4, 18, 16, 0), color: 'emerald', location: 'Jetty A' },
    ],
  },
  {
    name: 'Jetty B',
    events: [
      { id: '3', title: 'Vessel B - Loading', start: new Date(2025, 4, 12, 9, 0), end: new Date(2025, 4, 12, 18, 0), color: 'amber', location: 'Jetty B' },
      { id: '4', title: 'Inspection', start: new Date(2025, 4, 13, 10, 0), end: new Date(2025, 4, 13, 11, 0), color: 'violet', location: 'Jetty B' },
      { id: '8', title: 'Emergency Repair', start: new Date(2025, 4, 20, 7, 0), end: new Date(2025, 4, 20, 20, 0), color: 'rose', location: 'Jetty B' },
      { id: '9', title: 'Vessel E - Unloading', start: new Date(2025, 4, 22, 11, 0), end: new Date(2025, 4, 23, 10, 0), color: 'sky', location: 'Jetty B' },
    ],
  },
  {
    name: 'Jetty C',
    events: [
      { id: '5', title: 'Vessel C - Unloading', start: new Date(2025, 4, 14, 7, 0), end: new Date(2025, 4, 14, 16, 0), color: 'emerald', location: 'Jetty C' },
      { id: '10', title: 'Routine Cleaning', start: new Date(2025, 4, 25, 8, 0), end: new Date(2025, 4, 25, 12, 0), color: 'violet', location: 'Jetty C' },
      { id: '11', title: 'Vessel F - Docking', start: new Date(2025, 4, 28, 6, 0), end: new Date(2025, 4, 29, 14, 0), color: 'amber', location: 'Jetty C' },
    ],
  },
];

export default function JettySchedule() {
  const [selectedJetty, setSelectedJetty] = useState<string | null>(mockJettySchedules[0]?.name || null);
  const [events, setEvents] = useState<CalendarEvent[]>([]);

  const handleEventAdd = (event: CalendarEvent) => {
    setEvents((prevEvents) => [...prevEvents, event]);
  };

  const handleEventUpdate = (updatedEvent: CalendarEvent) => {
    setEvents((prevEvents) =>
      prevEvents.map((event) =>
        event.id === updatedEvent.id ? updatedEvent : event
      )
    );
  };

  const handleEventDelete = (eventId: string) => {
    setEvents((prevEvents) => prevEvents.filter((event) => event.id !== eventId));
  };

  useEffect(() => {
    if (selectedJetty) {
      const jettyData = mockJettySchedules.find(jetty => jetty.name === selectedJetty);
      setEvents(jettyData ? jettyData.events : []);
    } else {
      setEvents([]);
    }
  }, [selectedJetty]);

  return (
    <AppLayout>
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Jetty Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 flex items-center gap-4">
              <label htmlFor="jetty-select" className="font-medium">Select Jetty:</label>
              <Select onValueChange={setSelectedJetty} value={selectedJetty || ''}>
                <SelectTrigger id="jetty-select" className="w-[200px]">
                  <SelectValue placeholder="Select a jetty" />
                </SelectTrigger>
                <SelectContent>
                  {mockJettySchedules.map((jetty) => (
                    <SelectItem key={jetty.name} value={jetty.name}>
                      {jetty.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <EventCalendar
              events={events}
              initialView="month"
              onEventAdd={handleEventAdd}
              onEventUpdate={handleEventUpdate}
              onEventDelete={handleEventDelete}
            />
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
