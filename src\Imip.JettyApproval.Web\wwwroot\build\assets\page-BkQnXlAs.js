import{j as s,f as m}from"./vendor-6tJeyfYI.js";import{F as c,A as u,u as d,M as l}from"./app-layout-rNt37hVL.js";import{E as S}from"./export-vessel-form-CVscznfP.js";import{$ as x,m as y}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */import"./attachment-dialog-D7s9nIdd.js";import"./dialog-BmEXyFlW.js";import"./table-BKSoE52x.js";import"./tabs-Dk-TLCdA.js";const g=()=>{const{t:r}=c(),{toast:n}=d(),a=m({mutationFn:async({header:t,items:i})=>{const o=await l.createExportVessel({...t,vesselId:t.vesselId?String(t.vesselId):"",jettyId:t.jettyId?String(t.jettyId):"",concurrencyStamp:t.concurrencyStamp?String(t.concurrencyStamp):"",items:i.map(e=>({...e,createdBy:"",docType:"",isScan:"",isOriginal:"",isActive:!0,isDeleted:!1,isSend:"",isFeOri:"",isFeSend:"",isChange:"",isFeChange:"",isFeActive:"",deleted:"",isUrgent:"",tenantId:e.tenantId||"",businessPartnerId:e.businessPartnerId||"",concurrencyStamp:e.concurrencyStamp||""}))});if(o.error)throw new Error(o.error);return o.data},onSuccess:t=>{n({title:"Success",description:"Export vessel created.",variant:"success"}),t&&t.id&&y.visit(`/export/edit/${t.id}`)},onError:t=>{n({title:t instanceof Error?t.message:t?.error?.message||"Error",description:t instanceof Error?void 0:t?.error?.details,variant:"destructive"})}}),p=async(t,i)=>{await a.mutateAsync({header:t,items:i})};return s.jsx(S,{mode:"create",title:r("pages.vessel.create.export"),initialHeader:{},initialItems:[],onSubmit:p,isSubmitting:a.isPending})};function R(){const{t:r}=c();return s.jsxs(u,{children:[s.jsx(x,{title:r("pages.vessel.create.export")}),s.jsx(g,{})]})}export{R as default};
//# sourceMappingURL=page-BkQnXlAs.js.map
