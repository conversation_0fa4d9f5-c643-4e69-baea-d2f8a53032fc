import type { FilterCondition, FilterOperator } from "@/client/types.gen";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowDown, ArrowUp, ArrowUpDown, Filter, Plus, X } from "lucide-react";
import React, { useState } from "react";

export type SortDirection = "ASC" | "DESC";

export type FilterSortBarProps = {
  filterFields: { value: string; label: string }[];
  operators: { value: FilterOperator; label: string }[];
  filters: FilterCondition[];
  sorts: { field: string; direction: SortDirection }[];
  onFiltersChange: (filters: FilterCondition[]) => void;
  onSortsChange: (sorts: { field: string; direction: SortDirection }[]) => void;
  children?: React.ReactNode;
};

const FilterSortBar: React.FC<FilterSortBarProps> = ({
  filterFields,
  operators,
  filters,
  sorts,
  onFiltersChange,
  onSortsChange,
  children,
}) => {
  const [showFilterAdd, setShowFilterAdd] = useState(false);
  const [showSortAdd, setShowSortAdd] = useState(false);
  const [newFilter, setNewFilter] = useState<Partial<FilterCondition>>({});
  const [newSort, setNewSort] = useState<{ field?: string; direction?: SortDirection }>({});
  const [editingFilterIdx, setEditingFilterIdx] = useState<number | null>(null);
  const [editingSortIdx, setEditingSortIdx] = useState<number | null>(null);
  const [filterFieldSearch, setFilterFieldSearch] = useState("");

  // Filter field options with search
  const filteredFilterFields = filterFields.filter(f =>
    f.label.toLowerCase().includes(filterFieldSearch.toLowerCase())
  );

  // Add or edit filter
  const handleSaveFilter = () => {
    if (
      newFilter.fieldName &&
      newFilter.operator &&
      newFilter.value !== undefined &&
      newFilter.value !== ""
    ) {
      if (editingFilterIdx !== null) {
        const updated = [...filters];
        updated[editingFilterIdx] = {
          fieldName: newFilter.fieldName,
          operator: newFilter.operator as FilterOperator,
          value: newFilter.value,
        };
        onFiltersChange(updated);
        setEditingFilterIdx(null);
      } else {
        onFiltersChange([
          ...filters,
          {
            fieldName: newFilter.fieldName,
            operator: newFilter.operator as FilterOperator,
            value: newFilter.value,
          },
        ]);
      }
      setNewFilter({});
      setShowFilterAdd(false);
      setFilterFieldSearch("");
    }
  };
  const handleRemoveFilter = (idx: number) => {
    onFiltersChange(filters.filter((_, i) => i !== idx));
    if (editingFilterIdx === idx) setEditingFilterIdx(null);
  };
  const handleEditFilter = (idx: number) => {
    setEditingFilterIdx(idx);
    setShowFilterAdd(false);
    setNewFilter(filters[idx]);
    setShowFilterAdd(false);
  };

  // Add or edit sort
  const handleSaveSort = () => {
    if (newSort.field && newSort.direction) {
      if (editingSortIdx !== null) {
        const updated = [...sorts];
        updated[editingSortIdx] = { field: newSort.field, direction: newSort.direction };
        onSortsChange(updated);
        setEditingSortIdx(null);
      } else {
        onSortsChange([...sorts, { field: newSort.field, direction: newSort.direction }]);
      }
      setNewSort({});
      setShowSortAdd(false);
    }
  };
  const handleRemoveSort = (idx: number) => {
    onSortsChange(sorts.filter((_, i) => i !== idx));
    if (editingSortIdx === idx) setEditingSortIdx(null);
  };
  const handleEditSort = (idx: number) => {
    setEditingSortIdx(idx);
    setShowSortAdd(false);
    setNewSort(sorts[idx]);
    setShowSortAdd(false);
  };

  return (
    <div className="flex flex-wrap items-center justify-between gap-2 mb-2 px-2 py-2 bg-muted/30 rounded-t-lg">
      <div className="flex flex-wrap items-center gap-2">
        {/* Filter badges */}
        {filters.map((f, idx) => (
          <Popover key={idx} open={editingFilterIdx === idx} onOpenChange={open => open ? handleEditFilter(idx) : setEditingFilterIdx(null)}>
            <PopoverTrigger asChild>
              <Badge className="flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-full cursor-pointer">
                <Filter className="h-4 w-4" />
                <span>{filterFields.find(ff => ff.value === f.fieldName)?.label || f.fieldName} {operators.find(op => op.value === f.operator)?.label || f.operator} "{String(f.value)}"</span>
                <Button size="icon" variant="ghost" onClick={e => { e.stopPropagation(); handleRemoveFilter(idx); }} className="ml-1 h-5 w-5"><X className="h-4 w-4" /></Button>
              </Badge>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4 flex flex-col gap-3">
              <Input
                placeholder="Search field..."
                value={filterFieldSearch}
                onChange={e => setFilterFieldSearch(e.target.value)}
                className="mb-2"
              />
              <Select value={newFilter.fieldName || ""} onValueChange={val => setNewFilter(f => ({ ...f, fieldName: val }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Field" /></SelectTrigger>
                <SelectContent>
                  {filteredFilterFields.map(f => <SelectItem key={f.value} value={f.value}>{f.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Select value={newFilter.operator || ""} onValueChange={val => setNewFilter(f => ({ ...f, operator: val as FilterOperator }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Operator" /></SelectTrigger>
                <SelectContent>
                  {operators.map(op => <SelectItem key={op.value} value={op.value}>{op.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Input
                className="w-full"
                placeholder="Value"
                value={typeof newFilter.value === "string" ? newFilter.value : newFilter.value === undefined ? "" : String(newFilter.value)}
                onChange={e => setNewFilter(f => ({ ...f, value: e.target.value }))}
              />
              <div className="flex gap-2 justify-end">
                <Button onClick={handleSaveFilter} size="sm" className="bg-primary text-primary-foreground">Save</Button>
                <Button onClick={() => setEditingFilterIdx(null)} size="icon" variant="ghost"><X className="h-4 w-4" /></Button>
              </div>
            </PopoverContent>
          </Popover>
        ))}
        {/* Add filter popover */}
        <TooltipProvider>
          <Popover open={showFilterAdd} onOpenChange={setShowFilterAdd}>
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button size="icon" variant="secondary" className="rounded-full"><Plus className="h-4 w-4" /></Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent>Add Filter</TooltipContent>
            </Tooltip>
            <PopoverContent className="w-80 p-4 flex flex-col gap-3">
              <Input
                placeholder="Search field..."
                value={filterFieldSearch}
                onChange={e => setFilterFieldSearch(e.target.value)}
                className="mb-2"
              />
              <Select value={newFilter.fieldName || ""} onValueChange={val => setNewFilter(f => ({ ...f, fieldName: val }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Field" /></SelectTrigger>
                <SelectContent>
                  {filteredFilterFields.map(f => <SelectItem key={f.value} value={f.value}>{f.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Select value={newFilter.operator || ""} onValueChange={val => setNewFilter(f => ({ ...f, operator: val as FilterOperator }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Operator" /></SelectTrigger>
                <SelectContent>
                  {operators.map(op => <SelectItem key={op.value} value={op.value}>{op.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Input
                className="w-full"
                placeholder="Value"
                value={typeof newFilter.value === "string" ? newFilter.value : newFilter.value === undefined ? "" : String(newFilter.value)}
                onChange={e => setNewFilter(f => ({ ...f, value: e.target.value }))}
              />
              <div className="flex gap-2 justify-end">
                <Button onClick={handleSaveFilter} size="sm" className="bg-primary text-primary-foreground">Add</Button>
                <Button onClick={() => setShowFilterAdd(false)} size="icon" variant="ghost"><X className="h-4 w-4" /></Button>
              </div>
            </PopoverContent>
          </Popover>
        </TooltipProvider>
      </div>
      <div className="flex flex-wrap items-center gap-2">
        {/* Sort badges */}
        {sorts.map((s, idx) => (
          <Popover key={idx} open={editingSortIdx === idx} onOpenChange={open => open ? handleEditSort(idx) : setEditingSortIdx(null)}>
            <PopoverTrigger asChild>
              <Badge className="flex items-center gap-1 bg-muted text-muted-foreground px-2 py-1 rounded-full cursor-pointer">
                {filterFields.find(sf => sf.value === s.field)?.label || s.field}
                {s.direction === "ASC" ? <ArrowUp className="h-4 w-4 ml-1" /> : <ArrowDown className="h-4 w-4 ml-1" />}
                <Button size="icon" variant="ghost" onClick={e => { e.stopPropagation(); handleRemoveSort(idx); }} className="ml-1 h-5 w-5"><X className="h-4 w-4" /></Button>
              </Badge>
            </PopoverTrigger>
            <PopoverContent className="w-72 p-4 flex flex-col gap-3">
              <Select value={newSort.field || ""} onValueChange={val => setNewSort(s => ({ ...s, field: val }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Field" /></SelectTrigger>
                <SelectContent>
                  {filterFields.map(f => <SelectItem key={f.value} value={f.value}>{f.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Select value={newSort.direction || ""} onValueChange={val => setNewSort(s => ({ ...s, direction: val as SortDirection }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Direction" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="ASC">Ascending</SelectItem>
                  <SelectItem value="DESC">Descending</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex gap-2 justify-end">
                <Button onClick={handleSaveSort} size="sm" className="bg-primary text-primary-foreground">Save</Button>
                <Button onClick={() => setEditingSortIdx(null)} size="icon" variant="ghost"><X className="h-4 w-4" /></Button>
              </div>
            </PopoverContent>
          </Popover>
        ))}
        {/* Add sort popover */}
        <TooltipProvider>
          <Popover open={showSortAdd} onOpenChange={setShowSortAdd}>
            <Tooltip>
              <TooltipTrigger asChild>
                <PopoverTrigger asChild>
                  <Button size="icon" variant="secondary" className="rounded-full"><ArrowUpDown className="h-4 w-4" /></Button>
                </PopoverTrigger>
              </TooltipTrigger>
              <TooltipContent>Add Sort</TooltipContent>
            </Tooltip>
            <PopoverContent className="w-72 p-4 flex flex-col gap-3">
              <Select value={newSort.field || ""} onValueChange={val => setNewSort(s => ({ ...s, field: val }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Field" /></SelectTrigger>
                <SelectContent>
                  {filterFields.map(f => <SelectItem key={f.value} value={f.value}>{f.label}</SelectItem>)}
                </SelectContent>
              </Select>
              <Select value={newSort.direction || ""} onValueChange={val => setNewSort(s => ({ ...s, direction: val as SortDirection }))}>
                <SelectTrigger className="w-full"><SelectValue placeholder="Direction" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="ASC">Ascending</SelectItem>
                  <SelectItem value="DESC">Descending</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex gap-2 justify-end">
                <Button onClick={handleSaveSort} size="sm" className="bg-primary text-primary-foreground">Add</Button>
                <Button onClick={() => setShowSortAdd(false)} size="icon" variant="ghost"><X className="h-4 w-4" /></Button>
              </div>
            </PopoverContent>
          </Popover>
        </TooltipProvider>
      </div>
      {children && <div className="ml-auto flex items-center">{children}</div>}
    </div>
  );
};

export default FilterSortBar; 