// This file is auto-generated by @hey-api/openapi-ts

export type AbpLoginResult = {
    result?: LoginResultType;
    readonly description?: string | null;
};

export type AgentCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    status: string;
    type?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    npwpNo?: string | null;
    bdmSapcode?: string | null;
    taxCode?: string | null;
    addressNpwp?: string | null;
    address?: string | null;
    sapcodeS4?: string | null;
};

export type AgentDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    status?: string | null;
    type?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    npwpNo?: string | null;
    bdmSapcode?: string | null;
    taxCode?: string | null;
    addressNpwp?: string | null;
    address?: string | null;
    sapcodeS4?: string | null;
};

export type AgentProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    status?: string | null;
    type?: string | null;
    npwpNo?: string | null;
    bdmSapcode?: string | null;
    taxCode?: string | null;
    addressNpwp?: string | null;
    address?: string | null;
    sapcodeS4?: string | null;
};

export type AgentShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    type?: string | null;
};

export type ApplicationAuthConfigurationDto = {
    grantedPolicies?: {
        [key: string]: boolean;
    } | null;
};

export type ApplicationConfigurationDto = {
    localization?: ApplicationLocalizationConfigurationDto;
    auth?: ApplicationAuthConfigurationDto;
    setting?: ApplicationSettingConfigurationDto;
    currentUser?: CurrentUserDto;
    features?: ApplicationFeatureConfigurationDto;
    globalFeatures?: ApplicationGlobalFeatureConfigurationDto;
    multiTenancy?: MultiTenancyInfoDto;
    currentTenant?: CurrentTenantDto;
    timing?: TimingDto;
    clock?: ClockDto;
    objectExtensions?: ObjectExtensionsDto;
    extraProperties?: {
        [key: string]: unknown;
    } | null;
};

export type ApplicationFeatureConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type ApplicationGlobalFeatureConfigurationDto = {
    enabledFeatures?: Array<string> | null;
};

export type ApplicationLocalizationConfigurationDto = {
    values?: {
        [key: string]: {
            [key: string]: string;
        };
    } | null;
    resources?: {
        [key: string]: ApplicationLocalizationResourceDto;
    } | null;
    languages?: Array<LanguageInfo> | null;
    currentCulture?: CurrentCultureDto;
    defaultResourceName?: string | null;
    languagesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
    languageFilesMap?: {
        [key: string]: Array<NameValue>;
    } | null;
};

export type ApplicationLocalizationResourceDto = {
    texts?: {
        [key: string]: string;
    } | null;
    baseResources?: Array<string> | null;
};

export type ApplicationSettingConfigurationDto = {
    values?: {
        [key: string]: string | null;
    } | null;
};

export type BcTypeCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    type?: string | null;
    createdBy?: string | null;
    transNo?: number | null;
    transName?: string | null;
    status?: string | null;
};

export type BcTypeDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    type?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    transNo?: number | null;
    transName?: string | null;
    status?: string | null;
};

export type BcTypeShortDto = {
    docEntry?: number;
    id?: string;
    type?: string | null;
    transName?: string | null;
};

export type BillingItemDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    docNum?: number;
    noBl?: string | null;
    dateBl?: string | null;
    tenantId?: number;
    bctype?: number;
    loadingItem?: string | null;
    loadingQty?: number;
    remark?: string | null;
    deleted?: string | null;
    status?: string | null;
    portServiceType?: string | null;
    loadingUnloadingType?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    unit?: string | null;
    notification?: string | null;
    itemName?: string | null;
    companyHeader?: string | null;
    total?: number;
    signature1?: string | null;
    signature2?: string | null;
    signature3?: string | null;
    bp?: number | null;
    jetty?: number | null;
    attachmentText?: string | null;
    type?: string | null;
    price?: number | null;
    serviceLoading?: number | null;
    classification?: string | null;
    weightCategory?: string | null;
    totalServiceLoading?: number;
    currencyPortService?: string | null;
    currencyServiceLoading?: string | null;
    baseId?: number | null;
    agent?: number | null;
    baseIdString?: string | null;
    netWeight?: number | null;
    unitPrice?: number | null;
    totalInv?: number | null;
    statusServiceLoading?: string | null;
    qtyEstimate?: number | null;
    priceEstimate?: number | null;
    billingType?: string | null;
    chargeTo?: string | null;
    taxCode?: string | null;
    qtyRevised?: number | null;
    priceRevised?: number | null;
    noNota?: string | null;
    totalEstimate?: number | null;
    totalRevised?: number | null;
    noNotaSl?: string | null;
    lineNum?: number | null;
    isTenant?: string | null;
    subTotal?: number | null;
    vatValue?: number | null;
    vatValueSl?: number | null;
    subTotalSl?: number | null;
    totalEstimateSl?: number | null;
    headerId?: string | null;
    zoneDetailId?: string | null;
    masterTenantId?: string | null;
    bcTypeId?: string | null;
};

export type BusinessPartnerCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    createdBy: string;
    status: string;
    alias?: string | null;
    image?: string | null;
    direction?: string | null;
    regionType: string;
    address?: string | null;
    tenant?: string | null;
    npwp?: string | null;
    nitku?: string | null;
};

export type BusinessPartnerDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    image?: string | null;
    direction?: string | null;
    regionType?: string | null;
    address?: string | null;
    tenant?: string | null;
    npwp?: string | null;
    nitku?: string | null;
};

export type BusinessPartnerShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    npwp?: string | null;
    nitku?: string | null;
};

export type CargoCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    createdBy: string;
    status: string;
    alias?: string | null;
    flag?: string | null;
    grossWeight: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    status?: string | null;
    alias?: string | null;
    flag?: string | null;
    grossWeight?: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    status?: string | null;
    alias?: string | null;
    flag?: string | null;
    grossWeight?: number;
    type?: string | null;
    loaQty?: number | null;
};

export type CargoShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    type?: string | null;
    grossWeight?: number | null;
};

export type ChangePasswordInput = {
    currentPassword?: string | null;
    newPassword: string;
};

export type ClockDto = {
    kind?: string | null;
};

export type CreateUpdateDocAttachmentDto = {
    id?: string;
    fileName: string;
    contentType: string;
    createdBy: string;
    documentReferenceId?: number | null;
    referenceId?: string | null;
    blobName?: string | null;
    description?: string | null;
    docType: string;
    transType: string;
    secretKey?: string | null;
    typePa?: string | null;
    filePath?: string | null;
    nameNoExt?: string | null;
    size?: string | null;
    extension?: string | null;
};

export type CreateUpdateExportVesselBillingDto = {
    id?: string;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    exportId?: number;
    updatedBy?: number;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    type?: string | null;
    periodDate?: string | null;
    deadWeight?: number | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    items?: Array<BillingItemDto> | null;
};

export type CreateUpdateExportVesselDto = {
    id?: string;
    concurrencyStamp?: string | null;
    docNum?: string | null;
    postingDate?: string;
    vesselName?: number;
    vesselArrival?: string;
    vesselDeparture?: string | null;
    voyage?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remarks?: string | null;
    deleted?: string | null;
    updatedBy?: number;
    docStatus?: string | null;
    grossWeight?: number | null;
    vesselFlag?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    deadWeight?: number | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    docType?: string | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    items?: Array<CreateUpdateVesselItemDto> | null;
};

export type CreateUpdateImportVesselBillingDto = {
    id?: string;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    importId?: number;
    updatedBy?: number;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    items?: Array<BillingItemDto> | null;
};

export type CreateUpdateImportVesselDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docNum?: number | null;
    bp?: string | null;
    vesselName?: string | null;
    shipment?: string | null;
    shipmentNo?: string | null;
    vesselArrival?: string | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    postingDate?: string | null;
    color?: string | null;
    flags?: string | null;
    remarks?: string | null;
    status?: string | null;
    isLocked?: string | null;
    isChange?: string | null;
    transType?: string | null;
    docType?: string | null;
    bcType?: number | null;
    portOrigin?: string | null;
    emailToPpjk?: string | null;
    matchKey?: string | null;
    voyage?: string | null;
    deleted?: string | null;
    docStatus?: string | null;
    grossWeight?: number;
    vesselFlag?: string | null;
    vesselDeparture?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    type?: string | null;
    jettyUpdate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    items?: Array<CreateUpdateVesselItemDto> | null;
};

export type CreateUpdateLocalVesselBillingDto = {
    id?: string;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    localId?: number;
    updatedBy?: number | null;
    docNum?: number | null;
    postingDate?: string;
    remarks?: string | null;
    type?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    items?: Array<BillingItemDto> | null;
};

export type CreateUpdateLocalVesselDto = {
    id?: string;
    concurrencyStamp?: string | null;
    docNum?: string | null;
    postingDate?: string;
    vesselType?: string | null;
    vesselName?: number;
    tongkang?: number | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remark?: string | null;
    deleted?: string | null;
    transType?: string | null;
    docType: string;
    updatedBy?: number;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    jetty?: number | null;
    status?: string | null;
    beratTugboat?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    grtVessel?: number | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    bargeId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    items?: Array<CreateUpdateVesselItemDto> | null;
};

export type CreateUpdateTradingInvoiceDto = {
    id?: string;
    docNum?: number;
    pInvNo?: string | null;
    pInvDate?: string | null;
    invNo?: string | null;
    invDate?: string | null;
    suratJalan?: string | null;
    packingList?: string | null;
    faktur?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    deleted?: string | null;
    status?: string | null;
    zoneDetailId?: string | null;
};

export type CreateUpdateTradingVesselDto = {
    id?: string;
    docNum?: number;
    tenant?: number;
    bp?: string | null;
    postDate?: string;
    contract?: string | null;
    bc?: number;
    updatedBy?: number;
    deleted?: string | null;
    status?: string | null;
    remark?: string | null;
    contractDate?: string | null;
    subconType?: string | null;
    docStatus?: string | null;
    items?: Array<VesselItemDto> | null;
};

export type CreateUpdateVesselItemDto = {
    id?: string;
    concurrencyStamp?: string | null;
    docEntry?: number | null;
    docNum?: number | null;
    tenantName?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    cargo?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    noBl?: string | null;
    dateBl?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    headerId?: string | null;
    letterNo?: string | null;
    docType?: string | null;
    vesselType?: string | null;
    shippingInstructionNo?: string | null;
    regType?: string | null;
    status?: string | null;
    shippingInstructionDate?: string | null;
    letterDate?: string | null;
    tenantId: string;
    bcTypeId?: string | null;
    businessPartnerId?: string | null;
    agentId?: string | null;
    masterExportClassificationId?: string | null;
};

export type CreateUpdateZoneDetailInvoiceDto = {
    id?: string;
    concurrencyStamp?: string | null;
    docNum?: number | null;
    noInv?: string | null;
    dateInv?: string | null;
    vendor?: string | null;
    value?: number | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    flags?: string | null;
    itemName?: string | null;
    qty?: number;
    docType?: string | null;
    transType?: string | null;
    isScan?: string | null;
    isOriginal?: string | null;
    isSend?: string | null;
    isFeOri?: string | null;
    isFeSend?: string | null;
    secretKey?: string | null;
    currency?: string | null;
    formE?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    deleted?: string | null;
    parentKey?: string | null;
    blkey?: number | null;
    deletedAt?: string | null;
    deleteBy?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    packingList?: string | null;
    fob?: number | null;
    costOfRepair?: number | null;
    invoiceImportNo?: string | null;
    invoiceImportDate?: string | null;
    zoneDetailId?: string | null;
};

export type CurrentCultureDto = {
    displayName?: string | null;
    englishName?: string | null;
    threeLetterIsoLanguageName?: string | null;
    twoLetterIsoLanguageName?: string | null;
    isRightToLeft?: boolean;
    cultureName?: string | null;
    name?: string | null;
    nativeName?: string | null;
    dateTimeFormat?: DateTimeFormatDto;
};

export type CurrentTenantDto = {
    id?: string | null;
    name?: string | null;
    isAvailable?: boolean;
};

export type CurrentUserDto = {
    isAuthenticated?: boolean;
    id?: string | null;
    tenantId?: string | null;
    impersonatorUserId?: string | null;
    impersonatorTenantId?: string | null;
    impersonatorUserName?: string | null;
    impersonatorTenantName?: string | null;
    userName?: string | null;
    name?: string | null;
    surName?: string | null;
    email?: string | null;
    emailVerified?: boolean;
    phoneNumber?: string | null;
    phoneNumberVerified?: boolean;
    roles?: Array<string> | null;
    sessionId?: string | null;
};

export type DateTimeFormatDto = {
    calendarAlgorithmType?: string | null;
    dateTimeFormatLong?: string | null;
    shortDatePattern?: string | null;
    fullDateTimePattern?: string | null;
    dateSeparator?: string | null;
    shortTimePattern?: string | null;
    longTimePattern?: string | null;
};

export type DestinationPortCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    deleted: string;
    createdBy?: number | null;
    updatedBy?: number | null;
    country?: string | null;
    docType: string;
};

export type DestinationPortDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    country?: string | null;
    docType?: string | null;
};

export type DestinationPortProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    docType?: string | null;
};

export type DocAttachmentDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    fileName?: string | null;
    contentType?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    documentReferenceId?: number | null;
    referenceId?: string | null;
    blobName?: string | null;
    description?: string | null;
    docType?: string | null;
    transType?: string | null;
    secretKey?: string | null;
    typePa?: string | null;
    nameNoExt?: string | null;
    size?: string | null;
    extension?: string | null;
    filePath?: string | null;
    streamUrl?: string | null;
};

export type DocAttachmentSortDto = {
    id?: string;
    docType?: string | null;
    transType?: string | null;
    description?: string | null;
    blobName?: string | null;
    referenceId?: string | null;
    streamUrl?: string | null;
    tabName?: string | null;
    fileName?: string | null;
    typePa?: string | null;
};

export type EntityExtensionDto = {
    properties?: {
        [key: string]: ExtensionPropertyDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type ExportVesselBillingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    exportId?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    type?: string | null;
    periodDate?: string | null;
    deadWeight?: number | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
};

export type ExportVesselBillingWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    exportId?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    type?: string | null;
    periodDate?: string | null;
    deadWeight?: number | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
    items?: Array<BillingItemDto> | null;
};

export type ExportVesselDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselName?: number;
    vesselArrival?: string;
    vesselDeparture?: string | null;
    voyage?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remarks?: string | null;
    deleted?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docStatus?: string | null;
    grossWeight?: number | null;
    vesselFlag?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    deadWeight?: number | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    docType?: string | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
};

export type ExportVesselProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselName?: string | null;
    shipment?: string | null;
    vesselArrival?: string;
    vesselDeparture?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    status?: string | null;
    remarks?: string | null;
    docType?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    statusBms?: string | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterAgent?: AgentProjectionDto;
    masterTrading?: TradingProjectionDto;
    masterSurveyor?: SurveyorProjectionDto;
    masterJetty?: JettyProjectionDto;
    vessel?: CargoProjectionDto;
    masterPortOrigin?: PortOfOriginProjectionDto;
    masterDestinationPort?: DestinationPortProjectionDto;
};

export type ExportVesselShortDto = {
    id?: string;
    docNum?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string;
    vesselDeparture?: string | null;
    shipment?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    status?: string | null;
    agentName?: string | null;
    masterJetty?: MasterJettyShortDto;
    masterPortOrigin?: MasterPortOriginShortDto;
    masterDestinationPort?: MasterDestinationPortShortDto;
    masterVessel?: MasterVesselShortDto;
};

export type ExportVesselWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselName?: number;
    vesselArrival?: string;
    vesselDeparture?: string | null;
    voyage?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remarks?: string | null;
    deleted?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docStatus?: string | null;
    grossWeight?: number | null;
    vesselFlag?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    deadWeight?: number | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    docType?: string | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
    items?: Array<VesselItemDto> | null;
};

export type ExtensionEnumDto = {
    fields?: Array<ExtensionEnumFieldDto> | null;
    localizationResource?: string | null;
};

export type ExtensionEnumFieldDto = {
    name?: string | null;
    value?: unknown;
};

export type ExtensionPropertyApiCreateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiDto = {
    onGet?: ExtensionPropertyApiGetDto;
    onCreate?: ExtensionPropertyApiCreateDto;
    onUpdate?: ExtensionPropertyApiUpdateDto;
};

export type ExtensionPropertyApiGetDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyApiUpdateDto = {
    isAvailable?: boolean;
};

export type ExtensionPropertyAttributeDto = {
    typeSimple?: string | null;
    config?: {
        [key: string]: unknown;
    } | null;
};

export type ExtensionPropertyDto = {
    type?: string | null;
    typeSimple?: string | null;
    displayName?: LocalizableStringDto;
    api?: ExtensionPropertyApiDto;
    ui?: ExtensionPropertyUiDto;
    policy?: ExtensionPropertyPolicyDto;
    attributes?: Array<ExtensionPropertyAttributeDto> | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
    defaultValue?: unknown;
};

export type ExtensionPropertyFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyGlobalFeaturePolicyDto = {
    features?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPermissionPolicyDto = {
    permissionNames?: Array<string> | null;
    requiresAll?: boolean;
};

export type ExtensionPropertyPolicyDto = {
    globalFeatures?: ExtensionPropertyGlobalFeaturePolicyDto;
    features?: ExtensionPropertyFeaturePolicyDto;
    permissions?: ExtensionPropertyPermissionPolicyDto;
};

export type ExtensionPropertyUiDto = {
    onTable?: ExtensionPropertyUiTableDto;
    onCreateForm?: ExtensionPropertyUiFormDto;
    onEditForm?: ExtensionPropertyUiFormDto;
    lookup?: ExtensionPropertyUiLookupDto;
};

export type ExtensionPropertyUiFormDto = {
    isVisible?: boolean;
};

export type ExtensionPropertyUiLookupDto = {
    url?: string | null;
    resultListPropertyName?: string | null;
    displayPropertyName?: string | null;
    valuePropertyName?: string | null;
    filterParamName?: string | null;
};

export type ExtensionPropertyUiTableDto = {
    isVisible?: boolean;
};

export type FileUploadInputDto = {
    fileName: string;
    contentType: string;
    fileContent: string;
    description?: string | null;
    referenceId?: string | null;
    referenceType?: string | null;
};

export type FileUploadResultDto = {
    id?: string;
    fileName?: string | null;
    contentType?: string | null;
    size?: number;
    uploadTime?: string;
    url?: string | null;
    streamUrl?: string | null;
};

export type FilterCondition = {
    fieldName: string;
    operator: FilterOperator;
    value: unknown;
};

export type FilterGroup = {
    operator: LogicalOperator;
    conditions: Array<FilterCondition>;
};

export type FilterOperator = 'Equals' | 'NotEquals' | 'Contains' | 'StartsWith' | 'EndsWith' | 'GreaterThan' | 'GreaterThanOrEqual' | 'LessThan' | 'LessThanOrEqual' | 'In' | 'NotIn' | 'Between' | 'NotBetween' | 'IsNull' | 'IsNotNull' | 'IsEmpty' | 'IsNotEmpty' | 'IsTrue' | 'IsFalse' | 'IsNullOrEmpty' | 'IsNotNullOrEmpty' | 'IsNullOrWhiteSpace' | 'IsNotNullOrWhiteSpace' | 'IsNumeric' | 'IsAlpha' | 'IsAlphaNumeric' | 'IsEmail' | 'IsUrl' | 'IsIp' | 'IsIpv4' | 'IsIpv6' | 'IsGuid' | 'IsGuidEmpty' | 'IsGuidNotEmpty' | 'IsGuidNull' | 'IsGuidNotNull' | 'IsGuidNullOrEmpty' | 'IsGuidNotNullOrEmpty' | 'IsGuidNullOrWhiteSpace' | 'IsGuidNotNullOrWhiteSpace' | 'IsGuidNumeric' | 'IsGuidAlpha' | 'IsGuidAlphaNumeric';

export type FindTenantResultDto = {
    success?: boolean;
    tenantId?: string | null;
    name?: string | null;
    normalizedName?: string | null;
    isActive?: boolean;
};

export type GenerateDocNumDto = {
    docNum?: string | null;
};

export type IanaTimeZone = {
    timeZoneName?: string | null;
};

export type IdentityUserDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    isDeleted?: boolean;
    deleterId?: string | null;
    deletionTime?: string | null;
    tenantId?: string | null;
    userName?: string | null;
    name?: string | null;
    surname?: string | null;
    email?: string | null;
    emailConfirmed?: boolean;
    phoneNumber?: string | null;
    phoneNumberConfirmed?: boolean;
    isActive?: boolean;
    lockoutEnabled?: boolean;
    accessFailedCount?: number;
    lockoutEnd?: string | null;
    concurrencyStamp?: string | null;
    entityVersion?: number;
    lastPasswordChangeTime?: string | null;
};

export type ImportVesselBillingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    importId?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
};

export type ImportVesselBillingWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    importId?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    docNum?: string | null;
    postingDate?: string | null;
    remarks?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
    items?: Array<BillingItemDto> | null;
};

export type ImportVesselDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: number | null;
    bp?: string | null;
    vesselName?: string | null;
    shipment?: string | null;
    shipmentNo?: string | null;
    vesselArrival?: string | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    postingDate?: string | null;
    color?: string | null;
    flags?: string | null;
    remarks?: string | null;
    status?: string | null;
    isLocked?: string | null;
    isChange?: string | null;
    transType?: string | null;
    docType?: string | null;
    bcType?: number | null;
    portOrigin?: string | null;
    emailToPpjk?: string | null;
    matchKey?: string | null;
    voyage?: string | null;
    deleted?: string | null;
    docStatus?: string | null;
    grossWeight?: number;
    vesselFlag?: string | null;
    vesselDeparture?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    type?: string | null;
    jettyUpdate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
};

export type ImportVesselProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: number | null;
    postingDate?: string | null;
    vesselName?: string | null;
    shipment?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    status?: string | null;
    remarks?: string | null;
    docType?: string | null;
    transType?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    statusBms?: string | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterAgent?: AgentProjectionDto;
    masterTrading?: TradingProjectionDto;
    masterSurveyor?: SurveyorProjectionDto;
    masterJetty?: JettyProjectionDto;
    vessel?: CargoProjectionDto;
    masterPortOrigin?: PortOfOriginProjectionDto;
    masterDestinationPort?: DestinationPortProjectionDto;
};

export type ImportVesselShortDto = {
    id?: string;
    docNum?: number | null;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    status?: string | null;
    agentName?: string | null;
    masterJetty?: MasterJettyShortDto;
    masterPortOrigin?: MasterPortOriginShortDto;
    masterDestinationPort?: MasterDestinationPortShortDto;
    masterVessel?: MasterVesselShortDto;
};

export type ImportVesselWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: number | null;
    bp?: string | null;
    vesselName?: string | null;
    shipment?: string | null;
    shipmentNo?: string | null;
    vesselArrival?: string | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    postingDate?: string | null;
    color?: string | null;
    flags?: string | null;
    remarks?: string | null;
    status?: string | null;
    isLocked?: string | null;
    isChange?: string | null;
    transType?: string | null;
    docType?: string | null;
    bcType?: number | null;
    portOrigin?: string | null;
    emailToPpjk?: string | null;
    matchKey?: string | null;
    voyage?: string | null;
    deleted?: string | null;
    docStatus?: string | null;
    grossWeight?: number;
    vesselFlag?: string | null;
    vesselDeparture?: string | null;
    vesselStatus?: string | null;
    jetty?: number | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    type?: string | null;
    jettyUpdate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
    items?: Array<VesselItemDto> | null;
};

export type ItemClassificationCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name?: string | null;
    reportType?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    category?: number | null;
};

export type ItemClassificationDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    reportType?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    category?: number | null;
};

export type JettyCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    alias: string;
    max: number;
    deleted: string;
    createdBy: number;
    updatedBy: number;
    port?: string | null;
    isCustomArea?: boolean | null;
};

export type JettyDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    alias?: string | null;
    max?: number;
    deleted?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    port?: string | null;
    isCustomArea?: boolean | null;
};

export type JettyProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    alias?: string | null;
    max?: number;
    deleted?: string | null;
    port?: string | null;
    isCustomArea?: boolean | null;
};

export type JettyShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    alias?: string | null;
    port?: string | null;
    max?: number | null;
};

export type LanguageInfo = {
    cultureName?: string | null;
    uiCultureName?: string | null;
    displayName?: string | null;
    readonly twoLetterISOLanguageName?: string | null;
};

export type LocalVesselBillingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    localId?: number;
    createdBy?: number | null;
    updatedBy?: number | null;
    docNum?: number | null;
    postingDate?: string;
    remarks?: string | null;
    type?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
};

export type LocalVesselBillingWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    portService?: string | null;
    status?: string | null;
    yearArrival?: string | null;
    jetty?: number;
    localId?: number;
    createdBy?: number | null;
    updatedBy?: number | null;
    docNum?: number | null;
    postingDate?: string;
    remarks?: string | null;
    type?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    periodDate?: string | null;
    billingNoteDate?: string | null;
    jettyId?: string | null;
    masterJetty?: JettyDto;
    items?: Array<BillingItemDto> | null;
};

export type LocalVesselDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselType?: string | null;
    vesselName?: number;
    tongkang?: number | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remark?: string | null;
    deleted?: string | null;
    transType?: string | null;
    docType?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    jetty?: number | null;
    status?: string | null;
    beratTugboat?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    grtVessel?: number | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    bargeId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    barge?: CargoDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
};

export type LocalVesselProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselType?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remark?: string | null;
    transType?: string | null;
    docType?: string | null;
    createdBy?: number;
    updatedBy?: number;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    status?: string | null;
    beratTugboat?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    statusBms?: string | null;
    grtVessel?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    bargeId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterAgent?: AgentProjectionDto;
    masterTrading?: TradingProjectionDto;
    masterSurveyor?: SurveyorProjectionDto;
    masterJetty?: JettyProjectionDto;
    vessel?: CargoProjectionDto;
    barge?: CargoProjectionDto;
    masterPortOrigin?: PortOfOriginProjectionDto;
    masterDestinationPort?: DestinationPortProjectionDto;
};

export type LocalVesselShortDto = {
    id?: string;
    docNum?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    status?: string | null;
    agentName?: string | null;
    masterJetty?: MasterJettyShortDto;
    masterPortOrigin?: MasterPortOriginShortDto;
    masterDestinationPort?: MasterDestinationPortShortDto;
    masterVessel?: MasterVesselShortDto;
    masterBarge?: MasterVesselShortDto;
};

export type LocalVesselWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: string | null;
    postingDate?: string;
    vesselType?: string | null;
    vesselName?: number;
    tongkang?: number | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    vesselQty?: number;
    portOrigin?: string | null;
    destinationPort?: string | null;
    remark?: string | null;
    deleted?: string | null;
    transType?: string | null;
    docType?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    voyage?: string | null;
    grossWeight?: number | null;
    docStatus?: string | null;
    jetty?: number | null;
    status?: string | null;
    beratTugboat?: number | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    reportDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    invoiceStatus?: string | null;
    agentId?: number | null;
    agentName?: string | null;
    statusBms?: string | null;
    grtVessel?: number | null;
    surveyorId?: number | null;
    tradingId?: number | null;
    jettyId?: string | null;
    vesselId?: string | null;
    bargeId?: string | null;
    masterAgentId?: string | null;
    masterTradingId?: string | null;
    masterSurveyorId?: string | null;
    asideDate?: string | null;
    castOfDate?: string | null;
    portOriginId?: string | null;
    destinationPortId?: string | null;
    masterAgent?: AgentDto;
    masterTrading?: TradingDto;
    masterSurveyor?: SurveyorDto;
    masterJetty?: JettyDto;
    vessel?: CargoDto;
    barge?: CargoDto;
    masterPortOrigin?: PortOfLoadingDto;
    masterDestinationPort?: DestinationPortDto;
    items?: Array<VesselItemDto> | null;
};

export type LocalizableStringDto = {
    name?: string | null;
    resource?: string | null;
};

export type LogicalOperator = 'And' | 'Or';

export type LoginResultType = 1 | 2 | 3 | 4 | 5;

export type MasterDestinationPortShortDto = {
    id?: string;
    name?: string | null;
    code?: string | null;
    country?: string | null;
};

export type MasterJettyShortDto = {
    id?: string;
    name?: string | null;
    alias?: string | null;
    port?: string | null;
    max?: number | null;
};

export type MasterPortOriginShortDto = {
    id?: string;
    name?: string | null;
    code?: string | null;
    country?: string | null;
};

export type MasterTenantDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    flags?: string | null;
    fullName?: string | null;
    letterPerson?: string | null;
    letterRole?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    status?: string | null;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
    sapcode?: string | null;
    isExternal?: string | null;
    billing?: string | null;
    billingPrice?: number | null;
    esignUserId?: string | null;
    token?: string | null;
    sapcodeBdt?: string | null;
    sapcodeUsd?: string | null;
    coordinate?: string | null;
    boundaries?: string | null;
    isTenant?: string | null;
    channelId?: string | null;
    usePrivy?: string | null;
    sapcodeS4?: string | null;
    skbpph?: string | null;
    companyGroup?: string | null;
    factoryLocation?: string | null;
    masterGroupId?: number | null;
};

export type MasterVesselShortDto = {
    id?: string;
    name?: string | null;
    alias?: string | null;
    type?: string | null;
    grossWeight?: number | null;
};

export type ModuleExtensionDto = {
    entities?: {
        [key: string]: EntityExtensionDto;
    } | null;
    configuration?: {
        [key: string]: unknown;
    } | null;
};

export type MultiTenancyInfoDto = {
    isEnabled?: boolean;
};

export type NameValue = {
    name?: string | null;
    value?: string | null;
};

export type ObjectExtensionsDto = {
    modules?: {
        [key: string]: ModuleExtensionDto;
    } | null;
    enums?: {
        [key: string]: ExtensionEnumDto;
    } | null;
};

export type PagedResultDtoOfAgentDto = {
    items?: Array<AgentDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfBcTypeDto = {
    items?: Array<BcTypeDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfBusinessPartnerDto = {
    items?: Array<BusinessPartnerDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfCargoDto = {
    items?: Array<CargoDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfDestinationPortDto = {
    items?: Array<DestinationPortDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfDocAttachmentDto = {
    items?: Array<DocAttachmentDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfExportVesselBillingDto = {
    items?: Array<ExportVesselBillingDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfExportVesselDto = {
    items?: Array<ExportVesselDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfExportVesselProjectionDto = {
    items?: Array<ExportVesselProjectionDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfImportVesselBillingDto = {
    items?: Array<ImportVesselBillingDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfImportVesselDto = {
    items?: Array<ImportVesselDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfImportVesselProjectionDto = {
    items?: Array<ImportVesselProjectionDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfItemClassificationDto = {
    items?: Array<ItemClassificationDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfJettyDto = {
    items?: Array<JettyDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfLocalVesselBillingDto = {
    items?: Array<LocalVesselBillingDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfLocalVesselDto = {
    items?: Array<LocalVesselDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfLocalVesselProjectionDto = {
    items?: Array<LocalVesselProjectionDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfMasterTenantDto = {
    items?: Array<MasterTenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfPortOfLoadingDto = {
    items?: Array<PortOfLoadingDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfPortServiceDto = {
    items?: Array<PortServiceDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfSurveyorDto = {
    items?: Array<SurveyorDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTenantDto = {
    items?: Array<TenantDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTradingInvoiceDto = {
    items?: Array<TradingInvoiceDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTradingVesselDto = {
    items?: Array<TradingVesselDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfTradingVesselProjectionDto = {
    items?: Array<TradingVesselProjectionDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfVesselHeaderDto = {
    items?: Array<VesselHeaderDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfVesselItemDto = {
    items?: Array<VesselItemDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfZoneDetailDto = {
    items?: Array<ZoneDetailDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfZoneDetailInvoiceDto = {
    items?: Array<ZoneDetailInvoiceDto> | null;
    totalCount?: number;
};

export type PagedResultDtoOfZoneDetailWithVesselHeadersDto = {
    items?: Array<ZoneDetailWithVesselHeadersDto> | null;
    totalCount?: number;
};

export type PortOfLoadingCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    deleted: string;
    createdBy?: number | null;
    updatedBy?: number | null;
    country?: string | null;
    docType: string;
};

export type PortOfLoadingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    deleted?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    country?: string | null;
    docType?: string | null;
};

export type PortOfOriginProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    docType?: string | null;
};

export type PortServiceCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    docEntry?: number;
    itemCode?: string | null;
    itemName?: string | null;
    active?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    frgnName?: string | null;
    sapCodeS4?: string | null;
};

export type PortServiceDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    itemCode?: string | null;
    itemName?: string | null;
    active?: string | null;
    createdBy?: number | null;
    updatedBy?: number | null;
    frgnName?: string | null;
    sapCodeS4?: string | null;
};

export type ProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    isExternal?: boolean;
    hasPassword?: boolean;
    concurrencyStamp?: string | null;
};

export type QueryParametersDto = {
    sorting?: string | null;
    page?: number;
    sort?: Array<SortInfo> | null;
    filterGroup?: FilterGroup;
    readonly skipCount?: number;
    maxResultCount?: number;
};

export type RegisterDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName: string;
    emailAddress: string;
    password: string;
    appName: string;
};

export type RemoteServiceErrorInfo = {
    code?: string | null;
    message?: string | null;
    details?: string | null;
    data?: {
        [key: string]: unknown;
    } | null;
    validationErrors?: Array<RemoteServiceValidationErrorInfo> | null;
};

export type RemoteServiceErrorResponse = {
    error?: RemoteServiceErrorInfo;
};

export type RemoteServiceValidationErrorInfo = {
    message?: string | null;
    members?: Array<string> | null;
};

export type ResetPasswordDto = {
    userId?: string;
    resetToken: string;
    password: string;
};

export type SendPasswordResetCodeDto = {
    email: string;
    appName: string;
    returnUrl?: string | null;
    returnUrlHash?: string | null;
};

export type SortInfo = {
    field?: string | null;
    desc?: boolean;
};

export type SurveyorCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    address?: string | null;
    npwp?: string | null;
    isActive: string;
    createdBy: number;
    updatedBy?: number | null;
};

export type SurveyorDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    isActive?: string | null;
    createdBy?: number;
    updatedBy?: number | null;
};

export type SurveyorProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    isActive?: string | null;
};

export type TenantCreateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    adminEmailAddress: string;
    adminPassword: string;
};

export type TenantCreateUpdateDto = {
    id?: string;
    concurrencyStamp?: string | null;
    name: string;
    createdBy: string;
    flags?: string | null;
    fullName?: string | null;
    letterPerson?: string | null;
    letterRole?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    status: string;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
    sapcode?: string | null;
    isExternal: string;
    billing?: string | null;
    billingPrice?: number | null;
    esignUserId?: string | null;
    token?: string | null;
    sapcodeBdt?: string | null;
    sapcodeUsd?: string | null;
    coordinate?: string | null;
    boundaries?: string | null;
    isTenant?: string | null;
    channelId?: string | null;
    usePrivy: string;
    sapcodeS4?: string | null;
    skbpph?: string | null;
    companyGroup?: string | null;
    factoryLocation?: string | null;
    masterGroupId?: number | null;
};

export type TenantDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    id?: string;
    name?: string | null;
    concurrencyStamp?: string | null;
};

export type TenantShortDto = {
    docEntry?: number;
    id?: string;
    name?: string | null;
    fullName?: string | null;
    npwp?: string | null;
    address?: string | null;
    nib?: string | null;
    phone?: string | null;
    noAndDateNotaris?: string | null;
    descNotaris?: string | null;
};

export type TenantUpdateDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    name: string;
    concurrencyStamp?: string | null;
};

export type TimeZone = {
    iana?: IanaTimeZone;
    windows?: WindowsTimeZone;
};

export type TimingDto = {
    timeZone?: TimeZone;
};

export type TradingDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    isActive?: string | null;
    createdBy?: number;
    updatedBy?: number | null;
};

export type TradingInvoiceDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    docNum?: number;
    pInvNo?: string | null;
    pInvDate?: string | null;
    invNo?: string | null;
    invDate?: string | null;
    suratJalan?: string | null;
    packingList?: string | null;
    faktur?: string | null;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    deleted?: string | null;
    status?: string | null;
    zoneDetailId?: string | null;
};

export type TradingProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    name?: string | null;
    address?: string | null;
    npwp?: string | null;
    isActive?: string | null;
};

export type TradingVesselDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    docNum?: number;
    tenant?: number;
    bp?: string | null;
    postDate?: string;
    contract?: string | null;
    bc?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    deleted?: string | null;
    status?: string | null;
    remark?: string | null;
    contractDate?: string | null;
    subconType?: string | null;
    docStatus?: string | null;
};

export type TradingVesselProjectionDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    docNum?: number;
    tenant?: number;
    bp?: string | null;
    postDate?: string;
    contract?: string | null;
    bc?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    deleted?: string | null;
    status?: string | null;
    remark?: string | null;
    contractDate?: string | null;
    subconType?: string | null;
    docStatus?: string | null;
};

export type TradingVesselShortDto = {
    id?: string;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    shipment?: string | null;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    status?: string | null;
    agentName?: string | null;
    masterJetty?: MasterJettyShortDto;
    masterPortOrigin?: MasterPortOriginShortDto;
    masterDestinationPort?: MasterDestinationPortShortDto;
    masterVessel?: MasterVesselShortDto;
};

export type TradingVesselWithItemsDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    docEntry?: number;
    docNum?: number;
    tenant?: number;
    bp?: string | null;
    postDate?: string;
    contract?: string | null;
    bc?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string | null;
    updatedAt?: string | null;
    deleted?: string | null;
    status?: string | null;
    remark?: string | null;
    contractDate?: string | null;
    subconType?: string | null;
    docStatus?: string | null;
    items?: Array<VesselItemDto> | null;
};

export type UpdateProfileDto = {
    readonly extraProperties?: {
        [key: string]: unknown;
    } | null;
    userName?: string | null;
    email?: string | null;
    name?: string | null;
    surname?: string | null;
    phoneNumber?: string | null;
    concurrencyStamp?: string | null;
};

export type UserLoginInfo = {
    userNameOrEmailAddress: string;
    password: string;
    rememberMe?: boolean;
};

export type VerifyPasswordResetTokenInput = {
    userId?: string;
    resetToken: string;
};

export type VesselHeaderDto = {
    id?: string;
    docEntry?: number;
    vesselName?: string | null;
    voyage?: string | null;
    vesselArrival?: string | null;
    vesselDeparture?: string | null;
    vesselType?: string | null;
    items?: Array<VesselItemDto> | null;
    cargo?: CargoShortDto;
    barge?: CargoShortDto;
    jetty?: JettyShortDto;
    portOrigin?: string | null;
    destinationPort?: string | null;
    berthingDate?: string | null;
    anchorageDate?: string | null;
    unloadingDate?: string | null;
    finishUnloadingDate?: string | null;
    grtWeight?: number | null;
    agentName?: string | null;
};

export type VesselItemDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: number;
    tenantName?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    cargo?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    noBl?: string | null;
    dateBl?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    headerId?: string | null;
    letterNo?: string | null;
    docType?: string | null;
    vesselType?: string | null;
    shippingInstructionNo?: string | null;
    regType?: string | null;
    status?: string | null;
    shippingInstructionDate?: string | null;
    letterDate?: string | null;
    tenantId?: string | null;
    bcTypeId?: string | null;
    businessPartnerId?: string | null;
    agentId?: string | null;
    masterExportClassificationId?: string | null;
    item?: string | null;
    tenant?: TenantShortDto;
    bcType?: BcTypeShortDto;
    masterAgent?: AgentShortDto;
    businessPartner?: BusinessPartnerShortDto;
    attachments?: Array<DocAttachmentSortDto> | null;
};

export type VesselListRequestDto = {
    maxResultCount?: number;
    skipCount?: number;
    sorting?: string | null;
    vesselType?: string | null;
    fromDate?: string | null;
    toDate?: string | null;
    vesselName?: string | null;
    voyage?: string | null;
    tenantName?: string | null;
    filterGroup?: FilterGroup;
};

export type WindowsTimeZone = {
    timeZoneId?: string | null;
};

export type ZoneDetailDto = {
    id?: string;
    docEntry?: number;
    bcTypeKey?: number | null;
    tenantKey?: number | null;
    bp?: string | null;
    cargo?: string | null;
    weight?: number | null;
    blNo?: string | null;
    blDate?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    createdId?: number | null;
    updatedBy?: string | null;
    updatedId?: number | null;
    color?: string | null;
    sapKbEntry?: number | null;
    noBl?: string | null;
    dateBl?: string | null;
    noInv?: string | null;
    dateInv?: string | null;
    shipmentNo?: string | null;
    ebillingDate?: string | null;
    skep?: string | null;
    skepDate?: string | null;
    pibNo?: string | null;
    pibDate?: string | null;
    vesselArrive?: string | null;
    expiredDate?: string | null;
    item?: string | null;
    qty?: number | null;
    amount?: number | null;
    status?: string | null;
    siteStatus?: string | null;
    docNum?: number | null;
    flags?: string | null;
    oceanFreight?: string | null;
    currency?: string | null;
    ocean?: string | null;
    cbmb?: string | null;
    freightValue?: number | null;
    attachment?: string | null;
    postDate?: string | null;
    docType?: string | null;
    isScan?: string | null;
    isOriginal?: string | null;
    isSend?: string | null;
    isFeOri?: string | null;
    isFeSend?: string | null;
    secretKey?: string | null;
    ppjk?: number | null;
    ppjkcodeTemp?: string | null;
    portOfLoading?: string | null;
    emailToPpjk?: string | null;
    letterNo?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    bpnum?: number | null;
    cargoNum?: number | null;
    lineNum?: number | null;
    isChange?: string | null;
    deleted?: string | null;
    sppbUpdateDate?: string | null;
    sppbNoUpdate?: string | null;
    sppbDateUpdate?: string | null;
    sppdNoUpdate?: string | null;
    sppdDateUpdate?: string | null;
    deletedAt?: string | null;
    deleteBy?: string | null;
    eBillingNo?: string | null;
    contractNo?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    internalCode?: string | null;
    contractDate?: string | null;
    regType?: string | null;
    cbm?: number | null;
    notification?: string | null;
    sppbstatus?: string | null;
    agent?: number | null;
    billingId?: number | null;
    insuranceCurrency?: string | null;
    insuranceValue?: number | null;
    destinationPortId?: number | null;
    netWeight?: number | null;
    unitPrice?: number | null;
    totalInv?: number | null;
    qtyEstimate?: number | null;
    priceEstimate?: number | null;
    billingType?: string | null;
    chargeTo?: string | null;
    qtyRevised?: number | null;
    priceRevised?: number | null;
    noNota?: string | null;
    totalEstimate?: number | null;
    totalRevised?: number | null;
    serialNumber?: string | null;
    serialNumber1?: string | null;
    serialNumber2?: string | null;
    serialNumber3?: string | null;
    serialNumber4?: string | null;
    serialNumber5?: string | null;
    serialNumber6?: string | null;
    isParent?: string | null;
    grtVessel?: number | null;
    npwpBp?: string | null;
    esignDecimal?: number | null;
    cargoId?: number | null;
    bargeId?: number | null;
    voyage?: string | null;
    vesselName?: string | null;
    processName?: string | null;
    rate?: number | null;
    bm?: number | null;
    ppn?: number | null;
    pph?: number | null;
    bmad?: number | null;
    bmtp?: number | null;
    formType?: string | null;
    billingDate?: string | null;
    isUrgent?: string | null;
    surveyorId?: number | null;
    surveyorName?: string | null;
    emailToBcDate?: string | null;
    container?: number | null;
    exportClassificationId?: string | null;
    warehouseId?: number | null;
    increaseValue?: number | null;
    decreaseValue?: number | null;
    increaseValuePpn?: number | null;
    decreaseValuePpn?: number | null;
    increaseValuePph?: number | null;
    decreaseValuePph?: number | null;
    repairLocation?: string | null;
    representativeId?: number | null;
    invoiceDetailId?: number | null;
    costOfRepair?: number | null;
    itemCategoryCode?: string | null;
    itemCategoryDescription?: string | null;
    sapBillingStatus?: string | null;
    letterDate?: string | null;
    shippingInstructionNo?: string | null;
    shippingInstructionDate?: string | null;
    tenantId?: string | null;
    bcTypeId?: string | null;
    headerId?: string | null;
    businessPartnerId?: string | null;
    agentId?: string | null;
    masterExportClassificationId?: string | null;
};

export type ZoneDetailInvoiceDto = {
    id?: string;
    creationTime?: string;
    creatorId?: string | null;
    lastModificationTime?: string | null;
    lastModifierId?: string | null;
    concurrencyStamp?: string | null;
    docEntry?: number;
    docNum?: number | null;
    noInv?: string | null;
    dateInv?: string | null;
    vendor?: string | null;
    value?: number | null;
    createdBy?: string | null;
    updatedBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    flags?: string | null;
    itemName?: string | null;
    qty?: number;
    docType?: string | null;
    transType?: string | null;
    isScan?: string | null;
    isOriginal?: string | null;
    isSend?: string | null;
    isFeOri?: string | null;
    isFeSend?: string | null;
    secretKey?: string | null;
    currency?: string | null;
    formE?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    deleted?: string | null;
    parentKey?: string | null;
    blkey?: number | null;
    deletedAt?: string | null;
    deleteBy?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    packingList?: string | null;
    fob?: number | null;
    costOfRepair?: number | null;
    invoiceImportNo?: string | null;
    invoiceImportDate?: string | null;
    zoneDetailId?: string | null;
};

export type ZoneDetailWithVesselHeadersDto = {
    id?: string;
    docEntry?: number;
    bcTypeKey?: number | null;
    tenantKey?: number | null;
    bp?: string | null;
    cargo?: string | null;
    weight?: number | null;
    blNo?: string | null;
    blDate?: string | null;
    ajuNo?: string | null;
    regNo?: string | null;
    regDate?: string | null;
    sppbNo?: string | null;
    sppbDate?: string | null;
    sppdNo?: string | null;
    sppdDate?: string | null;
    shipment?: string | null;
    remarks?: string | null;
    createdBy?: string | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    createdId?: number | null;
    updatedBy?: string | null;
    updatedId?: number | null;
    color?: string | null;
    sapKbEntry?: number | null;
    noBl?: string | null;
    dateBl?: string | null;
    noInv?: string | null;
    dateInv?: string | null;
    shipmentNo?: string | null;
    ebillingDate?: string | null;
    skep?: string | null;
    skepDate?: string | null;
    pibNo?: string | null;
    pibDate?: string | null;
    vesselArrive?: string | null;
    expiredDate?: string | null;
    item?: string | null;
    qty?: number | null;
    amount?: number | null;
    status?: string | null;
    siteStatus?: string | null;
    docNum?: number | null;
    flags?: string | null;
    oceanFreight?: string | null;
    currency?: string | null;
    ocean?: string | null;
    cbmb?: string | null;
    freightValue?: number | null;
    attachment?: string | null;
    postDate?: string | null;
    docType?: string | null;
    isScan?: string | null;
    isOriginal?: string | null;
    isSend?: string | null;
    isFeOri?: string | null;
    isFeSend?: string | null;
    secretKey?: string | null;
    ppjk?: number | null;
    ppjkcodeTemp?: string | null;
    portOfLoading?: string | null;
    emailToPpjk?: string | null;
    letterNo?: string | null;
    itemName?: string | null;
    itemQty?: number | null;
    unitQty?: string | null;
    grossWeight?: number | null;
    unitWeight?: string | null;
    matchKey?: string | null;
    bpnum?: number | null;
    cargoNum?: number | null;
    lineNum?: number | null;
    isChange?: string | null;
    deleted?: string | null;
    sppbUpdateDate?: string | null;
    sppbNoUpdate?: string | null;
    sppbDateUpdate?: string | null;
    sppdNoUpdate?: string | null;
    sppdDateUpdate?: string | null;
    deletedAt?: string | null;
    deleteBy?: string | null;
    eBillingNo?: string | null;
    contractNo?: string | null;
    openDate?: string | null;
    updateDate?: string | null;
    internalCode?: string | null;
    contractDate?: string | null;
    regType?: string | null;
    cbm?: number | null;
    notification?: string | null;
    sppbstatus?: string | null;
    agent?: number | null;
    billingId?: number | null;
    insuranceCurrency?: string | null;
    insuranceValue?: number | null;
    destinationPortId?: number | null;
    netWeight?: number | null;
    unitPrice?: number | null;
    totalInv?: number | null;
    qtyEstimate?: number | null;
    priceEstimate?: number | null;
    billingType?: string | null;
    chargeTo?: string | null;
    qtyRevised?: number | null;
    priceRevised?: number | null;
    noNota?: string | null;
    totalEstimate?: number | null;
    totalRevised?: number | null;
    serialNumber?: string | null;
    serialNumber1?: string | null;
    serialNumber2?: string | null;
    serialNumber3?: string | null;
    serialNumber4?: string | null;
    serialNumber5?: string | null;
    serialNumber6?: string | null;
    isParent?: string | null;
    grtVessel?: number | null;
    npwpBp?: string | null;
    esignDecimal?: number | null;
    cargoId?: number | null;
    bargeId?: number | null;
    voyage?: string | null;
    vesselName?: string | null;
    processName?: string | null;
    rate?: number | null;
    bm?: number | null;
    ppn?: number | null;
    pph?: number | null;
    bmad?: number | null;
    bmtp?: number | null;
    formType?: string | null;
    billingDate?: string | null;
    isUrgent?: string | null;
    surveyorId?: number | null;
    surveyorName?: string | null;
    emailToBcDate?: string | null;
    container?: number | null;
    exportClassificationId?: string | null;
    warehouseId?: number | null;
    increaseValue?: number | null;
    decreaseValue?: number | null;
    increaseValuePpn?: number | null;
    decreaseValuePpn?: number | null;
    increaseValuePph?: number | null;
    decreaseValuePph?: number | null;
    repairLocation?: string | null;
    representativeId?: number | null;
    invoiceDetailId?: number | null;
    costOfRepair?: number | null;
    itemCategoryCode?: string | null;
    itemCategoryDescription?: string | null;
    sapBillingStatus?: string | null;
    letterDate?: string | null;
    shippingInstructionNo?: string | null;
    shippingInstructionDate?: string | null;
    tenantId?: string | null;
    bcTypeId?: string | null;
    headerId?: string | null;
    businessPartnerId?: string | null;
    agentId?: string | null;
    masterExportClassificationId?: string | null;
    importVessel?: ImportVesselShortDto;
    exportVessel?: ExportVesselShortDto;
    localVessel?: LocalVesselShortDto;
    tradingVessel?: TradingVesselShortDto;
};

export type GetApiAbpApplicationConfigurationData = {
    body?: never;
    path?: never;
    query?: {
        IncludeLocalizationResources?: boolean;
    };
    url: '/api/abp/application-configuration';
};

export type GetApiAbpApplicationConfigurationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpApplicationConfigurationError = GetApiAbpApplicationConfigurationErrors[keyof GetApiAbpApplicationConfigurationErrors];

export type GetApiAbpApplicationConfigurationResponses = {
    /**
     * OK
     */
    200: ApplicationConfigurationDto;
};

export type GetApiAbpApplicationConfigurationResponse = GetApiAbpApplicationConfigurationResponses[keyof GetApiAbpApplicationConfigurationResponses];

export type GetApiAbpMultiTenancyTenantsByNameByNameData = {
    body?: never;
    path: {
        name: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-name/{name}';
};

export type GetApiAbpMultiTenancyTenantsByNameByNameErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameError = GetApiAbpMultiTenancyTenantsByNameByNameErrors[keyof GetApiAbpMultiTenancyTenantsByNameByNameErrors];

export type GetApiAbpMultiTenancyTenantsByNameByNameResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByNameByNameResponse = GetApiAbpMultiTenancyTenantsByNameByNameResponses[keyof GetApiAbpMultiTenancyTenantsByNameByNameResponses];

export type GetApiAbpMultiTenancyTenantsByIdByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/abp/multi-tenancy/tenants/by-id/{id}';
};

export type GetApiAbpMultiTenancyTenantsByIdByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdError = GetApiAbpMultiTenancyTenantsByIdByIdErrors[keyof GetApiAbpMultiTenancyTenantsByIdByIdErrors];

export type GetApiAbpMultiTenancyTenantsByIdByIdResponses = {
    /**
     * OK
     */
    200: FindTenantResultDto;
};

export type GetApiAbpMultiTenancyTenantsByIdByIdResponse = GetApiAbpMultiTenancyTenantsByIdByIdResponses[keyof GetApiAbpMultiTenancyTenantsByIdByIdResponses];

export type PostApiAccountRegisterData = {
    body?: RegisterDto;
    path?: never;
    query?: never;
    url: '/api/account/register';
};

export type PostApiAccountRegisterErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountRegisterError = PostApiAccountRegisterErrors[keyof PostApiAccountRegisterErrors];

export type PostApiAccountRegisterResponses = {
    /**
     * OK
     */
    200: IdentityUserDto;
};

export type PostApiAccountRegisterResponse = PostApiAccountRegisterResponses[keyof PostApiAccountRegisterResponses];

export type PostApiAccountSendPasswordResetCodeData = {
    body?: SendPasswordResetCodeDto;
    path?: never;
    query?: never;
    url: '/api/account/send-password-reset-code';
};

export type PostApiAccountSendPasswordResetCodeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountSendPasswordResetCodeError = PostApiAccountSendPasswordResetCodeErrors[keyof PostApiAccountSendPasswordResetCodeErrors];

export type PostApiAccountSendPasswordResetCodeResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountVerifyPasswordResetTokenData = {
    body?: VerifyPasswordResetTokenInput;
    path?: never;
    query?: never;
    url: '/api/account/verify-password-reset-token';
};

export type PostApiAccountVerifyPasswordResetTokenErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountVerifyPasswordResetTokenError = PostApiAccountVerifyPasswordResetTokenErrors[keyof PostApiAccountVerifyPasswordResetTokenErrors];

export type PostApiAccountVerifyPasswordResetTokenResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type PostApiAccountVerifyPasswordResetTokenResponse = PostApiAccountVerifyPasswordResetTokenResponses[keyof PostApiAccountVerifyPasswordResetTokenResponses];

export type PostApiAccountResetPasswordData = {
    body?: ResetPasswordDto;
    path?: never;
    query?: never;
    url: '/api/account/reset-password';
};

export type PostApiAccountResetPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountResetPasswordError = PostApiAccountResetPasswordErrors[keyof PostApiAccountResetPasswordErrors];

export type PostApiAccountResetPasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiMasterAgentFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/master/agent/filter-list';
};

export type PostApiMasterAgentFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMasterAgentFilterListError = PostApiMasterAgentFilterListErrors[keyof PostApiMasterAgentFilterListErrors];

export type PostApiMasterAgentFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfAgentDto;
};

export type PostApiMasterAgentFilterListResponse = PostApiMasterAgentFilterListResponses[keyof PostApiMasterAgentFilterListResponses];

export type GetApiMasterAgentData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/master/agent';
};

export type GetApiMasterAgentErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMasterAgentError = GetApiMasterAgentErrors[keyof GetApiMasterAgentErrors];

export type GetApiMasterAgentResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfAgentDto;
};

export type GetApiMasterAgentResponse = GetApiMasterAgentResponses[keyof GetApiMasterAgentResponses];

export type PostApiMasterAgentData = {
    body?: AgentCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/master/agent';
};

export type PostApiMasterAgentErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMasterAgentError = PostApiMasterAgentErrors[keyof PostApiMasterAgentErrors];

export type PostApiMasterAgentResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type PostApiMasterAgentResponse = PostApiMasterAgentResponses[keyof PostApiMasterAgentResponses];

export type DeleteApiMasterAgentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/master/agent/{id}';
};

export type DeleteApiMasterAgentByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMasterAgentByIdError = DeleteApiMasterAgentByIdErrors[keyof DeleteApiMasterAgentByIdErrors];

export type DeleteApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMasterAgentByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/master/agent/{id}';
};

export type GetApiMasterAgentByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMasterAgentByIdError = GetApiMasterAgentByIdErrors[keyof GetApiMasterAgentByIdErrors];

export type GetApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type GetApiMasterAgentByIdResponse = GetApiMasterAgentByIdResponses[keyof GetApiMasterAgentByIdResponses];

export type PutApiMasterAgentByIdData = {
    body?: AgentCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/master/agent/{id}';
};

export type PutApiMasterAgentByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMasterAgentByIdError = PutApiMasterAgentByIdErrors[keyof PutApiMasterAgentByIdErrors];

export type PutApiMasterAgentByIdResponses = {
    /**
     * OK
     */
    200: AgentDto;
};

export type PutApiMasterAgentByIdResponse = PutApiMasterAgentByIdResponses[keyof PutApiMasterAgentByIdResponses];

export type GetApiAuthLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/Auth/logout';
};

export type GetApiAuthLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBcTypeData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/bc-type';
};

export type GetApiEkbBcTypeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBcTypeError = GetApiEkbBcTypeErrors[keyof GetApiEkbBcTypeErrors];

export type GetApiEkbBcTypeResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBcTypeDto;
};

export type GetApiEkbBcTypeResponse = GetApiEkbBcTypeResponses[keyof GetApiEkbBcTypeResponses];

export type PostApiEkbBcTypeData = {
    body?: BcTypeCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bc-type';
};

export type PostApiEkbBcTypeErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBcTypeError = PostApiEkbBcTypeErrors[keyof PostApiEkbBcTypeErrors];

export type PostApiEkbBcTypeResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type PostApiEkbBcTypeResponse = PostApiEkbBcTypeResponses[keyof PostApiEkbBcTypeResponses];

export type DeleteApiEkbBcTypeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type DeleteApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBcTypeByIdError = DeleteApiEkbBcTypeByIdErrors[keyof DeleteApiEkbBcTypeByIdErrors];

export type DeleteApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBcTypeByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type GetApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBcTypeByIdError = GetApiEkbBcTypeByIdErrors[keyof GetApiEkbBcTypeByIdErrors];

export type GetApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type GetApiEkbBcTypeByIdResponse = GetApiEkbBcTypeByIdResponses[keyof GetApiEkbBcTypeByIdResponses];

export type PutApiEkbBcTypeByIdData = {
    body?: BcTypeCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bc-type/{id}';
};

export type PutApiEkbBcTypeByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBcTypeByIdError = PutApiEkbBcTypeByIdErrors[keyof PutApiEkbBcTypeByIdErrors];

export type PutApiEkbBcTypeByIdResponses = {
    /**
     * OK
     */
    200: BcTypeDto;
};

export type PutApiEkbBcTypeByIdResponse = PutApiEkbBcTypeByIdResponses[keyof PutApiEkbBcTypeByIdResponses];

export type PostApiEkbBcTypeFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bc-type/filter-list';
};

export type PostApiEkbBcTypeFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBcTypeFilterListError = PostApiEkbBcTypeFilterListErrors[keyof PostApiEkbBcTypeFilterListErrors];

export type PostApiEkbBcTypeFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBcTypeDto;
};

export type PostApiEkbBcTypeFilterListResponse = PostApiEkbBcTypeFilterListResponses[keyof PostApiEkbBcTypeFilterListResponses];

export type PostApiEkbBoundedZoneListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/list';
};

export type PostApiEkbBoundedZoneListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneListError = PostApiEkbBoundedZoneListErrors[keyof PostApiEkbBoundedZoneListErrors];

export type PostApiEkbBoundedZoneListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type PostApiEkbBoundedZoneListResponse = PostApiEkbBoundedZoneListResponses[keyof PostApiEkbBoundedZoneListResponses];

export type PostApiEkbBoundedZoneVesselHeadersData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/vessel-headers';
};

export type PostApiEkbBoundedZoneVesselHeadersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneVesselHeadersError = PostApiEkbBoundedZoneVesselHeadersErrors[keyof PostApiEkbBoundedZoneVesselHeadersErrors];

export type PostApiEkbBoundedZoneVesselHeadersResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailWithVesselHeadersDto;
};

export type PostApiEkbBoundedZoneVesselHeadersResponse = PostApiEkbBoundedZoneVesselHeadersResponses[keyof PostApiEkbBoundedZoneVesselHeadersResponses];

export type GetApiEkbBoundedZoneData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/bounded-zone';
};

export type GetApiEkbBoundedZoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBoundedZoneError = GetApiEkbBoundedZoneErrors[keyof GetApiEkbBoundedZoneErrors];

export type GetApiEkbBoundedZoneResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type GetApiEkbBoundedZoneResponse = GetApiEkbBoundedZoneResponses[keyof GetApiEkbBoundedZoneResponses];

export type PostApiEkbBoundedZoneData = {
    body?: CreateUpdateVesselItemDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone';
};

export type PostApiEkbBoundedZoneErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneError = PostApiEkbBoundedZoneErrors[keyof PostApiEkbBoundedZoneErrors];

export type PostApiEkbBoundedZoneResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type PostApiEkbBoundedZoneResponse = PostApiEkbBoundedZoneResponses[keyof PostApiEkbBoundedZoneResponses];

export type DeleteApiEkbBoundedZoneByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type DeleteApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBoundedZoneByIdError = DeleteApiEkbBoundedZoneByIdErrors[keyof DeleteApiEkbBoundedZoneByIdErrors];

export type DeleteApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBoundedZoneByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type GetApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBoundedZoneByIdError = GetApiEkbBoundedZoneByIdErrors[keyof GetApiEkbBoundedZoneByIdErrors];

export type GetApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type GetApiEkbBoundedZoneByIdResponse = GetApiEkbBoundedZoneByIdResponses[keyof GetApiEkbBoundedZoneByIdResponses];

export type PutApiEkbBoundedZoneByIdData = {
    body?: CreateUpdateVesselItemDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/bounded-zone/{id}';
};

export type PutApiEkbBoundedZoneByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBoundedZoneByIdError = PutApiEkbBoundedZoneByIdErrors[keyof PutApiEkbBoundedZoneByIdErrors];

export type PutApiEkbBoundedZoneByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailDto;
};

export type PutApiEkbBoundedZoneByIdResponse = PutApiEkbBoundedZoneByIdResponses[keyof PutApiEkbBoundedZoneByIdResponses];

export type PostApiEkbBoundedZoneFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/filter-list';
};

export type PostApiEkbBoundedZoneFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneFilterListError = PostApiEkbBoundedZoneFilterListErrors[keyof PostApiEkbBoundedZoneFilterListErrors];

export type PostApiEkbBoundedZoneFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type PostApiEkbBoundedZoneFilterListResponse = PostApiEkbBoundedZoneFilterListResponses[keyof PostApiEkbBoundedZoneFilterListResponses];

export type GetApiEkbBoundedZoneQueryableWithVesselHeadersData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/bounded-zone/queryable-with-vessel-headers';
};

export type GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBoundedZoneQueryableWithVesselHeadersError = GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors[keyof GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors];

export type GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailDto;
};

export type GetApiEkbBoundedZoneQueryableWithVesselHeadersResponse = GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses[keyof GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses];

export type PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/bounded-zone/queryable-with-vessel-headers-dto';
};

export type PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoError = PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors[keyof PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors];

export type PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailWithVesselHeadersDto;
};

export type PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponse = PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses[keyof PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses];

export type GetApiEkbBusinessPartnerData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/business-partner';
};

export type GetApiEkbBusinessPartnerErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBusinessPartnerError = GetApiEkbBusinessPartnerErrors[keyof GetApiEkbBusinessPartnerErrors];

export type GetApiEkbBusinessPartnerResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBusinessPartnerDto;
};

export type GetApiEkbBusinessPartnerResponse = GetApiEkbBusinessPartnerResponses[keyof GetApiEkbBusinessPartnerResponses];

export type PostApiEkbBusinessPartnerData = {
    body?: BusinessPartnerCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/business-partner';
};

export type PostApiEkbBusinessPartnerErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBusinessPartnerError = PostApiEkbBusinessPartnerErrors[keyof PostApiEkbBusinessPartnerErrors];

export type PostApiEkbBusinessPartnerResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type PostApiEkbBusinessPartnerResponse = PostApiEkbBusinessPartnerResponses[keyof PostApiEkbBusinessPartnerResponses];

export type DeleteApiEkbBusinessPartnerByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type DeleteApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbBusinessPartnerByIdError = DeleteApiEkbBusinessPartnerByIdErrors[keyof DeleteApiEkbBusinessPartnerByIdErrors];

export type DeleteApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbBusinessPartnerByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type GetApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbBusinessPartnerByIdError = GetApiEkbBusinessPartnerByIdErrors[keyof GetApiEkbBusinessPartnerByIdErrors];

export type GetApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type GetApiEkbBusinessPartnerByIdResponse = GetApiEkbBusinessPartnerByIdResponses[keyof GetApiEkbBusinessPartnerByIdResponses];

export type PutApiEkbBusinessPartnerByIdData = {
    body?: BusinessPartnerCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/business-partner/{id}';
};

export type PutApiEkbBusinessPartnerByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbBusinessPartnerByIdError = PutApiEkbBusinessPartnerByIdErrors[keyof PutApiEkbBusinessPartnerByIdErrors];

export type PutApiEkbBusinessPartnerByIdResponses = {
    /**
     * OK
     */
    200: BusinessPartnerDto;
};

export type PutApiEkbBusinessPartnerByIdResponse = PutApiEkbBusinessPartnerByIdResponses[keyof PutApiEkbBusinessPartnerByIdResponses];

export type PostApiEkbBusinessPartnerFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/business-partner/filter-list';
};

export type PostApiEkbBusinessPartnerFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbBusinessPartnerFilterListError = PostApiEkbBusinessPartnerFilterListErrors[keyof PostApiEkbBusinessPartnerFilterListErrors];

export type PostApiEkbBusinessPartnerFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfBusinessPartnerDto;
};

export type PostApiEkbBusinessPartnerFilterListResponse = PostApiEkbBusinessPartnerFilterListResponses[keyof PostApiEkbBusinessPartnerFilterListResponses];

export type GetApiEkbCargoData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/cargo';
};

export type GetApiEkbCargoErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbCargoError = GetApiEkbCargoErrors[keyof GetApiEkbCargoErrors];

export type GetApiEkbCargoResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfCargoDto;
};

export type GetApiEkbCargoResponse = GetApiEkbCargoResponses[keyof GetApiEkbCargoResponses];

export type PostApiEkbCargoData = {
    body?: CargoCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/cargo';
};

export type PostApiEkbCargoErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbCargoError = PostApiEkbCargoErrors[keyof PostApiEkbCargoErrors];

export type PostApiEkbCargoResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type PostApiEkbCargoResponse = PostApiEkbCargoResponses[keyof PostApiEkbCargoResponses];

export type DeleteApiEkbCargoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type DeleteApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbCargoByIdError = DeleteApiEkbCargoByIdErrors[keyof DeleteApiEkbCargoByIdErrors];

export type DeleteApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbCargoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type GetApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbCargoByIdError = GetApiEkbCargoByIdErrors[keyof GetApiEkbCargoByIdErrors];

export type GetApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type GetApiEkbCargoByIdResponse = GetApiEkbCargoByIdResponses[keyof GetApiEkbCargoByIdResponses];

export type PutApiEkbCargoByIdData = {
    body?: CargoCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/cargo/{id}';
};

export type PutApiEkbCargoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbCargoByIdError = PutApiEkbCargoByIdErrors[keyof PutApiEkbCargoByIdErrors];

export type PutApiEkbCargoByIdResponses = {
    /**
     * OK
     */
    200: CargoDto;
};

export type PutApiEkbCargoByIdResponse = PutApiEkbCargoByIdResponses[keyof PutApiEkbCargoByIdResponses];

export type PostApiEkbCargoFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/cargo/filter-list';
};

export type PostApiEkbCargoFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbCargoFilterListError = PostApiEkbCargoFilterListErrors[keyof PostApiEkbCargoFilterListErrors];

export type PostApiEkbCargoFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfCargoDto;
};

export type PostApiEkbCargoFilterListResponse = PostApiEkbCargoFilterListResponses[keyof PostApiEkbCargoFilterListResponses];

export type GetApiEkbDestinationPortData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/destination-port';
};

export type GetApiEkbDestinationPortErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDestinationPortError = GetApiEkbDestinationPortErrors[keyof GetApiEkbDestinationPortErrors];

export type GetApiEkbDestinationPortResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDestinationPortDto;
};

export type GetApiEkbDestinationPortResponse = GetApiEkbDestinationPortResponses[keyof GetApiEkbDestinationPortResponses];

export type PostApiEkbDestinationPortData = {
    body?: DestinationPortCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/destination-port';
};

export type PostApiEkbDestinationPortErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDestinationPortError = PostApiEkbDestinationPortErrors[keyof PostApiEkbDestinationPortErrors];

export type PostApiEkbDestinationPortResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type PostApiEkbDestinationPortResponse = PostApiEkbDestinationPortResponses[keyof PostApiEkbDestinationPortResponses];

export type DeleteApiEkbDestinationPortByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type DeleteApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbDestinationPortByIdError = DeleteApiEkbDestinationPortByIdErrors[keyof DeleteApiEkbDestinationPortByIdErrors];

export type DeleteApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbDestinationPortByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type GetApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDestinationPortByIdError = GetApiEkbDestinationPortByIdErrors[keyof GetApiEkbDestinationPortByIdErrors];

export type GetApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type GetApiEkbDestinationPortByIdResponse = GetApiEkbDestinationPortByIdResponses[keyof GetApiEkbDestinationPortByIdResponses];

export type PutApiEkbDestinationPortByIdData = {
    body?: DestinationPortCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/destination-port/{id}';
};

export type PutApiEkbDestinationPortByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbDestinationPortByIdError = PutApiEkbDestinationPortByIdErrors[keyof PutApiEkbDestinationPortByIdErrors];

export type PutApiEkbDestinationPortByIdResponses = {
    /**
     * OK
     */
    200: DestinationPortDto;
};

export type PutApiEkbDestinationPortByIdResponse = PutApiEkbDestinationPortByIdResponses[keyof PutApiEkbDestinationPortByIdResponses];

export type PostApiEkbDestinationPortFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/destination-port/filter-list';
};

export type PostApiEkbDestinationPortFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDestinationPortFilterListError = PostApiEkbDestinationPortFilterListErrors[keyof PostApiEkbDestinationPortFilterListErrors];

export type PostApiEkbDestinationPortFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDestinationPortDto;
};

export type PostApiEkbDestinationPortFilterListResponse = PostApiEkbDestinationPortFilterListResponses[keyof PostApiEkbDestinationPortFilterListResponses];

export type PostApiEkbDocAttachmentsListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/doc-attachments/list';
};

export type PostApiEkbDocAttachmentsListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDocAttachmentsListError = PostApiEkbDocAttachmentsListErrors[keyof PostApiEkbDocAttachmentsListErrors];

export type PostApiEkbDocAttachmentsListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfDocAttachmentDto;
};

export type PostApiEkbDocAttachmentsListResponse = PostApiEkbDocAttachmentsListResponses[keyof PostApiEkbDocAttachmentsListResponses];

export type DeleteApiEkbDocAttachmentsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/{id}';
};

export type DeleteApiEkbDocAttachmentsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbDocAttachmentsByIdError = DeleteApiEkbDocAttachmentsByIdErrors[keyof DeleteApiEkbDocAttachmentsByIdErrors];

export type DeleteApiEkbDocAttachmentsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbDocAttachmentsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/{id}';
};

export type GetApiEkbDocAttachmentsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDocAttachmentsByIdError = GetApiEkbDocAttachmentsByIdErrors[keyof GetApiEkbDocAttachmentsByIdErrors];

export type GetApiEkbDocAttachmentsByIdResponses = {
    /**
     * OK
     */
    200: DocAttachmentDto;
};

export type GetApiEkbDocAttachmentsByIdResponse = GetApiEkbDocAttachmentsByIdResponses[keyof GetApiEkbDocAttachmentsByIdResponses];

export type PutApiEkbDocAttachmentsByIdData = {
    body?: CreateUpdateDocAttachmentDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/{id}';
};

export type PutApiEkbDocAttachmentsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbDocAttachmentsByIdError = PutApiEkbDocAttachmentsByIdErrors[keyof PutApiEkbDocAttachmentsByIdErrors];

export type PutApiEkbDocAttachmentsByIdResponses = {
    /**
     * OK
     */
    200: DocAttachmentDto;
};

export type PutApiEkbDocAttachmentsByIdResponse = PutApiEkbDocAttachmentsByIdResponses[keyof PutApiEkbDocAttachmentsByIdResponses];

export type PostApiEkbDocAttachmentsData = {
    body?: CreateUpdateDocAttachmentDto;
    path?: never;
    query?: never;
    url: '/api/ekb/doc-attachments';
};

export type PostApiEkbDocAttachmentsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDocAttachmentsError = PostApiEkbDocAttachmentsErrors[keyof PostApiEkbDocAttachmentsErrors];

export type PostApiEkbDocAttachmentsResponses = {
    /**
     * OK
     */
    200: DocAttachmentDto;
};

export type PostApiEkbDocAttachmentsResponse = PostApiEkbDocAttachmentsResponses[keyof PostApiEkbDocAttachmentsResponses];

export type PostApiEkbDocAttachmentsUploadData = {
    body?: {
        /**
         * The file to upload
         */
        File: Blob | File;
        /**
         * Optional description of the file
         */
        Description?: string;
        /**
         * Optional reference ID that this file is associated with
         */
        ReferenceId: string;
        /**
         * Optional reference type that this file is associated with
         */
        ReferenceType?: string;
        /**
         * Document type for path generation
         */
        DocType: string;
        /**
         * Transaction type for path generation
         */
        TransType: string;
        /**
         * TypePa for path generation (optional)
         */
        TypePa?: string;
        /**
         * Document DocEntry
         */
        DocumentReferenceId: number;
        /**
         * TabName for path generation (optional)
         */
        TabName: string;
    };
    path?: never;
    query?: never;
    url: '/api/ekb/doc-attachments/upload';
};

export type PostApiEkbDocAttachmentsUploadErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDocAttachmentsUploadError = PostApiEkbDocAttachmentsUploadErrors[keyof PostApiEkbDocAttachmentsUploadErrors];

export type PostApiEkbDocAttachmentsUploadResponses = {
    /**
     * OK
     */
    200: FileUploadResultDto;
};

export type PostApiEkbDocAttachmentsUploadResponse = PostApiEkbDocAttachmentsUploadResponses[keyof PostApiEkbDocAttachmentsUploadResponses];

export type PostApiEkbDocAttachmentsUploadWithContentData = {
    body?: FileUploadInputDto;
    path?: never;
    query?: never;
    url: '/api/ekb/doc-attachments/upload-with-content';
};

export type PostApiEkbDocAttachmentsUploadWithContentErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbDocAttachmentsUploadWithContentError = PostApiEkbDocAttachmentsUploadWithContentErrors[keyof PostApiEkbDocAttachmentsUploadWithContentErrors];

export type PostApiEkbDocAttachmentsUploadWithContentResponses = {
    /**
     * OK
     */
    200: FileUploadResultDto;
};

export type PostApiEkbDocAttachmentsUploadWithContentResponse = PostApiEkbDocAttachmentsUploadWithContentResponses[keyof PostApiEkbDocAttachmentsUploadWithContentResponses];

export type GetApiEkbDocAttachmentsDownloadByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/download/{id}';
};

export type GetApiEkbDocAttachmentsDownloadByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDocAttachmentsDownloadByIdError = GetApiEkbDocAttachmentsDownloadByIdErrors[keyof GetApiEkbDocAttachmentsDownloadByIdErrors];

export type GetApiEkbDocAttachmentsByReferenceByReferenceIdData = {
    body?: never;
    path: {
        referenceId: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/by-reference/{referenceId}';
};

export type GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDocAttachmentsByReferenceByReferenceIdError = GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors[keyof GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors];

export type GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses = {
    /**
     * OK
     */
    200: Array<FileUploadResultDto>;
};

export type GetApiEkbDocAttachmentsByReferenceByReferenceIdResponse = GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses[keyof GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses];

export type DeleteApiEkbDocAttachmentsFileByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/file/{id}';
};

export type DeleteApiEkbDocAttachmentsFileByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbDocAttachmentsFileByIdError = DeleteApiEkbDocAttachmentsFileByIdErrors[keyof DeleteApiEkbDocAttachmentsFileByIdErrors];

export type DeleteApiEkbDocAttachmentsFileByIdResponses = {
    /**
     * OK
     */
    200: boolean;
};

export type DeleteApiEkbDocAttachmentsFileByIdResponse = DeleteApiEkbDocAttachmentsFileByIdResponses[keyof DeleteApiEkbDocAttachmentsFileByIdResponses];

export type GetApiEkbDocAttachmentsInfoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/doc-attachments/info/{id}';
};

export type GetApiEkbDocAttachmentsInfoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbDocAttachmentsInfoByIdError = GetApiEkbDocAttachmentsInfoByIdErrors[keyof GetApiEkbDocAttachmentsInfoByIdErrors];

export type GetApiEkbDocAttachmentsInfoByIdResponses = {
    /**
     * OK
     */
    200: DocAttachmentDto;
};

export type GetApiEkbDocAttachmentsInfoByIdResponse = GetApiEkbDocAttachmentsInfoByIdResponses[keyof GetApiEkbDocAttachmentsInfoByIdResponses];

export type PostApiAccountDynamicClaimsRefreshData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/dynamic-claims/refresh';
};

export type PostApiAccountDynamicClaimsRefreshErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountDynamicClaimsRefreshError = PostApiAccountDynamicClaimsRefreshErrors[keyof PostApiAccountDynamicClaimsRefreshErrors];

export type PostApiAccountDynamicClaimsRefreshResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbExportVesselData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/export-vessel';
};

export type GetApiEkbExportVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselError = GetApiEkbExportVesselErrors[keyof GetApiEkbExportVesselErrors];

export type GetApiEkbExportVesselResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfExportVesselDto;
};

export type GetApiEkbExportVesselResponse = GetApiEkbExportVesselResponses[keyof GetApiEkbExportVesselResponses];

export type PostApiEkbExportVesselData = {
    body?: CreateUpdateExportVesselDto;
    path?: never;
    query?: never;
    url: '/api/ekb/export-vessel';
};

export type PostApiEkbExportVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbExportVesselError = PostApiEkbExportVesselErrors[keyof PostApiEkbExportVesselErrors];

export type PostApiEkbExportVesselResponses = {
    /**
     * OK
     */
    200: ExportVesselDto;
};

export type PostApiEkbExportVesselResponse = PostApiEkbExportVesselResponses[keyof PostApiEkbExportVesselResponses];

export type PostApiEkbExportVesselGenerateNextDocNumData = {
    body?: never;
    path?: never;
    query?: {
        postDate?: string;
    };
    url: '/api/ekb/export-vessel/generate-next-doc-num';
};

export type PostApiEkbExportVesselGenerateNextDocNumErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbExportVesselGenerateNextDocNumError = PostApiEkbExportVesselGenerateNextDocNumErrors[keyof PostApiEkbExportVesselGenerateNextDocNumErrors];

export type PostApiEkbExportVesselGenerateNextDocNumResponses = {
    /**
     * OK
     */
    200: GenerateDocNumDto;
};

export type PostApiEkbExportVesselGenerateNextDocNumResponse = PostApiEkbExportVesselGenerateNextDocNumResponses[keyof PostApiEkbExportVesselGenerateNextDocNumResponses];

export type DeleteApiEkbExportVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel/{id}';
};

export type DeleteApiEkbExportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbExportVesselByIdError = DeleteApiEkbExportVesselByIdErrors[keyof DeleteApiEkbExportVesselByIdErrors];

export type DeleteApiEkbExportVesselByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbExportVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel/{id}';
};

export type GetApiEkbExportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselByIdError = GetApiEkbExportVesselByIdErrors[keyof GetApiEkbExportVesselByIdErrors];

export type GetApiEkbExportVesselByIdResponses = {
    /**
     * OK
     */
    200: ExportVesselDto;
};

export type GetApiEkbExportVesselByIdResponse = GetApiEkbExportVesselByIdResponses[keyof GetApiEkbExportVesselByIdResponses];

export type PutApiEkbExportVesselByIdData = {
    body?: CreateUpdateExportVesselDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel/{id}';
};

export type PutApiEkbExportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbExportVesselByIdError = PutApiEkbExportVesselByIdErrors[keyof PutApiEkbExportVesselByIdErrors];

export type PutApiEkbExportVesselByIdResponses = {
    /**
     * OK
     */
    200: ExportVesselDto;
};

export type PutApiEkbExportVesselByIdResponse = PutApiEkbExportVesselByIdResponses[keyof PutApiEkbExportVesselByIdResponses];

export type PostApiEkbExportVesselFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/export-vessel/filter-list';
};

export type PostApiEkbExportVesselFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbExportVesselFilterListError = PostApiEkbExportVesselFilterListErrors[keyof PostApiEkbExportVesselFilterListErrors];

export type PostApiEkbExportVesselFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfExportVesselProjectionDto;
};

export type PostApiEkbExportVesselFilterListResponse = PostApiEkbExportVesselFilterListResponses[keyof PostApiEkbExportVesselFilterListResponses];

export type GetApiEkbExportVesselByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel/{id}/with-items';
};

export type GetApiEkbExportVesselByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselByIdWithItemsError = GetApiEkbExportVesselByIdWithItemsErrors[keyof GetApiEkbExportVesselByIdWithItemsErrors];

export type GetApiEkbExportVesselByIdWithItemsResponses = {
    /**
     * OK
     */
    200: ExportVesselWithItemsDto;
};

export type GetApiEkbExportVesselByIdWithItemsResponse = GetApiEkbExportVesselByIdWithItemsResponses[keyof GetApiEkbExportVesselByIdWithItemsResponses];

export type GetApiEkbExportVesselBillingData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/export-vessel-billing';
};

export type GetApiEkbExportVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselBillingError = GetApiEkbExportVesselBillingErrors[keyof GetApiEkbExportVesselBillingErrors];

export type GetApiEkbExportVesselBillingResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfExportVesselBillingDto;
};

export type GetApiEkbExportVesselBillingResponse = GetApiEkbExportVesselBillingResponses[keyof GetApiEkbExportVesselBillingResponses];

export type PostApiEkbExportVesselBillingData = {
    body?: CreateUpdateExportVesselBillingDto;
    path?: never;
    query?: never;
    url: '/api/ekb/export-vessel-billing';
};

export type PostApiEkbExportVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbExportVesselBillingError = PostApiEkbExportVesselBillingErrors[keyof PostApiEkbExportVesselBillingErrors];

export type PostApiEkbExportVesselBillingResponses = {
    /**
     * OK
     */
    200: ExportVesselBillingDto;
};

export type PostApiEkbExportVesselBillingResponse = PostApiEkbExportVesselBillingResponses[keyof PostApiEkbExportVesselBillingResponses];

export type PostApiEkbExportVesselBillingFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/export-vessel-billing/filter-list';
};

export type PostApiEkbExportVesselBillingFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbExportVesselBillingFilterListError = PostApiEkbExportVesselBillingFilterListErrors[keyof PostApiEkbExportVesselBillingFilterListErrors];

export type PostApiEkbExportVesselBillingFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfExportVesselBillingDto;
};

export type PostApiEkbExportVesselBillingFilterListResponse = PostApiEkbExportVesselBillingFilterListResponses[keyof PostApiEkbExportVesselBillingFilterListResponses];

export type DeleteApiEkbExportVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel-billing/{id}';
};

export type DeleteApiEkbExportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbExportVesselBillingByIdError = DeleteApiEkbExportVesselBillingByIdErrors[keyof DeleteApiEkbExportVesselBillingByIdErrors];

export type DeleteApiEkbExportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbExportVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel-billing/{id}';
};

export type GetApiEkbExportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselBillingByIdError = GetApiEkbExportVesselBillingByIdErrors[keyof GetApiEkbExportVesselBillingByIdErrors];

export type GetApiEkbExportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: ExportVesselBillingDto;
};

export type GetApiEkbExportVesselBillingByIdResponse = GetApiEkbExportVesselBillingByIdResponses[keyof GetApiEkbExportVesselBillingByIdResponses];

export type PutApiEkbExportVesselBillingByIdData = {
    body?: CreateUpdateExportVesselBillingDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel-billing/{id}';
};

export type PutApiEkbExportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbExportVesselBillingByIdError = PutApiEkbExportVesselBillingByIdErrors[keyof PutApiEkbExportVesselBillingByIdErrors];

export type PutApiEkbExportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: ExportVesselBillingDto;
};

export type PutApiEkbExportVesselBillingByIdResponse = PutApiEkbExportVesselBillingByIdResponses[keyof PutApiEkbExportVesselBillingByIdResponses];

export type GetApiEkbExportVesselBillingByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/export-vessel-billing/{id}/with-items';
};

export type GetApiEkbExportVesselBillingByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselBillingByIdWithItemsError = GetApiEkbExportVesselBillingByIdWithItemsErrors[keyof GetApiEkbExportVesselBillingByIdWithItemsErrors];

export type GetApiEkbExportVesselBillingByIdWithItemsResponses = {
    /**
     * OK
     */
    200: ExportVesselBillingWithItemsDto;
};

export type GetApiEkbExportVesselBillingByIdWithItemsResponse = GetApiEkbExportVesselBillingByIdWithItemsResponses[keyof GetApiEkbExportVesselBillingByIdWithItemsResponses];

export type GetApiEkbExportVesselBillingItemsData = {
    body?: never;
    path?: never;
    query?: {
        docEntry?: number;
    };
    url: '/api/ekb/export-vessel-billing/items';
};

export type GetApiEkbExportVesselBillingItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbExportVesselBillingItemsError = GetApiEkbExportVesselBillingItemsErrors[keyof GetApiEkbExportVesselBillingItemsErrors];

export type GetApiEkbExportVesselBillingItemsResponses = {
    /**
     * OK
     */
    200: Array<BillingItemDto>;
};

export type GetApiEkbExportVesselBillingItemsResponse = GetApiEkbExportVesselBillingItemsResponses[keyof GetApiEkbExportVesselBillingItemsResponses];

export type GetApiEkbFilesDownloadByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/files/download/{id}';
};

export type GetApiEkbFilesDownloadByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbFilesDownloadByIdError = GetApiEkbFilesDownloadByIdErrors[keyof GetApiEkbFilesDownloadByIdErrors];

export type GetApiEkbFilesStreamByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/files/stream/{id}';
};

export type GetApiEkbFilesStreamByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbFilesStreamByIdError = GetApiEkbFilesStreamByIdErrors[keyof GetApiEkbFilesStreamByIdErrors];

export type GetApiEkbFilesStreamByFilePathData = {
    body?: never;
    path: {
        filePath: string;
    };
    query?: never;
    url: '/api/ekb/files/stream/{filePath}';
};

export type GetApiEkbFilesStreamByFilePathErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbFilesStreamByFilePathError = GetApiEkbFilesStreamByFilePathErrors[keyof GetApiEkbFilesStreamByFilePathErrors];

export type GetApiEkbFilesInfoByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/files/info/{id}';
};

export type GetApiEkbFilesInfoByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbFilesInfoByIdError = GetApiEkbFilesInfoByIdErrors[keyof GetApiEkbFilesInfoByIdErrors];

export type GetApiHealthKubernetesData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/health/kubernetes';
};

export type GetApiHealthKubernetesResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbImportVesselData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/import-vessel';
};

export type GetApiEkbImportVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselError = GetApiEkbImportVesselErrors[keyof GetApiEkbImportVesselErrors];

export type GetApiEkbImportVesselResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfImportVesselDto;
};

export type GetApiEkbImportVesselResponse = GetApiEkbImportVesselResponses[keyof GetApiEkbImportVesselResponses];

export type PostApiEkbImportVesselData = {
    body?: CreateUpdateImportVesselDto;
    path?: never;
    query?: never;
    url: '/api/ekb/import-vessel';
};

export type PostApiEkbImportVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbImportVesselError = PostApiEkbImportVesselErrors[keyof PostApiEkbImportVesselErrors];

export type PostApiEkbImportVesselResponses = {
    /**
     * OK
     */
    200: ImportVesselDto;
};

export type PostApiEkbImportVesselResponse = PostApiEkbImportVesselResponses[keyof PostApiEkbImportVesselResponses];

export type PostApiEkbImportVesselGenerateNextDocNumData = {
    body?: never;
    path?: never;
    query?: {
        postDate?: string;
    };
    url: '/api/ekb/import-vessel/generate-next-doc-num';
};

export type PostApiEkbImportVesselGenerateNextDocNumErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbImportVesselGenerateNextDocNumError = PostApiEkbImportVesselGenerateNextDocNumErrors[keyof PostApiEkbImportVesselGenerateNextDocNumErrors];

export type PostApiEkbImportVesselGenerateNextDocNumResponses = {
    /**
     * OK
     */
    200: GenerateDocNumDto;
};

export type PostApiEkbImportVesselGenerateNextDocNumResponse = PostApiEkbImportVesselGenerateNextDocNumResponses[keyof PostApiEkbImportVesselGenerateNextDocNumResponses];

export type DeleteApiEkbImportVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel/{id}';
};

export type DeleteApiEkbImportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbImportVesselByIdError = DeleteApiEkbImportVesselByIdErrors[keyof DeleteApiEkbImportVesselByIdErrors];

export type DeleteApiEkbImportVesselByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbImportVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel/{id}';
};

export type GetApiEkbImportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselByIdError = GetApiEkbImportVesselByIdErrors[keyof GetApiEkbImportVesselByIdErrors];

export type GetApiEkbImportVesselByIdResponses = {
    /**
     * OK
     */
    200: ImportVesselDto;
};

export type GetApiEkbImportVesselByIdResponse = GetApiEkbImportVesselByIdResponses[keyof GetApiEkbImportVesselByIdResponses];

export type PutApiEkbImportVesselByIdData = {
    body?: CreateUpdateImportVesselDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel/{id}';
};

export type PutApiEkbImportVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbImportVesselByIdError = PutApiEkbImportVesselByIdErrors[keyof PutApiEkbImportVesselByIdErrors];

export type PutApiEkbImportVesselByIdResponses = {
    /**
     * OK
     */
    200: ImportVesselDto;
};

export type PutApiEkbImportVesselByIdResponse = PutApiEkbImportVesselByIdResponses[keyof PutApiEkbImportVesselByIdResponses];

export type GetApiEkbImportVesselByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel/{id}/with-items';
};

export type GetApiEkbImportVesselByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselByIdWithItemsError = GetApiEkbImportVesselByIdWithItemsErrors[keyof GetApiEkbImportVesselByIdWithItemsErrors];

export type GetApiEkbImportVesselByIdWithItemsResponses = {
    /**
     * OK
     */
    200: ImportVesselWithItemsDto;
};

export type GetApiEkbImportVesselByIdWithItemsResponse = GetApiEkbImportVesselByIdWithItemsResponses[keyof GetApiEkbImportVesselByIdWithItemsResponses];

export type PostApiEkbImportVesselFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/import-vessel/filter-list';
};

export type PostApiEkbImportVesselFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbImportVesselFilterListError = PostApiEkbImportVesselFilterListErrors[keyof PostApiEkbImportVesselFilterListErrors];

export type PostApiEkbImportVesselFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfImportVesselProjectionDto;
};

export type PostApiEkbImportVesselFilterListResponse = PostApiEkbImportVesselFilterListResponses[keyof PostApiEkbImportVesselFilterListResponses];

export type GetApiEkbImportVesselBillingData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/import-vessel-billing';
};

export type GetApiEkbImportVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselBillingError = GetApiEkbImportVesselBillingErrors[keyof GetApiEkbImportVesselBillingErrors];

export type GetApiEkbImportVesselBillingResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfImportVesselBillingDto;
};

export type GetApiEkbImportVesselBillingResponse = GetApiEkbImportVesselBillingResponses[keyof GetApiEkbImportVesselBillingResponses];

export type PostApiEkbImportVesselBillingData = {
    body?: CreateUpdateImportVesselBillingDto;
    path?: never;
    query?: never;
    url: '/api/ekb/import-vessel-billing';
};

export type PostApiEkbImportVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbImportVesselBillingError = PostApiEkbImportVesselBillingErrors[keyof PostApiEkbImportVesselBillingErrors];

export type PostApiEkbImportVesselBillingResponses = {
    /**
     * OK
     */
    200: ImportVesselBillingDto;
};

export type PostApiEkbImportVesselBillingResponse = PostApiEkbImportVesselBillingResponses[keyof PostApiEkbImportVesselBillingResponses];

export type PostApiEkbImportVesselBillingFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/import-vessel-billing/filter-list';
};

export type PostApiEkbImportVesselBillingFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbImportVesselBillingFilterListError = PostApiEkbImportVesselBillingFilterListErrors[keyof PostApiEkbImportVesselBillingFilterListErrors];

export type PostApiEkbImportVesselBillingFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfImportVesselBillingDto;
};

export type PostApiEkbImportVesselBillingFilterListResponse = PostApiEkbImportVesselBillingFilterListResponses[keyof PostApiEkbImportVesselBillingFilterListResponses];

export type DeleteApiEkbImportVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel-billing/{id}';
};

export type DeleteApiEkbImportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbImportVesselBillingByIdError = DeleteApiEkbImportVesselBillingByIdErrors[keyof DeleteApiEkbImportVesselBillingByIdErrors];

export type DeleteApiEkbImportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbImportVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel-billing/{id}';
};

export type GetApiEkbImportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselBillingByIdError = GetApiEkbImportVesselBillingByIdErrors[keyof GetApiEkbImportVesselBillingByIdErrors];

export type GetApiEkbImportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: ImportVesselBillingDto;
};

export type GetApiEkbImportVesselBillingByIdResponse = GetApiEkbImportVesselBillingByIdResponses[keyof GetApiEkbImportVesselBillingByIdResponses];

export type PutApiEkbImportVesselBillingByIdData = {
    body?: CreateUpdateImportVesselBillingDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel-billing/{id}';
};

export type PutApiEkbImportVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbImportVesselBillingByIdError = PutApiEkbImportVesselBillingByIdErrors[keyof PutApiEkbImportVesselBillingByIdErrors];

export type PutApiEkbImportVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: ImportVesselBillingDto;
};

export type PutApiEkbImportVesselBillingByIdResponse = PutApiEkbImportVesselBillingByIdResponses[keyof PutApiEkbImportVesselBillingByIdResponses];

export type GetApiEkbImportVesselBillingByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/import-vessel-billing/{id}/with-items';
};

export type GetApiEkbImportVesselBillingByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselBillingByIdWithItemsError = GetApiEkbImportVesselBillingByIdWithItemsErrors[keyof GetApiEkbImportVesselBillingByIdWithItemsErrors];

export type GetApiEkbImportVesselBillingByIdWithItemsResponses = {
    /**
     * OK
     */
    200: ImportVesselBillingWithItemsDto;
};

export type GetApiEkbImportVesselBillingByIdWithItemsResponse = GetApiEkbImportVesselBillingByIdWithItemsResponses[keyof GetApiEkbImportVesselBillingByIdWithItemsResponses];

export type GetApiEkbImportVesselBillingItemsData = {
    body?: never;
    path?: never;
    query?: {
        docEntry?: number;
    };
    url: '/api/ekb/import-vessel-billing/items';
};

export type GetApiEkbImportVesselBillingItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbImportVesselBillingItemsError = GetApiEkbImportVesselBillingItemsErrors[keyof GetApiEkbImportVesselBillingItemsErrors];

export type GetApiEkbImportVesselBillingItemsResponses = {
    /**
     * OK
     */
    200: Array<BillingItemDto>;
};

export type GetApiEkbImportVesselBillingItemsResponse = GetApiEkbImportVesselBillingItemsResponses[keyof GetApiEkbImportVesselBillingItemsResponses];

export type GetApiEkbItemClassificationData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/item-classification';
};

export type GetApiEkbItemClassificationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbItemClassificationError = GetApiEkbItemClassificationErrors[keyof GetApiEkbItemClassificationErrors];

export type GetApiEkbItemClassificationResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfItemClassificationDto;
};

export type GetApiEkbItemClassificationResponse = GetApiEkbItemClassificationResponses[keyof GetApiEkbItemClassificationResponses];

export type PostApiEkbItemClassificationData = {
    body?: ItemClassificationCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/item-classification';
};

export type PostApiEkbItemClassificationErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbItemClassificationError = PostApiEkbItemClassificationErrors[keyof PostApiEkbItemClassificationErrors];

export type PostApiEkbItemClassificationResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type PostApiEkbItemClassificationResponse = PostApiEkbItemClassificationResponses[keyof PostApiEkbItemClassificationResponses];

export type DeleteApiEkbItemClassificationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type DeleteApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbItemClassificationByIdError = DeleteApiEkbItemClassificationByIdErrors[keyof DeleteApiEkbItemClassificationByIdErrors];

export type DeleteApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbItemClassificationByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type GetApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbItemClassificationByIdError = GetApiEkbItemClassificationByIdErrors[keyof GetApiEkbItemClassificationByIdErrors];

export type GetApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type GetApiEkbItemClassificationByIdResponse = GetApiEkbItemClassificationByIdResponses[keyof GetApiEkbItemClassificationByIdResponses];

export type PutApiEkbItemClassificationByIdData = {
    body?: ItemClassificationCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/item-classification/{id}';
};

export type PutApiEkbItemClassificationByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbItemClassificationByIdError = PutApiEkbItemClassificationByIdErrors[keyof PutApiEkbItemClassificationByIdErrors];

export type PutApiEkbItemClassificationByIdResponses = {
    /**
     * OK
     */
    200: ItemClassificationDto;
};

export type PutApiEkbItemClassificationByIdResponse = PutApiEkbItemClassificationByIdResponses[keyof PutApiEkbItemClassificationByIdResponses];

export type PostApiEkbItemClassificationFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/item-classification/filter-list';
};

export type PostApiEkbItemClassificationFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbItemClassificationFilterListError = PostApiEkbItemClassificationFilterListErrors[keyof PostApiEkbItemClassificationFilterListErrors];

export type PostApiEkbItemClassificationFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfItemClassificationDto;
};

export type PostApiEkbItemClassificationFilterListResponse = PostApiEkbItemClassificationFilterListResponses[keyof PostApiEkbItemClassificationFilterListResponses];

export type GetApiEkbJettyData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/jetty';
};

export type GetApiEkbJettyErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbJettyError = GetApiEkbJettyErrors[keyof GetApiEkbJettyErrors];

export type GetApiEkbJettyResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyDto;
};

export type GetApiEkbJettyResponse = GetApiEkbJettyResponses[keyof GetApiEkbJettyResponses];

export type PostApiEkbJettyData = {
    body?: JettyCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/jetty';
};

export type PostApiEkbJettyErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbJettyError = PostApiEkbJettyErrors[keyof PostApiEkbJettyErrors];

export type PostApiEkbJettyResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type PostApiEkbJettyResponse = PostApiEkbJettyResponses[keyof PostApiEkbJettyResponses];

export type DeleteApiEkbJettyByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type DeleteApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbJettyByIdError = DeleteApiEkbJettyByIdErrors[keyof DeleteApiEkbJettyByIdErrors];

export type DeleteApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbJettyByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type GetApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbJettyByIdError = GetApiEkbJettyByIdErrors[keyof GetApiEkbJettyByIdErrors];

export type GetApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type GetApiEkbJettyByIdResponse = GetApiEkbJettyByIdResponses[keyof GetApiEkbJettyByIdResponses];

export type PutApiEkbJettyByIdData = {
    body?: JettyCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/jetty/{id}';
};

export type PutApiEkbJettyByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbJettyByIdError = PutApiEkbJettyByIdErrors[keyof PutApiEkbJettyByIdErrors];

export type PutApiEkbJettyByIdResponses = {
    /**
     * OK
     */
    200: JettyDto;
};

export type PutApiEkbJettyByIdResponse = PutApiEkbJettyByIdResponses[keyof PutApiEkbJettyByIdResponses];

export type PostApiEkbJettyFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/jetty/filter-list';
};

export type PostApiEkbJettyFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbJettyFilterListError = PostApiEkbJettyFilterListErrors[keyof PostApiEkbJettyFilterListErrors];

export type PostApiEkbJettyFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfJettyDto;
};

export type PostApiEkbJettyFilterListResponse = PostApiEkbJettyFilterListResponses[keyof PostApiEkbJettyFilterListResponses];

export type GetApiEkbLocalVesselData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/local-vessel';
};

export type GetApiEkbLocalVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselError = GetApiEkbLocalVesselErrors[keyof GetApiEkbLocalVesselErrors];

export type GetApiEkbLocalVesselResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfLocalVesselDto;
};

export type GetApiEkbLocalVesselResponse = GetApiEkbLocalVesselResponses[keyof GetApiEkbLocalVesselResponses];

export type PostApiEkbLocalVesselData = {
    body?: CreateUpdateLocalVesselDto;
    path?: never;
    query?: never;
    url: '/api/ekb/local-vessel';
};

export type PostApiEkbLocalVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbLocalVesselError = PostApiEkbLocalVesselErrors[keyof PostApiEkbLocalVesselErrors];

export type PostApiEkbLocalVesselResponses = {
    /**
     * OK
     */
    200: LocalVesselDto;
};

export type PostApiEkbLocalVesselResponse = PostApiEkbLocalVesselResponses[keyof PostApiEkbLocalVesselResponses];

export type PostApiEkbLocalVesselGenerateNextDocNumData = {
    body?: never;
    path?: never;
    query?: {
        postDate?: string;
    };
    url: '/api/ekb/local-vessel/generate-next-doc-num';
};

export type PostApiEkbLocalVesselGenerateNextDocNumErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbLocalVesselGenerateNextDocNumError = PostApiEkbLocalVesselGenerateNextDocNumErrors[keyof PostApiEkbLocalVesselGenerateNextDocNumErrors];

export type PostApiEkbLocalVesselGenerateNextDocNumResponses = {
    /**
     * OK
     */
    200: GenerateDocNumDto;
};

export type PostApiEkbLocalVesselGenerateNextDocNumResponse = PostApiEkbLocalVesselGenerateNextDocNumResponses[keyof PostApiEkbLocalVesselGenerateNextDocNumResponses];

export type DeleteApiEkbLocalVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel/{id}';
};

export type DeleteApiEkbLocalVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbLocalVesselByIdError = DeleteApiEkbLocalVesselByIdErrors[keyof DeleteApiEkbLocalVesselByIdErrors];

export type DeleteApiEkbLocalVesselByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbLocalVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel/{id}';
};

export type GetApiEkbLocalVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselByIdError = GetApiEkbLocalVesselByIdErrors[keyof GetApiEkbLocalVesselByIdErrors];

export type GetApiEkbLocalVesselByIdResponses = {
    /**
     * OK
     */
    200: LocalVesselDto;
};

export type GetApiEkbLocalVesselByIdResponse = GetApiEkbLocalVesselByIdResponses[keyof GetApiEkbLocalVesselByIdResponses];

export type PutApiEkbLocalVesselByIdData = {
    body?: CreateUpdateLocalVesselDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel/{id}';
};

export type PutApiEkbLocalVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbLocalVesselByIdError = PutApiEkbLocalVesselByIdErrors[keyof PutApiEkbLocalVesselByIdErrors];

export type PutApiEkbLocalVesselByIdResponses = {
    /**
     * OK
     */
    200: LocalVesselDto;
};

export type PutApiEkbLocalVesselByIdResponse = PutApiEkbLocalVesselByIdResponses[keyof PutApiEkbLocalVesselByIdResponses];

export type GetApiEkbLocalVesselByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel/{id}/with-items';
};

export type GetApiEkbLocalVesselByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselByIdWithItemsError = GetApiEkbLocalVesselByIdWithItemsErrors[keyof GetApiEkbLocalVesselByIdWithItemsErrors];

export type GetApiEkbLocalVesselByIdWithItemsResponses = {
    /**
     * OK
     */
    200: LocalVesselWithItemsDto;
};

export type GetApiEkbLocalVesselByIdWithItemsResponse = GetApiEkbLocalVesselByIdWithItemsResponses[keyof GetApiEkbLocalVesselByIdWithItemsResponses];

export type PostApiEkbLocalVesselFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/local-vessel/filter-list';
};

export type PostApiEkbLocalVesselFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbLocalVesselFilterListError = PostApiEkbLocalVesselFilterListErrors[keyof PostApiEkbLocalVesselFilterListErrors];

export type PostApiEkbLocalVesselFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfLocalVesselProjectionDto;
};

export type PostApiEkbLocalVesselFilterListResponse = PostApiEkbLocalVesselFilterListResponses[keyof PostApiEkbLocalVesselFilterListResponses];

export type GetApiEkbLocalVesselBillingData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/local-vessel-billing';
};

export type GetApiEkbLocalVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselBillingError = GetApiEkbLocalVesselBillingErrors[keyof GetApiEkbLocalVesselBillingErrors];

export type GetApiEkbLocalVesselBillingResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfLocalVesselBillingDto;
};

export type GetApiEkbLocalVesselBillingResponse = GetApiEkbLocalVesselBillingResponses[keyof GetApiEkbLocalVesselBillingResponses];

export type PostApiEkbLocalVesselBillingData = {
    body?: CreateUpdateLocalVesselBillingDto;
    path?: never;
    query?: never;
    url: '/api/ekb/local-vessel-billing';
};

export type PostApiEkbLocalVesselBillingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbLocalVesselBillingError = PostApiEkbLocalVesselBillingErrors[keyof PostApiEkbLocalVesselBillingErrors];

export type PostApiEkbLocalVesselBillingResponses = {
    /**
     * OK
     */
    200: LocalVesselBillingDto;
};

export type PostApiEkbLocalVesselBillingResponse = PostApiEkbLocalVesselBillingResponses[keyof PostApiEkbLocalVesselBillingResponses];

export type PostApiEkbLocalVesselBillingFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/local-vessel-billing/filter-list';
};

export type PostApiEkbLocalVesselBillingFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbLocalVesselBillingFilterListError = PostApiEkbLocalVesselBillingFilterListErrors[keyof PostApiEkbLocalVesselBillingFilterListErrors];

export type PostApiEkbLocalVesselBillingFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfLocalVesselBillingDto;
};

export type PostApiEkbLocalVesselBillingFilterListResponse = PostApiEkbLocalVesselBillingFilterListResponses[keyof PostApiEkbLocalVesselBillingFilterListResponses];

export type DeleteApiEkbLocalVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel-billing/{id}';
};

export type DeleteApiEkbLocalVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbLocalVesselBillingByIdError = DeleteApiEkbLocalVesselBillingByIdErrors[keyof DeleteApiEkbLocalVesselBillingByIdErrors];

export type DeleteApiEkbLocalVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbLocalVesselBillingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel-billing/{id}';
};

export type GetApiEkbLocalVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselBillingByIdError = GetApiEkbLocalVesselBillingByIdErrors[keyof GetApiEkbLocalVesselBillingByIdErrors];

export type GetApiEkbLocalVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: LocalVesselBillingDto;
};

export type GetApiEkbLocalVesselBillingByIdResponse = GetApiEkbLocalVesselBillingByIdResponses[keyof GetApiEkbLocalVesselBillingByIdResponses];

export type PutApiEkbLocalVesselBillingByIdData = {
    body?: CreateUpdateLocalVesselBillingDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel-billing/{id}';
};

export type PutApiEkbLocalVesselBillingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbLocalVesselBillingByIdError = PutApiEkbLocalVesselBillingByIdErrors[keyof PutApiEkbLocalVesselBillingByIdErrors];

export type PutApiEkbLocalVesselBillingByIdResponses = {
    /**
     * OK
     */
    200: LocalVesselBillingDto;
};

export type PutApiEkbLocalVesselBillingByIdResponse = PutApiEkbLocalVesselBillingByIdResponses[keyof PutApiEkbLocalVesselBillingByIdResponses];

export type GetApiEkbLocalVesselBillingByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/local-vessel-billing/{id}/with-items';
};

export type GetApiEkbLocalVesselBillingByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselBillingByIdWithItemsError = GetApiEkbLocalVesselBillingByIdWithItemsErrors[keyof GetApiEkbLocalVesselBillingByIdWithItemsErrors];

export type GetApiEkbLocalVesselBillingByIdWithItemsResponses = {
    /**
     * OK
     */
    200: LocalVesselBillingWithItemsDto;
};

export type GetApiEkbLocalVesselBillingByIdWithItemsResponse = GetApiEkbLocalVesselBillingByIdWithItemsResponses[keyof GetApiEkbLocalVesselBillingByIdWithItemsResponses];

export type GetApiEkbLocalVesselBillingItemsData = {
    body?: never;
    path?: never;
    query?: {
        docEntry?: number;
    };
    url: '/api/ekb/local-vessel-billing/items';
};

export type GetApiEkbLocalVesselBillingItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbLocalVesselBillingItemsError = GetApiEkbLocalVesselBillingItemsErrors[keyof GetApiEkbLocalVesselBillingItemsErrors];

export type GetApiEkbLocalVesselBillingItemsResponses = {
    /**
     * OK
     */
    200: Array<BillingItemDto>;
};

export type GetApiEkbLocalVesselBillingItemsResponse = GetApiEkbLocalVesselBillingItemsResponses[keyof GetApiEkbLocalVesselBillingItemsResponses];

export type PostApiAccountLoginData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/login';
};

export type PostApiAccountLoginErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountLoginError = PostApiAccountLoginErrors[keyof PostApiAccountLoginErrors];

export type PostApiAccountLoginResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountLoginResponse = PostApiAccountLoginResponses[keyof PostApiAccountLoginResponses];

export type GetApiAccountLogoutData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/logout';
};

export type GetApiAccountLogoutErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountLogoutError = GetApiAccountLogoutErrors[keyof GetApiAccountLogoutErrors];

export type GetApiAccountLogoutResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type PostApiAccountCheckPasswordData = {
    body?: UserLoginInfo;
    path?: never;
    query?: never;
    url: '/api/account/check-password';
};

export type PostApiAccountCheckPasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountCheckPasswordError = PostApiAccountCheckPasswordErrors[keyof PostApiAccountCheckPasswordErrors];

export type PostApiAccountCheckPasswordResponses = {
    /**
     * OK
     */
    200: AbpLoginResult;
};

export type PostApiAccountCheckPasswordResponse = PostApiAccountCheckPasswordResponses[keyof PostApiAccountCheckPasswordResponses];

export type GetApiEkbPortOfLoadingData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/port-of-loading';
};

export type GetApiEkbPortOfLoadingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortOfLoadingError = GetApiEkbPortOfLoadingErrors[keyof GetApiEkbPortOfLoadingErrors];

export type GetApiEkbPortOfLoadingResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortOfLoadingDto;
};

export type GetApiEkbPortOfLoadingResponse = GetApiEkbPortOfLoadingResponses[keyof GetApiEkbPortOfLoadingResponses];

export type PostApiEkbPortOfLoadingData = {
    body?: PortOfLoadingCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-of-loading';
};

export type PostApiEkbPortOfLoadingErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortOfLoadingError = PostApiEkbPortOfLoadingErrors[keyof PostApiEkbPortOfLoadingErrors];

export type PostApiEkbPortOfLoadingResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type PostApiEkbPortOfLoadingResponse = PostApiEkbPortOfLoadingResponses[keyof PostApiEkbPortOfLoadingResponses];

export type DeleteApiEkbPortOfLoadingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type DeleteApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbPortOfLoadingByIdError = DeleteApiEkbPortOfLoadingByIdErrors[keyof DeleteApiEkbPortOfLoadingByIdErrors];

export type DeleteApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbPortOfLoadingByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type GetApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortOfLoadingByIdError = GetApiEkbPortOfLoadingByIdErrors[keyof GetApiEkbPortOfLoadingByIdErrors];

export type GetApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type GetApiEkbPortOfLoadingByIdResponse = GetApiEkbPortOfLoadingByIdResponses[keyof GetApiEkbPortOfLoadingByIdResponses];

export type PutApiEkbPortOfLoadingByIdData = {
    body?: PortOfLoadingCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-of-loading/{id}';
};

export type PutApiEkbPortOfLoadingByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbPortOfLoadingByIdError = PutApiEkbPortOfLoadingByIdErrors[keyof PutApiEkbPortOfLoadingByIdErrors];

export type PutApiEkbPortOfLoadingByIdResponses = {
    /**
     * OK
     */
    200: PortOfLoadingDto;
};

export type PutApiEkbPortOfLoadingByIdResponse = PutApiEkbPortOfLoadingByIdResponses[keyof PutApiEkbPortOfLoadingByIdResponses];

export type PostApiEkbPortOfLoadingFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-of-loading/filter-list';
};

export type PostApiEkbPortOfLoadingFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortOfLoadingFilterListError = PostApiEkbPortOfLoadingFilterListErrors[keyof PostApiEkbPortOfLoadingFilterListErrors];

export type PostApiEkbPortOfLoadingFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortOfLoadingDto;
};

export type PostApiEkbPortOfLoadingFilterListResponse = PostApiEkbPortOfLoadingFilterListResponses[keyof PostApiEkbPortOfLoadingFilterListResponses];

export type GetApiEkbPortServiceData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/port-service';
};

export type GetApiEkbPortServiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortServiceError = GetApiEkbPortServiceErrors[keyof GetApiEkbPortServiceErrors];

export type GetApiEkbPortServiceResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortServiceDto;
};

export type GetApiEkbPortServiceResponse = GetApiEkbPortServiceResponses[keyof GetApiEkbPortServiceResponses];

export type PostApiEkbPortServiceData = {
    body?: PortServiceCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-service';
};

export type PostApiEkbPortServiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortServiceError = PostApiEkbPortServiceErrors[keyof PostApiEkbPortServiceErrors];

export type PostApiEkbPortServiceResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type PostApiEkbPortServiceResponse = PostApiEkbPortServiceResponses[keyof PostApiEkbPortServiceResponses];

export type DeleteApiEkbPortServiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type DeleteApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbPortServiceByIdError = DeleteApiEkbPortServiceByIdErrors[keyof DeleteApiEkbPortServiceByIdErrors];

export type DeleteApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbPortServiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type GetApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbPortServiceByIdError = GetApiEkbPortServiceByIdErrors[keyof GetApiEkbPortServiceByIdErrors];

export type GetApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type GetApiEkbPortServiceByIdResponse = GetApiEkbPortServiceByIdResponses[keyof GetApiEkbPortServiceByIdResponses];

export type PutApiEkbPortServiceByIdData = {
    body?: PortServiceCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/port-service/{id}';
};

export type PutApiEkbPortServiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbPortServiceByIdError = PutApiEkbPortServiceByIdErrors[keyof PutApiEkbPortServiceByIdErrors];

export type PutApiEkbPortServiceByIdResponses = {
    /**
     * OK
     */
    200: PortServiceDto;
};

export type PutApiEkbPortServiceByIdResponse = PutApiEkbPortServiceByIdResponses[keyof PutApiEkbPortServiceByIdResponses];

export type PostApiEkbPortServiceFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/port-service/filter-list';
};

export type PostApiEkbPortServiceFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbPortServiceFilterListError = PostApiEkbPortServiceFilterListErrors[keyof PostApiEkbPortServiceFilterListErrors];

export type PostApiEkbPortServiceFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfPortServiceDto;
};

export type PostApiEkbPortServiceFilterListResponse = PostApiEkbPortServiceFilterListResponses[keyof PostApiEkbPortServiceFilterListResponses];

export type GetApiAccountMyProfileData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type GetApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiAccountMyProfileError = GetApiAccountMyProfileErrors[keyof GetApiAccountMyProfileErrors];

export type GetApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type GetApiAccountMyProfileResponse = GetApiAccountMyProfileResponses[keyof GetApiAccountMyProfileResponses];

export type PutApiAccountMyProfileData = {
    body?: UpdateProfileDto;
    path?: never;
    query?: never;
    url: '/api/account/my-profile';
};

export type PutApiAccountMyProfileErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiAccountMyProfileError = PutApiAccountMyProfileErrors[keyof PutApiAccountMyProfileErrors];

export type PutApiAccountMyProfileResponses = {
    /**
     * OK
     */
    200: ProfileDto;
};

export type PutApiAccountMyProfileResponse = PutApiAccountMyProfileResponses[keyof PutApiAccountMyProfileResponses];

export type PostApiAccountMyProfileChangePasswordData = {
    body?: ChangePasswordInput;
    path?: never;
    query?: never;
    url: '/api/account/my-profile/change-password';
};

export type PostApiAccountMyProfileChangePasswordErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiAccountMyProfileChangePasswordError = PostApiAccountMyProfileChangePasswordErrors[keyof PostApiAccountMyProfileChangePasswordErrors];

export type PostApiAccountMyProfileChangePasswordResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbSurveyorData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/surveyor';
};

export type GetApiEkbSurveyorErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbSurveyorError = GetApiEkbSurveyorErrors[keyof GetApiEkbSurveyorErrors];

export type GetApiEkbSurveyorResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfSurveyorDto;
};

export type GetApiEkbSurveyorResponse = GetApiEkbSurveyorResponses[keyof GetApiEkbSurveyorResponses];

export type PostApiEkbSurveyorData = {
    body?: SurveyorCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/surveyor';
};

export type PostApiEkbSurveyorErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbSurveyorError = PostApiEkbSurveyorErrors[keyof PostApiEkbSurveyorErrors];

export type PostApiEkbSurveyorResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type PostApiEkbSurveyorResponse = PostApiEkbSurveyorResponses[keyof PostApiEkbSurveyorResponses];

export type DeleteApiEkbSurveyorByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type DeleteApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbSurveyorByIdError = DeleteApiEkbSurveyorByIdErrors[keyof DeleteApiEkbSurveyorByIdErrors];

export type DeleteApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbSurveyorByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type GetApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbSurveyorByIdError = GetApiEkbSurveyorByIdErrors[keyof GetApiEkbSurveyorByIdErrors];

export type GetApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type GetApiEkbSurveyorByIdResponse = GetApiEkbSurveyorByIdResponses[keyof GetApiEkbSurveyorByIdResponses];

export type PutApiEkbSurveyorByIdData = {
    body?: SurveyorCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/surveyor/{id}';
};

export type PutApiEkbSurveyorByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbSurveyorByIdError = PutApiEkbSurveyorByIdErrors[keyof PutApiEkbSurveyorByIdErrors];

export type PutApiEkbSurveyorByIdResponses = {
    /**
     * OK
     */
    200: SurveyorDto;
};

export type PutApiEkbSurveyorByIdResponse = PutApiEkbSurveyorByIdResponses[keyof PutApiEkbSurveyorByIdResponses];

export type PostApiEkbSurveyorFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/surveyor/filter-list';
};

export type PostApiEkbSurveyorFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbSurveyorFilterListError = PostApiEkbSurveyorFilterListErrors[keyof PostApiEkbSurveyorFilterListErrors];

export type PostApiEkbSurveyorFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfSurveyorDto;
};

export type PostApiEkbSurveyorFilterListResponse = PostApiEkbSurveyorFilterListResponses[keyof PostApiEkbSurveyorFilterListResponses];

export type DeleteApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type DeleteApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdError = DeleteApiMultiTenancyTenantsByIdErrors[keyof DeleteApiMultiTenancyTenantsByIdErrors];

export type DeleteApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type GetApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdError = GetApiMultiTenancyTenantsByIdErrors[keyof GetApiMultiTenancyTenantsByIdErrors];

export type GetApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type GetApiMultiTenancyTenantsByIdResponse = GetApiMultiTenancyTenantsByIdResponses[keyof GetApiMultiTenancyTenantsByIdResponses];

export type PutApiMultiTenancyTenantsByIdData = {
    body?: TenantUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}';
};

export type PutApiMultiTenancyTenantsByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdError = PutApiMultiTenancyTenantsByIdErrors[keyof PutApiMultiTenancyTenantsByIdErrors];

export type PutApiMultiTenancyTenantsByIdResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PutApiMultiTenancyTenantsByIdResponse = PutApiMultiTenancyTenantsByIdResponses[keyof PutApiMultiTenancyTenantsByIdResponses];

export type GetApiMultiTenancyTenantsData = {
    body?: never;
    path?: never;
    query?: {
        Filter?: string;
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/multi-tenancy/tenants';
};

export type GetApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsError = GetApiMultiTenancyTenantsErrors[keyof GetApiMultiTenancyTenantsErrors];

export type GetApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTenantDto;
};

export type GetApiMultiTenancyTenantsResponse = GetApiMultiTenancyTenantsResponses[keyof GetApiMultiTenancyTenantsResponses];

export type PostApiMultiTenancyTenantsData = {
    body?: TenantCreateDto;
    path?: never;
    query?: never;
    url: '/api/multi-tenancy/tenants';
};

export type PostApiMultiTenancyTenantsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiMultiTenancyTenantsError = PostApiMultiTenancyTenantsErrors[keyof PostApiMultiTenancyTenantsErrors];

export type PostApiMultiTenancyTenantsResponses = {
    /**
     * OK
     */
    200: TenantDto;
};

export type PostApiMultiTenancyTenantsResponse = PostApiMultiTenancyTenantsResponses[keyof PostApiMultiTenancyTenantsResponses];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringError = DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringError = GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: string;
};

export type GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponse = GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses[keyof GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        defaultConnectionString?: string;
    };
    url: '/api/multi-tenancy/tenants/{id}/default-connection-string';
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringError = PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors[keyof PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors];

export type PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTenantData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/tenant';
};

export type GetApiEkbTenantErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTenantError = GetApiEkbTenantErrors[keyof GetApiEkbTenantErrors];

export type GetApiEkbTenantResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfMasterTenantDto;
};

export type GetApiEkbTenantResponse = GetApiEkbTenantResponses[keyof GetApiEkbTenantResponses];

export type PostApiEkbTenantData = {
    body?: TenantCreateUpdateDto;
    path?: never;
    query?: never;
    url: '/api/ekb/tenant';
};

export type PostApiEkbTenantErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTenantError = PostApiEkbTenantErrors[keyof PostApiEkbTenantErrors];

export type PostApiEkbTenantResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type PostApiEkbTenantResponse = PostApiEkbTenantResponses[keyof PostApiEkbTenantResponses];

export type DeleteApiEkbTenantByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type DeleteApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbTenantByIdError = DeleteApiEkbTenantByIdErrors[keyof DeleteApiEkbTenantByIdErrors];

export type DeleteApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTenantByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type GetApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTenantByIdError = GetApiEkbTenantByIdErrors[keyof GetApiEkbTenantByIdErrors];

export type GetApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type GetApiEkbTenantByIdResponse = GetApiEkbTenantByIdResponses[keyof GetApiEkbTenantByIdResponses];

export type PutApiEkbTenantByIdData = {
    body?: TenantCreateUpdateDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/tenant/{id}';
};

export type PutApiEkbTenantByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbTenantByIdError = PutApiEkbTenantByIdErrors[keyof PutApiEkbTenantByIdErrors];

export type PutApiEkbTenantByIdResponses = {
    /**
     * OK
     */
    200: MasterTenantDto;
};

export type PutApiEkbTenantByIdResponse = PutApiEkbTenantByIdResponses[keyof PutApiEkbTenantByIdResponses];

export type PostApiEkbTenantFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/tenant/filter-list';
};

export type PostApiEkbTenantFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTenantFilterListError = PostApiEkbTenantFilterListErrors[keyof PostApiEkbTenantFilterListErrors];

export type PostApiEkbTenantFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfMasterTenantDto;
};

export type PostApiEkbTenantFilterListResponse = PostApiEkbTenantFilterListResponses[keyof PostApiEkbTenantFilterListResponses];

export type GetApiTestAuthStatusData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/test/auth-status';
};

export type GetApiTestAuthStatusResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiTestTokenInfoData = {
    body?: never;
    path?: never;
    query?: never;
    url: '/api/test/token-info';
};

export type GetApiTestTokenInfoResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTradingInvoiceData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/trading-invoice';
};

export type GetApiEkbTradingInvoiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingInvoiceError = GetApiEkbTradingInvoiceErrors[keyof GetApiEkbTradingInvoiceErrors];

export type GetApiEkbTradingInvoiceResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTradingInvoiceDto;
};

export type GetApiEkbTradingInvoiceResponse = GetApiEkbTradingInvoiceResponses[keyof GetApiEkbTradingInvoiceResponses];

export type PostApiEkbTradingInvoiceData = {
    body?: CreateUpdateTradingInvoiceDto;
    path?: never;
    query?: never;
    url: '/api/ekb/trading-invoice';
};

export type PostApiEkbTradingInvoiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTradingInvoiceError = PostApiEkbTradingInvoiceErrors[keyof PostApiEkbTradingInvoiceErrors];

export type PostApiEkbTradingInvoiceResponses = {
    /**
     * OK
     */
    200: TradingInvoiceDto;
};

export type PostApiEkbTradingInvoiceResponse = PostApiEkbTradingInvoiceResponses[keyof PostApiEkbTradingInvoiceResponses];

export type DeleteApiEkbTradingInvoiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-invoice/{id}';
};

export type DeleteApiEkbTradingInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbTradingInvoiceByIdError = DeleteApiEkbTradingInvoiceByIdErrors[keyof DeleteApiEkbTradingInvoiceByIdErrors];

export type DeleteApiEkbTradingInvoiceByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTradingInvoiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-invoice/{id}';
};

export type GetApiEkbTradingInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingInvoiceByIdError = GetApiEkbTradingInvoiceByIdErrors[keyof GetApiEkbTradingInvoiceByIdErrors];

export type GetApiEkbTradingInvoiceByIdResponses = {
    /**
     * OK
     */
    200: TradingInvoiceDto;
};

export type GetApiEkbTradingInvoiceByIdResponse = GetApiEkbTradingInvoiceByIdResponses[keyof GetApiEkbTradingInvoiceByIdResponses];

export type PutApiEkbTradingInvoiceByIdData = {
    body?: CreateUpdateTradingInvoiceDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-invoice/{id}';
};

export type PutApiEkbTradingInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbTradingInvoiceByIdError = PutApiEkbTradingInvoiceByIdErrors[keyof PutApiEkbTradingInvoiceByIdErrors];

export type PutApiEkbTradingInvoiceByIdResponses = {
    /**
     * OK
     */
    200: TradingInvoiceDto;
};

export type PutApiEkbTradingInvoiceByIdResponse = PutApiEkbTradingInvoiceByIdResponses[keyof PutApiEkbTradingInvoiceByIdResponses];

export type GetApiEkbTradingVesselData = {
    body?: never;
    path?: never;
    query: {
        Page?: number;
        Sort?: Array<SortInfo>;
        'FilterGroup.Operator': LogicalOperator;
        'FilterGroup.Conditions': Array<FilterCondition>;
        SkipCount?: number;
        MaxResultCount?: number;
        Sorting?: string;
    };
    url: '/api/ekb/trading-vessel';
};

export type GetApiEkbTradingVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingVesselError = GetApiEkbTradingVesselErrors[keyof GetApiEkbTradingVesselErrors];

export type GetApiEkbTradingVesselResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTradingVesselDto;
};

export type GetApiEkbTradingVesselResponse = GetApiEkbTradingVesselResponses[keyof GetApiEkbTradingVesselResponses];

export type PostApiEkbTradingVesselData = {
    body?: CreateUpdateTradingVesselDto;
    path?: never;
    query?: never;
    url: '/api/ekb/trading-vessel';
};

export type PostApiEkbTradingVesselErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTradingVesselError = PostApiEkbTradingVesselErrors[keyof PostApiEkbTradingVesselErrors];

export type PostApiEkbTradingVesselResponses = {
    /**
     * OK
     */
    200: TradingVesselDto;
};

export type PostApiEkbTradingVesselResponse = PostApiEkbTradingVesselResponses[keyof PostApiEkbTradingVesselResponses];

export type DeleteApiEkbTradingVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-vessel/{id}';
};

export type DeleteApiEkbTradingVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbTradingVesselByIdError = DeleteApiEkbTradingVesselByIdErrors[keyof DeleteApiEkbTradingVesselByIdErrors];

export type DeleteApiEkbTradingVesselByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbTradingVesselByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-vessel/{id}';
};

export type GetApiEkbTradingVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingVesselByIdError = GetApiEkbTradingVesselByIdErrors[keyof GetApiEkbTradingVesselByIdErrors];

export type GetApiEkbTradingVesselByIdResponses = {
    /**
     * OK
     */
    200: TradingVesselDto;
};

export type GetApiEkbTradingVesselByIdResponse = GetApiEkbTradingVesselByIdResponses[keyof GetApiEkbTradingVesselByIdResponses];

export type PutApiEkbTradingVesselByIdData = {
    body?: CreateUpdateTradingVesselDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-vessel/{id}';
};

export type PutApiEkbTradingVesselByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbTradingVesselByIdError = PutApiEkbTradingVesselByIdErrors[keyof PutApiEkbTradingVesselByIdErrors];

export type PutApiEkbTradingVesselByIdResponses = {
    /**
     * OK
     */
    200: TradingVesselDto;
};

export type PutApiEkbTradingVesselByIdResponse = PutApiEkbTradingVesselByIdResponses[keyof PutApiEkbTradingVesselByIdResponses];

export type GetApiEkbTradingVesselByIdWithItemsData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/trading-vessel/{id}/with-items';
};

export type GetApiEkbTradingVesselByIdWithItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingVesselByIdWithItemsError = GetApiEkbTradingVesselByIdWithItemsErrors[keyof GetApiEkbTradingVesselByIdWithItemsErrors];

export type GetApiEkbTradingVesselByIdWithItemsResponses = {
    /**
     * OK
     */
    200: TradingVesselWithItemsDto;
};

export type GetApiEkbTradingVesselByIdWithItemsResponse = GetApiEkbTradingVesselByIdWithItemsResponses[keyof GetApiEkbTradingVesselByIdWithItemsResponses];

export type GetApiEkbTradingVesselItemsData = {
    body?: never;
    path?: never;
    query?: {
        docEntry?: number;
    };
    url: '/api/ekb/trading-vessel/items';
};

export type GetApiEkbTradingVesselItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbTradingVesselItemsError = GetApiEkbTradingVesselItemsErrors[keyof GetApiEkbTradingVesselItemsErrors];

export type GetApiEkbTradingVesselItemsResponses = {
    /**
     * OK
     */
    200: Array<VesselItemDto>;
};

export type GetApiEkbTradingVesselItemsResponse = GetApiEkbTradingVesselItemsResponses[keyof GetApiEkbTradingVesselItemsResponses];

export type PostApiEkbTradingVesselFilterListData = {
    body?: QueryParametersDto;
    path?: never;
    query?: never;
    url: '/api/ekb/trading-vessel/filter-list';
};

export type PostApiEkbTradingVesselFilterListErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbTradingVesselFilterListError = PostApiEkbTradingVesselFilterListErrors[keyof PostApiEkbTradingVesselFilterListErrors];

export type PostApiEkbTradingVesselFilterListResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfTradingVesselProjectionDto;
};

export type PostApiEkbTradingVesselFilterListResponse = PostApiEkbTradingVesselFilterListResponses[keyof PostApiEkbTradingVesselFilterListResponses];

export type PostApiEkbVesselVesselHeadersData = {
    body?: VesselListRequestDto;
    path?: never;
    query?: never;
    url: '/api/ekb/vessel/vessel-headers';
};

export type PostApiEkbVesselVesselHeadersErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselVesselHeadersError = PostApiEkbVesselVesselHeadersErrors[keyof PostApiEkbVesselVesselHeadersErrors];

export type PostApiEkbVesselVesselHeadersResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfVesselHeaderDto;
};

export type PostApiEkbVesselVesselHeadersResponse = PostApiEkbVesselVesselHeadersResponses[keyof PostApiEkbVesselVesselHeadersResponses];

export type PostApiEkbVesselVesselItemsData = {
    body?: VesselListRequestDto;
    path?: never;
    query?: never;
    url: '/api/ekb/vessel/vessel-items';
};

export type PostApiEkbVesselVesselItemsErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselVesselItemsError = PostApiEkbVesselVesselItemsErrors[keyof PostApiEkbVesselVesselItemsErrors];

export type PostApiEkbVesselVesselItemsResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfVesselItemDto;
};

export type PostApiEkbVesselVesselItemsResponse = PostApiEkbVesselVesselItemsResponses[keyof PostApiEkbVesselVesselItemsResponses];

export type PostApiEkbVesselByIdVesselHeaderData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        vesselType?: string;
    };
    url: '/api/ekb/vessel/{id}/vessel-header';
};

export type PostApiEkbVesselByIdVesselHeaderErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselByIdVesselHeaderError = PostApiEkbVesselByIdVesselHeaderErrors[keyof PostApiEkbVesselByIdVesselHeaderErrors];

export type PostApiEkbVesselByIdVesselHeaderResponses = {
    /**
     * OK
     */
    200: VesselHeaderDto;
};

export type PostApiEkbVesselByIdVesselHeaderResponse = PostApiEkbVesselByIdVesselHeaderResponses[keyof PostApiEkbVesselByIdVesselHeaderResponses];

export type PostApiEkbVesselByIdVesselItemData = {
    body?: never;
    path: {
        id: string;
    };
    query?: {
        vesselType?: string;
    };
    url: '/api/ekb/vessel/{id}/vessel-item';
};

export type PostApiEkbVesselByIdVesselItemErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbVesselByIdVesselItemError = PostApiEkbVesselByIdVesselItemErrors[keyof PostApiEkbVesselByIdVesselItemErrors];

export type PostApiEkbVesselByIdVesselItemResponses = {
    /**
     * OK
     */
    200: VesselItemDto;
};

export type PostApiEkbVesselByIdVesselItemResponse = PostApiEkbVesselByIdVesselItemResponses[keyof PostApiEkbVesselByIdVesselItemResponses];

export type GetApiEkbZoneDetailInvoiceData = {
    body?: never;
    path?: never;
    query?: {
        Sorting?: string;
        SkipCount?: number;
        MaxResultCount?: number;
    };
    url: '/api/ekb/zone-detail-invoice';
};

export type GetApiEkbZoneDetailInvoiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbZoneDetailInvoiceError = GetApiEkbZoneDetailInvoiceErrors[keyof GetApiEkbZoneDetailInvoiceErrors];

export type GetApiEkbZoneDetailInvoiceResponses = {
    /**
     * OK
     */
    200: PagedResultDtoOfZoneDetailInvoiceDto;
};

export type GetApiEkbZoneDetailInvoiceResponse = GetApiEkbZoneDetailInvoiceResponses[keyof GetApiEkbZoneDetailInvoiceResponses];

export type PostApiEkbZoneDetailInvoiceData = {
    body?: CreateUpdateZoneDetailInvoiceDto;
    path?: never;
    query?: never;
    url: '/api/ekb/zone-detail-invoice';
};

export type PostApiEkbZoneDetailInvoiceErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PostApiEkbZoneDetailInvoiceError = PostApiEkbZoneDetailInvoiceErrors[keyof PostApiEkbZoneDetailInvoiceErrors];

export type PostApiEkbZoneDetailInvoiceResponses = {
    /**
     * OK
     */
    200: ZoneDetailInvoiceDto;
};

export type PostApiEkbZoneDetailInvoiceResponse = PostApiEkbZoneDetailInvoiceResponses[keyof PostApiEkbZoneDetailInvoiceResponses];

export type GetApiEkbZoneDetailInvoiceByIdByZoneDetailData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/zone-detail-invoice/{id}/by-zone-detail';
};

export type GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbZoneDetailInvoiceByIdByZoneDetailError = GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors[keyof GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors];

export type GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses = {
    /**
     * OK
     */
    200: Array<ZoneDetailInvoiceDto>;
};

export type GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponse = GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses[keyof GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses];

export type DeleteApiEkbZoneDetailInvoiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/zone-detail-invoice/{id}';
};

export type DeleteApiEkbZoneDetailInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type DeleteApiEkbZoneDetailInvoiceByIdError = DeleteApiEkbZoneDetailInvoiceByIdErrors[keyof DeleteApiEkbZoneDetailInvoiceByIdErrors];

export type DeleteApiEkbZoneDetailInvoiceByIdResponses = {
    /**
     * OK
     */
    200: unknown;
};

export type GetApiEkbZoneDetailInvoiceByIdData = {
    body?: never;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/zone-detail-invoice/{id}';
};

export type GetApiEkbZoneDetailInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type GetApiEkbZoneDetailInvoiceByIdError = GetApiEkbZoneDetailInvoiceByIdErrors[keyof GetApiEkbZoneDetailInvoiceByIdErrors];

export type GetApiEkbZoneDetailInvoiceByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailInvoiceDto;
};

export type GetApiEkbZoneDetailInvoiceByIdResponse = GetApiEkbZoneDetailInvoiceByIdResponses[keyof GetApiEkbZoneDetailInvoiceByIdResponses];

export type PutApiEkbZoneDetailInvoiceByIdData = {
    body?: CreateUpdateZoneDetailInvoiceDto;
    path: {
        id: string;
    };
    query?: never;
    url: '/api/ekb/zone-detail-invoice/{id}';
};

export type PutApiEkbZoneDetailInvoiceByIdErrors = {
    /**
     * Bad Request
     */
    400: RemoteServiceErrorResponse;
    /**
     * Unauthorized
     */
    401: RemoteServiceErrorResponse;
    /**
     * Forbidden
     */
    403: RemoteServiceErrorResponse;
    /**
     * Not Found
     */
    404: RemoteServiceErrorResponse;
    /**
     * Internal Server Error
     */
    500: RemoteServiceErrorResponse;
    /**
     * Not Implemented
     */
    501: RemoteServiceErrorResponse;
};

export type PutApiEkbZoneDetailInvoiceByIdError = PutApiEkbZoneDetailInvoiceByIdErrors[keyof PutApiEkbZoneDetailInvoiceByIdErrors];

export type PutApiEkbZoneDetailInvoiceByIdResponses = {
    /**
     * OK
     */
    200: ZoneDetailInvoiceDto;
};

export type PutApiEkbZoneDetailInvoiceByIdResponse = PutApiEkbZoneDetailInvoiceByIdResponses[keyof PutApiEkbZoneDetailInvoiceByIdResponses];

export type ClientOptions = {
    baseUrl: `${string}://swagger-ekb.json` | (string & {});
};