import { ekbProxyService } from '@/services/ekbProxyService';
import type { QueryParametersDto } from '@/clientEkb/types.gen';
import { MultiSelect } from '@/components/ui/multi-select';
import { useDebounce } from '@/lib/hooks/useDebounce';
import { useQuery } from '@tanstack/react-query';
import React from 'react';

interface AsyncSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const JettySelect: React.FC<AsyncSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select jetty...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  // Query for options based on search
  const { data: options = [], isLoading } = useQuery({
    queryKey: ['jetty-options', debouncedSearchValue],
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 50,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            { fieldName: 'name', operator: 'Contains', value: debouncedSearchValue },
            { fieldName: 'alias', operator: 'Contains', value: debouncedSearchValue },
            { fieldName: 'port', operator: 'Contains', value: debouncedSearchValue },
          ]
        } : undefined
      };
      const res = await ekbProxyService.filterJetties(filterRequest);
      const data = res.data?.items ?? [];
      return data.map(jetty => ({
        value: jetty.id || '',
        label: jetty.name || jetty.alias || 'Unknown Jetty',
        description: jetty.port ? `Port: ${jetty.port}` : undefined,
        data: jetty
      }));
    }
  });

  // Query for selected value if not in options
  const { data: selectedOption } = useQuery({
    queryKey: ['jetty-option', value],
    enabled: !!value && !options.find(option => option.value === value),
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            { fieldName: 'id', operator: 'Equals', value }
          ]
        }
      };
      const res = await ekbProxyService.filterJetties(filterRequest);
      const data = res.data?.items ?? [];
      if (data.length > 0) {
        return {
          value: data[0].id || '',
          label: data[0].name || data[0].alias || 'Unknown Jetty',
          description: data[0].port ? `Port: ${data[0].port}` : undefined,
          data: data[0]
        };
      }
      return undefined;
    }
  });

  // Merge selected option if not present
  const mergedOptions = React.useMemo(() => {
    if (selectedOption && !options.find(o => o.value === selectedOption.value)) {
      return [selectedOption, ...options];
    }
    return options;
  }, [options, selectedOption]);

  const handleChange = (values: string[]) => onValueChange(values[0] || '');

  return (
    <MultiSelect
      options={mergedOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      showDescription={true}
      isLoading={isLoading}
    />
  );
};

export const VesselSelect: React.FC<AsyncSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select vessel...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  const { data: options = [], isLoading } = useQuery({
    queryKey: ['vessel-options', debouncedSearchValue],
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 50,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            { fieldName: 'name', operator: 'Contains', value: debouncedSearchValue },
            { fieldName: 'alias', operator: 'Contains', value: debouncedSearchValue },
          ]
        } : undefined
      };
      const res = await ekbProxyService.filterCargo(filterRequest);
      const data = res.data?.items ?? [];
      return data.map(vessel => ({
        value: vessel.id || '',
        label: vessel.name || vessel.alias || 'Unknown Vessel',
        description: vessel.alias ? `Alias: ${vessel.alias}` : undefined,
        data: vessel
      }));
    }
  });

  const { data: selectedOption } = useQuery({
    queryKey: ['vessel-option', value],
    enabled: !!value && !options.find(option => option.value === value),
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            { fieldName: 'id', operator: 'Equals', value }
          ]
        }
      };
      const res = await ekbProxyService.filterCargo(filterRequest);
      const data = res.data?.items ?? [];
      if (data.length > 0) {
        return {
          value: data[0].id || '',
          label: data[0].name || data[0].alias || 'Unknown Vessel',
          description: data[0].alias ? `Alias: ${data[0].alias}` : undefined,
          data: data[0]
        };
      }
      return undefined;
    }
  });

  const mergedOptions = React.useMemo(() => {
    if (selectedOption && !options.find(o => o.value === selectedOption.value)) {
      return [selectedOption, ...options];
    }
    return options;
  }, [options, selectedOption]);

  const handleChange = (values: string[]) => onValueChange(values[0] || '');

  return (
    <MultiSelect
      options={mergedOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      showDescription={true}
      isLoading={isLoading}
    />
  );
};

export const DestinationPortSelect: React.FC<AsyncSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select destination port...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  const { data: options = [], isLoading } = useQuery({
    queryKey: ['destination-port-options', debouncedSearchValue],
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 50,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            { fieldName: 'name', operator: 'Contains', value: debouncedSearchValue },
          ]
        } : undefined
      };
      const res = await ekbProxyService.filterDestinationPorts(filterRequest);
      const data = res.data?.items ?? [];
      return data.map(port => ({
        value: port.id || '',
        label: port.name || 'Unknown Port',
        description: undefined,
        data: port
      }));
    }
  });

  const { data: selectedOption } = useQuery({
    queryKey: ['destination-port-option', value],
    enabled: !!value && !options.find(option => option.value === value),
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            { fieldName: 'id', operator: 'Equals', value }
          ]
        }
      };
      const res = await ekbProxyService.filterDestinationPorts(filterRequest);
      const data = res.data?.items ?? [];
      if (data.length > 0) {
        return {
          value: data[0].id || '',
          label: data[0].name || 'Unknown Port',
          description: undefined,
          data: data[0]
        };
      }
      return undefined;
    }
  });

  const mergedOptions = React.useMemo(() => {
    if (selectedOption && !options.find(o => o.value === selectedOption.value)) {
      return [selectedOption, ...options];
    }
    return options;
  }, [options, selectedOption]);

  const handleChange = (values: string[]) => onValueChange(values[0] || '');

  return (
    <MultiSelect
      options={mergedOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      showDescription={true}
      isLoading={isLoading}
    />
  );
};

export const AgentSelect: React.FC<AsyncSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select agent...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  const { data: options = [], isLoading } = useQuery({
    queryKey: ['agent-options', debouncedSearchValue],
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 50,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            { fieldName: 'name', operator: 'Contains', value: debouncedSearchValue },
          ]
        } : undefined
      };
      const res = await ekbProxyService.filterAgents(filterRequest);
      const data = res.data?.items ?? [];
      return data.map(agent => ({
        value: agent.id || '',
        label: agent.name || 'Unknown Agent',
        description: undefined,
        data: agent
      }));
    }
  });

  const { data: selectedOption } = useQuery({
    queryKey: ['agent-option', value],
    enabled: !!value && !options.find(option => option.value === value),
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            { fieldName: 'id', operator: 'Equals', value }
          ]
        }
      };
      const res = await ekbProxyService.filterAgents(filterRequest);
      const data = res.data?.items ?? [];
      if (data.length > 0) {
        return {
          value: data[0].id || '',
          label: data[0].name || 'Unknown Agent',
          description: undefined,
          data: data[0]
        };
      }
      return undefined;
    }
  });

  const mergedOptions = React.useMemo(() => {
    if (selectedOption && !options.find(o => o.value === selectedOption.value)) {
      return [selectedOption, ...options];
    }
    return options;
  }, [options, selectedOption]);

  const handleChange = (values: string[]) => onValueChange(values[0] || '');

  return (
    <MultiSelect
      options={mergedOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      showDescription={true}
      isLoading={isLoading}
    />
  );
};

export const PortOfLoadingSelect: React.FC<AsyncSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select port of loading...',
  className,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = React.useState('');
  const debouncedSearchValue = useDebounce(searchValue, 300);

  const { data: options = [], isLoading } = useQuery({
    queryKey: ['port-of-loading-options', debouncedSearchValue],
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 50,
        skipCount: 0,
        filterGroup: debouncedSearchValue ? {
          operator: 'Or',
          conditions: [
            { fieldName: 'name', operator: 'Contains', value: debouncedSearchValue },
          ]
        } : undefined
      };
      const res = await ekbProxyService.filterPortOfLoading(filterRequest);
      const data = res.data?.items ?? [];
      return data.map(port => ({
        value: port.id || '',
        label: port.name || 'Unknown Port',
        description: undefined,
        data: port
      }));
    }
  });

  const { data: selectedOption } = useQuery({
    queryKey: ['port-of-loading-option', value],
    enabled: !!value && !options.find(option => option.value === value),
    queryFn: async () => {
      const filterRequest: QueryParametersDto = {
        maxResultCount: 1,
        skipCount: 0,
        filterGroup: {
          operator: 'And',
          conditions: [
            { fieldName: 'id', operator: 'Equals', value }
          ]
        }
      };
      const res = await ekbProxyService.filterPortOfLoading(filterRequest);
      const data = res.data?.items ?? [];
      if (data.length > 0) {
        return {
          value: data[0].id || '',
          label: data[0].name || 'Unknown Port',
          description: undefined,
          data: data[0]
        };
      }
      return undefined;
    }
  });

  const mergedOptions = React.useMemo(() => {
    if (selectedOption && !options.find(o => o.value === selectedOption.value)) {
      return [selectedOption, ...options];
    }
    return options;
  }, [options, selectedOption]);

  const handleChange = (values: string[]) => onValueChange(values[0] || '');

  return (
    <MultiSelect
      options={mergedOptions}
      value={value ? [value] : []}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      mode="single"
      searchValue={searchValue}
      onSearchValueChange={setSearchValue}
      showDescription={true}
      isLoading={isLoading}
    />
  );
}; 