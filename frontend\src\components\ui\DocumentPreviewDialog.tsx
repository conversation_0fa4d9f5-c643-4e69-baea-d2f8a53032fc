import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';

interface DocumentPreviewDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  documentSrc: string;
}

export const DocumentPreviewDialog: React.FC<DocumentPreviewDialogProps> = ({ isOpen, onOpenChange, documentSrc }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-[800px] w-auto h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Preview Document</DialogTitle>
        </DialogHeader>
        <div className="flex-grow overflow-auto p-4">
          <iframe src={documentSrc} title="Document Preview" className="w-full h-full border-none"></iframe>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 