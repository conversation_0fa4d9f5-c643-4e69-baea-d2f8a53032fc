import { ekbProxyService } from '@/services/ekbProxyService';
import type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { LocalVesselFormWithData } from '@/components/jetty/vessel/local/local-vessel-form';
import type { LocalVesselHeaderForm } from '@/components/jetty/vessel/local/local-vessel-header-schema';
import type { LocalVesselItemForm } from '@/components/jetty/vessel/local/local-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { Head, router } from '@inertiajs/react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const LocalVesselCreatePage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: LocalVesselHeaderForm; items: LocalVesselItemForm[] }) => {
      const response = await ekbProxyService.createLocalVessel({
        ...header,
        docType: 'Local',
        vesselId: header.vesselId ? String(header.vesselId) : '',
        jettyId: header.jettyId ? String(header.jettyId) : '',
        concurrencyStamp: header.concurrencyStamp ? String(header.concurrencyStamp) : '',
        items: items.map(item => ({
          ...item,
          createdBy: '',
          docType: '',
          isScan: '',
          isOriginal: '',
          isActive: true,
          isDeleted: false,
          isSend: '',
          isFeOri: '',
          isFeSend: '',
          isChange: '',
          isFeChange: '',
          isFeActive: '',
          deleted: '',
          isUrgent: '',
          tenantId: item.tenantId || '',
          businessPartnerId: item.businessPartnerId || '',
          concurrencyStamp: item.concurrencyStamp || '',
        }))
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: (data) => {
      toast({ title: 'Success', description: 'Local vessel created.', variant: 'success' });
      if (data && data.id) {
        router.visit(`/local/edit/${data.id}`);
      }
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  return (
    <LocalVesselFormWithData
      mode="create"
      title={t('pages.vessel.create.local')}
      initialHeader={{}}
      initialItems={[]}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
      queryClient={queryClient}
      jettyList={[]}
    />
  );
};

export default function LocalVesselCreate() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.create.local')} />
      <LocalVesselCreatePage />
    </AppLayout>
  );
} 