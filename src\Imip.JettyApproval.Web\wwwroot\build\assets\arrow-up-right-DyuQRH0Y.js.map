{"version": 3, "file": "arrow-up-right-DyuQRH0Y.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/arrow-up-right.js"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M7 7h10v10\", key: \"1tivn9\" }],\n  [\"path\", { d: \"M7 17 17 7\", key: \"1vkiza\" }]\n];\nconst ArrowUpRight = createLucideIcon(\"arrow-up-right\", __iconNode);\n\nexport { __iconNode, ArrowUpRight as default };\n//# sourceMappingURL=arrow-up-right.js.map\n"], "names": ["__iconNode", "ArrowUpRight", "createLucideIcon"], "mappings": "6CAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAQ,CAAE,EAC3C,CAAC,OAAQ,CAAE,EAAG,aAAc,IAAK,QAAU,CAAA,CAC7C,EACMC,EAAeC,EAAiB,iBAAkBF,CAAU", "x_google_ignoreList": [0]}