import{k as n,l as s}from"./app-layout-rNt37hVL.js";import{j as a}from"./vendor-6tJeyfYI.js";import{R as r,L as o,T as i,C as d}from"./radix-e4nK4mWk.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],f=n("trash-2",c);function b({className:t,...e}){return a.jsx(r,{"data-slot":"tabs",className:s("flex flex-col gap-2",t),...e})}function g({className:t,...e}){return a.jsx(o,{"data-slot":"tabs-list",className:s("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...e})}function p({className:t,...e}){return a.jsx(i,{"data-slot":"tabs-trigger",className:s("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...e})}function m({className:t,...e}){return a.jsx(d,{"data-slot":"tabs-content",className:s("flex-1 outline-none",t),...e})}export{b as T,g as a,p as b,m as c,f as d};
//# sourceMappingURL=tabs-Dk-TLCdA.js.map
