{"version": 3, "mappings": ";8rBAYO,MAAMA,GAAgB,IACpBC,EAAgF,CACrF,WAAY,MAAO,CAAE,WAAAC,EAAY,YAAAC,EAAa,UAAAC,EAAY,EAAG,eAAAC,EAAiB,MAAS,CASrF,MAAMC,EAAM,MAAMC,EAAuB,CAAE,KAPE,CAC3C,WAAAL,EACA,YAAAC,EACA,UAAAC,EACA,eAAAC,CACF,EAEgE,EAC5D,OAACC,GAAK,MAAM,MAITA,EAAI,MAHTE,EAAM,CAAE,MAAO,mBAAoB,QAAS,cAAe,EACpD,CAAE,MAAO,GAAI,WAAY,CAAE,EAGtC,EACA,QAAUC,GAAmB,CAErBD,EAAA,CACJ,MAAO,4BACP,YAAaC,aAAiB,MAAQA,EAAM,QAAU,yBACtD,QAAS,cACV,EACH,CACD,ECpBGC,GAAgB,CACpB,CAAE,MAAO,SAAU,MAAO,iBAAkB,EAC5C,CAAE,MAAO,aAAc,MAAO,aAAc,EAC5C,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,UAAW,MAAO,cAAe,EAC1C,CAAE,MAAO,YAAa,MAAO,gBAAiB,CAChD,EAEMC,GAA+D,CACnE,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,WAAY,MAAO,UAAW,EACvC,CAAE,MAAO,YAAa,MAAO,YAAa,EAC1C,CAAE,MAAO,cAAe,MAAO,GAAI,EACnC,CAAE,MAAO,WAAY,MAAO,GAAI,CAClC,EAEMC,GAAiD,CAAC,CACtD,WAAAV,EACA,eAAAW,EACA,eAAAC,EACA,QAAAC,EACA,KAAAC,EACA,aAAAC,CACF,IAAM,CAEJ,MAAMC,EAAe,OAAOF,GAAS,WAAa,OAAOC,GAAiB,WACpE,CAACE,EAAoBC,CAAqB,EAAIC,WAAS,EAAK,EAC5DC,EAAeJ,EAAeF,EAAQG,EACtCI,EAAkBL,EAAeD,EAAgBG,EACjD,CAACI,EAASC,CAAU,EAAIJ,WAA4B,EAAE,EACtD,CAACK,EAAOC,CAAQ,EAAIN,WAAwD,EAAE,EAC9E,CAACO,EAAkBC,CAAmB,EAAIR,WAAwB,IAAI,EAGtElB,EAAuC2B,UAAQ,IAAM,CACrD,GAACN,EAAQ,OACN,OACL,SAAU,MACV,WAAYA,CACd,GACC,CAACA,CAAO,CAAC,EAGN,CAAE,OAAAO,EAAQ,KAAMC,EAAgB,UAAWC,EAAW,MAAAxB,GAAUT,GAAc,EAGpFkC,YAAU,IAAM,CACVhC,GACK6B,EAAA,CAAE,WAAA7B,EAAY,YAAAC,EAAa,CAEnC,GAACD,EAAYC,EAAa4B,CAAM,CAAC,EAG9B,MAAAI,EAAUH,GAAgB,OAAS,CAAC,EACpCI,EAAaJ,GAAgB,YAAc,EAG3CK,EAAqB,IAAM,CAC3BT,IAAqB,MAAQO,EAAQP,CAAgB,EACxCd,EAAAqB,EAAQP,CAAgB,CAAC,EAExCd,EAAe,IAAI,EAErBS,EAAgB,EAAK,CACvB,EAGMe,EAAmBC,GAAkB,CACzCV,EAAoBU,CAAK,CAC3B,EAEMC,EACHC,EAAA,IAAAC,EAAA,CAAO,QAAQ,UAAU,UAAU,mCACjC,SAAA7B,EAAiBA,EAAe,WAAa,eAChD,GAGF,OACG8B,OAAAC,GAAA,CAAO,KAAMtB,EAAc,aAAcC,EAEvC,UAAAR,IAAY,MACV0B,EAAA,IAAAI,GAAA,CAAc,QAAO,GACnB,YAAWL,EACd,EAEFG,OAACG,GAAc,WAAU,mDACvB,UAACL,EAAA,IAAAM,GAAA,CACC,gBAACC,GAAY,4BAAe9C,GAAc,IAAIA,CAAU,KAAI,CAC9D,GAEC+B,EACCQ,EAAA,IAACQ,GAAA,CACC,SAAU,GACV,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,GACd,EACExC,EACDgC,EAAA,WAAI,UAAU,yDAAyD,gCAExE,GACEN,EAAQ,SAAW,EACpBM,MAAA,OAAI,UAAU,8DACZ,SAAAvC,EAAa,wBAAwBA,CAAU,GAAK,mCACvD,GAGEyC,EAAA,KAAAO,EAAA,oBAAAT,EAAA,IAACU,GAAA,CACC,aAAczC,GACd,UAAWC,GACX,QAAAa,EACA,MAAAE,EACA,gBAAiBD,EACjB,cAAeE,CAAA,CACjB,EACAc,EAAA,IAAC,OAAI,UAAU,4BACb,eAAC,MAAI,WAAU,2CACb,SAAAE,EAAA,KAACS,GACC,WAAAX,MAACY,GACC,UAAAV,OAACW,EAAS,WAAU,gDAClB,UAACb,EAAA,IAAAc,EAAA,CAAU,UAAU,iDAAiD,SAEtE,WACCd,EAAA,IAAAc,EAAA,CAAU,UAAU,4CAA4C,SAEjE,oBACCd,EAAA,IAAAc,EAAA,CAAU,UAAU,4CAA4C,SAEjE,gBACCd,EAAA,IAAAc,EAAA,CAAU,UAAU,4CAA4C,SAEjE,WACCd,EAAA,IAAAc,EAAA,CAAU,UAAU,4CAA4C,SAEjE,iBACCd,EAAA,IAAAc,EAAA,CAAU,UAAU,4CAA4C,SAEjE,oBACF,CACF,SACCC,GACE,UAAArB,EAAQ,IAAI,CAACsB,EAAQlB,IACpBI,EAAA,KAACW,EAAA,CAEC,UAAU,2EACV,QAAS,IAAMhB,EAAgBC,CAAK,EAEpC,UAACE,MAAAiB,EAAA,CAAU,UAAU,wEACnB,SAAAjB,EAAA,IAAC,SACC,KAAK,QACL,KAAK,mBACL,QAASb,IAAqBW,EAC9B,SAAU,IAAMD,EAAgBC,CAAK,EACrC,UAAU,gLAEd,QACCmB,EAAU,WAAU,mEAClB,SAAAD,EAAO,UAAY,IACtB,QACCC,EAAU,WAAU,mEAClB,SAAAD,EAAO,YAAc,IACxB,QACCC,EAAU,WAAU,mEAClB,SAAAD,EAAO,QAAU,IACpB,EACChB,EAAA,IAAAiB,EAAA,CAAU,UAAU,mEAClB,SAAOD,EAAA,cAAgB,IAAI,KAAKA,EAAO,aAAa,EAAE,qBAAuB,GAChF,GACChB,MAAAiB,EAAA,CAAU,UAAU,mEAClB,SAAOD,EAAA,gBAAkB,IAAI,KAAKA,EAAO,eAAe,EAAE,qBAAuB,GACpF,KA3BKA,EAAO,IAAMlB,CAAA,CA6BrB,CACH,GACF,EACF,GACF,EACAI,OAAC,MAAI,WAAU,yCACb,UAACF,EAAA,WAAI,UAAU,gCACZ,SAAaL,EAAA,GAAK,WAAWD,EAAQ,MAAM,OAAOC,CAAU,WAC/D,QACCM,EAAO,SAASL,EAAoB,SAAUT,IAAqB,KAAM,SAE1E,kBACF,GACF,GAEJ,IACF,CAEJ,EC/LM+B,GAA0C,CAAC,CAC/C,MAAAC,EACA,cAAAC,EACA,YAAAC,EAAc,kBACd,UAAAC,EACA,SAAAC,EAAW,EACb,IAAM,CACJ,KAAM,CAACC,EAAaC,CAAc,EAAIC,EAAM,SAAS,EAAE,EACjD,CAACC,EAAcC,CAAe,EAAIF,EAAM,SAA8B,EAAE,EAGxEG,EAAuBC,EAAYN,EAAa,GAAG,EAGnD,CAAE,OAAQO,EAAgB,UAAWvC,CAAA,EAAcwC,GAAuB,EAGhFN,EAAM,UAAU,IAAM,CA0BpBK,EAzBwC,CACtC,eAAgB,GAChB,UAAW,EACX,YAAaF,EAAuB,CAClC,SAAU,KACV,WAAY,CACV,CACE,UAAW,OACX,SAAU,WACV,MAAOA,CACT,EACA,CACE,UAAW,QACX,SAAU,WACV,MAAOA,CACT,EACA,CACE,UAAW,OACX,SAAU,WACV,MAAOA,CAAA,CACT,CACF,EACE,MACN,EAE8B,CAC5B,UAAYI,GAAS,CACb,MAAAC,EAA+BD,EAAK,IAAcE,IAAA,CACtD,MAAOA,EAAM,IAAM,GACnB,MAAOA,EAAM,MAAQA,EAAM,OAAS,gBACpC,YAAaA,EAAM,KAAO,SAASA,EAAM,IAAI,GAAK,OAClD,KAAMA,CAAA,EACN,EACFP,EAAgBM,CAAO,EACzB,CACD,GACA,CAACL,EAAsBE,CAAc,CAAC,EAGzCL,EAAM,UAAU,IAAM,CAChBP,GAAS,CAACQ,EAAa,QAAeS,EAAO,QAAUjB,CAAK,GAiB9DY,EAfwC,CACtC,eAAgB,EAChB,UAAW,EACX,YAAa,CACX,SAAU,MACV,WAAY,CACV,CACE,UAAW,KACX,SAAU,SACV,MAAAZ,CAAA,CACF,CACF,CAEJ,EAE8B,CAC5B,UAAYc,GAAS,CACf,GAAAA,EAAK,OAAS,EAAG,CACnB,MAAMI,EAA+B,CACnC,MAAOJ,EAAK,CAAC,EAAE,IAAM,GACrB,MAAOA,EAAK,CAAC,EAAE,MAAQA,EAAK,CAAC,EAAE,OAAS,gBACxC,YAAaA,EAAK,CAAC,EAAE,KAAO,SAASA,EAAK,CAAC,EAAE,IAAI,GAAK,OACtD,KAAMA,EAAK,CAAC,CACd,EAGAL,EAAwBU,GACPA,EAAK,QAAeF,EAAO,QAAUC,EAAU,KAAK,EAK5DC,EAFE,CAACD,EAAW,GAAGC,CAAI,CAG7B,EACH,CACF,CACD,CAEF,GAACnB,EAAOQ,EAAcI,CAAc,CAAC,EAElC,MAAAQ,EAAgBC,GAAqB,CAEnC,MAAAC,EAAWD,EAAO,CAAC,GAAK,GAE9BpB,EAAcqB,CAAQ,CACxB,EAGAf,SAAM,UAAU,IAAM,GAEnB,CAACP,CAAK,CAAC,EAGRnB,EAAA,IAAC0C,EAAA,CACC,QAASf,EACT,MAAOR,EAAQ,CAACA,CAAK,EAAI,CAAC,EAC1B,SAAUoB,EACV,YAAAlB,EACA,UAAAC,EACA,SAAAC,EACA,KAAK,SACL,YAAAC,EACA,oBAAqBC,EACrB,UAAAjC,EACA,YAAY,qBACZ,UAAU,mBACV,gBAAiB,GACnB,CAEJ,EAEMmD,GAAkD,CAAC,CACvD,OAAAC,EACA,WAAAnF,EACA,OAAAuD,EACA,OAAA6B,EACA,MAAAV,EACA,YAAAW,EACA,cAAAC,EACA,UAAAC,EACA,WAAAC,EACA,SAAAC,EACA,WAAAC,EACA,gBAAAC,EACA,MAAAC,EACA,eAAAC,EACA,mBAAAC,EACA,eAAAC,EACA,eAAAC,EACA,cAAAC,EACA,oBAAAC,EACA,sBAAAC,EACA,kBAAAC,EACA,mBAAAC,EACA,iBAAAC,EACA,mBAAAC,EACA,wBAAAC,EACA,cAAAC,EACA,OAAAC,EAAS,CAAC,EACV,SAAAC,EACA,MAAAC,EAAQ,oBACV,IAAM,CAEE,MAAAC,EAAsBlG,GAA2C,CAErEoF,EAAepF,CAAc,EAGzBA,GAAgB,OAAO,GAEXsF,EAAAtF,EAAe,MAAM,EAAE,EAIrCsF,EAAc,EAAE,CAEpB,EAEA,OAEIxD,EAAA,KAAAO,WAAA,WAACP,OAAA,OAAI,UAAU,OACb,UAACF,EAAA,UAAG,UAAU,kDAAmD,SAAMqE,EAAA,EACvErE,MAAC,MAAI,WAAU,uCAAwC,IACzD,EACAE,OAAC,MAAI,WAAU,4DACb,UAAAA,OAACqE,EACC,WAAArE,EAAA,KAACsE,EAAU,OAAM,SAAS,WAAW,QAClC,UACCJ,EAAApE,EAAA,IAACyE,GAAM,GAAG,SAAU,GAAGL,EAAS,QAAQ,EAAG,EAE1CpE,EAAA,IAAAyE,EAAA,CAAM,GAAG,SAAS,MAAO7B,EAAQ,SAAW8B,GAAMpB,EAAeoB,EAAE,OAAO,KAAK,CAAG,GAEpFP,EAAO,QACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,OAAO,OAAkB,IAE5E,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,UAAAtE,EAAA,KAACyE,EAAO,OAAOlH,EAAY,cAAe8F,EACxC,UAAAvD,MAAC4E,GAAc,UAAU,SACvB,eAACC,EAAY,aAAY,qBAAqB,CAChD,UACCC,EACC,WAAC9E,EAAA,IAAA+E,EAAA,CAAW,MAAM,SAAS,SAAM,WAChC/E,EAAA,IAAA+E,EAAA,CAAW,MAAM,SAAS,SAAM,WAChC/E,EAAA,IAAA+E,EAAA,CAAW,MAAM,UAAU,SAAQ,aACnC/E,EAAA,IAAA+E,EAAA,CAAW,MAAM,WAAW,SAAS,cACxC,IACF,EACCZ,EAAO,YACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,WAAW,OAAkB,IAEhF,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,SAAS,WAAW,QACnC,UAAAxE,EAAA,IAACgF,GAAA,CACC,WAAAvH,EACA,eAAgBuD,EAChB,eAAgBsD,CAAA,CAClB,EACCH,EAAO,YACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,WAAW,OAAkB,IAEhF,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,SAAS,WAAW,QAClC,UACCJ,EAAApE,EAAA,IAACyE,GAAM,GAAG,SAAU,GAAGL,EAAS,QAAQ,EAAG,EAE1CpE,EAAA,IAAAyE,EAAA,CAAM,GAAG,SAAS,MAAO5B,EAAQ,SAAW6B,GAAMjB,EAAeiB,EAAE,OAAO,KAAK,CAAG,GAEpFP,EAAO,QACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,OAAO,OAAkB,IAE5E,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,QAAQ,WAAW,QAClC,UAAAxE,EAAA,IAACkB,GAAA,CACC,MAAOiB,EACP,cAAeuB,EACf,YAAY,eACd,EACCS,EAAO,OACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,MAAM,OAAkB,GAE3E,IACF,SAECI,EACC,WAAArE,EAAA,KAACsE,EAAU,OAAM,eAAe,WAAW,QACxC,UACCJ,EAAApE,MAACyE,EAAM,IAAG,cAAc,KAAK,iBAAkB,GAAGL,EAAS,aAAa,EAAG,EAE3EpE,EAAA,IAACyE,GAAM,GAAG,cAAc,KAAK,iBAAiB,MAAO3B,EAAa,SAAU4B,GAAKf,EAAoBe,EAAE,OAAO,KAAK,CAAG,GAEvHP,EAAO,aACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,YAAY,OAAkB,IAEjF,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,iBAAiB,WAAW,QAC1C,UACCJ,EAAApE,MAACyE,EAAM,IAAG,gBAAgB,KAAK,iBAAkB,GAAGL,EAAS,eAAe,EAAG,EAE/EpE,EAAA,IAACyE,GAAM,GAAG,gBAAgB,KAAK,iBAAiB,MAAO1B,EAAe,SAAU2B,GAAKd,EAAsBc,EAAE,OAAO,KAAK,CAAG,GAE7HP,EAAO,eACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,cAAc,OAAkB,IAEnF,EACAnE,MAACwE,GAAU,MAAM,cAAc,WAAW,QACxC,SAAAxE,EAAA,IAACyE,GAAM,GAAG,YAAY,KAAK,iBAAiB,MAAOzB,EAAW,SAAU0B,GAAKb,EAAkBa,EAAE,OAAO,KAAK,EAAG,CAClH,GACA1E,MAACwE,GAAU,MAAM,eAAe,WAAW,QACzC,SAAAxE,EAAA,IAACyE,GAAM,GAAG,aAAa,KAAK,iBAAiB,MAAOxB,EAAY,SAAUyB,GAAKZ,EAAmBY,EAAE,OAAO,KAAK,EAAG,CACrH,GACA1E,MAACwE,GAAU,MAAM,eAAe,WAAW,QACzC,SAAAxE,MAACyE,GAAM,GAAG,cAAc,KAAK,OAAO,MAAOvB,EAAU,SAAUwB,GAAKX,EAAiBW,EAAE,OAAO,KAAK,CAAG,EACxG,IACF,SAECH,EACC,WAAArE,EAAA,KAACsE,EAAU,OAAM,cAAc,WAAW,QACvC,UACCJ,EAAApE,EAAA,IAACyE,GAAM,GAAG,aAAc,GAAGL,EAAS,YAAY,EAAG,EAElDpE,EAAA,IAAAyE,EAAA,CAAM,GAAG,aAAa,MAAOtB,EAAY,SAAWuB,GAAMV,EAAmBU,EAAE,OAAO,KAAK,CAAG,GAEhGP,EAAO,YACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,WAAW,OAAkB,IAEhF,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,mBAAmB,WAAW,QAC5C,UACCJ,EAAApE,EAAA,IAACyE,GAAM,GAAG,kBAAmB,GAAGL,EAAS,iBAAiB,EAAG,EAE5DpE,EAAA,IAAAyE,EAAA,CAAM,GAAG,kBAAkB,MAAOrB,EAAiB,SAAWsB,GAAMT,EAAwBS,EAAE,OAAO,KAAK,CAAG,GAE/GP,EAAO,iBACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,gBAAgB,OAAkB,IAErF,EACCjE,EAAA,KAAAsE,EAAA,CAAU,MAAM,QAAQ,WAAW,QACjC,UACCJ,EAAApE,EAAA,IAACyE,GAAM,GAAG,QAAS,GAAGL,EAAS,OAAO,EAAG,EAExCpE,EAAA,IAAAyE,EAAA,CAAM,GAAG,QAAQ,MAAOpB,EAAO,SAAWqB,GAAMR,EAAcQ,EAAE,OAAO,KAAK,CAAG,GAEjFP,EAAO,OACLnE,MAAA,QAAK,UAAU,uBAAwB,SAAAmE,EAAO,MAAM,OAAkB,GAE3E,GACF,GACF,IACF,CAEJ,EC1UO,SAASc,GAAmBC,EAAoC,CACjE,IAACA,EAAmB,SAClB,MAAAC,EAAO,IAAI,KAAKD,CAAU,EAChC,GAAI,MAAMC,EAAK,QAAS,GAAU,SAE5B,MAAAC,EAAOC,GAAcA,EAAE,WAAW,SAAS,EAAG,GAAG,EACvD,MAAO,GAAGF,EAAK,YAAY,CAAC,IAAIC,EAAID,EAAK,WAAa,CAAC,CAAC,IAAIC,EAAID,EAAK,QAAQ,CAAC,CAAC,IAAIC,EAAID,EAAK,SAAS,CAAC,CAAC,IAAIC,EAAID,EAAK,WAAW,CAAC,CAAC,EACnI,CCRO,MAAMG,GAAU,CACrB,CAAE,KAAM,aAAc,KAAM,OAAQ,MAAO,SAAU,MAAO,GAAI,EAChE,CAAE,KAAM,WAAY,KAAM,OAAQ,MAAO,YAAa,MAAO,IAAK,SAAU,EAAM,EAClF,CAAE,KAAM,WAAY,KAAM,UAAW,MAAO,WAAY,MAAO,EAAG,EAClE,CAAE,KAAM,MAAO,KAAM,OAAQ,MAAO,MAAO,MAAO,GAAI,EACtD,CAAE,KAAM,SAAU,KAAM,OAAQ,MAAO,SAAU,MAAO,GAAI,EAC5D,CAAE,KAAM,SAAU,KAAM,OAAQ,MAAO,SAAU,MAAO,GAAI,SAAU,EAAK,EAC3E,CAAE,KAAM,WAAY,KAAM,OAAQ,MAAO,YAAa,MAAO,GAAI,EACjE,CACE,KAAM,aACN,KAAM,OACN,MAAO,cACP,WAAY,aACZ,MAAO,IACP,cAAe,GACf,iBAAkB,CAChB,SAAU,EACV,eAAgB,GAChB,eAAgB,EAEpB,EACA,CAAE,KAAM,KAAM,MAAO,UAAW,MAAO,GAAI,SAAU,GAAM,WAAY,EAAM,EAC7E,CAAE,KAAM,SAAU,MAAO,SAAU,MAAO,GAAI,SAAU,GAAM,WAAY,EAAM,EAChF,CAAE,KAAM,SAAU,MAAO,SAAU,MAAO,GAAI,SAAU,GAAM,WAAY,EAAM,CAClF,ECfA,eAAeC,GACbC,EACAC,EACAC,EACA,CACI,IACFA,EAAW,EAAI,EAOT,MAAAC,EAAW,MAAMC,EAAuE,CAC5F,KAN8C,CAC9C,mBAAAJ,EACA,YAAa,EACf,CAGQ,CACP,EAED,GAAIG,EAAS,KAAM,CAEjB,MAAME,EAASF,EAAS,KACpBE,EAAO,UAETJ,EAAUI,EAAO,SAAS,EAG1B,MAAM,gDAAgD,CACxD,MAGA,MAAM,gDAAgD,OAE1C,CAEd,MAAM,8CAA8C,SACpD,CACAH,EAAW,EAAK,EAEpB,CAEO,MAAMI,GAAsB,CACjCC,EACAN,EACAO,EACAC,IACG,CACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAKG,MAAAC,EAAUV,EAAUK,CAAI,EAC1B,GAAAK,GAAWA,EAAQ,SAAW,QAAS,CACzCN,EAAG,UAAY,GACf,OAGF,MAAMO,EAAQD,GAAS,GACjBjH,EAAYwG,EAAc,IAAII,CAAI,GAAK,GAavCO,EAAgB,kBAXFD,GAAS,CAAClH,EAC1B,oJACA,6EAS+C,eAAe4G,CAAI,kCAPlDM,EACflH,EAAY,yBAA2B,mBACxC,8CAK+G,KAH9FkH,GAAS,CAAClH,EAAY,GAAK,UAGoF,IAFjHA,EAAY,aAAe,SAEoG,YAClJ2G,EAAG,UAAYQ,EAET,MAAAC,EAAaT,EAAG,cAAc,yBAAyB,EACzDS,GAAcF,GAAS,CAAClH,GACfoH,EAAA,iBAAiB,QAAS,SAAY,CACzCH,QAAUV,EAAUK,CAAI,EAE1BK,GAAS,IACL,MAAAlB,GACJkB,EAAQ,GACRhB,EACCoB,GAAYZ,EAAgBG,EAAMS,CAAO,CAC5C,CACF,CACD,CAEL,EAKA,eAAeC,GACbtB,EACAuB,EACArB,EACAsB,EACAC,EACA,CACI,IACFvB,EAAW,EAAI,EAEf,KAAM,CAAE,2BAAAwB,CAAA,EAA+B,MAAMC,EAAA,2CAAAD,GAAA,aAAO,0BAAkB,OAAA7B,KAAA,sCAAA6B,CAAA,iCAChE,CAAE,MAAAnJ,CAAA,EAAU,MAAAoJ,EAAA,sBAAApJ,GAAA,KAAM,QAAO,0BAAgB,OAAAsH,KAAA,iBAAAtH,CAAA,kCAQ9B,MAAMmJ,EAA2B,CAChD,KAPY,CACZ,WAAY1B,EACZ,aAAAuB,EACA,MAAO,wBACT,CAGQ,CACP,GAEY,MACLhJ,EAAA,CACJ,MAAO,UACP,YAAa,sCACb,QAAS,UACV,EAEK,MAAAiJ,EAAY,eAAe,CAAE,SAAU,CAAC,gBAAiBC,CAAc,EAAG,GAE1ElJ,EAAA,CACJ,MAAO,QACP,YAAa,gCACb,QAAS,cACV,QAEIC,EAAgB,CAGvB,KAAM,CAAE,MAAAD,CAAA,EAAU,MAAAoJ,EAAA,sBAAApJ,CAAA,OAAM,QAAO,0BAAgB,OAAAsH,KAAA,iBAAAtH,CAAA,iCAC/C,IAAIqJ,EAAU,mDACV,OAAOpJ,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHoJ,EAAWpJ,EAA+B,SAAWoJ,GAGjDrJ,EAAA,CACJ,MAAO,gCACP,YAAaqJ,EACb,QAAS,cACV,SACD,CACA1B,EAAW,EAAK,EAEpB,CAEO,MAAM2B,GAAqB,CAChCtB,EACAgB,EACAf,EACAC,EACAe,EACAC,IACG,CACHf,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAKG,MAAAC,EAAUV,EAAUK,CAAI,EAC1B,GAAAK,GAAWA,EAAQ,SAAW,QAAS,CACzCN,EAAG,UAAY,GACf,OAGF,MAAMO,EAAQD,GAAS,GACjBjH,EAAYwG,EAAc,IAAII,CAAI,GAAK,GAavCkB,EAAe,kBAXDZ,GAAS,CAAClH,EAC1B,uJACA,6EAS8C,eAAe4G,CAAI,iCAPjDM,EACflH,EAAY,6BAA+B,sBAC5C,6CAK6G,KAH5FkH,GAAS,CAAClH,EAAY,GAAK,UAGkF,IAF/GA,EAAY,gBAAkB,QAE+F,YAChJ2G,EAAG,UAAYmB,EAET,MAAAC,EAAYpB,EAAG,cAAc,wBAAwB,EACvDoB,GAAab,GAAS,CAAClH,GACf+H,EAAA,iBAAiB,QAAS,SAAY,CACxCd,QAAUV,EAAUK,CAAI,EAC1BK,GAAS,IACL,MAAAK,GACJL,EAAQ,GACRM,EACCF,GAAYZ,EAAgBG,EAAMS,CAAO,EAC1CG,EACAC,CACF,CACF,CACD,CAEL,EAKA,eAAeO,GACbhC,EACAE,EACAsB,EACAC,EACA,CACI,IACFvB,EAAW,EAAI,EAET,MAAAC,EAAW,MAAM8B,EAAmC,CACxD,KAAM,CAAE,GAAIjC,CAAmB,EAChC,EAED,GAAIG,EAAS,MACX,MAAMA,EAAS,MAGjB,KAAM,CAAE,MAAA5H,CAAA,EAAU,MAAAoJ,EAAA,sBAAApJ,GAAA,KAAM,QAAO,0BAAgB,OAAAsH,KAAA,iBAAAtH,CAAA,iCACzCA,EAAA,CACJ,MAAO,UACP,YAAa,4BACb,QAAS,UACV,EAGK,MAAAiJ,EAAY,eAAe,CAAE,SAAU,CAAC,gBAAiBC,CAAc,EAAG,QACzEjJ,EAAgB,CAGvB,KAAM,CAAE,MAAAD,CAAA,EAAU,MAAAoJ,EAAA,sBAAApJ,CAAA,OAAM,QAAO,0BAAgB,OAAAsH,KAAA,iBAAAtH,CAAA,iCAC/C,IAAIqJ,EAAU,yCACV,OAAOpJ,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHoJ,EAAWpJ,EAA+B,SAAWoJ,GAGjDrJ,EAAA,CACJ,MAAO,sBACP,YAAaqJ,EACb,QAAS,cACV,SACD,CACA1B,EAAW,EAAK,EAEpB,CAEO,MAAMgC,GAAqB,CAChC3B,EACA4B,EACA3B,EACAC,EACAe,EACAC,IACG,CACHf,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAKG,MAAAC,EAAUV,EAAUK,CAAI,EAC1B,GAAAK,GAAWA,EAAQ,SAAW,QAAS,CACzCN,EAAG,UAAY,GACf,OAGF,MAAMO,EAAQD,GAAS,GACjBjH,EAAYwG,GAAe,IAAII,CAAI,GAAK,GAaxCwB,EAAe,kBAXDlB,GAAS,CAAClH,EAC1B,iJACA,6EAS8C,eAAe4G,CAAI,iCAPjDM,EACflH,EAAY,mBAAqB,4BAClC,6CAK6G,KAH5FkH,GAAS,CAAClH,EAAY,GAAK,UAGkF,IAF/GA,EAAY,cAAgB,QAEiG,YAChJ2G,EAAG,UAAYyB,EAET,MAAAC,EAAY1B,EAAG,cAAc,wBAAwB,EACvD0B,GAAanB,GAAS,CAAClH,GACfqI,EAAA,iBAAiB,QAAS,SAAY,CACxCpB,QAAUV,EAAUK,CAAI,EAC1BK,GAAS,IAEO,OAAO,QAAQ,0EAA0E,GAEnG,MAAAe,GACJf,EAAQ,GACPI,GAAYZ,IAAkBG,EAAMS,CAAO,EAC5CG,EACAC,CACF,CAEJ,CACD,CAEL", "names": ["useVesselData", "useMutation", "vesselType", "filterGroup", "skip<PERSON><PERSON>nt", "maxResultCount", "res", "postApiEkbVesselHeader", "toast", "error", "FILTER_FIELDS", "FILTER_OPERATORS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON>", "onVesselSelect", "trigger", "open", "onOpenChange", "isControlled", "internalDialogOpen", "setInternalDialogOpen", "useState", "isDialogOpen", "setIsDialogOpen", "filters", "setFilters", "sorts", "setSorts", "selectedRowIndex", "setSelectedRowIndex", "useMemo", "mutate", "vesselResponse", "isLoading", "useEffect", "vessels", "totalCount", "handleVesselSelect", "handleRowSelect", "index", "defaultTrigger", "jsx", "<PERSON><PERSON>", "jsxs", "Dialog", "DialogTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "TableSkeleton", "Fragment", "FilterSortBar", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "vessel", "TableCell", "JettySelect", "value", "onValueChange", "placeholder", "className", "disabled", "searchValue", "setSearchValue", "React", "jettyOptions", "setJettyOptions", "debouncedSearchValue", "useDebounce", "fetchJettyData", "useJettyDataWithFilter", "data", "options", "jetty", "option", "newOption", "prev", "handleChange", "values", "newValue", "MultiSelect", "ApplicationForm", "doc<PERSON>um", "voyage", "arrivalDate", "departureDate", "asideDate", "castOfDate", "postDate", "port<PERSON>rigin", "destinationPort", "barge", "onDocNumChange", "onVesselTypeChange", "onVesselChange", "onVoyageChange", "onJettyChange", "onArrivalDateChange", "onDepartureDateChange", "onAsideDateChange", "onCastOfDateChange", "onPostDateChange", "onPortOriginChange", "onDestinationPortChange", "onBargeChange", "errors", "register", "title", "handleVesselChange", "FormSection", "FormField", "Input", "e", "Select", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "VesselTable", "formatDateForInput", "dateString", "date", "pad", "n", "columns", "generateAndOpenDocument", "jettyRequestItemId", "onPreview", "setLoading", "response", "postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment", "result", "renderPreviewButton", "tableData", "loadingStates", "setLoadingState", "_instance", "td", "_row", "_col", "_prop", "_value", "_cellProperties", "rowData", "hasId", "previewButton", "previewBtn", "loading", "submitForApproval", "documentType", "queryClient", "jettyRequestId", "postApiIdjasApprovalSubmit", "__vitePreload", "message", "renderSubmitButton", "submitButton", "submitBtn", "deleteJettyRequestItem", "deleteApiIdjasJettyRequestItemById", "renderDeleteButton", "_setTableData", "deleteButton", "deleteBtn"], "ignoreList": [], "sources": ["../../../../../frontend/src/lib/hooks/useVesselData.ts", "../../../../../frontend/src/components/applications/vessel-table.tsx", "../../../../../frontend/src/components/applications/application-form.tsx", "../../../../../frontend/src/lib/date-helper.ts", "../../../../../frontend/src/components/applications/handsontable-column.ts", "../../../../../frontend/src/components/applications/handsontable-renderer.tsx"], "sourcesContent": ["import { useMutation } from '@tanstack/react-query';\r\nimport { postApiEkbVesselHeader } from '@/client/sdk.gen';\r\nimport { toast } from '@/lib/useToast';\r\nimport type { VesselQueryRequestDto, FilterGroup, PagedResultDtoOfExternalVesselHeaderDto } from '@/client/types.gen';\r\n\r\ninterface UseVesselDataProps {\r\n  vesselType?: string;\r\n  filterGroup?: FilterGroup;\r\n  skipCount?: number;\r\n  maxResultCount?: number;\r\n}\r\n\r\nexport const useVesselData = () => {\r\n  return useMutation<PagedResultDtoOfExternalVesselHeaderDto, Error, UseVesselDataProps>({\r\n    mutationFn: async ({ vesselType, filterGroup, skipCount = 0, maxResultCount = 50 }) => {\r\n      // Create vessel query request\r\n      const vesselRequest: VesselQueryRequestDto = {\r\n        vesselType,\r\n        filterGroup,\r\n        skipCount,\r\n        maxResultCount,\r\n      };\r\n\r\n      const res = await postApiEkbVesselHeader({ body: vesselRequest });\r\n      if (!res?.data?.items) {\r\n        toast({ title: 'No vessels found', variant: 'destructive' });\r\n        return { items: [], totalCount: 0 };\r\n      }\r\n      return res.data;\r\n    },\r\n    onError: (error: unknown) => {\r\n      console.error('Vessel mutation error:', error);\r\n      toast({\r\n        title: 'Error loading vessel data',\r\n        description: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  });\r\n}; ", "import type { FilterCondition, FilterGroup, FilterOperator, LogicalOperator, VesselHeaderDto } from \"@/client/types.gen\";\r\nimport FilterSortBar, { type SortDirection } from \"@/components/filter-sort-bar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from \"@/components/ui/dialog\";\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\r\nimport { useVesselData } from \"@/lib/hooks/useVesselData\";\r\nimport React, { useEffect, useMemo, useState } from \"react\";\r\nimport { TableSkeleton } from \"../ui/TableSkeleton\";\r\n\r\ninterface VesselTableProps {\r\n  vesselType?: string;\r\n  selectedVessel: VesselHeaderDto | null;\r\n  onVesselSelect: (vessel: VesselHeaderDto | null) => void;\r\n  trigger?: React.ReactNode;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}\r\n\r\nconst FILTER_FIELDS = [\r\n  { value: \"docNum\", label: \"Document Number\" },\r\n  { value: \"vesselName\", label: \"Vessel Name\" },\r\n  { value: \"voyage\", label: \"Voyage\" },\r\n  { value: \"arrival\", label: \"Arrival Date\" },\r\n  { value: \"departure\", label: \"Departure Date\" },\r\n];\r\n\r\nconst FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [\r\n  { value: \"Equals\", label: \"Equals\" },\r\n  { value: \"Contains\", label: \"Contains\" },\r\n  { value: \"NotEquals\", label: \"Not Equals\" },\r\n  { value: \"GreaterThan\", label: \">\" },\r\n  { value: \"LessThan\", label: \"<\" },\r\n];\r\n\r\nconst VesselTableContent: React.FC<VesselTableProps> = ({\r\n  vesselType,\r\n  selectedVessel,\r\n  onVesselSelect,\r\n  trigger,\r\n  open,\r\n  onOpenChange\r\n}) => {\r\n  // Remove internal dialog state if controlled\r\n  const isControlled = typeof open === 'boolean' && typeof onOpenChange === 'function';\r\n  const [internalDialogOpen, setInternalDialogOpen] = useState(false);\r\n  const isDialogOpen = isControlled ? open! : internalDialogOpen;\r\n  const setIsDialogOpen = isControlled ? onOpenChange! : setInternalDialogOpen;\r\n  const [filters, setFilters] = useState<FilterCondition[]>([]);\r\n  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);\r\n  const [selectedRowIndex, setSelectedRowIndex] = useState<number | null>(null);\r\n\r\n  // Build filter group for backend\r\n  const filterGroup: FilterGroup | undefined = useMemo(() => {\r\n    if (!filters.length) return undefined;\r\n    return {\r\n      operator: \"And\" as LogicalOperator,\r\n      conditions: filters,\r\n    };\r\n  }, [filters]);\r\n\r\n  // Fetch vessel data from API with filterGroup\r\n  const { mutate, data: vesselResponse, isPending: isLoading, error } = useVesselData();\r\n\r\n  // Trigger the mutation when vesselType or filterGroup changes\r\n  useEffect(() => {\r\n    if (vesselType) {\r\n      mutate({ vesselType, filterGroup });\r\n    }\r\n  }, [vesselType, filterGroup, mutate]);\r\n\r\n  // Extract vessels array and total count from the response\r\n  const vessels = vesselResponse?.items ?? [];\r\n  const totalCount = vesselResponse?.totalCount ?? 0;\r\n\r\n  // Handle vessel selection\r\n  const handleVesselSelect = () => {\r\n    if (selectedRowIndex !== null && vessels[selectedRowIndex]) {\r\n      onVesselSelect(vessels[selectedRowIndex]);\r\n    } else {\r\n      onVesselSelect(null);\r\n    }\r\n    setIsDialogOpen(false);\r\n  };\r\n\r\n  // Handle row selection with radio button\r\n  const handleRowSelect = (index: number) => {\r\n    setSelectedRowIndex(index);\r\n  };\r\n\r\n  const defaultTrigger = (\r\n    <Button variant=\"outline\" className=\"w-full justify-start font-normal\">\r\n      {selectedVessel ? selectedVessel.vesselName : \"Select Vessel\"}\r\n    </Button>\r\n  );\r\n\r\n  return (\r\n    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\r\n      {/* Only render DialogTrigger if trigger is provided and not null */}\r\n      {trigger !== null && (\r\n        <DialogTrigger asChild>\r\n          {trigger || defaultTrigger}\r\n        </DialogTrigger>\r\n      )}\r\n      <DialogContent className=\"min-w-[1000px] w-auto max-h-[80vh] flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle>Select Vessel {vesselType && `(${vesselType})`}</DialogTitle>\r\n        </DialogHeader>\r\n        \r\n        {isLoading ? (\r\n          <TableSkeleton\r\n            rowCount={10}\r\n            columnCount={4}\r\n            hasTitle={true}\r\n            hasSearch={true}\r\n            hasFilters={true}\r\n            hasPagination={true}\r\n            hasActions={true}\r\n          />\r\n        ) : error ? (\r\n          <div className=\"flex items-center justify-center py-8 text-destructive\">\r\n            Error loading vessels\r\n          </div>\r\n        ) : vessels.length === 0 ? (\r\n          <div className=\"flex items-center justify-center py-8 text-muted-foreground\">\r\n            {vesselType ? `No vessels found for ${vesselType}` : 'Please select a vessel type first'}\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <FilterSortBar\r\n              filterFields={FILTER_FIELDS}\r\n              operators={FILTER_OPERATORS}\r\n              filters={filters}\r\n              sorts={sorts}\r\n              onFiltersChange={setFilters}\r\n              onSortsChange={setSorts}\r\n            />\r\n            <div className=\"flex-grow overflow-hidden\">\r\n              <div className=\"relative overflow-hidden overflow-x-auto\">\r\n                <Table>\r\n                  <TableHeader>\r\n                    <TableRow className=\"border-y border-gray-200 dark:border-gray-800\">\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs w-12\">\r\n                        Select\r\n                      </TableHead>\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs\">\r\n                        Document Number\r\n                      </TableHead>\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs\">\r\n                        Vessel Name\r\n                      </TableHead>\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs\">\r\n                        Voyage\r\n                      </TableHead>\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs\">\r\n                        Arrival Date\r\n                      </TableHead>\r\n                      <TableHead className=\"whitespace-nowrap py-1 text-sm sm:text-xs\">\r\n                        Departure Date\r\n                      </TableHead>\r\n                    </TableRow>\r\n                  </TableHeader>\r\n                  <TableBody>\r\n                    {vessels.map((vessel, index) => (\r\n                      <TableRow\r\n                        key={vessel.id || index}\r\n                        className=\"group select-none hover:bg-gray-50 dark:hover:bg-gray-900 cursor-pointer\"\r\n                        onClick={() => handleRowSelect(index)}\r\n                      >\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400 w-12\">\r\n                          <input\r\n                            type=\"radio\"\r\n                            name=\"vessel-selection\"\r\n                            checked={selectedRowIndex === index}\r\n                            onChange={() => handleRowSelect(index)}\r\n                            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600\"\r\n                          />\r\n                        </TableCell>\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400\">\r\n                          {vessel.docEntry ?? \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400\">\r\n                          {vessel.vesselName ?? \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400\">\r\n                          {vessel.voyage ?? \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400\">\r\n                          {vessel.vesselArrival ? new Date(vessel.vesselArrival).toLocaleDateString() : \"-\"}\r\n                        </TableCell>\r\n                        <TableCell className=\"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400\">\r\n                          {vessel.vesselDeparture ? new Date(vessel.vesselDeparture).toLocaleDateString() : \"-\"}\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    ))}\r\n                  </TableBody>\r\n                </Table>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <div className=\"text-sm text-muted-foreground\">\r\n                {totalCount > 0 && `Showing ${vessels.length} of ${totalCount} vessels`}\r\n              </div>\r\n              <Button onClick={handleVesselSelect} disabled={selectedRowIndex === null}>\r\n                Select Vessel\r\n              </Button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default VesselTableContent; ", "import type { FilterRequestDto, VesselHeaderDto } from '@/client/types.gen';\r\nimport { FormField, FormSection } from '@/components/ui/FormField';\r\nimport { Input } from '@/components/ui/input';\r\nimport { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { useDebounce } from '@/lib/hooks/useDebounce';\r\nimport { useJettyDataWithFilter } from '@/lib/hooks/useJettyDataWithFilter';\r\nimport React from 'react';\r\nimport type { ApplicationFormProps } from './types';\r\nimport VesselTable from './vessel-table';\r\n\r\n// JettySelect component using MultiSelect in single mode\r\ninterface JettySelectProps {\r\n  value: string;\r\n  onValueChange: (value: string) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst JettySelect: React.FC<JettySelectProps> = ({\r\n  value,\r\n  onValueChange,\r\n  placeholder = 'Select jetty...',\r\n  className,\r\n  disabled = false,\r\n}) => {\r\n  const [searchValue, setSearchValue] = React.useState('');\r\n  const [jettyOptions, setJettyOptions] = React.useState<MultiSelectOption[]>([]);\r\n\r\n  // Debounce search to avoid too many API calls\r\n  const debouncedSearchValue = useDebounce(searchValue, 300);\r\n\r\n  // Fetch jetty data with filter\r\n  const { mutate: fetchJettyData, isPending: isLoading } = useJettyDataWithFilter();\r\n\r\n  // Fetch initial jetty data and when search changes\r\n  React.useEffect(() => {\r\n    const filterRequest: FilterRequestDto = {\r\n      maxResultCount: 50,\r\n      skipCount: 0,\r\n      filterGroup: debouncedSearchValue ? {\r\n        operator: 'Or',\r\n        conditions: [\r\n          {\r\n            fieldName: 'name',\r\n            operator: 'Contains',\r\n            value: debouncedSearchValue\r\n          },\r\n          {\r\n            fieldName: 'alias',\r\n            operator: 'Contains',\r\n            value: debouncedSearchValue\r\n          },\r\n          {\r\n            fieldName: 'port',\r\n            operator: 'Contains',\r\n            value: debouncedSearchValue\r\n          }\r\n        ]\r\n      } : undefined\r\n    };\r\n\r\n    fetchJettyData(filterRequest, {\r\n      onSuccess: (data) => {\r\n        const options: MultiSelectOption[] = data.map(jetty => ({\r\n          value: jetty.id || '',\r\n          label: jetty.name || jetty.alias || 'Unknown Jetty',\r\n          description: jetty.port ? `Port: ${jetty.port}` : undefined,\r\n          data: jetty\r\n        }));\r\n        setJettyOptions(options);\r\n      }\r\n    });\r\n  }, [debouncedSearchValue, fetchJettyData]);\r\n\r\n  // Fetch specific jetty when value is set but not in current options\r\n  React.useEffect(() => {\r\n    if (value && !jettyOptions.find(option => option.value === value)) {\r\n      console.log('Fetching specific jetty for value:', value);\r\n      const filterRequest: FilterRequestDto = {\r\n        maxResultCount: 1,\r\n        skipCount: 0,\r\n        filterGroup: {\r\n          operator: 'And',\r\n          conditions: [\r\n            {\r\n              fieldName: 'id',\r\n              operator: 'Equals',\r\n              value: value\r\n            }\r\n          ]\r\n        }\r\n      };\r\n\r\n      fetchJettyData(filterRequest, {\r\n        onSuccess: (data) => {\r\n          if (data.length > 0) {\r\n            const newOption: MultiSelectOption = {\r\n              value: data[0].id || '',\r\n              label: data[0].name || data[0].alias || 'Unknown Jetty',\r\n              description: data[0].port ? `Port: ${data[0].port}` : undefined,\r\n              data: data[0]\r\n            };\r\n\r\n            // Add the new option to existing options if not already present\r\n            setJettyOptions(prev => {\r\n              const exists = prev.find(option => option.value === newOption.value);\r\n              if (!exists) {\r\n                console.log('Adding specific jetty option:', newOption);\r\n                return [newOption, ...prev];\r\n              }\r\n              return prev;\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }\r\n  }, [value, jettyOptions, fetchJettyData]);\r\n\r\n  const handleChange = (values: string[]) => {\r\n    // For single select, take the first value\r\n    const newValue = values[0] || '';\r\n    console.log('JettySelect handleChange called with:', values, 'setting value to:', newValue);\r\n    onValueChange(newValue);\r\n  };\r\n\r\n  // Debug: Log when value prop changes\r\n  React.useEffect(() => {\r\n    console.log('JettySelect value prop changed to:', value);\r\n  }, [value]);\r\n\r\n  return (\r\n    <MultiSelect\r\n      options={jettyOptions}\r\n      value={value ? [value] : []}\r\n      onChange={handleChange}\r\n      placeholder={placeholder}\r\n      className={className}\r\n      disabled={disabled}\r\n      mode=\"single\"\r\n      searchValue={searchValue}\r\n      onSearchValueChange={setSearchValue}\r\n      isLoading={isLoading}\r\n      loadingText=\"Loading jetties...\"\r\n      emptyText=\"No jetties found\"\r\n      showDescription={true}\r\n    />\r\n  );\r\n};\r\n\r\nconst ApplicationForm: React.FC<ApplicationFormProps> = ({\r\n  docNum,\r\n  vesselType,\r\n  vessel,\r\n  voyage,\r\n  jetty,\r\n  arrivalDate,\r\n  departureDate,\r\n  asideDate,\r\n  castOfDate,\r\n  postDate,\r\n  portOrigin,\r\n  destinationPort,\r\n  barge,\r\n  onDocNumChange,\r\n  onVesselTypeChange,\r\n  onVesselChange,\r\n  onVoyageChange,\r\n  onJettyChange,\r\n  onArrivalDateChange,\r\n  onDepartureDateChange,\r\n  onAsideDateChange,\r\n  onCastOfDateChange,\r\n  onPostDateChange,\r\n  onPortOriginChange,\r\n  onDestinationPortChange,\r\n  onBargeChange,\r\n  errors = {},\r\n  register,\r\n  title = 'Create Application',\r\n}) => {\r\n  // Enhanced vessel change handler that also updates jetty\r\n  const handleVesselChange = (selectedVessel: VesselHeaderDto | null) => {\r\n    // console.log('handleVesselChange called with:', selectedVessel);\r\n    onVesselChange(selectedVessel);\r\n\r\n    // Auto-update jetty when vessel is selected\r\n    if (selectedVessel?.jetty?.id) {\r\n      // console.log('Setting jetty to:', selectedVessel.jetty.id);\r\n      onJettyChange(selectedVessel.jetty.id);\r\n    } else {\r\n      // console.log('No jetty found in vessel data or vessel is null');\r\n      // Clear jetty if vessel has no jetty or vessel is cleared\r\n      onJettyChange('');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6\">\r\n        <h2 className=\"text-lg font-bold text-gray-800 dark:text-white\">{title}</h2>\r\n        <div className=\"h-1 w-16 bg-primary rounded mt-2 mb-4\" />\r\n      </div>\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6 mb-2\">\r\n        <FormSection>\r\n          <FormField label=\"DocNum\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"docNum\" {...register('docNum')} />\r\n            ) : (\r\n              <Input id=\"docNum\" value={docNum} onChange={(e) => onDocNumChange(e.target.value)} />\r\n            )}\r\n            {errors.docNum && (\r\n              <span className=\"text-red-500 text-xs\">{errors.docNum.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Vessel Type\" labelWidth='100px'>\r\n            <Select value={vesselType} onValueChange={onVesselTypeChange}>\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder=\"Select Vessel Type\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"Import\">Import</SelectItem>\r\n                <SelectItem value=\"Export\">Export</SelectItem>\r\n                <SelectItem value=\"LocalIn\">Local In</SelectItem>\r\n                <SelectItem value=\"LocalOut\">Local Out</SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n            {errors.vesselType && (\r\n              <span className=\"text-red-500 text-xs\">{errors.vesselType.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Vessel\" labelWidth='100px'>\r\n            <VesselTable\r\n              vesselType={vesselType}\r\n              selectedVessel={vessel}\r\n              onVesselSelect={handleVesselChange}\r\n            />\r\n            {errors.vesselName && (\r\n              <span className=\"text-red-500 text-xs\">{errors.vesselName.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Voyage\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"voyage\" {...register('voyage')} />\r\n            ) : (\r\n              <Input id=\"voyage\" value={voyage} onChange={(e) => onVoyageChange(e.target.value)} />\r\n            )}\r\n            {errors.voyage && (\r\n              <span className=\"text-red-500 text-xs\">{errors.voyage.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Jetty\" labelWidth='100px'>\r\n            <JettySelect\r\n              value={jetty}\r\n              onValueChange={onJettyChange}\r\n              placeholder=\"Select Jetty\"\r\n            />\r\n            {errors.jetty && (\r\n              <span className=\"text-red-500 text-xs\">{errors.jetty.message as string}</span>\r\n            )}\r\n          </FormField>\r\n        </FormSection>\r\n\r\n        <FormSection>\r\n          <FormField label=\"Arrival Date\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"arrivalDate\" type=\"datetime-local\" {...register('arrivalDate')} />\r\n            ) : (\r\n              <Input id=\"arrivalDate\" type=\"datetime-local\" value={arrivalDate} onChange={e => onArrivalDateChange(e.target.value)} />\r\n            )}\r\n            {errors.arrivalDate && (\r\n              <span className=\"text-red-500 text-xs\">{errors.arrivalDate.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Departure Date\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"departureDate\" type=\"datetime-local\" {...register('departureDate')} />\r\n            ) : (\r\n              <Input id=\"departureDate\" type=\"datetime-local\" value={departureDate} onChange={e => onDepartureDateChange(e.target.value)} />\r\n            )}\r\n            {errors.departureDate && (\r\n              <span className=\"text-red-500 text-xs\">{errors.departureDate.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"A/Side Date\" labelWidth='100px'>\r\n            <Input id=\"asideDate\" type=\"datetime-local\" value={asideDate} onChange={e => onAsideDateChange(e.target.value)} />\r\n          </FormField>\r\n          <FormField label=\"Cast Of Date\" labelWidth='100px'>\r\n            <Input id=\"castOfDate\" type=\"datetime-local\" value={castOfDate} onChange={e => onCastOfDateChange(e.target.value)} />\r\n          </FormField>\r\n          <FormField label=\"Posting Date\" labelWidth='100px'>\r\n            <Input id=\"postingDate\" type=\"date\" value={postDate} onChange={e => onPostDateChange(e.target.value)} />\r\n          </FormField>\r\n        </FormSection>\r\n\r\n        <FormSection>\r\n          <FormField label=\"Port Origin\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"portOrigin\" {...register('portOrigin')} />\r\n            ) : (\r\n              <Input id=\"portOrigin\" value={portOrigin} onChange={(e) => onPortOriginChange(e.target.value)} />\r\n            )}\r\n            {errors.portOrigin && (\r\n              <span className=\"text-red-500 text-xs\">{errors.portOrigin.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Destination Port\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"destinationPort\" {...register('destinationPort')} />\r\n            ) : (\r\n              <Input id=\"destinationPort\" value={destinationPort} onChange={(e) => onDestinationPortChange(e.target.value)} />\r\n            )}\r\n            {errors.destinationPort && (\r\n              <span className=\"text-red-500 text-xs\">{errors.destinationPort.message as string}</span>\r\n            )}\r\n          </FormField>\r\n          <FormField label=\"Barge\" labelWidth='100px'>\r\n            {register ? (\r\n              <Input id=\"barge\" {...register('barge')} />\r\n            ) : (\r\n              <Input id=\"barge\" value={barge} onChange={(e) => onBargeChange(e.target.value)} />\r\n            )}\r\n            {errors.barge && (\r\n              <span className=\"text-red-500 text-xs\">{errors.barge.message as string}</span>\r\n            )}\r\n          </FormField>\r\n        </FormSection>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default ApplicationForm;", "// Helper to format ISO date string to 'yyyy-MM-ddTHH:mm' for datetime-local input\r\nexport function formatDateForInput(dateString?: string | null): string {\r\n  if (!dateString) return '';\r\n  const date = new Date(dateString);\r\n  if (isNaN(date.getTime())) return '';\r\n  // Pad month, day, hour, minute\r\n  const pad = (n: number) => n.toString().padStart(2, '0');\r\n  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}T${pad(date.getHours())}:${pad(date.getMinutes())}`;\r\n}\r\n\r\n// Helper to format ISO date string to 'YYYY-MM-DD' format\r\nexport function formatDateToYMD(dateString?: string | null): string {\r\n  if (!dateString) return '';\r\n  const date = new Date(dateString);\r\n  if (isNaN(date.getTime())) return '';\r\n  // Pad month and day\r\n  const pad = (n: number) => n.toString().padStart(2, '0');\r\n  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())}`;\r\n}", "export const columns = [\r\n  { data: 'tenantName', type: 'text', title: 'Tenant', width: 100 },\r\n  { data: 'itemName', type: 'text', title: 'Item Name', width: 200, wordWrap: false },\r\n  { data: 'quantity', type: 'numeric', title: 'Quantity', width: 80 },\r\n  { data: 'uom', type: 'text', title: 'UoM', width: 120 },\r\n  { data: 'remark', type: 'text', title: 'Remark', width: 120 },\r\n  { data: 'status', type: 'text', title: 'Status', width: 80, readOnly: true },\r\n  { data: 'letterNo', type: 'text', title: 'Letter No', width: 120 },\r\n  {\r\n    data: 'letterDate',\r\n    type: 'date',\r\n    title: 'Letter Date',\r\n    dateFormat: 'YYYY-MM-DD',\r\n    width: 120,\r\n    correctFormat: true,\r\n    datePickerConfig: {\r\n      firstDay: 0,\r\n      showWeekNumber: true,\r\n      numberOfMonths: 1,\r\n    },\r\n  },\r\n  { data: 'id', title: 'Preview', width: 80, readOnly: true, filterable: false },\r\n  { data: 'submit', title: 'Submit', width: 80, readOnly: true, filterable: false },\r\n  { data: 'delete', title: 'Delete', width: 80, readOnly: true, filterable: false },\r\n];\r\n\r\nexport type TableRowData = {\r\n  id?: string; // JettyRequestItem ID for document generation\r\n  tenantName: string;\r\n  itemName: string;\r\n  quantity: string;\r\n  uom: string;\r\n  remark: string;\r\n  status: string;\r\n  letterNo: string;\r\n  letterDate: string;\r\n  preview?: string;\r\n  submit: string;\r\n  delete: string;\r\n};", "import { deleteApiIdjasJettyRequestItemById, postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment } from '@/client/sdk.gen';\r\nimport type { ApplicationDocumentGenerationDto } from '@/client/types.gen';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport Handsontable from 'handsontable';\r\nimport type { TableRowData } from './handsontable-column';\r\n\r\n/**\r\n * Generates and opens an application document for preview\r\n */\r\nasync function generateAndOpenDocument(\r\n  jettyRequestItemId: string,\r\n  onPreview: (documentSrc: string) => void,\r\n  setLoading: (loading: boolean) => void\r\n) {\r\n  try {\r\n    setLoading(true);\r\n\r\n    const input: ApplicationDocumentGenerationDto = {\r\n      jettyRequestItemId: jettyRequestItemId,\r\n      generatePdf: true\r\n    };\r\n\r\n    const response = await postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment({\r\n      body: input\r\n    });\r\n\r\n    if (response.data) {\r\n      // The response.data is the custom object from the controller\r\n      const result = response.data;\r\n      if (result.streamUrl) {\r\n        // Open the document in the preview dialog\r\n        onPreview(result.streamUrl);\r\n      } else {\r\n        console.error('Failed to generate document:', result);\r\n        alert('Failed to generate document. Please try again.');\r\n      }\r\n    } else {\r\n      console.error('No response data received');\r\n      alert('Failed to generate document. Please try again.');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error generating document:', error);\r\n    alert('Error generating document. Please try again.');\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\nexport const renderPreviewButton = (\r\n  tableData: TableRowData[],\r\n  onPreview: (documentSrc: string) => void,\r\n  loadingStates: Map<number, boolean>,\r\n  setLoadingState: (row: number, loading: boolean) => void\r\n) => (\r\n  _instance: Handsontable.Core | undefined,\r\n  td: HTMLTableCellElement,\r\n  _row: number,\r\n  _col: number,\r\n  _prop: string | number,\r\n  _value: unknown,\r\n  _cellProperties: Handsontable.CellProperties\r\n) => {\r\n  void _col;\r\n  void _prop;\r\n  void _value;\r\n  void _cellProperties;\r\n  const rowData = tableData[_row];\r\n  if (rowData && rowData.status === 'Draft') {\r\n    td.innerHTML = '';\r\n    return;\r\n  }\r\n\r\n  const hasId = rowData?.id;\r\n  const isLoading = loadingStates.get(_row) || false;\r\n\r\n  const buttonClass = hasId && !isLoading\r\n    ? 'px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'\r\n    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n  const buttonTitle = hasId\r\n    ? (isLoading ? 'Generating document...' : 'Preview document')\r\n    : 'Save the application first to enable preview';\r\n\r\n  const disabledAttr = hasId && !isLoading ? '' : 'disabled';\r\n  const buttonText = isLoading ? 'Loading...' : 'Preview';\r\n\r\n  const previewButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"preview\" title=\"${buttonTitle}\" ${disabledAttr}>${buttonText}</button>`;\r\n  td.innerHTML = previewButton;\r\n\r\n  const previewBtn = td.querySelector('[data-action=\"preview\"]');\r\n  if (previewBtn && hasId && !isLoading) {\r\n    previewBtn.addEventListener('click', async () => {\r\n      const rowData = tableData[_row];\r\n      console.log(\"rowData\", rowData)\r\n      if (rowData?.id) {\r\n        await generateAndOpenDocument(\r\n          rowData.id,\r\n          onPreview,\r\n          (loading) => setLoadingState(_row, loading)\r\n        );\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Submits a document for approval\r\n */\r\nasync function submitForApproval(\r\n  jettyRequestItemId: string,\r\n  documentType: string,\r\n  setLoading: (loading: boolean) => void,\r\n  queryClient: QueryClient,\r\n  jettyRequestId: string\r\n) {\r\n  try {\r\n    setLoading(true);\r\n\r\n    const { postApiIdjasApprovalSubmit } = await import('@/client/sdk.gen');\r\n    const { toast } = await import('@/lib/useToast');\r\n\r\n    const input = {\r\n      documentId: jettyRequestItemId,\r\n      documentType: documentType,\r\n      notes: 'Submitted for approval'\r\n    };\r\n\r\n    const response = await postApiIdjasApprovalSubmit({\r\n      body: input\r\n    });\r\n\r\n    if (response.data) {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Successfully submitted for approval',\r\n        variant: 'default',\r\n      });\r\n      // Refetch the query to update the table data\r\n      await queryClient.refetchQueries({ queryKey: ['jetty-request', jettyRequestId] });\r\n    } else {\r\n      toast({\r\n        title: 'Error',\r\n        description: 'Failed to submit for approval',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  } catch (error: unknown) {\r\n    console.error('Error submitting for approval:', error);\r\n\r\n    const { toast } = await import('@/lib/useToast');\r\n    let message = 'Error submitting for approval. Please try again.';\r\n    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n      message = (error as { message?: string }).message ?? message;\r\n    }\r\n\r\n    toast({\r\n      title: 'Error submitting for approval',\r\n      description: message,\r\n      variant: 'destructive',\r\n    });\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\nexport const renderSubmitButton = (\r\n  tableData: TableRowData[],\r\n  documentType: string,\r\n  loadingStates: Map<number, boolean>,\r\n  setLoadingState: (row: number, loading: boolean) => void,\r\n  queryClient: QueryClient,\r\n  jettyRequestId: string\r\n) => (\r\n  _instance: Handsontable.Core | undefined,\r\n  td: HTMLTableCellElement,\r\n  _row: number,\r\n  _col: number,\r\n  _prop: string | number,\r\n  _value: unknown,\r\n  _cellProperties: Handsontable.CellProperties\r\n) => {\r\n  void _col;\r\n  void _prop;\r\n  void _value;\r\n  void _cellProperties;\r\n  const rowData = tableData[_row];\r\n  if (rowData && rowData.status === 'Draft') {\r\n    td.innerHTML = '';\r\n    return;\r\n  }\r\n\r\n  const hasId = rowData?.id;\r\n  const isLoading = loadingStates.get(_row) || false;\r\n\r\n  const buttonClass = hasId && !isLoading\r\n    ? 'px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50'\r\n    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n  const buttonTitle = hasId\r\n    ? (isLoading ? 'Submitting for approval...' : 'Submit for approval')\r\n    : 'Save the application first to enable submit';\r\n\r\n  const disabledAttr = hasId && !isLoading ? '' : 'disabled';\r\n  const buttonText = isLoading ? 'Submitting...' : 'Submit';\r\n\r\n  const submitButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"submit\" title=\"${buttonTitle}\" ${disabledAttr}>${buttonText}</button>`;\r\n  td.innerHTML = submitButton;\r\n\r\n  const submitBtn = td.querySelector('[data-action=\"submit\"]');\r\n  if (submitBtn && hasId && !isLoading) {\r\n    submitBtn.addEventListener('click', async () => {\r\n      const rowData = tableData[_row];\r\n      if (rowData?.id) {\r\n        await submitForApproval(\r\n          rowData.id,\r\n          documentType,\r\n          (loading) => setLoadingState(_row, loading),\r\n          queryClient,\r\n          jettyRequestId\r\n        );\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Deletes a jetty request item from the database\r\n */\r\nasync function deleteJettyRequestItem(\r\n  jettyRequestItemId: string,\r\n  setLoading: (loading: boolean) => void,\r\n  queryClient: QueryClient, // QueryClient type from @tanstack/react-query\r\n  jettyRequestId: string\r\n) {\r\n  try {\r\n    setLoading(true);\r\n\r\n    const response = await deleteApiIdjasJettyRequestItemById({\r\n      path: { id: jettyRequestItemId }\r\n    });\r\n\r\n    if (response.error) {\r\n      throw response.error;\r\n    }\r\n\r\n    const { toast } = await import('@/lib/useToast');\r\n    toast({\r\n      title: 'Success',\r\n      description: 'Item deleted successfully',\r\n      variant: 'default',\r\n    });\r\n\r\n    // Refetch the query to update the table data\r\n    await queryClient.refetchQueries({ queryKey: ['jetty-request', jettyRequestId] });\r\n  } catch (error: unknown) {\r\n    console.error('Error deleting item:', error);\r\n\r\n    const { toast } = await import('@/lib/useToast');\r\n    let message = 'Error deleting item. Please try again.';\r\n    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n      message = (error as { message?: string }).message ?? message;\r\n    }\r\n\r\n    toast({\r\n      title: 'Error deleting item',\r\n      description: message,\r\n      variant: 'destructive',\r\n    });\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\nexport const renderDeleteButton = (\r\n  tableData: TableRowData[],\r\n  _setTableData: (rows: TableRowData[]) => void,\r\n  loadingStates: Map<number, boolean> | undefined,\r\n  setLoadingState: ((row: number, loading: boolean) => void) | undefined,\r\n  queryClient: QueryClient, // QueryClient type from @tanstack/react-query\r\n  jettyRequestId: string // <-- pass the main request id from parent\r\n) => (\r\n  _instance: Handsontable.Core | undefined,\r\n  td: HTMLTableCellElement,\r\n  _row: number,\r\n  _col: number,\r\n  _prop: string | number,\r\n  _value: unknown,\r\n  _cellProperties: Handsontable.CellProperties\r\n) => {\r\n  void _col;\r\n  void _prop;\r\n  void _value;\r\n  void _cellProperties;\r\n  const rowData = tableData[_row];\r\n  if (rowData && rowData.status === 'Draft') {\r\n    td.innerHTML = '';\r\n    return;\r\n  }\r\n\r\n  const hasId = rowData?.id;\r\n  const isLoading = loadingStates?.get(_row) || false;\r\n\r\n  const buttonClass = hasId && !isLoading\r\n    ? 'px-2 py-0.5 bg-red-500 text-white rounded-md text-xs hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50'\r\n    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n  const buttonTitle = hasId\r\n    ? (isLoading ? 'Deleting item...' : 'Delete item from database')\r\n    : 'Save the application first to enable delete';\r\n\r\n  const disabledAttr = hasId && !isLoading ? '' : 'disabled';\r\n  const buttonText = isLoading ? 'Deleting...' : 'Delete';\r\n\r\n  const deleteButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"delete\" title=\"${buttonTitle}\" ${disabledAttr}>${buttonText}</button>`;\r\n  td.innerHTML = deleteButton;\r\n\r\n  const deleteBtn = td.querySelector('[data-action=\"delete\"]');\r\n  if (deleteBtn && hasId && !isLoading) {\r\n    deleteBtn.addEventListener('click', async () => {\r\n      const rowData = tableData[_row];\r\n      if (rowData?.id) {\r\n        // Show confirmation dialog\r\n        const confirmed = window.confirm('Are you sure you want to delete this item? This action cannot be undone.');\r\n        if (confirmed) {\r\n          await deleteJettyRequestItem(\r\n            rowData.id,\r\n            (loading) => setLoadingState?.(_row, loading),\r\n            queryClient,\r\n            jettyRequestId\r\n          );\r\n        }\r\n      }\r\n    });\r\n  }\r\n};\r\n"], "file": "assets/handsontable-renderer-bnhdgeQj.js"}