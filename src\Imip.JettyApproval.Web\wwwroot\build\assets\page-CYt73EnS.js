import{j as r}from"./vendor-6tJeyfYI.js";import{u as n,F as p,G as d,H as u,B as m,J as v,K as y,A as h}from"./app-layout-rNt37hVL.js";import{D as g}from"./data-grid-DZ2U-5jU.js";import{B as b}from"./badge-DWaCYvGm.js";import{m as o}from"./App-DnhJzTNn.js";import{A as x}from"./arrow-up-right-DyuQRH0Y.js";import"./radix-e4nK4mWk.js";import"./checkbox-D1loOtZt.js";import"./dialog-BmEXyFlW.js";import"./input-DlXlkYlT.js";import"./popover-ChFN9yvN.js";import"./table-BKSoE52x.js";import"./useDebounce-B2N8e_3P.js";import"./index-CaiFFM4D.js";import"./TableSkeleton-CIQBoxBh.js";import"./skeleton-DAOxGMKm.js";import"./plus-PD53KOti.js";import"./arrow-up-DDQ17ADi.js";import"./chevron-left-DJFXm33k.js";const f=()=>{const{toast:i}=n(),{t}=p(),c=[{accessorKey:"vessel.vesselName",header:t("table.vesselName"),cell:e=>e.getValue()??"-"},{accessorKey:"vessel.voyage",header:t("table.voyage"),cell:e=>e.getValue()??"-"},{accessorKey:"vessel.vesselType",header:t("table.vesselType"),cell:e=>e.getValue()??"-"},{accessorKey:"vessel.vesselArrival",header:t("table.arrival"),cell:e=>e.getValue()?new Date(e.getValue()).toLocaleString():"-"},{accessorKey:"vessel.destinationPort",header:t("table.destinationPort"),cell:e=>e.getValue()??"-"},{accessorKey:"status",header:t("table.status"),cell:e=>{const a=e.getValue();let s,l;switch(a){case 0:s=t("table.statusPending"),l="warning";break;case 1:s=t("table.statusApproved"),l="success";break;case 2:s=t("table.statusRejected"),l="destructive";break;case 3:s=t("table.statusCancelled"),l="secondary";break;default:s=t("table.statusUnknown"),l="primary";break}return r.jsx(b,{variant:l,size:"sm",children:s})}},{accessorKey:"requesterUserName",header:t("table.requester"),cell:e=>e.getValue()??"-"},{accessorKey:"approverUserName",header:t("table.approver"),cell:e=>e.getValue()??"-"},{accessorKey:"requestDate",header:t("table.requestDate"),cell:e=>e.getValue()?new Date(e.getValue()).toLocaleDateString():"-"},{id:"actions",header:t("table.actions"),cell:({row:e})=>{const a=e.original.vessel;return r.jsx("div",{className:"flex space-x-1",children:r.jsxs(d,{children:[r.jsx(u,{asChild:!0,children:r.jsx(m,{variant:"ghost",size:"icon",onClick:()=>{a&&a.id&&(a.vesselType==="Import"?o.visit(`/import/edit/${a.id}`):a.vesselType==="Export"?o.visit(`/export/edit/${a.id}`):o.visit(`/local/edit/${a.id}`))},"aria-label":"Details",children:r.jsx(x,{className:"w-5 h-5"})})}),r.jsx(v,{children:t("table.viewDetails")})]})})}}];return r.jsx("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:r.jsx(g,{columns:c,title:t("datagrid.approvalHistory"),queryKey:["approval-history"],manualSorting:!0,manualFiltering:!0,autoSizeColumns:!0,queryFn:async({pageIndex:e,pageSize:a})=>{try{const s=await y({body:{maxResultCount:a,skipCount:e*a}});if(!s||!s.data)throw new Error("Failed to fetch approval history");return{items:s.data.items||[],totalCount:s.data.totalCount||0}}catch{return i({title:"Error",description:"Failed to load approval history",variant:"destructive"}),{items:[],totalCount:0}}}})})},U=()=>r.jsx(h,{children:r.jsx("div",{className:"flex flex-col space-y-4 p-4",children:r.jsx(f,{})})});export{U as default};
//# sourceMappingURL=page-CYt73EnS.js.map
