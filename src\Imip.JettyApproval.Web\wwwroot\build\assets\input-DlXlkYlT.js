import{j as t}from"./vendor-6tJeyfYI.js";import{l as o}from"./app-layout-rNt37hVL.js";function d({className:r,type:e,...i}){return t.jsx("input",{type:e,"data-slot":"input",className:o("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground","flex w-full min-w-0 rounded border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none","file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium","disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50","md:text-sm","border-input dark:bg-input/30","focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]","aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40","hover:border-primary/50 dark:hover:border-primary/30","active:border-primary/70 dark:active:border-primary/50",r),...i})}export{d as I};
//# sourceMappingURL=input-DlXlkYlT.js.map
