import type { DocAttachmentDto, DocAttachmentSortDto } from '@/clientEkb';
import { ekbProxyService } from '@/services/ekbProxyService';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatBytes, useFileUpload } from '@/hooks/use-file-upload';
import { getCookie } from '@/lib/utils/cookie';
import type { QueryClient } from '@tanstack/react-query';
import { AlertCircleIcon, Download, File, FileText, FileUpIcon, Image, Trash2, XIcon } from 'lucide-react';
import React, { useState } from 'react';

interface AttachmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  attachments: DocAttachmentSortDto[];
  title: string;
  queryClient: QueryClient;
  referenceId: string | number;
  documentReferenceId: number;
  defaultTabName?: string;
  docType?: string;
  transType?: string;
  tabName?: string;
  onUploadSuccess?: () => void; // Add callback for successful upload
}

// Separate preview dialog component
interface AttachmentPreviewDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  attachment: DocAttachmentSortDto | null;
}

const AttachmentPreviewDialog: React.FC<AttachmentPreviewDialogProps> = ({
  isOpen,
  onOpenChange,
  attachment
}) => {
  const [documentSrc, setDocumentSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  React.useEffect(() => {
    if (attachment && isOpen) {
      setIsLoading(true);
      setError('');

      ekbProxyService.getFileStream(attachment.id!)
        .then((response) => {
          if (response && response.data instanceof Blob) {
            const url = URL.createObjectURL(response.data);
            setDocumentSrc(url);
            return;
          }
          throw new Error('Invalid response type for file stream');
        })
        .catch(err => {
          console.error('Error loading file:', err);
          setError('Failed to load file preview');
        })
        .finally(() => {
          setIsLoading(false);
        });
    }

    // Cleanup function - only cleanup when attachment or isOpen changes
    return () => {
      // This cleanup will run when the effect re-runs or component unmounts
      // We'll handle cleanup in a separate effect
    };
  }, [attachment, isOpen]);

  // Separate cleanup effect for blob URLs
  React.useEffect(() => {
    return () => {
      if (documentSrc && documentSrc.startsWith('blob:')) {
        URL.revokeObjectURL(documentSrc);
      }
    };
  }, [documentSrc]);

  const canPreviewInBrowser = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'htm'].includes(extension || '');
  };

  if (!attachment) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-[1000px] w-auto h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>{attachment.fileName}</DialogTitle>
        </DialogHeader>
        <div className="flex-grow overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Loading preview...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full text-red-500">
              <span>{error}</span>
            </div>
          ) : canPreviewInBrowser(attachment.fileName ?? '') ? (
            <iframe
              src={documentSrc}
              title="Document Preview"
              className="w-full h-full border-none"
            />
          ) : (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <File className="w-12 h-12 mx-auto mb-4" />
                <p>Preview not available for this file type</p>
                <p className="text-sm">Please download the file to view it</p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const AttachmentDialog: React.FC<AttachmentDialogProps> = ({
  open,
  onOpenChange,
  attachments,
  title,
  queryClient,
  referenceId,
  documentReferenceId,
  defaultTabName,
  docType,
  transType,
  tabName,
  onUploadSuccess,
}) => {
  const [selectedAttachment, setSelectedAttachment] = useState<DocAttachmentDto | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Group attachments by tab name
  const groupedAttachments = attachments.reduce((groups, attachment) => {
    const tabName = attachment.tabName || 'Other';
    if (!groups[tabName]) {
      groups[tabName] = [];
    }
    groups[tabName].push(attachment);
    return groups;
  }, {} as Record<string, DocAttachmentDto[]>);

  const tabNames = Object.keys(groupedAttachments);
  const hasShippingTab = tabNames.includes('SHIPPING');
  const allTabNames = hasShippingTab ? tabNames : [...tabNames, 'SHIPPING'];
  const [selectedTab, setSelectedTab] = useState(
    defaultTabName && allTabNames.includes(defaultTabName)
      ? defaultTabName
      : allTabNames[0] ?? ''
  );

  // Ensure selectedTab is always valid when tab names change (including SHIPPING)
  React.useEffect(() => {
    if (!allTabNames.includes(selectedTab)) {
      setSelectedTab(allTabNames[0] ?? '');
    }
  }, [allTabNames, selectedTab]);

  const handlePreview = (attachment: DocAttachmentDto) => {
    setSelectedAttachment(attachment);
    setPreviewOpen(true);
  };

  const handleDownload = async (attachment: DocAttachmentDto) => {
    try {
      // Manually construct the URL to avoid double encoding
      const rawBaseUrl = getCookie("ekbUrl");
      const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : "https://ekb-dev.imip.co.id";
      const url = `${ekbBaseUrl}/api/ekb/files/stream/${attachment.id}`;

      const response = await fetch(url, {
        method: 'GET',
        credentials: 'include', // Include cookies for authentication
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // Create a blob from the response and download it
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = attachment.fileName ?? '';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handleDelete = async (attachment: DocAttachmentDto) => {
    if (!attachment.id) {
      console.error('No attachment ID found');
      return;
    }

    if (!confirm(`Are you sure you want to delete "${attachment.fileName}"?`)) {
      return;
    }

    try {
      await ekbProxyService.deleteDocAttachment(attachment.id);

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
      queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
      queryClient.invalidateQueries({ queryKey: ['import-vessel'] });

      // Show a success message (do not close the dialog)
      setUploadSuccess('Attachment deleted successfully!');
      setTimeout(() => setUploadSuccess(null), 3000);

      // Refresh the data, do not close the dialog
      onUploadSuccess?.();
    } catch (error) {
      console.error('Error deleting attachment:', error);
      alert('Failed to delete attachment');
    }
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="w-4 h-4" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="w-4 h-4" />;
      default:
        return <File className="w-4 h-4" />;
    }
  };

  const maxSize = 100 * 1024 * 1024;
  const maxFiles = 10;
  const [uploading, setUploading] = React.useState(false);
  const [state, actions] = useFileUpload({
    multiple: true,
    maxFiles,
    maxSize,
    onFilesAdded: async (files) => {
      for (const fileObj of files) {
        // Use a type check instead of instanceof File
        if (fileObj.file && typeof fileObj.file === 'object' && 'name' in fileObj.file && 'size' in fileObj.file) {
          // This part is now handled by the separate selectedFile state and handleAgentUpload
        }
      }
      actions.clearFiles();
    },
  });
  const [uploadError, setUploadError] = React.useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = React.useState<string | null>(null);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="min-w-[800px] w-auto h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
          </DialogHeader>
          <div className="flex-grow overflow-hidden">
            <Tabs value={selectedTab} onValueChange={val => setSelectedTab(val)} className="h-full flex flex-col">
              <TabsList className={`grid w-full grid-cols-${allTabNames.length}`}>
                {tabNames.map(tabName => (
                  <TabsTrigger key={tabName} value={tabName}>
                    {tabName}
                  </TabsTrigger>
                ))}
                {/* <TabsTrigger key="SHIPPING" value="SHIPPING">SHIPPING</TabsTrigger> */}
              </TabsList>
              {tabNames.length > 0 ? tabNames.map(tabName => (
                <TabsContent key={tabName} value={tabName}>
                  <div className="p-4">
                    {/* Dropzone always at the top */}
                    <div
                      role="button"
                      onClick={uploading ? undefined : actions.openFileDialog}
                      onDragEnter={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragEnter(e); })}
                      onDragLeave={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragLeave(e); })}
                      onDragOver={uploading ? undefined : (e => e.preventDefault())}
                      onDrop={uploading ? undefined : async e => {
                        e.preventDefault();
                        actions.handleDrop(e);
                        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                          const file = e.dataTransfer.files[0];
                          setUploading(true);
                          try {
                            const rawBaseUrl = getCookie('ekbUrl');
                            const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';
                            let url = '/api/ekb/doc-attachments/upload';
                            if (url.startsWith('/')) {
                              url = ekbBaseUrl.replace(/\/$/, '') + url;
                            }
                            const token = getCookie('EkbApiToken') ?? '';
                            const formData = new FormData();
                            formData.append('File', file);
                            formData.append('ReferenceId', String(referenceId));
                            formData.append('DocType', docType ?? 'Import');
                            formData.append('ReferenceType', docType ?? 'Import');
                            formData.append('TransType', transType ?? 'ImportDetails');
                            formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));
                            formData.append('DocumentReferenceId', String(documentReferenceId));
                            await fetch(url, {
                              method: 'POST',
                              body: formData,
                              credentials: 'include',
                              headers: token ? { Authorization: `Bearer ${token}` } : undefined,
                            });
                            queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
                            queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
                            queryClient.invalidateQueries({ queryKey: ['import-vessel'] });
                            setUploadError(null);
                            setUploadSuccess('File uploaded successfully!');
                            setUploading(false);
                            onUploadSuccess?.(); // Call the new prop
                            // Clear success message after 3 seconds
                            setTimeout(() => setUploadSuccess(null), 3000);
                          } catch (err) {
                            setUploading(false);

                            console.error('Upload failed', err);
                            let message = 'Upload failed';
                            let details = '';
                            if (err && typeof err === 'object') {
                              if ('message' in err && typeof err.message === 'string') message = err.message;
                              if ('details' in err && typeof err.details === 'string') details = err.details;
                            }
                            setUploadError(details ? `${message}\n${details}` : message);
                          }
                        }
                      }}
                      data-dragging={state.isDragging || undefined}
                      className="border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]"
                      aria-disabled={uploading}
                      tabIndex={uploading ? -1 : 0}
                    >
                      <Input
                        {...actions.getInputProps()}
                        disabled={uploading}
                        onChange={async e => {
                          if (e.target.files && e.target.files.length > 0) {
                            const file = e.target.files[0];
                            setUploading(true);
                            try {
                              const rawBaseUrl = getCookie('ekbUrl');
                              const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';
                              let url = '/api/ekb/doc-attachments/upload';
                              if (url.startsWith('/')) {
                                url = ekbBaseUrl.replace(/\/$/, '') + url;
                              }
                              const token = getCookie('EkbApiToken') ?? '';
                              const formData = new FormData();
                              formData.append('File', file);
                              formData.append('ReferenceId', String(referenceId));
                              formData.append('DocType', docType ?? 'Import');
                              formData.append('ReferenceType', docType ?? 'Import');
                              formData.append('TransType', transType ?? 'ImportDetails');
                              formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));
                              formData.append('DocumentReferenceId', String(documentReferenceId));
                              await fetch(url, {
                                method: 'POST',
                                body: formData,
                                credentials: 'include',
                                headers: token ? { Authorization: `Bearer ${token}` } : undefined,
                              });
                              queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
                              queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
                              queryClient.invalidateQueries({ queryKey: ['import-vessel'] });
                              setUploadError(null);
                              setUploadSuccess('File uploaded successfully!');
                              setUploading(false);
                              onUploadSuccess?.(); // Call the new prop
                              // Clear success message after 3 seconds
                              setTimeout(() => setUploadSuccess(null), 3000);
                            } catch (err) {
                              setUploading(false);

                              console.error('Upload failed', err);
                              let message = 'Upload failed';
                              let details = '';
                              if (err && typeof err === 'object') {
                                if ('message' in err && typeof err.message === 'string') message = err.message;
                                if ('details' in err && typeof err.details === 'string') details = err.details;
                              }
                              setUploadError(details ? `${message}\n${details}` : message);
                            }
                          }
                        }}
                        className="sr-only"
                        aria-label="Upload files"
                      />
                      <div className="flex flex-col items-center justify-center text-center">
                        <div
                          className="bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border"
                          aria-hidden="true"
                        >
                          <FileUpIcon className="size-4 opacity-60" />
                        </div>
                        <p className="mb-1.5 text-sm font-medium">Upload files</p>
                        <p className="text-muted-foreground mb-2 text-xs">
                          Drag & drop or click to browse
                        </p>
                        <div className="text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs">
                          <span>All files</span>
                          <span>∙</span>
                          <span>Max {maxFiles} files</span>
                          <span>∙</span>
                          <span>Up to {formatBytes(maxSize)}</span>
                        </div>
                      </div>
                    </div>
                    {uploading && (
                      <div className="flex items-center gap-2 text-xs text-muted-foreground mt-2">
                        <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></span>
                        Uploading...
                      </div>
                    )}
                    {state.errors.length > 0 && (
                      <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
                        <AlertCircleIcon className="size-3 shrink-0" />
                        <span>{state.errors[0]}</span>
                      </div>
                    )}
                    {uploadError && (
                      <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
                        <AlertCircleIcon className="size-3 shrink-0" />
                        <span>{uploadError}</span>
                      </div>
                    )}
                    {uploadSuccess && (
                      <div className="text-green-600 flex items-center gap-1 text-xs" role="alert">
                        <span>✓</span>
                        <span>{uploadSuccess}</span>
                      </div>
                    )}
                    {/* File list (pending uploads) */}
                    {state.files.length > 0 && (
                      <div className="space-y-2">
                        {state.files.map((file) => (
                          <div
                            key={file.id}
                            className="bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3"
                          >
                            <div className="flex items-center gap-3 overflow-hidden">
                              <div className="flex aspect-square size-10 shrink-0 items-center justify-center rounded border">
                                {getFileIcon((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name)}
                              </div>
                              <div className="flex min-w-0 flex-col gap-0.5">
                                <p className="truncate text-[13px] font-medium">
                                  {(typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name}
                                </p>
                                <p className="text-muted-foreground text-xs">
                                  {formatBytes((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.size : file.file.size)}
                                </p>
                              </div>
                            </div>
                            <Button
                              size="icon"
                              variant="ghost"
                              className="text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent"
                              onClick={() => actions.removeFile(file.id)}
                              aria-label="Remove file"
                            >
                              <XIcon className="size-4" aria-hidden="true" />
                            </Button>
                          </div>
                        ))}
                        {state.files.length > 1 && (
                          <div>
                            <Button size="sm" variant="outline" onClick={actions.clearFiles}>
                              Remove all files
                            </Button>
                          </div>
                        )}
                      </div>
                    )}
                    {/* Attachments preview table below dropzone */}
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>File Name</TableHead>
                          <TableHead className="w-40">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {attachments.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={2} className="text-center text-muted-foreground">No files uploaded yet.</TableCell>
                          </TableRow>
                        ) : (
                          attachments.map((attachment, idx) => (
                            <TableRow key={attachment.id || idx}>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  {getFileIcon(attachment.fileName ?? '')}
                                  <span title={attachment.fileName ?? ''}>{attachment.fileName}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button variant="outline" size="sm" onClick={() => handlePreview(attachment)}>
                                    Preview
                                  </Button>
                                  <Button variant="outline" size="sm" onClick={() => handleDownload(attachment)}>
                                    <Download className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDelete(attachment)}
                                    className="text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </TabsContent>
              )) : <TabsContent key="SHIPPING" value="SHIPPING">
                <AgentTabContent
                  attachments={attachments}
                  getFileIcon={getFileIcon}
                  queryClient={queryClient}
                  referenceId={referenceId}
                  documentReferenceId={documentReferenceId}
                  docType={docType}
                  transType={transType}
                  tabName={tabName}
                  defaultTabName={defaultTabName}
                  onUploadSuccess={onUploadSuccess} // Pass the new prop down
                />
              </TabsContent>}
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      <AttachmentPreviewDialog
        isOpen={previewOpen}
        onOpenChange={setPreviewOpen}
        attachment={selectedAttachment}
      />
    </>
  );
};

// AGENT Tab Content Component
type AgentTabContentProps = {
  attachments: DocAttachmentSortDto[];
  getFileIcon: (fileName: string) => React.ReactNode;
  queryClient: QueryClient;
  referenceId: string | number;
  documentReferenceId: number;
  docType?: string;
  transType?: string;
  tabName?: string;
  defaultTabName?: string;
  onUploadSuccess?: () => void; // Add callback for successful upload
};
function AgentTabContent({
  attachments, getFileIcon, queryClient, referenceId, documentReferenceId,
  docType, transType, tabName, defaultTabName, onUploadSuccess
}: AgentTabContentProps) {
  const maxSize = 100 * 1024 * 1024;
  const maxFiles = 10;
  const [uploading, setUploading] = React.useState(false);
  const [state, actions] = useFileUpload({
    multiple: true,
    maxFiles,
    maxSize,
    onFilesAdded: async (files) => {
      for (const fileObj of files) {
        // Use a type check instead of instanceof File
        if (fileObj.file && typeof fileObj.file === 'object' && 'name' in fileObj.file && 'size' in fileObj.file) {
          // This part is now handled by the separate selectedFile state and handleAgentUpload
        }
      }
      actions.clearFiles();
    },
  });
  const [uploadError, setUploadError] = React.useState<string | null>(null);
  const [uploadSuccess, setUploadSuccess] = React.useState<string | null>(null);

  const handleDelete = async (attachment: DocAttachmentDto) => {
    if (!attachment.id) {
      console.error('No attachment ID found');
      return;
    }

    if (!confirm(`Are you sure you want to delete "${attachment.fileName}"?`)) {
      return;
    }

    try {
      await ekbProxyService.deleteDocAttachment(attachment.id);

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
      queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
      queryClient.invalidateQueries({ queryKey: ['import-vessel'] });

      // Call the upload success callback to refresh the table
      onUploadSuccess?.();
    } catch (error) {
      console.error('Error deleting attachment:', error);
      alert('Failed to delete attachment');
    }
  };

  return (
    <form onSubmit={e => e.preventDefault()} className="space-y-4">
      <div className="flex flex-col gap-4">
        {/* Drop area */}
        <div
          role="button"
          onClick={uploading ? undefined : actions.openFileDialog}
          onDragEnter={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragEnter(e); })}
          onDragLeave={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragLeave(e); })}
          onDragOver={uploading ? undefined : (e => e.preventDefault())}
          onDrop={uploading ? undefined : async e => {
            e.preventDefault();
            actions.handleDrop(e);
            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
              const file = e.dataTransfer.files[0];
              setUploading(true);
              try {
                const rawBaseUrl = getCookie('ekbUrl');
                const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';
                let url = '/api/ekb/doc-attachments/upload';
                if (url.startsWith('/')) {
                  url = ekbBaseUrl.replace(/\/$/, '') + url;
                }
                const token = getCookie('EkbApiToken') ?? '';
                const formData = new FormData();
                formData.append('File', file);
                formData.append('ReferenceId', String(referenceId));
                formData.append('DocType', docType ?? 'Import');
                formData.append('ReferenceType', docType ?? 'Import');
                formData.append('TransType', transType ?? 'ImportDetails');
                formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));
                formData.append('DocumentReferenceId', String(documentReferenceId));
                await fetch(url, {
                  method: 'POST',
                  body: formData,
                  credentials: 'include',
                  headers: token ? { Authorization: `Bearer ${token}` } : undefined,
                });
                queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
                queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
                queryClient.invalidateQueries({ queryKey: ['import-vessel'] });
                setUploadError(null);
                setUploadSuccess('File uploaded successfully!');
                setUploading(false);
                onUploadSuccess?.(); // Call the new prop
                // Clear success message after 3 seconds
                setTimeout(() => setUploadSuccess(null), 3000);
              } catch (err) {
                setUploading(false);

                console.error('Upload failed', err);
                let message = 'Upload failed';
                let details = '';
                if (err && typeof err === 'object') {
                  if ('message' in err && typeof err.message === 'string') message = err.message;
                  if ('details' in err && typeof err.details === 'string') details = err.details;
                }
                setUploadError(details ? `${message}\n${details}` : message);
              }
            }
          }}
          data-dragging={state.isDragging || undefined}
          className="border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]"
          aria-disabled={uploading}
          tabIndex={uploading ? -1 : 0}
        >
          <Input
            {...actions.getInputProps()}
            disabled={uploading}
            onChange={async e => {
              if (e.target.files && e.target.files.length > 0) {
                const file = e.target.files[0];
                setUploading(true);
                try {
                  const rawBaseUrl = getCookie('ekbUrl');
                  const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';
                  let url = '/api/ekb/doc-attachments/upload';
                  if (url.startsWith('/')) {
                    url = ekbBaseUrl.replace(/\/$/, '') + url;
                  }
                  const token = getCookie('EkbApiToken') ?? '';
                  const formData = new FormData();
                  formData.append('File', file);
                  formData.append('ReferenceId', String(referenceId));
                  formData.append('DocType', docType ?? 'Import');
                  formData.append('ReferenceType', docType ?? 'Import');
                  formData.append('TransType', transType ?? 'ImportDetails');
                  formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));
                  formData.append('DocumentReferenceId', String(documentReferenceId));
                  await fetch(url, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
                  });
                  queryClient.invalidateQueries({ queryKey: ['export-vessel'] });
                  queryClient.invalidateQueries({ queryKey: ['local-vessel'] });
                  queryClient.invalidateQueries({ queryKey: ['import-vessel'] });
                  setUploadError(null);
                  setUploadSuccess('File uploaded successfully!');
                  setUploading(false);
                  onUploadSuccess?.(); // Call the new prop
                  // Clear success message after 3 seconds
                  setTimeout(() => setUploadSuccess(null), 3000);
                } catch (err) {
                  setUploading(false);

                  console.error('Upload failed', err);
                  let message = 'Upload failed';
                  let details = '';
                  if (err && typeof err === 'object') {
                    if ('message' in err && typeof err.message === 'string') message = err.message;
                    if ('details' in err && typeof err.details === 'string') details = err.details;
                  }
                  setUploadError(details ? `${message}\n${details}` : message);
                }
              }
            }}
            className="sr-only"
            aria-label="Upload files"
          />
          <div className="flex flex-col items-center justify-center text-center">
            <div
              className="bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border"
              aria-hidden="true"
            >
              <FileUpIcon className="size-4 opacity-60" />
            </div>
            <p className="mb-1.5 text-sm font-medium">Upload files</p>
            <p className="text-muted-foreground mb-2 text-xs">
              Drag & drop or click to browse
            </p>
            <div className="text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs">
              <span>All files</span>
              <span>∙</span>
              <span>Max {maxFiles} files</span>
              <span>∙</span>
              <span>Up to {formatBytes(maxSize)}</span>
            </div>
          </div>
        </div>
        {uploading && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-2">
            <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></span>
            Uploading...
          </div>
        )}
        {state.errors.length > 0 && (
          <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
            <AlertCircleIcon className="size-3 shrink-0" />
            <span>{state.errors[0]}</span>
          </div>
        )}
        {uploadError && (
          <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
            <AlertCircleIcon className="size-3 shrink-0" />
            <span>{uploadError}</span>
          </div>
        )}
        {uploadSuccess && (
          <div className="text-green-600 flex items-center gap-1 text-xs" role="alert">
            <span>✓</span>
            <span>{uploadSuccess}</span>
          </div>
        )}
        {/* File list (pending uploads) */}
        {state.files.length > 0 && (
          <div className="space-y-2">
            {state.files.map((file) => (
              <div
                key={file.id}
                className="bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3"
              >
                <div className="flex items-center gap-3 overflow-hidden">
                  <div className="flex aspect-square size-10 shrink-0 items-center justify-center rounded border">
                    {getFileIcon((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name)}
                  </div>
                  <div className="flex min-w-0 flex-col gap-0.5">
                    <p className="truncate text-[13px] font-medium">
                      {(typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name}
                    </p>
                    <p className="text-muted-foreground text-xs">
                      {formatBytes((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.size : file.file.size)}
                    </p>
                  </div>
                </div>
                <Button
                  size="icon"
                  variant="ghost"
                  className="text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent"
                  onClick={() => actions.removeFile(file.id)}
                  aria-label="Remove file"
                >
                  <XIcon className="size-4" aria-hidden="true" />
                </Button>
              </div>
            ))}
            {state.files.length > 1 && (
              <div>
                <Button size="sm" variant="outline" onClick={actions.clearFiles}>
                  Remove all files
                </Button>
              </div>
            )}
          </div>
        )}
        {/* Uploaded files table */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>File Name</TableHead>
              <TableHead className="w-40">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {attachments.length === 0 ? (
              <TableRow>
                <TableCell colSpan={2} className="text-center text-muted-foreground">No files uploaded yet.</TableCell>
              </TableRow>
            ) : (
              attachments.map((attachment, idx) => (
                <TableRow key={attachment.id || idx}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getFileIcon(attachment.fileName ?? '')}
                      <span title={attachment.fileName ?? ''}>{attachment.fileName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(attachment)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </form>
  );
}