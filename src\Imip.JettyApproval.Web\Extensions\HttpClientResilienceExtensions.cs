using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http.Resilience;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Resilience;
using Polly;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Extensions;

/// <summary>
/// Extension methods for configuring HttpClient resilience patterns with monitoring
/// </summary>
public static class HttpClientResilienceExtensions
{
    /// <summary>
    /// Adds standard resilience configuration optimized for critical authentication services
    /// </summary>
    public static IHttpStandardResiliencePipelineBuilder AddCriticalServiceResilience(this IHttpClientBuilder builder)
    {
        return builder.AddStandardResilienceHandler(options =>
        {
            // Retry Policy - Aggressive for critical services
            options.Retry.MaxRetryAttempts = 5;
            options.Retry.BackoffType = DelayBackoffType.Exponential;
            options.Retry.Delay = TimeSpan.FromMilliseconds(500);
            options.Retry.MaxDelay = TimeSpan.FromSeconds(5);
            options.Retry.UseJitter = true;
            options.Retry.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .Handle<TaskCanceledException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError ||
                    response.StatusCode == HttpStatusCode.RequestTimeout ||
                    response.StatusCode == HttpStatusCode.TooManyRequests);

            // Circuit Breaker - Conservative for critical services
            options.CircuitBreaker.FailureRatio = 0.7;
            options.CircuitBreaker.MinimumThroughput = 10;
            options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
            options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(15);
            options.CircuitBreaker.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError);

            // Timeout Configuration
            options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(10);
            options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(30);
        });
    }

    /// <summary>
    /// Adds standard resilience configuration optimized for external services
    /// </summary>
    public static IHttpStandardResiliencePipelineBuilder AddExternalServiceResilience(this IHttpClientBuilder builder)
    {
        return builder.AddStandardResilienceHandler(options =>
        {
            // Retry Policy - Moderate for external services
            options.Retry.MaxRetryAttempts = 3;
            options.Retry.BackoffType = DelayBackoffType.Exponential;
            options.Retry.Delay = TimeSpan.FromSeconds(1);
            options.Retry.MaxDelay = TimeSpan.FromSeconds(8);
            options.Retry.UseJitter = true;
            options.Retry.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .Handle<TaskCanceledException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError ||
                    response.StatusCode == HttpStatusCode.RequestTimeout ||
                    response.StatusCode == HttpStatusCode.TooManyRequests);

            // Circuit Breaker - More aggressive for external services
            options.CircuitBreaker.FailureRatio = 0.5;
            options.CircuitBreaker.MinimumThroughput = 5;
            options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(20);
            options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(30);
            options.CircuitBreaker.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError);

            // Timeout Configuration
            options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(5);
            options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(15);
        });
    }

    /// <summary>
    /// Adds standard resilience configuration optimized for internal app-to-app communication
    /// </summary>
    public static IHttpStandardResiliencePipelineBuilder AddInternalServiceResilience(this IHttpClientBuilder builder)
    {
        return builder.AddStandardResilienceHandler(options =>
        {
            // Retry Policy - Balanced for internal services
            options.Retry.MaxRetryAttempts = 3;
            options.Retry.BackoffType = DelayBackoffType.Exponential;
            options.Retry.Delay = TimeSpan.FromSeconds(1);
            options.Retry.MaxDelay = TimeSpan.FromSeconds(15);
            options.Retry.UseJitter = true;
            options.Retry.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .Handle<TaskCanceledException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError ||
                    response.StatusCode == HttpStatusCode.RequestTimeout);

            // Circuit Breaker - Balanced for internal services
            options.CircuitBreaker.FailureRatio = 0.6;
            options.CircuitBreaker.MinimumThroughput = 5;
            options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
            options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(25);
            options.CircuitBreaker.ShouldHandle = new PredicateBuilder<HttpResponseMessage>()
                .Handle<HttpRequestException>()
                .HandleResult(response =>
                    response.StatusCode >= HttpStatusCode.InternalServerError);

            // Timeout Configuration - Longer for app-to-app
            options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(12);
            options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(45);
        });
    }

    /// <summary>
    /// Adds resilience configuration with custom telemetry and logging
    /// </summary>
    public static IHttpStandardResiliencePipelineBuilder AddResilienceWithTelemetry(
        this IHttpClientBuilder builder,
        string serviceName,
        ILogger logger)
    {
        return builder.AddStandardResilienceHandler(options =>
        {
            // Configure based on service name
            ConfigureResilienceByServiceType(options, serviceName);

            // Add telemetry callbacks
            options.Retry.OnRetry = args =>
            {
                logger.LogWarning("HTTP Retry attempt {AttemptNumber} for {ServiceName}. " +
                    "Outcome: {Outcome}, Duration: {Duration}ms",
                    args.AttemptNumber,
                    serviceName,
                    args.Outcome,
                    args.Duration.TotalMilliseconds);
                return ValueTask.CompletedTask;
            };

            options.CircuitBreaker.OnOpened = args =>
            {
                logger.LogError("Circuit breaker OPENED for {ServiceName}. " +
                    "Outcome: {Outcome}",
                    serviceName,
                    args.Outcome);
                return ValueTask.CompletedTask;
            };

            options.CircuitBreaker.OnClosed = args =>
            {
                logger.LogInformation("Circuit breaker CLOSED for {ServiceName}",
                    serviceName);
                return ValueTask.CompletedTask;
            };

            options.CircuitBreaker.OnHalfOpened = args =>
            {
                logger.LogInformation("Circuit breaker HALF-OPENED for {ServiceName}",
                    serviceName);
                return ValueTask.CompletedTask;
            };

            options.AttemptTimeout.OnTimeout = args =>
            {
                logger.LogWarning("HTTP request timeout for {ServiceName}. " +
                    "Timeout: {Timeout}ms",
                    serviceName,
                    args.Timeout.TotalMilliseconds);
                return ValueTask.CompletedTask;
            };
        });
    }

    private static void ConfigureResilienceByServiceType(HttpStandardResilienceOptions options, string serviceName)
    {
        switch (serviceName.ToLowerInvariant())
        {
            case "identityserver":
            case "authentication":
                ConfigureCriticalServiceResilience(options);
                break;
            case "externalauth":
            case "external":
                ConfigureExternalServiceResilience(options);
                break;
            case "apptoapp":
            case "internal":
                ConfigureInternalServiceResilience(options);
                break;
            default:
                ConfigureDefaultResilience(options);
                break;
        }
    }

    private static void ConfigureCriticalServiceResilience(HttpStandardResilienceOptions options)
    {
        options.Retry.MaxRetryAttempts = 5;
        options.Retry.Delay = TimeSpan.FromMilliseconds(500);
        options.Retry.MaxDelay = TimeSpan.FromSeconds(5);
        options.CircuitBreaker.FailureRatio = 0.7;
        options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(15);
        options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(10);
        options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(30);
    }

    private static void ConfigureExternalServiceResilience(HttpStandardResilienceOptions options)
    {
        options.Retry.MaxRetryAttempts = 3;
        options.Retry.Delay = TimeSpan.FromSeconds(1);
        options.Retry.MaxDelay = TimeSpan.FromSeconds(8);
        options.CircuitBreaker.FailureRatio = 0.5;
        options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(30);
        options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(5);
        options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(15);
    }

    private static void ConfigureInternalServiceResilience(HttpStandardResilienceOptions options)
    {
        options.Retry.MaxRetryAttempts = 3;
        options.Retry.Delay = TimeSpan.FromSeconds(1);
        options.Retry.MaxDelay = TimeSpan.FromSeconds(15);
        options.CircuitBreaker.FailureRatio = 0.6;
        options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(25);
        options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(12);
        options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(45);
    }

    private static void ConfigureDefaultResilience(HttpStandardResilienceOptions options)
    {
        options.Retry.MaxRetryAttempts = 3;
        options.Retry.Delay = TimeSpan.FromMilliseconds(500);
        options.Retry.MaxDelay = TimeSpan.FromSeconds(5);
        options.CircuitBreaker.FailureRatio = 0.5;
        options.CircuitBreaker.BreakDuration = TimeSpan.FromSeconds(20);
        options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(10);
        options.TotalRequestTimeout.Timeout = TimeSpan.FromSeconds(30);
    }
}
