import { useQuery } from '@tanstack/react-query';
import { toast } from '@/lib/useToast';
import { QueryNames } from './QueryConstants';

export interface JettyStatusData {
  jettyName: string;
  occupied: number;
  available: number;
}

export const useJettyStatus = () => {
  return useQuery<JettyStatusData[], Error>({
    queryKey: [QueryNames.GetJettyStatus],
    queryFn: async (): Promise<JettyStatusData[]> => {
      try {
        const response = await fetch('/api/dashboard/jetty-status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data.map((item: any) => ({
          jettyName: item.jettyName || '',
          occupied: item.occupied || 0,
          available: item.available || 0,
        }));
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading jetty status';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Jetty Status API Error:', error);
        toast({
          title: 'Error loading jetty status',
          description: message,
          variant: 'destructive',
        });
        
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};
