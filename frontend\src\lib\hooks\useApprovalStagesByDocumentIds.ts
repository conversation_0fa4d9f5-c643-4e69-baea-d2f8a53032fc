import { useQuery } from '@tanstack/react-query';
import { toast } from '@/lib/useToast';
import type { ApprovalStageDto } from '@/client/types.gen';

export const useApprovalStagesByDocumentIds = (documentIds: string[]) => {
  return useQuery<ApprovalStageDto[], Error>({
    queryKey: ['approval-stages-by-document-ids', documentIds],
    queryFn: async (): Promise<ApprovalStageDto[]> => {
      if (!documentIds.length) {
        return [];
      }

      try {
        const response = await fetch('/api/idjas/approval/by-document-ids', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ documentIds }),
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        return result.data || [];
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading approval stages';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Approval Stages by Document IDs API Error:', error);
        toast({
          title: 'Error loading approval stages',
          description: message,
          variant: 'destructive',
        });
        
        return [];
      }
    },
    enabled: documentIds.length > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}; 