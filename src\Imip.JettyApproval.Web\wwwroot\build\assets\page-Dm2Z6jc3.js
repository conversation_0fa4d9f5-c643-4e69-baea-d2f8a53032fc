import{j as t,u as y,h as f,f as g}from"./vendor-6tJeyfYI.js";import{F as c,A as v,u as I,M as m}from"./app-layout-rNt37hVL.js";import{I as x}from"./import-vessel-form-CB_uPBh9.js";import{$ as S,m as b}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */import"./attachment-dialog-D7s9nIdd.js";import"./dialog-BmEXyFlW.js";import"./table-BKSoE52x.js";import"./tabs-Dk-TLCdA.js";const h=()=>{const{t:s}=c(),{toast:n}=I(),p=y(),{data:u,isLoading:d}=f({queryKey:["import-vessel-next-docnum"],queryFn:async()=>(await m.generateNextImportVesselDocNum()).data}),a=g({mutationFn:async({header:e,items:r})=>{const i=await m.createImportVessel({...e,docNum:Number(e.docNum),items:r.map(o=>({...o,createdBy:"",docType:"",isScan:"",isOriginal:"",isActive:!0,isDeleted:!1,isSend:"",isFeOri:"",isFeSend:"",isChange:"",isFeChange:"",isFeActive:"",deleted:"",isUrgent:"",tenantId:o.tenantId||"",businessPartnerId:o.businessPartnerId||""}))});if(i.error)throw new Error(i.error);return i.data},onSuccess:e=>{n({title:"Success",description:"Import vessel created.",variant:"success"}),e&&e.id&&b.visit(`/jetty/vessel/import/edit/${e.id}`)},onError:e=>{n({title:e instanceof Error?e.message:e?.error?.message||"Error",description:e instanceof Error?void 0:e?.error?.details,variant:"destructive"})}}),l=async(e,r)=>{await a.mutateAsync({header:e,items:r})};return d?t.jsx("div",{children:"Loading..."}):t.jsx(x,{mode:"create",title:s("pages.vessel.create.import"),initialHeader:{docNum:Number(u)},initialItems:[],onSubmit:l,isSubmitting:a.isPending,queryClient:p,showAddLineButton:!1})};function R(){const{t:s}=c();return t.jsxs(v,{children:[t.jsx(S,{title:s("pages.vessel.create.import")}),t.jsx(h,{})]})}export{R as default};
//# sourceMappingURL=page-Dm2Z6jc3.js.map
