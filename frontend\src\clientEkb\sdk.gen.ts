// This file is auto-generated by @hey-api/openapi-ts

import { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-fetch';
import type { GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, PostApiMasterAgentFilterListData, PostApiMasterAgentFilterListResponses, PostApiMasterAgentFilterListErrors, GetApiMasterAgentData, GetApiMasterAgentResponses, GetApiMasterAgentErrors, PostApiMasterAgentData, PostApiMasterAgentResponses, PostApiMasterAgentErrors, DeleteApiMasterAgentByIdData, DeleteApiMasterAgentByIdResponses, DeleteApiMasterAgentByIdErrors, GetApiMasterAgentByIdData, GetApiMasterAgentByIdResponses, GetApiMasterAgentByIdErrors, PutApiMasterAgentByIdData, PutApiMasterAgentByIdResponses, PutApiMasterAgentByIdErrors, GetApiAuthLogoutData, GetApiAuthLogoutResponses, GetApiEkbBcTypeData, GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, PostApiEkbBcTypeData, PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, DeleteApiEkbBcTypeByIdData, DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, GetApiEkbBcTypeByIdData, GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, PutApiEkbBcTypeByIdData, PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, PostApiEkbBcTypeFilterListData, PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, PostApiEkbBoundedZoneListData, PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, PostApiEkbBoundedZoneVesselHeadersData, PostApiEkbBoundedZoneVesselHeadersResponses, PostApiEkbBoundedZoneVesselHeadersErrors, GetApiEkbBoundedZoneData, GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, PostApiEkbBoundedZoneData, PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, DeleteApiEkbBoundedZoneByIdData, DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, GetApiEkbBoundedZoneByIdData, GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, PutApiEkbBoundedZoneByIdData, PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, PostApiEkbBoundedZoneFilterListData, PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, GetApiEkbBoundedZoneQueryableWithVesselHeadersData, GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses, GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoData, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors, GetApiEkbBusinessPartnerData, GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, PostApiEkbBusinessPartnerData, PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, DeleteApiEkbBusinessPartnerByIdData, DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, GetApiEkbBusinessPartnerByIdData, GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, PutApiEkbBusinessPartnerByIdData, PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, PostApiEkbBusinessPartnerFilterListData, PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, GetApiEkbCargoData, GetApiEkbCargoResponses, GetApiEkbCargoErrors, PostApiEkbCargoData, PostApiEkbCargoResponses, PostApiEkbCargoErrors, DeleteApiEkbCargoByIdData, DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, GetApiEkbCargoByIdData, GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, PutApiEkbCargoByIdData, PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, PostApiEkbCargoFilterListData, PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, GetApiEkbDestinationPortData, GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, PostApiEkbDestinationPortData, PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, DeleteApiEkbDestinationPortByIdData, DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, GetApiEkbDestinationPortByIdData, GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, PutApiEkbDestinationPortByIdData, PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, PostApiEkbDestinationPortFilterListData, PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, PostApiEkbDocAttachmentsListData, PostApiEkbDocAttachmentsListResponses, PostApiEkbDocAttachmentsListErrors, DeleteApiEkbDocAttachmentsByIdData, DeleteApiEkbDocAttachmentsByIdResponses, DeleteApiEkbDocAttachmentsByIdErrors, GetApiEkbDocAttachmentsByIdData, GetApiEkbDocAttachmentsByIdResponses, GetApiEkbDocAttachmentsByIdErrors, PutApiEkbDocAttachmentsByIdData, PutApiEkbDocAttachmentsByIdResponses, PutApiEkbDocAttachmentsByIdErrors, PostApiEkbDocAttachmentsData, PostApiEkbDocAttachmentsResponses, PostApiEkbDocAttachmentsErrors, PostApiEkbDocAttachmentsUploadData, PostApiEkbDocAttachmentsUploadResponses, PostApiEkbDocAttachmentsUploadErrors, PostApiEkbDocAttachmentsUploadWithContentData, PostApiEkbDocAttachmentsUploadWithContentResponses, PostApiEkbDocAttachmentsUploadWithContentErrors, GetApiEkbDocAttachmentsDownloadByIdData, GetApiEkbDocAttachmentsDownloadByIdErrors, GetApiEkbDocAttachmentsByReferenceByReferenceIdData, GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses, GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors, DeleteApiEkbDocAttachmentsFileByIdData, DeleteApiEkbDocAttachmentsFileByIdResponses, DeleteApiEkbDocAttachmentsFileByIdErrors, GetApiEkbDocAttachmentsInfoByIdData, GetApiEkbDocAttachmentsInfoByIdResponses, GetApiEkbDocAttachmentsInfoByIdErrors, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, GetApiEkbExportVesselData, GetApiEkbExportVesselResponses, GetApiEkbExportVesselErrors, PostApiEkbExportVesselData, PostApiEkbExportVesselResponses, PostApiEkbExportVesselErrors, PostApiEkbExportVesselGenerateNextDocNumData, PostApiEkbExportVesselGenerateNextDocNumResponses, PostApiEkbExportVesselGenerateNextDocNumErrors, DeleteApiEkbExportVesselByIdData, DeleteApiEkbExportVesselByIdResponses, DeleteApiEkbExportVesselByIdErrors, GetApiEkbExportVesselByIdData, GetApiEkbExportVesselByIdResponses, GetApiEkbExportVesselByIdErrors, PutApiEkbExportVesselByIdData, PutApiEkbExportVesselByIdResponses, PutApiEkbExportVesselByIdErrors, PostApiEkbExportVesselFilterListData, PostApiEkbExportVesselFilterListResponses, PostApiEkbExportVesselFilterListErrors, GetApiEkbExportVesselByIdWithItemsData, GetApiEkbExportVesselByIdWithItemsResponses, GetApiEkbExportVesselByIdWithItemsErrors, GetApiEkbExportVesselBillingData, GetApiEkbExportVesselBillingResponses, GetApiEkbExportVesselBillingErrors, PostApiEkbExportVesselBillingData, PostApiEkbExportVesselBillingResponses, PostApiEkbExportVesselBillingErrors, PostApiEkbExportVesselBillingFilterListData, PostApiEkbExportVesselBillingFilterListResponses, PostApiEkbExportVesselBillingFilterListErrors, DeleteApiEkbExportVesselBillingByIdData, DeleteApiEkbExportVesselBillingByIdResponses, DeleteApiEkbExportVesselBillingByIdErrors, GetApiEkbExportVesselBillingByIdData, GetApiEkbExportVesselBillingByIdResponses, GetApiEkbExportVesselBillingByIdErrors, PutApiEkbExportVesselBillingByIdData, PutApiEkbExportVesselBillingByIdResponses, PutApiEkbExportVesselBillingByIdErrors, GetApiEkbExportVesselBillingByIdWithItemsData, GetApiEkbExportVesselBillingByIdWithItemsResponses, GetApiEkbExportVesselBillingByIdWithItemsErrors, GetApiEkbExportVesselBillingItemsData, GetApiEkbExportVesselBillingItemsResponses, GetApiEkbExportVesselBillingItemsErrors, GetApiEkbFilesDownloadByIdData, GetApiEkbFilesDownloadByIdErrors, GetApiEkbFilesStreamByIdData, GetApiEkbFilesStreamByIdErrors, GetApiEkbFilesStreamByFilePathData, GetApiEkbFilesStreamByFilePathErrors, GetApiEkbFilesInfoByIdData, GetApiEkbFilesInfoByIdErrors, GetApiHealthKubernetesData, GetApiHealthKubernetesResponses, GetApiEkbImportVesselData, GetApiEkbImportVesselResponses, GetApiEkbImportVesselErrors, PostApiEkbImportVesselData, PostApiEkbImportVesselResponses, PostApiEkbImportVesselErrors, PostApiEkbImportVesselGenerateNextDocNumData, PostApiEkbImportVesselGenerateNextDocNumResponses, PostApiEkbImportVesselGenerateNextDocNumErrors, DeleteApiEkbImportVesselByIdData, DeleteApiEkbImportVesselByIdResponses, DeleteApiEkbImportVesselByIdErrors, GetApiEkbImportVesselByIdData, GetApiEkbImportVesselByIdResponses, GetApiEkbImportVesselByIdErrors, PutApiEkbImportVesselByIdData, PutApiEkbImportVesselByIdResponses, PutApiEkbImportVesselByIdErrors, GetApiEkbImportVesselByIdWithItemsData, GetApiEkbImportVesselByIdWithItemsResponses, GetApiEkbImportVesselByIdWithItemsErrors, PostApiEkbImportVesselFilterListData, PostApiEkbImportVesselFilterListResponses, PostApiEkbImportVesselFilterListErrors, GetApiEkbImportVesselBillingData, GetApiEkbImportVesselBillingResponses, GetApiEkbImportVesselBillingErrors, PostApiEkbImportVesselBillingData, PostApiEkbImportVesselBillingResponses, PostApiEkbImportVesselBillingErrors, PostApiEkbImportVesselBillingFilterListData, PostApiEkbImportVesselBillingFilterListResponses, PostApiEkbImportVesselBillingFilterListErrors, DeleteApiEkbImportVesselBillingByIdData, DeleteApiEkbImportVesselBillingByIdResponses, DeleteApiEkbImportVesselBillingByIdErrors, GetApiEkbImportVesselBillingByIdData, GetApiEkbImportVesselBillingByIdResponses, GetApiEkbImportVesselBillingByIdErrors, PutApiEkbImportVesselBillingByIdData, PutApiEkbImportVesselBillingByIdResponses, PutApiEkbImportVesselBillingByIdErrors, GetApiEkbImportVesselBillingByIdWithItemsData, GetApiEkbImportVesselBillingByIdWithItemsResponses, GetApiEkbImportVesselBillingByIdWithItemsErrors, GetApiEkbImportVesselBillingItemsData, GetApiEkbImportVesselBillingItemsResponses, GetApiEkbImportVesselBillingItemsErrors, GetApiEkbItemClassificationData, GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, PostApiEkbItemClassificationData, PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, DeleteApiEkbItemClassificationByIdData, DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, GetApiEkbItemClassificationByIdData, GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, PutApiEkbItemClassificationByIdData, PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, PostApiEkbItemClassificationFilterListData, PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, GetApiEkbJettyData, GetApiEkbJettyResponses, GetApiEkbJettyErrors, PostApiEkbJettyData, PostApiEkbJettyResponses, PostApiEkbJettyErrors, DeleteApiEkbJettyByIdData, DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, GetApiEkbJettyByIdData, GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, PutApiEkbJettyByIdData, PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, PostApiEkbJettyFilterListData, PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, GetApiEkbLocalVesselData, GetApiEkbLocalVesselResponses, GetApiEkbLocalVesselErrors, PostApiEkbLocalVesselData, PostApiEkbLocalVesselResponses, PostApiEkbLocalVesselErrors, PostApiEkbLocalVesselGenerateNextDocNumData, PostApiEkbLocalVesselGenerateNextDocNumResponses, PostApiEkbLocalVesselGenerateNextDocNumErrors, DeleteApiEkbLocalVesselByIdData, DeleteApiEkbLocalVesselByIdResponses, DeleteApiEkbLocalVesselByIdErrors, GetApiEkbLocalVesselByIdData, GetApiEkbLocalVesselByIdResponses, GetApiEkbLocalVesselByIdErrors, PutApiEkbLocalVesselByIdData, PutApiEkbLocalVesselByIdResponses, PutApiEkbLocalVesselByIdErrors, GetApiEkbLocalVesselByIdWithItemsData, GetApiEkbLocalVesselByIdWithItemsResponses, GetApiEkbLocalVesselByIdWithItemsErrors, PostApiEkbLocalVesselFilterListData, PostApiEkbLocalVesselFilterListResponses, PostApiEkbLocalVesselFilterListErrors, GetApiEkbLocalVesselBillingData, GetApiEkbLocalVesselBillingResponses, GetApiEkbLocalVesselBillingErrors, PostApiEkbLocalVesselBillingData, PostApiEkbLocalVesselBillingResponses, PostApiEkbLocalVesselBillingErrors, PostApiEkbLocalVesselBillingFilterListData, PostApiEkbLocalVesselBillingFilterListResponses, PostApiEkbLocalVesselBillingFilterListErrors, DeleteApiEkbLocalVesselBillingByIdData, DeleteApiEkbLocalVesselBillingByIdResponses, DeleteApiEkbLocalVesselBillingByIdErrors, GetApiEkbLocalVesselBillingByIdData, GetApiEkbLocalVesselBillingByIdResponses, GetApiEkbLocalVesselBillingByIdErrors, PutApiEkbLocalVesselBillingByIdData, PutApiEkbLocalVesselBillingByIdResponses, PutApiEkbLocalVesselBillingByIdErrors, GetApiEkbLocalVesselBillingByIdWithItemsData, GetApiEkbLocalVesselBillingByIdWithItemsResponses, GetApiEkbLocalVesselBillingByIdWithItemsErrors, GetApiEkbLocalVesselBillingItemsData, GetApiEkbLocalVesselBillingItemsResponses, GetApiEkbLocalVesselBillingItemsErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, GetApiEkbPortOfLoadingData, GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, PostApiEkbPortOfLoadingData, PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, DeleteApiEkbPortOfLoadingByIdData, DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, GetApiEkbPortOfLoadingByIdData, GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, PutApiEkbPortOfLoadingByIdData, PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, PostApiEkbPortOfLoadingFilterListData, PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, GetApiEkbPortServiceData, GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, PostApiEkbPortServiceData, PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, DeleteApiEkbPortServiceByIdData, DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, GetApiEkbPortServiceByIdData, GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, PutApiEkbPortServiceByIdData, PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, PostApiEkbPortServiceFilterListData, PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, GetApiEkbSurveyorData, GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, PostApiEkbSurveyorData, PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, DeleteApiEkbSurveyorByIdData, DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, GetApiEkbSurveyorByIdData, GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, PutApiEkbSurveyorByIdData, PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, PostApiEkbSurveyorFilterListData, PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiEkbTenantData, GetApiEkbTenantResponses, GetApiEkbTenantErrors, PostApiEkbTenantData, PostApiEkbTenantResponses, PostApiEkbTenantErrors, DeleteApiEkbTenantByIdData, DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, GetApiEkbTenantByIdData, GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, PutApiEkbTenantByIdData, PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, PostApiEkbTenantFilterListData, PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, GetApiTestAuthStatusData, GetApiTestAuthStatusResponses, GetApiTestTokenInfoData, GetApiTestTokenInfoResponses, GetApiEkbTradingInvoiceData, GetApiEkbTradingInvoiceResponses, GetApiEkbTradingInvoiceErrors, PostApiEkbTradingInvoiceData, PostApiEkbTradingInvoiceResponses, PostApiEkbTradingInvoiceErrors, DeleteApiEkbTradingInvoiceByIdData, DeleteApiEkbTradingInvoiceByIdResponses, DeleteApiEkbTradingInvoiceByIdErrors, GetApiEkbTradingInvoiceByIdData, GetApiEkbTradingInvoiceByIdResponses, GetApiEkbTradingInvoiceByIdErrors, PutApiEkbTradingInvoiceByIdData, PutApiEkbTradingInvoiceByIdResponses, PutApiEkbTradingInvoiceByIdErrors, GetApiEkbTradingVesselData, GetApiEkbTradingVesselResponses, GetApiEkbTradingVesselErrors, PostApiEkbTradingVesselData, PostApiEkbTradingVesselResponses, PostApiEkbTradingVesselErrors, DeleteApiEkbTradingVesselByIdData, DeleteApiEkbTradingVesselByIdResponses, DeleteApiEkbTradingVesselByIdErrors, GetApiEkbTradingVesselByIdData, GetApiEkbTradingVesselByIdResponses, GetApiEkbTradingVesselByIdErrors, PutApiEkbTradingVesselByIdData, PutApiEkbTradingVesselByIdResponses, PutApiEkbTradingVesselByIdErrors, GetApiEkbTradingVesselByIdWithItemsData, GetApiEkbTradingVesselByIdWithItemsResponses, GetApiEkbTradingVesselByIdWithItemsErrors, GetApiEkbTradingVesselItemsData, GetApiEkbTradingVesselItemsResponses, GetApiEkbTradingVesselItemsErrors, PostApiEkbTradingVesselFilterListData, PostApiEkbTradingVesselFilterListResponses, PostApiEkbTradingVesselFilterListErrors, PostApiEkbVesselVesselHeadersData, PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, PostApiEkbVesselVesselItemsData, PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, PostApiEkbVesselByIdVesselHeaderData, PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, PostApiEkbVesselByIdVesselItemData, PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors, GetApiEkbZoneDetailInvoiceData, GetApiEkbZoneDetailInvoiceResponses, GetApiEkbZoneDetailInvoiceErrors, PostApiEkbZoneDetailInvoiceData, PostApiEkbZoneDetailInvoiceResponses, PostApiEkbZoneDetailInvoiceErrors, GetApiEkbZoneDetailInvoiceByIdByZoneDetailData, GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses, GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors, DeleteApiEkbZoneDetailInvoiceByIdData, DeleteApiEkbZoneDetailInvoiceByIdResponses, DeleteApiEkbZoneDetailInvoiceByIdErrors, GetApiEkbZoneDetailInvoiceByIdData, GetApiEkbZoneDetailInvoiceByIdResponses, GetApiEkbZoneDetailInvoiceByIdErrors, PutApiEkbZoneDetailInvoiceByIdData, PutApiEkbZoneDetailInvoiceByIdResponses, PutApiEkbZoneDetailInvoiceByIdErrors } from './types.gen';
import { client as _heyApiClient } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

export const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({
        url: '/api/abp/application-configuration',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',
        ...options
    });
};

export const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({
        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',
        ...options
    });
};

export const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({
        url: '/api/account/register',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({
        url: '/api/account/send-password-reset-code',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({
        url: '/api/account/verify-password-reset-token',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({
        url: '/api/account/reset-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiMasterAgentFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiMasterAgentFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMasterAgentFilterListResponses, PostApiMasterAgentFilterListErrors, ThrowOnError>({
        url: '/api/master/agent/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiMasterAgent = <ThrowOnError extends boolean = false>(options?: Options<GetApiMasterAgentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMasterAgentResponses, GetApiMasterAgentErrors, ThrowOnError>({
        url: '/api/master/agent',
        ...options
    });
};

export const postApiMasterAgent = <ThrowOnError extends boolean = false>(options?: Options<PostApiMasterAgentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMasterAgentResponses, PostApiMasterAgentErrors, ThrowOnError>({
        url: '/api/master/agent',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMasterAgentByIdResponses, DeleteApiMasterAgentByIdErrors, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options
    });
};

export const getApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<GetApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMasterAgentByIdResponses, GetApiMasterAgentByIdErrors, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options
    });
};

export const putApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<PutApiMasterAgentByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMasterAgentByIdResponses, PutApiMasterAgentByIdErrors, ThrowOnError>({
        url: '/api/master/agent/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiAuthLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAuthLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAuthLogoutResponses, unknown, ThrowOnError>({
        url: '/api/Auth/logout',
        ...options
    });
};

export const getApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBcTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, ThrowOnError>({
        url: '/api/ekb/bc-type',
        ...options
    });
};

export const postApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, ThrowOnError>({
        url: '/api/ekb/bc-type',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options
    });
};

export const getApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options
    });
};

export const putApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBcTypeByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBcTypeFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, ThrowOnError>({
        url: '/api/ekb/bc-type/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbBoundedZoneList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbBoundedZoneVesselHeaders = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneVesselHeadersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneVesselHeadersResponses, PostApiEkbBoundedZoneVesselHeadersErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/vessel-headers',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBoundedZoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone',
        ...options
    });
};

export const postApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options
    });
};

export const getApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options
    });
};

export const putApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBoundedZoneByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBoundedZoneFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbBoundedZoneQueryableWithVesselHeaders = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBoundedZoneQueryableWithVesselHeadersData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses, GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/queryable-with-vessel-headers',
        ...options
    });
};

export const postApiEkbBoundedZoneQueryableWithVesselHeadersDto = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors, ThrowOnError>({
        url: '/api/ekb/bounded-zone/queryable-with-vessel-headers-dto',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBusinessPartnerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, ThrowOnError>({
        url: '/api/ekb/business-partner',
        ...options
    });
};

export const postApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, ThrowOnError>({
        url: '/api/ekb/business-partner',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options
    });
};

export const getApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options
    });
};

export const putApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBusinessPartnerByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbBusinessPartnerFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, ThrowOnError>({
        url: '/api/ekb/business-partner/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbCargoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbCargoResponses, GetApiEkbCargoErrors, ThrowOnError>({
        url: '/api/ekb/cargo',
        ...options
    });
};

export const postApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoResponses, PostApiEkbCargoErrors, ThrowOnError>({
        url: '/api/ekb/cargo',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options
    });
};

export const getApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options
    });
};

export const putApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbCargoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, ThrowOnError>({
        url: '/api/ekb/cargo/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbCargoFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, ThrowOnError>({
        url: '/api/ekb/cargo/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbDestinationPortData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, ThrowOnError>({
        url: '/api/ekb/destination-port',
        ...options
    });
};

export const postApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, ThrowOnError>({
        url: '/api/ekb/destination-port',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options
    });
};

export const getApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options
    });
};

export const putApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbDestinationPortByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbDestinationPortFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, ThrowOnError>({
        url: '/api/ekb/destination-port/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbDocAttachmentsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsListResponses, PostApiEkbDocAttachmentsListErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDocAttachmentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDocAttachmentsByIdResponses, DeleteApiEkbDocAttachmentsByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/{id}',
        ...options
    });
};

export const getApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsByIdResponses, GetApiEkbDocAttachmentsByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/{id}',
        ...options
    });
};

export const putApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbDocAttachmentsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbDocAttachmentsByIdResponses, PutApiEkbDocAttachmentsByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbDocAttachments = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsResponses, PostApiEkbDocAttachmentsErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbDocAttachmentsUpload = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsUploadData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsUploadResponses, PostApiEkbDocAttachmentsUploadErrors, ThrowOnError>({
        ...formDataBodySerializer,
        url: '/api/ekb/doc-attachments/upload',
        ...options,
        headers: {
            'Content-Type': null,
            ...options?.headers
        }
    });
};

export const postApiEkbDocAttachmentsUploadWithContent = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsUploadWithContentData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsUploadWithContentResponses, PostApiEkbDocAttachmentsUploadWithContentErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/upload-with-content',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbDocAttachmentsDownloadById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsDownloadByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbDocAttachmentsDownloadByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/download/{id}',
        ...options
    });
};

export const getApiEkbDocAttachmentsByReferenceByReferenceId = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsByReferenceByReferenceIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses, GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/by-reference/{referenceId}',
        ...options
    });
};

export const deleteApiEkbDocAttachmentsFileById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDocAttachmentsFileByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDocAttachmentsFileByIdResponses, DeleteApiEkbDocAttachmentsFileByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/file/{id}',
        ...options
    });
};

export const getApiEkbDocAttachmentsInfoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsInfoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsInfoByIdResponses, GetApiEkbDocAttachmentsInfoByIdErrors, ThrowOnError>({
        url: '/api/ekb/doc-attachments/info/{id}',
        ...options
    });
};

export const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({
        url: '/api/account/dynamic-claims/refresh',
        ...options
    });
};

export const getApiEkbExportVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselResponses, GetApiEkbExportVesselErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel',
        ...options
    });
};

export const postApiEkbExportVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselResponses, PostApiEkbExportVesselErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbExportVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselGenerateNextDocNumData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselGenerateNextDocNumResponses, PostApiEkbExportVesselGenerateNextDocNumErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/generate-next-doc-num',
        ...options
    });
};

export const deleteApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbExportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbExportVesselByIdResponses, DeleteApiEkbExportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/{id}',
        ...options
    });
};

export const getApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselByIdResponses, GetApiEkbExportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/{id}',
        ...options
    });
};

export const putApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbExportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbExportVesselByIdResponses, PutApiEkbExportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbExportVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselFilterListResponses, PostApiEkbExportVesselFilterListErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbExportVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselByIdWithItemsResponses, GetApiEkbExportVesselByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel/{id}/with-items',
        ...options
    });
};

export const getApiEkbExportVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingResponses, GetApiEkbExportVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing',
        ...options
    });
};

export const postApiEkbExportVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselBillingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselBillingResponses, PostApiEkbExportVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbExportVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselBillingFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselBillingFilterListResponses, PostApiEkbExportVesselBillingFilterListErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbExportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbExportVesselBillingByIdResponses, DeleteApiEkbExportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/{id}',
        ...options
    });
};

export const getApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingByIdResponses, GetApiEkbExportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/{id}',
        ...options
    });
};

export const putApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbExportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbExportVesselBillingByIdResponses, PutApiEkbExportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbExportVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingByIdWithItemsResponses, GetApiEkbExportVesselBillingByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/{id}/with-items',
        ...options
    });
};

export const getApiEkbExportVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbExportVesselBillingItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingItemsResponses, GetApiEkbExportVesselBillingItemsErrors, ThrowOnError>({
        url: '/api/ekb/export-vessel-billing/items',
        ...options
    });
};

export const getApiEkbFilesDownloadById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesDownloadByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesDownloadByIdErrors, ThrowOnError>({
        url: '/api/ekb/files/download/{id}',
        ...options
    });
};

export const getApiEkbFilesStreamById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesStreamByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesStreamByIdErrors, ThrowOnError>({
        url: '/api/ekb/files/stream/{id}',
        ...options
    });
};

export const getApiEkbFilesStreamByFilePath = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesStreamByFilePathData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesStreamByFilePathErrors, ThrowOnError>({
        url: '/api/ekb/files/stream/{filePath}',
        ...options
    });
};

export const getApiEkbFilesInfoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesInfoByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesInfoByIdErrors, ThrowOnError>({
        url: '/api/ekb/files/info/{id}',
        ...options
    });
};

export const getApiHealthKubernetes = <ThrowOnError extends boolean = false>(options?: Options<GetApiHealthKubernetesData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiHealthKubernetesResponses, unknown, ThrowOnError>({
        url: '/api/health/kubernetes',
        ...options
    });
};

export const getApiEkbImportVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselResponses, GetApiEkbImportVesselErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel',
        ...options
    });
};

export const postApiEkbImportVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselResponses, PostApiEkbImportVesselErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbImportVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselGenerateNextDocNumData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselGenerateNextDocNumResponses, PostApiEkbImportVesselGenerateNextDocNumErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/generate-next-doc-num',
        ...options
    });
};

export const deleteApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbImportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbImportVesselByIdResponses, DeleteApiEkbImportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/{id}',
        ...options
    });
};

export const getApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselByIdResponses, GetApiEkbImportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/{id}',
        ...options
    });
};

export const putApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbImportVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbImportVesselByIdResponses, PutApiEkbImportVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbImportVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselByIdWithItemsResponses, GetApiEkbImportVesselByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/{id}/with-items',
        ...options
    });
};

export const postApiEkbImportVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselFilterListResponses, PostApiEkbImportVesselFilterListErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbImportVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingResponses, GetApiEkbImportVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing',
        ...options
    });
};

export const postApiEkbImportVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselBillingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselBillingResponses, PostApiEkbImportVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbImportVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselBillingFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselBillingFilterListResponses, PostApiEkbImportVesselBillingFilterListErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbImportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbImportVesselBillingByIdResponses, DeleteApiEkbImportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/{id}',
        ...options
    });
};

export const getApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingByIdResponses, GetApiEkbImportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/{id}',
        ...options
    });
};

export const putApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbImportVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbImportVesselBillingByIdResponses, PutApiEkbImportVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbImportVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingByIdWithItemsResponses, GetApiEkbImportVesselBillingByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/{id}/with-items',
        ...options
    });
};

export const getApiEkbImportVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbImportVesselBillingItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingItemsResponses, GetApiEkbImportVesselBillingItemsErrors, ThrowOnError>({
        url: '/api/ekb/import-vessel-billing/items',
        ...options
    });
};

export const getApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbItemClassificationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, ThrowOnError>({
        url: '/api/ekb/item-classification',
        ...options
    });
};

export const postApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, ThrowOnError>({
        url: '/api/ekb/item-classification',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options
    });
};

export const getApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options
    });
};

export const putApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbItemClassificationByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbItemClassificationFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, ThrowOnError>({
        url: '/api/ekb/item-classification/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbJettyResponses, GetApiEkbJettyErrors, ThrowOnError>({
        url: '/api/ekb/jetty',
        ...options
    });
};

export const postApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyResponses, PostApiEkbJettyErrors, ThrowOnError>({
        url: '/api/ekb/jetty',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options
    });
};

export const getApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options
    });
};

export const putApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbJettyByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, ThrowOnError>({
        url: '/api/ekb/jetty/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbJettyFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, ThrowOnError>({
        url: '/api/ekb/jetty/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbLocalVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselResponses, GetApiEkbLocalVesselErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel',
        ...options
    });
};

export const postApiEkbLocalVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselResponses, PostApiEkbLocalVesselErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbLocalVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselGenerateNextDocNumData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselGenerateNextDocNumResponses, PostApiEkbLocalVesselGenerateNextDocNumErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/generate-next-doc-num',
        ...options
    });
};

export const deleteApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbLocalVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbLocalVesselByIdResponses, DeleteApiEkbLocalVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/{id}',
        ...options
    });
};

export const getApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselByIdResponses, GetApiEkbLocalVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/{id}',
        ...options
    });
};

export const putApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbLocalVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbLocalVesselByIdResponses, PutApiEkbLocalVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbLocalVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselByIdWithItemsResponses, GetApiEkbLocalVesselByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/{id}/with-items',
        ...options
    });
};

export const postApiEkbLocalVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselFilterListResponses, PostApiEkbLocalVesselFilterListErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbLocalVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingResponses, GetApiEkbLocalVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing',
        ...options
    });
};

export const postApiEkbLocalVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselBillingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselBillingResponses, PostApiEkbLocalVesselBillingErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbLocalVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselBillingFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselBillingFilterListResponses, PostApiEkbLocalVesselBillingFilterListErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbLocalVesselBillingByIdResponses, DeleteApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/{id}',
        ...options
    });
};

export const getApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingByIdResponses, GetApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/{id}',
        ...options
    });
};

export const putApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbLocalVesselBillingByIdResponses, PutApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbLocalVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingByIdWithItemsResponses, GetApiEkbLocalVesselBillingByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/{id}/with-items',
        ...options
    });
};

export const getApiEkbLocalVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbLocalVesselBillingItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingItemsResponses, GetApiEkbLocalVesselBillingItemsErrors, ThrowOnError>({
        url: '/api/ekb/local-vessel-billing/items',
        ...options
    });
};

export const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({
        url: '/api/account/login',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({
        url: '/api/account/logout',
        ...options
    });
};

export const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({
        url: '/api/account/check-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortOfLoadingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading',
        ...options
    });
};

export const postApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options
    });
};

export const getApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options
    });
};

export const putApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortOfLoadingByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbPortOfLoadingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, ThrowOnError>({
        url: '/api/ekb/port-of-loading/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortServiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, ThrowOnError>({
        url: '/api/ekb/port-service',
        ...options
    });
};

export const postApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, ThrowOnError>({
        url: '/api/ekb/port-service',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options
    });
};

export const getApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options
    });
};

export const putApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortServiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/port-service/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbPortServiceFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, ThrowOnError>({
        url: '/api/ekb/port-service/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options
    });
};

export const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({
        url: '/api/account/my-profile',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({
        url: '/api/account/my-profile/change-password',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbSurveyorData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, ThrowOnError>({
        url: '/api/ekb/surveyor',
        ...options
    });
};

export const postApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, ThrowOnError>({
        url: '/api/ekb/surveyor',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options
    });
};

export const getApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options
    });
};

export const putApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbSurveyorByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbSurveyorFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, ThrowOnError>({
        url: '/api/ekb/surveyor/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options
    });
};

export const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options
    });
};

export const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({
        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',
        ...options
    });
};

export const getApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTenantData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbTenantResponses, GetApiEkbTenantErrors, ThrowOnError>({
        url: '/api/ekb/tenant',
        ...options
    });
};

export const postApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantResponses, PostApiEkbTenantErrors, ThrowOnError>({
        url: '/api/ekb/tenant',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options
    });
};

export const getApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options
    });
};

export const putApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTenantByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, ThrowOnError>({
        url: '/api/ekb/tenant/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const postApiEkbTenantFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, ThrowOnError>({
        url: '/api/ekb/tenant/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiTestAuthStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestAuthStatusData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTestAuthStatusResponses, unknown, ThrowOnError>({
        url: '/api/test/auth-status',
        ...options
    });
};

export const getApiTestTokenInfo = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestTokenInfoData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiTestTokenInfoResponses, unknown, ThrowOnError>({
        url: '/api/test/token-info',
        ...options
    });
};

export const getApiEkbTradingInvoice = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTradingInvoiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbTradingInvoiceResponses, GetApiEkbTradingInvoiceErrors, ThrowOnError>({
        url: '/api/ekb/trading-invoice',
        ...options
    });
};

export const postApiEkbTradingInvoice = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingInvoiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingInvoiceResponses, PostApiEkbTradingInvoiceErrors, ThrowOnError>({
        url: '/api/ekb/trading-invoice',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTradingInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTradingInvoiceByIdResponses, DeleteApiEkbTradingInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-invoice/{id}',
        ...options
    });
};

export const getApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTradingInvoiceByIdResponses, GetApiEkbTradingInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-invoice/{id}',
        ...options
    });
};

export const putApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTradingInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbTradingInvoiceByIdResponses, PutApiEkbTradingInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-invoice/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbTradingVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselResponses, GetApiEkbTradingVesselErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel',
        ...options
    });
};

export const postApiEkbTradingVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingVesselData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingVesselResponses, PostApiEkbTradingVesselErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const deleteApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTradingVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTradingVesselByIdResponses, DeleteApiEkbTradingVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/{id}',
        ...options
    });
};

export const getApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselByIdResponses, GetApiEkbTradingVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/{id}',
        ...options
    });
};

export const putApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTradingVesselByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbTradingVesselByIdResponses, PutApiEkbTradingVesselByIdErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

export const getApiEkbTradingVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselByIdWithItemsData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselByIdWithItemsResponses, GetApiEkbTradingVesselByIdWithItemsErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/{id}/with-items',
        ...options
    });
};

export const getApiEkbTradingVesselItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTradingVesselItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbTradingVesselItemsResponses, GetApiEkbTradingVesselItemsErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/items',
        ...options
    });
};

export const postApiEkbTradingVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingVesselFilterListData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingVesselFilterListResponses, PostApiEkbTradingVesselFilterListErrors, ThrowOnError>({
        url: '/api/ekb/trading-vessel/filter-list',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbVesselVesselHeaders = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselHeadersData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, ThrowOnError>({
        url: '/api/ekb/vessel/vessel-headers',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbVesselVesselItems = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselItemsData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, ThrowOnError>({
        url: '/api/ekb/vessel/vessel-items',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const postApiEkbVesselByIdVesselHeader = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselHeaderData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, ThrowOnError>({
        url: '/api/ekb/vessel/{id}/vessel-header',
        ...options
    });
};

export const postApiEkbVesselByIdVesselItem = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselItemData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors, ThrowOnError>({
        url: '/api/ekb/vessel/{id}/vessel-item',
        ...options
    });
};

export const getApiEkbZoneDetailInvoice = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbZoneDetailInvoiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceResponses, GetApiEkbZoneDetailInvoiceErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice',
        ...options
    });
};

export const postApiEkbZoneDetailInvoice = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbZoneDetailInvoiceData, ThrowOnError>) => {
    return (options?.client ?? _heyApiClient).post<PostApiEkbZoneDetailInvoiceResponses, PostApiEkbZoneDetailInvoiceErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

export const getApiEkbZoneDetailInvoiceByIdByZoneDetail = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbZoneDetailInvoiceByIdByZoneDetailData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses, GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice/{id}/by-zone-detail',
        ...options
    });
};

export const deleteApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).delete<DeleteApiEkbZoneDetailInvoiceByIdResponses, DeleteApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice/{id}',
        ...options
    });
};

export const getApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceByIdResponses, GetApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice/{id}',
        ...options
    });
};

export const putApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {
    return (options.client ?? _heyApiClient).put<PutApiEkbZoneDetailInvoiceByIdResponses, PutApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({
        url: '/api/ekb/zone-detail-invoice/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};