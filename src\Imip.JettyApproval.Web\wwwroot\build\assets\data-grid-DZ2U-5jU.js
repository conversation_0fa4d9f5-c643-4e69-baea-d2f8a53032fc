import{a as o,h as Ye,$ as p,j as e}from"./vendor-6tJeyfYI.js";import{k as b,a0 as G,B as c,R as Ze,S as et,c as tt,d as st,e as at,f as nt,a1 as lt}from"./app-layout-rNt37hVL.js";import{C as W}from"./checkbox-D1loOtZt.js";import{D as ot}from"./dialog-BmEXyFlW.js";import{I as L}from"./input-DlXlkYlT.js";import{P as it}from"./popover-ChFN9yvN.js";import{T as rt,a as ct,b as N,c as ue,d as dt,e as k}from"./table-BKSoE52x.js";import{u as ge}from"./useDebounce-B2N8e_3P.js";import{u as ut,f as me,d as mt,c as gt,a as ht,b as xt,g as ft}from"./index-CaiFFM4D.js";import{T as q}from"./TableSkeleton-CIQBoxBh.js";import{P as pt}from"./plus-PD53KOti.js";import{A as yt,a as jt,b as Ct}from"./arrow-up-DDQ17ADi.js";import{C as wt}from"./chevron-left-DJFXm33k.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bt=[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],St=b("align-center",bt);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vt=[["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 18h18",key:"1h113x"}],["path",{d:"M3 6h18",key:"d0wm0j"}]],Nt=b("align-justify",vt);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]],Mt=b("align-right",kt);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],It=b("eye",zt);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rt=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]],Ft=b("list-filter",Rt);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dt=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Pt=b("pen-line",Dt),Tt={xsmall:e.jsx(Nt,{className:"w-4 h-4"}),small:e.jsx(St,{className:"w-5 h-5"}),medium:e.jsx(Mt,{className:"w-6 h-6"})};function Jt({columns:u,queryFn:F,queryKey:D,defaultPageSize:M=10,enableRowSelection:m=!1,enableRowExpansion:y=!1,renderExpandedRow:z,onCreate:K,createModalContent:P,onRefresh:B,title:he,initialState:xe,manualSorting:fe=!0,manualFiltering:pe=!0,rowIdAccessor:S,striped:ye=!1,enableInlineEditing:T=!1,onRowUpdate:U,editableColumns:O=[],autoSizeColumns:I=!1,meta:je}){const[g,J]=o.useState(0),[h,Q]=o.useState(M),[E,Ce]=o.useState([]),[V,we]=o.useState([]),[be,Se]=o.useState({}),[ve,Ne]=o.useState({}),[v,ke]=o.useState(()=>{const t=G.get("dataGridDensity");return t==="xsmall"||t==="small"||t==="medium"?t:"medium"}),[X,Me]=o.useState(!1),[Y,Z]=o.useState(!1),[$,ee]=o.useState(""),te=ge($,300),[ze,Ie]=o.useState({}),[Re,se]=o.useState(!1),[Fe,De]=o.useState(0),[x,A]=o.useState(null),[ae,R]=o.useState(""),[ne,le]=o.useState(!1),{data:Pe,isLoading:H,isFetching:Te}=Ye({queryKey:[...D,{pageIndex:g,pageSize:h,sorting:E,columnFilters:V,globalFilter:te,refreshKey:Fe}],queryFn:()=>F({pageIndex:g,pageSize:h,sorting:E,filters:V,globalFilter:te})}),i=Pe??{items:[],totalCount:0},a=ut({data:i.items,columns:u,pageCount:Math.ceil(i.totalCount/h),state:{pagination:{pageIndex:g,pageSize:h},sorting:E,columnFilters:V,rowSelection:be,expanded:ve,globalFilter:$,columnVisibility:ze},manualPagination:!0,manualSorting:fe,manualFiltering:pe,getCoreRowModel:ft(),getPaginationRowModel:xt(),getSortedRowModel:ht(),getFilteredRowModel:gt(),getExpandedRowModel:mt(),onPaginationChange:t=>{const s=typeof t=="function"?t({pageIndex:g,pageSize:h}):t;J(s.pageIndex),Q(s.pageSize)},onSortingChange:Ce,onColumnFiltersChange:we,onRowSelectionChange:Se,onExpandedChange:Ne,onGlobalFilterChange:ee,onColumnVisibilityChange:Ie,...xe,getRowId:S?t=>String(S(t)):void 0,enableRowSelection:m,enableExpanding:y,enableColumnResizing:!0,columnResizeMode:"onChange",meta:je}),oe=p.useCallback(()=>{if(!I||!i.items.length)return;const t=document.querySelector("[data-table]");if(!t)return;const s=t.querySelectorAll("th"),n=t.querySelectorAll("td"),r=new Map;s.forEach((l,C)=>{const f=a.getAllColumns()[C]?.id;if(f){const w=l.scrollWidth;r.set(f,Math.max(r.get(f)||0,w))}});const d=a.getAllColumns();d.forEach((l,C)=>{const f=l.id;let w=r.get(f)||0;const Ue=Math.min(10,i.items.length);for(let _=0;_<Ue;_++){const de=_*d.length+C;if(n[de]){const Xe=n[de].scrollWidth;w=Math.max(w,Xe)}}w+=20;const Oe=l.columnDef.minSize||50,Je=l.columnDef.maxSize||500,Qe=Math.max(Oe,Math.min(w,Je));r.set(f,Qe)}),d.forEach(l=>{const C=r.get(l.id);C&&l.getCanResize()&&a.setColumnSizing(f=>({...f,[l.id]:C}))})},[I,i.items,a]);p.useEffect(()=>{I&&i.items.length>0&&setTimeout(oe,100)},[I,i.items,oe]);const Ee=v==="xsmall"?"text-xs [&_td]:py-0.5 [&_th]:py-0.5":v==="small"?"text-sm [&_td]:py-1.5 [&_th]:py-1.5":"text-base [&_td]:py-3 [&_th]:py-3",Ve=()=>{ke(t=>{const s=t==="xsmall"?"small":t==="small"?"medium":"xsmall";return G.set("dataGridDensity",s,{expires:365}),s})};p.useEffect(()=>{G.set("dataGridDensity",v,{expires:365})},[v]);const ie=()=>{B?B():De(t=>t+1)},$e=()=>{P?se(!0):K&&K()},Ae=(t,s,n)=>{!T||!O.includes(s)||(A({rowId:t,columnId:s}),R(n))},re=async()=>{if(!(!x||!U))try{le(!0);const t=S?S(i.items.find(s=>String(S(s))===x.rowId)):x.rowId;await U(t,{[x.columnId]:ae}),A(null),R(""),ie()}catch{}finally{le(!1)}},ce=()=>{A(null),R("")},He=t=>{t.key==="Enter"?(t.preventDefault(),re()):t.key==="Escape"&&(t.preventDefault(),ce())},j=p.useMemo(()=>a&&typeof a.getIsAllRowsSelected=="function"&&typeof a.getToggleAllRowsSelectedHandler=="function"&&typeof a.getHeaderGroups=="function"&&typeof a.getRowModel=="function"&&i.items&&i.items.length>0,[a,i.items]),_e=({cell:t})=>{const s=x?.rowId===t.row.id&&x?.columnId===t.column.id,n=T&&O.includes(t.column.id),r=t.getValue();return s?e.jsxs("div",{className:"relative",children:[e.jsx(L,{value:ae,onChange:d=>R(d.target.value),onKeyDown:He,onBlur:re,className:"h-8 text-sm",autoFocus:!0,disabled:ne}),ne&&e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-background/80",children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"})})]}):e.jsx("div",{className:`min-h-[2rem] flex items-center ${n?"cursor-pointer hover:bg-muted/50 rounded px-1 -mx-1":""}`,onClick:()=>n&&Ae(t.row.id,t.column.id,r||""),title:n?"Click to edit":void 0,children:me(t.column.columnDef.cell,t.getContext())})},Ge=()=>{try{return j?a.getIsAllRowsSelected?.()??!1:!1}catch{return!1}},We=()=>t=>{try{if(!j)return;const s=a.getToggleAllRowsSelectedHandler?.();s&&s(t)}catch{}},qe=t=>{try{return j?t?.getIsSelected?.()??!1:!1}catch{return!1}},Le=t=>s=>{try{if(!j)return;const n=t?.getToggleSelectedHandler?.();n&&n(s)}catch{}},Ke=t=>{try{return t?.getIsVisible?.()??!0}catch{return!0}},Be=t=>()=>{try{t?.toggleVisibility?.()}catch{}};return e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 px-1",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-lg truncate max-w-[40vw]",children:he}),e.jsx("div",{className:"h-1 w-16 bg-primary rounded mt-2 mb-4"})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx(L,{placeholder:"Global search...",value:$,onChange:t=>ee(t.target.value),className:"w-48"}),e.jsxs(it,{open:Y,onOpenChange:Z,children:[e.jsx(c,{size:"sm",variant:"outline",onClick:()=>Z(t=>!t),"aria-label":"Show/Hide Columns",children:e.jsx(It,{className:"w-4 h-4 mr-1"})}),Y&&e.jsxs("div",{className:"absolute z-50 mt-2 bg-popover border rounded shadow p-2 min-w-[180px]",children:[e.jsx("div",{className:"font-semibold mb-2",children:"Toggle columns"}),a?.getAllLeafColumns?.()?.map(t=>e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(W,{checked:Ke(t),onCheckedChange:Be(t),id:`colvis-${t.id}`}),e.jsx("label",{htmlFor:`colvis-${t.id}`,children:t.columnDef.header})]},t.id))]})]}),e.jsx(c,{size:"sm",variant:X?"default":"outline",onClick:()=>Me(t=>!t),"aria-label":"Toggle Filters",children:e.jsx(Ft,{className:"w-4 h-4 mr-1"})}),e.jsx(c,{size:"sm",variant:"outline",onClick:Ve,"aria-label":"Density",children:Tt[v]}),e.jsx(c,{size:"sm",variant:"outline",onClick:ie,"aria-label":"Refresh",children:e.jsx(Ze,{className:"w-4 h-4"})}),T&&e.jsx(c,{size:"sm",variant:x?"default":"outline",onClick:()=>{x&&ce()},"aria-label":"Inline Editing",title:x?"Cancel editing":"Inline editing enabled",children:e.jsx(Pt,{className:"w-4 h-4"})}),e.jsxs(c,{size:"sm",variant:"default",onClick:$e,"aria-label":"New",children:[e.jsx(pt,{className:"w-4 h-4 mr-1"})," New"]})]})]}),P&&e.jsx(ot,{open:Re,onOpenChange:se,children:P}),e.jsx("div",{className:`overflow-x-auto  rounded-xl ${Ee}`,children:e.jsxs(rt,{"data-table":!0,children:[e.jsx(ct,{children:a?.getHeaderGroups?.()?.map(t=>e.jsxs(N,{children:[m&&j&&e.jsx(ue,{className:"w-6",children:e.jsx(W,{checked:Ge(),onCheckedChange:We(),"aria-label":"Select all"})}),t.headers.map(s=>e.jsx(ue,{style:{width:s.getSize()},className:"font-bold",children:e.jsxs("div",{className:"flex flex-col gap-1 relative group",children:[e.jsxs("div",{className:s.column.getCanSort()?"flex items-center gap-1 cursor-pointer select-none group":"flex items-center gap-1",onClick:s.column.getToggleSortingHandler?.(),role:s.column.getCanSort()?"button":void 0,tabIndex:s.column.getCanSort()?0:void 0,"aria-label":s.column.getCanSort()?`Sort by ${s.column.columnDef.header||"column"}`:void 0,onKeyDown:n=>{s.column.getCanSort()&&(n.key==="Enter"||n.key===" ")&&s.column.toggleSorting()},children:[me(s.column.columnDef.header,s.getContext()),s.column.getCanSort()&&(s.column.getIsSorted()==="asc"?e.jsx(yt,{className:"w-4 h-4 text-muted-foreground ml-1"}):s.column.getIsSorted()==="desc"?e.jsx(jt,{className:"w-4 h-4 text-muted-foreground ml-1"}):e.jsx(Ct,{className:"w-4 h-4 text-muted-foreground ml-1 opacity-50 group-hover:opacity-100 transition-opacity"}))]}),X&&s.column.getCanFilter()&&e.jsx(Et,{value:s.column.getFilterValue()??"",onChange:n=>s.column.setFilterValue(n),placeholder:`Filter by ${s.column.columnDef.header||""}`}),s.column.getCanResize?.()&&e.jsx("div",{onMouseDown:s.getResizeHandler(),onTouchStart:s.getResizeHandler(),className:"absolute right-0 top-0 h-full w-1.5 cursor-col-resize select-none bg-transparent group-hover:bg-primary/30 transition-colors",style:{userSelect:"none",touchAction:"none"}})]})},s.id))]},t.id))}),e.jsx(dt,{children:H||Te?e.jsx(N,{children:e.jsx(k,{colSpan:u.length+(m?1:0),children:e.jsx(q,{rowCount:10,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0})})}):a?.getRowModel?.()?.rows?.length===0?e.jsx(N,{children:e.jsx(k,{colSpan:u.length+(m?1:0),className:"text-center",children:"No data"})}):a?.getRowModel?.()?.rows?.map(t=>e.jsxs(p.Fragment,{children:[e.jsxs(N,{"data-state":t.getIsSelected()?"selected":void 0,className:ye&&t.index%2===1?"bg-muted/50":void 0,children:[m&&j&&e.jsx(k,{children:e.jsx(W,{checked:qe(t),onCheckedChange:Le(t),"aria-label":"Select row"})}),t.getVisibleCells().map(s=>e.jsx(k,{children:e.jsx(_e,{cell:s})},s.id))]}),y&&t.getIsExpanded()&&z&&e.jsx(N,{children:e.jsx(k,{colSpan:u.length+(m?1:0),children:z(t)})})]},t.id))})]})}),e.jsxs("div",{"data-slot":"data-grid-pagination",className:"flex flex-wrap flex-col sm:flex-row justify-between items-center gap-2.5 py-2.5 sm:py-0 grow",children:[e.jsx("div",{className:"flex flex-wrap items-center space-x-2.5 py-3 px-3",children:H?e.jsx(q,{rowCount:10,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Rows per page"}),e.jsxs(et,{value:`${h}`,indicatorPosition:"right",onValueChange:t=>{const s=Number(t);Q(s),J(0)},children:[e.jsx(tt,{className:"w-fit",size:"sm",children:e.jsx(st,{placeholder:`${h}`})}),e.jsx(at,{side:"top",className:"min-w-[50px]",children:[5,10,25,50,100].map(t=>e.jsx(nt,{value:`${t}`,children:t},t))})]})]})}),e.jsx("div",{className:"flex flex-col sm:flex-row justify-center sm:justify-end items-center gap-2.5 pt-2.5 sm:pt-0 order-1 sm:order-2",children:H?e.jsx(q,{rowCount:10,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-sm text-muted-foreground text-nowrap order-2 sm:order-1",children:`${g*h+1} - ${Math.min((g+1)*h,i.totalCount)} of ${i.totalCount}`}),a?.getPageCount?.()>1&&e.jsxs("div",{className:"flex items-center space-x-1 order-1 sm:order-2",children:[e.jsxs(c,{size:"sm",variant:"ghost",className:"size-7 p-0 text-sm rtl:transform rtl:rotate-180",onClick:()=>a?.previousPage?.(),disabled:!a?.getCanPreviousPage?.(),children:[e.jsx("span",{className:"sr-only",children:"Go to previous page"}),e.jsx(wt,{className:"size-4"})]}),(()=>{const t=a?.getPageCount?.()??0,s=5,n=Math.floor(g/s)*s,r=Math.min(n+s,t),d=[];n>0&&d.push(e.jsx(c,{size:"sm",className:"size-7 p-0 text-sm",variant:"ghost",onClick:()=>a?.setPageIndex?.(n-1),children:"..."},"ellipsis-prev"));for(let l=n;l<r;l++)d.push(e.jsx(c,{size:"sm",variant:"ghost",className:`size-7 p-0 text-sm text-muted-foreground${g===l?" bg-accent text-accent-foreground":""}`,onClick:()=>{g!==l&&a?.setPageIndex?.(l)},children:l+1},l));return r<t&&d.push(e.jsx(c,{className:"size-7 p-0 text-sm",variant:"ghost",size:"sm",onClick:()=>a?.setPageIndex?.(r),children:"..."},"ellipsis-next")),d})(),e.jsxs(c,{size:"sm",variant:"ghost",className:"size-7 p-0 text-sm rtl:transform rtl:rotate-180",onClick:()=>a?.nextPage?.(),disabled:!a?.getCanNextPage?.(),children:[e.jsx("span",{className:"sr-only",children:"Go to next page"}),e.jsx(lt,{className:"size-4"})]})]})]})})]})]})}const Et=({value:u,onChange:F,placeholder:D})=>{const[M,m]=p.useState(u),y=ge(M,300);return p.useEffect(()=>{m(u)},[u]),p.useEffect(()=>{y!==u&&F(y)},[y]),e.jsx(L,{value:M,onChange:z=>m(z.target.value),placeholder:D,className:"w-full h-7 text-xs font-normal"})};export{Jt as D};
//# sourceMappingURL=data-grid-DZ2U-5jU.js.map
