import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2 } from "lucide-react";
import React from "react";
import type { FieldArrayWithId, UseFieldArrayAppend } from "react-hook-form";
import { useFormContext } from "react-hook-form";
import type { FormValues } from "./approval-template-dialog";

type StagesTabProps = {
  fields: FieldArrayWithId<FormValues, "stages", "id">[];
  append: UseFieldArrayAppend<FormValues, "stages">;
  remove: (index: number) => void;
};

const StagesTab: React.FC<StagesTabProps> = ({ fields, append, remove }) => {
  const { register } = useFormContext();

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <div className="font-semibold">Stages</div>
        <Button type="button" onClick={() => append({ id: "00000000-0000-0000-0000-000000000000", approvalTemplateId: "", approverId: "" })} size="sm">
          Add Stage
        </Button>
      </div>
      <div className="space-y-2">
        {fields.length === 0 && <div className="text-muted-foreground text-sm">No stages added.</div>}
        {fields.map((field, idx) => (
          <div key={field.id} className="flex flex-col md:flex-row items-center gap-2 border rounded p-2">
            <span className="font-semibold">Stage #{idx + 1}</span>
            <input type="hidden" {...register(`stages.${idx}.id`)} />
            <Input
              {...register(`stages.${idx}.approvalTemplateId`, { required: true })}
              placeholder="Template ID"
              className="w-32"
            />
            <Input
              {...register(`stages.${idx}.approverId`, { required: true })}
              placeholder="Approver ID"
              className="w-32"
            />
            <Input
              type="date"
              {...register(`stages.${idx}.actionDate`)}
              placeholder="Action Date"
              className="w-36"
            />
            <Input
              {...register(`stages.${idx}.documentId`)}
              placeholder="Document ID"
              className="w-32"
            />
            <Input
              {...register(`stages.${idx}.requesterId`)}
              placeholder="Requester ID"
              className="w-32"
            />
            <Input
              type="date"
              {...register(`stages.${idx}.requestDate`)}
              placeholder="Request Date"
              className="w-36"
            />
            <Input
              {...register(`stages.${idx}.status`)}
              placeholder="Status"
              className="w-24"
            />
            <Input
              {...register(`stages.${idx}.notes`)}
              placeholder="Notes"
              className="w-32"
            />
            <Button type="button" variant="ghost" size="icon" onClick={() => remove(idx)} aria-label="Remove Stage">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StagesTab; 