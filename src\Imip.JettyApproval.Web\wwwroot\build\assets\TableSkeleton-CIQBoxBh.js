import{j as s}from"./vendor-6tJeyfYI.js";import{S as e}from"./skeleton-DAOxGMKm.js";import{o as w}from"./app-layout-rNt37hVL.js";function f({rowCount:c=10,columnCount:t=4,hasTitle:j=!0,hasSearch:r=!0,hasFilters:x=!0,hasPagination:n=!0,hasActions:i=!0}){const h=Array.from({length:c},(a,l)=>l),m=Array.from({length:t},(a,l)=>l);return s.jsxs(w,{className:"space-y-4 py-4",children:[j&&s.jsxs("div",{className:"flex items-center justify-between mb-6 px-4",children:[s.jsx(e,{className:"h-8 w-48"})," ",i&&s.jsxs("div",{className:"flex space-x-2",children:[s.jsx(e,{className:"h-9 w-24"})," ",s.jsx(e,{className:"h-9 w-24"})," "]})]}),(r||x)&&s.jsxs("div",{className:"flex items-center justify-between mb-4 px-4",children:[r&&s.jsx(e,{className:"h-10 w-64"})," ",x&&s.jsx(e,{className:"h-10 w-32"})," "]}),s.jsxs("div",{className:"flex w-full border-b pb-2 px-4",children:[s.jsx(e,{className:"h-6 w-8 mr-4"})," ",m.map(a=>s.jsx(e,{className:`h-6 ${a===m.length-1?"w-1/6":"w-1/4 mr-4"}`},`header-${a}`))]}),h.map(a=>s.jsxs("div",{className:"flex w-full py-3 border-b px-4",children:[s.jsx(e,{className:"h-5 w-5 mr-4"})," ",m.map(l=>s.jsx(e,{className:`h-5 ${l===m.length-1?"w-1/6":"w-1/4 mr-4"}`},`cell-${a}-${l}`))]},`row-${a}`)),n&&s.jsxs("div",{className:"flex items-center justify-between pt-4 px-4",children:[s.jsx(e,{className:"h-5 w-32"})," ",s.jsxs("div",{className:"flex space-x-1",children:[s.jsx(e,{className:"h-8 w-8"})," ",s.jsx(e,{className:"h-8 w-8"})," ",s.jsx(e,{className:"h-8 w-8"})," ",s.jsx(e,{className:"h-8 w-8"})," "]})]})]})}export{f as T};
//# sourceMappingURL=TableSkeleton-CIQBoxBh.js.map
