import{h as v,j as e,a as l}from"./vendor-6tJeyfYI.js";import{D as I}from"./DataTable-CDIoPElA.js";import{F as R}from"./filter-sort-bar-MpsapXP_.js";import{j as S,t as w,E as C,T as q,B as u,R as x,A as T}from"./app-layout-rNt37hVL.js";import{T as E}from"./table-skeleton-CE69MDqJ.js";import{m as F}from"./App-DnhJzTNn.js";import{P as L}from"./plus-PD53KOti.js";import"./table-BKSoE52x.js";import"./input-DlXlkYlT.js";import"./useDebounce-BdjXjarW.js";import"./index-X4QX0AQ3.js";import"./popover-ChFN9yvN.js";import"./radix-e4nK4mWk.js";import"./tiny-invariant-CopsF_GD.js";import"./checkbox-D1loOtZt.js";import"./index-CaiFFM4D.js";import"./badge-DWaCYvGm.js";import"./arrow-up-DDQ17ADi.js";import"./skeleton-DAOxGMKm.js";const A=(t,a,r,i)=>v({queryKey:["jetty-request-items",t,a,JSON.stringify(r),i],queryFn:async()=>{const o={skipCount:t*a,maxResultCount:a,sorting:i,filterGroup:r};try{return(await S({body:o})).data||{items:[],totalCount:0}}catch(s){let n="Unknown error occurred while loading Jetty Request Items";return typeof s=="object"&&s&&"message"in s&&typeof s.message=="string"&&(n=s.message??n),w({title:"Error loading Jetty Request Items",description:n,variant:"destructive"}),{items:[],totalCount:0}}},retry:1,retryDelay:1e3}),D=[{value:"tenantName",label:"Tenant Name"},{value:"itemName",label:"Item Name"},{value:"qty",label:"Quantity"},{value:"uoM",label:"Unit of Measurement"},{value:"status",label:"Status"},{value:"jettyRequestId",label:"Jetty Request ID"}],K=[{value:"Equals",label:"Equals"},{value:"Contains",label:"Contains"},{value:"NotEquals",label:"Not Equals"},{value:"GreaterThan",label:">"},{value:"LessThan",label:"<"}],y=[{accessorKey:"tenantName",header:"Tenant Name",cell:t=>t.getValue()??"-"},{accessorKey:"itemName",header:"Item Name",cell:t=>t.getValue()??"-"},{accessorKey:"qty",header:"Quantity",cell:t=>{const a=t.getValue();return a!==void 0?a.toString():"-"}},{accessorKey:"uoM",header:"Unit of Measurement",cell:t=>t.getValue()??"-"},{accessorKey:"status",header:"Status",cell:t=>t.getValue()??"-"},{accessorKey:"notes",header:"Notes",cell:t=>{const a=t.getValue();return a?a.length>50?`${a.substring(0,50)}...`:a:"-"}},{accessorKey:"creationTime",header:"Created",cell:t=>t.getValue()?new Date(t.getValue()).toLocaleDateString():"-"}],V=()=>{const[t,a]=l.useState({pageIndex:0,pageSize:10}),[r,i]=l.useState([]),[o,s]=l.useState([]),[n,m]=l.useState(!1),f=l.useMemo(()=>{if(r.length!==0)return{operator:"And",conditions:r}},[r]),b=l.useMemo(()=>{if(o.length!==0)return o.map(h=>`${h.field} ${h.direction}`).join(",")},[o]),{data:d,isLoading:c,error:p,refetch:j}=A(t.pageIndex,t.pageSize,f,b),g=async()=>{m(!0);try{await j()}finally{m(!1)}},N=()=>{F.visit("/application/items/create")};return p?e.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 text-destructive mb-4",children:[e.jsx(q,{className:"h-5 w-5"}),e.jsx("h2",{className:"text-lg font-semibold",children:"Error Loading Items"})]}),e.jsx("p",{className:"text-muted-foreground mb-4",children:p.message||"An unexpected error occurred while loading the items."}),e.jsxs(u,{onClick:g,variant:"outline",children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Try Again"]})]}):e.jsxs("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:[e.jsx("div",{className:"text-xl font-bold px-2 pt-2 pb-1",children:"Jetty Request Items"}),e.jsx(R,{filterFields:D,operators:K,filters:r,sorts:o,onFiltersChange:i,onSortsChange:s,children:e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsx(u,{onClick:g,variant:"outline",size:"icon",className:"h-10 w-10",disabled:c||n,children:e.jsx(x,{className:`h-4 w-4 ${n?"animate-spin":""}`})}),e.jsxs(u,{onClick:N,className:"flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base",size:"lg",children:[e.jsx(L,{className:"h-5 w-5"})," New Item"]})]})}),c?e.jsx(E,{columns:y}):e.jsx(I,{title:"",columns:y,data:d?.items??[],totalCount:d?.totalCount??0,isLoading:c,manualPagination:!0,pageSize:t.pageSize,onPaginationChange:a,hideDefaultFilterbar:!0,enableRowSelection:!1,manualSorting:!0})]})},J=()=>e.jsx(C,{children:e.jsx(V,{})}),se=()=>e.jsx(T,{children:e.jsx("div",{className:"flex flex-col space-y-4 p-4",children:e.jsx(J,{})})});export{se as default};
//# sourceMappingURL=page-CEbW1OEi.js.map
