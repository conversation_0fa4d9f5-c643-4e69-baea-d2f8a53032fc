import { ekbProxyService } from '@/services/ekbProxyService';
import type { CreateUpdateImportVesselDto, CreateUpdateVesselItemDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { ImportVesselFormWithData } from '@/components/jetty/vessel/import/import-vessel-form';
import type { ImportVesselHeaderForm } from '@/components/jetty/vessel/import/import-vessel-header-schema';
import type { ImportVesselItemForm } from '@/components/jetty/vessel/import/import-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { toDatetimeLocalString } from '@/lib/utils/date-convert';
import { Head, usePage } from '@inertiajs/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const ImportVesselEditPage = () => {
  const { t } = useTranslation();
  const { props } = usePage();
  const { toast } = useToast();
  const id = typeof props.id === 'string' ? props.id : undefined;
  const queryClient = useQueryClient();

  const {
    data: vesselData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['import-vessel', id],
    queryFn: async () => {
      if (!id) return null;
      const response = await ekbProxyService.getImportVesselWithItems(id);
      return response.data;
    },
    enabled: !!id,
  });

  const initialHeader: Partial<CreateUpdateImportVesselDto> = vesselData
    ? {
      docNum: vesselData.docNum ?? 0,
      voyage: vesselData.voyage ?? '',
      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? ''),
      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? ''),
      vesselId: vesselData.vesselId ?? '',
      jettyId: vesselData.jettyId ?? '',
      portOriginId: vesselData.portOriginId ?? '',
      destinationPortId: vesselData.destinationPortId ?? '',
      postingDate: vesselData.postingDate ?? '',
      asideDate: toDatetimeLocalString(vesselData.asideDate ?? ''),
      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? ''),
      deleted: vesselData.deleted ?? '',
      docType: vesselData.docType ?? '',
      isChange: vesselData.isChange ?? '',
      isLocked: vesselData.isLocked ?? '',
      createdBy: vesselData.createdBy ?? '',
      docStatus: vesselData.docStatus ?? '',
      statusBms: vesselData.statusBms ?? '',
      transType: vesselData.transType ?? '',
      concurrencyStamp: vesselData.concurrencyStamp ?? '',
    }
    : {
      deleted: '',
      docType: '',
      isChange: '',
      isLocked: '',
      createdBy: '',
      docStatus: '',
      statusBms: '',
      transType: '',
      concurrencyStamp: '',
    };

  const initialItems: CreateUpdateVesselItemDto[] = vesselData?.items
    ? vesselData.items.map(item => {
      let unitWeight: string | null | undefined = null;
      if (item.unitWeight != null) {
        if (typeof item.unitWeight === 'string') {
          unitWeight = item.unitWeight !== '' ? item.unitWeight : null;
        } else {
          unitWeight = String(item.unitWeight);
        }
      }
      let grossWeight: number | null = null;
      if (item.grossWeight != null) {
        if (typeof item.grossWeight === 'string') {
          grossWeight = item.grossWeight !== '' ? Number(item.grossWeight) : null;
        } else {
          grossWeight = item.grossWeight;
        }
      }
      return {
        itemName: item.itemName ?? null,
        itemQty: item.itemQty ?? 0,
        unitQty: item.unitQty ?? null,
        remarks: item.remarks ?? null,
        ajuNo: item.ajuNo ?? null,
        regDate: item.regDate ? item.regDate : null,
        regNo: item.regNo ?? null,
        grossWeight,
        unitWeight,
        shippingInstructionNo: item.shippingInstructionNo ?? null,
        shippingInstructionDate: item.shippingInstructionDate ?? null,
        letterNo: item.letterNo ?? null,
        letterDate: item.letterDate ?? null,
        status: item.status ?? null,
        regType: item.regType ?? null,
        attachments: item.attachments ?? [],
        tenant: item.tenantName ?? null,
        tenantId: item.tenantId ?? '',
        businessPartner: item.businessPartner?.name ?? null,
        businessPartnerId: item.businessPartnerId ?? null,
        concurrencyStamp: item.concurrencyStamp ?? undefined,
        id: item.id ?? undefined,
      };
    })
    : [];

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: ImportVesselHeaderForm; items: ImportVesselItemForm[] }) => {
      if (!id) throw new Error('No ID provided');
      const response = await ekbProxyService.updateImportVessel(id, {
        ...header,
        docNum: Number(header.docNum),
        deleted: header.deleted ?? '', // or your default
        docType: header.docType ?? '',
        isChange: header.isChange ?? '',
        isLocked: header.isLocked ?? '',
        createdBy: header.createdBy ?? '',
        docStatus: header.docStatus ?? 'Open',
        statusBms: header.statusBms ?? '',
        transType: header.transType ?? '',
        concurrencyStamp: header.concurrencyStamp ?? '',
        items: items.map(item => ({
          ...item,
          regDate: item.regDate ? item.regDate : null,
          tenantId: item.tenantId || '',
          businessPartnerId: item.businessPartnerId || '',
        })),
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Import vessel updated.', variant: 'success' });
      queryClient.invalidateQueries({ queryKey: ['import-vessel', id] });
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;

  return (
    <ImportVesselFormWithData
      mode="edit"
      title={t('pages.vessel.edit.import')}
      initialHeader={initialHeader}
      initialItems={initialItems}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
      queryClient={queryClient}
      showAddLineButton={false}
    />
  );
};

export default function ImportVesselEdit() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.edit.import')} />
      <ImportVesselEditPage />
    </AppLayout>
  );
} 