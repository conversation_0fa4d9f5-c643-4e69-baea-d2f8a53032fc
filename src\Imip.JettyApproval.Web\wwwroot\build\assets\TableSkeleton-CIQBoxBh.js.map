{"version": 3, "file": "TableSkeleton-CIQBoxBh.js", "sources": ["../../../../../frontend/src/components/ui/TableSkeleton.tsx"], "sourcesContent": ["import { Skeleton } from '@/components/ui/skeleton'\r\nimport { Card } from '@/components/ui/card'\r\n\r\ninterface TableSkeletonProps {\r\n  rowCount?: number\r\n  columnCount?: number\r\n  hasTitle?: boolean\r\n  hasSearch?: boolean\r\n  hasFilters?: boolean\r\n  hasPagination?: boolean\r\n  hasActions?: boolean\r\n}\r\n\r\nexport function TableSkeleton({\r\n  rowCount = 10,\r\n  columnCount = 4,\r\n  hasTitle = true,\r\n  hasSearch = true,\r\n  hasFilters = true,\r\n  hasPagination = true,\r\n  hasActions = true,\r\n}: TableSkeletonProps) {\r\n  // Create arrays for rows and columns\r\n  const rows = Array.from({ length: rowCount }, (_, i) => i)\r\n  const columns = Array.from({ length: columnCount }, (_, i) => i)\r\n  \r\n  return (\r\n    <Card className=\"space-y-4 py-4\">\r\n      {/* Table header skeleton */}\r\n      {hasTitle && (\r\n        <div className=\"flex items-center justify-between mb-6 px-4\">\r\n          <Skeleton className=\"h-8 w-48\" /> {/* Title */}\r\n          {hasActions && (\r\n            <div className=\"flex space-x-2\">\r\n              <Skeleton className=\"h-9 w-24\" /> {/* Button */}\r\n              <Skeleton className=\"h-9 w-24\" /> {/* Button */}\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Search and filter bar skeleton */}\r\n      {(hasSearch || hasFilters) && (\r\n        <div className=\"flex items-center justify-between mb-4 px-4\">\r\n          {hasSearch && <Skeleton className=\"h-10 w-64\" />} {/* Search bar */}\r\n          {hasFilters && <Skeleton className=\"h-10 w-32\" />} {/* Filter button */}\r\n        </div>\r\n      )}\r\n      \r\n      {/* Table header row */}\r\n      <div className=\"flex w-full border-b pb-2 px-4\">\r\n        <Skeleton className=\"h-6 w-8 mr-4\" /> {/* Checkbox */}\r\n        {columns.map((col) => (\r\n          <Skeleton \r\n            key={`header-${col}`} \r\n            className={`h-6 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} \r\n          />\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Table rows */}\r\n      {rows.map((row) => (\r\n        <div key={`row-${row}`} className=\"flex w-full py-3 border-b px-4\">\r\n          <Skeleton className=\"h-5 w-5 mr-4\" /> {/* Checkbox */}\r\n          {columns.map((col) => (\r\n            <Skeleton \r\n              key={`cell-${row}-${col}`} \r\n              className={`h-5 ${col === columns.length - 1 ? 'w-1/6' : 'w-1/4 mr-4'}`} \r\n            />\r\n          ))}\r\n        </div>\r\n      ))}\r\n      \r\n      {/* Pagination skeleton */}\r\n      {hasPagination && (\r\n        <div className=\"flex items-center justify-between pt-4 px-4\">\r\n          <Skeleton className=\"h-5 w-32\" /> {/* Page info */}\r\n          <div className=\"flex space-x-1\">\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n            <Skeleton className=\"h-8 w-8\" /> {/* Pagination button */}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </Card>\r\n  )\r\n}\r\n"], "names": ["TableSkeleton", "rowCount", "columnCount", "hasTitle", "hasSearch", "hasFilters", "hasPagination", "hasActions", "rows", "_", "i", "columns", "jsxs", "Card", "jsx", "Skeleton", "col", "row"], "mappings": "iIAaO,SAASA,EAAc,CAC5B,SAAAC,EAAW,GACX,YAAAC,EAAc,EACd,SAAAC,EAAW,GACX,UAAAC,EAAY,GACZ,WAAAC,EAAa,GACb,cAAAC,EAAgB,GAChB,WAAAC,EAAa,EACf,EAAuB,CAEf,MAAAC,EAAO,MAAM,KAAK,CAAE,OAAQP,GAAY,CAACQ,EAAGC,IAAMA,CAAC,EACnDC,EAAU,MAAM,KAAK,CAAE,OAAQT,GAAe,CAACO,EAAGC,IAAMA,CAAC,EAG7D,OAAAE,EAAA,KAACC,EAAK,CAAA,UAAU,iBAEb,SAAA,CACCV,GAAAS,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IAChCR,GACCK,EAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IACjCD,EAAAA,IAACC,EAAS,CAAA,UAAU,UAAW,CAAA,EAAE,GAAA,CACnC,CAAA,CAAA,EAEJ,GAIAX,GAAaC,IACZO,EAAA,KAAA,MAAA,CAAI,UAAU,8CACZ,SAAA,CAAaR,GAAAU,EAAAA,IAACC,EAAS,CAAA,UAAU,WAAY,CAAA,EAAG,IAChDV,GAAcS,EAAAA,IAACC,EAAS,CAAA,UAAU,WAAY,CAAA,EAAG,GAAA,EACpD,EAIFH,EAAAA,KAAC,MAAI,CAAA,UAAU,iCACb,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,cAAe,CAAA,EAAE,IACpCJ,EAAQ,IAAKK,GACZF,EAAA,IAACC,EAAA,CAEC,UAAW,OAAOC,IAAQL,EAAQ,OAAS,EAAI,QAAU,YAAY,EAAA,EADhE,UAAUK,CAAG,EAGrB,CAAA,CAAA,EACH,EAGCR,EAAK,IAAKS,GACRL,EAAAA,KAAA,MAAA,CAAuB,UAAU,iCAChC,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,cAAe,CAAA,EAAE,IACpCJ,EAAQ,IAAKK,GACZF,EAAA,IAACC,EAAA,CAEC,UAAW,OAAOC,IAAQL,EAAQ,OAAS,EAAI,QAAU,YAAY,EAAA,EADhE,QAAQM,CAAG,IAAID,CAAG,EAG1B,CAAA,CAPO,CAAA,EAAA,OAAOC,CAAG,EAQpB,CACD,EAGAX,GACCM,EAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,UAAW,CAAA,EAAE,IACjCH,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAACE,EAAAA,IAAAC,EAAA,CAAS,UAAU,SAAU,CAAA,EAAE,IAChCD,EAAAA,IAACC,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,IAChCD,EAAAA,IAACC,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,IAChCD,EAAAA,IAACC,EAAS,CAAA,UAAU,SAAU,CAAA,EAAE,GAAA,CAClC,CAAA,CAAA,CACF,CAAA,CAAA,EAEJ,CAEJ"}