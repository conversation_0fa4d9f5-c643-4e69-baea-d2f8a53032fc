import { useCallback } from 'react';
import { oidcManager } from '../lib/oidc-init';

export const useOidc = () => {
  const refreshTokenSilently = useCallback(async (): Promise<boolean> => {
    return await oidcManager.tryRefreshToken();
  }, []);

  const fetchWithTokenRefresh = useCallback(async (
    url: string, 
    options: RequestInit = {}
  ): Promise<Response> => {
    return await oidcManager.fetchWithTokenRefresh(url, options);
  }, []);

  const checkTokenStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/token/status', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        return await response.json();
      }
      
      return null;
    } catch (error) {
      console.error('Error checking token status:', error);
      return null;
    }
  }, []);

  return {
    refreshTokenSilently,
    fetchWithTokenRefresh,
    checkTokenStatus,
  };
}; 