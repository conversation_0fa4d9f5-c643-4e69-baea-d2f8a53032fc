{"version": 3, "file": "page-BkQnXlAs.js", "sources": ["../../../../../frontend/src/pages/export/create/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { ExportVesselFormWithData } from '@/components/jetty/vessel/export/export-vessel-form';\r\nimport type { ExportVesselHeaderForm } from '@/components/jetty/vessel/export/export-vessel-header-schema';\r\nimport type { ExportVesselItemForm } from '@/components/jetty/vessel/export/export-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { Head, router } from '@inertiajs/react';\r\nimport { useMutation } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ExportVesselCreatePage = () => {\r\n  const { t } = useTranslation();\r\n  const { toast } = useToast();\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: ExportVesselHeaderForm; items: ExportVesselItemForm[] }) => {\r\n      const response = await ekbProxyService.createExportVessel({\r\n        ...header,\r\n        vesselId: header.vesselId ? String(header.vesselId) : '',\r\n        jettyId: header.jettyId ? String(header.jettyId) : '',\r\n        concurrencyStamp: header.concurrencyStamp ? String(header.concurrencyStamp) : '',\r\n        items: items.map(item => ({\r\n          ...item,\r\n          createdBy: '',\r\n          docType: '',\r\n          isScan: '',\r\n          isOriginal: '',\r\n          isActive: true,\r\n          isDeleted: false,\r\n          isSend: '',\r\n          isFeOri: '',\r\n          isFeSend: '',\r\n          isChange: '',\r\n          isFeChange: '',\r\n          isFeActive: '',\r\n          deleted: '',\r\n          isUrgent: '',\r\n          tenantId: item.tenantId || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n          concurrencyStamp: item.concurrencyStamp || '',\r\n        })),\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: (data) => {\r\n      toast({ title: 'Success', description: 'Export vessel created.', variant: 'success' });\r\n      if (data && data.id) {\r\n        router.visit(`/export/edit/${data.id}`);\r\n      }\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  return (\r\n    <ExportVesselFormWithData\r\n      mode=\"create\"\r\n      title={t('pages.vessel.create.export')}\r\n      initialHeader={{} as Partial<ExportVesselHeaderForm>}\r\n      initialItems={[]}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function ExportVesselCreate() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.create.export')} />\r\n      <ExportVesselCreatePage />\r\n    </AppLayout>\r\n  );\r\n}"], "names": ["ExportVesselCreatePage", "t", "useTranslation", "toast", "useToast", "mutation", "useMutation", "header", "items", "response", "ekbProxyService", "item", "data", "router", "err", "handleSubmit", "jsx", "ExportVesselFormWithData", "ExportVesselCreate", "AppLayout", "Head"], "mappings": "kvBAWA,MAAMA,EAAyB,IAAM,CAC7B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EAErBC,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA+E,CACpG,MAAAC,EAAW,MAAMC,EAAgB,mBAAmB,CACxD,GAAGH,EACH,SAAUA,EAAO,SAAW,OAAOA,EAAO,QAAQ,EAAI,GACtD,QAASA,EAAO,QAAU,OAAOA,EAAO,OAAO,EAAI,GACnD,iBAAkBA,EAAO,iBAAmB,OAAOA,EAAO,gBAAgB,EAAI,GAC9E,MAAOC,EAAM,IAAaG,IAAA,CACxB,GAAGA,EACH,UAAW,GACX,QAAS,GACT,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,UAAW,GACX,OAAQ,GACR,QAAS,GACT,SAAU,GACV,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,QAAS,GACT,SAAU,GACV,SAAUA,EAAK,UAAY,GAC3B,kBAAmBA,EAAK,mBAAqB,GAC7C,iBAAkBA,EAAK,kBAAoB,EAAA,EAC3C,CAAA,CACH,EACD,GAAIF,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAYG,GAAS,CACnBT,EAAM,CAAE,MAAO,UAAW,YAAa,yBAA0B,QAAS,UAAW,EACjFS,GAAQA,EAAK,IACfC,EAAO,MAAM,gBAAgBD,EAAK,EAAE,EAAE,CAE1C,EACA,QAAUE,GAAoC,CACtCX,EAAA,CACJ,MAAOW,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOR,EAAgCC,IAAkC,CAC5F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAGE,OAAAQ,EAAA,IAACC,EAAA,CACC,KAAK,SACL,MAAOhB,EAAE,4BAA4B,EACrC,cAAe,CAAC,EAChB,aAAc,CAAC,EACf,SAAUc,EACV,aAAcV,EAAS,SAAA,CACzB,CAEJ,EAEA,SAAwBa,GAAqB,CACrC,KAAA,CAAE,EAAAjB,CAAE,EAAIC,EAAe,EAC7B,cACGiB,EACC,CAAA,SAAA,CAAAH,EAAA,IAACI,EAAK,CAAA,MAAOnB,EAAE,4BAA4B,CAAG,CAAA,QAC7CD,EAAuB,CAAA,CAAA,CAAA,EAC1B,CAEJ"}