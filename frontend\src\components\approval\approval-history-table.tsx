import type { ApprovalStageDto } from '@/client';
import { postApiApprovalStagesFilterList } from '@/client/sdk.gen';
import { Button } from '@/components/ui/button';
import { DataGrid } from '@/components/ui/data-grid';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/lib/useToast';
import { router } from '@inertiajs/react';
import type { ColumnDef } from '@tanstack/react-table';
import { ArrowUpRight } from 'lucide-react';
import React from "react";
import { useTranslation } from 'react-i18next';
import { ContentCard } from '../layout/content-card';

const ApprovalHistoryTable: React.FC = () => {
  const { toast } = useToast();
  const { t } = useTranslation();

  // Define columns with translations
  const approvalHistoryColumns: ColumnDef<ApprovalStageDto>[] = [
    {
      accessorKey: "vessel.vesselName",
      header: t('table.vesselName'),
      cell: (info: { getValue: () => unknown }) => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.voyage",
      header: t('table.voyage'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.vesselType",
      header: t('table.vesselType'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.vesselArrival",
      header: t('table.arrival'),
      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : "-",
    },
    {
      accessorKey: "vessel.destinationPort",
      header: t('table.destinationPort'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "status",
      header: t('table.status'),
      cell: (info: { getValue: () => unknown }) => {
        const status = info.getValue() as string | number;
        let statusText: string;
        let variant: "warning" | "success" | "destructive" | "secondary" | "primary";

        switch (status) {
          case 0:
            statusText = t('table.statusPending');
            variant = "warning";
            break;
          case 1:
            statusText = t('table.statusApproved');
            variant = "success";
            break;
          case 2:
            statusText = t('table.statusRejected');
            variant = "destructive";
            break;
          case 3:
            statusText = t('table.statusCancelled');
            variant = "secondary";
            break;
          default:
            statusText = t('table.statusUnknown');
            variant = "primary";
            break;
        }

        return <Badge variant={variant} size="sm">{statusText}</Badge>;
      },
    },
    {
      accessorKey: "requesterUserName",
      header: t('table.requester'),
      cell: (info: { getValue: () => unknown }) => info.getValue() ?? "-",
    },
    {
      accessorKey: "approverUserName",
      header: t('table.approver'),
      cell: (info: { getValue: () => unknown }) => info.getValue() ?? "-",
    },
    {
      accessorKey: "requestDate",
      header: t('table.requestDate'),
      cell: (info: { getValue: () => unknown }) => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
    },
    {
      id: "actions",
      header: t('table.actions'),
      cell: ({ row }) => {
        const vessel = row.original.vessel;
        return (
          <div className="flex space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    if (vessel && vessel.id) {
                      if (vessel.vesselType === 'Import') {
                        router.visit(`/import/edit/${vessel.id}`);
                      } else if (vessel.vesselType === 'Export') {
                        router.visit(`/export/edit/${vessel.id}`);
                      } else {
                        router.visit(`/local/edit/${vessel.id}`);
                      }
                    }
                  }}
                  aria-label="Details"
                >
                  <ArrowUpRight className="w-5 h-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>{t('table.viewDetails')}</TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <ContentCard>
      <DataGrid
        columns={approvalHistoryColumns}
        title={t('datagrid.approvalHistory')}
        queryKey={["approval-history"]}
        manualSorting={true}
        manualFiltering={true}
        autoSizeColumns={true}
        queryFn={async ({ pageIndex, pageSize }) => {
          try {
            const response = await postApiApprovalStagesFilterList({
              body: {
                maxResultCount: pageSize,
                skipCount: pageIndex * pageSize,
                // Add filterGroup or sorting as needed for history
              }
            });
            if (!response || !response.data) {
              throw new Error("Failed to fetch approval history");
            }
            return {
              items: response.data.items || [],
              totalCount: response.data.totalCount || 0,
            };
          } catch (err) {
            console.error("Error fetching approval history:", err);
            toast({
              title: "Error",
              description: "Failed to load approval history",
              variant: "destructive",
            });
            return { items: [], totalCount: 0 };
          }
        }}
      />
    </ContentCard>
    // </div>
  );
};

export default ApprovalHistoryTable; 