import{$ as D,h as $,j as e,a as C}from"./vendor-6tJeyfYI.js";import{B as V,M as H}from"./app-layout-rNt37hVL.js";import{F as K,a as m}from"./FormField-AGj4WUYd.js";import{I as b}from"./input-DlXlkYlT.js";import{H as ee,r as te}from"./ht-theme-main.min-DuylQxQp.js";import{o as X,s,n as ae,V as se,J as re,P as ne,D as oe,a as le}from"./types-B5GxFm4f.js";/* empty css                         */import{u as ie,F as de,C as O}from"./index.esm-BubGICDC.js";import{c as ce}from"./App-DnhJzTNn.js";import{A as me}from"./attachment-dialog-D7s9nIdd.js";function pe(t){return typeof t=="object"&&t!==null&&"_reactRootContainer"in t?t._reactRootContainer:void 0}function ue(t,n){typeof t=="object"&&t!==null&&(t._reactRootContainer=n)}const Z=t=>{if(!t)throw new Error("queryClient is required for renderAttachmentButton");return(n,l,i,j,o,U,p)=>{let x=pe(l);x||(x=ce.createRoot(l),ue(l,x));const W=()=>{const[Y,P]=D.useState(!1),A=n?.getSourceDataAtRow?.(i),y=A&&!Array.isArray(A)?A:{},k=y.vesselId||y.id,w=t.getQueryData(["import-vessel",k])?.items?.find(f=>f.id===y.id)?.attachments||n?.getDataAtRowProp(i,"attachments")||[],R=n?.getDataAtRowProp(i,"itemName")||"",F=y.id??"",u=y.docEntry??0,h=()=>{const f=n?.getSourceDataAtRow?.(i),r=f&&!Array.isArray(f)?f:{},I=r.vesselId||r.id;I?t.invalidateQueries({queryKey:["import-vessel",I]}):t.invalidateQueries({queryKey:["import-vessel"]}),setTimeout(()=>{if(n)try{const N=t.getQueryData(["import-vessel",I]);if(N&&N.items){const E=N.items,q=r.id,T=E.find(B=>B.id===q);T&&n.setDataAtRowProp(i,"attachments",T.attachments||[])}n.render(),setTimeout(()=>{if(n)try{x.render(D.createElement(W))}catch{}},50)}catch{}},100)};return D.createElement(D.Fragment,null,[D.createElement(V,{key:"button",size:"xs",variant:"success",type:"button",onClick:()=>P(!0),"aria-label":"View Attachments",disabled:!w||w.length===0,children:`Attachment (${w?.length||0})`}),D.createElement(me,{key:"dialog",open:Y,onOpenChange:P,attachments:w||[],title:`Attachments - ${R||"Item"}`,queryClient:t,referenceId:F,documentReferenceId:u,defaultTabName:"AGENT",docType:"Export",transType:"ExportDetails",tabName:"AGENT",onUploadSuccess:h})])};return x.render(D.createElement(W)),l}},_=(t,n,l)=>{const i=t.map(o=>o.name??"").filter(o=>o!==""),j=n.map(o=>o.name??"").filter(o=>o!=="");return[{data:"id",title:"Id",type:"text",width:200},{data:"docEntry",title:"DocEntry",type:"text",width:200},{data:"concurrencyStamp",title:"concurrencyStamp",type:"text",width:200},{data:"tenant",title:"Tenant",type:"autocomplete",width:140,source:i,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"businessPartner",title:"Business Partner",type:"autocomplete",width:290,source:j,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"itemName",title:"Item Name",wordWrap:!1,type:"text",width:200},{data:"itemQty",title:"Quantity",wordWrap:!1,type:"numeric",width:80},{data:"unitQty",title:"UOM",wordWrap:!1,type:"text",width:150},{data:"remarks",title:"Remark",wordWrap:!1,type:"text",width:120},{data:"letterNo",title:"Letter No",wordWrap:!1,type:"text",width:120},{data:"letterDate",title:"Letter Date",wordWrap:!1,type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"noBl",title:"No BL",wordWrap:!1,type:"text",width:120},{data:"dateBl",title:"Date BL",wordWrap:!1,type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"ajuNo",title:"AJU No",wordWrap:!1,type:"text",width:120},{data:"regNo",title:"Reg No",wordWrap:!1,type:"text",width:120},{data:"regDate",title:"Reg Date",wordWrap:!1,type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppbNo",title:"SPPB No",wordWrap:!1,type:"text",width:120},{data:"sppbDate",title:"SPPB Date",wordWrap:!1,type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppdNo",title:"SPPD No",wordWrap:!1,type:"text",width:120},{data:"sppdDate",title:"SPPD Date",wordWrap:!1,type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"grossWeight",title:"Gross Weight",wordWrap:!1,type:"numeric",width:100},{data:"unitWeight",title:"Unit Weight",wordWrap:!1,type:"text",width:100},{data:"attachments",title:"Attachment",width:100,renderer:Z(l),readOnly:!0,filterable:!1}]},he=X({docNum:s().min(1,"DocNum is required"),vesselId:s().min(1,"Vessel Name is required"),voyage:s().min(1,"Voyage is required"),postingDate:s().min(1,"Posting Date is required"),vesselArrival:s().min(1,"Arrival Date is required"),vesselDeparture:s().min(1,"Departure Date is required"),portOriginId:s().optional(),destinationPortId:s().optional(),deleted:s().optional(),docType:s().optional(),isChange:s().optional(),isLocked:s().optional(),createdBy:s().optional(),docStatus:s().optional(),statusBms:s().optional(),transType:s().optional(),concurrencyStamp:s().optional(),jettyId:s().min(1,"Jetty is required"),status:s().optional(),vesselType:s().optional(),shipment:s().optional(),portOrigin:s().optional(),destinationPort:s().optional(),asideDate:s().optional(),castOfDate:s().optional()}),xe=X({itemName:s().nullable().optional(),itemQty:ae().nullable().optional(),unitQty:s().nullable().optional(),remarks:s().nullable().optional(),tenant:s().nullable().optional(),tenantId:s().nullable().optional(),businessPartner:s().nullable().optional(),businessPartnerId:s().nullable().optional()});te();function G(t){return{docNum:t.docNum?.toString()??"",vesselId:t.vesselId??"",voyage:t.voyage??"",postingDate:t.postingDate??"",vesselArrival:t.vesselArrival??"",vesselDeparture:t.vesselDeparture??"",portOriginId:t.portOriginId??"",destinationPortId:t.destinationPortId??"",jettyId:t.jettyId??"",deleted:t.deleted??"",docType:t.docType??"",isChange:t.isChange??"",isLocked:t.isLocked??"",createdBy:t.createdBy??"",docStatus:t.docStatus??"",statusBms:t.statusBms??"",transType:t.transType??"",concurrencyStamp:t.concurrencyStamp??"",asideDate:t.asideDate??"",castOfDate:t.castOfDate??""}}const Ae=t=>{const{data:n=[],isLoading:l}=$({queryKey:["tenants"],queryFn:()=>H.filterTenants({page:1,maxResultCount:1e3}).then(o=>o.data?.items??[])}),{data:i=[],isLoading:j}=$({queryKey:["businessPartners"],queryFn:()=>H.filterBusinessPartners({page:1,maxResultCount:1e4}).then(o=>o.data?.items??[])});return l||j?e.jsx("div",{children:"Loading data..."}):e.jsx(ye,{...t,columns:_(n,i,t.queryClient),tenants:n,businessPartners:i,queryClient:t.queryClient})},ye=({mode:t,initialHeader:n,initialItems:l,onSubmit:i,columns:j,headerSchema:o=he,itemSchema:U=xe,isSubmitting:p=!1,title:x="Create Import Vessel",tenants:W,businessPartners:Y,queryClient:P,showAddLineButton:A=!0})=>{const y=j??_([],[],P),[k,L]=C.useState(l),[M,w]=C.useState([]),R=C.useRef(null),F=C.useRef(null),u=ie({resolver:le(o),defaultValues:G(n),mode:"onBlur"}),{register:h,handleSubmit:f,formState:{errors:r},reset:I}=u;C.useEffect(()=>{const a=String(n.docNum??"");t==="edit"&&a&&F.current!==a&&(I(G(n)),L(l.map(d=>({...d,concurrencyStamp:d.concurrencyStamp??void 0,id:d.id??void 0}))),F.current=a)},[n,l,t,I]);const N=a=>{const d=[];return a.forEach((S,c)=>{const g=U.safeParse(S);g.success?d[c]="":d[c]=Object.values(g.error.flatten().fieldErrors).flat().join(", ")}),w(d),d.every(S=>!S)},E=async a=>{const S=(R.current?.hotInstance?.getSourceData?.()??[]).map(c=>{const g={...c,concurrencyStamp:c.concurrencyStamp??void 0,id:c.id??void 0};if(c.tenant){const v=W.find(J=>J.name===c.tenant);v&&(g.tenantId=v.id||"")}if(c.businessPartner){const v=Y.find(J=>J.name===c.businessPartner);v&&(g.businessPartnerId=v.id||"")}const Q=l.find(v=>v.id===c.id);return Q&&Q.concurrencyStamp&&(g.concurrencyStamp=Q.concurrencyStamp),g});N(S)&&await i(a,S)},q=()=>{L(a=>[...a,{}])};function T(){}const B=y.map(a=>a.data==="id"?{...a,renderer:Z(P)}:a),z=[].find(a=>a.id===u.watch("jettyId"))||null;return e.jsx("div",{className:"w-full mx-auto",children:e.jsxs("div",{className:"bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-lg font-bold text-gray-800 dark:text-white",children:x}),e.jsx("div",{className:"h-1 w-16 bg-primary rounded mt-2 mb-4"})]}),e.jsx(de,{...u,children:e.jsxs("form",{onSubmit:f(E),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(K,{showDivider:!1,children:[e.jsxs(m,{label:"DocNum",labelWidth:"100px",children:[e.jsx(b,{...h("docNum"),disabled:t==="edit"}),r.docNum&&e.jsx("span",{className:"text-red-500 text-xs",children:r.docNum.message})]}),e.jsxs(m,{label:"Vessel Name",labelWidth:"100px",children:[e.jsx(O,{name:"vesselId",control:u.control,render:({field:a})=>e.jsx(se,{value:a.value,onValueChange:a.onChange,placeholder:"Select vessel...",disabled:p})}),r.vesselId&&e.jsx("span",{className:"text-red-500 text-xs",children:r.vesselId.message})]}),e.jsxs(m,{label:"Voyage",labelWidth:"100px",children:[e.jsx(b,{...h("voyage")}),r.voyage&&e.jsx("span",{className:"text-red-500 text-xs",children:r.voyage.message})]}),e.jsxs(m,{label:"Jetty",labelWidth:"100px",children:[e.jsx(O,{name:"jettyId",control:u.control,render:({field:a})=>e.jsx(re,{value:a.value,onValueChange:a.onChange,placeholder:"Select jetty...",disabled:p})}),r.jettyId&&e.jsx("span",{className:"text-red-500 text-xs",children:r.jettyId.message})]})]}),e.jsxs(K,{showDivider:!1,children:[e.jsxs(m,{label:"A/Side Date",labelWidth:"100px",children:[e.jsx(b,{type:"datetime-local",...h("asideDate")}),r.asideDate&&e.jsx("span",{className:"text-red-500 text-xs",children:r.asideDate.message})]}),e.jsxs(m,{label:"Cast Of Date",labelWidth:"100px",children:[e.jsx(b,{type:"datetime-local",...h("castOfDate")}),r.castOfDate&&e.jsx("span",{className:"text-red-500 text-xs",children:r.castOfDate.message})]}),e.jsxs(m,{label:"Arrival Date",labelWidth:"100px",children:[e.jsx(b,{type:"datetime-local",...h("vesselArrival")}),r.vesselArrival&&e.jsx("span",{className:"text-red-500 text-xs",children:r.vesselArrival.message})]}),e.jsxs(m,{label:"Departure Date",labelWidth:"100px",children:[e.jsx(b,{type:"datetime-local",...h("vesselDeparture")}),r.vesselDeparture&&e.jsx("span",{className:"text-red-500 text-xs",children:r.vesselDeparture.message})]})]}),e.jsxs(K,{showDivider:!1,children:[e.jsxs(m,{label:"Posting Date",labelWidth:"100px",children:[e.jsx(b,{type:"date",...h("postingDate")}),r.postingDate&&e.jsx("span",{className:"text-red-500 text-xs",children:r.postingDate.message})]}),e.jsxs(m,{label:"Port Origin",labelWidth:"100px",children:[e.jsx(O,{name:"portOriginId",control:u.control,render:({field:a})=>e.jsx(ne,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select port origin...",disabled:p})}),r.portOriginId&&e.jsx("span",{className:"text-red-500 text-xs",children:r.portOriginId.message})]}),e.jsxs(m,{label:"Destination Port",labelWidth:"100px",children:[e.jsx(O,{name:"destinationPortId",control:u.control,render:({field:a})=>e.jsx(oe,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select destination port...",disabled:p})}),r.destinationPortId&&e.jsx("span",{className:"text-red-500 text-xs",children:r.destinationPortId.message})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block font-medium mb-2",children:"Items"}),e.jsx(ee,{ref:R,themeName:"ht-theme-main",data:k,columns:B,colHeaders:B.map(a=>a.title).filter(a=>typeof a=="string"),rowHeaders:!0,height:"50vh",licenseKey:"non-commercial-and-evaluation",stretchH:"all",contextMenu:!0,manualColumnResize:!0,manualRowResize:!0,autoColumnSize:!1,autoRowSize:!1,startRows:1,dropdownMenu:!0,filters:!0,colWidths:80,hiddenColumns:{copyPasteEnabled:!0,indicators:!0,columns:[0,1,2]},width:"100%",persistentState:!0}),M.some(a=>a)&&e.jsx("div",{className:"mt-2 text-red-500 text-xs",children:M.map((a,d)=>a&&e.jsxs("div",{children:["Row ",d+1,": ",a]},d))})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[A&&e.jsx(V,{type:"button",variant:"outline",onClick:q,disabled:p,children:"+ Add Line"}),typeof z?.isCustomArea=="boolean"&&!z.isCustomArea&&e.jsx(V,{type:"button",variant:"primary",onClick:T,disabled:p,children:"Submit Approval"}),e.jsx(V,{type:"submit",disabled:p,children:p?t==="edit"?"Saving...":"Creating...":t==="edit"?"Save Changes":"Create"})]})]})})]})})};export{Ae as I};
//# sourceMappingURL=import-vessel-form-CB_uPBh9.js.map
