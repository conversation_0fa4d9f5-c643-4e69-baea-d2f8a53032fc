import{a as s,j as e}from"./vendor-6tJeyfYI.js";import"./dnd-CVW30V3W.js";import{E as D}from"./use-event-visibility-DzKSWdzr.js";import{A as y,o as w,q as x,r as h,s as u,S as J,c as j,d as v,e as f,f as C}from"./app-layout-rNt37hVL.js";import"./radix-e4nK4mWk.js";import"./index-X4QX0AQ3.js";import"./chevron-left-DJFXm33k.js";import"./checkbox-D1loOtZt.js";import"./dialog-BmEXyFlW.js";import"./input-DlXlkYlT.js";import"./popover-ChFN9yvN.js";import"./textarea-DwrdARTr.js";import"./plus-PD53KOti.js";import"./App-DnhJzTNn.js";const i=[{name:"Jetty A",events:[{id:"1",title:"Vessel A - Docking",start:new Date(2025,4,10,8,0),end:new Date(2025,4,10,12,0),color:"sky",location:"Jetty A"},{id:"2",title:"Maintenance",start:new Date(2025,4,11,14,0),end:new Date(2025,4,11,17,0),color:"rose",location:"Jetty A"},{id:"6",title:"Annual Inspection",start:new Date(2025,4,15),end:new Date(2025,4,16),allDay:!0,color:"orange",location:"Jetty A"},{id:"7",title:"Vessel D - Loading",start:new Date(2025,4,18,9,30),end:new Date(2025,4,18,16,0),color:"emerald",location:"Jetty A"}]},{name:"Jetty B",events:[{id:"3",title:"Vessel B - Loading",start:new Date(2025,4,12,9,0),end:new Date(2025,4,12,18,0),color:"amber",location:"Jetty B"},{id:"4",title:"Inspection",start:new Date(2025,4,13,10,0),end:new Date(2025,4,13,11,0),color:"violet",location:"Jetty B"},{id:"8",title:"Emergency Repair",start:new Date(2025,4,20,7,0),end:new Date(2025,4,20,20,0),color:"rose",location:"Jetty B"},{id:"9",title:"Vessel E - Unloading",start:new Date(2025,4,22,11,0),end:new Date(2025,4,23,10,0),color:"sky",location:"Jetty B"}]},{name:"Jetty C",events:[{id:"5",title:"Vessel C - Unloading",start:new Date(2025,4,14,7,0),end:new Date(2025,4,14,16,0),color:"emerald",location:"Jetty C"},{id:"10",title:"Routine Cleaning",start:new Date(2025,4,25,8,0),end:new Date(2025,4,25,12,0),color:"violet",location:"Jetty C"},{id:"11",title:"Vessel F - Docking",start:new Date(2025,4,28,6,0),end:new Date(2025,4,29,14,0),color:"amber",location:"Jetty C"}]}];function T(){const[l,r]=s.useState(i[0]?.name||null),[c,a]=s.useState([]),d=t=>{a(n=>[...n,t])},m=t=>{a(n=>n.map(o=>o.id===t.id?t:o))},p=t=>{a(n=>n.filter(o=>o.id!==t))};return s.useEffect(()=>{if(l){const t=i.find(n=>n.name===l);a(t?t.events:[])}else a([])},[l]),e.jsx(y,{children:e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(w,{children:[e.jsx(x,{children:e.jsx(h,{className:"text-2xl font-bold",children:"Jetty Schedule"})}),e.jsxs(u,{children:[e.jsxs("div",{className:"mb-4 flex items-center gap-4",children:[e.jsx("label",{htmlFor:"jetty-select",className:"font-medium",children:"Select Jetty:"}),e.jsxs(J,{onValueChange:r,value:l||"",children:[e.jsx(j,{id:"jetty-select",className:"w-[200px]",children:e.jsx(v,{placeholder:"Select a jetty"})}),e.jsx(f,{children:i.map(t=>e.jsx(C,{value:t.name,children:t.name},t.name))})]})]}),e.jsx(D,{events:c,initialView:"month",onEventAdd:d,onEventUpdate:m,onEventDelete:p})]})]})})})}export{T as default};
//# sourceMappingURL=page-3ek5xT9F.js.map
