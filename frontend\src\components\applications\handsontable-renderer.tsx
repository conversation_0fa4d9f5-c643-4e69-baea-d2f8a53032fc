import { deleteApiIdjasJettyRequestItemById, postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment } from '@/client/sdk.gen';
import type { ApplicationDocumentGenerationDto } from '@/client/types.gen';
import type { QueryClient } from '@tanstack/react-query';
import Handsontable from 'handsontable';
import type { TableRowData } from './handsontable-column';

/**
 * Generates and opens an application document for preview
 */
async function generateAndOpenDocument(
  jettyRequestItemId: string,
  onPreview: (documentSrc: string) => void,
  setLoading: (loading: boolean) => void
) {
  try {
    setLoading(true);

    const input: ApplicationDocumentGenerationDto = {
      jettyRequestItemId: jettyRequestItemId,
      generatePdf: true
    };

    const response = await postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment({
      body: input
    });

    if (response.data) {
      // The response.data is the custom object from the controller
      const result = response.data;
      if (result.streamUrl) {
        // Open the document in the preview dialog
        onPreview(result.streamUrl);
      } else {
        console.error('Failed to generate document:', result);
        alert('Failed to generate document. Please try again.');
      }
    } else {
      console.error('No response data received');
      alert('Failed to generate document. Please try again.');
    }
  } catch (error) {
    console.error('Error generating document:', error);
    alert('Error generating document. Please try again.');
  } finally {
    setLoading(false);
  }
}

export const renderPreviewButton = (
  tableData: TableRowData[],
  onPreview: (documentSrc: string) => void,
  loadingStates: Map<number, boolean>,
  setLoadingState: (row: number, loading: boolean) => void
) => (
  _instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }

  const hasId = rowData?.id;
  const isLoading = loadingStates.get(_row) || false;

  const buttonClass = hasId && !isLoading
    ? 'px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'
    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

  const buttonTitle = hasId
    ? (isLoading ? 'Generating document...' : 'Preview document')
    : 'Save the application first to enable preview';

  const disabledAttr = hasId && !isLoading ? '' : 'disabled';
  const buttonText = isLoading ? 'Loading...' : 'Preview';

  const previewButton = `<button class="${buttonClass}" data-row="${_row}" data-action="preview" title="${buttonTitle}" ${disabledAttr}>${buttonText}</button>`;
  td.innerHTML = previewButton;

  const previewBtn = td.querySelector('[data-action="preview"]');
  if (previewBtn && hasId && !isLoading) {
    previewBtn.addEventListener('click', async () => {
      const rowData = tableData[_row];
      console.log("rowData", rowData)
      if (rowData?.id) {
        await generateAndOpenDocument(
          rowData.id,
          onPreview,
          (loading) => setLoadingState(_row, loading)
        );
      }
    });
  }
};

/**
 * Submits a document for approval
 */
async function submitForApproval(
  jettyRequestItemId: string,
  documentType: string,
  setLoading: (loading: boolean) => void,
  queryClient: QueryClient,
  jettyRequestId: string
) {
  try {
    setLoading(true);

    const { postApiIdjasApprovalSubmit } = await import('@/client/sdk.gen');
    const { toast } = await import('@/lib/useToast');

    const input = {
      documentId: jettyRequestItemId,
      documentType: documentType,
      notes: 'Submitted for approval'
    };

    const response = await postApiIdjasApprovalSubmit({
      body: input
    });

    if (response.data) {
      toast({
        title: 'Success',
        description: 'Successfully submitted for approval',
        variant: 'default',
      });
      // Refetch the query to update the table data
      await queryClient.refetchQueries({ queryKey: ['jetty-request', jettyRequestId] });
    } else {
      toast({
        title: 'Error',
        description: 'Failed to submit for approval',
        variant: 'destructive',
      });
    }
  } catch (error: unknown) {
    console.error('Error submitting for approval:', error);

    const { toast } = await import('@/lib/useToast');
    let message = 'Error submitting for approval. Please try again.';
    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
      message = (error as { message?: string }).message ?? message;
    }

    toast({
      title: 'Error submitting for approval',
      description: message,
      variant: 'destructive',
    });
  } finally {
    setLoading(false);
  }
}

export const renderSubmitButton = (
  tableData: TableRowData[],
  documentType: string,
  loadingStates: Map<number, boolean>,
  setLoadingState: (row: number, loading: boolean) => void,
  queryClient: QueryClient,
  jettyRequestId: string
) => (
  _instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }

  const hasId = rowData?.id;
  const isLoading = loadingStates.get(_row) || false;

  const buttonClass = hasId && !isLoading
    ? 'px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50'
    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

  const buttonTitle = hasId
    ? (isLoading ? 'Submitting for approval...' : 'Submit for approval')
    : 'Save the application first to enable submit';

  const disabledAttr = hasId && !isLoading ? '' : 'disabled';
  const buttonText = isLoading ? 'Submitting...' : 'Submit';

  const submitButton = `<button class="${buttonClass}" data-row="${_row}" data-action="submit" title="${buttonTitle}" ${disabledAttr}>${buttonText}</button>`;
  td.innerHTML = submitButton;

  const submitBtn = td.querySelector('[data-action="submit"]');
  if (submitBtn && hasId && !isLoading) {
    submitBtn.addEventListener('click', async () => {
      const rowData = tableData[_row];
      if (rowData?.id) {
        await submitForApproval(
          rowData.id,
          documentType,
          (loading) => setLoadingState(_row, loading),
          queryClient,
          jettyRequestId
        );
      }
    });
  }
};

/**
 * Deletes a jetty request item from the database
 */
async function deleteJettyRequestItem(
  jettyRequestItemId: string,
  setLoading: (loading: boolean) => void,
  queryClient: QueryClient, // QueryClient type from @tanstack/react-query
  jettyRequestId: string
) {
  try {
    setLoading(true);

    const response = await deleteApiIdjasJettyRequestItemById({
      path: { id: jettyRequestItemId }
    });

    if (response.error) {
      throw response.error;
    }

    const { toast } = await import('@/lib/useToast');
    toast({
      title: 'Success',
      description: 'Item deleted successfully',
      variant: 'default',
    });

    // Refetch the query to update the table data
    await queryClient.refetchQueries({ queryKey: ['jetty-request', jettyRequestId] });
  } catch (error: unknown) {
    console.error('Error deleting item:', error);

    const { toast } = await import('@/lib/useToast');
    let message = 'Error deleting item. Please try again.';
    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
      message = (error as { message?: string }).message ?? message;
    }

    toast({
      title: 'Error deleting item',
      description: message,
      variant: 'destructive',
    });
  } finally {
    setLoading(false);
  }
}

export const renderDeleteButton = (
  tableData: TableRowData[],
  _setTableData: (rows: TableRowData[]) => void,
  loadingStates: Map<number, boolean> | undefined,
  setLoadingState: ((row: number, loading: boolean) => void) | undefined,
  queryClient: QueryClient, // QueryClient type from @tanstack/react-query
  jettyRequestId: string // <-- pass the main request id from parent
) => (
  _instance: Handsontable.Core | undefined,
  td: HTMLTableCellElement,
  _row: number,
  _col: number,
  _prop: string | number,
  _value: unknown,
  _cellProperties: Handsontable.CellProperties
) => {
  void _col;
  void _prop;
  void _value;
  void _cellProperties;
  const rowData = tableData[_row];
  if (rowData && rowData.status === 'Draft') {
    td.innerHTML = '';
    return;
  }

  const hasId = rowData?.id;
  const isLoading = loadingStates?.get(_row) || false;

  const buttonClass = hasId && !isLoading
    ? 'px-2 py-0.5 bg-red-500 text-white rounded-md text-xs hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50'
    : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';

  const buttonTitle = hasId
    ? (isLoading ? 'Deleting item...' : 'Delete item from database')
    : 'Save the application first to enable delete';

  const disabledAttr = hasId && !isLoading ? '' : 'disabled';
  const buttonText = isLoading ? 'Deleting...' : 'Delete';

  const deleteButton = `<button class="${buttonClass}" data-row="${_row}" data-action="delete" title="${buttonTitle}" ${disabledAttr}>${buttonText}</button>`;
  td.innerHTML = deleteButton;

  const deleteBtn = td.querySelector('[data-action="delete"]');
  if (deleteBtn && hasId && !isLoading) {
    deleteBtn.addEventListener('click', async () => {
      const rowData = tableData[_row];
      if (rowData?.id) {
        // Show confirmation dialog
        const confirmed = window.confirm('Are you sure you want to delete this item? This action cannot be undone.');
        if (confirmed) {
          await deleteJettyRequestItem(
            rowData.id,
            (loading) => setLoadingState?.(_row, loading),
            queryClient,
            jettyRequestId
          );
        }
      }
    });
  }
};
