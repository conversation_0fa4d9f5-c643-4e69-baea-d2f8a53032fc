import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { DynamicBreadcrumb } from '../breadcrumbs';
import { ThemeSelector } from '../theme-selector';
import { ModeToggle } from './ThemeToggle/theme-toggle';
import { LanguageSwitcher } from './language-switcher';

export default function Header() {
  return (
    <header className='sticky top-0 z-50 flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 bg-background border-b'>
      <div className='flex items-center gap-2 px-4'>
        <SidebarTrigger className='-ml-1' />
        <Separator orientation='vertical' className='mr-2 h-4' />
        <DynamicBreadcrumb />
      </div>

      <div className='flex items-center gap-2 px-4'>
        <LanguageSwitcher />
        {/* <div className='hidden md:flex'>
          <SearchInput />
        </div> */}
        <ModeToggle />
        <ThemeSelector />
      </div>
    </header>
  );
}
