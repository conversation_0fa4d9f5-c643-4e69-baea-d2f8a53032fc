import{a as r,j as e}from"./vendor-6tJeyfYI.js";import{k as L,B as P,l as f,C as R,A as V}from"./app-layout-rNt37hVL.js";import{C as T,a as F,b as G,c as I,d as O,e as q}from"./command-BPGQPJw5.js";import{P as B,a as M,b as U}from"./popover-ChFN9yvN.js";import{S as W}from"./scroll-area-DuGBN-Ug.js";import{u as _}from"./useDebounce-BdjXjarW.js";import{u as C}from"./useJettyDataWithFilter-CK58-c0U.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],z=L("loader-circle",$),Y=({value:t,onValueChange:o,placeholder:b="Select jetty...",className:S,disabled:v=!1,maxHeight:x=300})=>{const[n,i]=r.useState(!1),[j,y]=r.useState(""),l=r.useRef(null),d=_(j,300),{mutate:N,data:m=[],isPending:w,error:J}=C(),{mutate:g,data:u=[]}=C();r.useEffect(()=>{n&&N({maxResultCount:20,skipCount:0,filterGroup:d.trim()?{operator:"And",conditions:[{fieldName:"name",operator:"Contains",value:d.trim()}]}:void 0})},[d,n,N]),r.useEffect(()=>{t&&n&&g({maxResultCount:1,skipCount:0,filterGroup:{operator:"And",conditions:[{fieldName:"id",operator:"Equals",value:t}]}})},[t,n,g]);const h=r.useCallback(s=>{if(l.current){const a=l.current.querySelector("[data-radix-scroll-area-viewport]");a&&(s.preventDefault(),a.scrollTop+=s.deltaY)}},[]);r.useEffect(()=>{const s=l.current;if(n&&s)return s.addEventListener("wheel",h,{passive:!1}),()=>{s.removeEventListener("wheel",h)}},[n,h]);const c=r.useMemo(()=>{const s=m.find(a=>a.id===t||a.name===t);return s||(t&&u.length>0?u.find(a=>a.id===t||a.name===t):null)},[m,t,u]),k=r.useCallback(s=>{o(s),i(!1),y("")},[o]),E=r.useCallback(s=>{y(s)},[]),A=c?c.name||c.alias||c.id:b;return e.jsxs(B,{open:n,onOpenChange:i,children:[e.jsx(M,{asChild:!0,children:e.jsxs(P,{variant:"outline",role:"combobox","aria-expanded":n,className:f("w-full justify-between min-h-6 h-auto py-2",!t&&"text-muted-foreground",S),onClick:()=>i(!n),disabled:v,children:[e.jsx("span",{className:"truncate",children:A}),e.jsx(T,{className:"h-4 w-4 shrink-0 opacity-50"})]})}),e.jsx(U,{className:"p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden",align:"start",sideOffset:5,children:e.jsxs(F,{shouldFilter:!1,className:"max-h-full",children:[e.jsx(G,{placeholder:"Search jetties...",value:j,onValueChange:E,className:"h-9"}),e.jsx(I,{children:w?e.jsxs("div",{className:"flex items-center justify-center py-4",children:[e.jsx(z,{className:"h-4 w-4 animate-spin mr-2"}),"Loading..."]}):J?e.jsx("div",{className:"text-center py-4 text-destructive",children:"Error loading jetties"}):"No jetties found"}),e.jsx(W,{className:"overflow-hidden h-full",style:{height:`${x-40}px`,maxHeight:`${x-40}px`},ref:l,children:e.jsx(O,{children:m.map(s=>{const a=t===s.id||t===s.name,D=s.name||s.alias||s.id||"Unknown",p=s.id||s.name||"";return e.jsxs(q,{value:p,onSelect:()=>k(p),className:f("flex items-center gap-2",a?"bg-muted":""),children:[e.jsx("div",{className:f("flex h-4 w-4 items-center justify-center rounded-sm border",a?"bg-primary border-primary text-primary-foreground":"opacity-50"),children:a&&e.jsx(R,{className:"h-3 w-3"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"font-medium",children:D}),s.port&&e.jsxs("span",{className:"text-xs text-muted-foreground",children:["Port: ",s.port]})]})]},p)})})})]})})]})},re=()=>{const[t,o]=r.useState("");return e.jsx(V,{children:e.jsxs("div",{className:"flex flex-col space-y-6 p-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Jetty Selector Demo"}),e.jsx("p",{className:"text-muted-foreground",children:"This demonstrates the custom JettySelector component with debounced filtering. Try typing in the search box to filter jetties from the database."})]}),e.jsxs("div",{className:"max-w-md space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Select Jetty"}),e.jsx(Y,{value:t,onValueChange:o,placeholder:"Search and select a jetty..."})]}),t&&e.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[e.jsx("h3",{className:"font-medium mb-2",children:"Selected Jetty:"}),e.jsx("p",{className:"text-sm",children:t})]})]})]})})};export{re as default};
//# sourceMappingURL=page-B36zAKzh.js.map
