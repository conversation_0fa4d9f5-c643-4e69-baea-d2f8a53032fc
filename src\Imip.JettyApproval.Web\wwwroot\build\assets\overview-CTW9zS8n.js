import{h as lf,d as ti,g as se,a as q,$ as T,j as M}from"./vendor-6tJeyfYI.js";import{B as tn}from"./badge-DWaCYvGm.js";import{k as dw,N as ab,a2 as ff,t as hf,a3 as ie,l as Bt,o as et,q as tt,r as rt,s as dr,a4 as rn,a5 as ri,a6 as ni}from"./app-layout-rNt37hVL.js";import{S as Pt}from"./skeleton-DAOxGMKm.js";import{i as Vt}from"./tiny-invariant-CopsF_GD.js";import{T as pw,a as vw,b as bh,c as ii,d as yw,e as ai}from"./table-BKSoE52x.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gw=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],mw=dw("trending-up",gw);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var xh=ab("outline","trending-down","IconTrendingDown",[["path",{d:"M3 7l6 6l4 -4l8 8",key:"svg-0"}],["path",{d:"M21 10l0 7l-7 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var ar=ab("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]]);const bw=()=>lf({queryKey:[ff.GetDashboardStatistics],queryFn:async()=>{try{const e=await fetch("/api/dashboard/statistics",{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();return{totalApprovalRequests:t.totalApprovalRequests||0,pendingApprovals:t.pendingApprovals||0,rejectedRequests:t.rejectedRequests||0,scheduledVesselsToday:t.scheduledVesselsToday||0,lastUpdated:t.lastUpdated||new Date().toISOString()}}catch(e){let t="Unknown error occurred while loading dashboard statistics";throw typeof e=="object"&&e&&"message"in e&&typeof e.message=="string"&&(t=e.message??t),hf({title:"Error loading dashboard statistics",description:t,variant:"destructive"}),e}},staleTime:5*60*1e3,refetchInterval:5*60*1e3});var Va,wh;function Re(){if(wh)return Va;wh=1;var e=Array.isArray;return Va=e,Va}var Xa,Oh;function ob(){if(Oh)return Xa;Oh=1;var e=typeof ti=="object"&&ti&&ti.Object===Object&&ti;return Xa=e,Xa}var Ya,_h;function st(){if(_h)return Ya;_h=1;var e=ob(),t=typeof self=="object"&&self&&self.Object===Object&&self,r=e||t||Function("return this")();return Ya=r,Ya}var Za,Sh;function Gn(){if(Sh)return Za;Sh=1;var e=st(),t=e.Symbol;return Za=t,Za}var Ja,Ah;function xw(){if(Ah)return Ja;Ah=1;var e=Gn(),t=Object.prototype,r=t.hasOwnProperty,n=t.toString,i=e?e.toStringTag:void 0;function a(o){var u=r.call(o,i),s=o[i];try{o[i]=void 0;var c=!0}catch{}var f=n.call(o);return c&&(u?o[i]=s:delete o[i]),f}return Ja=a,Ja}var Qa,Ph;function ww(){if(Ph)return Qa;Ph=1;var e=Object.prototype,t=e.toString;function r(n){return t.call(n)}return Qa=r,Qa}var eo,Th;function xt(){if(Th)return eo;Th=1;var e=Gn(),t=xw(),r=ww(),n="[object Null]",i="[object Undefined]",a=e?e.toStringTag:void 0;function o(u){return u==null?u===void 0?i:n:a&&a in Object(u)?t(u):r(u)}return eo=o,eo}var to,jh;function wt(){if(jh)return to;jh=1;function e(t){return t!=null&&typeof t=="object"}return to=e,to}var ro,Eh;function Br(){if(Eh)return ro;Eh=1;var e=xt(),t=wt(),r="[object Symbol]";function n(i){return typeof i=="symbol"||t(i)&&e(i)==r}return ro=n,ro}var no,Mh;function df(){if(Mh)return no;Mh=1;var e=Re(),t=Br(),r=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,n=/^\w*$/;function i(a,o){if(e(a))return!1;var u=typeof a;return u=="number"||u=="symbol"||u=="boolean"||a==null||t(a)?!0:n.test(a)||!r.test(a)||o!=null&&a in Object(o)}return no=i,no}var io,Ch;function Mt(){if(Ch)return io;Ch=1;function e(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}return io=e,io}var ao,$h;function pf(){if($h)return ao;$h=1;var e=xt(),t=Mt(),r="[object AsyncFunction]",n="[object Function]",i="[object GeneratorFunction]",a="[object Proxy]";function o(u){if(!t(u))return!1;var s=e(u);return s==n||s==i||s==r||s==a}return ao=o,ao}var oo,Ih;function Ow(){if(Ih)return oo;Ih=1;var e=st(),t=e["__core-js_shared__"];return oo=t,oo}var uo,Nh;function _w(){if(Nh)return uo;Nh=1;var e=Ow(),t=function(){var n=/[^.]+$/.exec(e&&e.keys&&e.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function r(n){return!!t&&t in n}return uo=r,uo}var so,Rh;function ub(){if(Rh)return so;Rh=1;var e=Function.prototype,t=e.toString;function r(n){if(n!=null){try{return t.call(n)}catch{}try{return n+""}catch{}}return""}return so=r,so}var co,kh;function Sw(){if(kh)return co;kh=1;var e=pf(),t=_w(),r=Mt(),n=ub(),i=/[\\^$.*+?()[\]{}|]/g,a=/^\[object .+?Constructor\]$/,o=Function.prototype,u=Object.prototype,s=o.toString,c=u.hasOwnProperty,f=RegExp("^"+s.call(c).replace(i,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function l(h){if(!r(h)||t(h))return!1;var p=e(h)?f:a;return p.test(n(h))}return co=l,co}var lo,Dh;function Aw(){if(Dh)return lo;Dh=1;function e(t,r){return t?.[r]}return lo=e,lo}var fo,qh;function Qt(){if(qh)return fo;qh=1;var e=Sw(),t=Aw();function r(n,i){var a=t(n,i);return e(a)?a:void 0}return fo=r,fo}var ho,Bh;function da(){if(Bh)return ho;Bh=1;var e=Qt(),t=e(Object,"create");return ho=t,ho}var po,Lh;function Pw(){if(Lh)return po;Lh=1;var e=da();function t(){this.__data__=e?e(null):{},this.size=0}return po=t,po}var vo,Fh;function Tw(){if(Fh)return vo;Fh=1;function e(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}return vo=e,vo}var yo,Uh;function jw(){if(Uh)return yo;Uh=1;var e=da(),t="__lodash_hash_undefined__",r=Object.prototype,n=r.hasOwnProperty;function i(a){var o=this.__data__;if(e){var u=o[a];return u===t?void 0:u}return n.call(o,a)?o[a]:void 0}return yo=i,yo}var go,zh;function Ew(){if(zh)return go;zh=1;var e=da(),t=Object.prototype,r=t.hasOwnProperty;function n(i){var a=this.__data__;return e?a[i]!==void 0:r.call(a,i)}return go=n,go}var mo,Wh;function Mw(){if(Wh)return mo;Wh=1;var e=da(),t="__lodash_hash_undefined__";function r(n,i){var a=this.__data__;return this.size+=this.has(n)?0:1,a[n]=e&&i===void 0?t:i,this}return mo=r,mo}var bo,Hh;function Cw(){if(Hh)return bo;Hh=1;var e=Pw(),t=Tw(),r=jw(),n=Ew(),i=Mw();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,bo=a,bo}var xo,Gh;function $w(){if(Gh)return xo;Gh=1;function e(){this.__data__=[],this.size=0}return xo=e,xo}var wo,Kh;function vf(){if(Kh)return wo;Kh=1;function e(t,r){return t===r||t!==t&&r!==r}return wo=e,wo}var Oo,Vh;function pa(){if(Vh)return Oo;Vh=1;var e=vf();function t(r,n){for(var i=r.length;i--;)if(e(r[i][0],n))return i;return-1}return Oo=t,Oo}var _o,Xh;function Iw(){if(Xh)return _o;Xh=1;var e=pa(),t=Array.prototype,r=t.splice;function n(i){var a=this.__data__,o=e(a,i);if(o<0)return!1;var u=a.length-1;return o==u?a.pop():r.call(a,o,1),--this.size,!0}return _o=n,_o}var So,Yh;function Nw(){if(Yh)return So;Yh=1;var e=pa();function t(r){var n=this.__data__,i=e(n,r);return i<0?void 0:n[i][1]}return So=t,So}var Ao,Zh;function Rw(){if(Zh)return Ao;Zh=1;var e=pa();function t(r){return e(this.__data__,r)>-1}return Ao=t,Ao}var Po,Jh;function kw(){if(Jh)return Po;Jh=1;var e=pa();function t(r,n){var i=this.__data__,a=e(i,r);return a<0?(++this.size,i.push([r,n])):i[a][1]=n,this}return Po=t,Po}var To,Qh;function va(){if(Qh)return To;Qh=1;var e=$w(),t=Iw(),r=Nw(),n=Rw(),i=kw();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,To=a,To}var jo,ed;function yf(){if(ed)return jo;ed=1;var e=Qt(),t=st(),r=e(t,"Map");return jo=r,jo}var Eo,td;function Dw(){if(td)return Eo;td=1;var e=Cw(),t=va(),r=yf();function n(){this.size=0,this.__data__={hash:new e,map:new(r||t),string:new e}}return Eo=n,Eo}var Mo,rd;function qw(){if(rd)return Mo;rd=1;function e(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}return Mo=e,Mo}var Co,nd;function ya(){if(nd)return Co;nd=1;var e=qw();function t(r,n){var i=r.__data__;return e(n)?i[typeof n=="string"?"string":"hash"]:i.map}return Co=t,Co}var $o,id;function Bw(){if(id)return $o;id=1;var e=ya();function t(r){var n=e(this,r).delete(r);return this.size-=n?1:0,n}return $o=t,$o}var Io,ad;function Lw(){if(ad)return Io;ad=1;var e=ya();function t(r){return e(this,r).get(r)}return Io=t,Io}var No,od;function Fw(){if(od)return No;od=1;var e=ya();function t(r){return e(this,r).has(r)}return No=t,No}var Ro,ud;function Uw(){if(ud)return Ro;ud=1;var e=ya();function t(r,n){var i=e(this,r),a=i.size;return i.set(r,n),this.size+=i.size==a?0:1,this}return Ro=t,Ro}var ko,sd;function gf(){if(sd)return ko;sd=1;var e=Dw(),t=Bw(),r=Lw(),n=Fw(),i=Uw();function a(o){var u=-1,s=o==null?0:o.length;for(this.clear();++u<s;){var c=o[u];this.set(c[0],c[1])}}return a.prototype.clear=e,a.prototype.delete=t,a.prototype.get=r,a.prototype.has=n,a.prototype.set=i,ko=a,ko}var Do,cd;function sb(){if(cd)return Do;cd=1;var e=gf(),t="Expected a function";function r(n,i){if(typeof n!="function"||i!=null&&typeof i!="function")throw new TypeError(t);var a=function(){var o=arguments,u=i?i.apply(this,o):o[0],s=a.cache;if(s.has(u))return s.get(u);var c=n.apply(this,o);return a.cache=s.set(u,c)||s,c};return a.cache=new(r.Cache||e),a}return r.Cache=e,Do=r,Do}var qo,ld;function zw(){if(ld)return qo;ld=1;var e=sb(),t=500;function r(n){var i=e(n,function(o){return a.size===t&&a.clear(),o}),a=i.cache;return i}return qo=r,qo}var Bo,fd;function Ww(){if(fd)return Bo;fd=1;var e=zw(),t=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,r=/\\(\\)?/g,n=e(function(i){var a=[];return i.charCodeAt(0)===46&&a.push(""),i.replace(t,function(o,u,s,c){a.push(s?c.replace(r,"$1"):u||o)}),a});return Bo=n,Bo}var Lo,hd;function mf(){if(hd)return Lo;hd=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=Array(i);++n<i;)a[n]=r(t[n],n,t);return a}return Lo=e,Lo}var Fo,dd;function Hw(){if(dd)return Fo;dd=1;var e=Gn(),t=mf(),r=Re(),n=Br(),i=e?e.prototype:void 0,a=i?i.toString:void 0;function o(u){if(typeof u=="string")return u;if(r(u))return t(u,o)+"";if(n(u))return a?a.call(u):"";var s=u+"";return s=="0"&&1/u==-1/0?"-0":s}return Fo=o,Fo}var Uo,pd;function cb(){if(pd)return Uo;pd=1;var e=Hw();function t(r){return r==null?"":e(r)}return Uo=t,Uo}var zo,vd;function lb(){if(vd)return zo;vd=1;var e=Re(),t=df(),r=Ww(),n=cb();function i(a,o){return e(a)?a:t(a,o)?[a]:r(n(a))}return zo=i,zo}var Wo,yd;function ga(){if(yd)return Wo;yd=1;var e=Br();function t(r){if(typeof r=="string"||e(r))return r;var n=r+"";return n=="0"&&1/r==-1/0?"-0":n}return Wo=t,Wo}var Ho,gd;function bf(){if(gd)return Ho;gd=1;var e=lb(),t=ga();function r(n,i){i=e(i,n);for(var a=0,o=i.length;n!=null&&a<o;)n=n[t(i[a++])];return a&&a==o?n:void 0}return Ho=r,Ho}var Go,md;function fb(){if(md)return Go;md=1;var e=bf();function t(r,n,i){var a=r==null?void 0:e(r,n);return a===void 0?i:a}return Go=t,Go}var Gw=fb();const ze=se(Gw);var Ko,bd;function Kw(){if(bd)return Ko;bd=1;function e(t){return t==null}return Ko=e,Ko}var Vw=Kw();const ne=se(Vw);var Vo,xd;function Xw(){if(xd)return Vo;xd=1;var e=xt(),t=Re(),r=wt(),n="[object String]";function i(a){return typeof a=="string"||!t(a)&&r(a)&&e(a)==n}return Vo=i,Vo}var Yw=Xw();const Xt=se(Yw);var Zw=pf();const Z=se(Zw);var Jw=Mt();const Lr=se(Jw);var Xo={exports:{}},te={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wd;function Qw(){if(wd)return te;wd=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),l=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),y;y=Symbol.for("react.module.reference");function v(d){if(typeof d=="object"&&d!==null){var x=d.$$typeof;switch(x){case e:switch(d=d.type,d){case r:case i:case n:case c:case f:return d;default:switch(d=d&&d.$$typeof,d){case u:case o:case s:case h:case l:case a:return d;default:return x}}case t:return x}}}return te.ContextConsumer=o,te.ContextProvider=a,te.Element=e,te.ForwardRef=s,te.Fragment=r,te.Lazy=h,te.Memo=l,te.Portal=t,te.Profiler=i,te.StrictMode=n,te.Suspense=c,te.SuspenseList=f,te.isAsyncMode=function(){return!1},te.isConcurrentMode=function(){return!1},te.isContextConsumer=function(d){return v(d)===o},te.isContextProvider=function(d){return v(d)===a},te.isElement=function(d){return typeof d=="object"&&d!==null&&d.$$typeof===e},te.isForwardRef=function(d){return v(d)===s},te.isFragment=function(d){return v(d)===r},te.isLazy=function(d){return v(d)===h},te.isMemo=function(d){return v(d)===l},te.isPortal=function(d){return v(d)===t},te.isProfiler=function(d){return v(d)===i},te.isStrictMode=function(d){return v(d)===n},te.isSuspense=function(d){return v(d)===c},te.isSuspenseList=function(d){return v(d)===f},te.isValidElementType=function(d){return typeof d=="string"||typeof d=="function"||d===r||d===i||d===n||d===c||d===f||d===p||typeof d=="object"&&d!==null&&(d.$$typeof===h||d.$$typeof===l||d.$$typeof===a||d.$$typeof===o||d.$$typeof===s||d.$$typeof===y||d.getModuleId!==void 0)},te.typeOf=v,te}var Od;function e1(){return Od||(Od=1,Xo.exports=Qw()),Xo.exports}var t1=e1(),Yo,_d;function hb(){if(_d)return Yo;_d=1;var e=xt(),t=wt(),r="[object Number]";function n(i){return typeof i=="number"||t(i)&&e(i)==r}return Yo=n,Yo}var Zo,Sd;function r1(){if(Sd)return Zo;Sd=1;var e=hb();function t(r){return e(r)&&r!=+r}return Zo=t,Zo}var n1=r1();const Kn=se(n1);var i1=hb();const a1=se(i1);var Ye=function(t){return t===0?0:t>0?1:-1},Ut=function(t){return Xt(t)&&t.indexOf("%")===t.length-1},B=function(t){return a1(t)&&!Kn(t)},xe=function(t){return B(t)||Xt(t)},o1=0,ma=function(t){var r=++o1;return"".concat(t||"").concat(r)},Yt=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!B(t)&&!Xt(t))return n;var a;if(Ut(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return Kn(a)&&(a=n),i&&a>r&&(a=r),a},At=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},u1=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},or=function(t,r){return B(t)&&B(r)?function(n){return t+n*(r-t)}:function(){return r}};function Vc(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):ze(n,t))===r})}var s1=function(t,r){return B(t)&&B(r)?t-r:Xt(t)&&Xt(r)?t.localeCompare(r):t instanceof Date&&r instanceof Date?t.getTime()-r.getTime():String(t).localeCompare(String(r))};function pr(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function Xc(e){"@babel/helpers - typeof";return Xc=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xc(e)}var c1=["viewBox","children"],l1=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Ad=["points","pathLength"],Jo={svg:c1,polygon:Ad,polyline:Ad},xf=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],bi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(q.isValidElement(t)&&(n=t.props),!Lr(n))return null;var i={};return Object.keys(n).forEach(function(a){xf.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},f1=function(t,r,n){return function(i){return t(r,n,i),null}},xi=function(t,r,n){if(!Lr(t)||Xc(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];xf.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=f1(o,r,n))}),i},h1=["children"],d1=["children"];function Pd(e,t){if(e==null)return{};var r=p1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function p1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Td={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},dt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},jd=null,Qo=null,wf=function e(t){if(t===jd&&Array.isArray(Qo))return Qo;var r=[];return q.Children.forEach(t,function(n){ne(n)||(t1.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Qo=r,jd=t,r};function Ze(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return dt(i)}):n=[dt(t)],wf(e).forEach(function(i){var a=ze(i,"type.displayName")||ze(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function qe(e,t){var r=Ze(e,t);return r&&r[0]}var Ed=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!B(n)||n<=0||!B(i)||i<=0)},v1=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],y1=function(t){return t&&t.type&&Xt(t.type)&&v1.indexOf(t.type)>=0},g1=function(t,r,n,i){var a,o=(a=Jo?.[i])!==null&&a!==void 0?a:[];return r.startsWith("data-")||!Z(t)&&(i&&o.includes(r)||l1.includes(r))||n&&xf.includes(r)},ee=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(q.isValidElement(t)&&(i=t.props),!Lr(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;g1((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Yc=function e(t,r){if(t===r)return!0;var n=q.Children.count(t);if(n!==q.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Md(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Md(a,o))return!1}return!0},Md=function(t,r){if(ne(t)&&ne(r))return!0;if(!ne(t)&&!ne(r)){var n=t.props||{},i=n.children,a=Pd(n,h1),o=r.props||{},u=o.children,s=Pd(o,d1);return i&&u?pr(a,s)&&Yc(i,u):!i&&!u?pr(a,s):!1}return!1},Cd=function(t,r){var n=[],i={};return wf(t).forEach(function(a,o){if(y1(a))n.push(a);else if(a){var u=dt(a.type),s=r[u]||{},c=s.handler,f=s.once;if(c&&(!f||!i[u])){var l=c(a,u,o);n.push(l),i[u]=!0}}}),n},m1=function(t){var r=t&&t.type;return r&&Td[r]?Td[r]:null},b1=function(t,r){return wf(r).indexOf(t)},x1=["children","width","height","viewBox","className","style","title","desc"];function Zc(){return Zc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zc.apply(this,arguments)}function w1(e,t){if(e==null)return{};var r=O1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function O1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Jc(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,s=e.desc,c=w1(e,x1),f=i||{width:r,height:n,x:0,y:0},l=ie("recharts-surface",a);return T.createElement("svg",Zc({},ee(c,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),T.createElement("title",null,u),T.createElement("desc",null,s),t)}var _1=["children","className"];function Qc(){return Qc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qc.apply(this,arguments)}function S1(e,t){if(e==null)return{};var r=A1(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function A1(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var _e=T.forwardRef(function(e,t){var r=e.children,n=e.className,i=S1(e,_1),a=ie("recharts-layer",n);return T.createElement("g",Qc({className:a},ee(i,!0),{ref:t}),r)}),pt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},eu,$d;function P1(){if($d)return eu;$d=1;function e(t,r,n){var i=-1,a=t.length;r<0&&(r=-r>a?0:a+r),n=n>a?a:n,n<0&&(n+=a),a=r>n?0:n-r>>>0,r>>>=0;for(var o=Array(a);++i<a;)o[i]=t[i+r];return o}return eu=e,eu}var tu,Id;function T1(){if(Id)return tu;Id=1;var e=P1();function t(r,n,i){var a=r.length;return i=i===void 0?a:i,!n&&i>=a?r:e(r,n,i)}return tu=t,tu}var ru,Nd;function db(){if(Nd)return ru;Nd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="\\u200d",u=RegExp("["+o+e+i+a+"]");function s(c){return u.test(c)}return ru=s,ru}var nu,Rd;function j1(){if(Rd)return nu;Rd=1;function e(t){return t.split("")}return nu=e,nu}var iu,kd;function E1(){if(kd)return iu;kd=1;var e="\\ud800-\\udfff",t="\\u0300-\\u036f",r="\\ufe20-\\ufe2f",n="\\u20d0-\\u20ff",i=t+r+n,a="\\ufe0e\\ufe0f",o="["+e+"]",u="["+i+"]",s="\\ud83c[\\udffb-\\udfff]",c="(?:"+u+"|"+s+")",f="[^"+e+"]",l="(?:\\ud83c[\\udde6-\\uddff]){2}",h="[\\ud800-\\udbff][\\udc00-\\udfff]",p="\\u200d",y=c+"?",v="["+a+"]?",d="(?:"+p+"(?:"+[f,l,h].join("|")+")"+v+y+")*",x=v+y+d,w="(?:"+[f+u+"?",u,l,h,o].join("|")+")",b=RegExp(s+"(?="+s+")|"+w+x,"g");function O(g){return g.match(b)||[]}return iu=O,iu}var au,Dd;function M1(){if(Dd)return au;Dd=1;var e=j1(),t=db(),r=E1();function n(i){return t(i)?r(i):e(i)}return au=n,au}var ou,qd;function C1(){if(qd)return ou;qd=1;var e=T1(),t=db(),r=M1(),n=cb();function i(a){return function(o){o=n(o);var u=t(o)?r(o):void 0,s=u?u[0]:o.charAt(0),c=u?e(u,1).join(""):o.slice(1);return s[a]()+c}}return ou=i,ou}var uu,Bd;function $1(){if(Bd)return uu;Bd=1;var e=C1(),t=e("toUpperCase");return uu=t,uu}var I1=$1();const ba=se(I1);function ue(e){return function(){return e}}const pb=Math.cos,wi=Math.sin,Je=Math.sqrt,Oi=Math.PI,xa=2*Oi,el=Math.PI,tl=2*el,Lt=1e-6,N1=tl-Lt;function vb(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function R1(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return vb;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class k1{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?vb:R1(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,s=n-t,c=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Lt)if(!(Math.abs(l*s-c*f)>Lt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let p=n-o,y=i-u,v=s*s+c*c,d=p*p+y*y,x=Math.sqrt(v),w=Math.sqrt(h),b=a*Math.tan((el-Math.acos((v+h-d)/(2*x*w)))/2),O=b/w,g=b/x;Math.abs(O-1)>Lt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*p>f*y)},${this._x1=t+g*s},${this._y1=r+g*c}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),s=n*Math.sin(i),c=t+u,f=r+s,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${c},${f}`:(Math.abs(this._x1-c)>Lt||Math.abs(this._y1-f)>Lt)&&this._append`L${c},${f}`,n&&(h<0&&(h=h%tl+tl),h>N1?this._append`A${n},${n},0,1,${l},${t-u},${r-s}A${n},${n},0,1,${l},${this._x1=c},${this._y1=f}`:h>Lt&&this._append`A${n},${n},0,${+(h>=el)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function Of(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new k1(t)}function _f(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function yb(e){this._context=e}yb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function wa(e){return new yb(e)}function gb(e){return e[0]}function mb(e){return e[1]}function bb(e,t){var r=ue(!0),n=null,i=wa,a=null,o=Of(u);e=typeof e=="function"?e:e===void 0?gb:ue(e),t=typeof t=="function"?t:t===void 0?mb:ue(t);function u(s){var c,f=(s=_f(s)).length,l,h=!1,p;for(n==null&&(a=i(p=o())),c=0;c<=f;++c)!(c<f&&r(l=s[c],c,s))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,c,s),+t(l,c,s));if(p)return a=null,p+""||null}return u.x=function(s){return arguments.length?(e=typeof s=="function"?s:ue(+s),u):e},u.y=function(s){return arguments.length?(t=typeof s=="function"?s:ue(+s),u):t},u.defined=function(s){return arguments.length?(r=typeof s=="function"?s:ue(!!s),u):r},u.curve=function(s){return arguments.length?(i=s,n!=null&&(a=i(n)),u):i},u.context=function(s){return arguments.length?(s==null?n=a=null:a=i(n=s),u):n},u}function oi(e,t,r){var n=null,i=ue(!0),a=null,o=wa,u=null,s=Of(c);e=typeof e=="function"?e:e===void 0?gb:ue(+e),t=typeof t=="function"?t:ue(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?mb:ue(+r);function c(l){var h,p,y,v=(l=_f(l)).length,d,x=!1,w,b=new Array(v),O=new Array(v);for(a==null&&(u=o(w=s())),h=0;h<=v;++h){if(!(h<v&&i(d=l[h],h,l))===x)if(x=!x)p=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),y=h-1;y>=p;--y)u.point(b[y],O[y]);u.lineEnd(),u.areaEnd()}x&&(b[h]=+e(d,h,l),O[h]=+t(d,h,l),u.point(n?+n(d,h,l):b[h],r?+r(d,h,l):O[h]))}if(w)return u=null,w+""||null}function f(){return bb().defined(i).curve(o).context(a)}return c.x=function(l){return arguments.length?(e=typeof l=="function"?l:ue(+l),n=null,c):e},c.x0=function(l){return arguments.length?(e=typeof l=="function"?l:ue(+l),c):e},c.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:ue(+l),c):n},c.y=function(l){return arguments.length?(t=typeof l=="function"?l:ue(+l),r=null,c):t},c.y0=function(l){return arguments.length?(t=typeof l=="function"?l:ue(+l),c):t},c.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:ue(+l),c):r},c.lineX0=c.lineY0=function(){return f().x(e).y(t)},c.lineY1=function(){return f().x(e).y(r)},c.lineX1=function(){return f().x(n).y(t)},c.defined=function(l){return arguments.length?(i=typeof l=="function"?l:ue(!!l),c):i},c.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),c):o},c.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),c):a},c}class xb{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function D1(e){return new xb(e,!0)}function q1(e){return new xb(e,!1)}const Sf={draw(e,t){const r=Je(t/Oi);e.moveTo(r,0),e.arc(0,0,r,0,xa)}},B1={draw(e,t){const r=Je(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},wb=Je(1/3),L1=wb*2,F1={draw(e,t){const r=Je(t/L1),n=r*wb;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},U1={draw(e,t){const r=Je(t),n=-r/2;e.rect(n,n,r,r)}},z1=.8908130915292852,Ob=wi(Oi/10)/wi(7*Oi/10),W1=wi(xa/10)*Ob,H1=-pb(xa/10)*Ob,G1={draw(e,t){const r=Je(t*z1),n=W1*r,i=H1*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=xa*a/5,u=pb(o),s=wi(o);e.lineTo(s*r,-u*r),e.lineTo(u*n-s*i,s*n+u*i)}e.closePath()}},su=Je(3),K1={draw(e,t){const r=-Je(t/(su*3));e.moveTo(0,r*2),e.lineTo(-su*r,-r),e.lineTo(su*r,-r),e.closePath()}},Le=-.5,Fe=Je(3)/2,rl=1/Je(12),V1=(rl/2+1)*3,X1={draw(e,t){const r=Je(t/V1),n=r/2,i=r*rl,a=n,o=r*rl+r,u=-a,s=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,s),e.lineTo(Le*n-Fe*i,Fe*n+Le*i),e.lineTo(Le*a-Fe*o,Fe*a+Le*o),e.lineTo(Le*u-Fe*s,Fe*u+Le*s),e.lineTo(Le*n+Fe*i,Le*i-Fe*n),e.lineTo(Le*a+Fe*o,Le*o-Fe*a),e.lineTo(Le*u+Fe*s,Le*s-Fe*u),e.closePath()}};function Y1(e,t){let r=null,n=Of(i);e=typeof e=="function"?e:ue(e||Sf),t=typeof t=="function"?t:ue(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ue(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ue(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function _i(){}function Si(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function _b(e){this._context=e}_b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Si(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Si(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Z1(e){return new _b(e)}function Sb(e){this._context=e}Sb.prototype={areaStart:_i,areaEnd:_i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Si(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function J1(e){return new Sb(e)}function Ab(e){this._context=e}Ab.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Si(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function Q1(e){return new Ab(e)}function Pb(e){this._context=e}Pb.prototype={areaStart:_i,areaEnd:_i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function eO(e){return new Pb(e)}function Ld(e){return e<0?-1:1}function Fd(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(Ld(a)+Ld(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function Ud(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function cu(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Ai(e){this._context=e}Ai.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:cu(this,this._t0,Ud(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,cu(this,Ud(this,r=Fd(this,e,t)),r);break;default:cu(this,this._t0,r=Fd(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function Tb(e){this._context=new jb(e)}(Tb.prototype=Object.create(Ai.prototype)).point=function(e,t){Ai.prototype.point.call(this,t,e)};function jb(e){this._context=e}jb.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function tO(e){return new Ai(e)}function rO(e){return new Tb(e)}function Eb(e){this._context=e}Eb.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=zd(e),i=zd(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function zd(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function nO(e){return new Eb(e)}function Oa(e,t){this._context=e,this._t=t}Oa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function iO(e){return new Oa(e,.5)}function aO(e){return new Oa(e,0)}function oO(e){return new Oa(e,1)}function mr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function nl(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function uO(e,t){return e[t]}function sO(e){const t=[];return t.key=e,t}function cO(){var e=ue([]),t=nl,r=mr,n=uO;function i(a){var o=Array.from(e.apply(this,arguments),sO),u,s=o.length,c=-1,f;for(const l of a)for(u=0,++c;u<s;++u)(o[u][c]=[0,+n(l,o[u].key,c,a)]).data=l;for(u=0,f=_f(t(o));u<s;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ue(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:ue(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?nl:typeof a=="function"?a:ue(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??mr,i):r},i}function lO(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}mr(e,t)}}function fO(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}mr(e,t)}}function hO(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,s=0,c=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,p=(l-h)/2,y=0;y<u;++y){var v=e[t[y]],d=v[n][1]||0,x=v[n-1][1]||0;p+=d-x}s+=l,c+=p*l}i[n-1][1]+=i[n-1][0]=r,s&&(r-=c/s)}i[n-1][1]+=i[n-1][0]=r,mr(e,t)}}function fn(e){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(e)}var dO=["type","size","sizeType"];function il(){return il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},il.apply(this,arguments)}function Wd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Hd(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wd(Object(r),!0).forEach(function(n){pO(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function pO(e,t,r){return t=vO(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vO(e){var t=yO(e,"string");return fn(t)=="symbol"?t:t+""}function yO(e,t){if(fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function gO(e,t){if(e==null)return{};var r=mO(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mO(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var Mb={symbolCircle:Sf,symbolCross:B1,symbolDiamond:F1,symbolSquare:U1,symbolStar:G1,symbolTriangle:K1,symbolWye:X1},bO=Math.PI/180,xO=function(t){var r="symbol".concat(ba(t));return Mb[r]||Sf},wO=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*bO;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},OO=function(t,r){Mb["symbol".concat(ba(t))]=r},Af=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,s=gO(t,dO),c=Hd(Hd({},s),{},{type:n,size:a,sizeType:u}),f=function(){var d=xO(n),x=Y1().type(d).size(wO(a,u,n));return x()},l=c.className,h=c.cx,p=c.cy,y=ee(c,!0);return h===+h&&p===+p&&a===+a?T.createElement("path",il({},y,{className:ie("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(p,")"),d:f()})):null};Af.registerSymbol=OO;function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function al(){return al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},al.apply(this,arguments)}function Gd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function _O(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Gd(Object(r),!0).forEach(function(n){hn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Gd(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SO(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function AO(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$b(n.key),n)}}function PO(e,t,r){return t&&AO(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function TO(e,t,r){return t=Pi(t),jO(e,Cb()?Reflect.construct(t,r||[],Pi(e).constructor):t.apply(e,r))}function jO(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return EO(e)}function EO(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Cb=function(){return!!e})()}function Pi(e){return Pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Pi(e)}function MO(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ol(e,t)}function ol(e,t){return ol=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ol(e,t)}function hn(e,t,r){return t=$b(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $b(e){var t=CO(e,"string");return br(t)=="symbol"?t:t+""}function CO(e,t){if(br(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Ge=32,Pf=function(e){function t(){return SO(this,t),TO(this,t,arguments)}return MO(t,e),PO(t,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ge/2,o=Ge/6,u=Ge/3,s=n.inactive?i:n.color;if(n.type==="plainline")return T.createElement("line",{strokeWidth:4,fill:"none",stroke:s,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ge,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return T.createElement("path",{strokeWidth:4,fill:"none",stroke:s,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ge,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return T.createElement("path",{stroke:"none",fill:s,d:"M0,".concat(Ge/8,"h").concat(Ge,"v").concat(Ge*3/4,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(T.isValidElement(n.legendIcon)){var c=_O({},n);return delete c.legendIcon,T.cloneElement(n.legendIcon,c)}return T.createElement(Af,{fill:s,cx:a,cy:a,size:Ge,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,s=i.formatter,c=i.inactiveColor,f={x:0,y:0,width:Ge,height:Ge},l={display:u==="horizontal"?"inline-block":"block",marginRight:10},h={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(p,y){var v=p.formatter||s,d=ie(hn(hn({"recharts-legend-item":!0},"legend-item-".concat(y),!0),"inactive",p.inactive));if(p.type==="none")return null;var x=Z(p.value)?null:p.value;pt(!Z(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var w=p.inactive?c:p.color;return T.createElement("li",al({className:d,style:l,key:"legend-item-".concat(y)},xi(n.props,p,y)),T.createElement(Jc,{width:o,height:o,viewBox:f,style:h},n.renderIcon(p)),T.createElement("span",{className:"recharts-legend-item-text",style:{color:w}},v?v(x,p,y):x))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return T.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(q.PureComponent);hn(Pf,"displayName","Legend");hn(Pf,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var lu,Kd;function $O(){if(Kd)return lu;Kd=1;var e=va();function t(){this.__data__=new e,this.size=0}return lu=t,lu}var fu,Vd;function IO(){if(Vd)return fu;Vd=1;function e(t){var r=this.__data__,n=r.delete(t);return this.size=r.size,n}return fu=e,fu}var hu,Xd;function NO(){if(Xd)return hu;Xd=1;function e(t){return this.__data__.get(t)}return hu=e,hu}var du,Yd;function RO(){if(Yd)return du;Yd=1;function e(t){return this.__data__.has(t)}return du=e,du}var pu,Zd;function kO(){if(Zd)return pu;Zd=1;var e=va(),t=yf(),r=gf(),n=200;function i(a,o){var u=this.__data__;if(u instanceof e){var s=u.__data__;if(!t||s.length<n-1)return s.push([a,o]),this.size=++u.size,this;u=this.__data__=new r(s)}return u.set(a,o),this.size=u.size,this}return pu=i,pu}var vu,Jd;function Ib(){if(Jd)return vu;Jd=1;var e=va(),t=$O(),r=IO(),n=NO(),i=RO(),a=kO();function o(u){var s=this.__data__=new e(u);this.size=s.size}return o.prototype.clear=t,o.prototype.delete=r,o.prototype.get=n,o.prototype.has=i,o.prototype.set=a,vu=o,vu}var yu,Qd;function DO(){if(Qd)return yu;Qd=1;var e="__lodash_hash_undefined__";function t(r){return this.__data__.set(r,e),this}return yu=t,yu}var gu,ep;function qO(){if(ep)return gu;ep=1;function e(t){return this.__data__.has(t)}return gu=e,gu}var mu,tp;function Nb(){if(tp)return mu;tp=1;var e=gf(),t=DO(),r=qO();function n(i){var a=-1,o=i==null?0:i.length;for(this.__data__=new e;++a<o;)this.add(i[a])}return n.prototype.add=n.prototype.push=t,n.prototype.has=r,mu=n,mu}var bu,rp;function Rb(){if(rp)return bu;rp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(r(t[n],n,t))return!0;return!1}return bu=e,bu}var xu,np;function kb(){if(np)return xu;np=1;function e(t,r){return t.has(r)}return xu=e,xu}var wu,ip;function Db(){if(ip)return wu;ip=1;var e=Nb(),t=Rb(),r=kb(),n=1,i=2;function a(o,u,s,c,f,l){var h=s&n,p=o.length,y=u.length;if(p!=y&&!(h&&y>p))return!1;var v=l.get(o),d=l.get(u);if(v&&d)return v==u&&d==o;var x=-1,w=!0,b=s&i?new e:void 0;for(l.set(o,u),l.set(u,o);++x<p;){var O=o[x],g=u[x];if(c)var m=h?c(g,O,x,u,o,l):c(O,g,x,o,u,l);if(m!==void 0){if(m)continue;w=!1;break}if(b){if(!t(u,function(_,S){if(!r(b,S)&&(O===_||f(O,_,s,c,l)))return b.push(S)})){w=!1;break}}else if(!(O===g||f(O,g,s,c,l))){w=!1;break}}return l.delete(o),l.delete(u),w}return wu=a,wu}var Ou,ap;function BO(){if(ap)return Ou;ap=1;var e=st(),t=e.Uint8Array;return Ou=t,Ou}var _u,op;function LO(){if(op)return _u;op=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i,a){n[++r]=[a,i]}),n}return _u=e,_u}var Su,up;function Tf(){if(up)return Su;up=1;function e(t){var r=-1,n=Array(t.size);return t.forEach(function(i){n[++r]=i}),n}return Su=e,Su}var Au,sp;function FO(){if(sp)return Au;sp=1;var e=Gn(),t=BO(),r=vf(),n=Db(),i=LO(),a=Tf(),o=1,u=2,s="[object Boolean]",c="[object Date]",f="[object Error]",l="[object Map]",h="[object Number]",p="[object RegExp]",y="[object Set]",v="[object String]",d="[object Symbol]",x="[object ArrayBuffer]",w="[object DataView]",b=e?e.prototype:void 0,O=b?b.valueOf:void 0;function g(m,_,S,P,C,A,j){switch(S){case w:if(m.byteLength!=_.byteLength||m.byteOffset!=_.byteOffset)return!1;m=m.buffer,_=_.buffer;case x:return!(m.byteLength!=_.byteLength||!A(new t(m),new t(_)));case s:case c:case h:return r(+m,+_);case f:return m.name==_.name&&m.message==_.message;case p:case v:return m==_+"";case l:var E=i;case y:var N=P&o;if(E||(E=a),m.size!=_.size&&!N)return!1;var I=j.get(m);if(I)return I==_;P|=u,j.set(m,_);var R=n(E(m),E(_),P,C,A,j);return j.delete(m),R;case d:if(O)return O.call(m)==O.call(_)}return!1}return Au=g,Au}var Pu,cp;function qb(){if(cp)return Pu;cp=1;function e(t,r){for(var n=-1,i=r.length,a=t.length;++n<i;)t[a+n]=r[n];return t}return Pu=e,Pu}var Tu,lp;function UO(){if(lp)return Tu;lp=1;var e=qb(),t=Re();function r(n,i,a){var o=i(n);return t(n)?o:e(o,a(n))}return Tu=r,Tu}var ju,fp;function zO(){if(fp)return ju;fp=1;function e(t,r){for(var n=-1,i=t==null?0:t.length,a=0,o=[];++n<i;){var u=t[n];r(u,n,t)&&(o[a++]=u)}return o}return ju=e,ju}var Eu,hp;function WO(){if(hp)return Eu;hp=1;function e(){return[]}return Eu=e,Eu}var Mu,dp;function HO(){if(dp)return Mu;dp=1;var e=zO(),t=WO(),r=Object.prototype,n=r.propertyIsEnumerable,i=Object.getOwnPropertySymbols,a=i?function(o){return o==null?[]:(o=Object(o),e(i(o),function(u){return n.call(o,u)}))}:t;return Mu=a,Mu}var Cu,pp;function GO(){if(pp)return Cu;pp=1;function e(t,r){for(var n=-1,i=Array(t);++n<t;)i[n]=r(n);return i}return Cu=e,Cu}var $u,vp;function KO(){if(vp)return $u;vp=1;var e=xt(),t=wt(),r="[object Arguments]";function n(i){return t(i)&&e(i)==r}return $u=n,$u}var Iu,yp;function jf(){if(yp)return Iu;yp=1;var e=KO(),t=wt(),r=Object.prototype,n=r.hasOwnProperty,i=r.propertyIsEnumerable,a=e(function(){return arguments}())?e:function(o){return t(o)&&n.call(o,"callee")&&!i.call(o,"callee")};return Iu=a,Iu}var nn={exports:{}},Nu,gp;function VO(){if(gp)return Nu;gp=1;function e(){return!1}return Nu=e,Nu}nn.exports;var mp;function Bb(){return mp||(mp=1,function(e,t){var r=st(),n=VO(),i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,s=u?u.isBuffer:void 0,c=s||n;e.exports=c}(nn,nn.exports)),nn.exports}var Ru,bp;function Ef(){if(bp)return Ru;bp=1;var e=9007199254740991,t=/^(?:0|[1-9]\d*)$/;function r(n,i){var a=typeof n;return i=i??e,!!i&&(a=="number"||a!="symbol"&&t.test(n))&&n>-1&&n%1==0&&n<i}return Ru=r,Ru}var ku,xp;function Mf(){if(xp)return ku;xp=1;var e=9007199254740991;function t(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=e}return ku=t,ku}var Du,wp;function XO(){if(wp)return Du;wp=1;var e=xt(),t=Mf(),r=wt(),n="[object Arguments]",i="[object Array]",a="[object Boolean]",o="[object Date]",u="[object Error]",s="[object Function]",c="[object Map]",f="[object Number]",l="[object Object]",h="[object RegExp]",p="[object Set]",y="[object String]",v="[object WeakMap]",d="[object ArrayBuffer]",x="[object DataView]",w="[object Float32Array]",b="[object Float64Array]",O="[object Int8Array]",g="[object Int16Array]",m="[object Int32Array]",_="[object Uint8Array]",S="[object Uint8ClampedArray]",P="[object Uint16Array]",C="[object Uint32Array]",A={};A[w]=A[b]=A[O]=A[g]=A[m]=A[_]=A[S]=A[P]=A[C]=!0,A[n]=A[i]=A[d]=A[a]=A[x]=A[o]=A[u]=A[s]=A[c]=A[f]=A[l]=A[h]=A[p]=A[y]=A[v]=!1;function j(E){return r(E)&&t(E.length)&&!!A[e(E)]}return Du=j,Du}var qu,Op;function Lb(){if(Op)return qu;Op=1;function e(t){return function(r){return t(r)}}return qu=e,qu}var an={exports:{}};an.exports;var _p;function YO(){return _p||(_p=1,function(e,t){var r=ob(),n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var s=i&&i.require&&i.require("util").types;return s||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u}(an,an.exports)),an.exports}var Bu,Sp;function Fb(){if(Sp)return Bu;Sp=1;var e=XO(),t=Lb(),r=YO(),n=r&&r.isTypedArray,i=n?t(n):e;return Bu=i,Bu}var Lu,Ap;function ZO(){if(Ap)return Lu;Ap=1;var e=GO(),t=jf(),r=Re(),n=Bb(),i=Ef(),a=Fb(),o=Object.prototype,u=o.hasOwnProperty;function s(c,f){var l=r(c),h=!l&&t(c),p=!l&&!h&&n(c),y=!l&&!h&&!p&&a(c),v=l||h||p||y,d=v?e(c.length,String):[],x=d.length;for(var w in c)(f||u.call(c,w))&&!(v&&(w=="length"||p&&(w=="offset"||w=="parent")||y&&(w=="buffer"||w=="byteLength"||w=="byteOffset")||i(w,x)))&&d.push(w);return d}return Lu=s,Lu}var Fu,Pp;function JO(){if(Pp)return Fu;Pp=1;var e=Object.prototype;function t(r){var n=r&&r.constructor,i=typeof n=="function"&&n.prototype||e;return r===i}return Fu=t,Fu}var Uu,Tp;function Ub(){if(Tp)return Uu;Tp=1;function e(t,r){return function(n){return t(r(n))}}return Uu=e,Uu}var zu,jp;function QO(){if(jp)return zu;jp=1;var e=Ub(),t=e(Object.keys,Object);return zu=t,zu}var Wu,Ep;function e_(){if(Ep)return Wu;Ep=1;var e=JO(),t=QO(),r=Object.prototype,n=r.hasOwnProperty;function i(a){if(!e(a))return t(a);var o=[];for(var u in Object(a))n.call(a,u)&&u!="constructor"&&o.push(u);return o}return Wu=i,Wu}var Hu,Mp;function Vn(){if(Mp)return Hu;Mp=1;var e=pf(),t=Mf();function r(n){return n!=null&&t(n.length)&&!e(n)}return Hu=r,Hu}var Gu,Cp;function _a(){if(Cp)return Gu;Cp=1;var e=ZO(),t=e_(),r=Vn();function n(i){return r(i)?e(i):t(i)}return Gu=n,Gu}var Ku,$p;function t_(){if($p)return Ku;$p=1;var e=UO(),t=HO(),r=_a();function n(i){return e(i,r,t)}return Ku=n,Ku}var Vu,Ip;function r_(){if(Ip)return Vu;Ip=1;var e=t_(),t=1,r=Object.prototype,n=r.hasOwnProperty;function i(a,o,u,s,c,f){var l=u&t,h=e(a),p=h.length,y=e(o),v=y.length;if(p!=v&&!l)return!1;for(var d=p;d--;){var x=h[d];if(!(l?x in o:n.call(o,x)))return!1}var w=f.get(a),b=f.get(o);if(w&&b)return w==o&&b==a;var O=!0;f.set(a,o),f.set(o,a);for(var g=l;++d<p;){x=h[d];var m=a[x],_=o[x];if(s)var S=l?s(_,m,x,o,a,f):s(m,_,x,a,o,f);if(!(S===void 0?m===_||c(m,_,u,s,f):S)){O=!1;break}g||(g=x=="constructor")}if(O&&!g){var P=a.constructor,C=o.constructor;P!=C&&"constructor"in a&&"constructor"in o&&!(typeof P=="function"&&P instanceof P&&typeof C=="function"&&C instanceof C)&&(O=!1)}return f.delete(a),f.delete(o),O}return Vu=i,Vu}var Xu,Np;function n_(){if(Np)return Xu;Np=1;var e=Qt(),t=st(),r=e(t,"DataView");return Xu=r,Xu}var Yu,Rp;function i_(){if(Rp)return Yu;Rp=1;var e=Qt(),t=st(),r=e(t,"Promise");return Yu=r,Yu}var Zu,kp;function zb(){if(kp)return Zu;kp=1;var e=Qt(),t=st(),r=e(t,"Set");return Zu=r,Zu}var Ju,Dp;function a_(){if(Dp)return Ju;Dp=1;var e=Qt(),t=st(),r=e(t,"WeakMap");return Ju=r,Ju}var Qu,qp;function o_(){if(qp)return Qu;qp=1;var e=n_(),t=yf(),r=i_(),n=zb(),i=a_(),a=xt(),o=ub(),u="[object Map]",s="[object Object]",c="[object Promise]",f="[object Set]",l="[object WeakMap]",h="[object DataView]",p=o(e),y=o(t),v=o(r),d=o(n),x=o(i),w=a;return(e&&w(new e(new ArrayBuffer(1)))!=h||t&&w(new t)!=u||r&&w(r.resolve())!=c||n&&w(new n)!=f||i&&w(new i)!=l)&&(w=function(b){var O=a(b),g=O==s?b.constructor:void 0,m=g?o(g):"";if(m)switch(m){case p:return h;case y:return u;case v:return c;case d:return f;case x:return l}return O}),Qu=w,Qu}var es,Bp;function u_(){if(Bp)return es;Bp=1;var e=Ib(),t=Db(),r=FO(),n=r_(),i=o_(),a=Re(),o=Bb(),u=Fb(),s=1,c="[object Arguments]",f="[object Array]",l="[object Object]",h=Object.prototype,p=h.hasOwnProperty;function y(v,d,x,w,b,O){var g=a(v),m=a(d),_=g?f:i(v),S=m?f:i(d);_=_==c?l:_,S=S==c?l:S;var P=_==l,C=S==l,A=_==S;if(A&&o(v)){if(!o(d))return!1;g=!0,P=!1}if(A&&!P)return O||(O=new e),g||u(v)?t(v,d,x,w,b,O):r(v,d,_,x,w,b,O);if(!(x&s)){var j=P&&p.call(v,"__wrapped__"),E=C&&p.call(d,"__wrapped__");if(j||E){var N=j?v.value():v,I=E?d.value():d;return O||(O=new e),b(N,I,x,w,O)}}return A?(O||(O=new e),n(v,d,x,w,b,O)):!1}return es=y,es}var ts,Lp;function Cf(){if(Lp)return ts;Lp=1;var e=u_(),t=wt();function r(n,i,a,o,u){return n===i?!0:n==null||i==null||!t(n)&&!t(i)?n!==n&&i!==i:e(n,i,a,o,r,u)}return ts=r,ts}var rs,Fp;function s_(){if(Fp)return rs;Fp=1;var e=Ib(),t=Cf(),r=1,n=2;function i(a,o,u,s){var c=u.length,f=c,l=!s;if(a==null)return!f;for(a=Object(a);c--;){var h=u[c];if(l&&h[2]?h[1]!==a[h[0]]:!(h[0]in a))return!1}for(;++c<f;){h=u[c];var p=h[0],y=a[p],v=h[1];if(l&&h[2]){if(y===void 0&&!(p in a))return!1}else{var d=new e;if(s)var x=s(y,v,p,a,o,d);if(!(x===void 0?t(v,y,r|n,s,d):x))return!1}}return!0}return rs=i,rs}var ns,Up;function Wb(){if(Up)return ns;Up=1;var e=Mt();function t(r){return r===r&&!e(r)}return ns=t,ns}var is,zp;function c_(){if(zp)return is;zp=1;var e=Wb(),t=_a();function r(n){for(var i=t(n),a=i.length;a--;){var o=i[a],u=n[o];i[a]=[o,u,e(u)]}return i}return is=r,is}var as,Wp;function Hb(){if(Wp)return as;Wp=1;function e(t,r){return function(n){return n==null?!1:n[t]===r&&(r!==void 0||t in Object(n))}}return as=e,as}var os,Hp;function l_(){if(Hp)return os;Hp=1;var e=s_(),t=c_(),r=Hb();function n(i){var a=t(i);return a.length==1&&a[0][2]?r(a[0][0],a[0][1]):function(o){return o===i||e(o,i,a)}}return os=n,os}var us,Gp;function f_(){if(Gp)return us;Gp=1;function e(t,r){return t!=null&&r in Object(t)}return us=e,us}var ss,Kp;function h_(){if(Kp)return ss;Kp=1;var e=lb(),t=jf(),r=Re(),n=Ef(),i=Mf(),a=ga();function o(u,s,c){s=e(s,u);for(var f=-1,l=s.length,h=!1;++f<l;){var p=a(s[f]);if(!(h=u!=null&&c(u,p)))break;u=u[p]}return h||++f!=l?h:(l=u==null?0:u.length,!!l&&i(l)&&n(p,l)&&(r(u)||t(u)))}return ss=o,ss}var cs,Vp;function d_(){if(Vp)return cs;Vp=1;var e=f_(),t=h_();function r(n,i){return n!=null&&t(n,i,e)}return cs=r,cs}var ls,Xp;function p_(){if(Xp)return ls;Xp=1;var e=Cf(),t=fb(),r=d_(),n=df(),i=Wb(),a=Hb(),o=ga(),u=1,s=2;function c(f,l){return n(f)&&i(l)?a(o(f),l):function(h){var p=t(h,f);return p===void 0&&p===l?r(h,f):e(l,p,u|s)}}return ls=c,ls}var fs,Yp;function Fr(){if(Yp)return fs;Yp=1;function e(t){return t}return fs=e,fs}var hs,Zp;function v_(){if(Zp)return hs;Zp=1;function e(t){return function(r){return r?.[t]}}return hs=e,hs}var ds,Jp;function y_(){if(Jp)return ds;Jp=1;var e=bf();function t(r){return function(n){return e(n,r)}}return ds=t,ds}var ps,Qp;function g_(){if(Qp)return ps;Qp=1;var e=v_(),t=y_(),r=df(),n=ga();function i(a){return r(a)?e(n(a)):t(a)}return ps=i,ps}var vs,ev;function Ct(){if(ev)return vs;ev=1;var e=l_(),t=p_(),r=Fr(),n=Re(),i=g_();function a(o){return typeof o=="function"?o:o==null?r:typeof o=="object"?n(o)?t(o[0],o[1]):e(o):i(o)}return vs=a,vs}var ys,tv;function Gb(){if(tv)return ys;tv=1;function e(t,r,n,i){for(var a=t.length,o=n+(i?1:-1);i?o--:++o<a;)if(r(t[o],o,t))return o;return-1}return ys=e,ys}var gs,rv;function m_(){if(rv)return gs;rv=1;function e(t){return t!==t}return gs=e,gs}var ms,nv;function b_(){if(nv)return ms;nv=1;function e(t,r,n){for(var i=n-1,a=t.length;++i<a;)if(t[i]===r)return i;return-1}return ms=e,ms}var bs,iv;function x_(){if(iv)return bs;iv=1;var e=Gb(),t=m_(),r=b_();function n(i,a,o){return a===a?r(i,a,o):e(i,t,o)}return bs=n,bs}var xs,av;function w_(){if(av)return xs;av=1;var e=x_();function t(r,n){var i=r==null?0:r.length;return!!i&&e(r,n,0)>-1}return xs=t,xs}var ws,ov;function O_(){if(ov)return ws;ov=1;function e(t,r,n){for(var i=-1,a=t==null?0:t.length;++i<a;)if(n(r,t[i]))return!0;return!1}return ws=e,ws}var Os,uv;function __(){if(uv)return Os;uv=1;function e(){}return Os=e,Os}var _s,sv;function S_(){if(sv)return _s;sv=1;var e=zb(),t=__(),r=Tf(),n=1/0,i=e&&1/r(new e([,-0]))[1]==n?function(a){return new e(a)}:t;return _s=i,_s}var Ss,cv;function A_(){if(cv)return Ss;cv=1;var e=Nb(),t=w_(),r=O_(),n=kb(),i=S_(),a=Tf(),o=200;function u(s,c,f){var l=-1,h=t,p=s.length,y=!0,v=[],d=v;if(f)y=!1,h=r;else if(p>=o){var x=c?null:i(s);if(x)return a(x);y=!1,h=n,d=new e}else d=c?[]:v;e:for(;++l<p;){var w=s[l],b=c?c(w):w;if(w=f||w!==0?w:0,y&&b===b){for(var O=d.length;O--;)if(d[O]===b)continue e;c&&d.push(b),v.push(w)}else h(d,b,f)||(d!==v&&d.push(b),v.push(w))}return v}return Ss=u,Ss}var As,lv;function P_(){if(lv)return As;lv=1;var e=Ct(),t=A_();function r(n,i){return n&&n.length?t(n,e(i,2)):[]}return As=r,As}var T_=P_();const fv=se(T_);function Kb(e,t,r){return t===!0?fv(e,r):Z(t)?fv(e,t):e}function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}var j_=["ref"];function hv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ct(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hv(Object(r),!0).forEach(function(n){Sa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function E_(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dv(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xb(n.key),n)}}function M_(e,t,r){return t&&dv(e.prototype,t),r&&dv(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function C_(e,t,r){return t=Ti(t),$_(e,Vb()?Reflect.construct(t,r||[],Ti(e).constructor):t.apply(e,r))}function $_(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return I_(e)}function I_(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vb(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Vb=function(){return!!e})()}function Ti(e){return Ti=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ti(e)}function N_(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ul(e,t)}function ul(e,t){return ul=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ul(e,t)}function Sa(e,t,r){return t=Xb(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xb(e){var t=R_(e,"string");return xr(t)=="symbol"?t:t+""}function R_(e,t){if(xr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function k_(e,t){if(e==null)return{};var r=D_(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function D_(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function q_(e){return e.value}function B_(e,t){if(T.isValidElement(e))return T.cloneElement(e,t);if(typeof e=="function")return T.createElement(e,t);t.ref;var r=k_(t,j_);return T.createElement(Pf,r)}var pv=1,vr=function(e){function t(){var r;E_(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=C_(this,t,[].concat(i)),Sa(r,"lastBoundingBox",{width:-1,height:-1}),r}return N_(t,e),M_(t,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>pv||Math.abs(i.height-this.lastBoundingBox.height)>pv)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?ct({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,s=i.margin,c=i.chartWidth,f=i.chartHeight,l,h;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var p=this.getBBoxSnapshot();l={left:((c||0)-p.width)/2}}else l=o==="right"?{right:s&&s.right||0}:{left:s&&s.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var y=this.getBBoxSnapshot();h={top:((f||0)-y.height)/2}}else h=u==="bottom"?{bottom:s&&s.bottom||0}:{top:s&&s.top||0};return ct(ct({},l),h)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,s=i.wrapperStyle,c=i.payloadUniqBy,f=i.payload,l=ct(ct({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(s)),s);return T.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(p){n.wrapperNode=p}},B_(a,ct(ct({},this.props),{},{payload:Kb(f,c,q_)})))}}],[{key:"getWithHeight",value:function(n,i){var a=ct(ct({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&B(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(q.PureComponent);Sa(vr,"displayName","Legend");Sa(vr,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Ps,vv;function L_(){if(vv)return Ps;vv=1;var e=Gn(),t=jf(),r=Re(),n=e?e.isConcatSpreadable:void 0;function i(a){return r(a)||t(a)||!!(n&&a&&a[n])}return Ps=i,Ps}var Ts,yv;function Yb(){if(yv)return Ts;yv=1;var e=qb(),t=L_();function r(n,i,a,o,u){var s=-1,c=n.length;for(a||(a=t),u||(u=[]);++s<c;){var f=n[s];i>0&&a(f)?i>1?r(f,i-1,a,o,u):e(u,f):o||(u[u.length]=f)}return u}return Ts=r,Ts}var js,gv;function F_(){if(gv)return js;gv=1;function e(t){return function(r,n,i){for(var a=-1,o=Object(r),u=i(r),s=u.length;s--;){var c=u[t?s:++a];if(n(o[c],c,o)===!1)break}return r}}return js=e,js}var Es,mv;function U_(){if(mv)return Es;mv=1;var e=F_(),t=e();return Es=t,Es}var Ms,bv;function Zb(){if(bv)return Ms;bv=1;var e=U_(),t=_a();function r(n,i){return n&&e(n,i,t)}return Ms=r,Ms}var Cs,xv;function z_(){if(xv)return Cs;xv=1;var e=Vn();function t(r,n){return function(i,a){if(i==null)return i;if(!e(i))return r(i,a);for(var o=i.length,u=n?o:-1,s=Object(i);(n?u--:++u<o)&&a(s[u],u,s)!==!1;);return i}}return Cs=t,Cs}var $s,wv;function $f(){if(wv)return $s;wv=1;var e=Zb(),t=z_(),r=t(e);return $s=r,$s}var Is,Ov;function Jb(){if(Ov)return Is;Ov=1;var e=$f(),t=Vn();function r(n,i){var a=-1,o=t(n)?Array(n.length):[];return e(n,function(u,s,c){o[++a]=i(u,s,c)}),o}return Is=r,Is}var Ns,_v;function W_(){if(_v)return Ns;_v=1;function e(t,r){var n=t.length;for(t.sort(r);n--;)t[n]=t[n].value;return t}return Ns=e,Ns}var Rs,Sv;function H_(){if(Sv)return Rs;Sv=1;var e=Br();function t(r,n){if(r!==n){var i=r!==void 0,a=r===null,o=r===r,u=e(r),s=n!==void 0,c=n===null,f=n===n,l=e(n);if(!c&&!l&&!u&&r>n||u&&s&&f&&!c&&!l||a&&s&&f||!i&&f||!o)return 1;if(!a&&!u&&!l&&r<n||l&&i&&o&&!a&&!u||c&&i&&o||!s&&o||!f)return-1}return 0}return Rs=t,Rs}var ks,Av;function G_(){if(Av)return ks;Av=1;var e=H_();function t(r,n,i){for(var a=-1,o=r.criteria,u=n.criteria,s=o.length,c=i.length;++a<s;){var f=e(o[a],u[a]);if(f){if(a>=c)return f;var l=i[a];return f*(l=="desc"?-1:1)}}return r.index-n.index}return ks=t,ks}var Ds,Pv;function K_(){if(Pv)return Ds;Pv=1;var e=mf(),t=bf(),r=Ct(),n=Jb(),i=W_(),a=Lb(),o=G_(),u=Fr(),s=Re();function c(f,l,h){l.length?l=e(l,function(v){return s(v)?function(d){return t(d,v.length===1?v[0]:v)}:v}):l=[u];var p=-1;l=e(l,a(r));var y=n(f,function(v,d,x){var w=e(l,function(b){return b(v)});return{criteria:w,index:++p,value:v}});return i(y,function(v,d){return o(v,d,h)})}return Ds=c,Ds}var qs,Tv;function V_(){if(Tv)return qs;Tv=1;function e(t,r,n){switch(n.length){case 0:return t.call(r);case 1:return t.call(r,n[0]);case 2:return t.call(r,n[0],n[1]);case 3:return t.call(r,n[0],n[1],n[2])}return t.apply(r,n)}return qs=e,qs}var Bs,jv;function X_(){if(jv)return Bs;jv=1;var e=V_(),t=Math.max;function r(n,i,a){return i=t(i===void 0?n.length-1:i,0),function(){for(var o=arguments,u=-1,s=t(o.length-i,0),c=Array(s);++u<s;)c[u]=o[i+u];u=-1;for(var f=Array(i+1);++u<i;)f[u]=o[u];return f[i]=a(c),e(n,this,f)}}return Bs=r,Bs}var Ls,Ev;function Y_(){if(Ev)return Ls;Ev=1;function e(t){return function(){return t}}return Ls=e,Ls}var Fs,Mv;function Qb(){if(Mv)return Fs;Mv=1;var e=Qt(),t=function(){try{var r=e(Object,"defineProperty");return r({},"",{}),r}catch{}}();return Fs=t,Fs}var Us,Cv;function Z_(){if(Cv)return Us;Cv=1;var e=Y_(),t=Qb(),r=Fr(),n=t?function(i,a){return t(i,"toString",{configurable:!0,enumerable:!1,value:e(a),writable:!0})}:r;return Us=n,Us}var zs,$v;function J_(){if($v)return zs;$v=1;var e=800,t=16,r=Date.now;function n(i){var a=0,o=0;return function(){var u=r(),s=t-(u-o);if(o=u,s>0){if(++a>=e)return arguments[0]}else a=0;return i.apply(void 0,arguments)}}return zs=n,zs}var Ws,Iv;function Q_(){if(Iv)return Ws;Iv=1;var e=Z_(),t=J_(),r=t(e);return Ws=r,Ws}var Hs,Nv;function eS(){if(Nv)return Hs;Nv=1;var e=Fr(),t=X_(),r=Q_();function n(i,a){return r(t(i,a,e),i+"")}return Hs=n,Hs}var Gs,Rv;function Aa(){if(Rv)return Gs;Rv=1;var e=vf(),t=Vn(),r=Ef(),n=Mt();function i(a,o,u){if(!n(u))return!1;var s=typeof o;return(s=="number"?t(u)&&r(o,u.length):s=="string"&&o in u)?e(u[o],a):!1}return Gs=i,Gs}var Ks,kv;function tS(){if(kv)return Ks;kv=1;var e=Yb(),t=K_(),r=eS(),n=Aa(),i=r(function(a,o){if(a==null)return[];var u=o.length;return u>1&&n(a,o[0],o[1])?o=[]:u>2&&n(o[0],o[1],o[2])&&(o=[o[0]]),t(a,e(o,1),[])});return Ks=i,Ks}var rS=tS();const If=se(rS);function dn(e){"@babel/helpers - typeof";return dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dn(e)}function sl(){return sl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},sl.apply(this,arguments)}function nS(e,t){return uS(e)||oS(e,t)||aS(e,t)||iS()}function iS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function aS(e,t){if(e){if(typeof e=="string")return Dv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dv(e,t)}}function Dv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function uS(e){if(Array.isArray(e))return e}function qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vs(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qv(Object(r),!0).forEach(function(n){sS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sS(e,t,r){return t=cS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cS(e){var t=lS(e,"string");return dn(t)=="symbol"?t:t+""}function lS(e,t){if(dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fS(e){return Array.isArray(e)&&xe(e[0])&&xe(e[1])?e.join(" ~ "):e}var hS=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,s=t.labelStyle,c=s===void 0?{}:s,f=t.payload,l=t.formatter,h=t.itemSorter,p=t.wrapperClassName,y=t.labelClassName,v=t.label,d=t.labelFormatter,x=t.accessibilityLayer,w=x===void 0?!1:x,b=function(){if(f&&f.length){var j={padding:0,margin:0},E=(h?If(f,h):f).map(function(N,I){if(N.type==="none")return null;var R=Vs({display:"block",paddingTop:4,paddingBottom:4,color:N.color||"#000"},u),D=N.formatter||l||fS,L=N.value,F=N.name,H=L,K=F;if(D&&H!=null&&K!=null){var z=D(L,F,N,I,f);if(Array.isArray(z)){var V=nS(z,2);H=V[0],K=V[1]}else H=z}return T.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(I),style:R},xe(K)?T.createElement("span",{className:"recharts-tooltip-item-name"},K):null,xe(K)?T.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,T.createElement("span",{className:"recharts-tooltip-item-value"},H),T.createElement("span",{className:"recharts-tooltip-item-unit"},N.unit||""))});return T.createElement("ul",{className:"recharts-tooltip-item-list",style:j},E)}return null},O=Vs({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),g=Vs({margin:0},c),m=!ne(v),_=m?v:"",S=ie("recharts-default-tooltip",p),P=ie("recharts-tooltip-label",y);m&&d&&f!==void 0&&f!==null&&(_=d(v,f));var C=w?{role:"status","aria-live":"assertive"}:{};return T.createElement("div",sl({className:S,style:O},C),T.createElement("p",{className:P,style:g},T.isValidElement(_)?_:"".concat(_)),b())};function pn(e){"@babel/helpers - typeof";return pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pn(e)}function ui(e,t,r){return t=dS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dS(e){var t=pS(e,"string");return pn(t)=="symbol"?t:t+""}function pS(e,t){if(pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Gr="recharts-tooltip-wrapper",vS={visibility:"hidden"};function yS(e){var t=e.coordinate,r=e.translateX,n=e.translateY;return ie(Gr,ui(ui(ui(ui({},"".concat(Gr,"-right"),B(r)&&t&&B(t.x)&&r>=t.x),"".concat(Gr,"-left"),B(r)&&t&&B(t.x)&&r<t.x),"".concat(Gr,"-bottom"),B(n)&&t&&B(t.y)&&n>=t.y),"".concat(Gr,"-top"),B(n)&&t&&B(t.y)&&n<t.y))}function Bv(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,s=e.viewBox,c=e.viewBoxDimension;if(a&&B(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,p=s[n];return h<p?Math.max(l,s[n]):Math.max(f,s[n])}var y=l+u,v=s[n]+c;return y>v?Math.max(f,s[n]):Math.max(l,s[n])}function gS(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}function mS(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,s=e.viewBox,c,f,l;return o.height>0&&o.width>0&&r?(f=Bv({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:s,viewBoxDimension:s.width}),l=Bv({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:s,viewBoxDimension:s.height}),c=gS({translateX:f,translateY:l,useTranslate3d:u})):c=vS,{cssProperties:c,cssClasses:yS({translateX:f,translateY:l,coordinate:r})}}function wr(e){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wr(e)}function Lv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lv(Object(r),!0).forEach(function(n){ll(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function bS(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,t0(n.key),n)}}function wS(e,t,r){return t&&xS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function OS(e,t,r){return t=ji(t),_S(e,e0()?Reflect.construct(t,r||[],ji(e).constructor):t.apply(e,r))}function _S(e,t){if(t&&(wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return SS(e)}function SS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(e0=function(){return!!e})()}function ji(e){return ji=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ji(e)}function AS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cl(e,t)}function cl(e,t){return cl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},cl(e,t)}function ll(e,t,r){return t=t0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function t0(e){var t=PS(e,"string");return wr(t)=="symbol"?t:t+""}function PS(e,t){if(wr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Uv=1,TS=function(e){function t(){var r;bS(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=OS(this,t,[].concat(i)),ll(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),ll(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,s,c,f;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(s=r.props.coordinate)===null||s===void 0?void 0:s.x)!==null&&u!==void 0?u:0,y:(c=(f=r.props.coordinate)===null||f===void 0?void 0:f.y)!==null&&c!==void 0?c:0}})}}),r}return AS(t,e),wS(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Uv||Math.abs(n.height-this.state.lastBoundingBox.height)>Uv)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.children,f=i.coordinate,l=i.hasPayload,h=i.isAnimationActive,p=i.offset,y=i.position,v=i.reverseDirection,d=i.useTranslate3d,x=i.viewBox,w=i.wrapperStyle,b=mS({allowEscapeViewBox:o,coordinate:f,offsetTopLeft:p,position:y,reverseDirection:v,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:x}),O=b.cssClasses,g=b.cssProperties,m=Fv(Fv({transition:h&&a?"transform ".concat(u,"ms ").concat(s):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&l?"visible":"hidden",position:"absolute",top:0,left:0},w);return T.createElement("div",{tabIndex:-1,className:O,style:m,ref:function(S){n.wrapperNode=S}},c)}}])}(q.PureComponent),jS=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Xn={isSsr:jS()};function Or(e){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(e)}function zv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Wv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zv(Object(r),!0).forEach(function(n){Nf(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ES(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function MS(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n0(n.key),n)}}function CS(e,t,r){return t&&MS(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function $S(e,t,r){return t=Ei(t),IS(e,r0()?Reflect.construct(t,r||[],Ei(e).constructor):t.apply(e,r))}function IS(e,t){if(t&&(Or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return NS(e)}function NS(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function r0(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(r0=function(){return!!e})()}function Ei(e){return Ei=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ei(e)}function RS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&fl(e,t)}function fl(e,t){return fl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},fl(e,t)}function Nf(e,t,r){return t=n0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n0(e){var t=kS(e,"string");return Or(t)=="symbol"?t:t+""}function kS(e,t){if(Or(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Or(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function DS(e){return e.dataKey}function qS(e,t){return T.isValidElement(e)?T.cloneElement(e,t):typeof e=="function"?T.createElement(e,t):T.createElement(hS,t)}var Qe=function(e){function t(){return ES(this,t),$S(this,t,arguments)}return RS(t,e),CS(t,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,s=i.animationEasing,c=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,p=i.offset,y=i.payload,v=i.payloadUniqBy,d=i.position,x=i.reverseDirection,w=i.useTranslate3d,b=i.viewBox,O=i.wrapperStyle,g=y??[];l&&g.length&&(g=Kb(y.filter(function(_){return _.value!=null&&(_.hide!==!0||n.props.includeHidden)}),v,DS));var m=g.length>0;return T.createElement(TS,{allowEscapeViewBox:o,animationDuration:u,animationEasing:s,isAnimationActive:h,active:a,coordinate:f,hasPayload:m,offset:p,position:d,reverseDirection:x,useTranslate3d:w,viewBox:b,wrapperStyle:O},qS(c,Wv(Wv({},this.props),{},{payload:g})))}}])}(q.PureComponent);Nf(Qe,"displayName","Tooltip");Nf(Qe,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Xn.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Xs,Hv;function BS(){if(Hv)return Xs;Hv=1;var e=st(),t=function(){return e.Date.now()};return Xs=t,Xs}var Ys,Gv;function LS(){if(Gv)return Ys;Gv=1;var e=/\s/;function t(r){for(var n=r.length;n--&&e.test(r.charAt(n)););return n}return Ys=t,Ys}var Zs,Kv;function FS(){if(Kv)return Zs;Kv=1;var e=LS(),t=/^\s+/;function r(n){return n&&n.slice(0,e(n)+1).replace(t,"")}return Zs=r,Zs}var Js,Vv;function i0(){if(Vv)return Js;Vv=1;var e=FS(),t=Mt(),r=Br(),n=NaN,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,o=/^0o[0-7]+$/i,u=parseInt;function s(c){if(typeof c=="number")return c;if(r(c))return n;if(t(c)){var f=typeof c.valueOf=="function"?c.valueOf():c;c=t(f)?f+"":f}if(typeof c!="string")return c===0?c:+c;c=e(c);var l=a.test(c);return l||o.test(c)?u(c.slice(2),l?2:8):i.test(c)?n:+c}return Js=s,Js}var Qs,Xv;function US(){if(Xv)return Qs;Xv=1;var e=Mt(),t=BS(),r=i0(),n="Expected a function",i=Math.max,a=Math.min;function o(u,s,c){var f,l,h,p,y,v,d=0,x=!1,w=!1,b=!0;if(typeof u!="function")throw new TypeError(n);s=r(s)||0,e(c)&&(x=!!c.leading,w="maxWait"in c,h=w?i(r(c.maxWait)||0,s):h,b="trailing"in c?!!c.trailing:b);function O(E){var N=f,I=l;return f=l=void 0,d=E,p=u.apply(I,N),p}function g(E){return d=E,y=setTimeout(S,s),x?O(E):p}function m(E){var N=E-v,I=E-d,R=s-N;return w?a(R,h-I):R}function _(E){var N=E-v,I=E-d;return v===void 0||N>=s||N<0||w&&I>=h}function S(){var E=t();if(_(E))return P(E);y=setTimeout(S,m(E))}function P(E){return y=void 0,b&&f?O(E):(f=l=void 0,p)}function C(){y!==void 0&&clearTimeout(y),d=0,f=v=l=y=void 0}function A(){return y===void 0?p:P(t())}function j(){var E=t(),N=_(E);if(f=arguments,l=this,v=E,N){if(y===void 0)return g(v);if(w)return clearTimeout(y),y=setTimeout(S,s),O(v)}return y===void 0&&(y=setTimeout(S,s)),p}return j.cancel=C,j.flush=A,j}return Qs=o,Qs}var ec,Yv;function zS(){if(Yv)return ec;Yv=1;var e=US(),t=Mt(),r="Expected a function";function n(i,a,o){var u=!0,s=!0;if(typeof i!="function")throw new TypeError(r);return t(o)&&(u="leading"in o?!!o.leading:u,s="trailing"in o?!!o.trailing:s),e(i,a,{leading:u,maxWait:a,trailing:s})}return ec=n,ec}var WS=zS();const a0=se(WS);function vn(e){"@babel/helpers - typeof";return vn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vn(e)}function Zv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function si(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zv(Object(r),!0).forEach(function(n){HS(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function HS(e,t,r){return t=GS(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GS(e){var t=KS(e,"string");return vn(t)=="symbol"?t:t+""}function KS(e,t){if(vn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(vn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VS(e,t){return JS(e)||ZS(e,t)||YS(e,t)||XS()}function XS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function YS(e,t){if(e){if(typeof e=="string")return Jv(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jv(e,t)}}function Jv(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ZS(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function JS(e){if(Array.isArray(e))return e}var QS=q.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,s=u===void 0?"100%":u,c=e.minWidth,f=c===void 0?0:c,l=e.minHeight,h=e.maxHeight,p=e.children,y=e.debounce,v=y===void 0?0:y,d=e.id,x=e.className,w=e.onResize,b=e.style,O=b===void 0?{}:b,g=q.useRef(null),m=q.useRef();m.current=w,q.useImperativeHandle(t,function(){return Object.defineProperty(g.current,"current",{get:function(){return g.current},configurable:!0})});var _=q.useState({containerWidth:i.width,containerHeight:i.height}),S=VS(_,2),P=S[0],C=S[1],A=q.useCallback(function(E,N){C(function(I){var R=Math.round(E),D=Math.round(N);return I.containerWidth===R&&I.containerHeight===D?I:{containerWidth:R,containerHeight:D}})},[]);q.useEffect(function(){var E=function(F){var H,K=F[0].contentRect,z=K.width,V=K.height;A(z,V),(H=m.current)===null||H===void 0||H.call(m,z,V)};v>0&&(E=a0(E,v,{trailing:!0,leading:!1}));var N=new ResizeObserver(E),I=g.current.getBoundingClientRect(),R=I.width,D=I.height;return A(R,D),N.observe(g.current),function(){N.disconnect()}},[A,v]);var j=q.useMemo(function(){var E=P.containerWidth,N=P.containerHeight;if(E<0||N<0)return null;pt(Ut(o)||Ut(s),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,s),pt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var I=Ut(o)?E:o,R=Ut(s)?N:s;r&&r>0&&(I?R=I/r:R&&(I=R*r),h&&R>h&&(R=h)),pt(I>0||R>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,I,R,o,s,f,l,r);var D=!Array.isArray(p)&&dt(p.type).endsWith("Chart");return T.Children.map(p,function(L){return T.isValidElement(L)?q.cloneElement(L,si({width:I,height:R},D?{style:si({height:"100%",width:"100%",maxHeight:R,maxWidth:I},L.props.style)}:{})):L})},[r,p,s,h,l,f,P,o]);return T.createElement("div",{id:d?"".concat(d):void 0,className:ie("recharts-responsive-container",x),style:si(si({},O),{},{width:o,height:s,minWidth:f,minHeight:l,maxHeight:h}),ref:g},j)}),o0=function(t){return null};o0.displayName="Cell";function yn(e){"@babel/helpers - typeof";return yn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yn(e)}function Qv(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Qv(Object(r),!0).forEach(function(n){eA(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Qv(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function eA(e,t,r){return t=tA(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tA(e){var t=rA(e,"string");return yn(t)=="symbol"?t:t+""}function rA(e,t){if(yn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(yn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ur={widthCache:{},cacheCount:0},nA=2e3,iA={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},ey="recharts_measurement_span";function aA(e){var t=hl({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var un=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||Xn.isSsr)return{width:0,height:0};var n=aA(r),i=JSON.stringify({text:t,copyStyle:n});if(ur.widthCache[i])return ur.widthCache[i];try{var a=document.getElementById(ey);a||(a=document.createElement("span"),a.setAttribute("id",ey),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=hl(hl({},iA),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),s={width:u.width,height:u.height};return ur.widthCache[i]=s,++ur.cacheCount>nA&&(ur.cacheCount=0,ur.widthCache={}),s}catch{return{width:0,height:0}}},oA=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function gn(e){"@babel/helpers - typeof";return gn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gn(e)}function Mi(e,t){return lA(e)||cA(e,t)||sA(e,t)||uA()}function uA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sA(e,t){if(e){if(typeof e=="string")return ty(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ty(e,t)}}function ty(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function lA(e){if(Array.isArray(e))return e}function fA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ry(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dA(n.key),n)}}function hA(e,t,r){return t&&ry(e.prototype,t),r&&ry(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function dA(e){var t=pA(e,"string");return gn(t)=="symbol"?t:t+""}function pA(e,t){if(gn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(gn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ny=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,iy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,vA=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,yA=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,u0={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},gA=Object.keys(u0),lr="NaN";function mA(e,t){return e*u0[t]}var ci=function(){function e(t,r){fA(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!vA.test(r)&&(this.num=NaN,this.unit=""),gA.includes(r)&&(this.num=mA(t,r),this.unit="px")}return hA(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=yA.exec(r))!==null&&n!==void 0?n:[],a=Mi(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}])}();function s0(e){if(e.includes(lr))return lr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=ny.exec(t))!==null&&r!==void 0?r:[],i=Mi(n,4),a=i[1],o=i[2],u=i[3],s=ci.parse(a??""),c=ci.parse(u??""),f=o==="*"?s.multiply(c):s.divide(c);if(f.isNaN())return lr;t=t.replace(ny,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=iy.exec(t))!==null&&l!==void 0?l:[],p=Mi(h,4),y=p[1],v=p[2],d=p[3],x=ci.parse(y??""),w=ci.parse(d??""),b=v==="+"?x.add(w):x.subtract(w);if(b.isNaN())return lr;t=t.replace(iy,b.toString())}return t}var ay=/\(([^()]*)\)/;function bA(e){for(var t=e;t.includes("(");){var r=ay.exec(t),n=Mi(r,2),i=n[1];t=t.replace(ay,s0(i))}return t}function xA(e){var t=e.replace(/\s+/g,"");return t=bA(t),t=s0(t),t}function wA(e){try{return xA(e)}catch{return lr}}function tc(e){var t=wA(e.slice(5,-1));return t===lr?"":t}var OA=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],_A=["dx","dy","angle","className","breakAll"];function dl(){return dl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},dl.apply(this,arguments)}function oy(e,t){if(e==null)return{};var r=SA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function SA(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function uy(e,t){return jA(e)||TA(e,t)||PA(e,t)||AA()}function AA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function PA(e,t){if(e){if(typeof e=="string")return sy(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sy(e,t)}}function sy(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function TA(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function jA(e){if(Array.isArray(e))return e}var c0=/[ \f\n\r\t\v\u2028\u2029]+/,l0=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];ne(r)||(n?a=r.toString().split(""):a=r.toString().split(c0));var o=a.map(function(s){return{word:s,width:un(s,i).width}}),u=n?0:un(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},EA=function(t,r,n,i,a){var o=t.maxLines,u=t.children,s=t.style,c=t.breakAll,f=B(o),l=u,h=function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return I.reduce(function(R,D){var L=D.word,F=D.width,H=R[R.length-1];if(H&&(i==null||a||H.width+F+n<Number(i)))H.words.push(L),H.width+=F+n;else{var K={words:[L],width:F};R.push(K)}return R},[])},p=h(r),y=function(I){return I.reduce(function(R,D){return R.width>D.width?R:D})};if(!f)return p;for(var v="…",d=function(I){var R=l.slice(0,I),D=l0({breakAll:c,style:s,children:R+v}).wordsWithComputedWidth,L=h(D),F=L.length>o||y(L).width>Number(i);return[F,L]},x=0,w=l.length-1,b=0,O;x<=w&&b<=l.length-1;){var g=Math.floor((x+w)/2),m=g-1,_=d(m),S=uy(_,2),P=S[0],C=S[1],A=d(g),j=uy(A,1),E=j[0];if(!P&&!E&&(x=g+1),P&&E&&(w=g-1),!P&&E){O=C;break}b++}return O||p},cy=function(t){var r=ne(t)?[]:t.toString().split(c0);return[{words:r}]},MA=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!Xn.isSsr){var s,c,f=l0({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;s=l,c=h}else return cy(i);return EA({breakAll:o,children:i,maxLines:u,style:a},s,c,r,n)}return cy(i)},ly="#808080",Ci=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,s=t.capHeight,c=s===void 0?"0.71em":s,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,p=h===void 0?"start":h,y=t.verticalAnchor,v=y===void 0?"end":y,d=t.fill,x=d===void 0?ly:d,w=oy(t,OA),b=q.useMemo(function(){return MA({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:l,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,l,w.style,w.width]),O=w.dx,g=w.dy,m=w.angle,_=w.className,S=w.breakAll,P=oy(w,_A);if(!xe(n)||!xe(a))return null;var C=n+(B(O)?O:0),A=a+(B(g)?g:0),j;switch(v){case"start":j=tc("calc(".concat(c,")"));break;case"middle":j=tc("calc(".concat((b.length-1)/2," * -").concat(u," + (").concat(c," / 2))"));break;default:j=tc("calc(".concat(b.length-1," * -").concat(u,")"));break}var E=[];if(l){var N=b[0].width,I=w.width;E.push("scale(".concat((B(I)?I/N:1)/N,")"))}return m&&E.push("rotate(".concat(m,", ").concat(C,", ").concat(A,")")),E.length&&(P.transform=E.join(" ")),T.createElement("text",dl({},ee(P,!0),{x:C,y:A,className:ie("recharts-text",_),textAnchor:p,fill:x.includes("url")?ly:x}),b.map(function(R,D){var L=R.words.join(S?"":" ");return T.createElement("tspan",{x:C,dy:D===0?j:u,key:"".concat(L,"-").concat(D)},L)}))};function jt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function CA(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Rf(e){let t,r,n;e.length!==2?(t=jt,r=(u,s)=>jt(e(u),s),n=(u,s)=>e(u)-s):(t=e===jt||e===CA?e:$A,r=e,n=e);function i(u,s,c=0,f=u.length){if(c<f){if(t(s,s)!==0)return f;do{const l=c+f>>>1;r(u[l],s)<0?c=l+1:f=l}while(c<f)}return c}function a(u,s,c=0,f=u.length){if(c<f){if(t(s,s)!==0)return f;do{const l=c+f>>>1;r(u[l],s)<=0?c=l+1:f=l}while(c<f)}return c}function o(u,s,c=0,f=u.length){const l=i(u,s,c,f-1);return l>c&&n(u[l-1],s)>-n(u[l],s)?l-1:l}return{left:i,center:o,right:a}}function $A(){return 0}function f0(e){return e===null?NaN:+e}function*IA(e,t){for(let r of e)r!=null&&(r=+r)>=r&&(yield r)}const NA=Rf(jt),Yn=NA.right;Rf(f0).center;class fy extends Map{constructor(t,r=DA){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(hy(this,t))}has(t){return super.has(hy(this,t))}set(t,r){return super.set(RA(this,t),r)}delete(t){return super.delete(kA(this,t))}}function hy({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function RA({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function kA({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function DA(e){return e!==null&&typeof e=="object"?e.valueOf():e}function qA(e=jt){if(e===jt)return h0;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function h0(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const BA=Math.sqrt(50),LA=Math.sqrt(10),FA=Math.sqrt(2);function $i(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=BA?10:a>=LA?5:a>=FA?2:1;let u,s,c;return i<0?(c=Math.pow(10,-i)/o,u=Math.round(e*c),s=Math.round(t*c),u/c<e&&++u,s/c>t&&--s,c=-c):(c=Math.pow(10,i)*o,u=Math.round(e/c),s=Math.round(t/c),u*c<e&&++u,s*c>t&&--s),s<u&&.5<=r&&r<2?$i(e,t,r*2):[u,s,c]}function pl(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?$i(t,e,r):$i(e,t,r);if(!(a>=i))return[];const u=a-i+1,s=new Array(u);if(n)if(o<0)for(let c=0;c<u;++c)s[c]=(a-c)/-o;else for(let c=0;c<u;++c)s[c]=(a-c)*o;else if(o<0)for(let c=0;c<u;++c)s[c]=(i+c)/-o;else for(let c=0;c<u;++c)s[c]=(i+c)*o;return s}function vl(e,t,r){return t=+t,e=+e,r=+r,$i(e,t,r)[2]}function yl(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?vl(t,e,r):vl(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function dy(e,t){let r;for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);return r}function py(e,t){let r;for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);return r}function d0(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?h0:qA(i);n>r;){if(n-r>600){const s=n-r+1,c=t-r+1,f=Math.log(s),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(s-l)/s)*(c-s/2<0?-1:1),p=Math.max(r,Math.floor(t-c*l/s+h)),y=Math.min(n,Math.floor(t+(s-c)*l/s+h));d0(e,t,p,y,i)}const a=e[t];let o=r,u=n;for(Kr(e,r,t),i(e[n],a)>0&&Kr(e,r,n);o<u;){for(Kr(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Kr(e,r,u):(++u,Kr(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Kr(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function UA(e,t,r){if(e=Float64Array.from(IA(e)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return py(e);if(t>=1)return dy(e);var n,i=(n-1)*t,a=Math.floor(i),o=dy(d0(e,a).subarray(0,a+1)),u=py(e.subarray(a+1));return o+(u-o)*(i-a)}}function zA(e,t,r=f0){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function WA(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function He(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function Ot(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const gl=Symbol("implicit");function kf(){var e=new fy,t=[],r=[],n=gl;function i(a){let o=e.get(a);if(o===void 0){if(n!==gl)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new fy;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return kf(t,r).unknown(n)},He.apply(i,arguments),i}function mn(){var e=kf().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,s=0,c=0,f=.5;delete e.unknown;function l(){var h=t().length,p=i<n,y=p?i:n,v=p?n:i;a=(v-y)/Math.max(1,h-s+c*2),u&&(a=Math.floor(a)),y+=(v-y-a*(h-s))*f,o=a*(1-s),u&&(y=Math.round(y),o=Math.round(o));var d=WA(h).map(function(x){return y+a*x});return r(p?d.reverse():d)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(s=Math.min(1,c=+h),l()):s},e.paddingInner=function(h){return arguments.length?(s=Math.min(1,h),l()):s},e.paddingOuter=function(h){return arguments.length?(c=+h,l()):c},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return mn(t(),[n,i]).round(u).paddingInner(s).paddingOuter(c).align(f)},He.apply(l(),arguments)}function p0(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return p0(t())},e}function sn(){return p0(mn.apply(null,arguments).paddingInner(1))}function Df(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function v0(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Zn(){}var bn=.7,Ii=1/bn,yr="\\s*([+-]?\\d+)\\s*",xn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",it="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",HA=/^#([0-9a-f]{3,8})$/,GA=new RegExp(`^rgb\\(${yr},${yr},${yr}\\)$`),KA=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),VA=new RegExp(`^rgba\\(${yr},${yr},${yr},${xn}\\)$`),XA=new RegExp(`^rgba\\(${it},${it},${it},${xn}\\)$`),YA=new RegExp(`^hsl\\(${xn},${it},${it}\\)$`),ZA=new RegExp(`^hsla\\(${xn},${it},${it},${xn}\\)$`),vy={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Df(Zn,wn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:yy,formatHex:yy,formatHex8:JA,formatHsl:QA,formatRgb:gy,toString:gy});function yy(){return this.rgb().formatHex()}function JA(){return this.rgb().formatHex8()}function QA(){return y0(this).formatHsl()}function gy(){return this.rgb().formatRgb()}function wn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=HA.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?my(t):r===3?new Ne(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?li(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?li(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=GA.exec(e))?new Ne(t[1],t[2],t[3],1):(t=KA.exec(e))?new Ne(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=VA.exec(e))?li(t[1],t[2],t[3],t[4]):(t=XA.exec(e))?li(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=YA.exec(e))?wy(t[1],t[2]/100,t[3]/100,1):(t=ZA.exec(e))?wy(t[1],t[2]/100,t[3]/100,t[4]):vy.hasOwnProperty(e)?my(vy[e]):e==="transparent"?new Ne(NaN,NaN,NaN,0):null}function my(e){return new Ne(e>>16&255,e>>8&255,e&255,1)}function li(e,t,r,n){return n<=0&&(e=t=r=NaN),new Ne(e,t,r,n)}function eP(e){return e instanceof Zn||(e=wn(e)),e?(e=e.rgb(),new Ne(e.r,e.g,e.b,e.opacity)):new Ne}function ml(e,t,r,n){return arguments.length===1?eP(e):new Ne(e,t,r,n??1)}function Ne(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}Df(Ne,ml,v0(Zn,{brighter(e){return e=e==null?Ii:Math.pow(Ii,e),new Ne(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?bn:Math.pow(bn,e),new Ne(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ne(Gt(this.r),Gt(this.g),Gt(this.b),Ni(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:by,formatHex:by,formatHex8:tP,formatRgb:xy,toString:xy}));function by(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}`}function tP(){return`#${zt(this.r)}${zt(this.g)}${zt(this.b)}${zt((isNaN(this.opacity)?1:this.opacity)*255)}`}function xy(){const e=Ni(this.opacity);return`${e===1?"rgb(":"rgba("}${Gt(this.r)}, ${Gt(this.g)}, ${Gt(this.b)}${e===1?")":`, ${e})`}`}function Ni(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Gt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function zt(e){return e=Gt(e),(e<16?"0":"")+e.toString(16)}function wy(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Xe(e,t,r,n)}function y0(e){if(e instanceof Xe)return new Xe(e.h,e.s,e.l,e.opacity);if(e instanceof Zn||(e=wn(e)),!e)return new Xe;if(e instanceof Xe)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,s=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=s<.5?a+i:2-a-i,o*=60):u=s>0&&s<1?0:o,new Xe(o,u,s,e.opacity)}function rP(e,t,r,n){return arguments.length===1?y0(e):new Xe(e,t,r,n??1)}function Xe(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}Df(Xe,rP,v0(Zn,{brighter(e){return e=e==null?Ii:Math.pow(Ii,e),new Xe(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?bn:Math.pow(bn,e),new Xe(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Ne(rc(e>=240?e-240:e+120,i,n),rc(e,i,n),rc(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Xe(Oy(this.h),fi(this.s),fi(this.l),Ni(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ni(this.opacity);return`${e===1?"hsl(":"hsla("}${Oy(this.h)}, ${fi(this.s)*100}%, ${fi(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Oy(e){return e=(e||0)%360,e<0?e+360:e}function fi(e){return Math.max(0,Math.min(1,e||0))}function rc(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const qf=e=>()=>e;function nP(e,t){return function(r){return e+r*t}}function iP(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function aP(e){return(e=+e)==1?g0:function(t,r){return r-t?iP(t,r,e):qf(isNaN(t)?r:t)}}function g0(e,t){var r=t-e;return r?nP(e,r):qf(isNaN(e)?t:e)}const _y=function e(t){var r=aP(t);function n(i,a){var o=r((i=ml(i)).r,(a=ml(a)).r),u=r(i.g,a.g),s=r(i.b,a.b),c=g0(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=s(f),i.opacity=c(f),i+""}}return n.gamma=e,n}(1);function oP(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function uP(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function sP(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=Ur(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function cP(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Ri(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function lP(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=Ur(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var bl=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,nc=new RegExp(bl.source,"g");function fP(e){return function(){return e}}function hP(e){return function(t){return e(t)+""}}function dP(e,t){var r=bl.lastIndex=nc.lastIndex=0,n,i,a,o=-1,u=[],s=[];for(e=e+"",t=t+"";(n=bl.exec(e))&&(i=nc.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,s.push({i:o,x:Ri(n,i)})),r=nc.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?s[0]?hP(s[0].x):fP(t):(t=s.length,function(c){for(var f=0,l;f<t;++f)u[(l=s[f]).i]=l.x(c);return u.join("")})}function Ur(e,t){var r=typeof t,n;return t==null||r==="boolean"?qf(t):(r==="number"?Ri:r==="string"?(n=wn(t))?(t=n,_y):dP:t instanceof wn?_y:t instanceof Date?cP:uP(t)?oP:Array.isArray(t)?sP:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?lP:Ri)(e,t)}function Bf(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function pP(e,t){t===void 0&&(t=e,e=Ur);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function vP(e){return function(){return e}}function ki(e){return+e}var Sy=[0,1];function Ce(e){return e}function xl(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:vP(isNaN(t)?NaN:.5)}function yP(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function gP(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=xl(i,n),a=r(o,a)):(n=xl(n,i),a=r(a,o)),function(u){return a(n(u))}}function mP(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=xl(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var s=Yn(e,u,1,n)-1;return a[s](i[s](u))}}function Jn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Pa(){var e=Sy,t=Sy,r=Ur,n,i,a,o=Ce,u,s,c;function f(){var h=Math.min(e.length,t.length);return o!==Ce&&(o=yP(e[0],e[h-1])),u=h>2?mP:gP,s=c=null,l}function l(h){return h==null||isNaN(h=+h)?a:(s||(s=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((c||(c=u(t,e.map(n),Ri)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,ki),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=Bf,f()},l.clamp=function(h){return arguments.length?(o=h?!0:Ce,f()):o!==Ce},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,p){return n=h,i=p,f()}}function Lf(){return Pa()(Ce,Ce)}function bP(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Di(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function _r(e){return e=Di(Math.abs(e)),e?e[1]:NaN}function xP(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],s=0;i>0&&u>0&&(s+u+1>n&&(u=Math.max(1,n-s)),a.push(r.substring(i-=u,i+u)),!((s+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function wP(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var OP=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function On(e){if(!(t=OP.exec(e)))throw new Error("invalid format: "+e);var t;return new Ff({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}On.prototype=Ff.prototype;function Ff(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Ff.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function _P(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var m0;function SP(e,t){var r=Di(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(m0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Di(e,Math.max(0,t+a-1))[0]}function Ay(e,t){var r=Di(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const Py={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:bP,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ay(e*100,t),r:Ay,s:SP,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Ty(e){return e}var jy=Array.prototype.map,Ey=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function AP(e){var t=e.grouping===void 0||e.thousands===void 0?Ty:xP(jy.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Ty:wP(jy.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",s=e.nan===void 0?"NaN":e.nan+"";function c(l){l=On(l);var h=l.fill,p=l.align,y=l.sign,v=l.symbol,d=l.zero,x=l.width,w=l.comma,b=l.precision,O=l.trim,g=l.type;g==="n"?(w=!0,g="g"):Py[g]||(b===void 0&&(b=12),O=!0,g="g"),(d||h==="0"&&p==="=")&&(d=!0,h="0",p="=");var m=v==="$"?r:v==="#"&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",_=v==="$"?n:/[%p]/.test(g)?o:"",S=Py[g],P=/[defgprs%]/.test(g);b=b===void 0?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b));function C(A){var j=m,E=_,N,I,R;if(g==="c")E=S(A)+E,A="";else{A=+A;var D=A<0||1/A<0;if(A=isNaN(A)?s:S(Math.abs(A),b),O&&(A=_P(A)),D&&+A==0&&y!=="+"&&(D=!1),j=(D?y==="("?y:u:y==="-"||y==="("?"":y)+j,E=(g==="s"?Ey[8+m0/3]:"")+E+(D&&y==="("?")":""),P){for(N=-1,I=A.length;++N<I;)if(R=A.charCodeAt(N),48>R||R>57){E=(R===46?i+A.slice(N+1):A.slice(N))+E,A=A.slice(0,N);break}}}w&&!d&&(A=t(A,1/0));var L=j.length+A.length+E.length,F=L<x?new Array(x-L+1).join(h):"";switch(w&&d&&(A=t(F+A,F.length?x-E.length:1/0),F=""),p){case"<":A=j+A+E+F;break;case"=":A=j+F+A+E;break;case"^":A=F.slice(0,L=F.length>>1)+j+A+E+F.slice(L);break;default:A=F+j+A+E;break}return a(A)}return C.toString=function(){return l+""},C}function f(l,h){var p=c((l=On(l),l.type="f",l)),y=Math.max(-8,Math.min(8,Math.floor(_r(h)/3)))*3,v=Math.pow(10,-y),d=Ey[8+y/3];return function(x){return p(v*x)+d}}return{format:c,formatPrefix:f}}var hi,Uf,b0;PP({thousands:",",grouping:[3],currency:["$",""]});function PP(e){return hi=AP(e),Uf=hi.format,b0=hi.formatPrefix,hi}function TP(e){return Math.max(0,-_r(Math.abs(e)))}function jP(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(_r(t)/3)))*3-_r(Math.abs(e)))}function EP(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,_r(t)-_r(e))+1}function x0(e,t,r,n){var i=yl(e,t,r),a;switch(n=On(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=jP(i,o))&&(n.precision=a),b0(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=EP(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=TP(i))&&(n.precision=a-(n.type==="%")*2);break}}return Uf(n)}function $t(e){var t=e.domain;return e.ticks=function(r){var n=t();return pl(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return x0(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],s,c,f=10;for(u<o&&(c=o,o=u,u=c,c=i,i=a,a=c);f-- >0;){if(c=vl(o,u,r),c===s)return n[i]=o,n[a]=u,t(n);if(c>0)o=Math.floor(o/c)*c,u=Math.ceil(u/c)*c;else if(c<0)o=Math.ceil(o*c)/c,u=Math.floor(u*c)/c;else break;s=c}return e},e}function qi(){var e=Lf();return e.copy=function(){return Jn(e,qi())},He.apply(e,arguments),$t(e)}function w0(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,ki),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return w0(e).unknown(t)},e=arguments.length?Array.from(e,ki):[0,1],$t(r)}function O0(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function My(e){return Math.log(e)}function Cy(e){return Math.exp(e)}function MP(e){return-Math.log(-e)}function CP(e){return-Math.exp(-e)}function $P(e){return isFinite(e)?+("1e"+e):e<0?0:e}function IP(e){return e===10?$P:e===Math.E?Math.exp:t=>Math.pow(e,t)}function NP(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function $y(e){return(t,r)=>-e(-t,r)}function zf(e){const t=e(My,Cy),r=t.domain;let n=10,i,a;function o(){return i=NP(n),a=IP(n),r()[0]<0?(i=$y(i),a=$y(a),e(MP,CP)):e(My,Cy),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const s=r();let c=s[0],f=s[s.length-1];const l=f<c;l&&([c,f]=[f,c]);let h=i(c),p=i(f),y,v;const d=u==null?10:+u;let x=[];if(!(n%1)&&p-h<d){if(h=Math.floor(h),p=Math.ceil(p),c>0){for(;h<=p;++h)for(y=1;y<n;++y)if(v=h<0?y/a(-h):y*a(h),!(v<c)){if(v>f)break;x.push(v)}}else for(;h<=p;++h)for(y=n-1;y>=1;--y)if(v=h>0?y/a(-h):y*a(h),!(v<c)){if(v>f)break;x.push(v)}x.length*2<d&&(x=pl(c,f,d))}else x=pl(h,p,Math.min(p-h,d)).map(a);return l?x.reverse():x},t.tickFormat=(u,s)=>{if(u==null&&(u=10),s==null&&(s=n===10?"s":","),typeof s!="function"&&(!(n%1)&&(s=On(s)).precision==null&&(s.trim=!0),s=Uf(s)),u===1/0)return s;const c=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=c?s(f):""}},t.nice=()=>r(O0(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function _0(){const e=zf(Pa()).domain([1,10]);return e.copy=()=>Jn(e,_0()).base(e.base()),He.apply(e,arguments),e}function Iy(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Ny(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Wf(e){var t=1,r=e(Iy(t),Ny(t));return r.constant=function(n){return arguments.length?e(Iy(t=+n),Ny(t)):t},$t(r)}function S0(){var e=Wf(Pa());return e.copy=function(){return Jn(e,S0()).constant(e.constant())},He.apply(e,arguments)}function Ry(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function RP(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function kP(e){return e<0?-e*e:e*e}function Hf(e){var t=e(Ce,Ce),r=1;function n(){return r===1?e(Ce,Ce):r===.5?e(RP,kP):e(Ry(r),Ry(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},$t(t)}function Gf(){var e=Hf(Pa());return e.copy=function(){return Jn(e,Gf()).exponent(e.exponent())},He.apply(e,arguments),e}function DP(){return Gf.apply(null,arguments).exponent(.5)}function ky(e){return Math.sign(e)*e*e}function qP(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function A0(){var e=Lf(),t=[0,1],r=!1,n;function i(a){var o=qP(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(ky(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,ki)).map(ky)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return A0(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},He.apply(i,arguments),$t(i)}function P0(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=zA(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Yn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(jt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return P0().domain(e).range(t).unknown(n)},He.apply(a,arguments)}function T0(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(s){return s!=null&&s<=s?i[Yn(n,s,0,r)]:a}function u(){var s=-1;for(n=new Array(r);++s<r;)n[s]=((s+1)*t-(s-r)*e)/(r+1);return o}return o.domain=function(s){return arguments.length?([e,t]=s,e=+e,t=+t,u()):[e,t]},o.range=function(s){return arguments.length?(r=(i=Array.from(s)).length-1,u()):i.slice()},o.invertExtent=function(s){var c=i.indexOf(s);return c<0?[NaN,NaN]:c<1?[e,n[0]]:c>=r?[n[r-1],t]:[n[c-1],n[c]]},o.unknown=function(s){return arguments.length&&(a=s),o},o.thresholds=function(){return n.slice()},o.copy=function(){return T0().domain([e,t]).range(i).unknown(a)},He.apply($t(o),arguments)}function j0(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Yn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return j0().domain(e).range(t).unknown(r)},He.apply(i,arguments)}const ic=new Date,ac=new Date;function we(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const s=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return s;let c;do s.push(c=new Date(+a)),t(a,u),e(a);while(c<a&&a<o);return s},i.filter=a=>we(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(ic.setTime(+a),ac.setTime(+o),e(ic),e(ac),Math.floor(r(ic,ac))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Bi=we(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Bi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?we(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):Bi);Bi.range;const lt=1e3,Ue=lt*60,ft=Ue*60,gt=ft*24,Kf=gt*7,Dy=gt*30,oc=gt*365,Wt=we(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*lt)},(e,t)=>(t-e)/lt,e=>e.getUTCSeconds());Wt.range;const Vf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*lt)},(e,t)=>{e.setTime(+e+t*Ue)},(e,t)=>(t-e)/Ue,e=>e.getMinutes());Vf.range;const Xf=we(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Ue)},(e,t)=>(t-e)/Ue,e=>e.getUTCMinutes());Xf.range;const Yf=we(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*lt-e.getMinutes()*Ue)},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getHours());Yf.range;const Zf=we(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*ft)},(e,t)=>(t-e)/ft,e=>e.getUTCHours());Zf.range;const Qn=we(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Ue)/gt,e=>e.getDate()-1);Qn.range;const Ta=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/gt,e=>e.getUTCDate()-1);Ta.range;const E0=we(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/gt,e=>Math.floor(e/gt));E0.range;function er(e){return we(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*Ue)/Kf)}const ja=er(0),Li=er(1),BP=er(2),LP=er(3),Sr=er(4),FP=er(5),UP=er(6);ja.range;Li.range;BP.range;LP.range;Sr.range;FP.range;UP.range;function tr(e){return we(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/Kf)}const Ea=tr(0),Fi=tr(1),zP=tr(2),WP=tr(3),Ar=tr(4),HP=tr(5),GP=tr(6);Ea.range;Fi.range;zP.range;WP.range;Ar.range;HP.range;GP.range;const Jf=we(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Jf.range;const Qf=we(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Qf.range;const mt=we(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());mt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});mt.range;const bt=we(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());bt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:we(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});bt.range;function M0(e,t,r,n,i,a){const o=[[Wt,1,lt],[Wt,5,5*lt],[Wt,15,15*lt],[Wt,30,30*lt],[a,1,Ue],[a,5,5*Ue],[a,15,15*Ue],[a,30,30*Ue],[i,1,ft],[i,3,3*ft],[i,6,6*ft],[i,12,12*ft],[n,1,gt],[n,2,2*gt],[r,1,Kf],[t,1,Dy],[t,3,3*Dy],[e,1,oc]];function u(c,f,l){const h=f<c;h&&([c,f]=[f,c]);const p=l&&typeof l.range=="function"?l:s(c,f,l),y=p?p.range(c,+f+1):[];return h?y.reverse():y}function s(c,f,l){const h=Math.abs(f-c)/l,p=Rf(([,,d])=>d).right(o,h);if(p===o.length)return e.every(yl(c/oc,f/oc,l));if(p===0)return Bi.every(Math.max(yl(c,f,l),1));const[y,v]=o[h/o[p-1][2]<o[p][2]/h?p-1:p];return y.every(v)}return[u,s]}const[KP,VP]=M0(bt,Qf,Ea,E0,Zf,Xf),[XP,YP]=M0(mt,Jf,ja,Qn,Yf,Vf);function uc(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function sc(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Vr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function ZP(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,s=e.shortMonths,c=Xr(i),f=Yr(i),l=Xr(a),h=Yr(a),p=Xr(o),y=Yr(o),v=Xr(u),d=Yr(u),x=Xr(s),w=Yr(s),b={a:D,A:L,b:F,B:H,c:null,d:zy,e:zy,f:xT,g:MT,G:$T,H:gT,I:mT,j:bT,L:C0,m:wT,M:OT,p:K,q:z,Q:Gy,s:Ky,S:_T,u:ST,U:AT,V:PT,w:TT,W:jT,x:null,X:null,y:ET,Y:CT,Z:IT,"%":Hy},O={a:V,A:ce,b:pe,B:ke,c:null,d:Wy,e:Wy,f:DT,g:KT,G:XT,H:NT,I:RT,j:kT,L:I0,m:qT,M:BT,p:Rt,q:$e,Q:Gy,s:Ky,S:LT,u:FT,U:UT,V:zT,w:WT,W:HT,x:null,X:null,y:GT,Y:VT,Z:YT,"%":Hy},g={a:C,A,b:j,B:E,c:N,d:Fy,e:Fy,f:dT,g:Ly,G:By,H:Uy,I:Uy,j:cT,L:hT,m:sT,M:lT,p:P,q:uT,Q:vT,s:yT,S:fT,u:rT,U:nT,V:iT,w:tT,W:aT,x:I,X:R,y:Ly,Y:By,Z:oT,"%":pT};b.x=m(r,b),b.X=m(n,b),b.c=m(t,b),O.x=m(r,O),O.X=m(n,O),O.c=m(t,O);function m(U,X){return function(Y){var k=[],he=-1,J=0,ge=U.length,me,Ie,_t;for(Y instanceof Date||(Y=new Date(+Y));++he<ge;)U.charCodeAt(he)===37&&(k.push(U.slice(J,he)),(Ie=qy[me=U.charAt(++he)])!=null?me=U.charAt(++he):Ie=me==="e"?" ":"0",(_t=X[me])&&(me=_t(Y,Ie)),k.push(me),J=he+1);return k.push(U.slice(J,he)),k.join("")}}function _(U,X){return function(Y){var k=Vr(1900,void 0,1),he=S(k,U,Y+="",0),J,ge;if(he!=Y.length)return null;if("Q"in k)return new Date(k.Q);if("s"in k)return new Date(k.s*1e3+("L"in k?k.L:0));if(X&&!("Z"in k)&&(k.Z=0),"p"in k&&(k.H=k.H%12+k.p*12),k.m===void 0&&(k.m="q"in k?k.q:0),"V"in k){if(k.V<1||k.V>53)return null;"w"in k||(k.w=1),"Z"in k?(J=sc(Vr(k.y,0,1)),ge=J.getUTCDay(),J=ge>4||ge===0?Fi.ceil(J):Fi(J),J=Ta.offset(J,(k.V-1)*7),k.y=J.getUTCFullYear(),k.m=J.getUTCMonth(),k.d=J.getUTCDate()+(k.w+6)%7):(J=uc(Vr(k.y,0,1)),ge=J.getDay(),J=ge>4||ge===0?Li.ceil(J):Li(J),J=Qn.offset(J,(k.V-1)*7),k.y=J.getFullYear(),k.m=J.getMonth(),k.d=J.getDate()+(k.w+6)%7)}else("W"in k||"U"in k)&&("w"in k||(k.w="u"in k?k.u%7:"W"in k?1:0),ge="Z"in k?sc(Vr(k.y,0,1)).getUTCDay():uc(Vr(k.y,0,1)).getDay(),k.m=0,k.d="W"in k?(k.w+6)%7+k.W*7-(ge+5)%7:k.w+k.U*7-(ge+6)%7);return"Z"in k?(k.H+=k.Z/100|0,k.M+=k.Z%100,sc(k)):uc(k)}}function S(U,X,Y,k){for(var he=0,J=X.length,ge=Y.length,me,Ie;he<J;){if(k>=ge)return-1;if(me=X.charCodeAt(he++),me===37){if(me=X.charAt(he++),Ie=g[me in qy?X.charAt(he++):me],!Ie||(k=Ie(U,Y,k))<0)return-1}else if(me!=Y.charCodeAt(k++))return-1}return k}function P(U,X,Y){var k=c.exec(X.slice(Y));return k?(U.p=f.get(k[0].toLowerCase()),Y+k[0].length):-1}function C(U,X,Y){var k=p.exec(X.slice(Y));return k?(U.w=y.get(k[0].toLowerCase()),Y+k[0].length):-1}function A(U,X,Y){var k=l.exec(X.slice(Y));return k?(U.w=h.get(k[0].toLowerCase()),Y+k[0].length):-1}function j(U,X,Y){var k=x.exec(X.slice(Y));return k?(U.m=w.get(k[0].toLowerCase()),Y+k[0].length):-1}function E(U,X,Y){var k=v.exec(X.slice(Y));return k?(U.m=d.get(k[0].toLowerCase()),Y+k[0].length):-1}function N(U,X,Y){return S(U,t,X,Y)}function I(U,X,Y){return S(U,r,X,Y)}function R(U,X,Y){return S(U,n,X,Y)}function D(U){return o[U.getDay()]}function L(U){return a[U.getDay()]}function F(U){return s[U.getMonth()]}function H(U){return u[U.getMonth()]}function K(U){return i[+(U.getHours()>=12)]}function z(U){return 1+~~(U.getMonth()/3)}function V(U){return o[U.getUTCDay()]}function ce(U){return a[U.getUTCDay()]}function pe(U){return s[U.getUTCMonth()]}function ke(U){return u[U.getUTCMonth()]}function Rt(U){return i[+(U.getUTCHours()>=12)]}function $e(U){return 1+~~(U.getUTCMonth()/3)}return{format:function(U){var X=m(U+="",b);return X.toString=function(){return U},X},parse:function(U){var X=_(U+="",!1);return X.toString=function(){return U},X},utcFormat:function(U){var X=m(U+="",O);return X.toString=function(){return U},X},utcParse:function(U){var X=_(U+="",!0);return X.toString=function(){return U},X}}}var qy={"-":"",_:" ",0:"0"},Se=/^\s*\d+/,JP=/^%/,QP=/[\\^$*+?|[\]().{}]/g;function Q(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function eT(e){return e.replace(QP,"\\$&")}function Xr(e){return new RegExp("^(?:"+e.map(eT).join("|")+")","i")}function Yr(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function tT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function rT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function nT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function iT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function aT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function By(e,t,r){var n=Se.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function Ly(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function oT(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function uT(e,t,r){var n=Se.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function sT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function Fy(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function cT(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function Uy(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function lT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function fT(e,t,r){var n=Se.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function hT(e,t,r){var n=Se.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function dT(e,t,r){var n=Se.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function pT(e,t,r){var n=JP.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function vT(e,t,r){var n=Se.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function yT(e,t,r){var n=Se.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function zy(e,t){return Q(e.getDate(),t,2)}function gT(e,t){return Q(e.getHours(),t,2)}function mT(e,t){return Q(e.getHours()%12||12,t,2)}function bT(e,t){return Q(1+Qn.count(mt(e),e),t,3)}function C0(e,t){return Q(e.getMilliseconds(),t,3)}function xT(e,t){return C0(e,t)+"000"}function wT(e,t){return Q(e.getMonth()+1,t,2)}function OT(e,t){return Q(e.getMinutes(),t,2)}function _T(e,t){return Q(e.getSeconds(),t,2)}function ST(e){var t=e.getDay();return t===0?7:t}function AT(e,t){return Q(ja.count(mt(e)-1,e),t,2)}function $0(e){var t=e.getDay();return t>=4||t===0?Sr(e):Sr.ceil(e)}function PT(e,t){return e=$0(e),Q(Sr.count(mt(e),e)+(mt(e).getDay()===4),t,2)}function TT(e){return e.getDay()}function jT(e,t){return Q(Li.count(mt(e)-1,e),t,2)}function ET(e,t){return Q(e.getFullYear()%100,t,2)}function MT(e,t){return e=$0(e),Q(e.getFullYear()%100,t,2)}function CT(e,t){return Q(e.getFullYear()%1e4,t,4)}function $T(e,t){var r=e.getDay();return e=r>=4||r===0?Sr(e):Sr.ceil(e),Q(e.getFullYear()%1e4,t,4)}function IT(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+Q(t/60|0,"0",2)+Q(t%60,"0",2)}function Wy(e,t){return Q(e.getUTCDate(),t,2)}function NT(e,t){return Q(e.getUTCHours(),t,2)}function RT(e,t){return Q(e.getUTCHours()%12||12,t,2)}function kT(e,t){return Q(1+Ta.count(bt(e),e),t,3)}function I0(e,t){return Q(e.getUTCMilliseconds(),t,3)}function DT(e,t){return I0(e,t)+"000"}function qT(e,t){return Q(e.getUTCMonth()+1,t,2)}function BT(e,t){return Q(e.getUTCMinutes(),t,2)}function LT(e,t){return Q(e.getUTCSeconds(),t,2)}function FT(e){var t=e.getUTCDay();return t===0?7:t}function UT(e,t){return Q(Ea.count(bt(e)-1,e),t,2)}function N0(e){var t=e.getUTCDay();return t>=4||t===0?Ar(e):Ar.ceil(e)}function zT(e,t){return e=N0(e),Q(Ar.count(bt(e),e)+(bt(e).getUTCDay()===4),t,2)}function WT(e){return e.getUTCDay()}function HT(e,t){return Q(Fi.count(bt(e)-1,e),t,2)}function GT(e,t){return Q(e.getUTCFullYear()%100,t,2)}function KT(e,t){return e=N0(e),Q(e.getUTCFullYear()%100,t,2)}function VT(e,t){return Q(e.getUTCFullYear()%1e4,t,4)}function XT(e,t){var r=e.getUTCDay();return e=r>=4||r===0?Ar(e):Ar.ceil(e),Q(e.getUTCFullYear()%1e4,t,4)}function YT(){return"+0000"}function Hy(){return"%"}function Gy(e){return+e}function Ky(e){return Math.floor(+e/1e3)}var sr,R0,k0;ZT({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function ZT(e){return sr=ZP(e),R0=sr.format,sr.parse,k0=sr.utcFormat,sr.utcParse,sr}function JT(e){return new Date(e)}function QT(e){return e instanceof Date?+e:+new Date(+e)}function eh(e,t,r,n,i,a,o,u,s,c){var f=Lf(),l=f.invert,h=f.domain,p=c(".%L"),y=c(":%S"),v=c("%I:%M"),d=c("%I %p"),x=c("%a %d"),w=c("%b %d"),b=c("%B"),O=c("%Y");function g(m){return(s(m)<m?p:u(m)<m?y:o(m)<m?v:a(m)<m?d:n(m)<m?i(m)<m?x:w:r(m)<m?b:O)(m)}return f.invert=function(m){return new Date(l(m))},f.domain=function(m){return arguments.length?h(Array.from(m,QT)):h().map(JT)},f.ticks=function(m){var _=h();return e(_[0],_[_.length-1],m??10)},f.tickFormat=function(m,_){return _==null?g:c(_)},f.nice=function(m){var _=h();return(!m||typeof m.range!="function")&&(m=t(_[0],_[_.length-1],m??10)),m?h(O0(_,m)):f},f.copy=function(){return Jn(f,eh(e,t,r,n,i,a,o,u,s,c))},f}function ej(){return He.apply(eh(XP,YP,mt,Jf,ja,Qn,Yf,Vf,Wt,R0).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function tj(){return He.apply(eh(KP,VP,bt,Qf,Ea,Ta,Zf,Xf,Wt,k0).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Ma(){var e=0,t=1,r,n,i,a,o=Ce,u=!1,s;function c(l){return l==null||isNaN(l=+l)?s:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}c.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),c):[e,t]},c.clamp=function(l){return arguments.length?(u=!!l,c):u},c.interpolator=function(l){return arguments.length?(o=l,c):o};function f(l){return function(h){var p,y;return arguments.length?([p,y]=h,o=l(p,y),c):[o(0),o(1)]}}return c.range=f(Ur),c.rangeRound=f(Bf),c.unknown=function(l){return arguments.length?(s=l,c):s},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),c}}function It(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function D0(){var e=$t(Ma()(Ce));return e.copy=function(){return It(e,D0())},Ot.apply(e,arguments)}function q0(){var e=zf(Ma()).domain([1,10]);return e.copy=function(){return It(e,q0()).base(e.base())},Ot.apply(e,arguments)}function B0(){var e=Wf(Ma());return e.copy=function(){return It(e,B0()).constant(e.constant())},Ot.apply(e,arguments)}function th(){var e=Hf(Ma());return e.copy=function(){return It(e,th()).exponent(e.exponent())},Ot.apply(e,arguments)}function rj(){return th.apply(null,arguments).exponent(.5)}function L0(){var e=[],t=Ce;function r(n){if(n!=null&&!isNaN(n=+n))return t((Yn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(jt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>UA(e,a/n))},r.copy=function(){return L0(t).domain(e)},Ot.apply(r,arguments)}function Ca(){var e=0,t=.5,r=1,n=1,i,a,o,u,s,c=Ce,f,l=!1,h;function p(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:s),c(l?Math.max(0,Math.min(1,v)):v))}p.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p):[e,t,r]},p.clamp=function(v){return arguments.length?(l=!!v,p):l},p.interpolator=function(v){return arguments.length?(c=v,p):c};function y(v){return function(d){var x,w,b;return arguments.length?([x,w,b]=d,c=pP(v,[x,w,b]),p):[c(0),c(.5),c(1)]}}return p.range=y(Ur),p.rangeRound=y(Bf),p.unknown=function(v){return arguments.length?(h=v,p):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),s=a===o?0:.5/(o-a),n=a<i?-1:1,p}}function F0(){var e=$t(Ca()(Ce));return e.copy=function(){return It(e,F0())},Ot.apply(e,arguments)}function U0(){var e=zf(Ca()).domain([.1,1,10]);return e.copy=function(){return It(e,U0()).base(e.base())},Ot.apply(e,arguments)}function z0(){var e=Wf(Ca());return e.copy=function(){return It(e,z0()).constant(e.constant())},Ot.apply(e,arguments)}function rh(){var e=Hf(Ca());return e.copy=function(){return It(e,rh()).exponent(e.exponent())},Ot.apply(e,arguments)}function nj(){return rh.apply(null,arguments).exponent(.5)}const Vy=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:mn,scaleDiverging:F0,scaleDivergingLog:U0,scaleDivergingPow:rh,scaleDivergingSqrt:nj,scaleDivergingSymlog:z0,scaleIdentity:w0,scaleImplicit:gl,scaleLinear:qi,scaleLog:_0,scaleOrdinal:kf,scalePoint:sn,scalePow:Gf,scaleQuantile:P0,scaleQuantize:T0,scaleRadial:A0,scaleSequential:D0,scaleSequentialLog:q0,scaleSequentialPow:th,scaleSequentialQuantile:L0,scaleSequentialSqrt:rj,scaleSequentialSymlog:B0,scaleSqrt:DP,scaleSymlog:S0,scaleThreshold:j0,scaleTime:ej,scaleUtc:tj,tickFormat:x0},Symbol.toStringTag,{value:"Module"}));var cc,Xy;function W0(){if(Xy)return cc;Xy=1;var e=Br();function t(r,n,i){for(var a=-1,o=r.length;++a<o;){var u=r[a],s=n(u);if(s!=null&&(c===void 0?s===s&&!e(s):i(s,c)))var c=s,f=u}return f}return cc=t,cc}var lc,Yy;function ij(){if(Yy)return lc;Yy=1;function e(t,r){return t>r}return lc=e,lc}var fc,Zy;function aj(){if(Zy)return fc;Zy=1;var e=W0(),t=ij(),r=Fr();function n(i){return i&&i.length?e(i,r,t):void 0}return fc=n,fc}var oj=aj();const $a=se(oj);var hc,Jy;function uj(){if(Jy)return hc;Jy=1;function e(t,r){return t<r}return hc=e,hc}var dc,Qy;function sj(){if(Qy)return dc;Qy=1;var e=W0(),t=uj(),r=Fr();function n(i){return i&&i.length?e(i,r,t):void 0}return dc=n,dc}var cj=sj();const Ia=se(cj);var pc,eg;function lj(){if(eg)return pc;eg=1;var e=mf(),t=Ct(),r=Jb(),n=Re();function i(a,o){var u=n(a)?e:r;return u(a,t(o,3))}return pc=i,pc}var vc,tg;function fj(){if(tg)return vc;tg=1;var e=Yb(),t=lj();function r(n,i){return e(t(n,i),1)}return vc=r,vc}var hj=fj();const dj=se(hj);var yc,rg;function pj(){if(rg)return yc;rg=1;var e=Cf();function t(r,n){return e(r,n)}return yc=t,yc}var vj=pj();const nh=se(vj);var zr=1e9,yj={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},ah,fe=!0,We="[DecimalError] ",Kt=We+"Invalid argument: ",ih=We+"Exponent out of range: ",Wr=Math.floor,Ft=Math.pow,gj=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Be,Oe=1e7,le=7,H0=9007199254740991,Ui=Wr(H0/le),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*le;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return vt(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return oe(vt(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return ye(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(Be))throw Error(We+"NaN");if(r.s<1)throw Error(We+(r.s?"NaN":"-Infinity"));return r.eq(Be)?new n(0):(fe=!1,t=vt(_n(r,a),_n(e,a),a),fe=!0,oe(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?V0(t,e):G0(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(We+"NaN");return r.s?(fe=!1,t=vt(r,e,0,1).times(e),fe=!0,r.minus(t)):oe(new n(r),i)};W.naturalExponential=W.exp=function(){return K0(this)};W.naturalLogarithm=W.ln=function(){return _n(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?G0(t,e):V0(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Kt+e);if(t=ye(i)+1,n=i.d.length-1,r=n*le+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,s=u.constructor;if(u.s<1){if(!u.s)return new s(0);throw Error(We+"NaN")}for(e=ye(u),fe=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=nt(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Wr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new s(t)):n=new s(i.toString()),r=s.precision,i=o=r+3;;)if(a=n,n=a.plus(vt(u,a,o+2)).times(.5),nt(a.d).slice(0,o)===(t=nt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(oe(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return fe=!0,oe(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,s,c,f=this,l=f.constructor,h=f.d,p=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,s=h.length,c=p.length,s<c&&(a=h,h=p,p=a,o=s,s=c,c=o),a=[],o=s+c,n=o;n--;)a.push(0);for(n=c;--n>=0;){for(t=0,i=s+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%Oe|0,t=u/Oe|0;a[i]=(a[i]+t)%Oe|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,fe?oe(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(ot(e,0,zr),t===void 0?t=n.rounding:ot(t,0,8),oe(r,e+ye(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Zt(n,!0):(ot(e,0,zr),t===void 0?t=i.rounding:ot(t,0,8),n=oe(new i(n),e+1,t),r=Zt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Zt(i):(ot(e,0,zr),t===void 0?t=a.rounding:ot(t,0,8),n=oe(new a(i),e+ye(i)+1,t),r=Zt(n.abs(),!1,e+ye(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return oe(new t(e),ye(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,s=u.constructor,c=12,f=+(e=new s(e));if(!e.s)return new s(Be);if(u=new s(u),!u.s){if(e.s<1)throw Error(We+"Infinity");return u}if(u.eq(Be))return u;if(n=s.precision,e.eq(Be))return oe(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=H0){for(i=new s(Be),t=Math.ceil(n/le+4),fe=!1;r%2&&(i=i.times(u),ig(i.d,t)),r=Wr(r/2),r!==0;)u=u.times(u),ig(u.d,t);return fe=!0,e.s<0?new s(Be).div(i):oe(i,n)}}else if(a<0)throw Error(We+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,fe=!1,i=e.times(_n(u,n+c)),fe=!0,i=K0(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=ye(i),n=Zt(i,r<=a.toExpNeg||r>=a.toExpPos)):(ot(e,1,zr),t===void 0?t=a.rounding:ot(t,0,8),i=oe(new a(i),e,t),r=ye(i),n=Zt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(ot(e,1,zr),t===void 0?t=n.rounding:ot(t,0,8)),oe(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=ye(e),r=e.constructor;return Zt(e,t<=r.toExpNeg||t>=r.toExpPos)};function G0(e,t){var r,n,i,a,o,u,s,c,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),fe?oe(t,l):t;if(s=e.d,c=t.d,o=e.e,i=t.e,s=s.slice(),a=o-i,a){for(a<0?(n=s,a=-a,u=c.length):(n=c,i=o,u=s.length),o=Math.ceil(l/le),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=s.length,a=c.length,u-a<0&&(a=u,n=c,c=s,s=n),r=0;a;)r=(s[--a]=s[a]+c[a]+r)/Oe|0,s[a]%=Oe;for(r&&(s.unshift(r),++i),u=s.length;s[--u]==0;)s.pop();return t.d=s,t.e=i,fe?oe(t,l):t}function ot(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Kt+e)}function nt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=le-n.length,r&&(a+=St(r)),a+=n;o=e[t],n=o+"",r=le-n.length,r&&(a+=St(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var vt=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%Oe|0,o=a/Oe|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,s;if(a!=o)s=a>o?1:-1;else for(u=s=0;u<a;u++)if(n[u]!=i[u]){s=n[u]>i[u]?1:-1;break}return s}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*Oe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,s,c,f,l,h,p,y,v,d,x,w,b,O,g,m,_,S,P=n.constructor,C=n.s==i.s?1:-1,A=n.d,j=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(We+"Division by zero");for(s=n.e-i.e,_=j.length,g=A.length,p=new P(C),y=p.d=[],c=0;j[c]==(A[c]||0);)++c;if(j[c]>(A[c]||0)&&--s,a==null?w=a=P.precision:o?w=a+(ye(n)-ye(i))+1:w=a,w<0)return new P(0);if(w=w/le+2|0,c=0,_==1)for(f=0,j=j[0],w++;(c<g||f)&&w--;c++)b=f*Oe+(A[c]||0),y[c]=b/j|0,f=b%j|0;else{for(f=Oe/(j[0]+1)|0,f>1&&(j=e(j,f),A=e(A,f),_=j.length,g=A.length),O=_,v=A.slice(0,_),d=v.length;d<_;)v[d++]=0;S=j.slice(),S.unshift(0),m=j[0],j[1]>=Oe/2&&++m;do f=0,u=t(j,v,_,d),u<0?(x=v[0],_!=d&&(x=x*Oe+(v[1]||0)),f=x/m|0,f>1?(f>=Oe&&(f=Oe-1),l=e(j,f),h=l.length,d=v.length,u=t(l,v,h,d),u==1&&(f--,r(l,_<h?S:j,h))):(f==0&&(u=f=1),l=j.slice()),h=l.length,h<d&&l.unshift(0),r(v,l,d),u==-1&&(d=v.length,u=t(j,v,_,d),u<1&&(f++,r(v,_<d?S:j,d))),d=v.length):u===0&&(f++,v=[0]),y[c++]=f,u&&v[0]?v[d++]=A[O]||0:(v=[A[O]],d=1);while((O++<g||v[0]!==void 0)&&w--)}return y[0]||y.shift(),p.e=s,oe(p,o?a+ye(p)+1:a)}}();function K0(e,t){var r,n,i,a,o,u,s=0,c=0,f=e.constructor,l=f.precision;if(ye(e)>16)throw Error(ih+ye(e));if(!e.s)return new f(Be);for(fe=!1,u=l,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),c+=5;for(n=Math.log(Ft(2,c))/Math.LN10*2+5|0,u+=n,r=i=a=new f(Be),f.precision=u;;){if(i=oe(i.times(e),u),r=r.times(++s),o=a.plus(vt(i,r,u)),nt(o.d).slice(0,u)===nt(a.d).slice(0,u)){for(;c--;)a=oe(a.times(a),u);return f.precision=l,t==null?(fe=!0,oe(a,l)):a}a=o}}function ye(e){for(var t=e.e*le,r=e.d[0];r>=10;r/=10)t++;return t}function gc(e,t,r){if(t>e.LN10.sd())throw fe=!0,r&&(e.precision=r),Error(We+"LN10 precision limit exceeded");return oe(new e(e.LN10),t)}function St(e){for(var t="";e--;)t+="0";return t}function _n(e,t){var r,n,i,a,o,u,s,c,f,l=1,h=10,p=e,y=p.d,v=p.constructor,d=v.precision;if(p.s<1)throw Error(We+(p.s?"NaN":"-Infinity"));if(p.eq(Be))return new v(0);if(t==null?(fe=!1,c=d):c=t,p.eq(10))return t==null&&(fe=!0),gc(v,c);if(c+=h,v.precision=c,r=nt(y),n=r.charAt(0),a=ye(p),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)p=p.times(e),r=nt(p.d),n=r.charAt(0),l++;a=ye(p),n>1?(p=new v("0."+r),a++):p=new v(n+"."+r.slice(1))}else return s=gc(v,c+2,d).times(a+""),p=_n(new v(n+"."+r.slice(1)),c-h).plus(s),v.precision=d,t==null?(fe=!0,oe(p,d)):p;for(u=o=p=vt(p.minus(Be),p.plus(Be),c),f=oe(p.times(p),c),i=3;;){if(o=oe(o.times(f),c),s=u.plus(vt(o,new v(i),c)),nt(s.d).slice(0,c)===nt(u.d).slice(0,c))return u=u.times(2),a!==0&&(u=u.plus(gc(v,c+2,d).times(a+""))),u=vt(u,new v(l),c),v.precision=d,t==null?(fe=!0,oe(u,d)):u;u=s,i+=2}}function ng(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Wr(r/le),e.d=[],n=(r+1)%le,r<0&&(n+=le),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=le;n<i;)e.d.push(+t.slice(n,n+=le));t=t.slice(n),n=le-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),fe&&(e.e>Ui||e.e<-Ui))throw Error(ih+r)}else e.s=0,e.e=0,e.d=[0];return e}function oe(e,t,r){var n,i,a,o,u,s,c,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=le,i=t,c=l[f=0];else{if(f=Math.ceil((n+1)/le),a=l.length,f>=a)return e;for(c=a=l[f],o=1;a>=10;a/=10)o++;n%=le,i=n-le+o}if(r!==void 0&&(a=Ft(10,o-i-1),u=c/a%10|0,s=t<0||l[f+1]!==void 0||c%a,s=r<4?(u||s)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||s||r==6&&(n>0?i>0?c/Ft(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return s?(a=ye(e),l.length=1,t=t-a-1,l[0]=Ft(10,(le-t%le)%le),e.e=Wr(-t/le)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Ft(10,le-n),l[f]=i>0?(c/Ft(10,o-i)%Ft(10,i)|0)*a:0),s)for(;;)if(f==0){(l[0]+=a)==Oe&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=Oe)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(fe&&(e.e>Ui||e.e<-Ui))throw Error(ih+ye(e));return e}function V0(e,t){var r,n,i,a,o,u,s,c,f,l,h=e.constructor,p=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),fe?oe(t,p):t;if(s=e.d,l=t.d,n=t.e,c=e.e,s=s.slice(),o=c-n,o){for(f=o<0,f?(r=s,o=-o,u=l.length):(r=l,n=c,u=s.length),i=Math.max(Math.ceil(p/le),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=s.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(s[i]!=l[i]){f=s[i]<l[i];break}o=0}for(f&&(r=s,s=l,l=r,t.s=-t.s),u=s.length,i=l.length-u;i>0;--i)s[u++]=0;for(i=l.length;i>o;){if(s[--i]<l[i]){for(a=i;a&&s[--a]===0;)s[a]=Oe-1;--s[a],s[i]+=Oe}s[i]-=l[i]}for(;s[--u]===0;)s.pop();for(;s[0]===0;s.shift())--n;return s[0]?(t.d=s,t.e=n,fe?oe(t,p):t):new h(0)}function Zt(e,t,r){var n,i=ye(e),a=nt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+St(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+St(-i-1)+a,r&&(n=r-o)>0&&(a+=St(n))):i>=o?(a+=St(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+St(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=St(n))),e.s<0?"-"+a:a}function ig(e,t){if(e.length>t)return e.length=t,!0}function X0(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Kt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return ng(o,a.toString())}else if(typeof a!="string")throw Error(Kt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,gj.test(a))ng(o,a);else throw Error(Kt+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=X0,i.config=i.set=mj,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function mj(e){if(!e||typeof e!="object")throw Error(We+"Object expected");var t,r,n,i=["precision",1,zr,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Wr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Kt+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Kt+r+": "+n);return this}var ah=X0(yj);Be=new ah(1);const ae=ah;function bj(e){return _j(e)||Oj(e)||wj(e)||xj()}function xj(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wj(e,t){if(e){if(typeof e=="string")return wl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return wl(e,t)}}function Oj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function _j(e){if(Array.isArray(e))return wl(e)}function wl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Sj=function(t){return t},Y0={},Z0=function(t){return t===Y0},ag=function(t){return function r(){return arguments.length===0||arguments.length===1&&Z0(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},Aj=function e(t,r){return t===1?r:ag(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Y0}).length;return o>=t?r.apply(void 0,i):e(t-o,ag(function(){for(var u=arguments.length,s=new Array(u),c=0;c<u;c++)s[c]=arguments[c];var f=i.map(function(l){return Z0(l)?s.shift():l});return r.apply(void 0,bj(f).concat(s))}))})},Na=function(t){return Aj(t.length,t)},Ol=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},Pj=Na(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),Tj=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return Sj;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,s){return s(u)},a.apply(void 0,arguments))}},_l=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},J0=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,s){return u===r[s]})||(r=a,n=t.apply(void 0,a)),n}};function jj(e){var t;return e===0?t=1:t=Math.floor(new ae(e).abs().log(10).toNumber())+1,t}function Ej(e,t,r){for(var n=new ae(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var Mj=Na(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),Cj=Na(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),$j=Na(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Ra={rangeStep:Ej,getDigitCount:jj,interpolateNumber:Mj,uninterpolateNumber:Cj,uninterpolateTruncation:$j};function Sl(e){return Rj(e)||Nj(e)||Q0(e)||Ij()}function Ij(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Nj(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function Rj(e){if(Array.isArray(e))return Al(e)}function Sn(e,t){return qj(e)||Dj(e,t)||Q0(e,t)||kj()}function kj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Q0(e,t){if(e){if(typeof e=="string")return Al(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Al(e,t)}}function Al(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Dj(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(s){i=!0,a=s}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function qj(e){if(Array.isArray(e))return e}function ex(e){var t=Sn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function tx(e,t,r){if(e.lte(0))return new ae(0);var n=Ra.getDigitCount(e.toNumber()),i=new ae(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ae(Math.ceil(a.div(o).toNumber())).add(r).mul(o),s=u.mul(i);return t?s:new ae(Math.ceil(s))}function Bj(e,t,r){var n=1,i=new ae(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ae(10).pow(Ra.getDigitCount(e)-1),i=new ae(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ae(Math.floor(e)))}else e===0?i=new ae(Math.floor((t-1)/2)):r||(i=new ae(Math.floor(e)));var o=Math.floor((t-1)/2),u=Tj(Pj(function(s){return i.add(new ae(s-o).mul(n)).toNumber()}),Ol);return u(0,t)}function rx(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ae(0),tickMin:new ae(0),tickMax:new ae(0)};var a=tx(new ae(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ae(0):(o=new ae(e).add(t).div(2),o=o.sub(new ae(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),s=Math.ceil(new ae(t).sub(o).div(a).toNumber()),c=u+s+1;return c>r?rx(e,t,r,n,i+1):(c<r&&(s=t>0?s+(r-c):s,u=t>0?u:u+(r-c)),{step:a,tickMin:o.sub(new ae(u).mul(a)),tickMax:o.add(new ae(s).mul(a))})}function Lj(e){var t=Sn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=ex([r,n]),s=Sn(u,2),c=s[0],f=s[1];if(c===-1/0||f===1/0){var l=f===1/0?[c].concat(Sl(Ol(0,i-1).map(function(){return 1/0}))):[].concat(Sl(Ol(0,i-1).map(function(){return-1/0})),[f]);return r>n?_l(l):l}if(c===f)return Bj(c,i,a);var h=rx(c,f,o,a),p=h.step,y=h.tickMin,v=h.tickMax,d=Ra.rangeStep(y,v.add(new ae(.1).mul(p)),p);return r>n?_l(d):d}function Fj(e,t){var r=Sn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=ex([n,i]),u=Sn(o,2),s=u[0],c=u[1];if(s===-1/0||c===1/0)return[n,i];if(s===c)return[s];var f=Math.max(t,2),l=tx(new ae(c).sub(s).div(f-1),a,0),h=[].concat(Sl(Ra.rangeStep(new ae(s),new ae(c).sub(new ae(.99).mul(l)),l)),[c]);return n>i?_l(h):h}var Uj=J0(Lj),zj=J0(Fj),Wj=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Pr(e){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pr(e)}function zi(){return zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zi.apply(this,arguments)}function Hj(e,t){return Xj(e)||Vj(e,t)||Kj(e,t)||Gj()}function Gj(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Kj(e,t){if(e){if(typeof e=="string")return og(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return og(e,t)}}function og(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Vj(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function Xj(e){if(Array.isArray(e))return e}function Yj(e,t){if(e==null)return{};var r=Zj(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Zj(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function Jj(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qj(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ax(n.key),n)}}function eE(e,t,r){return t&&Qj(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function tE(e,t,r){return t=Wi(t),rE(e,nx()?Reflect.construct(t,r||[],Wi(e).constructor):t.apply(e,r))}function rE(e,t){if(t&&(Pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return nE(e)}function nE(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function nx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(nx=function(){return!!e})()}function Wi(e){return Wi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wi(e)}function iE(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pl(e,t)}function Pl(e,t){return Pl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Pl(e,t)}function ix(e,t,r){return t=ax(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ax(e){var t=aE(e,"string");return Pr(t)=="symbol"?t:t+""}function aE(e,t){if(Pr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ka=function(e){function t(){return Jj(this,t),tE(this,t,arguments)}return iE(t,e),eE(t,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,s=n.data,c=n.dataPointFormatter,f=n.xAxis,l=n.yAxis,h=Yj(n,Wj),p=ee(h,!1);this.props.direction==="x"&&f.type!=="number"&&Vt(!1);var y=s.map(function(v){var d=c(v,u),x=d.x,w=d.y,b=d.value,O=d.errorVal;if(!O)return null;var g=[],m,_;if(Array.isArray(O)){var S=Hj(O,2);m=S[0],_=S[1]}else m=_=O;if(a==="vertical"){var P=f.scale,C=w+i,A=C+o,j=C-o,E=P(b-m),N=P(b+_);g.push({x1:N,y1:A,x2:N,y2:j}),g.push({x1:E,y1:C,x2:N,y2:C}),g.push({x1:E,y1:A,x2:E,y2:j})}else if(a==="horizontal"){var I=l.scale,R=x+i,D=R-o,L=R+o,F=I(b-m),H=I(b+_);g.push({x1:D,y1:H,x2:L,y2:H}),g.push({x1:R,y1:F,x2:R,y2:H}),g.push({x1:D,y1:F,x2:L,y2:F})}return T.createElement(_e,zi({className:"recharts-errorBar",key:"bar-".concat(g.map(function(K){return"".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))},p),g.map(function(K){return T.createElement("line",zi({},K,{key:"line-".concat(K.x1,"-").concat(K.x2,"-").concat(K.y1,"-").concat(K.y2)}))}))});return T.createElement(_e,{className:"recharts-errorBars"},y)}}])}(T.Component);ix(ka,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});ix(ka,"displayName","ErrorBar");function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(e)}function ug(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ug(Object(r),!0).forEach(function(n){oE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ug(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oE(e,t,r){return t=uE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uE(e){var t=sE(e,"string");return An(t)=="symbol"?t:t+""}function sE(e,t){if(An(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(An(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ox=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=qe(r,vr);if(!o)return null;var u=vr.defaultProps,s=u!==void 0?qt(qt({},u),o.props):{},c;return o.props&&o.props.payload?c=o.props&&o.props.payload:a==="children"?c=(n||[]).reduce(function(f,l){var h=l.item,p=l.props,y=p.sectors||p.data||[];return f.concat(y.map(function(v){return{type:o.props.iconType||h.props.legendType,value:v.name,color:v.fill,payload:v}}))},[]):c=(n||[]).map(function(f){var l=f.item,h=l.type.defaultProps,p=h!==void 0?qt(qt({},h),l.props):{},y=p.dataKey,v=p.name,d=p.legendType,x=p.hide;return{inactive:x,dataKey:y,type:s.iconType||d||"square",color:oh(l),value:v||y,payload:p}}),qt(qt(qt({},s),vr.getWithHeight(o,i)),{},{payload:c,item:o})};function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}function sg(e){return hE(e)||fE(e)||lE(e)||cE()}function cE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lE(e,t){if(e){if(typeof e=="string")return Tl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tl(e,t)}}function fE(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function hE(e){if(Array.isArray(e))return Tl(e)}function Tl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function cg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function de(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?cg(Object(r),!0).forEach(function(n){gr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gr(e,t,r){return t=dE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dE(e){var t=pE(e,"string");return Pn(t)=="symbol"?t:t+""}function pE(e,t){if(Pn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Pn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ut(e,t,r){return ne(e)||ne(t)?r:xe(t)?ze(e,t,r):Z(t)?t(e):r}function cn(e,t,r,n){var i=dj(e,function(u){return ut(u,t)});if(r==="number"){var a=i.filter(function(u){return B(u)||parseFloat(u)});return a.length?[Ia(a),$a(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!ne(u)}):i;return o.map(function(u){return xe(u)||u instanceof Date?u:""})}var vE=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n?.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var s=a.range,c=0;c<u;c++){var f=c>0?i[c-1].coordinate:i[u-1].coordinate,l=i[c].coordinate,h=c>=u-1?i[0].coordinate:i[c+1].coordinate,p=void 0;if(Ye(l-f)!==Ye(h-l)){var y=[];if(Ye(h-l)===Ye(s[1]-s[0])){p=h;var v=l+s[1]-s[0];y[0]=Math.min(v,(v+f)/2),y[1]=Math.max(v,(v+f)/2)}else{p=f;var d=h+s[1]-s[0];y[0]=Math.min(l,(d+l)/2),y[1]=Math.max(l,(d+l)/2)}var x=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>x[0]&&t<=x[1]||t>=y[0]&&t<=y[1]){o=i[c].index;break}}else{var w=Math.min(f,h),b=Math.max(f,h);if(t>(w+l)/2&&t<=(b+l)/2){o=i[c].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},oh=function(t){var r,n=t,i=n.type.displayName,a=(r=t.type)!==null&&r!==void 0&&r.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,o=a.stroke,u=a.fill,s;switch(i){case"Line":s=o;break;case"Area":case"Radar":s=o&&o!=="none"?o:u;break;default:s=u;break}return s},yE=function(t){var r=t.barSize,n=t.totalSize,i=t.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),s=0,c=u.length;s<c;s++)for(var f=a[u[s]].stackGroups,l=Object.keys(f),h=0,p=l.length;h<p;h++){var y=f[l[h]],v=y.items,d=y.cateAxisId,x=v.filter(function(_){return dt(_.type).indexOf("Bar")>=0});if(x&&x.length){var w=x[0].type.defaultProps,b=w!==void 0?de(de({},w),x[0].props):x[0].props,O=b.barSize,g=b[d];o[g]||(o[g]=[]);var m=ne(O)?r:O;o[g].push({item:x[0],stackList:x.slice(1),barSize:ne(m)?void 0:Yt(m,n,0)})}}return o},gE=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,s=o.length;if(s<1)return null;var c=Yt(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,p=i/s,y=o.reduce(function(O,g){return O+g.barSize||0},0);y+=(s-1)*c,y>=i&&(y-=(s-1)*c,c=0),y>=i&&p>0&&(h=!0,p*=.9,y=s*p);var v=(i-y)/2>>0,d={offset:v-c,size:0};f=o.reduce(function(O,g){var m={item:g.item,position:{offset:d.offset+d.size+c,size:h?p:g.barSize}},_=[].concat(sg(O),[m]);return d=_[_.length-1].position,g.stackList&&g.stackList.length&&g.stackList.forEach(function(S){_.push({item:S,position:d})}),_},l)}else{var x=Yt(n,i,0,!0);i-2*x-(s-1)*c<=0&&(c=0);var w=(i-2*x-(s-1)*c)/s;w>1&&(w>>=0);var b=u===+u?Math.min(w,u):w;f=o.reduce(function(O,g,m){var _=[].concat(sg(O),[{item:g.item,position:{offset:x+(w+c)*m+(w-b)/2,size:b}}]);return g.stackList&&g.stackList.length&&g.stackList.forEach(function(S){_.push({item:S,position:_[_.length-1].position})}),_},l)}return f},mE=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,s=o-(u.left||0)-(u.right||0),c=ox({children:a,legendWidth:s});if(c){var f=i||{},l=f.width,h=f.height,p=c.align,y=c.verticalAlign,v=c.layout;if((v==="vertical"||v==="horizontal"&&y==="middle")&&p!=="center"&&B(t[p]))return de(de({},t),{},gr({},p,t[p]+(l||0)));if((v==="horizontal"||v==="vertical"&&p==="center")&&y!=="middle"&&B(t[y]))return de(de({},t),{},gr({},y,t[y]+(h||0)))}return t},bE=function(t,r,n){return ne(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},ux=function(t,r,n,i,a){var o=r.props.children,u=Ze(o,ka).filter(function(c){return bE(i,a,c.props.direction)});if(u&&u.length){var s=u.map(function(c){return c.props.dataKey});return t.reduce(function(c,f){var l=ut(f,n);if(ne(l))return c;var h=Array.isArray(l)?[Ia(l),$a(l)]:[l,l],p=s.reduce(function(y,v){var d=ut(f,v,0),x=h[0]-Math.abs(Array.isArray(d)?d[0]:d),w=h[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(x,y[0]),Math.max(w,y[1])]},[1/0,-1/0]);return[Math.min(p[0],c[0]),Math.max(p[1],c[1])]},[1/0,-1/0])}return null},xE=function(t,r,n,i,a){var o=r.map(function(u){return ux(t,u,n,a,i)}).filter(function(u){return!ne(u)});return o&&o.length?o.reduce(function(u,s){return[Math.min(u[0],s[0]),Math.max(u[1],s[1])]},[1/0,-1/0]):null},sx=function(t,r,n,i,a){var o=r.map(function(s){var c=s.props.dataKey;return n==="number"&&c&&ux(t,s,c,i)||cn(t,c,n,a)});if(n==="number")return o.reduce(function(s,c){return[Math.min(s[0],c[0]),Math.max(s[1],c[1])]},[1/0,-1/0]);var u={};return o.reduce(function(s,c){for(var f=0,l=c.length;f<l;f++)u[c[f]]||(u[c[f]]=!0,s.push(c[f]));return s},[])},cx=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},lx=function(t,r,n,i){if(i)return t.map(function(s){return s.coordinate});var a,o,u=t.map(function(s){return s.coordinate===r&&(a=!0),s.coordinate===n&&(o=!0),s.coordinate});return a||u.push(r),o||u.push(n),u},ht=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,s=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,c=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/s:0;if(c=t.axisType==="angleAxis"&&u?.length>=2?Ye(u[0]-u[1])*2*c:c,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+c,value:l,offset:c}});return f.filter(function(l){return!Kn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+c,value:l,index:h,offset:c}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+c,value:l,offset:c}}):i.domain().map(function(l,h){return{coordinate:i(l)+c,value:a?a[l]:l,index:h,offset:c}})},mc=new WeakMap,di=function(t,r){if(typeof r!="function")return t;mc.has(t)||mc.set(t,new WeakMap);var n=mc.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},wE=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:mn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:qi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:sn(),realScaleType:"point"}:a==="category"?{scale:mn(),realScaleType:"band"}:{scale:qi(),realScaleType:"linear"};if(Xt(i)){var s="scale".concat(ba(i));return{scale:(Vy[s]||sn)(),realScaleType:Vy[s]?s:"point"}}return Z(i)?{scale:i}:{scale:sn(),realScaleType:"point"}},lg=1e-4,OE=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-lg,o=Math.max(i[0],i[1])+lg,u=t(r[0]),s=t(r[n-1]);(u<a||u>o||s<a||s>o)&&t.domain([r[0],r[n-1]])}},_E=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},SE=function(t,r){if(!r||r.length!==2||!B(r[0])||!B(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!B(t[0])||t[0]<n)&&(a[0]=n),(!B(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},AE=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var s=Kn(t[u][n][1])?t[u][n][0]:t[u][n][1];s>=0?(t[u][n][0]=a,t[u][n][1]=a+s,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+s,o=t[u][n][1])}},PE=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Kn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},TE={sign:AE,expand:lO,none:mr,silhouette:fO,wiggle:hO,positive:PE},jE=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=TE[n],o=cO().keys(i).value(function(u,s){return+ut(u,s,0)}).order(nl).offset(a);return o(t)},EE=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,s={},c=u.reduce(function(l,h){var p,y=(p=h.type)!==null&&p!==void 0&&p.defaultProps?de(de({},h.type.defaultProps),h.props):h.props,v=y.stackId,d=y.hide;if(d)return l;var x=y[n],w=l[x]||{hasStack:!1,stackGroups:{}};if(xe(v)){var b=w.stackGroups[v]||{numericAxisId:n,cateAxisId:i,items:[]};b.items.push(h),w.hasStack=!0,w.stackGroups[v]=b}else w.stackGroups[ma("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return de(de({},l),{},gr({},x,w))},s),f={};return Object.keys(c).reduce(function(l,h){var p=c[h];if(p.hasStack){var y={};p.stackGroups=Object.keys(p.stackGroups).reduce(function(v,d){var x=p.stackGroups[d];return de(de({},v),{},gr({},d,{numericAxisId:n,cateAxisId:i,items:x.items,stackedData:jE(t,x.items,a)}))},y)}return de(de({},l),{},gr({},h,p))},f)},ME=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,s=n||r.scale;if(s!=="auto"&&s!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var c=t.domain();if(!c.length)return null;var f=Uj(c,a,u);return t.domain([Ia(f),$a(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=zj(l,a,u);return{niceTicks:h}}return null},fg=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var s=ut(o,r.dataKey,r.domain[u]);return ne(s)?null:r.scale(s)-a/2+i},CE=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},$E=function(t,r){var n,i=(n=t.type)!==null&&n!==void 0&&n.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,a=i.stackId;if(xe(a)){var o=r[a];if(o){var u=o.items.indexOf(t);return u>=0?o.stackedData[u]:null}}return null},IE=function(t){return t.reduce(function(r,n){return[Ia(n.concat([r[0]]).filter(B)),$a(n.concat([r[1]]).filter(B))]},[1/0,-1/0])},fx=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,s=u.reduce(function(c,f){var l=IE(f.slice(r,n+1));return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);return[Math.min(s[0],i[0]),Math.max(s[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},hg=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,dg=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,jl=function(t,r,n){if(Z(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(B(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(hg.test(t[0])){var a=+hg.exec(t[0])[1];i[0]=r[0]-a}else Z(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(B(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(dg.test(t[1])){var o=+dg.exec(t[1])[1];i[1]=r[1]+o}else Z(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Hi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=If(r,function(l){return l.coordinate}),o=1/0,u=1,s=a.length;u<s;u++){var c=a[u],f=a[u-1];o=Math.min((c.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},pg=function(t,r,n){return!t||!t.length||nh(t,ze(n,"type.defaultProps.domain"))?r:t},hx=function(t,r){var n=t.type.defaultProps?de(de({},t.type.defaultProps),t.props):t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,s=n.tooltipType,c=n.chartType,f=n.hide;return de(de({},ee(t,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:oh(t),value:ut(r,i),type:s,payload:r,chartType:c,hide:f})};function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}function vg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function yg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vg(Object(r),!0).forEach(function(n){NE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function NE(e,t,r){return t=RE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function RE(e){var t=kE(e,"string");return Tn(t)=="symbol"?t:t+""}function kE(e,t){if(Tn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Gi=Math.PI/180,DE=function(t){return t*180/Math.PI},Pe=function(t,r,n,i){return{x:t+Math.cos(-Gi*i)*n,y:r+Math.sin(-Gi*i)*n}},qE=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},BE=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=qE({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var s=(n-a)/u,c=Math.acos(s);return i>o&&(c=2*Math.PI-c),{radius:u,angle:DE(c),angleInRadian:c}},LE=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},FE=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},gg=function(t,r){var n=t.x,i=t.y,a=BE({x:n,y:i},r),o=a.radius,u=a.angle,s=r.innerRadius,c=r.outerRadius;if(o<s||o>c)return!1;if(o===0)return!0;var f=LE(r),l=f.startAngle,h=f.endAngle,p=u,y;if(l<=h){for(;p>h;)p-=360;for(;p<l;)p+=360;y=p>=l&&p<=h}else{for(;p>l;)p-=360;for(;p<h;)p+=360;y=p>=h&&p<=l}return y?yg(yg({},r),{},{radius:o,angle:FE(p,r)}):null};function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}var UE=["offset"];function zE(e){return KE(e)||GE(e)||HE(e)||WE()}function WE(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function HE(e,t){if(e){if(typeof e=="string")return El(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return El(e,t)}}function GE(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function KE(e){if(Array.isArray(e))return El(e)}function El(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function VE(e,t){if(e==null)return{};var r=XE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function XE(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function mg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function be(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mg(Object(r),!0).forEach(function(n){YE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function YE(e,t,r){return t=ZE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ZE(e){var t=JE(e,"string");return jn(t)=="symbol"?t:t+""}function JE(e,t){if(jn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function En(){return En=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},En.apply(this,arguments)}var QE=function(t){var r=t.value,n=t.formatter,i=ne(t.children)?r:t.children;return Z(n)?n(i):i},eM=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),360);return n*i},tM=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,s=a,c=s.cx,f=s.cy,l=s.innerRadius,h=s.outerRadius,p=s.startAngle,y=s.endAngle,v=s.clockWise,d=(l+h)/2,x=eM(p,y),w=x>=0?1:-1,b,O;i==="insideStart"?(b=p+w*o,O=v):i==="insideEnd"?(b=y-w*o,O=!v):i==="end"&&(b=y+w*o,O=v),O=x<=0?O:!O;var g=Pe(c,f,d,b),m=Pe(c,f,d,b+(O?1:-1)*359),_="M".concat(g.x,",").concat(g.y,`
    A`).concat(d,",").concat(d,",0,1,").concat(O?0:1,`,
    `).concat(m.x,",").concat(m.y),S=ne(t.id)?ma("recharts-radial-line-"):t.id;return T.createElement("text",En({},n,{dominantBaseline:"central",className:ie("recharts-radial-bar-label",u)}),T.createElement("defs",null,T.createElement("path",{id:S,d:_})),T.createElement("textPath",{xlinkHref:"#".concat(S)},r))},rM=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,s=a.innerRadius,c=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var p=Pe(o,u,c+n,h),y=p.x,v=p.y;return{x:y,y:v,textAnchor:y>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var d=(s+c)/2,x=Pe(o,u,d,h),w=x.x,b=x.y;return{x:w,y:b,textAnchor:"middle",verticalAnchor:"middle"}},nM=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,s=o.y,c=o.width,f=o.height,l=f>=0?1:-1,h=l*i,p=l>0?"end":"start",y=l>0?"start":"end",v=c>=0?1:-1,d=v*i,x=v>0?"end":"start",w=v>0?"start":"end";if(a==="top"){var b={x:u+c/2,y:s-l*i,textAnchor:"middle",verticalAnchor:p};return be(be({},b),n?{height:Math.max(s-n.y,0),width:c}:{})}if(a==="bottom"){var O={x:u+c/2,y:s+f+h,textAnchor:"middle",verticalAnchor:y};return be(be({},O),n?{height:Math.max(n.y+n.height-(s+f),0),width:c}:{})}if(a==="left"){var g={x:u-d,y:s+f/2,textAnchor:x,verticalAnchor:"middle"};return be(be({},g),n?{width:Math.max(g.x-n.x,0),height:f}:{})}if(a==="right"){var m={x:u+c+d,y:s+f/2,textAnchor:w,verticalAnchor:"middle"};return be(be({},m),n?{width:Math.max(n.x+n.width-m.x,0),height:f}:{})}var _=n?{width:c,height:f}:{};return a==="insideLeft"?be({x:u+d,y:s+f/2,textAnchor:w,verticalAnchor:"middle"},_):a==="insideRight"?be({x:u+c-d,y:s+f/2,textAnchor:x,verticalAnchor:"middle"},_):a==="insideTop"?be({x:u+c/2,y:s+h,textAnchor:"middle",verticalAnchor:y},_):a==="insideBottom"?be({x:u+c/2,y:s+f-h,textAnchor:"middle",verticalAnchor:p},_):a==="insideTopLeft"?be({x:u+d,y:s+h,textAnchor:w,verticalAnchor:y},_):a==="insideTopRight"?be({x:u+c-d,y:s+h,textAnchor:x,verticalAnchor:y},_):a==="insideBottomLeft"?be({x:u+d,y:s+f-h,textAnchor:w,verticalAnchor:p},_):a==="insideBottomRight"?be({x:u+c-d,y:s+f-h,textAnchor:x,verticalAnchor:p},_):Lr(a)&&(B(a.x)||Ut(a.x))&&(B(a.y)||Ut(a.y))?be({x:u+Yt(a.x,c),y:s+Yt(a.y,f),textAnchor:"end",verticalAnchor:"end"},_):be({x:u+c/2,y:s+f/2,textAnchor:"middle",verticalAnchor:"middle"},_)},iM=function(t){return"cx"in t&&B(t.cx)};function je(e){var t=e.offset,r=t===void 0?5:t,n=VE(e,UE),i=be({offset:r},n),a=i.viewBox,o=i.position,u=i.value,s=i.children,c=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||ne(u)&&ne(s)&&!q.isValidElement(c)&&!Z(c))return null;if(q.isValidElement(c))return q.cloneElement(c,i);var p;if(Z(c)){if(p=q.createElement(c,i),q.isValidElement(p))return p}else p=QE(i);var y=iM(a),v=ee(i,!0);if(y&&(o==="insideStart"||o==="insideEnd"||o==="end"))return tM(i,p,v);var d=y?rM(i):nM(i);return T.createElement(Ci,En({className:ie("recharts-label",l)},v,d,{breakAll:h}),p)}je.displayName="Label";var dx=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,s=t.radius,c=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,p=t.top,y=t.left,v=t.width,d=t.height,x=t.clockWise,w=t.labelViewBox;if(w)return w;if(B(v)&&B(d)){if(B(l)&&B(h))return{x:l,y:h,width:v,height:d};if(B(p)&&B(y))return{x:p,y,width:v,height:d}}return B(l)&&B(h)?{x:l,y:h,width:0,height:0}:B(r)&&B(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:c||0,outerRadius:f||s||u||0,clockWise:x}:t.viewBox?t.viewBox:{}},aM=function(t,r){return t?t===!0?T.createElement(je,{key:"label-implicit",viewBox:r}):xe(t)?T.createElement(je,{key:"label-implicit",viewBox:r,value:t}):q.isValidElement(t)?t.type===je?q.cloneElement(t,{key:"label-implicit",viewBox:r}):T.createElement(je,{key:"label-implicit",content:t,viewBox:r}):Z(t)?T.createElement(je,{key:"label-implicit",content:t,viewBox:r}):Lr(t)?T.createElement(je,En({viewBox:r},t,{key:"label-implicit"})):null:null},oM=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=dx(t),o=Ze(i,je).map(function(s,c){return q.cloneElement(s,{viewBox:r||a,key:"label-".concat(c)})});if(!n)return o;var u=aM(t.label,r||a);return[u].concat(zE(o))};je.parseViewBox=dx;je.renderCallByParent=oM;var bc,bg;function uM(){if(bg)return bc;bg=1;function e(t){var r=t==null?0:t.length;return r?t[r-1]:void 0}return bc=e,bc}var sM=uM();const cM=se(sM);function Mn(e){"@babel/helpers - typeof";return Mn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mn(e)}var lM=["valueAccessor"],fM=["data","dataKey","clockWise","id","textBreakAll"];function hM(e){return yM(e)||vM(e)||pM(e)||dM()}function dM(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function pM(e,t){if(e){if(typeof e=="string")return Ml(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ml(e,t)}}function vM(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function yM(e){if(Array.isArray(e))return Ml(e)}function Ml(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ki(){return Ki=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ki.apply(this,arguments)}function xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function wg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xg(Object(r),!0).forEach(function(n){gM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gM(e,t,r){return t=mM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mM(e){var t=bM(e,"string");return Mn(t)=="symbol"?t:t+""}function bM(e,t){if(Mn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Og(e,t){if(e==null)return{};var r=xM(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function xM(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var wM=function(t){return Array.isArray(t.value)?cM(t.value):t.value};function yt(e){var t=e.valueAccessor,r=t===void 0?wM:t,n=Og(e,lM),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,s=n.textBreakAll,c=Og(n,fM);return!i||!i.length?null:T.createElement(_e,{className:"recharts-label-list"},i.map(function(f,l){var h=ne(a)?r(f,l):ut(f&&f.payload,a),p=ne(u)?{}:{id:"".concat(u,"-").concat(l)};return T.createElement(je,Ki({},ee(f,!0),c,p,{parentViewBox:f.parentViewBox,value:h,textBreakAll:s,viewBox:je.parseViewBox(ne(o)?f:wg(wg({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}yt.displayName="LabelList";function OM(e,t){return e?e===!0?T.createElement(yt,{key:"labelList-implicit",data:t}):T.isValidElement(e)||Z(e)?T.createElement(yt,{key:"labelList-implicit",data:t,content:e}):Lr(e)?T.createElement(yt,Ki({data:t},e,{key:"labelList-implicit"})):null:null}function _M(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=Ze(n,yt).map(function(o,u){return q.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=OM(e.label,t);return[a].concat(hM(i))}yt.renderCallByParent=_M;function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Cl(){return Cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Cl.apply(this,arguments)}function _g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Sg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?_g(Object(r),!0).forEach(function(n){SM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_g(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SM(e,t,r){return t=AM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function AM(e){var t=PM(e,"string");return Cn(t)=="symbol"?t:t+""}function PM(e,t){if(Cn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var TM=function(t,r){var n=Ye(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},pi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,s=t.cornerRadius,c=t.cornerIsExternal,f=s*(u?1:-1)+i,l=Math.asin(s/f)/Gi,h=c?a:a+o*l,p=Pe(r,n,f,h),y=Pe(r,n,i,h),v=c?a-o*l:a,d=Pe(r,n,f*Math.cos(l*Gi),v);return{center:p,circleTangency:y,lineTangency:d,theta:l}},px=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,s=TM(o,u),c=o+s,f=Pe(r,n,a,o),l=Pe(r,n,a,c),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(s)>180),",").concat(+(o>c),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var p=Pe(r,n,i,o),y=Pe(r,n,i,c);h+="L ".concat(y.x,",").concat(y.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(s)>180),",").concat(+(o<=c),`,
            `).concat(p.x,",").concat(p.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},jM=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,s=t.cornerIsExternal,c=t.startAngle,f=t.endAngle,l=Ye(f-c),h=pi({cx:r,cy:n,radius:a,angle:c,sign:l,cornerRadius:o,cornerIsExternal:s}),p=h.circleTangency,y=h.lineTangency,v=h.theta,d=pi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:s}),x=d.circleTangency,w=d.lineTangency,b=d.theta,O=s?Math.abs(c-f):Math.abs(c-f)-v-b;if(O<0)return u?"M ".concat(y.x,",").concat(y.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):px({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:c,endAngle:f});var g="M ".concat(y.x,",").concat(y.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(w.x,",").concat(w.y,`
  `);if(i>0){var m=pi({cx:r,cy:n,radius:i,angle:c,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),_=m.circleTangency,S=m.lineTangency,P=m.theta,C=pi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),A=C.circleTangency,j=C.lineTangency,E=C.theta,N=s?Math.abs(c-f):Math.abs(c-f)-P-E;if(N<0&&o===0)return"".concat(g,"L").concat(r,",").concat(n,"Z");g+="L".concat(j.x,",").concat(j.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(N>180),",").concat(+(l>0),",").concat(_.x,",").concat(_.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(S.x,",").concat(S.y,"Z")}else g+="L".concat(r,",").concat(n,"Z");return g},EM={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},vx=function(t){var r=Sg(Sg({},EM),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,s=r.forceCornerRadius,c=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var p=ie("recharts-sector",h),y=o-a,v=Yt(u,y,0,!0),d;return v>0&&Math.abs(f-l)<360?d=jM({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,y/2),forceCornerRadius:s,cornerIsExternal:c,startAngle:f,endAngle:l}):d=px({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),T.createElement("path",Cl({},ee(r,!0),{className:p,d,role:"img"}))};function $n(e){"@babel/helpers - typeof";return $n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$n(e)}function $l(){return $l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},$l.apply(this,arguments)}function Ag(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ag(Object(r),!0).forEach(function(n){MM(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ag(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MM(e,t,r){return t=CM(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function CM(e){var t=$M(e,"string");return $n(t)=="symbol"?t:t+""}function $M(e,t){if($n(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($n(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Tg={curveBasisClosed:J1,curveBasisOpen:Q1,curveBasis:Z1,curveBumpX:D1,curveBumpY:q1,curveLinearClosed:eO,curveLinear:wa,curveMonotoneX:tO,curveMonotoneY:rO,curveNatural:nO,curveStep:iO,curveStepAfter:oO,curveStepBefore:aO},vi=function(t){return t.x===+t.x&&t.y===+t.y},Zr=function(t){return t.x},Jr=function(t){return t.y},IM=function(t,r){if(Z(t))return t;var n="curve".concat(ba(t));return(n==="curveMonotone"||n==="curveBump")&&r?Tg["".concat(n).concat(r==="vertical"?"Y":"X")]:Tg[n]||wa},NM=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,s=t.connectNulls,c=s===void 0?!1:s,f=IM(n,u),l=c?a.filter(function(v){return vi(v)}):a,h;if(Array.isArray(o)){var p=c?o.filter(function(v){return vi(v)}):o,y=l.map(function(v,d){return Pg(Pg({},v),{},{base:p[d]})});return u==="vertical"?h=oi().y(Jr).x1(Zr).x0(function(v){return v.base.x}):h=oi().x(Zr).y1(Jr).y0(function(v){return v.base.y}),h.defined(vi).curve(f),h(y)}return u==="vertical"&&B(o)?h=oi().y(Jr).x1(Zr).x0(o):B(o)?h=oi().x(Zr).y1(Jr).y0(o):h=bb().x(Zr).y(Jr),h.defined(vi).curve(f),h(l)},jg=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?NM(t):i;return T.createElement("path",$l({},ee(t,!1),bi(t),{className:ie("recharts-curve",r),d:o,ref:a}))},xc={exports:{}},wc,Eg;function RM(){if(Eg)return wc;Eg=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return wc=e,wc}var Oc,Mg;function kM(){if(Mg)return Oc;Mg=1;var e=RM();function t(){}function r(){}return r.resetWarningCache=t,Oc=function(){function n(o,u,s,c,f,l){if(l!==e){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}n.isRequired=n;function i(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:i,element:n,elementType:n,instanceOf:i,node:n,objectOf:i,oneOf:i,oneOfType:i,shape:i,exact:i,checkPropTypes:r,resetWarningCache:t};return a.PropTypes=a,a},Oc}var Cg;function DM(){return Cg||(Cg=1,xc.exports=kM()()),xc.exports}var qM=DM();const re=se(qM);var BM=Object.getOwnPropertyNames,LM=Object.getOwnPropertySymbols,FM=Object.prototype.hasOwnProperty;function $g(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function yi(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var s=e(r,n,i);return a.delete(r),a.delete(n),s}}function Ig(e){return BM(e).concat(LM(e))}var UM=Object.hasOwn||function(e,t){return FM.call(e,t)};function rr(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var zM="__v",WM="__o",HM="_owner",Ng=Object.getOwnPropertyDescriptor,Rg=Object.keys;function GM(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function KM(e,t){return rr(e.getTime(),t.getTime())}function VM(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function XM(e,t){return e===t}function kg(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,s=0;(o=a.next())&&!o.done;){for(var c=t.entries(),f=!1,l=0;(u=c.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,p=u.value;if(r.equals(h[0],p[0],s,l,e,t,r)&&r.equals(h[1],p[1],h[0],p[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;s++}return!0}var YM=rr;function ZM(e,t,r){var n=Rg(e),i=n.length;if(Rg(t).length!==i)return!1;for(;i-- >0;)if(!yx(e,t,r,n[i]))return!1;return!0}function Qr(e,t,r){var n=Ig(e),i=n.length;if(Ig(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!yx(e,t,r,a)||(o=Ng(e,a),u=Ng(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function JM(e,t){return rr(e.valueOf(),t.valueOf())}function QM(e,t){return e.source===t.source&&e.flags===t.flags}function Dg(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var s=t.values(),c=!1,f=0;(u=s.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){c=i[f]=!0;break}f++}if(!c)return!1}return!0}function eC(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function tC(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function yx(e,t,r,n){return(n===HM||n===WM||n===zM)&&(e.$$typeof||t.$$typeof)?!0:UM(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var rC="[object Arguments]",nC="[object Boolean]",iC="[object Date]",aC="[object Error]",oC="[object Map]",uC="[object Number]",sC="[object Object]",cC="[object RegExp]",lC="[object Set]",fC="[object String]",hC="[object URL]",dC=Array.isArray,qg=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Bg=Object.assign,pC=Object.prototype.toString.call.bind(Object.prototype.toString);function vC(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,s=e.arePrimitiveWrappersEqual,c=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(y,v,d){if(y===v)return!0;if(y==null||v==null)return!1;var x=typeof y;if(x!==typeof v)return!1;if(x!=="object")return x==="number"?o(y,v,d):x==="function"?i(y,v,d):!1;var w=y.constructor;if(w!==v.constructor)return!1;if(w===Object)return u(y,v,d);if(dC(y))return t(y,v,d);if(qg!=null&&qg(y))return l(y,v,d);if(w===Date)return r(y,v,d);if(w===RegExp)return c(y,v,d);if(w===Map)return a(y,v,d);if(w===Set)return f(y,v,d);var b=pC(y);return b===iC?r(y,v,d):b===cC?c(y,v,d):b===oC?a(y,v,d):b===lC?f(y,v,d):b===sC?typeof y.then!="function"&&typeof v.then!="function"&&u(y,v,d):b===hC?h(y,v,d):b===aC?n(y,v,d):b===rC?u(y,v,d):b===nC||b===uC||b===fC?s(y,v,d):!1}}function yC(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Qr:GM,areDatesEqual:KM,areErrorsEqual:VM,areFunctionsEqual:XM,areMapsEqual:n?$g(kg,Qr):kg,areNumbersEqual:YM,areObjectsEqual:n?Qr:ZM,arePrimitiveWrappersEqual:JM,areRegExpsEqual:QM,areSetsEqual:n?$g(Dg,Qr):Dg,areTypedArraysEqual:n?Qr:eC,areUrlsEqual:tC};if(r&&(i=Bg({},i,r(i))),t){var a=yi(i.areArraysEqual),o=yi(i.areMapsEqual),u=yi(i.areObjectsEqual),s=yi(i.areSetsEqual);i=Bg({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:s})}return i}function gC(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function mC(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(s,c){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,p=f.meta;return r(s,c,{cache:h,equals:i,meta:p,strict:a})};if(t)return function(s,c){return r(s,c,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(s,c){return r(s,c,o)}}var bC=Nt();Nt({strict:!0});Nt({circular:!0});Nt({circular:!0,strict:!0});Nt({createInternalComparator:function(){return rr}});Nt({strict:!0,createInternalComparator:function(){return rr}});Nt({circular:!0,createInternalComparator:function(){return rr}});Nt({circular:!0,createInternalComparator:function(){return rr},strict:!0});function Nt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=yC(e),s=vC(u),c=n?n(s):gC(s);return mC({circular:r,comparator:s,createState:i,equals:c,strict:o})}function xC(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Lg(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):xC(i)};requestAnimationFrame(n)}function Il(e){"@babel/helpers - typeof";return Il=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Il(e)}function wC(e){return AC(e)||SC(e)||_C(e)||OC()}function OC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function _C(e,t){if(e){if(typeof e=="string")return Fg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fg(e,t)}}function Fg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function SC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function AC(e){if(Array.isArray(e))return e}function PC(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=wC(o),s=u[0],c=u.slice(1);if(typeof s=="number"){Lg(i.bind(null,c),s);return}i(s),Lg(i.bind(null,c));return}Il(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}function Ug(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ug(Object(r),!0).forEach(function(n){gx(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ug(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gx(e,t,r){return t=TC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function TC(e){var t=jC(e,"string");return In(t)==="symbol"?t:String(t)}function jC(e,t){if(In(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(In(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var EC=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},MC=function(t){return t},CC=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},ln=function(t,r){return Object.keys(r).reduce(function(n,i){return zg(zg({},n),{},gx({},i,t(i,r[i])))},{})},Wg=function(t,r,n){return t.map(function(i){return"".concat(CC(i)," ").concat(r,"ms ").concat(n)}).join(",")};function $C(e,t){return RC(e)||NC(e,t)||mx(e,t)||IC()}function IC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function NC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function RC(e){if(Array.isArray(e))return e}function kC(e){return BC(e)||qC(e)||mx(e)||DC()}function DC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mx(e,t){if(e){if(typeof e=="string")return Nl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Nl(e,t)}}function qC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function BC(e){if(Array.isArray(e))return Nl(e)}function Nl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Vi=1e-4,bx=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},xx=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Hg=function(t,r){return function(n){var i=bx(t,r);return xx(i,n)}},LC=function(t,r){return function(n){var i=bx(t,r),a=[].concat(kC(i.map(function(o,u){return o*u}).slice(1)),[0]);return xx(a,n)}},Gg=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var s=r[0].split("(");if(s[0]==="cubic-bezier"&&s[1].split(")")[0].split(",").length===4){var c=s[1].split(")")[0].split(",").map(function(d){return parseFloat(d)}),f=$C(c,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=Hg(i,o),h=Hg(a,u),p=LC(i,o),y=function(x){return x>1?1:x<0?0:x},v=function(x){for(var w=x>1?1:x,b=w,O=0;O<8;++O){var g=l(b)-w,m=p(b);if(Math.abs(g-w)<Vi||m<Vi)return h(b);b=y(b-g/m)}return h(b)};return v.isStepper=!1,v},FC=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,s=function(f,l,h){var p=-(f-l)*n,y=h*a,v=h+(p-y)*u/1e3,d=h*u/1e3+f;return Math.abs(d-l)<Vi&&Math.abs(v)<Vi?[l,0]:[d,v]};return s.isStepper=!0,s.dt=u,s},UC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Gg(i);case"spring":return FC();default:if(i.split("(")[0]==="cubic-bezier")return Gg(i)}return typeof i=="function"?i:null};function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function Kg(e){return HC(e)||WC(e)||wx(e)||zC()}function zC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function WC(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function HC(e){if(Array.isArray(e))return kl(e)}function Vg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vg(Object(r),!0).forEach(function(n){Rl(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Rl(e,t,r){return t=GC(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function GC(e){var t=KC(e,"string");return Nn(t)==="symbol"?t:String(t)}function KC(e,t){if(Nn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function VC(e,t){return ZC(e)||YC(e,t)||wx(e,t)||XC()}function XC(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wx(e,t){if(e){if(typeof e=="string")return kl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kl(e,t)}}function kl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function YC(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function ZC(e){if(Array.isArray(e))return e}var Xi=function(t,r,n){return t+(r-t)*n},Dl=function(t){var r=t.from,n=t.to;return r!==n},JC=function e(t,r,n){var i=ln(function(a,o){if(Dl(o)){var u=t(o.from,o.to,o.velocity),s=VC(u,2),c=s[0],f=s[1];return Ae(Ae({},o),{},{from:c,velocity:f})}return o},r);return n<1?ln(function(a,o){return Dl(o)?Ae(Ae({},o),{},{velocity:Xi(o.velocity,i[a].velocity,n),from:Xi(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const QC=function(e,t,r,n,i){var a=EC(e,t),o=a.reduce(function(d,x){return Ae(Ae({},d),{},Rl({},x,[e[x],t[x]]))},{}),u=a.reduce(function(d,x){return Ae(Ae({},d),{},Rl({},x,{from:e[x],velocity:0,to:t[x]}))},{}),s=-1,c,f,l=function(){return null},h=function(){return ln(function(x,w){return w.from},u)},p=function(){return!Object.values(u).filter(Dl).length},y=function(x){c||(c=x);var w=x-c,b=w/r.dt;u=JC(r,u,b),i(Ae(Ae(Ae({},e),t),h())),c=x,p()||(s=requestAnimationFrame(l))},v=function(x){f||(f=x);var w=(x-f)/n,b=ln(function(g,m){return Xi.apply(void 0,Kg(m).concat([r(w)]))},o);if(i(Ae(Ae(Ae({},e),t),b)),w<1)s=requestAnimationFrame(l);else{var O=ln(function(g,m){return Xi.apply(void 0,Kg(m).concat([r(1)]))},o);i(Ae(Ae(Ae({},e),t),O))}};return l=r.isStepper?y:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(s)}}};function Tr(e){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tr(e)}var e$=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function t$(e,t){if(e==null)return{};var r=r$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function r$(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function _c(e){return o$(e)||a$(e)||i$(e)||n$()}function n$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function i$(e,t){if(e){if(typeof e=="string")return ql(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ql(e,t)}}function a$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function o$(e){if(Array.isArray(e))return ql(e)}function ql(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Xg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Xg(Object(r),!0).forEach(function(n){on(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function on(e,t,r){return t=Ox(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function u$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s$(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Ox(n.key),n)}}function c$(e,t,r){return t&&s$(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ox(e){var t=l$(e,"string");return Tr(t)==="symbol"?t:String(t)}function l$(e,t){if(Tr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Tr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function f$(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Bl(e,t)}function Bl(e,t){return Bl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Bl(e,t)}function h$(e){var t=d$();return function(){var n=Yi(e),i;if(t){var a=Yi(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Ll(this,i)}}function Ll(e,t){if(t&&(Tr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Fl(e)}function Fl(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function d$(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Yi(e){return Yi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Yi(e)}var Et=function(e){f$(r,e);var t=h$(r);function r(n,i){var a;u$(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,s=o.attributeName,c=o.from,f=o.to,l=o.steps,h=o.children,p=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Fl(a)),a.changeStyle=a.changeStyle.bind(Fl(a)),!u||p<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),Ll(a);if(l&&l.length)a.state={style:l[0].style};else if(c){if(typeof h=="function")return a.state={style:c},Ll(a);a.state={style:s?on({},s,c):c}}else a.state={style:{}};return a}return c$(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,s=a.attributeName,c=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var p={style:s?on({},s,f):f};this.state&&h&&(s&&h[s]!==f||!s&&h!==f)&&this.setState(p);return}if(!(bC(i.to,f)&&i.canBegin&&i.isActive)){var y=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=y||c?l:i.to;if(this.state&&h){var d={style:s?on({},s,v):v};(s&&h[s]!==v||!s&&h!==v)&&this.setState(d)}this.runAnimation(Ke(Ke({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,s=i.duration,c=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,p=QC(o,u,UC(c),s,this.changeStyle),y=function(){a.stopJSAnimation=p()};this.manager.start([h,f,y,s,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,s=i.onAnimationStart,c=o[0],f=c.style,l=c.duration,h=l===void 0?0:l,p=function(v,d,x){if(x===0)return v;var w=d.duration,b=d.easing,O=b===void 0?"ease":b,g=d.style,m=d.properties,_=d.onAnimationEnd,S=x>0?o[x-1]:d,P=m||Object.keys(g);if(typeof O=="function"||O==="spring")return[].concat(_c(v),[a.runJSAnimation.bind(a,{from:S.style,to:g,duration:w,easing:O}),w]);var C=Wg(P,w,O),A=Ke(Ke(Ke({},S.style),g),{},{transition:C});return[].concat(_c(v),[A,w,_]).filter(MC)};return this.manager.start([s].concat(_c(o.reduce(p,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=PC());var a=i.begin,o=i.duration,u=i.attributeName,s=i.to,c=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,p=i.children,y=this.manager;if(this.unSubscribe=y.subscribe(this.handleStyleChange),typeof c=="function"||typeof p=="function"||c==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?on({},u,s):s,d=Wg(Object.keys(v),o,c);y.start([f,a,Ke(Ke({},v),{},{transition:d}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var s=t$(i,e$),c=q.Children.count(a),f=this.state.style;if(typeof a=="function")return a(f);if(!u||c===0||o<=0)return a;var l=function(p){var y=p.props,v=y.style,d=v===void 0?{}:v,x=y.className,w=q.cloneElement(p,Ke(Ke({},s),{},{style:Ke(Ke({},d),f),className:x}));return w};return c===1?l(q.Children.only(a)):T.createElement("div",null,q.Children.map(a,function(h){return l(h)}))}}]),r}(q.PureComponent);Et.displayName="Animate";Et.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};Et.propTypes={from:re.oneOfType([re.object,re.string]),to:re.oneOfType([re.object,re.string]),attributeName:re.string,duration:re.number,begin:re.number,easing:re.oneOfType([re.string,re.func]),steps:re.arrayOf(re.shape({duration:re.number.isRequired,style:re.object.isRequired,easing:re.oneOfType([re.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),re.func]),properties:re.arrayOf("string"),onAnimationEnd:re.func})),children:re.oneOfType([re.node,re.func]),isActive:re.bool,canBegin:re.bool,onAnimationEnd:re.func,shouldReAnimate:re.bool,onAnimationStart:re.func,onAnimationReStart:re.func};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Zi(){return Zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zi.apply(this,arguments)}function p$(e,t){return m$(e)||g$(e,t)||y$(e,t)||v$()}function v$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function y$(e,t){if(e){if(typeof e=="string")return Yg(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Yg(e,t)}}function Yg(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function g$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function m$(e){if(Array.isArray(e))return e}function Zg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Jg(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zg(Object(r),!0).forEach(function(n){b$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zg(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function b$(e,t,r){return t=x$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function x$(e){var t=w$(e,"string");return Rn(t)=="symbol"?t:t+""}function w$(e,t){if(Rn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qg=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,s=n>=0?1:-1,c=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,p=4;h<p;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(c,",").concat(t+s*l[0],",").concat(r)),f+="L ".concat(t+n-s*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(c,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(c,`,
        `).concat(t+n-s*l[2],",").concat(r+i)),f+="L ".concat(t+s*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(c,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var y=Math.min(o,a);f="M ".concat(t,",").concat(r+u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+s*y,",").concat(r,`
            L `).concat(t+n-s*y,",").concat(r,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+n,",").concat(r+u*y,`
            L `).concat(t+n,",").concat(r+i-u*y,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t+n-s*y,",").concat(r+i,`
            L `).concat(t+s*y,",").concat(r+i,`
            A `).concat(y,",").concat(y,",0,0,").concat(c,",").concat(t,",").concat(r+i-u*y," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},O$=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,s=r.height;if(Math.abs(u)>0&&Math.abs(s)>0){var c=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+s),h=Math.max(o,o+s);return n>=c&&n<=f&&i>=l&&i<=h}return!1},_$={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},uh=function(t){var r=Jg(Jg({},_$),t),n=q.useRef(),i=q.useState(-1),a=p$(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var s=r.x,c=r.y,f=r.width,l=r.height,h=r.radius,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isAnimationActive,w=r.isUpdateAnimationActive;if(s!==+s||c!==+c||f!==+f||l!==+l||f===0||l===0)return null;var b=ie("recharts-rectangle",p);return w?T.createElement(Et,{canBegin:o>0,from:{width:f,height:l,x:s,y:c},to:{width:f,height:l,x:s,y:c},duration:v,animationEasing:y,isActive:w},function(O){var g=O.width,m=O.height,_=O.x,S=O.y;return T.createElement(Et,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,isActive:x,easing:y},T.createElement("path",Zi({},ee(r,!0),{className:b,d:Qg(_,S,g,m,h),ref:n})))}):T.createElement("path",Zi({},ee(r,!0),{className:b,d:Qg(s,c,f,l,h)}))};function Ul(){return Ul=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ul.apply(this,arguments)}var _x=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ie("recharts-dot",a);return r===+r&&n===+n&&i===+i?T.createElement("circle",Ul({},ee(t,!1),bi(t),{className:o,cx:r,cy:n,r:i})):null};function kn(e){"@babel/helpers - typeof";return kn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kn(e)}var S$=["x","y","top","left","width","height","className"];function zl(){return zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},zl.apply(this,arguments)}function em(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function A$(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?em(Object(r),!0).forEach(function(n){P$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):em(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function P$(e,t,r){return t=T$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T$(e){var t=j$(e,"string");return kn(t)=="symbol"?t:t+""}function j$(e,t){if(kn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function E$(e,t){if(e==null)return{};var r=M$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function M$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var C$=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},$$=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,s=t.left,c=s===void 0?0:s,f=t.width,l=f===void 0?0:f,h=t.height,p=h===void 0?0:h,y=t.className,v=E$(t,S$),d=A$({x:n,y:a,top:u,left:c,width:l,height:p},v);return!B(n)||!B(a)||!B(l)||!B(p)||!B(u)||!B(c)?null:T.createElement("path",zl({},ee(d,!0),{className:ie("recharts-cross",y),d:C$(n,a,l,p,u,c)}))},Sc,tm;function I$(){if(tm)return Sc;tm=1;var e=Ub(),t=e(Object.getPrototypeOf,Object);return Sc=t,Sc}var Ac,rm;function N$(){if(rm)return Ac;rm=1;var e=xt(),t=I$(),r=wt(),n="[object Object]",i=Function.prototype,a=Object.prototype,o=i.toString,u=a.hasOwnProperty,s=o.call(Object);function c(f){if(!r(f)||e(f)!=n)return!1;var l=t(f);if(l===null)return!0;var h=u.call(l,"constructor")&&l.constructor;return typeof h=="function"&&h instanceof h&&o.call(h)==s}return Ac=c,Ac}var R$=N$();const k$=se(R$);var Pc,nm;function D$(){if(nm)return Pc;nm=1;var e=xt(),t=wt(),r="[object Boolean]";function n(i){return i===!0||i===!1||t(i)&&e(i)==r}return Pc=n,Pc}var q$=D$();const B$=se(q$);function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function L$(e,t){return W$(e)||z$(e,t)||U$(e,t)||F$()}function F$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function U$(e,t){if(e){if(typeof e=="string")return im(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return im(e,t)}}function im(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function z$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function W$(e){if(Array.isArray(e))return e}function am(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function om(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?am(Object(r),!0).forEach(function(n){H$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):am(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H$(e,t,r){return t=G$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function G$(e){var t=K$(e,"string");return Dn(t)=="symbol"?t:t+""}function K$(e,t){if(Dn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var um=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},V$={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},X$=function(t){var r=om(om({},V$),t),n=q.useRef(),i=q.useState(-1),a=L$(i,2),o=a[0],u=a[1];q.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var b=n.current.getTotalLength();b&&u(b)}catch{}},[]);var s=r.x,c=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,p=r.className,y=r.animationEasing,v=r.animationDuration,d=r.animationBegin,x=r.isUpdateAnimationActive;if(s!==+s||c!==+c||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var w=ie("recharts-trapezoid",p);return x?T.createElement(Et,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:s,y:c},to:{upperWidth:f,lowerWidth:l,height:h,x:s,y:c},duration:v,animationEasing:y,isActive:x},function(b){var O=b.upperWidth,g=b.lowerWidth,m=b.height,_=b.x,S=b.y;return T.createElement(Et,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:d,duration:v,easing:y},T.createElement("path",Ji({},ee(r,!0),{className:w,d:um(_,S,O,g,m),ref:n})))}):T.createElement("g",null,T.createElement("path",Ji({},ee(r,!0),{className:w,d:um(s,c,f,l,h)})))},Y$=["option","shapeType","propTransformer","activeClassName","isActive"];function qn(e){"@babel/helpers - typeof";return qn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qn(e)}function Z$(e,t){if(e==null)return{};var r=J$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function J$(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function sm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sm(Object(r),!0).forEach(function(n){Q$(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Q$(e,t,r){return t=eI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eI(e){var t=tI(e,"string");return qn(t)=="symbol"?t:t+""}function tI(e,t){if(qn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(qn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function rI(e,t){return Qi(Qi({},t),e)}function nI(e,t){return e==="symbols"}function cm(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return T.createElement(uh,r);case"trapezoid":return T.createElement(X$,r);case"sector":return T.createElement(vx,r);case"symbols":if(nI(t))return T.createElement(Af,r);break;default:return null}}function iI(e){return q.isValidElement(e)?e.props:e}function aI(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?rI:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,s=Z$(e,Y$),c;if(q.isValidElement(t))c=q.cloneElement(t,Qi(Qi({},s),iI(t)));else if(Z(t))c=t(s);else if(k$(t)&&!B$(t)){var f=i(t,s);c=T.createElement(cm,{shapeType:r,elementProps:f})}else{var l=s;c=T.createElement(cm,{shapeType:r,elementProps:l})}return u?T.createElement(_e,{className:o},c):c}function Da(e,t){return t!=null&&"trapezoids"in e.props}function qa(e,t){return t!=null&&"sectors"in e.props}function Bn(e,t){return t!=null&&"points"in e.props}function oI(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function uI(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function sI(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function cI(e,t){var r;return Da(e,t)?r=oI:qa(e,t)?r=uI:Bn(e,t)&&(r=sI),r}function lI(e,t){var r;return Da(e,t)?r="trapezoids":qa(e,t)?r="sectors":Bn(e,t)&&(r="points"),r}function fI(e,t){if(Da(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(qa(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Bn(e,t)?t.payload:{}}function hI(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=lI(r,t),a=fI(r,t),o=n.filter(function(s,c){var f=nh(a,s),l=r.props[i].filter(function(y){var v=cI(r,t);return v(y,t)}),h=r.props[i].indexOf(l[l.length-1]),p=c===h;return f&&p}),u=n.indexOf(o[o.length-1]);return u}var Tc,lm;function dI(){if(lm)return Tc;lm=1;var e=Math.ceil,t=Math.max;function r(n,i,a,o){for(var u=-1,s=t(e((i-n)/(a||1)),0),c=Array(s);s--;)c[o?s:++u]=n,n+=a;return c}return Tc=r,Tc}var jc,fm;function Sx(){if(fm)return jc;fm=1;var e=i0(),t=1/0,r=17976931348623157e292;function n(i){if(!i)return i===0?i:0;if(i=e(i),i===t||i===-1/0){var a=i<0?-1:1;return a*r}return i===i?i:0}return jc=n,jc}var Ec,hm;function pI(){if(hm)return Ec;hm=1;var e=dI(),t=Aa(),r=Sx();function n(i){return function(a,o,u){return u&&typeof u!="number"&&t(a,o,u)&&(o=u=void 0),a=r(a),o===void 0?(o=a,a=0):o=r(o),u=u===void 0?a<o?1:-1:r(u),e(a,o,u,i)}}return Ec=n,Ec}var Mc,dm;function vI(){if(dm)return Mc;dm=1;var e=pI(),t=e();return Mc=t,Mc}var yI=vI();const ea=se(yI);function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function pm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function vm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?pm(Object(r),!0).forEach(function(n){Ax(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ax(e,t,r){return t=gI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gI(e){var t=mI(e,"string");return Ln(t)=="symbol"?t:t+""}function mI(e,t){if(Ln(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ln(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var bI=["Webkit","Moz","O","ms"],xI=function(t,r){var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=bI.reduce(function(a,o){return vm(vm({},a),{},Ax({},o+n,r))},{});return i[t]=r,i};function jr(e){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jr(e)}function ta(){return ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ta.apply(this,arguments)}function ym(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Cc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ym(Object(r),!0).forEach(function(n){De(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ym(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function wI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function gm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Tx(n.key),n)}}function OI(e,t,r){return t&&gm(e.prototype,t),r&&gm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _I(e,t,r){return t=ra(t),SI(e,Px()?Reflect.construct(t,r||[],ra(e).constructor):t.apply(e,r))}function SI(e,t){if(t&&(jr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return AI(e)}function AI(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Px(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Px=function(){return!!e})()}function ra(e){return ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ra(e)}function PI(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Wl(e,t)}function Wl(e,t){return Wl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Wl(e,t)}function De(e,t,r){return t=Tx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Tx(e){var t=TI(e,"string");return jr(t)=="symbol"?t:t+""}function TI(e,t){if(jr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var jI=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var s=r.length,c=sn().domain(ea(0,s)).range([a,a+o-u]),f=c.domain().map(function(l){return c(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(i),scale:c,scaleValues:f}},mm=function(t){return t.changedTouches&&!!t.changedTouches.length},Er=function(e){function t(r){var n;return wI(this,t),n=_I(this,t,[r]),De(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),De(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),De(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o?.({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),De(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),De(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),De(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),De(n,"handleSlideDragStart",function(i){var a=mm(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return PI(t,e),OI(t,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,s=u.gap,c=u.data,f=c.length-1,l=Math.min(i,a),h=Math.max(i,a),p=t.getIndexInRange(o,l),y=t.getIndexInRange(o,h);return{startIndex:p-p%s,endIndex:y===f?f:y-y%s}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,s=ut(a[n],u,n);return Z(o)?o(s,n):s}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,s=this.props,c=s.x,f=s.width,l=s.travellerWidth,h=s.startIndex,p=s.endIndex,y=s.onChange,v=n.pageX-a;v>0?v=Math.min(v,c+f-l-u,c+f-l-o):v<0&&(v=Math.max(v,c-o,c-u));var d=this.getIndex({startX:o+v,endX:u+v});(d.startIndex!==h||d.endIndex!==p)&&y&&y(d),this.setState({startX:o+v,endX:u+v,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=mm(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,s=i.startX,c=this.state[o],f=this.props,l=f.x,h=f.width,p=f.travellerWidth,y=f.onChange,v=f.gap,d=f.data,x={startX:this.state.startX,endX:this.state.endX},w=n.pageX-a;w>0?w=Math.min(w,l+h-p-c):w<0&&(w=Math.max(w,l-c)),x[o]=c+w;var b=this.getIndex(x),O=b.startIndex,g=b.endIndex,m=function(){var S=d.length-1;return o==="startX"&&(u>s?O%v===0:g%v===0)||u<s&&g===S||o==="endX"&&(u>s?g%v===0:O%v===0)||u>s&&g===S};this.setState(De(De({},o,c+w),"brushMoveStartX",n.pageX),function(){y&&m()&&y(b)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,s=o.startX,c=o.endX,f=this.state[i],l=u.indexOf(f);if(l!==-1){var h=l+n;if(!(h===-1||h>=u.length)){var p=u[h];i==="startX"&&p>=c||i==="endX"&&p<=s||this.setState(De({},i,p),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.fill,c=n.stroke;return T.createElement("rect",{stroke:c,fill:s,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.data,c=n.children,f=n.padding,l=q.Children.only(c);return l?T.cloneElement(l,{x:i,y:a,width:o,height:u,margin:f,compact:!0,data:s}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,s=this.props,c=s.y,f=s.travellerWidth,l=s.height,h=s.traveller,p=s.ariaLabel,y=s.data,v=s.startIndex,d=s.endIndex,x=Math.max(n,this.props.x),w=Cc(Cc({},ee(this.props,!1)),{},{x,y:c,width:f,height:l}),b=p||"Min value: ".concat((a=y[v])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=y[d])===null||o===void 0?void 0:o.name);return T.createElement(_e,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(g){["ArrowLeft","ArrowRight"].includes(g.key)&&(g.preventDefault(),g.stopPropagation(),u.handleTravellerMoveKeyboard(g.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(h,w))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,s=a.stroke,c=a.travellerWidth,f=Math.min(n,i)+c,l=Math.max(Math.abs(i-n)-c,0);return T.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:s,fillOpacity:.2,x:f,y:o,width:l,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,s=n.travellerWidth,c=n.stroke,f=this.state,l=f.startX,h=f.endX,p=5,y={pointerEvents:"none",fill:c};return T.createElement(_e,{className:"recharts-brush-texts"},T.createElement(Ci,ta({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,h)-p,y:o+u/2},y),this.getTextOfTick(i)),T.createElement(Ci,ta({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,h)+s+p,y:o+u/2},y),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,s=n.y,c=n.width,f=n.height,l=n.alwaysShowText,h=this.state,p=h.startX,y=h.endX,v=h.isTextActive,d=h.isSlideMoving,x=h.isTravellerMoving,w=h.isTravellerFocused;if(!i||!i.length||!B(u)||!B(s)||!B(c)||!B(f)||c<=0||f<=0)return null;var b=ie("recharts-brush",a),O=T.Children.count(o)===1,g=xI("userSelect","none");return T.createElement(_e,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(p,y),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(y,"endX"),(v||d||x||w||l)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,s=n.stroke,c=Math.floor(a+u/2)-1;return T.createElement(T.Fragment,null,T.createElement("rect",{x:i,y:a,width:o,height:u,fill:s,stroke:"none"}),T.createElement("line",{x1:i+1,y1:c,x2:i+o-1,y2:c,fill:"none",stroke:"#fff"}),T.createElement("line",{x1:i+1,y1:c+2,x2:i+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return T.isValidElement(n)?a=T.cloneElement(n,i):Z(n)?a=n(i):a=t.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,s=n.travellerWidth,c=n.updateId,f=n.startIndex,l=n.endIndex;if(a!==i.prevData||c!==i.prevUpdateId)return Cc({prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o},a&&a.length?jI({data:a,width:o,x:u,travellerWidth:s,startIndex:f,endIndex:l}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||s!==i.prevTravellerWidth)){i.scale.range([u,u+o-s]);var h=i.scale.domain().map(function(p){return i.scale(p)});return{prevData:a,prevTravellerWidth:s,prevUpdateId:c,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:h}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var s=Math.floor((o+u)/2);n[s]>i?u=s:o=s}return i>=n[u]?u:o}}])}(q.PureComponent);De(Er,"displayName","Brush");De(Er,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var $c,bm;function EI(){if(bm)return $c;bm=1;var e=$f();function t(r,n){var i;return e(r,function(a,o,u){return i=n(a,o,u),!i}),!!i}return $c=t,$c}var Ic,xm;function MI(){if(xm)return Ic;xm=1;var e=Rb(),t=Ct(),r=EI(),n=Re(),i=Aa();function a(o,u,s){var c=n(o)?e:r;return s&&i(o,u,s)&&(u=void 0),c(o,t(u,3))}return Ic=a,Ic}var CI=MI();const $I=se(CI);var at=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},Nc,wm;function II(){if(wm)return Nc;wm=1;var e=Qb();function t(r,n,i){n=="__proto__"&&e?e(r,n,{configurable:!0,enumerable:!0,value:i,writable:!0}):r[n]=i}return Nc=t,Nc}var Rc,Om;function NI(){if(Om)return Rc;Om=1;var e=II(),t=Zb(),r=Ct();function n(i,a){var o={};return a=r(a,3),t(i,function(u,s,c){e(o,s,a(u,s,c))}),o}return Rc=n,Rc}var RI=NI();const kI=se(RI);var kc,_m;function DI(){if(_m)return kc;_m=1;function e(t,r){for(var n=-1,i=t==null?0:t.length;++n<i;)if(!r(t[n],n,t))return!1;return!0}return kc=e,kc}var Dc,Sm;function qI(){if(Sm)return Dc;Sm=1;var e=$f();function t(r,n){var i=!0;return e(r,function(a,o,u){return i=!!n(a,o,u),i}),i}return Dc=t,Dc}var qc,Am;function BI(){if(Am)return qc;Am=1;var e=DI(),t=qI(),r=Ct(),n=Re(),i=Aa();function a(o,u,s){var c=n(o)?e:t;return s&&i(o,u,s)&&(u=void 0),c(o,r(u,3))}return qc=a,qc}var LI=BI();const jx=se(LI);var FI=["x","y"];function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function Hl(){return Hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Hl.apply(this,arguments)}function Pm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function en(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Pm(Object(r),!0).forEach(function(n){UI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function UI(e,t,r){return t=zI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zI(e){var t=WI(e,"string");return Fn(t)=="symbol"?t:t+""}function WI(e,t){if(Fn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Fn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function HI(e,t){if(e==null)return{};var r=GI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function GI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function KI(e,t){var r=e.x,n=e.y,i=HI(e,FI),a="".concat(r),o=parseInt(a,10),u="".concat(n),s=parseInt(u,10),c="".concat(t.height||i.height),f=parseInt(c,10),l="".concat(t.width||i.width),h=parseInt(l,10);return en(en(en(en(en({},t),i),o?{x:o}:{}),s?{y:s}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function Tm(e){return T.createElement(aI,Hl({shapeType:"rectangle",propTransformer:KI,activeClassName:"recharts-active-bar"},e))}var VI=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof t=="number")return t;var a=typeof n=="number";return a?t(n,i):(a||Vt(!1),r)}},XI=["value","background"],Ex;function Mr(e){"@babel/helpers - typeof";return Mr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(e)}function YI(e,t){if(e==null)return{};var r=ZI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function ZI(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},na.apply(this,arguments)}function jm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jm(Object(r),!0).forEach(function(n){Tt(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function JI(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Em(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Cx(n.key),n)}}function QI(e,t,r){return t&&Em(e.prototype,t),r&&Em(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function eN(e,t,r){return t=ia(t),tN(e,Mx()?Reflect.construct(t,r||[],ia(e).constructor):t.apply(e,r))}function tN(e,t){if(t&&(Mr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rN(e)}function rN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Mx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Mx=function(){return!!e})()}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function nN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Gl(e,t)}function Gl(e,t){return Gl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Gl(e,t)}function Tt(e,t,r){return t=Cx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cx(e){var t=iN(e,"string");return Mr(t)=="symbol"?t:t+""}function iN(e,t){if(Mr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Mr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var nr=function(e){function t(){var r;JI(this,t);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=eN(this,t,[].concat(i)),Tt(r,"state",{isAnimationFinished:!1}),Tt(r,"id",ma("recharts-bar-")),Tt(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Tt(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return nN(t,e),QI(t,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,s=a.activeIndex,c=a.activeBar,f=ee(this.props,!1);return n&&n.map(function(l,h){var p=h===s,y=p?c:o,v=ve(ve(ve({},f),l),{},{isActive:p,option:y,index:h,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return T.createElement(_e,na({className:"recharts-bar-rectangle"},xi(i.props,l,h),{key:"rectangle-".concat(l?.x,"-").concat(l?.y,"-").concat(l?.value,"-").concat(h)}),T.createElement(Tm,v))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,s=i.animationBegin,c=i.animationDuration,f=i.animationEasing,l=i.animationId,h=this.state.prevData;return T.createElement(Et,{begin:s,duration:c,isActive:u,easing:f,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var y=p.t,v=a.map(function(d,x){var w=h&&h[x];if(w){var b=or(w.x,d.x),O=or(w.y,d.y),g=or(w.width,d.width),m=or(w.height,d.height);return ve(ve({},d),{},{x:b(y),y:O(y),width:g(y),height:m(y)})}if(o==="horizontal"){var _=or(0,d.height),S=_(y);return ve(ve({},d),{},{y:d.y+d.height-S,height:S})}var P=or(0,d.width),C=P(y);return ve(ve({},d),{},{width:C})});return T.createElement(_e,null,n.renderRectanglesStatically(v))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!nh(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,s=ee(this.props.background,!1);return a.map(function(c,f){c.value;var l=c.background,h=YI(c,XI);if(!l)return null;var p=ve(ve(ve(ve(ve({},h),{},{fill:"#eee"},l),s),xi(n.props,c,f)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:f,className:"recharts-bar-background-rectangle"});return T.createElement(Tm,na({key:"background-bar-".concat(f),option:n.props.background,isActive:f===u},p))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,s=a.yAxis,c=a.layout,f=a.children,l=Ze(f,ka);if(!l)return null;var h=c==="vertical"?o[0].height/2:o[0].width/2,p=function(d,x){var w=Array.isArray(d.value)?d.value[1]:d.value;return{x:d.x,y:d.y,value:w,errorVal:ut(d,x)}},y={clipPath:n?"url(#clipPath-".concat(i,")"):null};return T.createElement(_e,y,l.map(function(v){return T.cloneElement(v,{key:"error-bar-".concat(i,"-").concat(v.props.dataKey),data:o,xAxis:u,yAxis:s,layout:c,offset:h,dataPointFormatter:p})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,s=n.yAxis,c=n.left,f=n.top,l=n.width,h=n.height,p=n.isAnimationActive,y=n.background,v=n.id;if(i||!a||!a.length)return null;var d=this.state.isAnimationFinished,x=ie("recharts-bar",o),w=u&&u.allowDataOverflow,b=s&&s.allowDataOverflow,O=w||b,g=ne(v)?this.id:v;return T.createElement(_e,{className:x},w||b?T.createElement("defs",null,T.createElement("clipPath",{id:"clipPath-".concat(g)},T.createElement("rect",{x:w?c:c-l/2,y:b?f:f-h/2,width:w?l:l*2,height:b?h:h*2}))):null,T.createElement(_e,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(g,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,g),(!p||d)&&yt.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(q.PureComponent);Ex=nr;Tt(nr,"displayName","Bar");Tt(nr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Xn.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Tt(nr,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,s=e.yAxisTicks,c=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,p=_E(n,r);if(!p)return null;var y=t.layout,v=r.type.defaultProps,d=v!==void 0?ve(ve({},v),r.props):r.props,x=d.dataKey,w=d.children,b=d.minPointSize,O=y==="horizontal"?o:a,g=c?O.scale.domain():null,m=CE({numericAxis:O}),_=Ze(w,o0),S=l.map(function(P,C){var A,j,E,N,I,R;c?A=SE(c[f+C],g):(A=ut(P,x),Array.isArray(A)||(A=[m,A]));var D=VI(b,Ex.defaultProps.minPointSize)(A[1],C);if(y==="horizontal"){var L,F=[o.scale(A[0]),o.scale(A[1])],H=F[0],K=F[1];j=fg({axis:a,ticks:u,bandSize:i,offset:p.offset,entry:P,index:C}),E=(L=K??H)!==null&&L!==void 0?L:void 0,N=p.size;var z=H-K;if(I=Number.isNaN(z)?0:z,R={x:j,y:o.y,width:N,height:o.height},Math.abs(D)>0&&Math.abs(I)<Math.abs(D)){var V=Ye(I||D)*(Math.abs(D)-Math.abs(I));E-=V,I+=V}}else{var ce=[a.scale(A[0]),a.scale(A[1])],pe=ce[0],ke=ce[1];if(j=pe,E=fg({axis:o,ticks:s,bandSize:i,offset:p.offset,entry:P,index:C}),N=ke-pe,I=p.size,R={x:a.x,y:E,width:a.width,height:I},Math.abs(D)>0&&Math.abs(N)<Math.abs(D)){var Rt=Ye(N||D)*(Math.abs(D)-Math.abs(N));N+=Rt}}return ve(ve(ve({},P),{},{x:j,y:E,width:N,height:I,value:c?A:A[1],payload:P,background:R},_&&_[C]&&_[C].props),{},{tooltipPayload:[hx(r,P)],tooltipPosition:{x:j+N/2,y:E+I/2}})});return ve({data:S,layout:y},h)});function Un(e){"@babel/helpers - typeof";return Un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Un(e)}function aN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Mm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,$x(n.key),n)}}function oN(e,t,r){return t&&Mm(e.prototype,t),r&&Mm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Cm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cm(Object(r),!0).forEach(function(n){Ba(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ba(e,t,r){return t=$x(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $x(e){var t=uN(e,"string");return Un(t)=="symbol"?t:t+""}function uN(e,t){if(Un(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Un(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var sN=function(t,r,n,i,a){var o=t.width,u=t.height,s=t.layout,c=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!qe(c,nr);return f.reduce(function(p,y){var v=r[y],d=v.orientation,x=v.domain,w=v.padding,b=w===void 0?{}:w,O=v.mirror,g=v.reversed,m="".concat(d).concat(O?"Mirror":""),_,S,P,C,A;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var j=x[1]-x[0],E=1/0,N=v.categoricalDomain.sort(s1);if(N.forEach(function(ce,pe){pe>0&&(E=Math.min((ce||0)-(N[pe-1]||0),E))}),Number.isFinite(E)){var I=E/j,R=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(_=I*R/2),v.padding==="no-gap"){var D=Yt(t.barCategoryGap,I*R),L=I*R/2;_=L-D-(L-D)/R*D}}}i==="xAxis"?S=[n.left+(b.left||0)+(_||0),n.left+n.width-(b.right||0)-(_||0)]:i==="yAxis"?S=s==="horizontal"?[n.top+n.height-(b.bottom||0),n.top+(b.top||0)]:[n.top+(b.top||0)+(_||0),n.top+n.height-(b.bottom||0)-(_||0)]:S=v.range,g&&(S=[S[1],S[0]]);var F=wE(v,a,h),H=F.scale,K=F.realScaleType;H.domain(x).range(S),OE(H);var z=ME(H,Ve(Ve({},v),{},{realScaleType:K}));i==="xAxis"?(A=d==="top"&&!O||d==="bottom"&&O,P=n.left,C=l[m]-A*v.height):i==="yAxis"&&(A=d==="left"&&!O||d==="right"&&O,P=l[m]-A*v.width,C=n.top);var V=Ve(Ve(Ve({},v),z),{},{realScaleType:K,x:P,y:C,scale:H,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return V.bandSize=Hi(V,z),!v.hide&&i==="xAxis"?l[m]+=(A?-1:1)*V.height:v.hide||(l[m]+=(A?-1:1)*V.width),Ve(Ve({},p),{},Ba({},y,V))},{})},Ix=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},cN=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return Ix({x:r,y:n},{x:i,y:a})},Nx=function(){function e(t){aN(this,e),this.scale=t}return oN(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var s=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+s}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}])}();Ba(Nx,"EPS",1e-4);var sh=function(t){var r=Object.keys(t).reduce(function(n,i){return Ve(Ve({},n),{},Ba({},i,Nx.create(t[i])))},{});return Ve(Ve({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return kI(i,function(s,c){return r[c].apply(s,{bandAware:o,position:u})})},isInRange:function(i){return jx(i,function(a,o){return r[o].isInRange(a)})}})};function lN(e){return(e%180+180)%180}var fN=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=lN(i),o=a*Math.PI/180,u=Math.atan(n/r),s=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(s)},Bc,$m;function hN(){if($m)return Bc;$m=1;var e=Ct(),t=Vn(),r=_a();function n(i){return function(a,o,u){var s=Object(a);if(!t(a)){var c=e(o,3);a=r(a),o=function(l){return c(s[l],l,s)}}var f=i(a,o,u);return f>-1?s[c?a[f]:f]:void 0}}return Bc=n,Bc}var Lc,Im;function dN(){if(Im)return Lc;Im=1;var e=Sx();function t(r){var n=e(r),i=n%1;return n===n?i?n-i:n:0}return Lc=t,Lc}var Fc,Nm;function pN(){if(Nm)return Fc;Nm=1;var e=Gb(),t=Ct(),r=dN(),n=Math.max;function i(a,o,u){var s=a==null?0:a.length;if(!s)return-1;var c=u==null?0:r(u);return c<0&&(c=n(s+c,0)),e(a,t(o,3),c)}return Fc=i,Fc}var Uc,Rm;function vN(){if(Rm)return Uc;Rm=1;var e=hN(),t=pN(),r=e(t);return Uc=r,Uc}var yN=vN();const gN=se(yN);var mN=sb();const bN=se(mN);var xN=bN(function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}},function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")}),ch=q.createContext(void 0),lh=q.createContext(void 0),Rx=q.createContext(void 0),kx=q.createContext({}),Dx=q.createContext(void 0),qx=q.createContext(0),Bx=q.createContext(0),km=function(t){var r=t.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=t.clipPathId,u=t.children,s=t.width,c=t.height,f=xN(a);return T.createElement(ch.Provider,{value:n},T.createElement(lh.Provider,{value:i},T.createElement(kx.Provider,{value:a},T.createElement(Rx.Provider,{value:f},T.createElement(Dx.Provider,{value:o},T.createElement(qx.Provider,{value:c},T.createElement(Bx.Provider,{value:s},u)))))))},wN=function(){return q.useContext(Dx)},Lx=function(t){var r=q.useContext(ch);r==null&&Vt(!1);var n=r[t];return n==null&&Vt(!1),n},ON=function(){var t=q.useContext(ch);return At(t)},_N=function(){var t=q.useContext(lh),r=gN(t,function(n){return jx(n.domain,Number.isFinite)});return r||At(t)},Fx=function(t){var r=q.useContext(lh);r==null&&Vt(!1);var n=r[t];return n==null&&Vt(!1),n},SN=function(){var t=q.useContext(Rx);return t},AN=function(){return q.useContext(kx)},fh=function(){return q.useContext(Bx)},hh=function(){return q.useContext(qx)};function Cr(e){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cr(e)}function PN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function TN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,zx(n.key),n)}}function jN(e,t,r){return t&&TN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function EN(e,t,r){return t=aa(t),MN(e,Ux()?Reflect.construct(t,r||[],aa(e).constructor):t.apply(e,r))}function MN(e,t){if(t&&(Cr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return CN(e)}function CN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ux(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ux=function(){return!!e})()}function aa(e){return aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},aa(e)}function $N(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Kl(e,t)}function Kl(e,t){return Kl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Kl(e,t)}function Dm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function qm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Dm(Object(r),!0).forEach(function(n){dh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Dm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function dh(e,t,r){return t=zx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function zx(e){var t=IN(e,"string");return Cr(t)=="symbol"?t:t+""}function IN(e,t){if(Cr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function NN(e,t){return qN(e)||DN(e,t)||kN(e,t)||RN()}function RN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kN(e,t){if(e){if(typeof e=="string")return Bm(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bm(e,t)}}function Bm(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function DN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function qN(e){if(Array.isArray(e))return e}function Vl(){return Vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Vl.apply(this,arguments)}var BN=function(t,r){var n;return T.isValidElement(t)?n=T.cloneElement(t,r):Z(t)?n=t(r):n=T.createElement("line",Vl({},r,{className:"recharts-reference-line-line"})),n},LN=function(t,r,n,i,a,o,u,s,c){var f=a.x,l=a.y,h=a.width,p=a.height;if(n){var y=c.y,v=t.y.apply(y,{position:o});if(at(c,"discard")&&!t.y.isInRange(v))return null;var d=[{x:f+h,y:v},{x:f,y:v}];return s==="left"?d.reverse():d}if(r){var x=c.x,w=t.x.apply(x,{position:o});if(at(c,"discard")&&!t.x.isInRange(w))return null;var b=[{x:w,y:l+p},{x:w,y:l}];return u==="top"?b.reverse():b}if(i){var O=c.segment,g=O.map(function(m){return t.apply(m,{position:o})});return at(c,"discard")&&$I(g,function(m){return!t.isInRange(m)})?null:g}return null};function FN(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxisId,a=e.yAxisId,o=e.shape,u=e.className,s=e.alwaysShow,c=wN(),f=Lx(i),l=Fx(a),h=SN();if(!c||!h)return null;pt(s===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var p=sh({x:f.scale,y:l.scale}),y=xe(t),v=xe(r),d=n&&n.length===2,x=LN(p,y,v,d,h,e.position,f.orientation,l.orientation,e);if(!x)return null;var w=NN(x,2),b=w[0],O=b.x,g=b.y,m=w[1],_=m.x,S=m.y,P=at(e,"hidden")?"url(#".concat(c,")"):void 0,C=qm(qm({clipPath:P},ee(e,!0)),{},{x1:O,y1:g,x2:_,y2:S});return T.createElement(_e,{className:ie("recharts-reference-line",u)},BN(o,C),je.renderCallByParent(e,cN({x1:O,y1:g,x2:_,y2:S})))}var ph=function(e){function t(){return PN(this,t),EN(this,t,arguments)}return $N(t,e),jN(t,[{key:"render",value:function(){return T.createElement(FN,this.props)}}])}(T.Component);dh(ph,"displayName","ReferenceLine");dh(ph,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Xl(){return Xl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Xl.apply(this,arguments)}function $r(e){"@babel/helpers - typeof";return $r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$r(e)}function Lm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Fm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Lm(Object(r),!0).forEach(function(n){La(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Lm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function UN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function zN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hx(n.key),n)}}function WN(e,t,r){return t&&zN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function HN(e,t,r){return t=oa(t),GN(e,Wx()?Reflect.construct(t,r||[],oa(e).constructor):t.apply(e,r))}function GN(e,t){if(t&&($r(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return KN(e)}function KN(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Wx=function(){return!!e})()}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function VN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Yl(e,t)}function Yl(e,t){return Yl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yl(e,t)}function La(e,t,r){return t=Hx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Hx(e){var t=XN(e,"string");return $r(t)=="symbol"?t:t+""}function XN(e,t){if($r(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if($r(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var YN=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=sh({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return at(t,"discard")&&!o.isInRange(u)?null:u},Fa=function(e){function t(){return UN(this,t),HN(this,t,arguments)}return VN(t,e),WN(t,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,s=n.clipPathId,c=xe(i),f=xe(a);if(pt(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!f)return null;var l=YN(this.props);if(!l)return null;var h=l.x,p=l.y,y=this.props,v=y.shape,d=y.className,x=at(this.props,"hidden")?"url(#".concat(s,")"):void 0,w=Fm(Fm({clipPath:x},ee(this.props,!0)),{},{cx:h,cy:p});return T.createElement(_e,{className:ie("recharts-reference-dot",d)},t.renderDot(v,w),je.renderCallByParent(this.props,{x:h-o,y:p-o,width:2*o,height:2*o}))}}])}(T.Component);La(Fa,"displayName","ReferenceDot");La(Fa,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});La(Fa,"renderDot",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Z(e)?r=e(t):r=T.createElement(_x,Xl({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r});function Zl(){return Zl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zl.apply(this,arguments)}function Ir(e){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ir(e)}function Um(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function zm(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Um(Object(r),!0).forEach(function(n){Ua(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Um(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function JN(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kx(n.key),n)}}function QN(e,t,r){return t&&JN(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eR(e,t,r){return t=ua(t),tR(e,Gx()?Reflect.construct(t,r||[],ua(e).constructor):t.apply(e,r))}function tR(e,t){if(t&&(Ir(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rR(e)}function rR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Gx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Gx=function(){return!!e})()}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function nR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jl(e,t)}function Jl(e,t){return Jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Jl(e,t)}function Ua(e,t,r){return t=Kx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kx(e){var t=iR(e,"string");return Ir(t)=="symbol"?t:t+""}function iR(e,t){if(Ir(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var aR=function(t,r,n,i,a){var o=a.x1,u=a.x2,s=a.y1,c=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=sh({x:f.scale,y:l.scale}),p={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(s,{position:"start"}):h.y.rangeMin},y={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(c,{position:"end"}):h.y.rangeMax};return at(a,"discard")&&(!h.isInRange(p)||!h.isInRange(y))?null:Ix(p,y)},za=function(e){function t(){return ZN(this,t),eR(this,t,arguments)}return nR(t,e),QN(t,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,s=n.className,c=n.alwaysShow,f=n.clipPathId;pt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=xe(i),h=xe(a),p=xe(o),y=xe(u),v=this.props.shape;if(!l&&!h&&!p&&!y&&!v)return null;var d=aR(l,h,p,y,this.props);if(!d&&!v)return null;var x=at(this.props,"hidden")?"url(#".concat(f,")"):void 0;return T.createElement(_e,{className:ie("recharts-reference-area",s)},t.renderRect(v,zm(zm({clipPath:x},ee(this.props,!0)),d)),je.renderCallByParent(this.props,d))}}])}(T.Component);Ua(za,"displayName","ReferenceArea");Ua(za,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});Ua(za,"renderRect",function(e,t){var r;return T.isValidElement(e)?r=T.cloneElement(e,t):Z(e)?r=e(t):r=T.createElement(uh,Zl({},t,{className:"recharts-reference-area-rect"})),r});function Vx(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)n.push(e[i]);return n}function oR(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return fN(n,r)}function uR(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function sa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function sR(e,t){return Vx(e,t+1)}function cR(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,s=0,c=1,f=o,l=function(){var y=n?.[s];if(y===void 0)return{v:Vx(n,c)};var v=s,d,x=function(){return d===void 0&&(d=r(y,v)),d},w=y.coordinate,b=s===0||sa(e,w,x,f,u);b||(s=0,f=o,c+=1),b&&(f=w+e*(x()/2+i),s+=c)},h;c<=a.length;)if(h=l(),h)return h.v;return[]}function zn(e){"@babel/helpers - typeof";return zn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zn(e)}function Wm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wm(Object(r),!0).forEach(function(n){lR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function lR(e,t,r){return t=fR(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function fR(e){var t=hR(e,"string");return zn(t)=="symbol"?t:t+""}function hR(e,t){if(zn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(zn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function dR(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,s=t.end,c=function(h){var p=a[h],y,v=function(){return y===void 0&&(y=r(p,h)),y};if(h===o-1){var d=e*(p.coordinate+e*v()/2-s);a[h]=p=Te(Te({},p),{},{tickCoord:d>0?p.coordinate-d*e:p.coordinate})}else a[h]=p=Te(Te({},p),{},{tickCoord:p.coordinate});var x=sa(e,p.tickCoord,v,u,s);x&&(s=p.tickCoord-e*(v()/2+i),a[h]=Te(Te({},p),{},{isShow:!0}))},f=o-1;f>=0;f--)c(f);return a}function pR(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,s=t.start,c=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-c);o[u-1]=f=Te(Te({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var p=sa(e,f.tickCoord,function(){return l},s,c);p&&(c=f.tickCoord-e*(l/2+i),o[u-1]=Te(Te({},f),{},{isShow:!0}))}for(var y=a?u-1:u,v=function(w){var b=o[w],O,g=function(){return O===void 0&&(O=r(b,w)),O};if(w===0){var m=e*(b.coordinate-e*g()/2-s);o[w]=b=Te(Te({},b),{},{tickCoord:m<0?b.coordinate-m*e:b.coordinate})}else o[w]=b=Te(Te({},b),{},{tickCoord:b.coordinate});var _=sa(e,b.tickCoord,g,s,c);_&&(s=b.tickCoord+e*(g()/2+i),o[w]=Te(Te({},b),{},{isShow:!0}))},d=0;d<y;d++)v(d);return o}function vh(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,s=e.interval,c=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(B(s)||Xn.isSsr)return sR(i,typeof s=="number"&&B(s)?s:0);var h=[],p=u==="top"||u==="bottom"?"width":"height",y=f&&p==="width"?un(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(b,O){var g=Z(c)?c(b.value,O):b.value;return p==="width"?oR(un(g,{fontSize:t,letterSpacing:r}),y,l):un(g,{fontSize:t,letterSpacing:r})[p]},d=i.length>=2?Ye(i[1].coordinate-i[0].coordinate):1,x=uR(a,d,p);return s==="equidistantPreserveStart"?cR(d,x,v,i,o):(s==="preserveStart"||s==="preserveStartEnd"?h=pR(d,x,v,i,o,s==="preserveStartEnd"):h=dR(d,x,v,i,o),h.filter(function(w){return w.isShow}))}var vR=["viewBox"],yR=["viewBox"],gR=["ticks"];function Nr(e){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nr(e)}function fr(){return fr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fr.apply(this,arguments)}function Hm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hm(Object(r),!0).forEach(function(n){yh(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zc(e,t){if(e==null)return{};var r=mR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function mR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function bR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Gm(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Yx(n.key),n)}}function xR(e,t,r){return t&&Gm(e.prototype,t),r&&Gm(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function wR(e,t,r){return t=ca(t),OR(e,Xx()?Reflect.construct(t,r||[],ca(e).constructor):t.apply(e,r))}function OR(e,t){if(t&&(Nr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _R(e)}function _R(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Xx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Xx=function(){return!!e})()}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function SR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ql(e,t)}function Ql(e,t){return Ql=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ql(e,t)}function yh(e,t,r){return t=Yx(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Yx(e){var t=AR(e,"string");return Nr(t)=="symbol"?t:t+""}function AR(e,t){if(Nr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var Hr=function(e){function t(r){var n;return bR(this,t),n=wR(this,t,[r]),n.state={fontSize:"",letterSpacing:""},n}return SR(t,e),xR(t,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=zc(n,vR),u=this.props,s=u.viewBox,c=zc(u,yR);return!pr(a,s)||!pr(o,c)||!pr(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,s=i.height,c=i.orientation,f=i.tickSize,l=i.mirror,h=i.tickMargin,p,y,v,d,x,w,b=l?-1:1,O=n.tickSize||f,g=B(n.tickCoord)?n.tickCoord:n.coordinate;switch(c){case"top":p=y=n.coordinate,d=o+ +!l*s,v=d-b*O,w=v-b*h,x=g;break;case"left":v=d=n.coordinate,y=a+ +!l*u,p=y-b*O,x=p-b*h,w=g;break;case"right":v=d=n.coordinate,y=a+ +l*u,p=y+b*O,x=p+b*h,w=g;break;default:p=y=n.coordinate,d=o+ +l*s,v=d+b*O,w=v+b*h,x=g;break}return{line:{x1:p,y1:v,x2:y,y2:d},tick:{x,y:w}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,s=n.orientation,c=n.mirror,f=n.axisLine,l=Me(Me(Me({},ee(this.props,!1)),ee(f,!1)),{},{fill:"none"});if(s==="top"||s==="bottom"){var h=+(s==="top"&&!c||s==="bottom"&&c);l=Me(Me({},l),{},{x1:i,y1:a+h*u,x2:i+o,y2:a+h*u})}else{var p=+(s==="left"&&!c||s==="right"&&c);l=Me(Me({},l),{},{x1:i+p*o,y1:a,x2:i+p*o,y2:a+u})}return T.createElement("line",fr({},l,{className:ie("recharts-cartesian-axis-line",ze(f,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,s=u.tickLine,c=u.stroke,f=u.tick,l=u.tickFormatter,h=u.unit,p=vh(Me(Me({},this.props),{},{ticks:n}),i,a),y=this.getTickTextAnchor(),v=this.getTickVerticalAnchor(),d=ee(this.props,!1),x=ee(f,!1),w=Me(Me({},d),{},{fill:"none"},ee(s,!1)),b=p.map(function(O,g){var m=o.getTickLineCoord(O),_=m.line,S=m.tick,P=Me(Me(Me(Me({textAnchor:y,verticalAnchor:v},d),{},{stroke:"none",fill:c},x),S),{},{index:g,payload:O,visibleTicksCount:p.length,tickFormatter:l});return T.createElement(_e,fr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(O.value,"-").concat(O.coordinate,"-").concat(O.tickCoord)},xi(o.props,O,g)),s&&T.createElement("line",fr({},w,_,{className:ie("recharts-cartesian-axis-tick-line",ze(s,"className"))})),f&&t.renderTickItem(f,P,"".concat(Z(l)?l(O.value,g):O.value).concat(h||"")))});return T.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,s=i.ticksGenerator,c=i.className,f=i.hide;if(f)return null;var l=this.props,h=l.ticks,p=zc(l,gR),y=h;return Z(s)&&(y=h&&h.length>0?s(this.props):s(p)),o<=0||u<=0||!y||!y.length?null:T.createElement(_e,{className:ie("recharts-cartesian-axis",c),ref:function(d){n.layerReference=d}},a&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),je.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return T.isValidElement(n)?o=T.cloneElement(n,i):Z(n)?o=n(i):o=T.createElement(Ci,fr({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(q.Component);yh(Hr,"displayName","CartesianAxis");yh(Hr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var PR=["x1","y1","x2","y2","key"],TR=["offset"];function Jt(e){"@babel/helpers - typeof";return Jt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jt(e)}function Km(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ee(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Km(Object(r),!0).forEach(function(n){jR(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Km(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function jR(e,t,r){return t=ER(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ER(e){var t=MR(e,"string");return Jt(t)=="symbol"?t:t+""}function MR(e,t){if(Jt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Jt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ht(){return Ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ht.apply(this,arguments)}function Vm(e,t){if(e==null)return{};var r=CR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function CR(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}var $R=function(t){var r=t.fill;if(!r||r==="none")return null;var n=t.fillOpacity,i=t.x,a=t.y,o=t.width,u=t.height,s=t.ry;return T.createElement("rect",{x:i,y:a,ry:s,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Zx(e,t){var r;if(T.isValidElement(e))r=T.cloneElement(e,t);else if(Z(e))r=e(t);else{var n=t.x1,i=t.y1,a=t.x2,o=t.y2,u=t.key,s=Vm(t,PR),c=ee(s,!1);c.offset;var f=Vm(c,TR);r=T.createElement("line",Ht({},f,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function IR(e){var t=e.x,r=e.width,n=e.horizontal,i=n===void 0?!0:n,a=e.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=Ee(Ee({},e),{},{x1:t,y1:u,x2:t+r,y2:u,key:"line-".concat(s),index:s});return Zx(i,c)});return T.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function NR(e){var t=e.y,r=e.height,n=e.vertical,i=n===void 0?!0:n,a=e.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,s){var c=Ee(Ee({},e),{},{x1:u,y1:t,x2:u,y2:t+r,key:"line-".concat(s),index:s});return Zx(i,c)});return T.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function RR(e){var t=e.horizontalFill,r=e.fillOpacity,n=e.x,i=e.y,a=e.width,o=e.height,u=e.horizontalPoints,s=e.horizontal,c=s===void 0?!0:s;if(!c||!t||!t.length)return null;var f=u.map(function(h){return Math.round(h+i-i)}).sort(function(h,p){return h-p});i!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?i+o-h:f[p+1]-h;if(v<=0)return null;var d=p%t.length;return T.createElement("rect",{key:"react-".concat(p),y:h,x:n,height:v,width:a,stroke:"none",fill:t[d],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return T.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},l)}function kR(e){var t=e.vertical,r=t===void 0?!0:t,n=e.verticalFill,i=e.fillOpacity,a=e.x,o=e.y,u=e.width,s=e.height,c=e.verticalPoints;if(!r||!n||!n.length)return null;var f=c.map(function(h){return Math.round(h+a-a)}).sort(function(h,p){return h-p});a!==f[0]&&f.unshift(0);var l=f.map(function(h,p){var y=!f[p+1],v=y?a+u-h:f[p+1]-h;if(v<=0)return null;var d=p%n.length;return T.createElement("rect",{key:"react-".concat(p),x:h,y:o,width:v,height:s,stroke:"none",fill:n[d],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return T.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},l)}var DR=function(t,r){var n=t.xAxis,i=t.width,a=t.height,o=t.offset;return lx(vh(Ee(Ee(Ee({},Hr.defaultProps),n),{},{ticks:ht(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},qR=function(t,r){var n=t.yAxis,i=t.width,a=t.height,o=t.offset;return lx(vh(Ee(Ee(Ee({},Hr.defaultProps),n),{},{ticks:ht(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},cr={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Jx(e){var t,r,n,i,a,o,u=fh(),s=hh(),c=AN(),f=Ee(Ee({},e),{},{stroke:(t=e.stroke)!==null&&t!==void 0?t:cr.stroke,fill:(r=e.fill)!==null&&r!==void 0?r:cr.fill,horizontal:(n=e.horizontal)!==null&&n!==void 0?n:cr.horizontal,horizontalFill:(i=e.horizontalFill)!==null&&i!==void 0?i:cr.horizontalFill,vertical:(a=e.vertical)!==null&&a!==void 0?a:cr.vertical,verticalFill:(o=e.verticalFill)!==null&&o!==void 0?o:cr.verticalFill,x:B(e.x)?e.x:c.left,y:B(e.y)?e.y:c.top,width:B(e.width)?e.width:c.width,height:B(e.height)?e.height:c.height}),l=f.x,h=f.y,p=f.width,y=f.height,v=f.syncWithTicks,d=f.horizontalValues,x=f.verticalValues,w=ON(),b=_N();if(!B(p)||p<=0||!B(y)||y<=0||!B(l)||l!==+l||!B(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||DR,g=f.horizontalCoordinatesGenerator||qR,m=f.horizontalPoints,_=f.verticalPoints;if((!m||!m.length)&&Z(g)){var S=d&&d.length,P=g({yAxis:b?Ee(Ee({},b),{},{ticks:S?d:b.ticks}):void 0,width:u,height:s,offset:c},S?!0:v);pt(Array.isArray(P),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(Jt(P),"]")),Array.isArray(P)&&(m=P)}if((!_||!_.length)&&Z(O)){var C=x&&x.length,A=O({xAxis:w?Ee(Ee({},w),{},{ticks:C?x:w.ticks}):void 0,width:u,height:s,offset:c},C?!0:v);pt(Array.isArray(A),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(Jt(A),"]")),Array.isArray(A)&&(_=A)}return T.createElement("g",{className:"recharts-cartesian-grid"},T.createElement($R,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),T.createElement(IR,Ht({},f,{offset:c,horizontalPoints:m,xAxis:w,yAxis:b})),T.createElement(NR,Ht({},f,{offset:c,verticalPoints:_,xAxis:w,yAxis:b})),T.createElement(RR,Ht({},f,{horizontalPoints:m})),T.createElement(kR,Ht({},f,{verticalPoints:_})))}Jx.displayName="CartesianGrid";function Rr(e){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rr(e)}function BR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function LR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tw(n.key),n)}}function FR(e,t,r){return t&&LR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function UR(e,t,r){return t=la(t),zR(e,Qx()?Reflect.construct(t,r||[],la(e).constructor):t.apply(e,r))}function zR(e,t){if(t&&(Rr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return WR(e)}function WR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Qx(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Qx=function(){return!!e})()}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},la(e)}function HR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ef(e,t)}function ef(e,t){return ef=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ef(e,t)}function ew(e,t,r){return t=tw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tw(e){var t=GR(e,"string");return Rr(t)=="symbol"?t:t+""}function GR(e,t){if(Rr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Rr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function tf(){return tf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},tf.apply(this,arguments)}function KR(e){var t=e.xAxisId,r=fh(),n=hh(),i=Lx(t);return i==null?null:T.createElement(Hr,tf({},i,{className:ie("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return ht(o,!0)}}))}var Wa=function(e){function t(){return BR(this,t),UR(this,t,arguments)}return HR(t,e),FR(t,[{key:"render",value:function(){return T.createElement(KR,this.props)}}])}(T.Component);ew(Wa,"displayName","XAxis");ew(Wa,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function kr(e){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},kr(e)}function VR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function XR(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,iw(n.key),n)}}function YR(e,t,r){return t&&XR(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ZR(e,t,r){return t=fa(t),JR(e,rw()?Reflect.construct(t,r||[],fa(e).constructor):t.apply(e,r))}function JR(e,t){if(t&&(kr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return QR(e)}function QR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rw=function(){return!!e})()}function fa(e){return fa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},fa(e)}function ek(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rf(e,t)}function rf(e,t){return rf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},rf(e,t)}function nw(e,t,r){return t=iw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function iw(e){var t=tk(e,"string");return kr(t)=="symbol"?t:t+""}function tk(e,t){if(kr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}function nf(){return nf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nf.apply(this,arguments)}var rk=function(t){var r=t.yAxisId,n=fh(),i=hh(),a=Fx(r);return a==null?null:T.createElement(Hr,nf({},a,{className:ie("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return ht(u,!0)}}))},Ha=function(e){function t(){return VR(this,t),ZR(this,t,arguments)}return ek(t,e),YR(t,[{key:"render",value:function(){return T.createElement(rk,this.props)}}])}(T.Component);nw(Ha,"displayName","YAxis");nw(Ha,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Xm(e){return ok(e)||ak(e)||ik(e)||nk()}function nk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ik(e,t){if(e){if(typeof e=="string")return af(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return af(e,t)}}function ak(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ok(e){if(Array.isArray(e))return af(e)}function af(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var of=function(t,r,n,i,a){var o=Ze(t,ph),u=Ze(t,Fa),s=[].concat(Xm(o),Xm(u)),c=Ze(t,za),f="".concat(i,"Id"),l=i[0],h=r;if(s.length&&(h=s.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&B(d.props[l])){var x=d.props[l];return[Math.min(v[0],x),Math.max(v[1],x)]}return v},h)),c.length){var p="".concat(l,"1"),y="".concat(l,"2");h=c.reduce(function(v,d){if(d.props[f]===n&&at(d.props,"extendDomain")&&B(d.props[p])&&B(d.props[y])){var x=d.props[p],w=d.props[y];return[Math.min(v[0],x,w),Math.max(v[1],x,w)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,d){return B(d)?[Math.min(v[0],d),Math.max(v[1],d)]:v},h)),h},Wc={exports:{}},Ym;function uk(){return Ym||(Ym=1,function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(s,c,f){this.fn=s,this.context=c,this.once=f||!1}function a(s,c,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var p=new i(f,l||s,h),y=r?r+c:c;return s._events[y]?s._events[y].fn?s._events[y]=[s._events[y],p]:s._events[y].push(p):(s._events[y]=p,s._eventsCount++),s}function o(s,c){--s._eventsCount===0?s._events=new n:delete s._events[c]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var c=[],f,l;if(this._eventsCount===0)return c;for(l in f=this._events)t.call(f,l)&&c.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(f)):c},u.prototype.listeners=function(c){var f=r?r+c:c,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,p=l.length,y=new Array(p);h<p;h++)y[h]=l[h].fn;return y},u.prototype.listenerCount=function(c){var f=r?r+c:c,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(c,f,l,h,p,y){var v=r?r+c:c;if(!this._events[v])return!1;var d=this._events[v],x=arguments.length,w,b;if(d.fn){switch(d.once&&this.removeListener(c,d.fn,void 0,!0),x){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,f),!0;case 3:return d.fn.call(d.context,f,l),!0;case 4:return d.fn.call(d.context,f,l,h),!0;case 5:return d.fn.call(d.context,f,l,h,p),!0;case 6:return d.fn.call(d.context,f,l,h,p,y),!0}for(b=1,w=new Array(x-1);b<x;b++)w[b-1]=arguments[b];d.fn.apply(d.context,w)}else{var O=d.length,g;for(b=0;b<O;b++)switch(d[b].once&&this.removeListener(c,d[b].fn,void 0,!0),x){case 1:d[b].fn.call(d[b].context);break;case 2:d[b].fn.call(d[b].context,f);break;case 3:d[b].fn.call(d[b].context,f,l);break;case 4:d[b].fn.call(d[b].context,f,l,h);break;default:if(!w)for(g=1,w=new Array(x-1);g<x;g++)w[g-1]=arguments[g];d[b].fn.apply(d[b].context,w)}}return!0},u.prototype.on=function(c,f,l){return a(this,c,f,l,!1)},u.prototype.once=function(c,f,l){return a(this,c,f,l,!0)},u.prototype.removeListener=function(c,f,l,h){var p=r?r+c:c;if(!this._events[p])return this;if(!f)return o(this,p),this;var y=this._events[p];if(y.fn)y.fn===f&&(!h||y.once)&&(!l||y.context===l)&&o(this,p);else{for(var v=0,d=[],x=y.length;v<x;v++)(y[v].fn!==f||h&&!y[v].once||l&&y[v].context!==l)&&d.push(y[v]);d.length?this._events[p]=d.length===1?d[0]:d:o(this,p)}return this},u.prototype.removeAllListeners=function(c){var f;return c?(f=r?r+c:c,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u}(Wc)),Wc.exports}var sk=uk();const ck=se(sk);var Hc=new ck,Gc="recharts.syncMouseEvents";function Wn(e){"@babel/helpers - typeof";return Wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wn(e)}function lk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fk(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,aw(n.key),n)}}function hk(e,t,r){return t&&fk(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Kc(e,t,r){return t=aw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function aw(e){var t=dk(e,"string");return Wn(t)=="symbol"?t:t+""}function dk(e,t){if(Wn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Wn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var pk=function(){function e(){lk(this,e),Kc(this,"activeIndex",0),Kc(this,"coordinateList",[]),Kc(this,"layout","horizontal")}return hk(e,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,s=r.layout,c=s===void 0?null:s,f=r.offset,l=f===void 0?null:f,h=r.mouseHandlerCallback,p=h===void 0?null:h;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=c??this.layout,this.offset=l??this.offset,this.mouseHandlerCallback=p??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,s=this.coordinateList[this.activeIndex].coordinate,c=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+s+c,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}])}();function vk(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e?.[0],i=e?.[1];if(n&&i&&B(n)&&B(i))return!0}return!1}function yk(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}function ow(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Pe(t,r,n,i),u=Pe(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function gk(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,s=t.cy,c=t.innerRadius,f=t.outerRadius,l=t.angle,h=Pe(u,s,c,l),p=Pe(u,s,f,l);n=h.x,i=h.y,a=p.x,o=p.y}else return ow(t);return[{x:n,y:i},{x:a,y:o}]}function Hn(e){"@babel/helpers - typeof";return Hn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hn(e)}function Zm(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function gi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zm(Object(r),!0).forEach(function(n){mk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zm(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mk(e,t,r){return t=bk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bk(e){var t=xk(e,"string");return Hn(t)=="symbol"?t:t+""}function xk(e,t){if(Hn(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Hn(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function wk(e){var t,r,n=e.element,i=e.tooltipEventType,a=e.isActive,o=e.activeCoordinate,u=e.activePayload,s=e.offset,c=e.activeTooltipIndex,f=e.tooltipAxisBandSize,l=e.layout,h=e.chartName,p=(t=n.props.cursor)!==null&&t!==void 0?t:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!p||!a||!o||h!=="ScatterChart"&&i!=="axis")return null;var y,v=jg;if(h==="ScatterChart")y=o,v=$$;else if(h==="BarChart")y=yk(l,o,s,f),v=uh;else if(l==="radial"){var d=ow(o),x=d.cx,w=d.cy,b=d.radius,O=d.startAngle,g=d.endAngle;y={cx:x,cy:w,startAngle:O,endAngle:g,innerRadius:b,outerRadius:b},v=vx}else y={points:gk(l,o,s)},v=jg;var m=gi(gi(gi(gi({stroke:"#ccc",pointerEvents:"none"},s),y),ee(p,!1)),{},{payload:u,payloadIndex:c,className:ie("recharts-tooltip-cursor",p.className)});return q.isValidElement(p)?q.cloneElement(p,m):q.createElement(v,m)}var Ok=["item"],_k=["children","className","width","height","style","compact","title","desc"];function Dr(e){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dr(e)}function hr(){return hr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},hr.apply(this,arguments)}function Jm(e,t){return Pk(e)||Ak(e,t)||sw(e,t)||Sk()}function Sk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ak(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],s=!0,c=!1;try{if(a=(r=r.call(e)).next,t!==0)for(;!(s=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);s=!0);}catch(f){c=!0,i=f}finally{try{if(!s&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return u}}function Pk(e){if(Array.isArray(e))return e}function Qm(e,t){if(e==null)return{};var r=Tk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function Tk(e,t){if(e==null)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}function jk(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ek(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cw(n.key),n)}}function Mk(e,t,r){return t&&Ek(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ck(e,t,r){return t=ha(t),$k(e,uw()?Reflect.construct(t,r||[],ha(e).constructor):t.apply(e,r))}function $k(e,t){if(t&&(Dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ik(e)}function Ik(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function uw(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(uw=function(){return!!e})()}function ha(e){return ha=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ha(e)}function Nk(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&uf(e,t)}function uf(e,t){return uf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},uf(e,t)}function qr(e){return Dk(e)||kk(e)||sw(e)||Rk()}function Rk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sw(e,t){if(e){if(typeof e=="string")return sf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sf(e,t)}}function kk(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Dk(e){if(Array.isArray(e))return sf(e)}function sf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function eb(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function $(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eb(Object(r),!0).forEach(function(n){G(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eb(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function G(e,t,r){return t=cw(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cw(e){var t=qk(e,"string");return Dr(t)=="symbol"?t:t+""}function qk(e,t){if(Dr(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t);if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Bk={xAxis:["bottom","top"],yAxis:["left","right"]},Lk={width:"100%",height:"100%"},lw={x:0,y:0};function mi(e){return e}var Fk=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},Uk=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return $($($({},i),Pe(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var s=a.coordinate,c=i.angle;return $($($({},i),Pe(i.cx,i.cy,s,c)),{},{angle:c,radius:s})}return lw},Ga=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,s){var c=s.props.data;return c&&c.length?[].concat(qr(u),qr(c)):u},[]);return o.length>0?o:t&&t.length&&B(i)&&B(a)?t.slice(i,a+1):[]};function fw(e){return e==="number"?[0,"auto"]:void 0}var cf=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=Ga(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(s,c){var f,l=(f=c.props.data)!==null&&f!==void 0?f:r;l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=n&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1));var h;if(o.dataKey&&!o.allowDuplicatedCategory){var p=l===void 0?u:l;h=Vc(p,o.dataKey,i)}else h=l&&l[n]||u[n];return h?[].concat(qr(s),[hx(c,h)]):s},[])},tb=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=Fk(a,n),u=t.orderedTooltipTicks,s=t.tooltipAxis,c=t.tooltipTicks,f=vE(o,u,c,s);if(f>=0&&c){var l=c[f]&&c[f].value,h=cf(t,r,f,l),p=Uk(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:p}}return null},zk=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,p=cx(f,a);return n.reduce(function(y,v){var d,x=v.type.defaultProps!==void 0?$($({},v.type.defaultProps),v.props):v.props,w=x.type,b=x.dataKey,O=x.allowDataOverflow,g=x.allowDuplicatedCategory,m=x.scale,_=x.ticks,S=x.includeHidden,P=x[o];if(y[P])return y;var C=Ga(t.data,{graphicalItems:i.filter(function(z){var V,ce=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o];return ce===P}),dataStartIndex:s,dataEndIndex:c}),A=C.length,j,E,N;vk(x.domain,O,w)&&(j=jl(x.domain,null,O),p&&(w==="number"||m!=="auto")&&(N=cn(C,b,"category")));var I=fw(w);if(!j||j.length===0){var R,D=(R=x.domain)!==null&&R!==void 0?R:I;if(b){if(j=cn(C,b,w),w==="category"&&p){var L=u1(j);g&&L?(E=j,j=ea(0,A)):g||(j=pg(D,j,v).reduce(function(z,V){return z.indexOf(V)>=0?z:[].concat(qr(z),[V])},[]))}else if(w==="category")g?j=j.filter(function(z){return z!==""&&!ne(z)}):j=pg(D,j,v).reduce(function(z,V){return z.indexOf(V)>=0||V===""||ne(V)?z:[].concat(qr(z),[V])},[]);else if(w==="number"){var F=xE(C,i.filter(function(z){var V,ce,pe=o in z.props?z.props[o]:(V=z.type.defaultProps)===null||V===void 0?void 0:V[o],ke="hide"in z.props?z.props.hide:(ce=z.type.defaultProps)===null||ce===void 0?void 0:ce.hide;return pe===P&&(S||!ke)}),b,a,f);F&&(j=F)}p&&(w==="number"||m!=="auto")&&(N=cn(C,b,"category"))}else p?j=ea(0,A):u&&u[P]&&u[P].hasStack&&w==="number"?j=h==="expand"?[0,1]:fx(u[P].stackGroups,s,c):j=sx(C,i.filter(function(z){var V=o in z.props?z.props[o]:z.type.defaultProps[o],ce="hide"in z.props?z.props.hide:z.type.defaultProps.hide;return V===P&&(S||!ce)}),w,f,!0);if(w==="number")j=of(l,j,P,a,_),D&&(j=jl(D,j,O));else if(w==="category"&&D){var H=D,K=j.every(function(z){return H.indexOf(z)>=0});K&&(j=H)}}return $($({},y),{},G({},P,$($({},x),{},{axisType:a,domain:j,categoricalDomain:N,duplicateDomain:E,originalDomain:(d=x.domain)!==null&&d!==void 0?d:I,isCategorical:p,layout:f})))},{})},Wk=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.layout,l=t.children,h=Ga(t.data,{graphicalItems:n,dataStartIndex:s,dataEndIndex:c}),p=h.length,y=cx(f,a),v=-1;return n.reduce(function(d,x){var w=x.type.defaultProps!==void 0?$($({},x.type.defaultProps),x.props):x.props,b=w[o],O=fw("number");if(!d[b]){v++;var g;return y?g=ea(0,p):u&&u[b]&&u[b].hasStack?(g=fx(u[b].stackGroups,s,c),g=of(l,g,b,a)):(g=jl(O,sx(h,n.filter(function(m){var _,S,P=o in m.props?m.props[o]:(_=m.type.defaultProps)===null||_===void 0?void 0:_[o],C="hide"in m.props?m.props.hide:(S=m.type.defaultProps)===null||S===void 0?void 0:S.hide;return P===b&&!C}),"number",f),i.defaultProps.allowDataOverflow),g=of(l,g,b,a)),$($({},d),{},G({},b,$($({axisType:a},i.defaultProps),{},{hide:!0,orientation:ze(Bk,"".concat(a,".").concat(v%2),null),domain:g,originalDomain:O,isCategorical:y,layout:f})))}return d},{})},Hk=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,s=r.dataStartIndex,c=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=Ze(f,a),p={};return h&&h.length?p=zk(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:s,dataEndIndex:c}):o&&o.length&&(p=Wk(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:s,dataEndIndex:c})),p},Gk=function(t){var r=At(t),n=ht(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:If(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Hi(r,n)}},rb=function(t){var r=t.children,n=t.defaultShowTooltip,i=qe(r,Er),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},Kk=function(t){return!t||!t.length?!1:t.some(function(r){var n=dt(r&&r.type);return n&&n.indexOf("Bar")>=0})},nb=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Vk=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,s=u===void 0?{}:u,c=n.width,f=n.height,l=n.children,h=n.margin||{},p=qe(l,Er),y=qe(l,vr),v=Object.keys(s).reduce(function(g,m){var _=s[m],S=_.orientation;return!_.mirror&&!_.hide?$($({},g),{},G({},S,g[S]+_.width)):g},{left:h.left||0,right:h.right||0}),d=Object.keys(o).reduce(function(g,m){var _=o[m],S=_.orientation;return!_.mirror&&!_.hide?$($({},g),{},G({},S,ze(g,"".concat(S))+_.height)):g},{top:h.top||0,bottom:h.bottom||0}),x=$($({},d),v),w=x.bottom;p&&(x.bottom+=p.props.height||Er.defaultProps.height),y&&r&&(x=mE(x,i,n,r));var b=c-x.left-x.right,O=f-x.top-x.bottom;return $($({brushBottom:w},x),{},{width:Math.max(b,0),height:Math.max(O,0)})},Xk=function(t,r){if(r==="xAxis")return t[r].width;if(r==="yAxis")return t[r].height},Yk=function(t){var r=t.chartName,n=t.GraphicalChild,i=t.defaultTooltipEventType,a=i===void 0?"axis":i,o=t.validateTooltipEventTypes,u=o===void 0?["axis"]:o,s=t.axisComponents,c=t.legendContent,f=t.formatAxisMap,l=t.defaultProps,h=function(x,w){var b=w.graphicalItems,O=w.stackGroups,g=w.offset,m=w.updateId,_=w.dataStartIndex,S=w.dataEndIndex,P=x.barSize,C=x.layout,A=x.barGap,j=x.barCategoryGap,E=x.maxBarSize,N=nb(C),I=N.numericAxisName,R=N.cateAxisName,D=Kk(b),L=[];return b.forEach(function(F,H){var K=Ga(x.data,{graphicalItems:[F],dataStartIndex:_,dataEndIndex:S}),z=F.type.defaultProps!==void 0?$($({},F.type.defaultProps),F.props):F.props,V=z.dataKey,ce=z.maxBarSize,pe=z["".concat(I,"Id")],ke=z["".concat(R,"Id")],Rt={},$e=s.reduce(function(kt,Dt){var Ka=w["".concat(Dt.axisType,"Map")],gh=z["".concat(Dt.axisType,"Id")];Ka&&Ka[gh]||Dt.axisType==="zAxis"||Vt(!1);var mh=Ka[gh];return $($({},kt),{},G(G({},Dt.axisType,mh),"".concat(Dt.axisType,"Ticks"),ht(mh)))},Rt),U=$e[R],X=$e["".concat(R,"Ticks")],Y=O&&O[pe]&&O[pe].hasStack&&$E(F,O[pe].stackGroups),k=dt(F.type).indexOf("Bar")>=0,he=Hi(U,X),J=[],ge=D&&yE({barSize:P,stackGroups:O,totalSize:Xk($e,R)});if(k){var me,Ie,_t=ne(ce)?E:ce,ir=(me=(Ie=Hi(U,X,!0))!==null&&Ie!==void 0?Ie:_t)!==null&&me!==void 0?me:0;J=gE({barGap:A,barCategoryGap:j,bandSize:ir!==he?ir:he,sizeList:ge[ke],maxBarSize:_t}),ir!==he&&(J=J.map(function(kt){return $($({},kt),{},{position:$($({},kt.position),{},{offset:kt.position.offset-ir/2})})}))}var ei=F&&F.type&&F.type.getComposedData;ei&&L.push({props:$($({},ei($($({},$e),{},{displayedData:K,props:x,dataKey:V,item:F,bandSize:he,barPosition:J,offset:g,stackedData:Y,layout:C,dataStartIndex:_,dataEndIndex:S}))),{},G(G(G({key:F.key||"item-".concat(H)},I,$e[I]),R,$e[R]),"animationId",m)),childIndex:b1(F,x.children),item:F})}),L},p=function(x,w){var b=x.props,O=x.dataStartIndex,g=x.dataEndIndex,m=x.updateId;if(!Ed({props:b}))return null;var _=b.children,S=b.layout,P=b.stackOffset,C=b.data,A=b.reverseStackOrder,j=nb(S),E=j.numericAxisName,N=j.cateAxisName,I=Ze(_,n),R=EE(C,I,"".concat(E,"Id"),"".concat(N,"Id"),P,A),D=s.reduce(function(z,V){var ce="".concat(V.axisType,"Map");return $($({},z),{},G({},ce,Hk(b,$($({},V),{},{graphicalItems:I,stackGroups:V.axisType===E&&R,dataStartIndex:O,dataEndIndex:g}))))},{}),L=Vk($($({},D),{},{props:b,graphicalItems:I}),w?.legendBBox);Object.keys(D).forEach(function(z){D[z]=f(b,D[z],L,z.replace("Map",""),r)});var F=D["".concat(N,"Map")],H=Gk(F),K=h(b,$($({},D),{},{dataStartIndex:O,dataEndIndex:g,updateId:m,graphicalItems:I,stackGroups:R,offset:L}));return $($({formattedGraphicalItems:K,graphicalItems:I,offset:L,stackGroups:R},H),D)},y=function(d){function x(w){var b,O,g;return jk(this,x),g=Ck(this,x,[w]),G(g,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),G(g,"accessibilityManager",new pk),G(g,"handleLegendBBoxUpdate",function(m){if(m){var _=g.state,S=_.dataStartIndex,P=_.dataEndIndex,C=_.updateId;g.setState($({legendBBox:m},p({props:g.props,dataStartIndex:S,dataEndIndex:P,updateId:C},$($({},g.state),{},{legendBBox:m}))))}}),G(g,"handleReceiveSyncEvent",function(m,_,S){if(g.props.syncId===m){if(S===g.eventEmitterSymbol&&typeof g.props.syncMethod!="function")return;g.applySyncEvent(_)}}),G(g,"handleBrushChange",function(m){var _=m.startIndex,S=m.endIndex;if(_!==g.state.dataStartIndex||S!==g.state.dataEndIndex){var P=g.state.updateId;g.setState(function(){return $({dataStartIndex:_,dataEndIndex:S},p({props:g.props,dataStartIndex:_,dataEndIndex:S,updateId:P},g.state))}),g.triggerSyncEvent({dataStartIndex:_,dataEndIndex:S})}}),G(g,"handleMouseEnter",function(m){var _=g.getMouseInfo(m);if(_){var S=$($({},_),{},{isTooltipActive:!0});g.setState(S),g.triggerSyncEvent(S);var P=g.props.onMouseEnter;Z(P)&&P(S,m)}}),G(g,"triggeredAfterMouseMove",function(m){var _=g.getMouseInfo(m),S=_?$($({},_),{},{isTooltipActive:!0}):{isTooltipActive:!1};g.setState(S),g.triggerSyncEvent(S);var P=g.props.onMouseMove;Z(P)&&P(S,m)}),G(g,"handleItemMouseEnter",function(m){g.setState(function(){return{isTooltipActive:!0,activeItem:m,activePayload:m.tooltipPayload,activeCoordinate:m.tooltipPosition||{x:m.cx,y:m.cy}}})}),G(g,"handleItemMouseLeave",function(){g.setState(function(){return{isTooltipActive:!1}})}),G(g,"handleMouseMove",function(m){m.persist(),g.throttleTriggeredAfterMouseMove(m)}),G(g,"handleMouseLeave",function(m){g.throttleTriggeredAfterMouseMove.cancel();var _={isTooltipActive:!1};g.setState(_),g.triggerSyncEvent(_);var S=g.props.onMouseLeave;Z(S)&&S(_,m)}),G(g,"handleOuterEvent",function(m){var _=m1(m),S=ze(g.props,"".concat(_));if(_&&Z(S)){var P,C;/.*touch.*/i.test(_)?C=g.getMouseInfo(m.changedTouches[0]):C=g.getMouseInfo(m),S((P=C)!==null&&P!==void 0?P:{},m)}}),G(g,"handleClick",function(m){var _=g.getMouseInfo(m);if(_){var S=$($({},_),{},{isTooltipActive:!0});g.setState(S),g.triggerSyncEvent(S);var P=g.props.onClick;Z(P)&&P(S,m)}}),G(g,"handleMouseDown",function(m){var _=g.props.onMouseDown;if(Z(_)){var S=g.getMouseInfo(m);_(S,m)}}),G(g,"handleMouseUp",function(m){var _=g.props.onMouseUp;if(Z(_)){var S=g.getMouseInfo(m);_(S,m)}}),G(g,"handleTouchMove",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.throttleTriggeredAfterMouseMove(m.changedTouches[0])}),G(g,"handleTouchStart",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseDown(m.changedTouches[0])}),G(g,"handleTouchEnd",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseUp(m.changedTouches[0])}),G(g,"handleDoubleClick",function(m){var _=g.props.onDoubleClick;if(Z(_)){var S=g.getMouseInfo(m);_(S,m)}}),G(g,"handleContextMenu",function(m){var _=g.props.onContextMenu;if(Z(_)){var S=g.getMouseInfo(m);_(S,m)}}),G(g,"triggerSyncEvent",function(m){g.props.syncId!==void 0&&Hc.emit(Gc,g.props.syncId,m,g.eventEmitterSymbol)}),G(g,"applySyncEvent",function(m){var _=g.props,S=_.layout,P=_.syncMethod,C=g.state.updateId,A=m.dataStartIndex,j=m.dataEndIndex;if(m.dataStartIndex!==void 0||m.dataEndIndex!==void 0)g.setState($({dataStartIndex:A,dataEndIndex:j},p({props:g.props,dataStartIndex:A,dataEndIndex:j,updateId:C},g.state)));else if(m.activeTooltipIndex!==void 0){var E=m.chartX,N=m.chartY,I=m.activeTooltipIndex,R=g.state,D=R.offset,L=R.tooltipTicks;if(!D)return;if(typeof P=="function")I=P(L,m);else if(P==="value"){I=-1;for(var F=0;F<L.length;F++)if(L[F].value===m.activeLabel){I=F;break}}var H=$($({},D),{},{x:D.left,y:D.top}),K=Math.min(E,H.x+H.width),z=Math.min(N,H.y+H.height),V=L[I]&&L[I].value,ce=cf(g.state,g.props.data,I),pe=L[I]?{x:S==="horizontal"?L[I].coordinate:K,y:S==="horizontal"?z:L[I].coordinate}:lw;g.setState($($({},m),{},{activeLabel:V,activeCoordinate:pe,activePayload:ce,activeTooltipIndex:I}))}else g.setState(m)}),G(g,"renderCursor",function(m){var _,S=g.state,P=S.isTooltipActive,C=S.activeCoordinate,A=S.activePayload,j=S.offset,E=S.activeTooltipIndex,N=S.tooltipAxisBandSize,I=g.getTooltipEventType(),R=(_=m.props.active)!==null&&_!==void 0?_:P,D=g.props.layout,L=m.key||"_recharts-cursor";return T.createElement(wk,{key:L,activeCoordinate:C,activePayload:A,activeTooltipIndex:E,chartName:r,element:m,isActive:R,layout:D,offset:j,tooltipAxisBandSize:N,tooltipEventType:I})}),G(g,"renderPolarAxis",function(m,_,S){var P=ze(m,"type.axisType"),C=ze(g.state,"".concat(P,"Map")),A=m.type.defaultProps,j=A!==void 0?$($({},A),m.props):m.props,E=C&&C[j["".concat(P,"Id")]];return q.cloneElement(m,$($({},E),{},{className:ie(P,E.className),key:m.key||"".concat(_,"-").concat(S),ticks:ht(E,!0)}))}),G(g,"renderPolarGrid",function(m){var _=m.props,S=_.radialLines,P=_.polarAngles,C=_.polarRadius,A=g.state,j=A.radiusAxisMap,E=A.angleAxisMap,N=At(j),I=At(E),R=I.cx,D=I.cy,L=I.innerRadius,F=I.outerRadius;return q.cloneElement(m,{polarAngles:Array.isArray(P)?P:ht(I,!0).map(function(H){return H.coordinate}),polarRadius:Array.isArray(C)?C:ht(N,!0).map(function(H){return H.coordinate}),cx:R,cy:D,innerRadius:L,outerRadius:F,key:m.key||"polar-grid",radialLines:S})}),G(g,"renderLegend",function(){var m=g.state.formattedGraphicalItems,_=g.props,S=_.children,P=_.width,C=_.height,A=g.props.margin||{},j=P-(A.left||0)-(A.right||0),E=ox({children:S,formattedGraphicalItems:m,legendWidth:j,legendContent:c});if(!E)return null;var N=E.item,I=Qm(E,Ok);return q.cloneElement(N,$($({},I),{},{chartWidth:P,chartHeight:C,margin:A,onBBoxUpdate:g.handleLegendBBoxUpdate}))}),G(g,"renderTooltip",function(){var m,_=g.props,S=_.children,P=_.accessibilityLayer,C=qe(S,Qe);if(!C)return null;var A=g.state,j=A.isTooltipActive,E=A.activeCoordinate,N=A.activePayload,I=A.activeLabel,R=A.offset,D=(m=C.props.active)!==null&&m!==void 0?m:j;return q.cloneElement(C,{viewBox:$($({},R),{},{x:R.left,y:R.top}),active:D,label:I,payload:D?N:[],coordinate:E,accessibilityLayer:P})}),G(g,"renderBrush",function(m){var _=g.props,S=_.margin,P=_.data,C=g.state,A=C.offset,j=C.dataStartIndex,E=C.dataEndIndex,N=C.updateId;return q.cloneElement(m,{key:m.key||"_recharts-brush",onChange:di(g.handleBrushChange,m.props.onChange),data:P,x:B(m.props.x)?m.props.x:A.left,y:B(m.props.y)?m.props.y:A.top+A.height+A.brushBottom-(S.bottom||0),width:B(m.props.width)?m.props.width:A.width,startIndex:j,endIndex:E,updateId:"brush-".concat(N)})}),G(g,"renderReferenceElement",function(m,_,S){if(!m)return null;var P=g,C=P.clipPathId,A=g.state,j=A.xAxisMap,E=A.yAxisMap,N=A.offset,I=m.type.defaultProps||{},R=m.props,D=R.xAxisId,L=D===void 0?I.xAxisId:D,F=R.yAxisId,H=F===void 0?I.yAxisId:F;return q.cloneElement(m,{key:m.key||"".concat(_,"-").concat(S),xAxis:j[L],yAxis:E[H],viewBox:{x:N.left,y:N.top,width:N.width,height:N.height},clipPathId:C})}),G(g,"renderActivePoints",function(m){var _=m.item,S=m.activePoint,P=m.basePoint,C=m.childIndex,A=m.isRange,j=[],E=_.props.key,N=_.item.type.defaultProps!==void 0?$($({},_.item.type.defaultProps),_.item.props):_.item.props,I=N.activeDot,R=N.dataKey,D=$($({index:C,dataKey:R,cx:S.x,cy:S.y,r:4,fill:oh(_.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},ee(I,!1)),bi(I));return j.push(x.renderActiveDot(I,D,"".concat(E,"-activePoint-").concat(C))),P?j.push(x.renderActiveDot(I,$($({},D),{},{cx:P.x,cy:P.y}),"".concat(E,"-basePoint-").concat(C))):A&&j.push(null),j}),G(g,"renderGraphicChild",function(m,_,S){var P=g.filterFormatItem(m,_,S);if(!P)return null;var C=g.getTooltipEventType(),A=g.state,j=A.isTooltipActive,E=A.tooltipAxis,N=A.activeTooltipIndex,I=A.activeLabel,R=g.props.children,D=qe(R,Qe),L=P.props,F=L.points,H=L.isRange,K=L.baseLine,z=P.item.type.defaultProps!==void 0?$($({},P.item.type.defaultProps),P.item.props):P.item.props,V=z.activeDot,ce=z.hide,pe=z.activeBar,ke=z.activeShape,Rt=!!(!ce&&j&&D&&(V||pe||ke)),$e={};C!=="axis"&&D&&D.props.trigger==="click"?$e={onClick:di(g.handleItemMouseEnter,m.props.onClick)}:C!=="axis"&&($e={onMouseLeave:di(g.handleItemMouseLeave,m.props.onMouseLeave),onMouseEnter:di(g.handleItemMouseEnter,m.props.onMouseEnter)});var U=q.cloneElement(m,$($({},P.props),$e));function X(Dt){return typeof E.dataKey=="function"?E.dataKey(Dt.payload):null}if(Rt)if(N>=0){var Y,k;if(E.dataKey&&!E.allowDuplicatedCategory){var he=typeof E.dataKey=="function"?X:"payload.".concat(E.dataKey.toString());Y=Vc(F,he,I),k=H&&K&&Vc(K,he,I)}else Y=F?.[N],k=H&&K&&K[N];if(ke||pe){var J=m.props.activeIndex!==void 0?m.props.activeIndex:N;return[q.cloneElement(m,$($($({},P.props),$e),{},{activeIndex:J})),null,null]}if(!ne(Y))return[U].concat(qr(g.renderActivePoints({item:P,activePoint:Y,basePoint:k,childIndex:N,isRange:H})))}else{var ge,me=(ge=g.getItemByXY(g.state.activeCoordinate))!==null&&ge!==void 0?ge:{graphicalItem:U},Ie=me.graphicalItem,_t=Ie.item,ir=_t===void 0?m:_t,ei=Ie.childIndex,kt=$($($({},P.props),$e),{},{activeIndex:ei});return[q.cloneElement(ir,kt),null,null]}return H?[U,null,null]:[U,null]}),G(g,"renderCustomized",function(m,_,S){return q.cloneElement(m,$($({key:"recharts-customized-".concat(S)},g.props),g.state))}),G(g,"renderMap",{CartesianGrid:{handler:mi,once:!0},ReferenceArea:{handler:g.renderReferenceElement},ReferenceLine:{handler:mi},ReferenceDot:{handler:g.renderReferenceElement},XAxis:{handler:mi},YAxis:{handler:mi},Brush:{handler:g.renderBrush,once:!0},Bar:{handler:g.renderGraphicChild},Line:{handler:g.renderGraphicChild},Area:{handler:g.renderGraphicChild},Radar:{handler:g.renderGraphicChild},RadialBar:{handler:g.renderGraphicChild},Scatter:{handler:g.renderGraphicChild},Pie:{handler:g.renderGraphicChild},Funnel:{handler:g.renderGraphicChild},Tooltip:{handler:g.renderCursor,once:!0},PolarGrid:{handler:g.renderPolarGrid,once:!0},PolarAngleAxis:{handler:g.renderPolarAxis},PolarRadiusAxis:{handler:g.renderPolarAxis},Customized:{handler:g.renderCustomized}}),g.clipPathId="".concat((b=w.id)!==null&&b!==void 0?b:ma("recharts"),"-clip"),g.throttleTriggeredAfterMouseMove=a0(g.triggeredAfterMouseMove,(O=w.throttleDelay)!==null&&O!==void 0?O:1e3/60),g.state={},g}return Nk(x,d),Mk(x,[{key:"componentDidMount",value:function(){var b,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(b=this.props.margin.left)!==null&&b!==void 0?b:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var b=this.props,O=b.children,g=b.data,m=b.height,_=b.layout,S=qe(O,Qe);if(S){var P=S.props.defaultIndex;if(!(typeof P!="number"||P<0||P>this.state.tooltipTicks.length-1)){var C=this.state.tooltipTicks[P]&&this.state.tooltipTicks[P].value,A=cf(this.state,g,P,C),j=this.state.tooltipTicks[P].coordinate,E=(this.state.offset.top+m)/2,N=_==="horizontal",I=N?{x:j,y:E}:{y:j,x:E},R=this.state.formattedGraphicalItems.find(function(L){var F=L.item;return F.type.name==="Scatter"});R&&(I=$($({},I),R.props.points[P].tooltipPosition),A=R.props.points[P].tooltipPayload);var D={activeTooltipIndex:P,isTooltipActive:!0,activeLabel:C,activePayload:A,activeCoordinate:I};this.setState(D),this.renderCursor(S),this.accessibilityManager.setIndex(P)}}}},{key:"getSnapshotBeforeUpdate",value:function(b,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==b.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==b.margin){var g,m;this.accessibilityManager.setDetails({offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(m=this.props.margin.top)!==null&&m!==void 0?m:0}})}return null}},{key:"componentDidUpdate",value:function(b){Yc([qe(b.children,Qe)],[qe(this.props.children,Qe)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var b=qe(this.props.children,Qe);if(b&&typeof b.props.shared=="boolean"){var O=b.props.shared?"axis":"item";return u.indexOf(O)>=0?O:a}return a}},{key:"getMouseInfo",value:function(b){if(!this.container)return null;var O=this.container,g=O.getBoundingClientRect(),m=oA(g),_={chartX:Math.round(b.pageX-m.left),chartY:Math.round(b.pageY-m.top)},S=g.width/O.offsetWidth||1,P=this.inRange(_.chartX,_.chartY,S);if(!P)return null;var C=this.state,A=C.xAxisMap,j=C.yAxisMap,E=this.getTooltipEventType(),N=tb(this.state,this.props.data,this.props.layout,P);if(E!=="axis"&&A&&j){var I=At(A).scale,R=At(j).scale,D=I&&I.invert?I.invert(_.chartX):null,L=R&&R.invert?R.invert(_.chartY):null;return $($({},_),{},{xValue:D,yValue:L},N)}return N?$($({},_),N):null}},{key:"inRange",value:function(b,O){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,m=this.props.layout,_=b/g,S=O/g;if(m==="horizontal"||m==="vertical"){var P=this.state.offset,C=_>=P.left&&_<=P.left+P.width&&S>=P.top&&S<=P.top+P.height;return C?{x:_,y:S}:null}var A=this.state,j=A.angleAxisMap,E=A.radiusAxisMap;if(j&&E){var N=At(j);return gg({x:_,y:S},N)}return null}},{key:"parseEventsOfWrapper",value:function(){var b=this.props.children,O=this.getTooltipEventType(),g=qe(b,Qe),m={};g&&O==="axis"&&(g.props.trigger==="click"?m={onClick:this.handleClick}:m={onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu});var _=bi(this.props,this.handleOuterEvent);return $($({},_),m)}},{key:"addListener",value:function(){Hc.on(Gc,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Hc.removeListener(Gc,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(b,O,g){for(var m=this.state.formattedGraphicalItems,_=0,S=m.length;_<S;_++){var P=m[_];if(P.item===b||P.props.key===b.key||O===dt(P.item.type)&&g===P.childIndex)return P}return null}},{key:"renderClipPath",value:function(){var b=this.clipPathId,O=this.state.offset,g=O.left,m=O.top,_=O.height,S=O.width;return T.createElement("defs",null,T.createElement("clipPath",{id:b},T.createElement("rect",{x:g,y:m,height:_,width:S})))}},{key:"getXScales",value:function(){var b=this.state.xAxisMap;return b?Object.entries(b).reduce(function(O,g){var m=Jm(g,2),_=m[0],S=m[1];return $($({},O),{},G({},_,S.scale))},{}):null}},{key:"getYScales",value:function(){var b=this.state.yAxisMap;return b?Object.entries(b).reduce(function(O,g){var m=Jm(g,2),_=m[0],S=m[1];return $($({},O),{},G({},_,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(b){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(b){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[b])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(b){var O=this.state,g=O.formattedGraphicalItems,m=O.activeItem;if(g&&g.length)for(var _=0,S=g.length;_<S;_++){var P=g[_],C=P.props,A=P.item,j=A.type.defaultProps!==void 0?$($({},A.type.defaultProps),A.props):A.props,E=dt(A.type);if(E==="Bar"){var N=(C.data||[]).find(function(L){return O$(b,L)});if(N)return{graphicalItem:P,payload:N}}else if(E==="RadialBar"){var I=(C.data||[]).find(function(L){return gg(b,L)});if(I)return{graphicalItem:P,payload:I}}else if(Da(P,m)||qa(P,m)||Bn(P,m)){var R=hI({graphicalItem:P,activeTooltipItem:m,itemData:j.data}),D=j.activeIndex===void 0?R:j.activeIndex;return{graphicalItem:$($({},P),{},{childIndex:D}),payload:Bn(P,m)?j.data[R]:P.props.data[R]}}}return null}},{key:"render",value:function(){var b=this;if(!Ed(this))return null;var O=this.props,g=O.children,m=O.className,_=O.width,S=O.height,P=O.style,C=O.compact,A=O.title,j=O.desc,E=Qm(O,_k),N=ee(E,!1);if(C)return T.createElement(km,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement(Jc,hr({},N,{width:_,height:S,title:A,desc:j}),this.renderClipPath(),Cd(g,this.renderMap)));if(this.props.accessibilityLayer){var I,R;N.tabIndex=(I=this.props.tabIndex)!==null&&I!==void 0?I:0,N.role=(R=this.props.role)!==null&&R!==void 0?R:"application",N.onKeyDown=function(L){b.accessibilityManager.keyboardEvent(L)},N.onFocus=function(){b.accessibilityManager.focus()}}var D=this.parseEventsOfWrapper();return T.createElement(km,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},T.createElement("div",hr({className:ie("recharts-wrapper",m),style:$({position:"relative",cursor:"default",width:_,height:S},P)},D,{ref:function(F){b.container=F}}),T.createElement(Jc,hr({},N,{width:_,height:S,title:A,desc:j,style:Lk}),this.renderClipPath(),Cd(g,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(q.Component);G(y,"displayName",r),G(y,"defaultProps",$({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},l)),G(y,"getDerivedStateFromProps",function(d,x){var w=d.dataKey,b=d.data,O=d.children,g=d.width,m=d.height,_=d.layout,S=d.stackOffset,P=d.margin,C=x.dataStartIndex,A=x.dataEndIndex;if(x.updateId===void 0){var j=rb(d);return $($($({},j),{},{updateId:0},p($($({props:d},j),{},{updateId:0}),x)),{},{prevDataKey:w,prevData:b,prevWidth:g,prevHeight:m,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(w!==x.prevDataKey||b!==x.prevData||g!==x.prevWidth||m!==x.prevHeight||_!==x.prevLayout||S!==x.prevStackOffset||!pr(P,x.prevMargin)){var E=rb(d),N={chartX:x.chartX,chartY:x.chartY,isTooltipActive:x.isTooltipActive},I=$($({},tb(x,b,_)),{},{updateId:x.updateId+1}),R=$($($({},E),N),I);return $($($({},R),p($({props:d},R),x)),{},{prevDataKey:w,prevData:b,prevWidth:g,prevHeight:m,prevLayout:_,prevStackOffset:S,prevMargin:P,prevChildren:O})}if(!Yc(O,x.prevChildren)){var D,L,F,H,K=qe(O,Er),z=K&&(D=(L=K.props)===null||L===void 0?void 0:L.startIndex)!==null&&D!==void 0?D:C,V=K&&(F=(H=K.props)===null||H===void 0?void 0:H.endIndex)!==null&&F!==void 0?F:A,ce=z!==C||V!==A,pe=!ne(b),ke=pe&&!ce?x.updateId:x.updateId+1;return $($({updateId:ke},p($($({props:d},x),{},{updateId:ke,dataStartIndex:z,dataEndIndex:V}),x)),{},{prevChildren:O,dataStartIndex:z,dataEndIndex:V})}return null}),G(y,"renderActiveDot",function(d,x,w){var b;return q.isValidElement(d)?b=q.cloneElement(d,x):Z(d)?b=d(x):b=T.createElement(_x,x),T.createElement(_e,{className:"recharts-active-dot",key:w},b)});var v=q.forwardRef(function(x,w){return T.createElement(y,hr({},x,{ref:w}))});return v.displayName=y.displayName,v},Zk=Yk({chartName:"BarChart",GraphicalChild:nr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Wa},{axisType:"yAxis",AxisComp:Ha}],formatAxisMap:sN});const Jk={light:"",dark:".dark"},hw=q.createContext(null);function Qk(){const e=q.useContext(hw);if(!e)throw new Error("useChart must be used within a <ChartContainer />");return e}function eD({id:e,className:t,children:r,config:n,...i}){const a=q.useId(),o=`chart-${e||a.replace(/:/g,"")}`;return M.jsx(hw.Provider,{value:{config:n},children:M.jsxs("div",{"data-slot":"chart","data-chart":o,className:Bt("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...i,children:[M.jsx(tD,{id:o,config:n}),M.jsx(QS,{children:r})]})})}const tD=({id:e,config:t})=>{const r=Object.entries(t).filter(([,n])=>n.theme||n.color);return r.length?M.jsx("style",{dangerouslySetInnerHTML:{__html:Object.entries(Jk).map(([n,i])=>`
${i} [data-chart=${e}] {
${r.map(([a,o])=>{const u=o.theme?.[n]||o.color;return u?`  --color-${a}: ${u};`:null}).join(`
`)}
}
`).join(`
`)}}):null},rD=Qe;function nD({active:e,payload:t,className:r,indicator:n="dot",hideLabel:i=!1,hideIndicator:a=!1,label:o,labelFormatter:u,labelClassName:s,formatter:c,color:f,nameKey:l,labelKey:h}){const{config:p}=Qk(),y=q.useMemo(()=>{if(i||!t?.length)return null;const[d]=t,x=`${h||d?.dataKey||d?.name||"value"}`,w=ib(p,d,x),b=!h&&typeof o=="string"?p[o]?.label||o:w?.label;return u?M.jsx("div",{className:Bt("font-medium",s),children:u(b,t)}):b?M.jsx("div",{className:Bt("font-medium",s),children:b}):null},[o,u,t,i,s,p,h]);if(!e||!t?.length)return null;const v=t.length===1&&n!=="dot";return M.jsxs("div",{className:Bt("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[v?null:y,M.jsx("div",{className:"grid gap-1.5",children:t.map((d,x)=>{const w=`${l||d.name||d.dataKey||"value"}`,b=ib(p,d,w),O=f||d.payload.fill||d.color;return M.jsx("div",{className:Bt("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5",n==="dot"&&"items-center"),children:c&&d?.value!==void 0&&d.name?c(d.value,d.name,d,x,d.payload):M.jsxs(M.Fragment,{children:[b?.icon?M.jsx(b.icon,{}):!a&&M.jsx("div",{className:Bt("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":n==="dot","w-1":n==="line","w-0 border-[1.5px] border-dashed bg-transparent":n==="dashed","my-0.5":v&&n==="dashed"}),style:{"--color-bg":O,"--color-border":O}}),M.jsxs("div",{className:Bt("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[M.jsxs("div",{className:"grid gap-1.5",children:[v?y:null,M.jsx("span",{className:"text-muted-foreground",children:b?.label||d.name})]}),d.value&&M.jsx("span",{className:"text-foreground font-mono font-medium tabular-nums",children:d.value.toLocaleString()})]})]})},d.dataKey)})})]})}function ib(e,t,r){if(typeof t!="object"||t===null)return;const n="payload"in t&&typeof t.payload=="object"&&t.payload!==null?t.payload:void 0;let i=r;return r in t&&typeof t[r]=="string"?i=t[r]:n&&r in n&&typeof n[r]=="string"&&(i=n[r]),i in e?e[i]:e[r]}const iD=()=>lf({queryKey:[ff.GetJettyStatus],queryFn:async()=>{try{const e=await fetch("/api/dashboard/jetty-status",{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return(await e.json()).map(r=>({jettyName:r.jettyName||"",occupied:r.occupied||0,available:r.available||0}))}catch(e){let t="Unknown error occurred while loading jetty status";throw typeof e=="object"&&e&&"message"in e&&typeof e.message=="string"&&(t=e.message??t),hf({title:"Error loading jetty status",description:t,variant:"destructive"}),e}},staleTime:5*60*1e3,refetchInterval:5*60*1e3}),aD={occupied:{label:"Occupied",color:"hsl(var(--chart-1))"},available:{label:"Available",color:"hsl(var(--chart-2))"},label:{color:"hsl(var(--background))"}};function oD(){const{data:e,isLoading:t,error:r}=iD(),n=e?.map(i=>({jetty:i.jettyName,occupied:i.occupied,available:i.available}))||[];return t?M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Jetty Status"})}),M.jsx(dr,{children:M.jsx(Pt,{className:"h-[250px] w-full"})})]}):r?M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Jetty Status"})}),M.jsx(dr,{children:M.jsx("div",{className:"flex items-center justify-center h-[250px] text-muted-foreground",children:"Error loading jetty status"})})]}):M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Jetty Status"})}),M.jsx(dr,{children:M.jsx(eD,{config:aD,children:M.jsxs(Zk,{accessibilityLayer:!0,data:n,layout:"vertical",margin:{right:16},children:[M.jsx(Jx,{horizontal:!1}),M.jsx(Ha,{dataKey:"jetty",type:"category",tickLine:!1,tickMargin:10,axisLine:!1,tickFormatter:i=>i.slice(0,3),hide:!0}),M.jsx(Wa,{dataKey:"occupied",type:"number",hide:!0}),M.jsx(rD,{cursor:!1,content:M.jsx(nD,{indicator:"line"})}),M.jsxs(nr,{dataKey:"occupied",layout:"horizontal",fill:"var(--color-occupied)",radius:4,children:[M.jsx(yt,{dataKey:"jetty",position:"insideLeft",offset:8,className:"fill-[--color-label]",fontSize:12}),M.jsx(yt,{dataKey:"occupied",position:"right",offset:8,className:"fill-foreground",fontSize:12})]})]})})}),M.jsxs(rn,{className:"flex-col items-start gap-2 text-sm",children:[M.jsxs("div",{className:"flex gap-2 font-medium leading-none",children:["Jetty utilization status ",M.jsx(mw,{className:"h-4 w-4"})]}),M.jsx("div",{className:"leading-none text-muted-foreground",children:"Showing occupied vs available berths"})]})]})}const uD=(e=10)=>lf({queryKey:[ff.GetPendingApprovals,e],queryFn:async()=>{try{const t=await fetch(`/api/dashboard/pending-approvals?maxResults=${e}`,{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);return await t.json()}catch(t){let r="Unknown error occurred while loading pending approvals";throw typeof t=="object"&&t&&"message"in t&&typeof t.message=="string"&&(r=t.message??r),hf({title:"Error loading pending approvals",description:r,variant:"destructive"}),t}},staleTime:2*60*1e3,refetchInterval:2*60*1e3});function sD(){const{data:e,isLoading:t,error:r}=uD(10);if(t)return M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Waiting for Approval"})}),M.jsx(dr,{children:M.jsx("div",{className:"space-y-3",children:Array.from({length:5}).map((a,o)=>M.jsxs("div",{className:"flex items-center space-x-4",children:[M.jsx(Pt,{className:"h-4 w-[100px]"}),M.jsx(Pt,{className:"h-4 w-[200px]"}),M.jsx(Pt,{className:"h-4 w-[80px]"})]},o))})})]});if(r)return M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Waiting for Approval"})}),M.jsx(dr,{children:M.jsx("div",{className:"flex items-center justify-center h-[200px] text-muted-foreground",children:"Error loading pending approvals"})})]});const n=e?.items||[],i={0:"Pending",1:"Approved",2:"Rejected",3:"Cancelled"};return M.jsxs(et,{children:[M.jsx(tt,{children:M.jsx(rt,{children:"Waiting for Approval"})}),M.jsx(dr,{children:n.length===0?M.jsx("div",{className:"flex items-center justify-center h-[200px] text-muted-foreground",children:"No pending approvals"}):M.jsxs(pw,{children:[M.jsx(vw,{children:M.jsxs(bh,{children:[M.jsx(ii,{children:"Request ID"}),M.jsx(ii,{children:"Stage"}),M.jsx(ii,{children:"Status"}),M.jsx(ii,{children:"Created"})]})}),M.jsx(yw,{children:n.slice(0,5).map(a=>M.jsxs(bh,{children:[M.jsxs(ai,{className:"font-medium",children:[a.id?.substring(0,8),"..."]}),M.jsx(ai,{children:a.approvalTemplate?.name||"N/A"}),M.jsx(ai,{children:M.jsx(tn,{variant:a.status===0?"secondary":"primary",children:i[a.status]??a.status})}),M.jsx(ai,{className:"text-muted-foreground",children:a.creationTime?new Date(a.creationTime).toLocaleDateString():"N/A"})]},a.id))})]})})]})}function vD(){const{data:e,isLoading:t,error:r}=bw();return M.jsxs("div",{className:"flex flex-1 flex-col space-y-2",children:[M.jsx("div",{className:"flex items-center justify-between space-y-2",children:M.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Hi, Welcome back 👋"})}),M.jsxs("div",{className:"*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4 mb-4",children:[M.jsxs(et,{className:"@container/card",children:[M.jsxs(tt,{children:[M.jsx(ri,{children:"Approval Requests"}),M.jsx(rt,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?M.jsx(Pt,{className:"h-8 w-16"}):r?"Error":e?.totalApprovalRequests||0}),M.jsx(ni,{children:M.jsxs(tn,{variant:"outline",children:[M.jsx(ar,{}),"+1.5%"]})})]}),M.jsxs(rn,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Total approval requests ",M.jsx(ar,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"All time requests"})]})]}),M.jsxs(et,{className:"@container/card",children:[M.jsxs(tt,{children:[M.jsx(ri,{children:"Pending Approval"}),M.jsx(rt,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?M.jsx(Pt,{className:"h-8 w-16"}):r?"Error":e?.pendingApprovals||0}),M.jsx(ni,{children:M.jsxs(tn,{variant:"outline",children:[M.jsx(xh,{}),"-20%"]})})]}),M.jsxs(rn,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Pending approvals ",M.jsx(xh,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Awaiting review"})]})]}),M.jsxs(et,{className:"@container/card",children:[M.jsxs(tt,{children:[M.jsx(ri,{children:"Rejected Requests"}),M.jsx(rt,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?M.jsx(Pt,{className:"h-8 w-16"}):r?"Error":e?.rejectedRequests||0}),M.jsx(ni,{children:M.jsxs(tn,{variant:"outline",children:[M.jsx(ar,{}),"+12.5%"]})})]}),M.jsxs(rn,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Rejected requests ",M.jsx(ar,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Quality control"})]})]}),M.jsxs(et,{className:"@container/card",children:[M.jsxs(tt,{children:[M.jsx(ri,{children:"Scheduled Vessels Today"}),M.jsx(rt,{className:"text-2xl font-semibold tabular-nums @[250px]/card:text-3xl",children:t?M.jsx(Pt,{className:"h-8 w-16"}):r?"Error":e?.scheduledVesselsToday||0}),M.jsx(ni,{children:M.jsxs(tn,{variant:"outline",children:[M.jsx(ar,{}),"+4.5%"]})})]}),M.jsxs(rn,{className:"flex-col items-start gap-1.5 text-sm",children:[M.jsxs("div",{className:"line-clamp-1 flex gap-2 font-medium",children:["Vessels scheduled today ",M.jsx(ar,{className:"size-4"})]}),M.jsx("div",{className:"text-muted-foreground",children:"Today's schedule"})]})]})]}),M.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7",children:[M.jsx("div",{className:"col-span-4",children:M.jsx(sD,{})}),M.jsx("div",{className:"col-span-4 md:col-span-3",children:M.jsx(oD,{})})]})]})}export{vD as O};
//# sourceMappingURL=overview-CTW9zS8n.js.map
