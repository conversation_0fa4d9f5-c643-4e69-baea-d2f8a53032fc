import { useCallback } from 'react'

import { useAppConfig } from './useAppConfig'

/**
 * Represents the various policies that can be granted within the application.
 *
 * The available policies include:
 * - `AbpIdentity.Roles`: Manage roles.
 * - `AbpIdentity.Roles.Create`: Create new roles.
 * - `AbpIdentity.Roles.Update`: Update existing roles.
 * - `AbpIdentity.Roles.Delete`: Delete roles.
 * - `AbpIdentity.Roles.ManagePermissions`: Manage permissions for roles.
 * - `AbpIdentity.Users`: Manage users.
 * - `AbpIdentity.Users.Create`: Create new users.
 * - `AbpIdentity.Users.Update`: Update existing users.
 * - `AbpIdentity.Users.Delete`: Delete users.
 * - `AbpIdentity.Users.ManagePermissions`: Manage permissions for users.
 * - `AbpTenantManagement.Tenants`: Manage tenants.
 * - `AbpTenantManagement.Tenants.Create`: Create new tenants.
 * - `AbpTenantManagement.Tenants.Update`: Update existing tenants.
 * - `AbpTenantManagement.Tenants.Delete`: Delete tenants.
 * - `AbpTenantManagement.Tenants.ManageFeatures`: Manage features for tenants.
 * - `AbpTenantManagement.Tenants.ManageConnectionStrings`: Manage connection strings for tenants.
 * - `FeatureManagement.ManageHostFeatures`: Manage host features.
 * - `SettingManagement.Emailing`: Manage emailing settings.
 * - `SettingManagement.Emailing.Test`: Test emailing settings.
 */
export type Policy =
| "QuartzAccess"
| "SilkierQuartz"
| "FeatureManagement.ManageHostFeatures"
| "SettingManagement.Emailing"
| "SettingManagement.Emailing.Test"
| "SettingManagement.TimeZone"
| "AbpIdentity.Roles"
| "AbpIdentity.Roles.Create"
| "AbpIdentity.Roles.Update"
| "AbpIdentity.Roles.Delete"
| "AbpIdentity.Roles.ManagePermissions"
| "AbpIdentity.Users"
| "AbpIdentity.Users.Create"
| "AbpIdentity.Users.Update"
| "AbpIdentity.Users.Update.ManageRoles"
| "AbpIdentity.Users.Delete"
| "AbpIdentity.Users.ManagePermissions"
| "AbpTenantManagement.Tenants"
| "AbpTenantManagement.Tenants.Create"
| "AbpTenantManagement.Tenants.Update"
| "AbpTenantManagement.Tenants.Delete"
| "AbpTenantManagement.Tenants.ManageFeatures"
| "AbpTenantManagement.Tenants.ManageConnectionStrings"
| "JettyApprovalApp.JettyRequest"
| "JettyApprovalApp.JettyRequest.View"
| "JettyApprovalApp.JettyRequest.Create"
| "JettyApprovalApp.JettyRequest.Edit"
| "JettyApprovalApp.JettyRequest.Delete"
| "JettyApprovalApp.JettyRequestItem"
| "JettyApprovalApp.JettyRequestItem.View"
| "JettyApprovalApp.JettyRequestItem.Create"
| "JettyApprovalApp.JettyRequestItem.Edit"
| "JettyApprovalApp.JettyRequestItem.Delete"
| "JettyApprovalApp.Attachment"
| "JettyApprovalApp.Attachment.View"
| "JettyApprovalApp.Attachment.Create"
| "JettyApprovalApp.Attachment.Edit"
| "JettyApprovalApp.Attachment.Delete"
| "JettyApprovalApp.Report"
| "JettyApprovalApp.Report.View"
| "JettyApprovalApp.Report.Create"
| "JettyApprovalApp.Report.Edit"
| "JettyApprovalApp.Report.Delete"
| "JettyApprovalApp.Report.Export"
| "JettyApprovalApp.Report.Execute"
| "JettyApprovalApp.JettySchedule"
| "JettyApprovalApp.JettySchedule.View"
| "JettyApprovalApp.JettySchedule.Create"
| "JettyApprovalApp.JettySchedule.Edit"
| "JettyApprovalApp.JettySchedule.Delete"
| "JettyApprovalApp.JettyDockedVessel"
| "JettyApprovalApp.JettyDockedVessel.View"
| "JettyApprovalApp.JettyDockedVessel.Create"
| "JettyApprovalApp.JettyDockedVessel.Edit"
| "JettyApprovalApp.JettyDockedVessel.Delete"
| "JettyApprovalApp.JettyManage"
| "JettyApprovalApp.JettyManage.View"
| "JettyApprovalApp.JettyManage.Create"
| "JettyApprovalApp.JettyManage.Edit"
| "JettyApprovalApp.JettyManage.Delete"
| "JettyApprovalApp.DocumentTemplate"
| "JettyApprovalApp.DocumentTemplate.View"
| "JettyApprovalApp.DocumentTemplate.Create"
| "JettyApprovalApp.DocumentTemplate.Edit"
| "JettyApprovalApp.DocumentTemplate.Delete"
| "JettyApprovalApp.ApprovalRequest"
| "JettyApprovalApp.ApprovalRequest.View"
| "JettyApprovalApp.ApprovalRequest.Create"
| "JettyApprovalApp.ApprovalRequest.Edit"
| "JettyApprovalApp.ApprovalRequest.Delete"
| "JettyApprovalApp.ApprovalTemplate"
| "JettyApprovalApp.ApprovalTemplate.View"
| "JettyApprovalApp.ApprovalTemplate.Create"
| "JettyApprovalApp.ApprovalTemplate.Edit"
| "JettyApprovalApp.ApprovalTemplate.Delete"
| "JettyApprovalApp.ApprovalApprover"
| "JettyApprovalApp.ApprovalApprover.View"
| "JettyApprovalApp.ApprovalApprover.Create"
| "JettyApprovalApp.ApprovalApprover.Edit"
| "JettyApprovalApp.ApprovalApprover.Delete"
| "JettyApprovalApp.ApprovalCriteria"
| "JettyApprovalApp.ApprovalCriteria.View"
| "JettyApprovalApp.ApprovalCriteria.Create"
| "JettyApprovalApp.ApprovalCriteria.Edit"
| "JettyApprovalApp.ApprovalCriteria.Delete"
| "JettyApprovalApp.ApprovalStages"
| "JettyApprovalApp.ApprovalStages.View"
| "JettyApprovalApp.ApprovalStages.Create"
| "JettyApprovalApp.ApprovalStages.Edit"
| "JettyApprovalApp.ApprovalStages.Delete"
| "JettyApprovalApp.ApprovalDelegation"
| "JettyApprovalApp.ApprovalDelegation.View"
| "JettyApprovalApp.ApprovalDelegation.Create"
| "JettyApprovalApp.ApprovalDelegation.Edit"
| "JettyApprovalApp.ApprovalDelegation.Delete"
| "JettyApprovalApp.CustomArea"
| "JettyApprovalApp.CustomArea.Create"
| "JettyApprovalApp.CustomArea.Delete"
| "JettyApprovalApp.CustomArea.Edit"
| "JettyApprovalApp.CustomArea.View"
| "JettyApprovalApp.ExportVesselCustomArea"
| "JettyApprovalApp.ExportVesselCustomArea.Create"
| "JettyApprovalApp.ExportVesselCustomArea.Delete"
| "JettyApprovalApp.ExportVesselCustomArea.Edit"
| "JettyApprovalApp.ExportVesselCustomArea.View"
| "JettyApprovalApp.ExportVesselNonCustomArea"
| "JettyApprovalApp.ExportVesselNonCustomArea.Create"
| "JettyApprovalApp.ExportVesselNonCustomArea.Delete"
| "JettyApprovalApp.ExportVesselNonCustomArea.Edit"
| "JettyApprovalApp.ExportVesselNonCustomArea.View"
| "JettyApprovalApp.ImportVesselCustomArea"
| "JettyApprovalApp.ImportVesselCustomArea.Create"
| "JettyApprovalApp.ImportVesselCustomArea.Delete"
| "JettyApprovalApp.ImportVesselCustomArea.Edit"
| "JettyApprovalApp.ImportVesselCustomArea.View"
| "JettyApprovalApp.ImportVesselNonCustomArea"
| "JettyApprovalApp.ImportVesselNonCustomArea.Create"
| "JettyApprovalApp.ImportVesselNonCustomArea.Delete"
| "JettyApprovalApp.ImportVesselNonCustomArea.Edit"
| "JettyApprovalApp.ImportVesselNonCustomArea.View"
| "JettyApprovalApp.LocalVesselCustomArea"
| "JettyApprovalApp.LocalVesselCustomArea.Create"
| "JettyApprovalApp.LocalVesselCustomArea.Delete"
| "JettyApprovalApp.LocalVesselCustomArea.Edit"
| "JettyApprovalApp.LocalVesselCustomArea.View"
| "JettyApprovalApp.LocalVesselNonCustomArea"
| "JettyApprovalApp.LocalVesselNonCustomArea.Create"
| "JettyApprovalApp.LocalVesselNonCustomArea.Delete"
| "JettyApprovalApp.LocalVesselNonCustomArea.Edit"
| "JettyApprovalApp.LocalVesselNonCustomArea.View"
| "JettyApprovalApp.NonCustomArea"
| "JettyApprovalApp.NonCustomArea.Create"
| "JettyApprovalApp.NonCustomArea.Delete"
| "JettyApprovalApp.NonCustomArea.Edit"
| "JettyApprovalApp.NonCustomArea.View"


/**
 * Custom hook to check if a specific policy is granted.
 *
 * This hook uses the application configuration to determine if a given policy is granted.
 *
 * @returns An object with a `can` function that takes a policy key and returns a boolean indicating if the policy is granted.
 *
 * @example
 * const { can } = useGrantedPolicies();
 * const hasPolicy = can('somePolicyKey');
 *
 * @function
 * @name useGrantedPolicies
 */
export const useGrantedPolicies = () => {
  const { data } = useAppConfig()
  // console.log('grantedPolicies', data?.auth?.grantedPolicies)
  const can = useCallback(
    (key: Policy): boolean => {
      if (data?.auth?.grantedPolicies && !!data.auth.grantedPolicies[key]) return true
      return false
    },
    [data?.auth?.grantedPolicies]
  )
  return { can }
}
