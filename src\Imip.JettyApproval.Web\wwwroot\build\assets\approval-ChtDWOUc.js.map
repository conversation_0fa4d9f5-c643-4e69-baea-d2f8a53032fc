{"version": 3, "file": "approval-ChtDWOUc.js", "sources": ["../../../../../frontend/src/pages/approval.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuItem, DropdownMenuLabel } from '@/components/ui/dropdown-menu';\r\nimport { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';\r\n\r\ninterface PendingApproval {\r\n  id: string;\r\n  vesselName: string;\r\n  arrivalDate: string;\r\n  departureDate: string;\r\n  itemName: string;\r\n  requestBy: string;\r\n}\r\n\r\nconst mockPendingApprovalData: PendingApproval[] = Array(10).fill(null).map((_, i) => ({\r\n  id: `pending_${i + 1}`,\r\n  vesselName: `MV. PENDING REQUEST V. 00${i + 1}`,\r\n  arrivalDate: '2025-05-05',\r\n  departureDate: '2025-05-06',\r\n  itemName: 'CRUDE OIL 200MT',\r\n  requestBy: 'USER3',\r\n}));\r\n\r\nexport default function ApprovalList() {\r\n  const [filter, setFilter] = useState('');\r\n  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());\r\n  const [visibleColumns, setVisibleColumns] = useState<Set<keyof PendingApproval>>(\r\n    new Set(Object.keys(mockPendingApprovalData[0]) as (keyof PendingApproval)[])\r\n  );\r\n  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);\r\n  const [rejectNotes, setRejectNotes] = useState('');\r\n  const [currentRejectId, setCurrentRejectId] = useState<string | null>(null);\r\n\r\n  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);\r\n  const [currentApproveId, setCurrentApproveId] = useState<string | null>(null);\r\n\r\n  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);\r\n  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');\r\n\r\n  const filteredData = mockPendingApprovalData.filter(approval =>\r\n    Object.values(approval).some(value =>\r\n      value.toString().toLowerCase().includes(filter.toLowerCase())\r\n    )\r\n  );\r\n\r\n  const handleSelectAll = (checked: boolean) => {\r\n    if (checked) {\r\n      setSelectedRows(new Set(filteredData.map(row => row.id)));\r\n    } else {\r\n      setSelectedRows(new Set());\r\n    }\r\n  };\r\n\r\n  const handleRowSelect = (id: string, checked: boolean) => {\r\n    const newSelection = new Set(selectedRows);\r\n    if (checked) {\r\n      newSelection.add(id);\r\n    } else {\r\n      newSelection.delete(id);\r\n    }\r\n    setSelectedRows(newSelection);\r\n  };\r\n\r\n  const handleToggleColumn = (columnKey: keyof PendingApproval, checked: boolean) => {\r\n    const newVisibleColumns = new Set(visibleColumns);\r\n    if (checked) {\r\n      newVisibleColumns.add(columnKey);\r\n    } else {\r\n      newVisibleColumns.delete(columnKey);\r\n    }\r\n    setVisibleColumns(newVisibleColumns);\r\n  };\r\n\r\n  const handleApproveClick = (id: string) => {\r\n    setCurrentApproveId(id);\r\n    setIsApproveDialogOpen(true);\r\n  };\r\n\r\n  const handleApproveConfirm = () => {\r\n    if (currentApproveId) {\r\n      console.log(`Approving application with ID: ${currentApproveId}`);\r\n      // Implement actual approve logic here\r\n      setIsApproveDialogOpen(false);\r\n      setCurrentApproveId(null);\r\n    }\r\n  };\r\n\r\n  const handleRejectClick = (id: string) => {\r\n    setCurrentRejectId(id);\r\n    setIsRejectDialogOpen(true);\r\n  };\r\n\r\n  const handleRejectConfirm = () => {\r\n    if (currentRejectId) {\r\n      console.log(`Rejecting application with ID: ${currentRejectId} with notes: ${rejectNotes}`);\r\n      // Implement actual reject logic here\r\n      setIsRejectDialogOpen(false);\r\n      setRejectNotes('');\r\n      setCurrentRejectId(null);\r\n    }\r\n  };\r\n\r\n  const handlePreview = (id: string) => {\r\n    console.log(`Preview document for ID: ${id}`);\r\n    setPreviewDocumentSrc('/pdf/surat1.pdf'); // Example PDF path\r\n    setIsPreviewDialogOpen(true);\r\n  };\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"container mx-auto p-4\">\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-2xl font-bold\">Waiting Approvals</CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <Input\r\n                placeholder=\"Filter lines...\"\r\n                value={filter}\r\n                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}\r\n                className=\"max-w-sm\"\r\n              />\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\" className=\"ml-auto\">\r\n                    Columns <IconChevronDown className=\"ml-2 h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\">\r\n                  {Object.keys(mockPendingApprovalData[0]).map((key) => (\r\n                    <DropdownMenuCheckboxItem\r\n                      key={key}\r\n                      className=\"capitalize\"\r\n                      checked={visibleColumns.has(key as keyof PendingApproval)}\r\n                      onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof PendingApproval, checked === true)}\r\n                    >\r\n                      {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                    </DropdownMenuCheckboxItem>\r\n                  ))}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            </div>\r\n\r\n            <div className=\"rounded-md border\">\r\n              <Table>\r\n                <TableHeader>\r\n                  <TableRow>\r\n                    <TableHead className=\"w-[30px]\">\r\n                      <Checkbox\r\n                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}\r\n                        onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}\r\n                      />\r\n                    </TableHead>\r\n                    {Object.keys(mockPendingApprovalData[0]).map((key) => (visibleColumns.has(key as keyof PendingApproval) && key !== 'id' &&\r\n                      <TableHead key={key} className=\"capitalize\">\r\n                        {key.replace(/([A-Z])/g, ' $1').trim()}\r\n                      </TableHead>\r\n                    ))}\r\n                    <TableHead className=\"text-right\">Actions</TableHead>\r\n                  </TableRow>\r\n                </TableHeader>\r\n                <TableBody>\r\n                  {filteredData.map((approval) => (\r\n                    <TableRow key={approval.id}>\r\n                      <TableCell>\r\n                        <Checkbox\r\n                          checked={selectedRows.has(approval.id)}\r\n                          onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(approval.id, checked === true)}\r\n                        />\r\n                      </TableCell>\r\n                      {Object.entries(approval).map(([key, value]) => (visibleColumns.has(key as keyof PendingApproval) && key !== 'id' &&\r\n                        <TableCell key={key}>{value}</TableCell>\r\n                      ))}\r\n                      <TableCell className=\"text-right\">\r\n                        <DropdownMenu>\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                              <span className=\"sr-only\">Open menu</span>\r\n                              <IconDotsVertical className=\"h-4 w-4\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent align=\"end\">\r\n                            <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                            <DropdownMenuItem onClick={() => handlePreview(approval.id)}>\r\n                              Preview\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem onClick={() => handleApproveClick(approval.id)}>\r\n                              Approve\r\n                            </DropdownMenuItem>\r\n                            <DropdownMenuItem onClick={() => handleRejectClick(approval.id)}>\r\n                              Reject\r\n                            </DropdownMenuItem>\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))}\r\n                </TableBody>\r\n              </Table>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between mt-4\">\r\n              <div className=\"text-sm text-gray-500\">\r\n                {selectedRows.size} of {filteredData.length} row(s) selected.\r\n              </div>\r\n              <div className=\"space-x-2\">\r\n                <Button variant=\"outline\" size=\"sm\">Previous</Button>\r\n                <Button variant=\"outline\" size=\"sm\">Next</Button>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Reject Request Dialog */}\r\n      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-[425px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Reject Request</DialogTitle>\r\n          </DialogHeader>\r\n          <div className=\"grid gap-4 py-4\">\r\n            <div className=\"grid gap-2\">\r\n              <Label htmlFor=\"notes\">Notes</Label>\r\n              <Textarea\r\n                id=\"notes\"\r\n                value={rejectNotes}\r\n                onChange={(e) => setRejectNotes(e.target.value)}\r\n                placeholder=\"Value\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <DialogFooter>\r\n            <Button onClick={handleRejectConfirm} className=\"px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50\">Reject</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Approve Confirmation Dialog */}\r\n      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>\r\n        <DialogContent className=\"sm:max-w-[425px]\">\r\n          <DialogHeader>\r\n            <DialogTitle>Confirmation</DialogTitle>\r\n            <DialogDescription>Are you sure you want to approve this document?</DialogDescription>\r\n          </DialogHeader>\r\n          <DialogFooter className=\"justify-end\">\r\n            <Button onClick={handleApproveConfirm} className=\"px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50\">Yes Approve</Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <DocumentPreviewDialog\r\n        isOpen={isPreviewDialogOpen}\r\n        onOpenChange={setIsPreviewDialogOpen}\r\n        documentSrc={previewDocumentSrc}\r\n      />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["mockPendingApprovalData", "_", "i", "ApprovalList", "filter", "setFilter", "useState", "selectedRows", "setSelectedRows", "visibleColumns", "setVisibleColumns", "isRejectDialogOpen", "setIsRejectDialogOpen", "rejectNotes", "setRejectNotes", "currentRejectId", "setCurrentRejectId", "isApproveDialogOpen", "setIsApproveDialogOpen", "currentApproveId", "setCurrentApproveId", "isPreviewDialogOpen", "setIsPreviewDialogOpen", "previewDocumentSrc", "setPreviewDocumentSrc", "filteredData", "approval", "value", "handleSelectAll", "checked", "row", "handleRowSelect", "id", "newSelection", "handleToggleColumn", "column<PERSON>ey", "newVisibleColumns", "handleApproveClick", "handleApproveConfirm", "handleRejectClick", "handleRejectConfirm", "handlePreview", "AppLayout", "jsx", "jsxs", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON><PERSON><PERSON>", "Input", "e", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON>", "IconChevronDown", "DropdownMenuContent", "key", "DropdownMenuCheckboxItem", "Table", "TableHeader", "TableRow", "TableHead", "Checkbox", "TableBody", "TableCell", "IconDotsVertical", "DropdownMenuLabel", "DropdownMenuItem", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "Label", "Textarea", "<PERSON><PERSON><PERSON><PERSON>er", "DialogDescription", "DocumentPreviewDialog"], "mappings": "onBAuBA,MAAMA,EAA6C,MAAM,EAAE,EAAE,KAAK,IAAI,EAAE,IAAI,CAACC,EAAGC,KAAO,CACrF,GAAI,WAAWA,EAAI,CAAC,GACpB,WAAY,4BAA4BA,EAAI,CAAC,GAC7C,YAAa,aACb,cAAe,aACf,SAAU,kBACV,UAAW,OACb,EAAE,EAEF,SAAwBC,IAAe,CACrC,KAAM,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,EAAE,EACjC,CAACC,EAAcC,CAAe,EAAIF,EAAAA,SAAsB,IAAI,GAAK,EACjE,CAACG,EAAgBC,CAAiB,EAAIJ,EAAA,SAC1C,IAAI,IAAI,OAAO,KAAKN,EAAwB,CAAC,CAAC,CAA8B,CAC9E,EACM,CAACW,EAAoBC,CAAqB,EAAIN,EAAAA,SAAS,EAAK,EAC5D,CAACO,EAAaC,CAAc,EAAIR,EAAAA,SAAS,EAAE,EAC3C,CAACS,EAAiBC,CAAkB,EAAIV,EAAAA,SAAwB,IAAI,EAEpE,CAACW,EAAqBC,CAAsB,EAAIZ,EAAAA,SAAS,EAAK,EAC9D,CAACa,EAAkBC,CAAmB,EAAId,EAAAA,SAAwB,IAAI,EAEtE,CAACe,EAAqBC,CAAsB,EAAIhB,EAAAA,SAAS,EAAK,EAC9D,CAACiB,EAAoBC,CAAqB,EAAIlB,EAAAA,SAAS,EAAE,EAEzDmB,EAAezB,EAAwB,OAC3C0B,GAAA,OAAO,OAAOA,CAAQ,EAAE,KAAKC,GAC3BA,EAAM,SAAS,EAAE,cAAc,SAASvB,EAAO,YAAa,CAAA,CAAA,CAEhE,EAEMwB,EAAmBC,GAAqB,CAE1BrB,EADdqB,EACc,IAAI,IAAIJ,EAAa,OAAWK,EAAI,EAAE,CAAC,EAEvC,IAAI,GAFoC,CAI5D,EAEMC,EAAkB,CAACC,EAAYH,IAAqB,CAClD,MAAAI,EAAe,IAAI,IAAI1B,CAAY,EACrCsB,EACFI,EAAa,IAAID,CAAE,EAEnBC,EAAa,OAAOD,CAAE,EAExBxB,EAAgByB,CAAY,CAC9B,EAEMC,EAAqB,CAACC,EAAkCN,IAAqB,CAC3E,MAAAO,EAAoB,IAAI,IAAI3B,CAAc,EAC5CoB,EACFO,EAAkB,IAAID,CAAS,EAE/BC,EAAkB,OAAOD,CAAS,EAEpCzB,EAAkB0B,CAAiB,CACrC,EAEMC,EAAsBL,GAAe,CACzCZ,EAAoBY,CAAE,EACtBd,EAAuB,EAAI,CAC7B,EAEMoB,EAAuB,IAAM,CAC7BnB,IAGFD,EAAuB,EAAK,EAC5BE,EAAoB,IAAI,EAE5B,EAEMmB,EAAqBP,GAAe,CACxChB,EAAmBgB,CAAE,EACrBpB,EAAsB,EAAI,CAC5B,EAEM4B,EAAsB,IAAM,CAC5BzB,IAGFH,EAAsB,EAAK,EAC3BE,EAAe,EAAE,EACjBE,EAAmB,IAAI,EAE3B,EAEMyB,EAAiBT,GAAe,CAEpCR,EAAsB,iBAAiB,EACvCF,EAAuB,EAAI,CAC7B,EAEA,cACGoB,EACC,CAAA,SAAA,CAAAC,MAAC,MAAI,CAAA,UAAU,wBACb,SAAAC,EAAA,KAACC,EACC,CAAA,SAAA,CAAAF,EAAAA,IAACG,GACC,SAACH,EAAA,IAAAI,EAAA,CAAU,UAAU,qBAAqB,6BAAiB,CAC7D,CAAA,SACCC,EACC,CAAA,SAAA,CAACJ,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAD,EAAA,IAACM,GAAA,CACC,YAAY,kBACZ,MAAO7C,EACP,SAAW8C,GAA2C7C,EAAU6C,EAAE,OAAO,KAAK,EAC9E,UAAU,UAAA,CACZ,SACCC,EACC,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GAC1B,SAAAR,EAAAA,KAACS,GAAO,QAAQ,UAAU,UAAU,UAAU,SAAA,CAAA,WACpCV,EAAAA,IAACW,GAAgB,CAAA,UAAU,cAAe,CAAA,CAAA,CAAA,CACpD,CACF,CAAA,EACCX,EAAAA,IAAAY,EAAA,CAAoB,MAAM,MACxB,SAAO,OAAA,KAAKvD,EAAwB,CAAC,CAAC,EAAE,IAAKwD,GAC5Cb,EAAA,IAACc,GAAA,CAEC,UAAU,aACV,QAAShD,EAAe,IAAI+C,CAA4B,EACxD,gBAAkB3B,GAAuCK,EAAmBsB,EAA8B3B,IAAY,EAAI,EAEzH,SAAI2B,EAAA,QAAQ,WAAY,KAAK,EAAE,KAAK,CAAA,EALhCA,CAAA,CAOR,CACH,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EAECb,MAAA,MAAA,CAAI,UAAU,oBACb,gBAACe,GACC,CAAA,SAAA,CAACf,EAAA,IAAAgB,GAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAACjB,EAAAA,IAAAkB,EAAA,CAAU,UAAU,WACnB,SAAAlB,EAAA,IAACmB,EAAA,CACC,QAASvD,EAAa,OAASkB,EAAa,QAAUA,EAAa,OAAS,EAC5E,gBAAkBI,GAAuCD,EAAgBC,IAAY,EAAI,CAAA,CAAA,EAE7F,EACC,OAAO,KAAK7B,EAAwB,CAAC,CAAC,EAAE,IAAKwD,GAAS/C,EAAe,IAAI+C,CAA4B,GAAKA,IAAQ,MAChHb,MAAAkB,EAAA,CAAoB,UAAU,aAC5B,SAAIL,EAAA,QAAQ,WAAY,KAAK,EAAE,MADlB,EAAAA,CAEhB,CACD,EACAb,EAAA,IAAAkB,EAAA,CAAU,UAAU,aAAa,SAAO,SAAA,CAAA,CAAA,CAAA,CAC3C,CACF,CAAA,QACCE,GACE,CAAA,SAAAtC,EAAa,IAAKC,UAChBkC,EACC,CAAA,SAAA,CAAAjB,MAACqB,EACC,CAAA,SAAArB,EAAA,IAACmB,EAAA,CACC,QAASvD,EAAa,IAAImB,EAAS,EAAE,EACrC,gBAAkBG,GAAuCE,EAAgBL,EAAS,GAAIG,IAAY,EAAI,CAAA,CAAA,EAE1G,EACC,OAAO,QAAQH,CAAQ,EAAE,IAAI,CAAC,CAAC8B,EAAK7B,CAAK,IAAOlB,EAAe,IAAI+C,CAA4B,GAAKA,IAAQ,YAC1GQ,EAAqB,CAAA,SAAArC,GAAN6B,CAAY,CAC7B,EACAb,MAAAqB,EAAA,CAAU,UAAU,aACnB,gBAACb,EACC,CAAA,SAAA,CAACR,EAAAA,IAAAS,EAAA,CAAoB,QAAO,GAC1B,SAAAR,EAAAA,KAACS,GAAO,QAAQ,QAAQ,UAAU,cAChC,SAAA,CAACV,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAS,YAAA,EACnCA,EAAAA,IAACsB,GAAiB,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,CACxC,CACF,CAAA,EACArB,EAAAA,KAACW,EAAoB,CAAA,MAAM,MACzB,SAAA,CAAAZ,EAAAA,IAACuB,IAAkB,SAAO,SAAA,CAAA,EAC1BvB,MAACwB,GAAiB,QAAS,IAAM1B,EAAcf,EAAS,EAAE,EAAG,SAE7D,UAAA,EACAiB,MAACwB,GAAiB,QAAS,IAAM9B,EAAmBX,EAAS,EAAE,EAAG,SAElE,UAAA,EACAiB,MAACwB,GAAiB,QAAS,IAAM5B,EAAkBb,EAAS,EAAE,EAAG,SAEjE,QAAA,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,GA/BaA,EAAS,EAgCxB,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAkB,EAAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wBACZ,SAAA,CAAarC,EAAA,KAAK,OAAKkB,EAAa,OAAO,mBAAA,EAC9C,EACAmB,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAD,MAACU,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAQ,WAAA,QAC3CA,EAAO,CAAA,QAAQ,UAAU,KAAK,KAAK,SAAI,MAAA,CAAA,CAAA,CAC1C,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAV,EAAAA,IAACyB,GAAO,KAAMzD,EAAoB,aAAcC,EAC9C,SAAAgC,EAAA,KAACyB,EAAc,CAAA,UAAU,mBACvB,SAAA,CAAA1B,MAAC2B,EACC,CAAA,SAAA3B,EAAA,IAAC4B,EAAY,CAAA,SAAA,gBAAc,CAAA,EAC7B,QACC,MAAI,CAAA,UAAU,kBACb,SAAC3B,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACD,EAAA,IAAA6B,GAAA,CAAM,QAAQ,QAAQ,SAAK,QAAA,EAC5B7B,EAAA,IAAC8B,GAAA,CACC,GAAG,QACH,MAAO5D,EACP,SAAWqC,GAAMpC,EAAeoC,EAAE,OAAO,KAAK,EAC9C,YAAY,OAAA,CAAA,CACd,CAAA,CACF,CACF,CAAA,EACAP,EAAAA,IAAC+B,GACC,SAAC/B,EAAAA,IAAAU,EAAA,CAAO,QAASb,EAAqB,UAAU,uIAAuI,SAAA,QAAM,CAAA,CAC/L,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAGAG,EAAAA,IAACyB,GAAO,KAAMnD,EAAqB,aAAcC,EAC/C,SAAA0B,EAAA,KAACyB,EAAc,CAAA,UAAU,mBACvB,SAAA,CAAAzB,OAAC0B,EACC,CAAA,SAAA,CAAA3B,EAAAA,IAAC4B,GAAY,SAAY,cAAA,CAAA,EACzB5B,EAAAA,IAACgC,IAAkB,SAA+C,iDAAA,CAAA,CAAA,EACpE,EACAhC,EAAA,IAAC+B,EAAa,CAAA,UAAU,cACtB,SAAA/B,EAAAA,IAACU,EAAO,CAAA,QAASf,EAAsB,UAAU,uIAAuI,SAAA,aAAA,CAAW,CACrM,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAK,EAAA,IAACiC,GAAA,CACC,OAAQvD,EACR,aAAcC,EACd,YAAaC,CAAA,CAAA,CACf,EACF,CAEJ"}