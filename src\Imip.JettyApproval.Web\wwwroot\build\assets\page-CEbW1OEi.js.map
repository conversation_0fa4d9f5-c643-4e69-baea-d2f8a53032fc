{"version": 3, "file": "page-CEbW1OEi.js", "sources": ["../../../../../frontend/src/lib/hooks/useJettyRequestItems.ts", "../../../../../frontend/src/components/applications/items-table.tsx", "../../../../../frontend/src/pages/application/items/page.tsx"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\r\nimport { postApiIdjasJettyRequestItemFilterList } from '@/client/sdk.gen';\r\nimport { toast } from '@/lib/useToast';\r\nimport type { FilterGroup, QueryParametersDto, PagedResultDtoOfJettyRequestItemDto } from '@/client/types.gen';\r\n\r\nexport const useJettyRequestItems = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterGroup?: FilterGroup,\r\n  sorting?: string\r\n) => {\r\n  return useQuery<PagedResultDtoOfJettyRequestItemDto, Error>({\r\n    queryKey: ['jetty-request-items', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],\r\n    queryFn: async (): Promise<PagedResultDtoOfJettyRequestItemDto> => {\r\n      const payload: QueryParametersDto = {\r\n        skipCount: pageIndex * pageSize,\r\n        maxResultCount: pageSize,\r\n        sorting,\r\n        filterGroup,\r\n      };\r\n      \r\n      try {\r\n        const response = await postApiIdjasJettyRequestItemFilterList({ body: payload });\r\n        return response.data || { items: [], totalCount: 0 };\r\n      } catch (error: unknown) {\r\n        let message = 'Unknown error occurred while loading Jetty Request Items';\r\n        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n          message = (error as { message?: string }).message ?? message;\r\n        }\r\n        \r\n        console.error('JettyRequestItems API Error:', error);\r\n        toast({\r\n          title: 'Error loading Jetty Request Items',\r\n          description: message,\r\n          variant: 'destructive',\r\n        });\r\n        \r\n        // Return empty result instead of throwing\r\n        return { items: [], totalCount: 0 };\r\n      }\r\n    },\r\n    retry: 1, // Only retry once\r\n    retryDelay: 1000, // Wait 1 second before retry\r\n  });\r\n}; ", "import type { FilterCondition, FilterGroup, FilterOperator, JettyRequestItemDto, LogicalOperator } from \"@/client/types.gen\";\r\nimport { DataTable } from \"@/components/data-table/DataTable\";\r\nimport FilterSortBar, { type SortDirection } from \"@/components/filter-sort-bar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport ErrorBoundary from \"@/components/ui/error-boundary\";\r\nimport TableSkeleton from \"@/components/ui/table-skeleton\";\r\nimport { useJettyRequestItems } from \"@/lib/hooks/useJettyRequestItems\";\r\nimport { router } from \"@inertiajs/react\";\r\nimport { type ColumnDef, type PaginationState } from \"@tanstack/react-table\";\r\nimport { AlertTriangle, Plus, RefreshCw } from \"lucide-react\";\r\nimport React, { useMemo, useState } from \"react\";\r\n\r\nconst FILTER_FIELDS = [\r\n  { value: \"tenantName\", label: \"Tenant Name\" },\r\n  { value: \"itemName\", label: \"Item Name\" },\r\n  { value: \"qty\", label: \"Quantity\" },\r\n  { value: \"uoM\", label: \"Unit of Measurement\" },\r\n  { value: \"status\", label: \"Status\" },\r\n  { value: \"jettyRequestId\", label: \"Jetty Request ID\" },\r\n];\r\n\r\nconst FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [\r\n  { value: \"Equals\", label: \"Equals\" },\r\n  { value: \"Contains\", label: \"Contains\" },\r\n  { value: \"NotEquals\", label: \"Not Equals\" },\r\n  { value: \"GreaterThan\", label: \">\" },\r\n  { value: \"LessThan\", label: \"<\" },\r\n];\r\n\r\nconst columns: ColumnDef<JettyRequestItemDto>[] = [\r\n  {\r\n    accessorKey: \"tenantName\",\r\n    header: \"Tenant Name\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"itemName\",\r\n    header: \"Item Name\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"qty\",\r\n    header: \"Quantity\",\r\n    cell: info => {\r\n      const value = info.getValue() as number;\r\n      return value !== undefined ? value.toString() : \"-\";\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"uoM\",\r\n    header: \"Unit of Measurement\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"status\",\r\n    header: \"Status\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"notes\",\r\n    header: \"Notes\",\r\n    cell: info => {\r\n      const notes = info.getValue() as string;\r\n      return notes ? (notes.length > 50 ? `${notes.substring(0, 50)}...` : notes) : \"-\";\r\n    },\r\n  },\r\n  {\r\n    accessorKey: \"creationTime\",\r\n    header: \"Created\",\r\n    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : \"-\",\r\n  },\r\n];\r\n\r\nconst ItemsTableContent: React.FC = () => {\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  });\r\n\r\n  const [filters, setFilters] = useState<FilterCondition[]>([]);\r\n  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  // Convert filters to FilterGroup\r\n  const filterGroup: FilterGroup | undefined = useMemo(() => {\r\n    if (filters.length === 0) return undefined;\r\n\r\n    return {\r\n      operator: \"And\" as LogicalOperator,\r\n      conditions: filters,\r\n    };\r\n  }, [filters]);\r\n\r\n  // Convert sorts to string\r\n  const sorting = useMemo(() => {\r\n    if (sorts.length === 0) return undefined;\r\n    return sorts.map(sort => `${sort.field} ${sort.direction}`).join(\",\");\r\n  }, [sorts]);\r\n\r\n  const { data, isLoading, error, refetch } = useJettyRequestItems(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterGroup,\r\n    sorting\r\n  );\r\n\r\n  const handleRefresh = async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      await refetch();\r\n    } finally {\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  const handleNewItem = () => {\r\n    router.visit(\"/application/items/create\");\r\n  };\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm p-6\">\r\n        <div className=\"flex items-center gap-2 text-destructive mb-4\">\r\n          <AlertTriangle className=\"h-5 w-5\" />\r\n          <h2 className=\"text-lg font-semibold\">Error Loading Items</h2>\r\n        </div>\r\n        <p className=\"text-muted-foreground mb-4\">\r\n          {error.message || \"An unexpected error occurred while loading the items.\"}\r\n        </p>\r\n        <Button onClick={handleRefresh} variant=\"outline\">\r\n          <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n          Try Again\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <div className=\"text-xl font-bold px-2 pt-2 pb-1\">Jetty Request Items</div>\r\n      <FilterSortBar\r\n        filterFields={FILTER_FIELDS}\r\n        operators={FILTER_OPERATORS}\r\n        filters={filters}\r\n        sorts={sorts}\r\n        onFiltersChange={setFilters}\r\n        onSortsChange={setSorts}\r\n      >\r\n        <div className=\"ml-auto flex items-center gap-2\">\r\n          <Button\r\n            onClick={handleRefresh}\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"h-10 w-10\"\r\n            disabled={isLoading || isRefreshing}\r\n          >\r\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\r\n          </Button>\r\n          <Button\r\n            onClick={handleNewItem}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base\"\r\n            size=\"lg\"\r\n          >\r\n            <Plus className=\"h-5 w-5\" /> New Item\r\n          </Button>\r\n        </div>\r\n      </FilterSortBar>\r\n      {isLoading ? (\r\n        <TableSkeleton columns={columns} />\r\n      ) : (\r\n        <DataTable\r\n          title=\"\"\r\n          columns={columns}\r\n          data={data?.items ?? []}\r\n          totalCount={data?.totalCount ?? 0}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={setPagination}\r\n          hideDefaultFilterbar={true}\r\n          enableRowSelection={false}\r\n          manualSorting={true}\r\n          // Sorting and filtering are handled above\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemsTable: React.FC = () => {\r\n  return (\r\n    <ErrorBoundary>\r\n      <ItemsTableContent />\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\nexport default ItemsTable; ", "import ItemsTable from \"@/components/applications/items-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport React from \"react\";\r\n\r\nconst ItemsPage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <ItemsTable />\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ItemsPage; "], "names": ["useJettyRequestItems", "pageIndex", "pageSize", "filterGroup", "sorting", "useQuery", "payload", "postApiIdjasJettyRequestItemFilterList", "error", "message", "toast", "FILTER_FIELDS", "FILTER_OPERATORS", "columns", "info", "value", "notes", "ItemsTableContent", "pagination", "setPagination", "useState", "filters", "setFilters", "sorts", "setSorts", "isRefreshing", "setIsRefreshing", "useMemo", "sort", "data", "isLoading", "refetch", "handleRefresh", "handleNewItem", "router", "jsxs", "jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "RefreshCw", "FilterSortBar", "Plus", "TableSkeleton", "DataTable", "ItemsTable", "Error<PERSON>ou<PERSON><PERSON>", "ItemsPage", "AppLayout"], "mappings": "otBAKO,MAAMA,EAAuB,CAClCC,EACAC,EACAC,EACAC,IAEOC,EAAqD,CAC1D,SAAU,CAAC,sBAAuBJ,EAAWC,EAAU,KAAK,UAAUC,CAAW,EAAGC,CAAO,EAC3F,QAAS,SAA0D,CACjE,MAAME,EAA8B,CAClC,UAAWL,EAAYC,EACvB,eAAgBA,EAChB,QAAAE,EACA,YAAAD,CACF,EAEI,GAAA,CAEF,OADiB,MAAMI,EAAuC,CAAE,KAAMD,EAAS,GAC/D,MAAQ,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,QAC5CE,EAAgB,CACvB,IAAIC,EAAU,2DACV,OAAA,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHC,EAAWD,EAA+B,SAAWC,GAIjDC,EAAA,CACJ,MAAO,oCACP,YAAaD,EACb,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EACP,WAAY,GAAA,CACb,EC/BGE,EAAgB,CACpB,CAAE,MAAO,aAAc,MAAO,aAAc,EAC5C,CAAE,MAAO,WAAY,MAAO,WAAY,EACxC,CAAE,MAAO,MAAO,MAAO,UAAW,EAClC,CAAE,MAAO,MAAO,MAAO,qBAAsB,EAC7C,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,iBAAkB,MAAO,kBAAmB,CACvD,EAEMC,EAA+D,CACnE,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,WAAY,MAAO,UAAW,EACvC,CAAE,MAAO,YAAa,MAAO,YAAa,EAC1C,CAAE,MAAO,cAAe,MAAO,GAAI,EACnC,CAAE,MAAO,WAAY,MAAO,GAAI,CAClC,EAEMC,EAA4C,CAChD,CACE,YAAa,aACb,OAAQ,cACR,KAAMC,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,WACb,OAAQ,YACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,MACb,OAAQ,WACR,KAAcA,GAAA,CACN,MAAAC,EAAQD,EAAK,SAAS,EAC5B,OAAOC,IAAU,OAAYA,EAAM,SAAa,EAAA,GAAA,CAEpD,EACA,CACE,YAAa,MACb,OAAQ,sBACR,KAAMD,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,SACb,OAAQ,SACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,QACb,OAAQ,QACR,KAAcA,GAAA,CACN,MAAAE,EAAQF,EAAK,SAAS,EACrB,OAAAE,EAASA,EAAM,OAAS,GAAK,GAAGA,EAAM,UAAU,EAAG,EAAE,CAAC,MAAQA,EAAS,GAAA,CAElF,EACA,CACE,YAAa,eACb,OAAQ,UACR,KAAMF,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,mBAAA,EAAuB,GAAA,CAE/F,EAEMG,EAA8B,IAAM,CACxC,KAAM,CAACC,EAAYC,CAAa,EAAIC,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EAEK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAA4B,CAAA,CAAE,EACtD,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAwD,CAAA,CAAE,EAC9E,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAK,EAGhDjB,EAAuCwB,EAAAA,QAAQ,IAAM,CACrD,GAAAN,EAAQ,SAAW,EAEhB,MAAA,CACL,SAAU,MACV,WAAYA,CACd,CAAA,EACC,CAACA,CAAO,CAAC,EAGNjB,EAAUuB,EAAAA,QAAQ,IAAM,CACxB,GAAAJ,EAAM,SAAW,EACrB,OAAOA,EAAM,IAAYK,GAAA,GAAGA,EAAK,KAAK,IAAIA,EAAK,SAAS,EAAE,EAAE,KAAK,GAAG,CAAA,EACnE,CAACL,CAAK,CAAC,EAEJ,CAAE,KAAAM,EAAM,UAAAC,EAAW,MAAAtB,EAAO,QAAAuB,CAAY,EAAA/B,EAC1CkB,EAAW,UACXA,EAAW,SACXf,EACAC,CACF,EAEM4B,EAAgB,SAAY,CAChCN,EAAgB,EAAI,EAChB,GAAA,CACF,MAAMK,EAAQ,CAAA,QACd,CACAL,EAAgB,EAAK,CAAA,CAEzB,EAEMO,EAAgB,IAAM,CAC1BC,EAAO,MAAM,2BAA2B,CAC1C,EAGA,OAAI1B,EAEA2B,EAAA,KAAC,MAAI,CAAA,UAAU,+DACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAc,UAAU,SAAU,CAAA,EAClCD,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAwB,SAAmB,qBAAA,CAAA,CAAA,EAC3D,QACC,IAAE,CAAA,UAAU,6BACV,SAAA5B,EAAM,SAAW,wDACpB,EACC2B,EAAA,KAAAG,EAAA,CAAO,QAASN,EAAe,QAAQ,UACtC,SAAA,CAACI,EAAAA,IAAAG,EAAA,CAAU,UAAU,cAAe,CAAA,EAAE,WAAA,CAExC,CAAA,CAAA,EACF,EAKFJ,EAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,mCAAmC,SAAmB,sBAAA,EACrEA,EAAA,IAACI,EAAA,CACC,aAAc7B,EACd,UAAWC,EACX,QAAAS,EACA,MAAAE,EACA,gBAAiBD,EACjB,cAAeE,EAEf,SAAAW,EAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAC,EAAA,IAACE,EAAA,CACC,QAASN,EACT,QAAQ,UACR,KAAK,OACL,UAAU,YACV,SAAUF,GAAaL,EAEvB,eAACc,EAAU,CAAA,UAAW,WAAWd,EAAe,eAAiB,EAAE,EAAI,CAAA,CAAA,CACzE,EACAU,EAAA,KAACG,EAAA,CACC,QAASL,EACT,UAAU,mHACV,KAAK,KAEL,SAAA,CAACG,EAAAA,IAAAK,EAAA,CAAK,UAAU,SAAU,CAAA,EAAE,WAAA,CAAA,CAAA,CAC9B,CACF,CAAA,CAAA,CACF,EACCX,EACCM,EAAAA,IAACM,EAAc,CAAA,QAAA7B,CAAA,CAAkB,EAEjCuB,EAAA,IAACO,EAAA,CACC,MAAM,GACN,QAAA9B,EACA,KAAMgB,GAAM,OAAS,CAAC,EACtB,WAAYA,GAAM,YAAc,EAChC,UAAAC,EACA,iBAAkB,GAClB,SAAUZ,EAAW,SACrB,mBAAoBC,EACpB,qBAAsB,GACtB,mBAAoB,GACpB,cAAe,EAAA,CAAA,CAEjB,EAEJ,CAEJ,EAEMyB,EAAuB,IAExBR,EAAAA,IAAAS,EAAA,CACC,SAACT,EAAAA,IAAAnB,EAAA,CAAkB,CAAA,EACrB,EC9LE6B,GAAsB,IAExBV,EAAAA,IAACW,GACC,SAACX,EAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAAAA,EAAAA,IAACQ,EAAW,CAAA,CAAA,CAAA,CACd,CACF,CAAA"}