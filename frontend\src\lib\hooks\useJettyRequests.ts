import { useQuery } from '@tanstack/react-query';
import { postApiIdjasJettyRequestFilterList } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterGroup, QueryParametersDto, PagedResultDtoOfJettyRequestDto } from '@/client/types.gen';

export const useJettyRequests = (
  pageIndex: number,
  pageSize: number,
  filterGroup?: FilterGroup,
  sorting?: string
) => {
  return useQuery<PagedResultDtoOfJettyRequestDto, Error>({
    queryKey: ['jetty-requests', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],
    queryFn: async (): Promise<PagedResultDtoOfJettyRequestDto> => {
      const payload: QueryParametersDto = {
        skipCount: pageIndex * pageSize,
        maxResultCount: pageSize,
        sorting,
        filterGroup,
      };
      
      try {
        const response = await postApiIdjasJettyRequestFilterList({ body: payload });
        return response.data || { items: [], totalCount: 0 };
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty Requests';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('JettyRequests API Error:', error);
        toast({
          title: 'Error loading Jetty Requests',
          description: message,
          variant: 'destructive',
        });
        
        // Return empty result instead of throwing
        return { items: [], totalCount: 0 };
      }
    },
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
  });
}; 