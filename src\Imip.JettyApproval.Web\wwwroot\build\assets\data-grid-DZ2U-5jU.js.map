{"version": 3, "file": "data-grid-DZ2U-5jU.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/align-center.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/align-justify.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/align-right.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/list-filter.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pen-line.js", "../../../../../frontend/src/components/ui/data-grid.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M17 12H7\", key: \"16if0g\" }],\n  [\"path\", { d: \"M19 18H5\", key: \"18s9l3\" }],\n  [\"path\", { d: \"M21 6H3\", key: \"1jwq7v\" }]\n];\nconst AlignCenter = createLucideIcon(\"align-center\", __iconNode);\n\nexport { __iconNode, AlignCenter as default };\n//# sourceMappingURL=align-center.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 12h18\", key: \"1i2n21\" }],\n  [\"path\", { d: \"M3 18h18\", key: \"1h113x\" }],\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }]\n];\nconst AlignJustify = createLucideIcon(\"align-justify\", __iconNode);\n\nexport { __iconNode, AlignJustify as default };\n//# sourceMappingURL=align-justify.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M21 12H9\", key: \"dn1m92\" }],\n  [\"path\", { d: \"M21 18H7\", key: \"1ygte8\" }],\n  [\"path\", { d: \"M21 6H3\", key: \"1jwq7v\" }]\n];\nconst AlignRight = createLucideIcon(\"align-right\", __iconNode);\n\nexport { __iconNode, AlignRight as default };\n//# sourceMappingURL=align-right.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\",\n      key: \"1nclc0\"\n    }\n  ],\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"3\", key: \"1v7zrd\" }]\n];\nconst Eye = createLucideIcon(\"eye\", __iconNode);\n\nexport { __iconNode, Eye as default };\n//# sourceMappingURL=eye.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 6h18\", key: \"d0wm0j\" }],\n  [\"path\", { d: \"M7 12h10\", key: \"b7w52i\" }],\n  [\"path\", { d: \"M10 18h4\", key: \"1ulq68\" }]\n];\nconst ListFilter = createLucideIcon(\"list-filter\", __iconNode);\n\nexport { __iconNode, ListFilter as default };\n//# sourceMappingURL=list-filter.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 20h9\", key: \"t2du7b\" }],\n  [\n    \"path\",\n    {\n      d: \"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z\",\n      key: \"1ykcvy\"\n    }\n  ]\n];\nconst PenLine = createLucideIcon(\"pen-line\", __iconNode);\n\nexport { __iconNode, PenLine as default };\n//# sourceMappingURL=pen-line.js.map\n", "import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Dialog } from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Popover } from \"@/components/ui/popover\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\";\nimport { useDebounce } from \"@/lib/hooks/useDebounce\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport {\n  flexRender,\n  getCoreRowModel,\n  getExpandedRowModel,\n  getFilteredRowModel,\n  getPaginationRowModel,\n  getSortedRowModel,\n  useReactTable,\n  type Cell,\n  type ColumnDef,\n  type ColumnFiltersState,\n  type ExpandedState,\n  type Row,\n  type SortingState,\n  type TableOptions,\n  type VisibilityState\n} from \"@tanstack/react-table\";\nimport Cookies from \"js-cookie\";\nimport { AlignCenter, AlignJustify, AlignRight, ArrowDown, ArrowUp, ArrowUpDown, ChevronLeftIcon, ChevronRightIcon, Edit3, Eye, ListFilter, Plus, RefreshCw } from \"lucide-react\";\nimport React, { useState } from \"react\";\nimport { TableSkeleton } from \"./TableSkeleton\";\n\n// Types\nexport interface DataGridProps<TData, TValue> {\n  columns: ColumnDef<TData, TValue>[];\n  queryFn: (params: {\n    pageIndex: number;\n    pageSize: number;\n    sorting?: SortingState;\n    filters?: ColumnFiltersState;\n    globalFilter?: string;\n  }) => Promise<{ items: TData[]; totalCount: number }>;\n  queryKey: unknown[];\n  pageSizeOptions?: number[];\n  defaultPageSize?: number;\n  enableRowSelection?: boolean;\n  enableRowExpansion?: boolean;\n  renderExpandedRow?: (row: Row<TData>) => React.ReactNode;\n  onCreate?: () => void;\n  createModalContent?: React.ReactNode;\n  onRefresh?: () => void;\n  title?: string;\n  initialState?: Partial<TableOptions<TData>>;\n  manualSorting?: boolean;\n  manualFiltering?: boolean;\n  rowIdAccessor?: (row: TData) => string | number;\n  striped?: boolean;\n  enableInlineEditing?: boolean;\n  onRowUpdate?: (rowId: string | number, updatedData: Partial<TData>) => Promise<void>;\n  editableColumns?: string[];\n  autoSizeColumns?: boolean; // New option for auto-sizing columns\n  meta?: Record<string, unknown>; // Add meta support\n}\n\ntype Density = \"xsmall\" | \"small\" | \"medium\";\n\nconst densityIcons = {\n  xsmall: <AlignJustify className=\"w-4 h-4\" />,\n  small: <AlignCenter className=\"w-5 h-5\" />,\n  medium: <AlignRight className=\"w-6 h-6\" />,\n};\n\nexport function DataGrid<TData, TValue = unknown>({\n  columns,\n  queryFn,\n  queryKey,\n  defaultPageSize = 10,\n  enableRowSelection = false,\n  enableRowExpansion = false,\n  renderExpandedRow,\n  onCreate,\n  createModalContent,\n  onRefresh,\n  title,\n  initialState,\n  manualSorting = true,\n  manualFiltering = true,\n  rowIdAccessor,\n  striped = false,\n  enableInlineEditing = false,\n  onRowUpdate,\n  editableColumns = [],\n  autoSizeColumns = false,\n  meta,\n}: DataGridProps<TData, TValue>) {\n  // State\n  const [pageIndex, setPageIndex] = useState(0);\n  const [pageSize, setPageSize] = useState(defaultPageSize);\n  const [sorting, setSorting] = useState<SortingState>([]);\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\n  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});\n  const [expanded, setExpanded] = useState<ExpandedState>({});\n  const [density, setDensity] = useState<Density>(() => {\n    const cookie = Cookies.get(\"dataGridDensity\");\n    if (cookie === \"xsmall\" || cookie === \"small\" || cookie === \"medium\") return cookie;\n    return \"medium\";\n  });\n  const [showFilters, setShowFilters] = useState(false);\n  const [showColumnVisibility, setShowColumnVisibility] = useState(false);\n  const [globalFilter, setGlobalFilter] = useState(\"\");\n  const debouncedGlobalFilter = useDebounce(globalFilter, 300);\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [refreshKey, setRefreshKey] = useState(0);\n\n  // Inline editing state\n  const [editingCell, setEditingCell] = useState<{ rowId: string; columnId: string } | null>(null);\n  const [editingValue, setEditingValue] = useState<string>(\"\");\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Data Fetching\n  const { data, isLoading, isFetching } = useQuery<{ items: TData[]; totalCount: number }>({\n    queryKey: [\n      ...queryKey,\n      { pageIndex, pageSize, sorting, columnFilters, globalFilter: debouncedGlobalFilter, refreshKey },\n    ],\n    queryFn: () =>\n      queryFn({\n        pageIndex,\n        pageSize,\n        sorting,\n        filters: columnFilters,\n        globalFilter: debouncedGlobalFilter,\n      }),\n  });\n  const safeData = data ?? { items: [], totalCount: 0 };\n\n  // Table Instance\n  const table = useReactTable({\n    data: safeData.items,\n    columns,\n    pageCount: Math.ceil(safeData.totalCount / pageSize),\n    state: {\n      pagination: { pageIndex, pageSize },\n      sorting,\n      columnFilters,\n      rowSelection,\n      expanded,\n      globalFilter,\n      columnVisibility,\n    },\n    manualPagination: true,\n    manualSorting,\n    manualFiltering,\n    getCoreRowModel: getCoreRowModel(),\n    getPaginationRowModel: getPaginationRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getFilteredRowModel: getFilteredRowModel(),\n    getExpandedRowModel: getExpandedRowModel(),\n    onPaginationChange: (updater) => {\n      const next = typeof updater === \"function\" ? updater({ pageIndex, pageSize }) : updater;\n      setPageIndex(next.pageIndex);\n      setPageSize(next.pageSize);\n    },\n    onSortingChange: setSorting,\n    onColumnFiltersChange: setColumnFilters,\n    onRowSelectionChange: setRowSelection,\n    onExpandedChange: setExpanded,\n    onGlobalFilterChange: setGlobalFilter,\n    onColumnVisibilityChange: setColumnVisibility,\n    ...initialState,\n    getRowId: rowIdAccessor\n      ? (originalRow) => String(rowIdAccessor(originalRow))\n      : undefined,\n    enableRowSelection,\n    enableExpanding: enableRowExpansion,\n    enableColumnResizing: true,\n    columnResizeMode: 'onChange',\n    meta,\n  });\n\n  // Auto-size columns function\n  const handleAutoSizeColumns = React.useCallback(() => {\n    if (!autoSizeColumns || !safeData.items.length) return;\n\n    const tableElement = document.querySelector('[data-table]');\n    if (!tableElement) return;\n\n    const headerCells = tableElement.querySelectorAll('th');\n    const dataCells = tableElement.querySelectorAll('td');\n\n    // Calculate optimal widths for each column\n    const columnWidths = new Map<string, number>();\n\n    // Get header widths\n    headerCells.forEach((cell, index) => {\n      const columnId = table.getAllColumns()[index]?.id;\n      if (columnId) {\n        const headerWidth = cell.scrollWidth;\n        columnWidths.set(columnId, Math.max(columnWidths.get(columnId) || 0, headerWidth));\n      }\n    });\n\n    // Get data cell widths\n    const columns = table.getAllColumns();\n    columns.forEach((column, colIndex) => {\n      const columnId = column.id;\n      let maxWidth = columnWidths.get(columnId) || 0;\n\n      // Check first few rows for content width\n      const sampleSize = Math.min(10, safeData.items.length);\n      for (let rowIndex = 0; rowIndex < sampleSize; rowIndex++) {\n        const cellIndex = rowIndex * columns.length + colIndex;\n        if (dataCells[cellIndex]) {\n          const cellWidth = dataCells[cellIndex].scrollWidth;\n          maxWidth = Math.max(maxWidth, cellWidth);\n        }\n      }\n\n      // Add some padding\n      maxWidth += 20;\n\n      // Apply min/max constraints\n      const minWidth = column.columnDef.minSize || 50;\n      const maxAllowedWidth = column.columnDef.maxSize || 500;\n      const finalWidth = Math.max(minWidth, Math.min(maxWidth, maxAllowedWidth));\n\n      columnWidths.set(columnId, finalWidth);\n    });\n\n    // Apply the calculated widths\n    columns.forEach(column => {\n      const width = columnWidths.get(column.id);\n      if (width && column.getCanResize()) {\n        // Use the table's column sizing API\n        table.setColumnSizing(prev => ({\n          ...prev,\n          [column.id]: width\n        }));\n      }\n    });\n  }, [autoSizeColumns, safeData.items, table]);\n\n  // Auto-size columns when data changes\n  React.useEffect(() => {\n    if (autoSizeColumns && safeData.items.length > 0) {\n      // Small delay to ensure DOM is updated\n      setTimeout(handleAutoSizeColumns, 100);\n    }\n  }, [autoSizeColumns, safeData.items, handleAutoSizeColumns]);\n\n  // Density classes\n  const densityClass =\n    density === \"xsmall\"\n      ? \"text-xs [&_td]:py-0.5 [&_th]:py-0.5\"\n      : density === \"small\"\n        ? \"text-sm [&_td]:py-1.5 [&_th]:py-1.5\"\n        : \"text-base [&_td]:py-3 [&_th]:py-3\";\n\n  // Density cycle logic\n  const handleDensityCycle = () => {\n    setDensity((prev) => {\n      const next = prev === \"xsmall\" ? \"small\" : prev === \"small\" ? \"medium\" : \"xsmall\";\n      Cookies.set(\"dataGridDensity\", next, { expires: 365 });\n      return next;\n    });\n  };\n\n  // Also update cookie if density is changed by other means\n  React.useEffect(() => {\n    Cookies.set(\"dataGridDensity\", density, { expires: 365 });\n  }, [density]);\n\n  // Refresh logic\n  const handleRefresh = () => {\n    if (onRefresh) onRefresh();\n    else setRefreshKey((k) => k + 1);\n  };\n\n  // New/Create logic\n  const handleCreate = () => {\n    if (createModalContent) setShowCreateModal(true);\n    else if (onCreate) onCreate();\n  };\n\n  // Inline editing handlers\n  const handleCellEdit = (rowId: string, columnId: string, value: string) => {\n    if (!enableInlineEditing || !editableColumns.includes(columnId)) return;\n\n    setEditingCell({ rowId, columnId });\n    setEditingValue(value);\n  };\n\n  const handleCellSave = async () => {\n    if (!editingCell || !onRowUpdate) return;\n\n    try {\n      setIsUpdating(true);\n      const rowId = rowIdAccessor\n        ? rowIdAccessor(safeData.items.find(item => String(rowIdAccessor(item)) === editingCell.rowId) as TData)\n        : editingCell.rowId;\n\n      await onRowUpdate(rowId, { [editingCell.columnId]: editingValue } as Partial<TData>);\n      setEditingCell(null);\n      setEditingValue(\"\");\n      handleRefresh(); // Refresh data after update\n    } catch (error) {\n      console.error('Error updating row:', error);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handleCellCancel = () => {\n    setEditingCell(null);\n    setEditingValue(\"\");\n  };\n\n  const handleCellKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleCellSave();\n    } else if (e.key === 'Escape') {\n      e.preventDefault();\n      handleCellCancel();\n    }\n  };\n\n  // Check if table is fully initialized\n  const isTableReady = React.useMemo(() => {\n    return table &&\n      typeof table.getIsAllRowsSelected === 'function' &&\n      typeof table.getToggleAllRowsSelectedHandler === 'function' &&\n      typeof table.getHeaderGroups === 'function' &&\n      typeof table.getRowModel === 'function' &&\n      safeData.items &&\n      safeData.items.length > 0;\n  }, [table, safeData.items]);\n\n  // Editable cell component\n  const EditableCell = ({ cell }: { cell: Cell<TData, unknown> }) => {\n    const isEditing = editingCell?.rowId === cell.row.id && editingCell?.columnId === cell.column.id;\n    const isEditable = enableInlineEditing && editableColumns.includes(cell.column.id);\n    const cellValue = cell.getValue() as string;\n\n    if (isEditing) {\n      return (\n        <div className=\"relative\">\n          <Input\n            value={editingValue}\n            onChange={(e) => setEditingValue(e.target.value)}\n            onKeyDown={handleCellKeyDown}\n            onBlur={handleCellSave}\n            className=\"h-8 text-sm\"\n            autoFocus\n            disabled={isUpdating}\n          />\n          {isUpdating && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-background/80\">\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"></div>\n            </div>\n          )}\n        </div>\n      );\n    }\n\n    return (\n      <div\n        className={`min-h-[2rem] flex items-center ${isEditable ? 'cursor-pointer hover:bg-muted/50 rounded px-1 -mx-1' : ''}`}\n        onClick={() => isEditable && handleCellEdit(cell.row.id, cell.column.id, cellValue || '')}\n        title={isEditable ? 'Click to edit' : undefined}\n      >\n        {flexRender(cell.column.columnDef.cell, cell.getContext())}\n      </div>\n    );\n  };\n\n  // Safe checkbox handlers\n  const getSafeIsAllRowsSelected = () => {\n    try {\n      // Only allow selection if table is ready and has data\n      if (!isTableReady) {\n        return false;\n      }\n      return table.getIsAllRowsSelected?.() ?? false;\n    } catch {\n      return false;\n    }\n  };\n\n  const getSafeToggleAllRowsSelectedHandler = () => {\n    return (checked: boolean) => {\n      try {\n        // Only allow interaction if table is ready and has data\n        if (!isTableReady) {\n          return;\n        }\n        const handler = table.getToggleAllRowsSelectedHandler?.();\n        if (handler) handler(checked);\n      } catch (error) {\n        console.warn('Error toggling all rows selection:', error);\n      }\n    };\n  };\n\n  const getSafeIsSelected = (row: Row<TData>) => {\n    try {\n      // Only allow selection if table is ready and has data\n      if (!isTableReady) {\n        return false;\n      }\n      return row?.getIsSelected?.() ?? false;\n    } catch {\n      return false;\n    }\n  };\n\n  const getSafeToggleSelectedHandler = (row: Row<TData>) => {\n    return (checked: boolean) => {\n      try {\n        // Only allow interaction if table is ready and has data\n        if (!isTableReady) {\n          return;\n        }\n        const handler = row?.getToggleSelectedHandler?.();\n        if (handler) handler(checked);\n      } catch (error) {\n        console.warn('Error toggling row selection:', error);\n      }\n    };\n  };\n\n  const getSafeIsVisible = (col: { getIsVisible?: () => boolean }) => {\n    try {\n      return col?.getIsVisible?.() ?? true;\n    } catch {\n      return true;\n    }\n  };\n\n  const getSafeToggleVisibility = (col: { toggleVisibility?: () => void }) => {\n    return () => {\n      try {\n        col?.toggleVisibility?.();\n      } catch (error) {\n        console.warn('Error toggling column visibility:', error);\n      }\n    };\n  };\n\n  // UI\n  return (\n    <div className=\"w-full\">\n      {/* Toolbar */}\n      <div className=\"flex items-center justify-between mb-2 px-1\">\n        {/* Left: Title */}\n        <div>\n          <div className=\"font-bold text-lg truncate max-w-[40vw]\">{title}</div>\n          <div className=\"h-1 w-16 bg-primary rounded mt-2 mb-4\" />\n        </div>\n        {/* Right: Actions */}\n        <div className=\"flex flex-wrap items-center gap-2\">\n          <Input\n            placeholder=\"Global search...\"\n            value={globalFilter}\n            onChange={e => setGlobalFilter(e.target.value)}\n            className=\"w-48\"\n          />\n          <Popover open={showColumnVisibility} onOpenChange={setShowColumnVisibility}>\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={() => setShowColumnVisibility(v => !v)}\n              aria-label=\"Show/Hide Columns\"\n            >\n              <Eye className=\"w-4 h-4 mr-1\" />\n            </Button>\n            {showColumnVisibility && (\n              <div className=\"absolute z-50 mt-2 bg-popover border rounded shadow p-2 min-w-[180px]\">\n                <div className=\"font-semibold mb-2\">Toggle columns</div>\n                {table?.getAllLeafColumns?.()?.map(col => (\n                  <div key={col.id} className=\"flex items-center gap-2 mb-1\">\n                    <Checkbox\n                      checked={getSafeIsVisible(col)}\n                      onCheckedChange={getSafeToggleVisibility(col)}\n                      id={`colvis-${col.id}`}\n                    />\n                    <label htmlFor={`colvis-${col.id}`}>{col.columnDef.header as string}</label>\n                  </div>\n                ))}\n              </div>\n            )}\n          </Popover>\n          <Button\n            size=\"sm\"\n            variant={showFilters ? \"default\" : \"outline\"}\n            onClick={() => setShowFilters(f => !f)}\n            aria-label=\"Toggle Filters\"\n          >\n            <ListFilter className=\"w-4 h-4 mr-1\" />\n          </Button>\n          <Button\n            size=\"sm\"\n            variant=\"outline\"\n            onClick={handleDensityCycle}\n            aria-label=\"Density\"\n          >\n            {densityIcons[density]}\n          </Button>\n          <Button\n            size=\"sm\"\n            variant=\"outline\"\n            onClick={handleRefresh}\n            aria-label=\"Refresh\"\n          >\n            <RefreshCw className=\"w-4 h-4\" />\n          </Button>\n          {enableInlineEditing && (\n            <Button\n              size=\"sm\"\n              variant={editingCell ? \"default\" : \"outline\"}\n              onClick={() => {\n                if (editingCell) {\n                  handleCellCancel();\n                }\n              }}\n              aria-label=\"Inline Editing\"\n              title={editingCell ? \"Cancel editing\" : \"Inline editing enabled\"}\n            >\n              <Edit3 className=\"w-4 h-4\" />\n            </Button>\n          )}\n          <Button\n            size=\"sm\"\n            variant=\"default\"\n            onClick={handleCreate}\n            aria-label=\"New\"\n          >\n            <Plus className=\"w-4 h-4 mr-1\" /> New\n          </Button>\n        </div>\n      </div>\n      {/* Create Modal */}\n      {createModalContent && (\n        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>\n          {createModalContent}\n        </Dialog>\n      )}\n      {/* Table */}\n      <div className={`overflow-x-auto  rounded-xl ${densityClass}`}>\n        <Table data-table>\n          <TableHeader>\n            {table?.getHeaderGroups?.()?.map(headerGroup => (\n              <TableRow key={headerGroup.id}>\n                {enableRowSelection && isTableReady && (\n                  <TableHead className=\"w-6\">\n                    <Checkbox\n                      checked={getSafeIsAllRowsSelected()}\n                      onCheckedChange={getSafeToggleAllRowsSelectedHandler()}\n                      aria-label=\"Select all\"\n                    />\n                  </TableHead>\n                )}\n                {headerGroup.headers.map(header => (\n                  <TableHead key={header.id} style={{ width: header.getSize() }} className=\"font-bold\">\n                    <div className=\"flex flex-col gap-1 relative group\">\n                      <div\n                        className={\n                          header.column.getCanSort()\n                            ? \"flex items-center gap-1 cursor-pointer select-none group\"\n                            : \"flex items-center gap-1\"\n                        }\n                        onClick={header.column.getToggleSortingHandler?.()}\n                        role={header.column.getCanSort() ? \"button\" : undefined}\n                        tabIndex={header.column.getCanSort() ? 0 : undefined}\n                        aria-label={\n                          header.column.getCanSort()\n                            ? `Sort by ${(header.column.columnDef.header as string) || \"column\"}`\n                            : undefined\n                        }\n                        onKeyDown={e => {\n                          if (!header.column.getCanSort()) return;\n                          if (e.key === \"Enter\" || e.key === \" \") {\n                            header.column.toggleSorting();\n                          }\n                        }}\n                      >\n                        {flexRender(header.column.columnDef.header, header.getContext())}\n                        {header.column.getCanSort() && (\n                          header.column.getIsSorted() === \"asc\" ? (\n                            <ArrowUp className=\"w-4 h-4 text-muted-foreground ml-1\" />\n                          ) : header.column.getIsSorted() === \"desc\" ? (\n                            <ArrowDown className=\"w-4 h-4 text-muted-foreground ml-1\" />\n                          ) : (\n                            <ArrowUpDown className=\"w-4 h-4 text-muted-foreground ml-1 opacity-50 group-hover:opacity-100 transition-opacity\" />\n                          )\n                        )}\n                      </div>\n                      {/* Per-column filter */}\n                      {showFilters && header.column.getCanFilter() && (\n                        <DebouncedColumnFilterInput\n                          value={(header.column.getFilterValue() ?? \"\") as string}\n                          onChange={(v: string) => header.column.setFilterValue(v)}\n                          placeholder={`Filter by ${(header.column.columnDef.header as string) || \"\"}`}\n                        />\n                      )}\n                      {/* Column resizer */}\n                      {header.column.getCanResize?.() && (\n                        <div\n                          onMouseDown={header.getResizeHandler()}\n                          onTouchStart={header.getResizeHandler()}\n                          className=\"absolute right-0 top-0 h-full w-1.5 cursor-col-resize select-none bg-transparent group-hover:bg-primary/30 transition-colors\"\n                          style={{ userSelect: 'none', touchAction: 'none' }}\n                        />\n                      )}\n                    </div>\n                  </TableHead>\n                ))}\n              </TableRow>\n            ))}\n          </TableHeader>\n          <TableBody>\n            {isLoading || isFetching ? (\n              <TableRow>\n                <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)}>\n                  <TableSkeleton\n                    rowCount={10}\n                    columnCount={4}\n                    hasTitle={true}\n                    hasSearch={true}\n                    hasFilters={true}\n                    hasPagination={true}\n                    hasActions={true}\n                  />\n                </TableCell>\n              </TableRow>\n            ) : table?.getRowModel?.()?.rows?.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)} className=\"text-center\">No data</TableCell>\n              </TableRow>\n            ) : (\n              table?.getRowModel?.()?.rows?.map(row => (\n                <React.Fragment key={row.id}>\n                  <TableRow\n                    data-state={row.getIsSelected() ? \"selected\" : undefined}\n                    className={\n                      striped && row.index % 2 === 1 ? \"bg-muted/50\" : undefined\n                    }\n                  >\n                    {enableRowSelection && isTableReady && (\n                      <TableCell>\n                        <Checkbox\n                          checked={getSafeIsSelected(row)}\n                          onCheckedChange={getSafeToggleSelectedHandler(row)}\n                          aria-label=\"Select row\"\n                        />\n                      </TableCell>\n                    )}\n                    {row.getVisibleCells().map(cell => (\n                      <TableCell key={cell.id}>\n                        <EditableCell cell={cell} />\n                      </TableCell>\n                    ))}\n                  </TableRow>\n                  {enableRowExpansion && row.getIsExpanded() && renderExpandedRow && (\n                    <TableRow>\n                      <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)}>\n                        {renderExpandedRow(row)}\n                      </TableCell>\n                    </TableRow>\n                  )}\n                </React.Fragment>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </div>\n      {/* Pagination */}\n      <div\n        data-slot=\"data-grid-pagination\"\n        className={\n          \"flex flex-wrap flex-col sm:flex-row justify-between items-center gap-2.5 py-2.5 sm:py-0 grow\"\n        }\n      >\n        <div className=\"flex flex-wrap items-center space-x-2.5 py-3 px-3\">\n          {isLoading ? (\n            <TableSkeleton\n              rowCount={10}\n              columnCount={4}\n              hasTitle={true}\n              hasSearch={true}\n              hasFilters={true}\n              hasPagination={true}\n              hasActions={true}\n            />\n          ) : (\n            <>\n              <div className=\"text-sm text-muted-foreground\">Rows per page</div>\n              <Select\n                value={`${pageSize}`}\n                indicatorPosition=\"right\"\n                onValueChange={(value) => {\n                  const newPageSize = Number(value);\n                  setPageSize(newPageSize);\n                  setPageIndex(0);\n                }}\n              >\n                <SelectTrigger className=\"w-fit\" size=\"sm\">\n                  <SelectValue placeholder={`${pageSize}`} />\n                </SelectTrigger>\n                <SelectContent side=\"top\" className=\"min-w-[50px]\">\n                  {[5, 10, 25, 50, 100].map((size) => (\n                    <SelectItem key={size} value={`${size}`}>{size}</SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </>\n          )}\n        </div>\n        <div className=\"flex flex-col sm:flex-row justify-center sm:justify-end items-center gap-2.5 pt-2.5 sm:pt-0 order-1 sm:order-2\">\n          {isLoading ? (\n            <TableSkeleton\n              rowCount={10}\n              columnCount={4}\n              hasTitle={true}\n              hasSearch={true}\n              hasFilters={true}\n              hasPagination={true}\n              hasActions={true}\n            />\n          ) : (\n            <>\n              <div className=\"text-sm text-muted-foreground text-nowrap order-2 sm:order-1\">\n                {`${pageIndex * pageSize + 1} - ${Math.min((pageIndex + 1) * pageSize, safeData.totalCount)} of ${safeData.totalCount}`}\n              </div>\n              {table?.getPageCount?.() > 1 && (\n                <div className=\"flex items-center space-x-1 order-1 sm:order-2\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"ghost\"\n                    className=\"size-7 p-0 text-sm rtl:transform rtl:rotate-180\"\n                    onClick={() => table?.previousPage?.()}\n                    disabled={!table?.getCanPreviousPage?.()}\n                  >\n                    <span className=\"sr-only\">Go to previous page</span>\n                    <ChevronLeftIcon className=\"size-4\" />\n                  </Button>\n                  {/* Page buttons with ellipsis */}\n                  {(() => {\n                    const pageCount = table?.getPageCount?.() ?? 0;\n                    const paginationMoreLimit = 5;\n                    const currentGroupStart = Math.floor(pageIndex / paginationMoreLimit) * paginationMoreLimit;\n                    const currentGroupEnd = Math.min(currentGroupStart + paginationMoreLimit, pageCount);\n                    const buttons = [];\n                    if (currentGroupStart > 0) {\n                      buttons.push(\n                        <Button\n                          key=\"ellipsis-prev\"\n                          size=\"sm\"\n                          className=\"size-7 p-0 text-sm\"\n                          variant=\"ghost\"\n                          onClick={() => table?.setPageIndex?.(currentGroupStart - 1)}\n                        >\n                          ...\n                        </Button>\n                      );\n                    }\n                    for (let i = currentGroupStart; i < currentGroupEnd; i++) {\n                      buttons.push(\n                        <Button\n                          key={i}\n                          size=\"sm\"\n                          variant=\"ghost\"\n                          className={\n                            `size-7 p-0 text-sm text-muted-foreground${pageIndex === i ? ' bg-accent text-accent-foreground' : ''}`\n                          }\n                          onClick={() => {\n                            if (pageIndex !== i) {\n                              table?.setPageIndex?.(i);\n                            }\n                          }}\n                        >\n                          {i + 1}\n                        </Button>\n                      );\n                    }\n                    if (currentGroupEnd < pageCount) {\n                      buttons.push(\n                        <Button\n                          key=\"ellipsis-next\"\n                          className=\"size-7 p-0 text-sm\"\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => table?.setPageIndex?.(currentGroupEnd)}\n                        >\n                          ...\n                        </Button>\n                      );\n                    }\n                    return buttons;\n                  })()}\n                  <Button\n                    size=\"sm\"\n                    variant=\"ghost\"\n                    className=\"size-7 p-0 text-sm rtl:transform rtl:rotate-180\"\n                    onClick={() => table?.nextPage?.()}\n                    disabled={!table?.getCanNextPage?.()}\n                  >\n                    <span className=\"sr-only\">Go to next page</span>\n                    <ChevronRightIcon className=\"size-4\" />\n                  </Button>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Debounced input for column filters\nconst DebouncedColumnFilterInput: React.FC<{ value: string; onChange: (v: string) => void; placeholder?: string }> = ({ value, onChange, placeholder }) => {\n  const [inputValue, setInputValue] = React.useState(value);\n  const debouncedValue = useDebounce(inputValue, 300);\n  React.useEffect(() => {\n    setInputValue(value);\n  }, [value]);\n  React.useEffect(() => {\n    if (debouncedValue !== value) {\n      onChange(debouncedValue);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [debouncedValue]);\n  return (\n    <Input\n      value={inputValue}\n      onChange={e => setInputValue(e.target.value)}\n      placeholder={placeholder}\n      className=\"w-full h-7 text-xs font-normal\"\n    />\n  );\n};\n\n"], "names": ["__iconNode", "AlignCenter", "createLucideIcon", "AlignJustify", "AlignRight", "Eye", "ListFilter", "PenLine", "densityIcons", "jsx", "DataGrid", "columns", "queryFn", "query<PERSON><PERSON>", "defaultPageSize", "enableRowSelection", "enableRowExpansion", "renderExpandedRow", "onCreate", "createModalContent", "onRefresh", "title", "initialState", "manualSorting", "manualFiltering", "rowIdAccessor", "striped", "enableInlineEditing", "onRowUpdate", "editableColumns", "autoSizeColumns", "meta", "pageIndex", "setPageIndex", "useState", "pageSize", "setPageSize", "sorting", "setSorting", "columnFilters", "setColumnFilters", "rowSelection", "setRowSelection", "expanded", "setExpanded", "density", "setDensity", "cookie", "Cookies", "showFilters", "setShowFilters", "showColumnVisibility", "setShowColumnVisibility", "globalFilter", "setGlobalFilter", "debouncedGlobalFilter", "useDebounce", "columnVisibility", "setColumnVisibility", "showCreateModal", "setShowCreateModal", "refresh<PERSON><PERSON>", "setRefresh<PERSON>ey", "editingCell", "setEditingCell", "editingValue", "setEditingValue", "isUpdating", "setIsUpdating", "data", "isLoading", "isFetching", "useQuery", "safeData", "table", "useReactTable", "getCoreRowModel", "getPaginationRowModel", "getSortedRowModel", "getFilteredRowModel", "getExpandedRowModel", "updater", "next", "originalRow", "handleAutoSizeColumns", "React", "tableElement", "headerCells", "dataCells", "columnWidths", "cell", "index", "columnId", "headerWidth", "column", "colIndex", "max<PERSON><PERSON><PERSON>", "sampleSize", "rowIndex", "cellIndex", "cellWidth", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "finalWidth", "width", "prev", "densityClass", "handleDensityCycle", "handleRefresh", "k", "handleCreate", "handleCellEdit", "rowId", "value", "handleCellSave", "item", "handleCellCancel", "handleCellKeyDown", "e", "isTableReady", "EditableCell", "isEditing", "isEditable", "cellValue", "jsxs", "Input", "getSafeIsAllRowsSelected", "getSafeToggleAllRowsSelectedHandler", "checked", "handler", "getSafeIsSelected", "row", "getSafeToggleSelectedHandler", "getSafeIsVisible", "col", "getSafeToggleVisibility", "Popover", "<PERSON><PERSON>", "v", "Checkbox", "f", "RefreshCw", "Edit3", "Plus", "Dialog", "Table", "TableHeader", "headerGroup", "TableRow", "TableHead", "header", "flexRender", "ArrowUp", "ArrowDown", "ArrowUpDown", "DebouncedColumnFilterInput", "TableBody", "TableCell", "TableSkeleton", "Fragment", "Select", "newPageSize", "SelectTrigger", "SelectValue", "SelectContent", "size", "SelectItem", "ChevronLeftIcon", "pageCount", "paginationMoreLimit", "currentGroupStart", "currentGroupEnd", "buttons", "i", "ChevronRightIcon", "onChange", "placeholder", "inputValue", "setInputValue", "debounced<PERSON><PERSON><PERSON>"], "mappings": "uvBAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMC,GAAcC,EAAiB,eAAgBF,EAAU,ECd/D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMG,GAAeD,EAAiB,gBAAiBF,EAAU,ECdjE;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAU,CAAA,CAC1C,EACMI,GAAaF,EAAiB,cAAeF,EAAU,ECd7D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CACE,OACA,CACE,EAAG,wGACH,IAAK,QACX,CACG,EACD,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,IAAK,IAAK,QAAU,CAAA,CAC1D,EACMK,GAAMH,EAAiB,MAAOF,EAAU,ECnB9C;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAQ,CAAE,EACxC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMM,GAAaJ,EAAiB,cAAeF,EAAU,ECd7D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CACE,OACA,CACE,EAAG,oIACH,IAAK,QACX,CACA,CACA,EACMO,GAAUL,EAAiB,WAAYF,EAAU,EC8CjDQ,GAAe,CACnB,OAAQC,EAAAA,IAACN,GAAa,CAAA,UAAU,SAAU,CAAA,EAC1C,MAAOM,EAAAA,IAACR,GAAY,CAAA,UAAU,SAAU,CAAA,EACxC,OAAQQ,EAAAA,IAACL,GAAW,CAAA,UAAU,SAAU,CAAA,CAC1C,EAEO,SAASM,GAAkC,CAChD,QAAAC,EACA,QAAAC,EACA,SAAAC,EACA,gBAAAC,EAAkB,GAClB,mBAAAC,EAAqB,GACrB,mBAAAC,EAAqB,GACrB,kBAAAC,EACA,SAAAC,EACA,mBAAAC,EACA,UAAAC,EACA,MAAAC,GACA,aAAAC,GACA,cAAAC,GAAgB,GAChB,gBAAAC,GAAkB,GAClB,cAAAC,EACA,QAAAC,GAAU,GACV,oBAAAC,EAAsB,GACtB,YAAAC,EACA,gBAAAC,EAAkB,CAAC,EACnB,gBAAAC,EAAkB,GAClB,KAAAC,EACF,EAAiC,CAE/B,KAAM,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAAS,CAAC,EACtC,CAACC,EAAUC,CAAW,EAAIF,EAAAA,SAASpB,CAAe,EAClD,CAACuB,EAASC,EAAU,EAAIJ,EAAAA,SAAuB,CAAA,CAAE,EACjD,CAACK,EAAeC,EAAgB,EAAIN,EAAAA,SAA6B,CAAA,CAAE,EACnE,CAACO,GAAcC,EAAe,EAAIR,EAAAA,SAAkC,CAAA,CAAE,EACtE,CAACS,GAAUC,EAAW,EAAIV,EAAAA,SAAwB,CAAA,CAAE,EACpD,CAACW,EAASC,EAAU,EAAIZ,WAAkB,IAAM,CAC9C,MAAAa,EAASC,EAAQ,IAAI,iBAAiB,EAC5C,OAAID,IAAW,UAAYA,IAAW,SAAWA,IAAW,SAAiBA,EACtE,QAAA,CACR,EACK,CAACE,EAAaC,EAAc,EAAIhB,EAAAA,SAAS,EAAK,EAC9C,CAACiB,EAAsBC,CAAuB,EAAIlB,EAAAA,SAAS,EAAK,EAChE,CAACmB,EAAcC,EAAe,EAAIpB,EAAAA,SAAS,EAAE,EAC7CqB,GAAwBC,GAAYH,EAAc,GAAG,EACrD,CAACI,GAAkBC,EAAmB,EAAIxB,EAAAA,SAA0B,CAAA,CAAE,EACtE,CAACyB,GAAiBC,EAAkB,EAAI1B,EAAAA,SAAS,EAAK,EACtD,CAAC2B,GAAYC,EAAa,EAAI5B,EAAAA,SAAS,CAAC,EAGxC,CAAC6B,EAAaC,CAAc,EAAI9B,EAAAA,SAAqD,IAAI,EACzF,CAAC+B,GAAcC,CAAe,EAAIhC,EAAAA,SAAiB,EAAE,EACrD,CAACiC,GAAYC,EAAa,EAAIlC,EAAAA,SAAS,EAAK,EAG5C,CAAE,KAAAmC,GAAM,UAAAC,EAAW,WAAAC,EAAA,EAAeC,GAAiD,CACvF,SAAU,CACR,GAAG3D,EACH,CAAE,UAAAmB,EAAW,SAAAG,EAAU,QAAAE,EAAS,cAAAE,EAAe,aAAcgB,GAAuB,WAAAM,EAAW,CACjG,EACA,QAAS,IACPjD,EAAQ,CACN,UAAAoB,EACA,SAAAG,EACA,QAAAE,EACA,QAASE,EACT,aAAcgB,EACf,CAAA,CAAA,CACJ,EACKkB,EAAWJ,IAAQ,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,EAG9CK,EAAQC,GAAc,CAC1B,KAAMF,EAAS,MACf,QAAA9D,EACA,UAAW,KAAK,KAAK8D,EAAS,WAAatC,CAAQ,EACnD,MAAO,CACL,WAAY,CAAE,UAAAH,EAAW,SAAAG,CAAS,EAClC,QAAAE,EACA,cAAAE,EACA,aAAAE,GACA,SAAAE,GACA,aAAAU,EACA,iBAAAI,EACF,EACA,iBAAkB,GAClB,cAAAlC,GACA,gBAAAC,GACA,gBAAiBoD,GAAgB,EACjC,sBAAuBC,GAAsB,EAC7C,kBAAmBC,GAAkB,EACrC,oBAAqBC,GAAoB,EACzC,oBAAqBC,GAAoB,EACzC,mBAAqBC,GAAY,CACzB,MAAAC,EAAO,OAAOD,GAAY,WAAaA,EAAQ,CAAE,UAAAjD,EAAW,SAAAG,CAAU,CAAA,EAAI8C,EAChFhD,EAAaiD,EAAK,SAAS,EAC3B9C,EAAY8C,EAAK,QAAQ,CAC3B,EACA,gBAAiB5C,GACjB,sBAAuBE,GACvB,qBAAsBE,GACtB,iBAAkBE,GAClB,qBAAsBU,GACtB,yBAA0BI,GAC1B,GAAGpC,GACH,SAAUG,EACL0D,GAAgB,OAAO1D,EAAc0D,CAAW,CAAC,EAClD,OACJ,mBAAApE,EACA,gBAAiBC,EACjB,qBAAsB,GACtB,iBAAkB,WAClB,KAAAe,EAAA,CACD,EAGKqD,GAAwBC,EAAM,YAAY,IAAM,CACpD,GAAI,CAACvD,GAAmB,CAAC2C,EAAS,MAAM,OAAQ,OAE1C,MAAAa,EAAe,SAAS,cAAc,cAAc,EAC1D,GAAI,CAACA,EAAc,OAEb,MAAAC,EAAcD,EAAa,iBAAiB,IAAI,EAChDE,EAAYF,EAAa,iBAAiB,IAAI,EAG9CG,MAAmB,IAGbF,EAAA,QAAQ,CAACG,EAAMC,IAAU,CACnC,MAAMC,EAAWlB,EAAM,cAAc,EAAEiB,CAAK,GAAG,GAC/C,GAAIC,EAAU,CACZ,MAAMC,EAAcH,EAAK,YACZD,EAAA,IAAIG,EAAU,KAAK,IAAIH,EAAa,IAAIG,CAAQ,GAAK,EAAGC,CAAW,CAAC,CAAA,CACnF,CACD,EAGKlF,MAAAA,EAAU+D,EAAM,cAAc,EACpC/D,EAAQ,QAAQ,CAACmF,EAAQC,IAAa,CACpC,MAAMH,EAAWE,EAAO,GACxB,IAAIE,EAAWP,EAAa,IAAIG,CAAQ,GAAK,EAG7C,MAAMK,GAAa,KAAK,IAAI,GAAIxB,EAAS,MAAM,MAAM,EACrD,QAASyB,EAAW,EAAGA,EAAWD,GAAYC,IAAY,CAClD,MAAAC,GAAYD,EAAWvF,EAAQ,OAASoF,EAC1C,GAAAP,EAAUW,EAAS,EAAG,CAClB,MAAAC,GAAYZ,EAAUW,EAAS,EAAE,YAC5BH,EAAA,KAAK,IAAIA,EAAUI,EAAS,CAAA,CACzC,CAIUJ,GAAA,GAGN,MAAAK,GAAWP,EAAO,UAAU,SAAW,GACvCQ,GAAkBR,EAAO,UAAU,SAAW,IAC9CS,GAAa,KAAK,IAAIF,GAAU,KAAK,IAAIL,EAAUM,EAAe,CAAC,EAE5Db,EAAA,IAAIG,EAAUW,EAAU,CAAA,CACtC,EAGD5F,EAAQ,QAAkBmF,GAAA,CACxB,MAAMU,EAAQf,EAAa,IAAIK,EAAO,EAAE,EACpCU,GAASV,EAAO,gBAElBpB,EAAM,gBAAyB+B,IAAA,CAC7B,GAAGA,EACH,CAACX,EAAO,EAAE,EAAGU,CAAA,EACb,CACJ,CACD,GACA,CAAC1E,EAAiB2C,EAAS,MAAOC,CAAK,CAAC,EAG3CW,EAAM,UAAU,IAAM,CAChBvD,GAAmB2C,EAAS,MAAM,OAAS,GAE7C,WAAWW,GAAuB,GAAG,GAEtC,CAACtD,EAAiB2C,EAAS,MAAOW,EAAqB,CAAC,EAG3D,MAAMsB,GACJ7D,IAAY,SACR,sCACAA,IAAY,QACV,sCACA,oCAGF8D,GAAqB,IAAM,CAC/B7D,GAAY2D,GAAS,CACnB,MAAMvB,EAAOuB,IAAS,SAAW,QAAUA,IAAS,QAAU,SAAW,SACzEzD,OAAAA,EAAQ,IAAI,kBAAmBkC,EAAM,CAAE,QAAS,IAAK,EAC9CA,CAAA,CACR,CACH,EAGAG,EAAM,UAAU,IAAM,CACpBrC,EAAQ,IAAI,kBAAmBH,EAAS,CAAE,QAAS,IAAK,CAAA,EACvD,CAACA,CAAO,CAAC,EAGZ,MAAM+D,GAAgB,IAAM,CACtBxF,EAAqBA,EAAA,EACN0C,GAAC+C,GAAMA,EAAI,CAAC,CACjC,EAGMC,GAAe,IAAM,CACrB3F,KAAuC,EAAI,EACtCD,GAAmBA,EAAA,CAC9B,EAGM6F,GAAiB,CAACC,EAAepB,EAAkBqB,IAAkB,CACrE,CAACtF,GAAuB,CAACE,EAAgB,SAAS+D,CAAQ,IAE/C5B,EAAA,CAAE,MAAAgD,EAAO,SAAApB,EAAU,EAClC1B,EAAgB+C,CAAK,EACvB,EAEMC,GAAiB,SAAY,CAC7B,GAAA,GAACnD,GAAe,CAACnC,GAEjB,GAAA,CACFwC,GAAc,EAAI,EAClB,MAAM4C,EAAQvF,EACVA,EAAcgD,EAAS,MAAM,KAAa0C,GAAA,OAAO1F,EAAc0F,CAAI,CAAC,IAAMpD,EAAY,KAAK,CAAU,EACrGA,EAAY,MAEV,MAAAnC,EAAYoF,EAAO,CAAE,CAACjD,EAAY,QAAQ,EAAGE,GAAgC,EACnFD,EAAe,IAAI,EACnBE,EAAgB,EAAE,EACJ0C,GAAA,OACA,CAAA,QAEd,CACAxC,GAAc,EAAK,CAAA,CAEvB,EAEMgD,GAAmB,IAAM,CAC7BpD,EAAe,IAAI,EACnBE,EAAgB,EAAE,CACpB,EAEMmD,GAAqBC,GAA2B,CAChDA,EAAE,MAAQ,SACZA,EAAE,eAAe,EACFJ,GAAA,GACNI,EAAE,MAAQ,WACnBA,EAAE,eAAe,EACAF,GAAA,EAErB,EAGMG,EAAelC,EAAM,QAAQ,IAC1BX,GACL,OAAOA,EAAM,sBAAyB,YACtC,OAAOA,EAAM,iCAAoC,YACjD,OAAOA,EAAM,iBAAoB,YACjC,OAAOA,EAAM,aAAgB,YAC7BD,EAAS,OACTA,EAAS,MAAM,OAAS,EACzB,CAACC,EAAOD,EAAS,KAAK,CAAC,EAGpB+C,GAAe,CAAC,CAAE,KAAA9B,KAA2C,CAC3D,MAAA+B,EAAY1D,GAAa,QAAU2B,EAAK,IAAI,IAAM3B,GAAa,WAAa2B,EAAK,OAAO,GACxFgC,EAAa/F,GAAuBE,EAAgB,SAAS6D,EAAK,OAAO,EAAE,EAC3EiC,EAAYjC,EAAK,SAAS,EAEhC,OAAI+B,EAEAG,EAAA,KAAC,MAAI,CAAA,UAAU,WACb,SAAA,CAAAnH,EAAA,IAACoH,EAAA,CACC,MAAO5D,GACP,SAAWqD,GAAMpD,EAAgBoD,EAAE,OAAO,KAAK,EAC/C,UAAWD,GACX,OAAQH,GACR,UAAU,cACV,UAAS,GACT,SAAU/C,EAAA,CACZ,EACCA,UACE,MAAI,CAAA,UAAU,qEACb,SAAC1D,EAAAA,IAAA,MAAA,CAAI,UAAU,6DAA8D,CAAA,CAC/E,CAAA,CAAA,EAEJ,EAKFA,EAAA,IAAC,MAAA,CACC,UAAW,kCAAkCiH,EAAa,sDAAwD,EAAE,GACpH,QAAS,IAAMA,GAAcX,GAAerB,EAAK,IAAI,GAAIA,EAAK,OAAO,GAAIiC,GAAa,EAAE,EACxF,MAAOD,EAAa,gBAAkB,OAErC,YAAWhC,EAAK,OAAO,UAAU,KAAMA,EAAK,WAAY,CAAA,CAAA,CAC3D,CAEJ,EAGMoC,GAA2B,IAAM,CACjC,GAAA,CAEF,OAAKP,EAGE7C,EAAM,0BAA4B,GAFhC,EAEgC,MACnC,CACC,MAAA,EAAA,CAEX,EAEMqD,GAAsC,IAClCC,GAAqB,CACvB,GAAA,CAEF,GAAI,CAACT,EACH,OAEI,MAAAU,EAAUvD,EAAM,kCAAkC,EACpDuD,KAAiBD,CAAO,OACd,CAAA,CAGlB,EAGIE,GAAqBC,GAAoB,CACzC,GAAA,CAEF,OAAKZ,EAGEY,GAAK,mBAAqB,GAFxB,EAEwB,MAC3B,CACC,MAAA,EAAA,CAEX,EAEMC,GAAgCD,GAC5BH,GAAqB,CACvB,GAAA,CAEF,GAAI,CAACT,EACH,OAEI,MAAAU,EAAUE,GAAK,2BAA2B,EAC5CF,KAAiBD,CAAO,OACd,CAAA,CAGlB,EAGIK,GAAoBC,GAA0C,CAC9D,GAAA,CACK,OAAAA,GAAK,kBAAoB,EAAA,MAC1B,CACC,MAAA,EAAA,CAEX,EAEMC,GAA2BD,GACxB,IAAM,CACP,GAAA,CACFA,GAAK,mBAAmB,OACV,CAAA,CAGlB,EAKA,OAAAV,EAAA,KAAC,MAAI,CAAA,UAAU,SAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8CAEb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACnH,EAAA,IAAA,MAAA,CAAI,UAAU,0CAA2C,SAAMY,GAAA,EAChEZ,EAAAA,IAAC,MAAI,CAAA,UAAU,uCAAwC,CAAA,CAAA,EACzD,EAEAmH,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAnH,EAAA,IAACoH,EAAA,CACC,YAAY,mBACZ,MAAOxE,EACP,SAAUiE,GAAKhE,GAAgBgE,EAAE,OAAO,KAAK,EAC7C,UAAU,MAAA,CACZ,EACCM,EAAA,KAAAY,GAAA,CAAQ,KAAMrF,EAAsB,aAAcC,EACjD,SAAA,CAAA3C,EAAA,IAACgI,EAAA,CACC,KAAK,KACL,QAAQ,UACR,QAAS,IAAMrF,EAAwBsF,GAAK,CAACA,CAAC,EAC9C,aAAW,oBAEX,SAAAjI,EAAAA,IAACJ,GAAI,CAAA,UAAU,cAAe,CAAA,CAAA,CAChC,EACC8C,GACCyE,EAAA,KAAC,MAAI,CAAA,UAAU,wEACb,SAAA,CAACnH,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAc,iBAAA,EACjDiE,GAAO,uBAAuB,OAC5BkD,OAAA,MAAA,CAAiB,UAAU,+BAC1B,SAAA,CAAAnH,EAAA,IAACkI,EAAA,CACC,QAASN,GAAiBC,CAAG,EAC7B,gBAAiBC,GAAwBD,CAAG,EAC5C,GAAI,UAAUA,EAAI,EAAE,EAAA,CACtB,EACA7H,EAAAA,IAAC,SAAM,QAAS,UAAU6H,EAAI,EAAE,GAAK,SAAIA,EAAA,UAAU,MAAiB,CAAA,CAAA,CAN5D,EAAAA,EAAI,EAOd,CACD,CAAA,CACH,CAAA,CAAA,EAEJ,EACA7H,EAAA,IAACgI,EAAA,CACC,KAAK,KACL,QAASxF,EAAc,UAAY,UACnC,QAAS,IAAMC,GAAe0F,GAAK,CAACA,CAAC,EACrC,aAAW,iBAEX,SAAAnI,EAAAA,IAACH,GAAW,CAAA,UAAU,cAAe,CAAA,CAAA,CACvC,EACAG,EAAA,IAACgI,EAAA,CACC,KAAK,KACL,QAAQ,UACR,QAAS9B,GACT,aAAW,UAEV,YAAa9D,CAAO,CAAA,CACvB,EACApC,EAAA,IAACgI,EAAA,CACC,KAAK,KACL,QAAQ,UACR,QAAS7B,GACT,aAAW,UAEX,SAAAnG,EAAAA,IAACoI,GAAU,CAAA,UAAU,SAAU,CAAA,CAAA,CACjC,EACClH,GACClB,EAAA,IAACgI,EAAA,CACC,KAAK,KACL,QAAS1E,EAAc,UAAY,UACnC,QAAS,IAAM,CACTA,GACeqD,GAAA,CAErB,EACA,aAAW,iBACX,MAAOrD,EAAc,iBAAmB,yBAExC,SAAAtD,EAAAA,IAACqI,GAAM,CAAA,UAAU,SAAU,CAAA,CAAA,CAC7B,EAEFlB,EAAA,KAACa,EAAA,CACC,KAAK,KACL,QAAQ,UACR,QAAS3B,GACT,aAAW,MAEX,SAAA,CAACrG,EAAAA,IAAAsI,GAAA,CAAK,UAAU,cAAe,CAAA,EAAE,MAAA,CAAA,CAAA,CACnC,CACF,CAAA,CAAA,EACF,EAEC5H,GACEV,EAAAA,IAAAuI,GAAA,CAAO,KAAMrF,GAAiB,aAAcC,GAC1C,SACHzC,EAAA,EAGFV,EAAAA,IAAC,OAAI,UAAW,+BAA+BiG,EAAY,GACzD,SAAAkB,EAAA,KAACqB,GAAM,CAAA,aAAU,GACf,SAAA,CAAAxI,EAAAA,IAACyI,IACE,SAAOxE,GAAA,kBAAA,GAAqB,IAAIyE,UAC9BC,EACE,CAAA,SAAA,CAAArI,GAAsBwG,GACrB9G,MAAC4I,GAAU,CAAA,UAAU,MACnB,SAAA5I,EAAA,IAACkI,EAAA,CACC,QAASb,GAAyB,EAClC,gBAAiBC,GAAoC,EACrD,aAAW,YAAA,CAAA,EAEf,EAEDoB,EAAY,QAAQ,OAClB1I,EAAA,IAAA4I,GAAA,CAA0B,MAAO,CAAE,MAAOC,EAAO,SAAa,EAAA,UAAU,YACvE,SAAC1B,OAAA,MAAA,CAAI,UAAU,qCACb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,UACE0B,EAAO,OAAO,aACV,2DACA,0BAEN,QAASA,EAAO,OAAO,0BAA0B,EACjD,KAAMA,EAAO,OAAO,aAAe,SAAW,OAC9C,SAAUA,EAAO,OAAO,aAAe,EAAI,OAC3C,aACEA,EAAO,OAAO,WAAW,EACrB,WAAYA,EAAO,OAAO,UAAU,QAAqB,QAAQ,GACjE,OAEN,UAAgBhC,GAAA,CACTgC,EAAO,OAAO,eACfhC,EAAE,MAAQ,SAAWA,EAAE,MAAQ,MACjCgC,EAAO,OAAO,cAAc,CAEhC,EAEC,SAAA,CAAAC,GAAWD,EAAO,OAAO,UAAU,OAAQA,EAAO,YAAY,EAC9DA,EAAO,OAAO,WACb,IAAAA,EAAO,OAAO,YAAA,IAAkB,MAC7B7I,EAAA,IAAA+I,GAAA,CAAQ,UAAU,oCAAqC,CAAA,EACtDF,EAAO,OAAO,YAAA,IAAkB,OACjC7I,EAAAA,IAAAgJ,GAAA,CAAU,UAAU,oCAAqC,CAAA,EAE1DhJ,EAAAA,IAACiJ,GAAY,CAAA,UAAU,0FAA2F,CAAA,EAAA,CAAA,CAGxH,EAECzG,GAAeqG,EAAO,OAAO,aAC5B,GAAA7I,EAAA,IAACkJ,GAAA,CACC,MAAQL,EAAO,OAAO,eAAoB,GAAA,GAC1C,SAAWZ,GAAcY,EAAO,OAAO,eAAeZ,CAAC,EACvD,YAAa,aAAcY,EAAO,OAAO,UAAU,QAAqB,EAAE,EAAA,CAC5E,EAGDA,EAAO,OAAO,eAAA,GACb7I,EAAA,IAAC,MAAA,CACC,YAAa6I,EAAO,iBAAiB,EACrC,aAAcA,EAAO,iBAAiB,EACtC,UAAU,+HACV,MAAO,CAAE,WAAY,OAAQ,YAAa,MAAO,CAAA,CAAA,CACnD,EAEJ,CAAA,EAnDcA,EAAO,EAoDvB,CACD,CAhEY,CAAA,EAAAH,EAAY,EAiE3B,CACD,EACH,EACC1I,MAAAmJ,GAAA,CACE,SAAatF,GAAAC,GACX9D,EAAA,IAAA2I,EAAA,CACC,SAAC3I,EAAAA,IAAAoJ,EAAA,CAAU,QAASlJ,EAAQ,QAAUI,EAAqB,EAAI,GAC7D,SAAAN,EAAA,IAACqJ,EAAA,CACC,SAAU,GACV,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CAAA,CAEhB,CAAA,CACF,CAAA,EACEpF,GAAO,iBAAiB,MAAM,SAAW,EAC1CjE,EAAAA,IAAA2I,EAAA,CACC,SAAC3I,MAAAoJ,EAAA,CAAU,QAASlJ,EAAQ,QAAUI,EAAqB,EAAI,GAAI,UAAU,cAAc,SAAA,SAAO,CAAA,CACpG,CAAA,EAEA2D,GAAO,cAAc,GAAG,MAAM,IAAIyD,GAC/BP,EAAA,KAAAvC,EAAM,SAAN,CACC,SAAA,CAAAuC,EAAA,KAACwB,EAAA,CACC,aAAYjB,EAAI,cAAc,EAAI,WAAa,OAC/C,UACEzG,IAAWyG,EAAI,MAAQ,IAAM,EAAI,cAAgB,OAGlD,SAAA,CAAsBpH,GAAAwG,SACpBsC,EACC,CAAA,SAAApJ,EAAA,IAACkI,EAAA,CACC,QAAST,GAAkBC,CAAG,EAC9B,gBAAiBC,GAA6BD,CAAG,EACjD,aAAW,YAAA,CAAA,EAEf,EAEDA,EAAI,gBAAA,EAAkB,IACrBzC,GAAAjF,EAAA,IAACoJ,EACC,CAAA,SAAApJ,EAAA,IAAC+G,GAAa,CAAA,KAAA9B,CAAY,CAAA,CADZ,EAAAA,EAAK,EAErB,CACD,CAAA,CAAA,CACH,EACC1E,GAAsBmH,EAAI,cAAA,GAAmBlH,GAC5CR,MAAC2I,GACC,SAAC3I,EAAA,IAAAoJ,EAAA,CAAU,QAASlJ,EAAQ,QAAUI,EAAqB,EAAI,GAC5D,SAAkBE,EAAAkH,CAAG,EACxB,CACF,CAAA,CAAA,GA3BiBA,EAAI,EA6BzB,CACD,CAEL,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAP,EAAA,KAAC,MAAA,CACC,YAAU,uBACV,UACE,+FAGF,SAAA,CAACnH,EAAA,IAAA,MAAA,CAAI,UAAU,oDACZ,SACC6D,EAAA7D,EAAA,IAACqJ,EAAA,CACC,SAAU,GACV,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CAAA,EAIZlC,EAAA,KAAAmC,WAAA,CAAA,SAAA,CAACtJ,EAAA,IAAA,MAAA,CAAI,UAAU,gCAAgC,SAAa,gBAAA,EAC5DmH,EAAA,KAACoC,GAAA,CACC,MAAO,GAAG7H,CAAQ,GAClB,kBAAkB,QAClB,cAAgB8E,GAAU,CAClB,MAAAgD,EAAc,OAAOhD,CAAK,EAChC7E,EAAY6H,CAAW,EACvBhI,EAAa,CAAC,CAChB,EAEA,SAAA,CAACxB,EAAA,IAAAyJ,GAAA,CAAc,UAAU,QAAQ,KAAK,KACpC,SAACzJ,EAAA,IAAA0J,GAAA,CAAY,YAAa,GAAGhI,CAAQ,EAAI,CAAA,EAC3C,EACA1B,EAAAA,IAAC2J,GAAc,CAAA,KAAK,MAAM,UAAU,eACjC,SAAC,CAAA,EAAG,GAAI,GAAI,GAAI,GAAG,EAAE,IAAKC,GACzB5J,EAAAA,IAAC6J,GAAsB,CAAA,MAAO,GAAGD,CAAI,GAAK,SAAAA,CAAA,EAAzBA,CAA8B,CAChD,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CACF,CAEJ,CAAA,EACC5J,EAAA,IAAA,MAAA,CAAI,UAAU,iHACZ,SACC6D,EAAA7D,EAAA,IAACqJ,EAAA,CACC,SAAU,GACV,YAAa,EACb,SAAU,GACV,UAAW,GACX,WAAY,GACZ,cAAe,GACf,WAAY,EAAA,CAAA,EAIZlC,EAAA,KAAAmC,WAAA,CAAA,SAAA,CAAAtJ,EAAAA,IAAC,OAAI,UAAU,+DACZ,YAAGuB,EAAYG,EAAW,CAAC,MAAM,KAAK,KAAKH,EAAY,GAAKG,EAAUsC,EAAS,UAAU,CAAC,OAAOA,EAAS,UAAU,EACvH,CAAA,EACCC,GAAO,eAAe,EAAI,GACxBkD,EAAAA,KAAA,MAAA,CAAI,UAAU,iDACb,SAAA,CAAAA,EAAA,KAACa,EAAA,CACC,KAAK,KACL,QAAQ,QACR,UAAU,kDACV,QAAS,IAAM/D,GAAO,eAAe,EACrC,SAAU,CAACA,GAAO,qBAAqB,EAEvC,SAAA,CAACjE,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAmB,sBAAA,EAC7CA,EAAAA,IAAC8J,GAAgB,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CACtC,GAEE,IAAM,CACA,MAAAC,EAAY9F,GAAO,eAAA,GAAoB,EACvC+F,EAAsB,EACtBC,EAAoB,KAAK,MAAM1I,EAAYyI,CAAmB,EAAIA,EAClEE,EAAkB,KAAK,IAAID,EAAoBD,EAAqBD,CAAS,EAC7EI,EAAU,CAAC,EACbF,EAAoB,GACdE,EAAA,KACNnK,EAAA,IAACgI,EAAA,CAEC,KAAK,KACL,UAAU,qBACV,QAAQ,QACR,QAAS,IAAM/D,GAAO,eAAegG,EAAoB,CAAC,EAC3D,SAAA,KAAA,EALK,eAAA,CAQR,EAEF,QAASG,EAAIH,EAAmBG,EAAIF,EAAiBE,IAC3CD,EAAA,KACNnK,EAAA,IAACgI,EAAA,CAEC,KAAK,KACL,QAAQ,QACR,UACE,2CAA2CzG,IAAc6I,EAAI,oCAAsC,EAAE,GAEvG,QAAS,IAAM,CACT7I,IAAc6I,GAChBnG,GAAO,eAAemG,CAAC,CAE3B,EAEC,SAAIA,EAAA,CAAA,EAZAA,CAAA,CAcT,EAEF,OAAIF,EAAkBH,GACZI,EAAA,KACNnK,EAAA,IAACgI,EAAA,CAEC,UAAU,qBACV,QAAQ,QACR,KAAK,KACL,QAAS,IAAM/D,GAAO,eAAeiG,CAAe,EACrD,SAAA,KAAA,EALK,eAAA,CAQR,EAEKC,CAAA,GACN,EACHhD,EAAA,KAACa,EAAA,CACC,KAAK,KACL,QAAQ,QACR,UAAU,kDACV,QAAS,IAAM/D,GAAO,WAAW,EACjC,SAAU,CAACA,GAAO,iBAAiB,EAEnC,SAAA,CAACjE,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAe,kBAAA,EACzCA,EAAAA,IAACqK,GAAiB,CAAA,UAAU,QAAS,CAAA,CAAA,CAAA,CAAA,CACvC,CACF,CAAA,CAAA,CAAA,CAEJ,CAEJ,CAAA,CAAA,CAAA,CAAA,CACF,EACF,CAEJ,CAGA,MAAMnB,GAA+G,CAAC,CAAE,MAAA1C,EAAO,SAAA8D,EAAU,YAAAC,KAAkB,CACzJ,KAAM,CAACC,EAAYC,CAAa,EAAI7F,EAAM,SAAS4B,CAAK,EAClDkE,EAAiB3H,GAAYyH,EAAY,GAAG,EAClD5F,OAAAA,EAAM,UAAU,IAAM,CACpB6F,EAAcjE,CAAK,CAAA,EAClB,CAACA,CAAK,CAAC,EACV5B,EAAM,UAAU,IAAM,CAChB8F,IAAmBlE,GACrB8D,EAASI,CAAc,CACzB,EAEC,CAACA,CAAc,CAAC,EAEjB1K,EAAA,IAACoH,EAAA,CACC,MAAOoD,EACP,SAAU3D,GAAK4D,EAAc5D,EAAE,OAAO,KAAK,EAC3C,YAAA0D,EACA,UAAU,gCAAA,CACZ,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}