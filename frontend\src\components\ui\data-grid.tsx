import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Popover } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useDebounce } from "@/lib/hooks/useDebounce";
import { useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type Cell,
  type ColumnDef,
  type ColumnFiltersState,
  type ExpandedState,
  type Row,
  type SortingState,
  type TableOptions,
  type VisibilityState
} from "@tanstack/react-table";
import Cookies from "js-cookie";
import { AlignCenter, AlignJustify, AlignRight, ArrowDown, ArrowUp, ArrowUpDown, ChevronLeftIcon, ChevronRightIcon, Edit3, Eye, ListFilter, Plus, RefreshCw } from "lucide-react";
import React, { useState } from "react";
import { TableSkeleton } from "./TableSkeleton";

// Types
export interface DataGridProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  queryFn: (params: {
    pageIndex: number;
    pageSize: number;
    sorting?: SortingState;
    filters?: ColumnFiltersState;
    globalFilter?: string;
  }) => Promise<{ items: TData[]; totalCount: number }>;
  queryKey: unknown[];
  pageSizeOptions?: number[];
  defaultPageSize?: number;
  enableRowSelection?: boolean;
  enableRowExpansion?: boolean;
  renderExpandedRow?: (row: Row<TData>) => React.ReactNode;
  onCreate?: () => void;
  createModalContent?: React.ReactNode;
  onRefresh?: () => void;
  title?: string;
  initialState?: Partial<TableOptions<TData>>;
  manualSorting?: boolean;
  manualFiltering?: boolean;
  rowIdAccessor?: (row: TData) => string | number;
  striped?: boolean;
  enableInlineEditing?: boolean;
  enableNewButton?: boolean;
  onRowUpdate?: (rowId: string | number, updatedData: Partial<TData>) => Promise<void>;
  editableColumns?: string[];
  autoSizeColumns?: boolean; // New option for auto-sizing columns
  meta?: Record<string, unknown>; // Add meta support
}

type Density = "xsmall" | "small" | "medium";

const densityIcons = {
  xsmall: <AlignJustify className="w-4 h-4" />,
  small: <AlignCenter className="w-5 h-5" />,
  medium: <AlignRight className="w-6 h-6" />,
};

export function DataGrid<TData, TValue = unknown>({
  columns,
  queryFn,
  queryKey,
  defaultPageSize = 10,
  enableRowSelection = false,
  enableRowExpansion = false,
  renderExpandedRow,
  onCreate,
  createModalContent,
  onRefresh,
  title,
  initialState,
  manualSorting = true,
  manualFiltering = true,
  rowIdAccessor,
  striped = false,
  enableInlineEditing = false,
  enableNewButton = true,
  onRowUpdate,
  editableColumns = [],
  autoSizeColumns = false,
  meta,
}: DataGridProps<TData, TValue>) {
  // State
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [density, setDensity] = useState<Density>(() => {
    const cookie = Cookies.get("dataGridDensity");
    if (cookie === "xsmall" || cookie === "small" || cookie === "medium") return cookie;
    return "xsmall";
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showColumnVisibility, setShowColumnVisibility] = useState(false);
  const [globalFilter, setGlobalFilter] = useState("");
  const debouncedGlobalFilter = useDebounce(globalFilter, 300);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // Inline editing state
  const [editingCell, setEditingCell] = useState<{ rowId: string; columnId: string } | null>(null);
  const [editingValue, setEditingValue] = useState<string>("");
  const [isUpdating, setIsUpdating] = useState(false);

  // Data Fetching
  const { data, isLoading, isFetching } = useQuery<{ items: TData[]; totalCount: number }>({
    queryKey: [
      ...queryKey,
      { pageIndex, pageSize, sorting, columnFilters, globalFilter: debouncedGlobalFilter, refreshKey },
    ],
    queryFn: () =>
      queryFn({
        pageIndex,
        pageSize,
        sorting,
        filters: columnFilters,
        globalFilter: debouncedGlobalFilter,
      }),
  });
  const safeData = data ?? { items: [], totalCount: 0 };

  // Table Instance
  const table = useReactTable({
    data: safeData.items,
    columns,
    pageCount: Math.ceil(safeData.totalCount / pageSize),
    state: {
      pagination: { pageIndex, pageSize },
      sorting,
      columnFilters,
      rowSelection,
      expanded,
      globalFilter,
      columnVisibility,
    },
    manualPagination: true,
    manualSorting,
    manualFiltering,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    onPaginationChange: (updater) => {
      const next = typeof updater === "function" ? updater({ pageIndex, pageSize }) : updater;
      setPageIndex(next.pageIndex);
      setPageSize(next.pageSize);
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    onExpandedChange: setExpanded,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    ...initialState,
    getRowId: rowIdAccessor
      ? (originalRow) => String(rowIdAccessor(originalRow))
      : undefined,
    enableRowSelection,
    enableExpanding: enableRowExpansion,
    enableColumnResizing: true,
    columnResizeMode: 'onChange',
    meta,
  });

  // Auto-size columns function
  const handleAutoSizeColumns = React.useCallback(() => {
    if (!autoSizeColumns || !safeData.items.length) return;

    const tableElement = document.querySelector('[data-table]');
    if (!tableElement) return;

    const headerCells = tableElement.querySelectorAll('th');
    const dataCells = tableElement.querySelectorAll('td');

    // Calculate optimal widths for each column
    const columnWidths = new Map<string, number>();

    // Get header widths
    headerCells.forEach((cell, index) => {
      const columnId = table.getAllColumns()[index]?.id;
      if (columnId) {
        const headerWidth = cell.scrollWidth;
        columnWidths.set(columnId, Math.max(columnWidths.get(columnId) || 0, headerWidth));
      }
    });

    // Get data cell widths
    const columns = table.getAllColumns();
    columns.forEach((column, colIndex) => {
      const columnId = column.id;
      let maxWidth = columnWidths.get(columnId) || 0;

      // Check first few rows for content width
      const sampleSize = Math.min(10, safeData.items.length);
      for (let rowIndex = 0; rowIndex < sampleSize; rowIndex++) {
        const cellIndex = rowIndex * columns.length + colIndex;
        if (dataCells[cellIndex]) {
          const cellWidth = dataCells[cellIndex].scrollWidth;
          maxWidth = Math.max(maxWidth, cellWidth);
        }
      }

      // Add some padding
      maxWidth += 20;

      // Apply min/max constraints
      const minWidth = column.columnDef.minSize || 50;
      const maxAllowedWidth = column.columnDef.maxSize || 500;
      const finalWidth = Math.max(minWidth, Math.min(maxWidth, maxAllowedWidth));

      columnWidths.set(columnId, finalWidth);
    });

    // Apply the calculated widths
    columns.forEach(column => {
      const width = columnWidths.get(column.id);
      if (width && column.getCanResize()) {
        // Use the table's column sizing API
        table.setColumnSizing(prev => ({
          ...prev,
          [column.id]: width
        }));
      }
    });
  }, [autoSizeColumns, safeData.items, table]);

  // Auto-size columns when data changes
  React.useEffect(() => {
    if (autoSizeColumns && safeData.items.length > 0) {
      // Small delay to ensure DOM is updated
      setTimeout(handleAutoSizeColumns, 100);
    }
  }, [autoSizeColumns, safeData.items, handleAutoSizeColumns]);

  // Density classes
  const densityClass =
    density === "xsmall"
      ? "text-xs [&_td]:py-0.5 [&_th]:py-0.5"
      : density === "small"
        ? "text-sm [&_td]:py-1.5 [&_th]:py-1.5"
        : "text-base [&_td]:py-3 [&_th]:py-3";

  // Density cycle logic
  const handleDensityCycle = () => {
    setDensity((prev) => {
      const next = prev === "xsmall" ? "small" : prev === "small" ? "medium" : "xsmall";
      Cookies.set("dataGridDensity", next, { expires: 365 });
      return next;
    });
  };

  // Also update cookie if density is changed by other means
  React.useEffect(() => {
    Cookies.set("dataGridDensity", density, { expires: 365 });
  }, [density]);

  // Refresh logic
  const handleRefresh = () => {
    if (onRefresh) onRefresh();
    else setRefreshKey((k) => k + 1);
  };

  // New/Create logic
  const handleCreate = () => {
    if (createModalContent) setShowCreateModal(true);
    else if (onCreate) onCreate();
  };

  // Inline editing handlers
  const handleCellEdit = (rowId: string, columnId: string, value: string) => {
    if (!enableInlineEditing || !editableColumns.includes(columnId)) return;

    setEditingCell({ rowId, columnId });
    setEditingValue(value);
  };

  const handleCellSave = async () => {
    if (!editingCell || !onRowUpdate) return;

    try {
      setIsUpdating(true);
      const rowId = rowIdAccessor
        ? rowIdAccessor(safeData.items.find(item => String(rowIdAccessor(item)) === editingCell.rowId) as TData)
        : editingCell.rowId;

      await onRowUpdate(rowId, { [editingCell.columnId]: editingValue } as Partial<TData>);
      setEditingCell(null);
      setEditingValue("");
      handleRefresh(); // Refresh data after update
    } catch (error) {
      console.error('Error updating row:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCellCancel = () => {
    setEditingCell(null);
    setEditingValue("");
  };

  const handleCellKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleCellSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCellCancel();
    }
  };

  // Check if table is fully initialized
  const isTableReady = React.useMemo(() => {
    return table &&
      typeof table.getIsAllRowsSelected === 'function' &&
      typeof table.getToggleAllRowsSelectedHandler === 'function' &&
      typeof table.getHeaderGroups === 'function' &&
      typeof table.getRowModel === 'function' &&
      safeData.items &&
      safeData.items.length > 0;
  }, [table, safeData.items]);

  // Editable cell component
  const EditableCell = ({ cell }: { cell: Cell<TData, unknown> }) => {
    const isEditing = editingCell?.rowId === cell.row.id && editingCell?.columnId === cell.column.id;
    const isEditable = enableInlineEditing && editableColumns.includes(cell.column.id);
    const cellValue = cell.getValue() as string;

    if (isEditing) {
      return (
        <div className="relative">
          <Input
            value={editingValue}
            onChange={(e) => setEditingValue(e.target.value)}
            onKeyDown={handleCellKeyDown}
            onBlur={handleCellSave}
            className="h-8 text-sm"
            autoFocus
            disabled={isUpdating}
          />
          {isUpdating && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        className={`min-h-[2rem] flex items-center ${isEditable ? 'cursor-pointer hover:bg-muted/50 rounded px-1 -mx-1' : ''}`}
        onClick={() => isEditable && handleCellEdit(cell.row.id, cell.column.id, cellValue || '')}
        title={isEditable ? 'Click to edit' : undefined}
      >
        {flexRender(cell.column.columnDef.cell, cell.getContext())}
      </div>
    );
  };

  // Safe checkbox handlers
  const getSafeIsAllRowsSelected = () => {
    try {
      // Only allow selection if table is ready and has data
      if (!isTableReady) {
        return false;
      }
      return table.getIsAllRowsSelected?.() ?? false;
    } catch {
      return false;
    }
  };

  const getSafeToggleAllRowsSelectedHandler = () => {
    return (checked: boolean) => {
      try {
        // Only allow interaction if table is ready and has data
        if (!isTableReady) {
          return;
        }
        const handler = table.getToggleAllRowsSelectedHandler?.();
        if (handler) handler(checked);
      } catch (error) {
        console.warn('Error toggling all rows selection:', error);
      }
    };
  };

  const getSafeIsSelected = (row: Row<TData>) => {
    try {
      // Only allow selection if table is ready and has data
      if (!isTableReady) {
        return false;
      }
      return row?.getIsSelected?.() ?? false;
    } catch {
      return false;
    }
  };

  const getSafeToggleSelectedHandler = (row: Row<TData>) => {
    return (checked: boolean) => {
      try {
        // Only allow interaction if table is ready and has data
        if (!isTableReady) {
          return;
        }
        const handler = row?.getToggleSelectedHandler?.();
        if (handler) handler(checked);
      } catch (error) {
        console.warn('Error toggling row selection:', error);
      }
    };
  };

  const getSafeIsVisible = (col: { getIsVisible?: () => boolean }) => {
    try {
      return col?.getIsVisible?.() ?? true;
    } catch {
      return true;
    }
  };

  const getSafeToggleVisibility = (col: { toggleVisibility?: () => void }) => {
    return () => {
      try {
        col?.toggleVisibility?.();
      } catch (error) {
        console.warn('Error toggling column visibility:', error);
      }
    };
  };

  // UI
  return (
    <div className="w-full">
      {/* Toolbar */}
      <div className="flex items-center justify-between mb-2 px-1">
        {/* Left: Title */}
        <div>
          <div className="font-bold text-lg truncate max-w-[40vw]">{title}</div>
          <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
        </div>
        {/* Right: Actions */}
        <div className="flex flex-wrap items-center gap-2">
          <Input
            placeholder="Global search..."
            value={globalFilter}
            onChange={e => setGlobalFilter(e.target.value)}
            className="w-48"
          />
          <Popover open={showColumnVisibility} onOpenChange={setShowColumnVisibility}>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowColumnVisibility(v => !v)}
              aria-label="Show/Hide Columns"
            >
              <Eye className="w-4 h-4 mr-1" />
            </Button>
            {showColumnVisibility && (
              <div className="absolute z-50 mt-2 bg-popover border rounded shadow p-2 min-w-[180px]">
                <div className="font-semibold mb-2">Toggle columns</div>
                {table?.getAllLeafColumns?.()?.map(col => (
                  <div key={col.id} className="flex items-center gap-2 mb-1">
                    <Checkbox
                      checked={getSafeIsVisible(col)}
                      onCheckedChange={getSafeToggleVisibility(col)}
                      id={`colvis-${col.id}`}
                    />
                    <label htmlFor={`colvis-${col.id}`}>{col.columnDef.header as string}</label>
                  </div>
                ))}
              </div>
            )}
          </Popover>
          <Button
            size="sm"
            variant={showFilters ? "default" : "outline"}
            onClick={() => setShowFilters(f => !f)}
            aria-label="Toggle Filters"
          >
            <ListFilter className="w-4 h-4 mr-1" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleDensityCycle}
            aria-label="Density"
          >
            {densityIcons[density]}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={handleRefresh}
            aria-label="Refresh"
          >
            <RefreshCw className="w-4 h-4" />
          </Button>
          {enableInlineEditing && (
            <Button
              size="sm"
              variant={editingCell ? "default" : "outline"}
              onClick={() => {
                if (editingCell) {
                  handleCellCancel();
                }
              }}
              aria-label="Inline Editing"
              title={editingCell ? "Cancel editing" : "Inline editing enabled"}
            >
              <Edit3 className="w-4 h-4" />
            </Button>
          )}

          {enableNewButton && (
            <Button
              size="sm"
              variant="default"
              onClick={handleCreate}
              aria-label="New"
            >
              <Plus className="w-4 h-4 mr-1" /> New
            </Button>
          )}
        </div>
      </div>
      {/* Create Modal */}
      {createModalContent && (
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          {createModalContent}
        </Dialog>
      )}
      {/* Table */}
      <div className={`overflow-x-auto  rounded-xl ${densityClass}`}>
        <Table data-table>
          <TableHeader>
            {table?.getHeaderGroups?.()?.map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {enableRowSelection && isTableReady && (
                  <TableHead className="w-6">
                    <Checkbox
                      checked={getSafeIsAllRowsSelected()}
                      onCheckedChange={getSafeToggleAllRowsSelectedHandler()}
                      aria-label="Select all"
                    />
                  </TableHead>
                )}
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id} style={{ width: header.getSize() }} className="font-bold">
                    <div className="flex flex-col gap-1 relative group">
                      <div
                        className={
                          header.column.getCanSort()
                            ? "flex items-center gap-1 cursor-pointer select-none group"
                            : "flex items-center gap-1"
                        }
                        onClick={header.column.getToggleSortingHandler?.()}
                        role={header.column.getCanSort() ? "button" : undefined}
                        tabIndex={header.column.getCanSort() ? 0 : undefined}
                        aria-label={
                          header.column.getCanSort()
                            ? `Sort by ${(header.column.columnDef.header as string) || "column"}`
                            : undefined
                        }
                        onKeyDown={e => {
                          if (!header.column.getCanSort()) return;
                          if (e.key === "Enter" || e.key === " ") {
                            header.column.toggleSorting();
                          }
                        }}
                      >
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {header.column.getCanSort() && (
                          header.column.getIsSorted() === "asc" ? (
                            <ArrowUp className="w-4 h-4 text-muted-foreground ml-1" />
                          ) : header.column.getIsSorted() === "desc" ? (
                            <ArrowDown className="w-4 h-4 text-muted-foreground ml-1" />
                          ) : (
                            <ArrowUpDown className="w-4 h-4 text-muted-foreground ml-1 opacity-50 group-hover:opacity-100 transition-opacity" />
                          )
                        )}
                      </div>
                      {/* Per-column filter */}
                      {showFilters && header.column.getCanFilter() && (
                        <DebouncedColumnFilterInput
                          value={(header.column.getFilterValue() ?? "") as string}
                          onChange={(v: string) => header.column.setFilterValue(v)}
                          placeholder={`Filter by ${(header.column.columnDef.header as string) || ""}`}
                        />
                      )}
                      {/* Column resizer */}
                      {header.column.getCanResize?.() && (
                        <div
                          onMouseDown={header.getResizeHandler()}
                          onTouchStart={header.getResizeHandler()}
                          className="absolute right-0 top-0 h-full w-1.5 cursor-col-resize select-none bg-transparent group-hover:bg-primary/30 transition-colors"
                          style={{ userSelect: 'none', touchAction: 'none' }}
                        />
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading || isFetching ? (
              <TableRow>
                <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)}>
                  <TableSkeleton
                    rowCount={10}
                    columnCount={4}
                    hasTitle={true}
                    hasSearch={true}
                    hasFilters={true}
                    hasPagination={true}
                    hasActions={true}
                  />
                </TableCell>
              </TableRow>
            ) : table?.getRowModel?.()?.rows?.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)} className="text-center">No data</TableCell>
              </TableRow>
            ) : (
              table?.getRowModel?.()?.rows?.map(row => (
                <React.Fragment key={row.id}>
                  <TableRow
                    data-state={row.getIsSelected() ? "selected" : undefined}
                    className={
                      striped && row.index % 2 === 1 ? "bg-muted/50" : undefined
                    }
                  >
                    {enableRowSelection && isTableReady && (
                      <TableCell>
                        <Checkbox
                          checked={getSafeIsSelected(row)}
                          onCheckedChange={getSafeToggleSelectedHandler(row)}
                          aria-label="Select row"
                        />
                      </TableCell>
                    )}
                    {row.getVisibleCells().map(cell => (
                      <TableCell key={cell.id}>
                        <EditableCell cell={cell} />
                      </TableCell>
                    ))}
                  </TableRow>
                  {enableRowExpansion && row.getIsExpanded() && renderExpandedRow && (
                    <TableRow>
                      <TableCell colSpan={columns.length + (enableRowSelection ? 1 : 0)}>
                        {renderExpandedRow(row)}
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      {/* Pagination */}
      <div
        data-slot="data-grid-pagination"
        className={
          "flex flex-wrap flex-col sm:flex-row justify-between items-center gap-2.5 py-2.5 sm:py-0 grow"
        }
      >
        <div className="flex flex-wrap items-center space-x-2.5 py-3 px-3">
          {isLoading ? (
            <TableSkeleton
              rowCount={10}
              columnCount={4}
              hasTitle={true}
              hasSearch={true}
              hasFilters={true}
              hasPagination={true}
              hasActions={true}
            />
          ) : (
            <>
              <div className="text-sm text-muted-foreground">Rows per page</div>
              <Select
                value={`${pageSize}`}
                indicatorPosition="right"
                onValueChange={(value) => {
                  const newPageSize = Number(value);
                  setPageSize(newPageSize);
                  setPageIndex(0);
                }}
              >
                <SelectTrigger className="w-fit" size="sm">
                  <SelectValue placeholder={`${pageSize}`} />
                </SelectTrigger>
                <SelectContent side="top" className="min-w-[50px]">
                  {[5, 10, 25, 50, 100].map((size) => (
                    <SelectItem key={size} value={`${size}`}>{size}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          )}
        </div>
        <div className="flex flex-col sm:flex-row justify-center sm:justify-end items-center gap-2.5 pt-2.5 sm:pt-0 order-1 sm:order-2">
          {isLoading ? (
            <TableSkeleton
              rowCount={10}
              columnCount={4}
              hasTitle={true}
              hasSearch={true}
              hasFilters={true}
              hasPagination={true}
              hasActions={true}
            />
          ) : (
            <>
              <div className="text-sm text-muted-foreground text-nowrap order-2 sm:order-1">
                {`${pageIndex * pageSize + 1} - ${Math.min((pageIndex + 1) * pageSize, safeData.totalCount)} of ${safeData.totalCount}`}
              </div>
              {table?.getPageCount?.() > 1 && (
                <div className="flex items-center space-x-1 order-1 sm:order-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="size-7 p-0 text-sm rtl:transform rtl:rotate-180"
                    onClick={() => table?.previousPage?.()}
                    disabled={!table?.getCanPreviousPage?.()}
                  >
                    <span className="sr-only">Go to previous page</span>
                    <ChevronLeftIcon className="size-4" />
                  </Button>
                  {/* Page buttons with ellipsis */}
                  {(() => {
                    const pageCount = table?.getPageCount?.() ?? 0;
                    const paginationMoreLimit = 5;
                    const currentGroupStart = Math.floor(pageIndex / paginationMoreLimit) * paginationMoreLimit;
                    const currentGroupEnd = Math.min(currentGroupStart + paginationMoreLimit, pageCount);
                    const buttons = [];
                    if (currentGroupStart > 0) {
                      buttons.push(
                        <Button
                          key="ellipsis-prev"
                          size="sm"
                          className="size-7 p-0 text-sm"
                          variant="ghost"
                          onClick={() => table?.setPageIndex?.(currentGroupStart - 1)}
                        >
                          ...
                        </Button>
                      );
                    }
                    for (let i = currentGroupStart; i < currentGroupEnd; i++) {
                      buttons.push(
                        <Button
                          key={i}
                          size="sm"
                          variant="ghost"
                          className={
                            `size-7 p-0 text-sm text-muted-foreground${pageIndex === i ? ' bg-accent text-accent-foreground' : ''}`
                          }
                          onClick={() => {
                            if (pageIndex !== i) {
                              table?.setPageIndex?.(i);
                            }
                          }}
                        >
                          {i + 1}
                        </Button>
                      );
                    }
                    if (currentGroupEnd < pageCount) {
                      buttons.push(
                        <Button
                          key="ellipsis-next"
                          className="size-7 p-0 text-sm"
                          variant="ghost"
                          size="sm"
                          onClick={() => table?.setPageIndex?.(currentGroupEnd)}
                        >
                          ...
                        </Button>
                      );
                    }
                    return buttons;
                  })()}
                  <Button
                    size="sm"
                    variant="ghost"
                    className="size-7 p-0 text-sm rtl:transform rtl:rotate-180"
                    onClick={() => table?.nextPage?.()}
                    disabled={!table?.getCanNextPage?.()}
                  >
                    <span className="sr-only">Go to next page</span>
                    <ChevronRightIcon className="size-4" />
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Debounced input for column filters
const DebouncedColumnFilterInput: React.FC<{ value: string; onChange: (v: string) => void; placeholder?: string }> = ({ value, onChange, placeholder }) => {
  const [inputValue, setInputValue] = React.useState(value);
  const debouncedValue = useDebounce(inputValue, 300);
  React.useEffect(() => {
    setInputValue(value);
  }, [value]);
  React.useEffect(() => {
    if (debouncedValue !== value) {
      onChange(debouncedValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedValue]);
  return (
    <Input
      value={inputValue}
      onChange={e => setInputValue(e.target.value)}
      placeholder={placeholder}
      className="w-full h-7 text-xs font-normal"
    />
  );
};

