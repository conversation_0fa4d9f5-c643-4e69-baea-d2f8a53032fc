import { Skeleton } from "@/components/ui/skeleton";
import { type ColumnDef } from "@tanstack/react-table";

interface TableSkeletonProps<TData> {
  columns: ColumnDef<TData>[];
  rowCount?: number;
}

const TableSkeleton = <TData,>({ 
  columns, 
  rowCount = 10 
}: TableSkeletonProps<TData>) => {
  const skeletonRows = Array.from({ length: rowCount }, (_, i) => i);
  
  return (
    <div className="w-full">
      {/* Table Header */}
      <div className="border-b bg-muted/50">
        <div className="flex items-center h-12 px-4">
          {columns.map((_column, index) => (
            <div key={index} className="flex-1 px-2">
              <Skeleton className="h-4 w-20" />
            </div>
          ))}
        </div>
      </div>
      
      {/* Table Body */}
      <div className="divide-y">
        {skeletonRows.map((rowIndex) => (
          <div key={rowIndex} className="flex items-center h-14 px-4 hover:bg-muted/50">
            {columns.map((_column, colIndex) => (
              <div key={colIndex} className="flex-1 px-2">
                <Skeleton className="h-4 w-full max-w-32" />
              </div>
            ))}
          </div>
        ))}
      </div>
      
      {/* Pagination Skeleton */}
      <div className="flex items-center justify-between px-4 py-3 border-t bg-muted/30">
        <Skeleton className="h-4 w-32" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-8 w-8" />
        </div>
      </div>
    </div>
  );
};

export default TableSkeleton; 