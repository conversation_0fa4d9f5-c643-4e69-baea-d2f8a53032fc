import{$ as I,h as U,j as e,a as g,u as ce}from"./vendor-6tJeyfYI.js";import{B as k,M as G}from"./app-layout-rNt37hVL.js";import{F as z,a as y}from"./FormField-AGj4WUYd.js";import{I as S}from"./input-DlXlkYlT.js";import{H as ue,r as me}from"./ht-theme-main.min-DuylQxQp.js";import{o as ae,s as r,n as pe,V as he,J as xe,P as ye,D as ge,a as fe}from"./types-B5GxFm4f.js";import{c as De,m as ve}from"./App-DnhJzTNn.js";/* empty css                         */import{u as je,F as be,C as T}from"./index.esm-BubGICDC.js";import{A as Ne}from"./attachment-dialog-D7s9nIdd.js";function Ie(t){return typeof t=="object"&&t!==null&&"_reactRootContainer"in t?t._reactRootContainer:void 0}function Se(t,o){typeof t=="object"&&t!==null&&(t._reactRootContainer=o)}const ee=new Map,we=t=>{if(!t)throw new Error("queryClient is required for renderAttachmentButton");return(o,l,h,b,c,f,q)=>{let j=Ie(l);j||(j=De.createRoot(l),Se(l,j));const O=()=>{const w=o?.getSourceDataAtRow?.(h),D=w&&!Array.isArray(w)?w:{},P=`${D.id||h}`,N=D.vesselId||D.id,[L,Y]=I.useState(ee.get(P)||!1),A=x=>{ee.set(P,x),Y(x)},V=t.getQueryData(["export-vessel",N])?.items?.find(x=>x.id===D.id)?.attachments||o?.getDataAtRowProp(h,"attachments")||[],W=o?.getDataAtRowProp(h,"itemName")||"",B=D.id??"",p=D.docEntry??0,v=()=>{N?t.invalidateQueries({queryKey:["export-vessel",N]}):t.invalidateQueries({queryKey:["export-vessel"]}),setTimeout(()=>{if(o)try{const x=t.getQueryData(["export-vessel",N]);if(x&&x.items){const s=D.id,C=x.items.find(M=>M.id===s);C&&o.setDataAtRowProp(h,"attachments",C.attachments||[])}j.render(I.createElement(O))}catch{}},200)};return I.createElement(I.Fragment,null,[I.createElement(k,{key:"button",size:"xs",variant:"success",type:"button",onClick:()=>A(!0),"aria-label":"View Attachments",children:`Attachment (${V?.length||0})`}),I.createElement(Ne,{key:"dialog",open:L,onOpenChange:A,attachments:V||[],title:`Attachments - ${W||"Item"}`,queryClient:t,referenceId:B,documentReferenceId:p,defaultTabName:"SHIPPING",docType:"Export",transType:"ExportDetails",tabName:"SHIPPING",onUploadSuccess:v})])};return j.render(I.createElement(O)),l}},Pe=(t,o,l)=>{const h=t.map(c=>c.name??"").filter(c=>c!==""),b=o.map(c=>c.name??"").filter(c=>c!=="");return[{data:"id",title:"Id",type:"text",width:200},{data:"docEntry",title:"DocEntry",type:"text",width:200},{data:"concurrencyStamp",title:"concurrencyStamp",type:"text",width:200},{data:"tenant",title:"Tenant",type:"autocomplete",width:140,source:h,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"businessPartner",title:"Business Partner",type:"autocomplete",width:300,source:b,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"itemName",title:"Item Name",type:"text",width:250},{data:"grossWeight",title:"Gross Weight",type:"numeric",width:100},{data:"unitWeight",title:"Unit Weight",type:"text",width:100},{data:"letterNo",title:"Letter No",type:"text",width:120},{data:"letterDate",title:"Letter Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"shippingInstructionNo",title:"Shipping Instruction No",type:"text",width:180},{data:"shippingInstructionDate",title:"Shipping Instruction Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"status",title:"Status",type:"text",width:120},{data:"regType",title:"RegType",type:"text",width:120},{data:"noBl",title:"No BL",type:"text",width:120},{data:"dateBl",title:"Date BL",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"ajuNo",title:"AJU No",type:"text",width:120},{data:"regNo",title:"Reg No",type:"text",width:120},{data:"regDate",title:"Reg Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppbNo",title:"SPPB No",type:"text",width:120},{data:"sppbDate",title:"SPPB Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppdNo",title:"SPPD No",type:"text",width:120},{data:"sppdDate",title:"SPPD Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"remarks",title:"Remark",type:"text",width:120},{data:"attachments",title:"Attachment",width:100,renderer:we(l),readOnly:!0,filterable:!1}]},Ae=ae({docNum:r().optional(),vesselId:r().min(1,"Vessel Name is required"),postingDate:r().min(1,"Posting Date is required"),voyage:r().optional(),vesselArrival:r().optional(),vesselDeparture:r().optional(),portOriginId:r().optional(),destinationPortId:r().optional(),concurrencyStamp:r().optional(),docStatus:r().optional(),jettyId:r().min(1,"Jetty is required"),asideDate:r().optional(),castOfDate:r().optional()}),Re=ae({itemName:r().optional(),itemQty:pe().optional(),unitQty:r().optional(),remarks:r().optional(),tenant:r().optional(),tenantId:r().optional(),businessPartner:r().optional(),businessPartnerId:r().optional(),concurrencyStamp:r().optional(),shippingInstructionDate:r().optional(),letterDate:r().optional(),regDate:r().optional()});me();function te(t){return{docNum:t.docNum??"",vesselId:t.vesselId??"",voyage:t.voyage??"",postingDate:t.postingDate??"",vesselArrival:t.vesselArrival??"",vesselDeparture:t.vesselDeparture??"",portOriginId:t.portOriginId??"",destinationPortId:t.destinationPortId??"",jettyId:t.jettyId??"",asideDate:t.asideDate??"",castOfDate:t.castOfDate??""}}function Ce(){}const Ke=t=>{const{data:o=[],isLoading:l}=U({queryKey:["tenants"],queryFn:()=>G.filterTenants({page:1,maxResultCount:1e3}).then(c=>c.data?.items??[])}),{data:h=[],isLoading:b}=U({queryKey:["businessPartners"],queryFn:()=>G.filterBusinessPartners({page:1,maxResultCount:1e4}).then(c=>c.data?.items??[])});return l||b?e.jsx("div",{children:"Loading data..."}):e.jsx(Ee,{...t,tenants:o,businessPartners:h,loadingTenants:l,loadingBusinessPartners:b})},Ee=({mode:t,initialHeader:o,initialItems:l,onSubmit:h,headerSchema:b=Ae,itemSchema:c=Re,isSubmitting:f=!1,tenants:q,businessPartners:j,title:O="Create Export Vessel",showAddLineButton:w=!0})=>{const[D,P]=g.useState(l),[N,L]=g.useState([]),Y=g.useRef(null),[A,Q]=g.useState(null),[R,V]=g.useState(""),W=g.useRef(void 0),B=g.useRef(void 0),p=je({resolver:fe(b),defaultValues:{...te(o),docNum:t==="create"?R:o.docNum??""},mode:"onBlur"}),{register:v,handleSubmit:x,formState:{errors:s},reset:C,setValue:M}=p,J=p.watch("vesselArrival")||new Date().toISOString().slice(0,10),{data:K}=U({queryKey:["generateDocNum",J,t],queryFn:()=>G.generateNextExportVesselDocNum(J).then(a=>String(a.data??"")),enabled:t==="create"&&!!J});g.useEffect(()=>{t==="create"&&K&&V(K)},[K,t]),g.useEffect(()=>{t==="create"&&R&&M("docNum",R)},[R,t,M]),g.useEffect(()=>{const a=JSON.stringify(o),d=JSON.stringify(l);t==="edit"&&(a&&W.current!==a||d&&B.current!==d)&&(C(te(o)),P(l.map(n=>({...n,concurrencyStamp:n.concurrencyStamp??void 0,id:n.id??void 0}))),W.current=a,B.current=d)},[o,l,t,C]);const se=a=>{const d=[];return a.forEach((n,u)=>{const E=c.safeParse(n);E.success?d[u]="":d[u]=Object.values(E.error.flatten().fieldErrors).flat().join(", ")}),L(d),d.every(n=>!n)},X=(a,d)=>{const n={...a};for(const u of d)(n[u]===void 0||n[u]===null)&&(n[u]="");for(const u in n)(typeof n[u]=="string"||n[u]===null)&&(n[u]=n[u]??"");return n};function re(a){return typeof a=="object"&&a!==null&&"id"in a&&(typeof a.id=="string"||typeof a.id=="number")}const ne=async a=>{const d=Y.current?.hotInstance?.getSourceData?.()??[],n=["deleted","docType","shipment","docStatus","statusBms","portOrigin","concurrencyStamp","tenantId","businessPartnerId","itemName","unitQty","remarks"],u={deleted:"N",docType:"",shipment:"",docStatus:"",statusBms:"",portOrigin:"",concurrencyStamp:"",tenantId:"",businessPartnerId:"",itemName:"",unitQty:"",remarks:""},E=d.map(m=>{const i={...m};if(m.tenant){const F=q.find(H=>H.name===m.tenant);i.tenantId=F?.id||""}else i.tenantId="";if(m.businessPartner){const F=j.find(H=>H.name===m.businessPartner);i.businessPartnerId=F?.id||""}else i.businessPartnerId="";i.shippingInstructionDate||(i.shippingInstructionDate=void 0),i.letterDate||(i.letterDate=void 0),i.regDate||(i.regDate=void 0);const de={...u,...i},$=l.find(F=>F.id===i.id);return $&&$.concurrencyStamp&&(i.concurrencyStamp=$.concurrencyStamp),X(de,n)}),le=X({...u,...a,docNum:String(a.docNum??"")},n);if(se(E)){Q(null);try{const m=await h(le,E);p.reset(),re(m)&&ve.visit(`/export-vessel/edit/${m.id}`)}catch(m){let i="An error occurred";typeof m=="object"&&m&&"message"in m&&typeof m.message=="string"&&(i=m.message),Q(i)}}},oe=()=>{P(a=>[...a,{}])},ie=ce(),Z=Pe(q,j,ie),_=[].find(a=>a.id===p.watch("jettyId"))||null;return e.jsx("div",{className:"w-full mx-auto",children:e.jsxs("div",{className:"bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-lg font-bold text-gray-800 dark:text-white",children:O}),e.jsx("div",{className:"h-1 w-16 bg-primary rounded mt-2 mb-4"})]}),e.jsx(be,{...p,children:e.jsxs("form",{onSubmit:x(ne),className:"space-y-6",children:[A&&e.jsx("div",{className:"text-red-500 text-sm mb-2",children:A}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(z,{showDivider:!1,children:[e.jsxs(y,{label:"DocNum",labelWidth:"100px",children:[e.jsx(S,{...v("docNum"),value:p.watch("docNum"),readOnly:t==="create",disabled:t==="edit"}),s.docNum&&e.jsx("span",{className:"text-red-500 text-xs",children:s.docNum.message})]}),e.jsxs(y,{label:"Vessel Name",labelWidth:"100px",children:[e.jsx(T,{name:"vesselId",control:p.control,render:({field:a})=>e.jsx(he,{value:a.value,onValueChange:a.onChange,placeholder:"Select vessel...",disabled:f})}),s.vesselId&&e.jsx("span",{className:"text-red-500 text-xs",children:s.vesselId.message})]}),e.jsxs(y,{label:"Voyage",labelWidth:"100px",children:[e.jsx(S,{...v("voyage")}),s.voyage&&e.jsx("span",{className:"text-red-500 text-xs",children:s.voyage.message})]}),e.jsxs(y,{label:"Jetty",labelWidth:"100px",children:[e.jsx(T,{name:"jettyId",control:p.control,render:({field:a})=>e.jsx(xe,{value:a.value,onValueChange:a.onChange,placeholder:"Select jetty...",disabled:f})}),s.jettyId&&e.jsx("span",{className:"text-red-500 text-xs",children:s.jettyId.message})]})]}),e.jsxs(z,{showDivider:!1,children:[e.jsxs(y,{label:"A/Side Date",labelWidth:"100px",children:[e.jsx(S,{type:"datetime-local",...v("asideDate")}),s.asideDate&&e.jsx("span",{className:"text-red-500 text-xs",children:s.asideDate.message})]}),e.jsxs(y,{label:"Cast Of Date",labelWidth:"100px",children:[e.jsx(S,{type:"datetime-local",...v("castOfDate")}),s.castOfDate&&e.jsx("span",{className:"text-red-500 text-xs",children:s.castOfDate.message})]}),e.jsxs(y,{label:"Arrival Date",labelWidth:"100px",children:[e.jsx(S,{type:"datetime-local",...v("vesselArrival")}),s.vesselArrival&&e.jsx("span",{className:"text-red-500 text-xs",children:s.vesselArrival.message})]}),e.jsxs(y,{label:"Departure Date",labelWidth:"100px",children:[e.jsx(S,{type:"datetime-local",...v("vesselDeparture")}),s.vesselDeparture&&e.jsx("span",{className:"text-red-500 text-xs",children:s.vesselDeparture.message})]})]}),e.jsxs(z,{showDivider:!1,children:[e.jsxs(y,{label:"Posting Date",labelWidth:"100px",children:[e.jsx(S,{type:"date",...v("postingDate")}),s.postingDate&&e.jsx("span",{className:"text-red-500 text-xs",children:s.postingDate.message})]}),e.jsxs(y,{label:"Port Origin",labelWidth:"100px",children:[e.jsx(T,{name:"portOriginId",control:p.control,render:({field:a})=>e.jsx(ye,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select port origin...",disabled:f})}),s.portOriginId&&e.jsx("span",{className:"text-red-500 text-xs",children:s.portOriginId.message})]}),e.jsxs(y,{label:"Destination Port",labelWidth:"100px",children:[e.jsx(T,{name:"destinationPortId",control:p.control,render:({field:a})=>e.jsx(ge,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select destination port...",disabled:f})}),s.destinationPortId&&e.jsx("span",{className:"text-red-500 text-xs",children:s.destinationPortId.message})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block font-medium mb-2",children:"Items"}),e.jsx(ue,{ref:Y,themeName:"ht-theme-main",data:D,columns:Z,colHeaders:Z.map(a=>a.title).filter(a=>typeof a=="string"),rowHeaders:!0,height:"50vh",licenseKey:"non-commercial-and-evaluation",stretchH:"all",contextMenu:!0,manualColumnResize:!0,manualRowResize:!0,autoColumnSize:!1,autoRowSize:!1,startRows:1,dropdownMenu:!0,filters:!0,colWidths:80,hiddenColumns:{copyPasteEnabled:!0,indicators:!0,columns:[0,1,2]},width:"100%",persistentState:!0}),N.some(a=>a)&&e.jsx("div",{className:"mt-2 text-red-500 text-xs",children:N.map((a,d)=>a&&e.jsxs("div",{children:["Row ",d+1,": ",a]},d))})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[w&&e.jsx(k,{type:"button",variant:"outline",onClick:oe,disabled:f,children:"+ Add Line"}),typeof _?.isCustomArea=="boolean"&&!_.isCustomArea&&e.jsx(k,{type:"button",variant:"primary",onClick:Ce,disabled:f,children:"Submit Approval"}),e.jsx(k,{type:"submit",disabled:f,children:f?t==="edit"?"Saving...":"Creating...":t==="edit"?"Save Changes":"Create"})]})]})})]})})};export{Ke as E};
//# sourceMappingURL=export-vessel-form-CVscznfP.js.map
