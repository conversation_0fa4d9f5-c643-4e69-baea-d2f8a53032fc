{"version": 3, "file": "page-B36zAKzh.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js", "../../../../../frontend/src/components/applications/jetty-selector.tsx", "../../../../../frontend/src/pages/application/jetty-demo/page.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"M21 12a9 9 0 1 1-6.219-8.56\", key: \"13zald\" }]];\nconst LoaderCircle = createLucideIcon(\"loader-circle\", __iconNode);\n\nexport { __iconNode, LoaderCircle as default };\n//# sourceMappingURL=loader-circle.js.map\n", "'use client'\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from \"@/components/ui/command\"\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\"\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\r\nimport { useDebounce } from \"@/components/ui/useDebounce\"\r\nimport { useJettyDataWithFilter } from \"@/lib/hooks/useJettyDataWithFilter\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Check, ChevronsUpDown, Loader2 } from \"lucide-react\"\r\nimport * as React from 'react'\r\nimport { useEffect } from 'react'\r\n\r\ninterface JettySelectorProps {\r\n  value?: string;\r\n  onValueChange: (value: string) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  maxHeight?: number;\r\n}\r\n\r\nexport const JettySelector: React.FC<JettySelectorProps> = ({\r\n  value,\r\n  onValueChange,\r\n  placeholder = 'Select jetty...',\r\n  className,\r\n  disabled = false,\r\n  maxHeight = 300,\r\n}) => {\r\n  const [open, setOpen] = React.useState(false)\r\n  const [searchValue, setSearchValue] = React.useState('')\r\n  const scrollAreaRef = React.useRef<HTMLDivElement>(null)\r\n\r\n  // Debounce the search value to avoid too many API calls\r\n  const debouncedSearchValue = useDebounce(searchValue, 300)\r\n\r\n  // Fetch jetty data with filter\r\n  const { mutate: fetchJettyData, data: jettyData = [], isPending: isLoading, error } = useJettyDataWithFilter()\r\n\r\n  // Fetch selected jetty data if we have a value but it's not in current results\r\n  const { mutate: fetchSelectedJetty, data: selectedJettyFromApi = [] } = useJettyDataWithFilter()\r\n\r\n  // Trigger the mutation when search value changes\r\n  useEffect(() => {\r\n    if (open) {\r\n      fetchJettyData({\r\n        maxResultCount: 20,\r\n        skipCount: 0,\r\n        filterGroup: debouncedSearchValue.trim() ? {\r\n          operator: 'And',\r\n          conditions: [{\r\n            fieldName: 'name',\r\n            operator: 'Contains',\r\n            value: debouncedSearchValue.trim()\r\n          }]\r\n        } : undefined\r\n      });\r\n    }\r\n  }, [debouncedSearchValue, open, fetchJettyData]);\r\n\r\n  // Trigger the mutation for selected jetty when value changes\r\n  useEffect(() => {\r\n    if (value && open) {\r\n      fetchSelectedJetty({\r\n        maxResultCount: 1,\r\n        skipCount: 0,\r\n        filterGroup: {\r\n          operator: 'And',\r\n          conditions: [{\r\n            fieldName: 'id',\r\n            operator: 'Equals',\r\n            value: value\r\n          }]\r\n        }\r\n      });\r\n    }\r\n  }, [value, open, fetchSelectedJetty]);\r\n\r\n  // Handle wheel events for scrolling\r\n  const handleWheel = React.useCallback((e: WheelEvent) => {\r\n    if (scrollAreaRef.current) {\r\n      const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');\r\n      if (scrollableElement) {\r\n        e.preventDefault();\r\n        scrollableElement.scrollTop += e.deltaY;\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Add wheel event listener when dropdown is open\r\n  React.useEffect(() => {\r\n    const scrollArea = scrollAreaRef.current;\r\n    if (open && scrollArea) {\r\n      scrollArea.addEventListener('wheel', handleWheel, { passive: false });\r\n      return () => {\r\n        scrollArea.removeEventListener('wheel', handleWheel);\r\n      };\r\n    }\r\n  }, [open, handleWheel]);\r\n\r\n  // Get selected jetty data - check current results first, then fallback to API fetch\r\n  const selectedJetty = React.useMemo(() => {\r\n    // First check if selected jetty is in current filtered results\r\n    const fromCurrentResults = jettyData.find(jetty => jetty.id === value || jetty.name === value);\r\n    if (fromCurrentResults) return fromCurrentResults;\r\n    \r\n    // If not in current results, check the API fetch for the selected value\r\n    if (value && selectedJettyFromApi.length > 0) {\r\n      return selectedJettyFromApi.find(jetty => jetty.id === value || jetty.name === value);\r\n    }\r\n    \r\n    return null;\r\n  }, [jettyData, value, selectedJettyFromApi])\r\n\r\n  // Handle selection\r\n  const handleSelect = React.useCallback((jettyId: string) => {\r\n    onValueChange(jettyId)\r\n    setOpen(false)\r\n    setSearchValue('') // Clear search when selection is made\r\n  }, [onValueChange])\r\n\r\n  // Handle search input change\r\n  const handleSearchChange = React.useCallback((search: string) => {\r\n    setSearchValue(search)\r\n  }, [])\r\n\r\n  // Display value for the trigger button\r\n  const displayValue = selectedJetty ? selectedJetty.name || selectedJetty.alias || selectedJetty.id : placeholder\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className={cn(\r\n            \"w-full justify-between min-h-6 h-auto py-2\",\r\n            !value && \"text-muted-foreground\",\r\n            className\r\n          )}\r\n          onClick={() => setOpen(!open)}\r\n          disabled={disabled}\r\n        >\r\n          <span className=\"truncate\">{displayValue}</span>\r\n          <ChevronsUpDown className=\"h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent\r\n        className=\"p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden\"\r\n        align=\"start\"\r\n        sideOffset={5}\r\n      >\r\n        <Command\r\n          shouldFilter={false}\r\n          className=\"max-h-full\"\r\n        >\r\n          <CommandInput\r\n            placeholder=\"Search jetties...\"\r\n            value={searchValue}\r\n            onValueChange={handleSearchChange}\r\n            className=\"h-9\"\r\n          />\r\n          <CommandEmpty>\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center py-4\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                Loading...\r\n              </div>\r\n            ) : error ? (\r\n              <div className=\"text-center py-4 text-destructive\">\r\n                Error loading jetties\r\n              </div>\r\n            ) : (\r\n              \"No jetties found\"\r\n            )}\r\n          </CommandEmpty>\r\n          <ScrollArea\r\n            className=\"overflow-hidden h-full\"\r\n            style={{ height: `${maxHeight - 40}px`, maxHeight: `${maxHeight - 40}px` }}\r\n            ref={scrollAreaRef}\r\n          >\r\n            <CommandGroup>\r\n              {jettyData.map((jetty) => {\r\n                const isSelected = value === jetty.id || value === jetty.name\r\n                const displayName = jetty.name || jetty.alias || jetty.id || 'Unknown'\r\n                const jettyValue = jetty.id || jetty.name || ''\r\n                \r\n                return (\r\n                  <CommandItem\r\n                    key={jettyValue}\r\n                    value={jettyValue}\r\n                    onSelect={() => handleSelect(jettyValue)}\r\n                    className={cn(\r\n                      \"flex items-center gap-2\",\r\n                      isSelected ? \"bg-muted\" : \"\"\r\n                    )}\r\n                  >\r\n                    <div className={cn(\r\n                      \"flex h-4 w-4 items-center justify-center rounded-sm border\",\r\n                      isSelected\r\n                        ? \"bg-primary border-primary text-primary-foreground\"\r\n                        : \"opacity-50\"\r\n                    )}>\r\n                      {isSelected && <Check className=\"h-3 w-3\" />}\r\n                    </div>\r\n                    <div className=\"flex flex-col\">\r\n                      <span className=\"font-medium\">{displayName}</span>\r\n                      {jetty.port && (\r\n                        <span className=\"text-xs text-muted-foreground\">\r\n                          Port: {jetty.port}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </CommandItem>\r\n                )\r\n              })}\r\n            </CommandGroup>\r\n          </ScrollArea>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  )\r\n} ", "import { JettySelector } from \"@/components/applications/jetty-selector\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport { useState } from \"react\";\r\n\r\nconst JettyDemoPage: React.FC = () => {\r\n  const [selectedJetty, setSelectedJetty] = useState<string>('');\r\n\r\n  return (\r\n    <AppLayout>\r\n      <div className=\"flex flex-col space-y-6 p-6\">\r\n        <div className=\"space-y-4\">\r\n          <h1 className=\"text-2xl font-bold\">Jetty Selector Demo</h1>\r\n          <p className=\"text-muted-foreground\">\r\n            This demonstrates the custom JettySelector component with debounced filtering.\r\n            Try typing in the search box to filter jetties from the database.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"max-w-md space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <label className=\"text-sm font-medium\">Select Jetty</label>\r\n            <JettySelector\r\n              value={selectedJetty}\r\n              onValueChange={setSelectedJetty}\r\n              placeholder=\"Search and select a jetty...\"\r\n            />\r\n          </div>\r\n\r\n          {selectedJetty && (\r\n            <div className=\"p-4 bg-muted rounded-lg\">\r\n              <h3 className=\"font-medium mb-2\">Selected Jetty:</h3>\r\n              <p className=\"text-sm\">{selectedJetty}</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default JettyDemoPage; "], "names": ["__iconNode", "LoaderCircle", "createLucideIcon", "JettySelector", "value", "onValueChange", "placeholder", "className", "disabled", "maxHeight", "open", "<PERSON><PERSON><PERSON>", "React.useState", "searchValue", "setSearchValue", "scrollAreaRef", "React.useRef", "debouncedSearchValue", "useDebounce", "fetchJettyData", "jettyData", "isLoading", "error", "useJettyDataWithFilter", "fetchSelectedJetty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "handleWheel", "React.useCallback", "e", "scrollableElement", "React.useEffect", "scrollArea", "<PERSON><PERSON><PERSON><PERSON>", "React.useMemo", "fromCurrentResults", "jetty", "handleSelect", "jettyId", "handleSearchChange", "search", "displayValue", "jsxs", "Popover", "jsx", "PopoverTrigger", "<PERSON><PERSON>", "cn", "ChevronsUpDown", "PopoverC<PERSON>nt", "Command", "CommandInput", "CommandEmpty", "Loader2", "ScrollArea", "CommandGroup", "isSelected", "displayName", "jettyValue", "CommandItem", "Check", "JettyDemoPage", "setSelectedJetty", "useState", "AppLayout"], "mappings": "ycAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CAAC,CAAC,OAAQ,CAAE,EAAG,8BAA+B,IAAK,QAAQ,CAAE,CAAC,EAC3EC,EAAeC,EAAiB,gBAAiBF,CAAU,ECYpDG,EAA8C,CAAC,CAC1D,MAAAC,EACA,cAAAC,EACA,YAAAC,EAAc,kBACd,UAAAC,EACA,SAAAC,EAAW,GACX,UAAAC,EAAY,GACd,IAAM,CACJ,KAAM,CAACC,EAAMC,CAAO,EAAIC,EAAAA,SAAe,EAAK,EACtC,CAACC,EAAaC,CAAc,EAAIF,EAAAA,SAAe,EAAE,EACjDG,EAAgBC,EAAM,OAAuB,IAAI,EAGjDC,EAAuBC,EAAYL,EAAa,GAAG,EAGnD,CAAE,OAAQM,EAAgB,KAAMC,EAAY,GAAI,UAAWC,EAAW,MAAAC,CAAM,EAAIC,EAAuB,EAGvG,CAAE,OAAQC,EAAoB,KAAMC,EAAuB,CAAA,GAAOF,EAAuB,EAG/FG,EAAAA,UAAU,IAAM,CACVhB,GACaS,EAAA,CACb,eAAgB,GAChB,UAAW,EACX,YAAaF,EAAqB,OAAS,CACzC,SAAU,MACV,WAAY,CAAC,CACX,UAAW,OACX,SAAU,WACV,MAAOA,EAAqB,KAAK,CAClC,CAAA,CAAA,EACC,MAAA,CACL,CAEF,EAAA,CAACA,EAAsBP,EAAMS,CAAc,CAAC,EAG/CO,EAAAA,UAAU,IAAM,CACVtB,GAASM,GACQc,EAAA,CACjB,eAAgB,EAChB,UAAW,EACX,YAAa,CACX,SAAU,MACV,WAAY,CAAC,CACX,UAAW,KACX,SAAU,SACV,MAAApB,CACD,CAAA,CAAA,CACH,CACD,CAEF,EAAA,CAACA,EAAOM,EAAMc,CAAkB,CAAC,EAGpC,MAAMG,EAAcC,cAAmBC,GAAkB,CACvD,GAAId,EAAc,QAAS,CACzB,MAAMe,EAAoBf,EAAc,QAAQ,cAAc,mCAAmC,EAC7Fe,IACFD,EAAE,eAAe,EACjBC,EAAkB,WAAaD,EAAE,OACnC,CAEJ,EAAG,EAAE,EAGLE,EAAAA,UAAgB,IAAM,CACpB,MAAMC,EAAajB,EAAc,QACjC,GAAIL,GAAQsB,EACV,OAAAA,EAAW,iBAAiB,QAASL,EAAa,CAAE,QAAS,GAAO,EAC7D,IAAM,CACAK,EAAA,oBAAoB,QAASL,CAAW,CACrD,CACF,EACC,CAACjB,EAAMiB,CAAW,CAAC,EAGhB,MAAAM,EAAgBC,EAAAA,QAAc,IAAM,CAElC,MAAAC,EAAqBf,EAAU,KAAKgB,GAASA,EAAM,KAAOhC,GAASgC,EAAM,OAAShC,CAAK,EAC7F,OAAI+B,IAGA/B,GAASqB,EAAqB,OAAS,EAClCA,EAAqB,KAAcW,GAAAA,EAAM,KAAOhC,GAASgC,EAAM,OAAShC,CAAK,EAG/E,KACN,EAAA,CAACgB,EAAWhB,EAAOqB,CAAoB,CAAC,EAGrCY,EAAeT,cAAmBU,GAAoB,CAC1DjC,EAAciC,CAAO,EACrB3B,EAAQ,EAAK,EACbG,EAAe,EAAE,CAAA,EAChB,CAACT,CAAa,CAAC,EAGZkC,EAAqBX,cAAmBY,GAAmB,CAC/D1B,EAAe0B,CAAM,CACvB,EAAG,EAAE,EAGCC,EAAeR,EAAgBA,EAAc,MAAQA,EAAc,OAASA,EAAc,GAAK3B,EAErG,OACGoC,EAAAA,KAAAC,EAAA,CAAQ,KAAAjC,EAAY,aAAcC,EACjC,SAAA,CAACiC,EAAAA,IAAAC,EAAA,CAAe,QAAO,GACrB,SAAAH,EAAA,KAACI,EAAA,CACC,QAAQ,UACR,KAAK,WACL,gBAAepC,EACf,UAAWqC,EACT,6CACA,CAAC3C,GAAS,wBACVG,CACF,EACA,QAAS,IAAMI,EAAQ,CAACD,CAAI,EAC5B,SAAAF,EAEA,SAAA,CAACoC,EAAA,IAAA,OAAA,CAAK,UAAU,WAAY,SAAaH,EAAA,EACzCG,EAAAA,IAACI,EAAe,CAAA,UAAU,6BAA8B,CAAA,CAAA,CAAA,CAAA,EAE5D,EACAJ,EAAA,IAACK,EAAA,CACC,UAAU,6DACV,MAAM,QACN,WAAY,EAEZ,SAAAP,EAAA,KAACQ,EAAA,CACC,aAAc,GACd,UAAU,aAEV,SAAA,CAAAN,EAAA,IAACO,EAAA,CACC,YAAY,oBACZ,MAAOtC,EACP,cAAe0B,EACf,UAAU,KAAA,CACZ,QACCa,EACE,CAAA,SAAA/B,EACEqB,OAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACE,EAAAA,IAAAS,EAAA,CAAQ,UAAU,2BAA4B,CAAA,EAAE,YAEnD,CAAA,CAAA,EACE/B,EACDsB,EAAA,IAAA,MAAA,CAAI,UAAU,oCAAoC,SAAA,wBAEnD,EAEA,kBAEJ,CAAA,EACAA,EAAA,IAACU,EAAA,CACC,UAAU,yBACV,MAAO,CAAE,OAAQ,GAAG7C,EAAY,EAAE,KAAM,UAAW,GAAGA,EAAY,EAAE,IAAK,EACzE,IAAKM,EAEL,SAAC6B,EAAA,IAAAW,EAAA,CACE,SAAUnC,EAAA,IAAKgB,GAAU,CACxB,MAAMoB,EAAapD,IAAUgC,EAAM,IAAMhC,IAAUgC,EAAM,KACnDqB,EAAcrB,EAAM,MAAQA,EAAM,OAASA,EAAM,IAAM,UACvDsB,EAAatB,EAAM,IAAMA,EAAM,MAAQ,GAG3C,OAAAM,EAAA,KAACiB,EAAA,CAEC,MAAOD,EACP,SAAU,IAAMrB,EAAaqB,CAAU,EACvC,UAAWX,EACT,0BACAS,EAAa,WAAa,EAC5B,EAEA,SAAA,CAAAZ,MAAC,OAAI,UAAWG,EACd,6DACAS,EACI,oDACA,YAAA,EAEH,SAAcA,GAAAZ,MAACgB,EAAM,CAAA,UAAU,SAAU,CAAA,EAC5C,EACAlB,EAAAA,KAAC,MAAI,CAAA,UAAU,gBACb,SAAA,CAACE,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,SAAYa,EAAA,EAC1CrB,EAAM,MACJM,OAAA,OAAA,CAAK,UAAU,gCAAgC,SAAA,CAAA,SACvCN,EAAM,IAAA,CACf,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,EAvBKsB,CAwBP,CAAA,CAEH,CACH,CAAA,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CACF,EACF,CAEJ,EC5NMG,GAA0B,IAAM,CACpC,KAAM,CAAC5B,EAAe6B,CAAgB,EAAIC,EAAAA,SAAiB,EAAE,EAE7D,OACGnB,EAAA,IAAAoB,EAAA,CACC,SAACtB,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,qBAAqB,SAAmB,sBAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAGrC,kJAAA,CAAA,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,qBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,sBAAsB,SAAY,eAAA,EACnDA,EAAA,IAACzC,EAAA,CACC,MAAO8B,EACP,cAAe6B,EACf,YAAY,8BAAA,CAAA,CACd,EACF,EAEC7B,GACCS,EAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,mBAAmB,SAAe,kBAAA,EAC/CA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAW,SAAcX,CAAA,CAAA,CAAA,CACxC,CAAA,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ", "x_google_ignoreList": [0]}