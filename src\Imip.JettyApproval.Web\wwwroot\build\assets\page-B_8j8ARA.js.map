{"version": 3, "file": "page-B_8j8ARA.js", "sources": ["../../../../../frontend/src/components/document-template/document-template-table.tsx", "../../../../../frontend/src/pages/master/document-template/page.tsx"], "sourcesContent": ["import { postApiIdjasDocument<PERSON><PERSON>er<PERSON>ist, postApiIdjasDocumentTemplatesUpload } from \"@/client/sdk.gen\";\r\nimport type { DocumentTemplateDto, DocumentType } from \"@/client/types.gen\";\r\nimport { DataTable } from \"@/components/data-table/DataTable\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Footer, DialogHeader, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useMutation, useQuery } from \"@tanstack/react-query\";\r\nimport type { CellContext, ColumnDef, PaginationState, Row } from \"@tanstack/react-table\";\r\nimport { Pencil, Plus, RefreshCw } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\nimport { Controller, useForm } from \"react-hook-form\";\r\n\r\nconst DOCUMENT_TYPE_OPTIONS: { value: DocumentType; label: string }[] = [\r\n  { value: 1, label: \"Invoice\" },\r\n  { value: 2, label: \"Report\" },\r\n  { value: 3, label: \"Contract\" },\r\n  { value: 4, label: \"Letter\" },\r\n  { value: 5, label: \"Certificate\" },\r\n  { value: 6, label: \"RegistrationCard\" },\r\n  { value: 99, label: \"Other\" },\r\n];\r\n\r\ntype DialogFormValues = {\r\n  Name: string;\r\n  DocumentType: DocumentType;\r\n  Description?: string;\r\n  IsDefault?: boolean;\r\n  File: File | null;\r\n};\r\n\r\nconst DocumentTemplateDialog: React.FC<{\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  initial?: Partial<DocumentTemplateDto>;\r\n  onSuccess: () => void;\r\n}> = ({ open, onOpenChange, initial, onSuccess }) => {\r\n  const { register, handleSubmit, reset, setValue, watch, control, formState: { errors, isSubmitting } } = useForm<Omit<DialogFormValues, \"File\">>({\r\n    defaultValues: {\r\n      Name: initial?.name ?? \"\",\r\n      DocumentType: initial?.documentType ?? 1,\r\n      Description: initial?.description ?? \"\",\r\n      IsDefault: initial?.isDefault ?? false,\r\n    },\r\n  });\r\n  const [file, setFile] = React.useState<File | null>(null);\r\n\r\n  React.useEffect(() => {\r\n    reset({\r\n      Name: initial?.name ?? \"\",\r\n      DocumentType: initial?.documentType ?? 1,\r\n      Description: initial?.description ?? \"\",\r\n      IsDefault: initial?.isDefault ?? false,\r\n    });\r\n    setFile(null);\r\n  }, [initial, open, reset]);\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async (values: Omit<DialogFormValues, \"File\">) => {\r\n      if (!file) throw new Error(\"No file selected\");\r\n      return postApiIdjasDocumentTemplatesUpload({\r\n        body: {\r\n          Name: values.Name,\r\n          DocumentType: values.DocumentType,\r\n          Description: values.Description,\r\n          IsDefault: values.IsDefault,\r\n          File: file,\r\n        },\r\n      });\r\n    },\r\n    onSuccess: () => {\r\n      onSuccess();\r\n      onOpenChange(false);\r\n    },\r\n  });\r\n\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files && e.target.files.length > 0) {\r\n      const selectedFile = e.target.files[0];\r\n      if (!selectedFile.name.toLowerCase().endsWith('.docx') && !selectedFile.name.toLowerCase().endsWith('.pdf')) {\r\n        e.target.value = '';\r\n        setFile(null);\r\n        return;\r\n      }\r\n      setFile(selectedFile);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent>\r\n        <DialogHeader>\r\n          <DialogTitle>{initial ? \"Edit\" : \"Create\"} Document Template</DialogTitle>\r\n        </DialogHeader>\r\n        <form onSubmit={handleSubmit((values) => mutation.mutate(values))} className=\"space-y-4\">\r\n          <div>\r\n            <label className=\"block text-sm font-medium mb-1\">Name *</label>\r\n            <Input {...register(\"Name\", { required: \"Name is required\" })} className=\"w-full\" />\r\n            {errors.Name && <div className=\"text-xs text-destructive mt-1\">{errors.Name.message}</div>}\r\n          </div>\r\n          <div>\r\n            <label className=\"block text-sm font-medium mb-1\">Document Type *</label>\r\n            <Controller\r\n              name=\"DocumentType\"\r\n              control={control}\r\n              rules={{ required: true }}\r\n              render={({ field }) => (\r\n                <Select\r\n                  value={field.value ? String(field.value) : \"\"}\r\n                  onValueChange={val => field.onChange(Number(val))}\r\n                >\r\n                  <SelectTrigger className=\"w-full\">\r\n                    <SelectValue placeholder=\"Select document type\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {DOCUMENT_TYPE_OPTIONS.map(opt => (\r\n                      <SelectItem key={opt.value} value={String(opt.value)}>\r\n                        {opt.label}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              )}\r\n            />\r\n          </div>\r\n          <div>\r\n            <label className=\"block text-sm font-medium mb-1\">Description</label>\r\n            <Textarea {...register(\"Description\")} className=\"w-full min-h-[60px]\" />\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Checkbox {...register(\"IsDefault\")}\r\n              id=\"isDefault\"\r\n              checked={!!watch(\"IsDefault\")}\r\n              onCheckedChange={val => setValue(\"IsDefault\", !!val)}\r\n            />\r\n            <label htmlFor=\"isDefault\" className=\"text-sm\">Is Default</label>\r\n          </div>\r\n          <div>\r\n            <Label htmlFor=\"templateFile\" className=\"text-left\">File *</Label>\r\n            <Input\r\n              id=\"templateFile\"\r\n              type=\"file\"\r\n              accept=\".docx,.pdf\"\r\n              onChange={handleFileChange}\r\n              disabled={mutation.isPending || isSubmitting}\r\n            />\r\n            {file && (\r\n              <p className=\"text-sm text-muted-foreground\">Selected file: {file.name}</p>\r\n            )}\r\n          </div>\r\n          {mutation.error && <div className=\"text-xs text-destructive mt-2\">{String(mutation.error instanceof Error ? mutation.error.message : mutation.error)}</div>}\r\n          <DialogFooter>\r\n            <Button type=\"button\" variant=\"outline\" onClick={() => { onOpenChange(false); setFile(null); }}>Cancel</Button>\r\n            <Button type=\"submit\" className=\"ml-2\" disabled={isSubmitting || mutation.isPending}>{mutation.isPending ? \"Saving...\" : \"Save\"}</Button>\r\n          </DialogFooter>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nconst DocumentTemplateTable: React.FC = () => {\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [editing, setEditing] = useState<DocumentTemplateDto | null>(null);\r\n  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  // Table columns (moved here for setEditing/setDialogOpen scope)\r\n  const columns: ColumnDef<DocumentTemplateDto>[] = [\r\n    {\r\n      accessorKey: \"id\",\r\n      header: \"Id\",\r\n      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"name\",\r\n      header: \"Name\",\r\n      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"documentType\",\r\n      header: \"Type\",\r\n      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"description\",\r\n      header: \"Description\",\r\n      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"isDefault\",\r\n      header: \"Default\",\r\n      cell: (info: CellContext<DocumentTemplateDto, unknown>) => info.getValue() ? \"Yes\" : \"No\",\r\n    },\r\n    {\r\n      id: \"edit\",\r\n      header: \"\",\r\n      cell: ({ row }: { row: Row<DocumentTemplateDto> }) => (\r\n        <Button\r\n          onClick={() => {\r\n            setEditing(row.original);\r\n            setDialogOpen(true);\r\n          }}\r\n          aria-label=\"Edit Document Template\"\r\n          tabIndex={0}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"ml-2 h-8 w-8\"\r\n        >\r\n          <Pencil className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n        </Button>\r\n      ),\r\n      enableSorting: false,\r\n      enableColumnFilter: false,\r\n    },\r\n  ];\r\n\r\n  // Query for document templates\r\n  const { data, isLoading, refetch } = useQuery({\r\n    queryKey: [\"document-templates\", pagination],\r\n    queryFn: async () => {\r\n      const res = await postApiIdjasDocumentFilterList({\r\n        body: {\r\n          page: pagination.pageIndex + 1,\r\n          maxResultCount: pagination.pageSize,\r\n          skipCount: pagination.pageIndex * pagination.pageSize,\r\n        },\r\n      });\r\n      // Always return an object with items and totalCount\r\n      if (res && typeof res === 'object' && 'items' in res && 'totalCount' in res) return res;\r\n      if (res && typeof res === 'object' && 'data' in res && res.data) return res.data;\r\n      return { items: [], totalCount: 0 };\r\n    },\r\n  });\r\n\r\n  const tableData: DocumentTemplateDto[] = Array.isArray(data?.items) ? data.items : [];\r\n  const totalCount = typeof data?.totalCount === 'number' ? data.totalCount : 0;\r\n\r\n  // Refresh handler\r\n  const handleRefresh = async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      await refetch();\r\n    } finally {\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <div className=\"text-xl font-bold px-2 pt-2 pb-1\">Document Template List</div>\r\n      <div className=\"flex justify-end mb-2 gap-2\">\r\n        <Button\r\n          onClick={handleRefresh}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"h-10 w-10\"\r\n          disabled={isLoading || isRefreshing}\r\n        >\r\n          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\r\n        </Button>\r\n        <Button\r\n          onClick={() => { setEditing(null); setDialogOpen(true); }}\r\n        >\r\n          <Plus className=\"h-3 w-3\" /> New Document Template\r\n        </Button>\r\n      </div>\r\n      <DataTable\r\n        title=\"\"\r\n        columns={columns}\r\n        data={tableData}\r\n        totalCount={totalCount}\r\n        isLoading={isLoading}\r\n        manualPagination={true}\r\n        pageSize={pagination.pageSize}\r\n        onPaginationChange={setPagination}\r\n        hideDefaultFilterbar={true}\r\n        enableRowSelection={false}\r\n        manualSorting={true}\r\n      />\r\n      <DocumentTemplateDialog\r\n        open={dialogOpen}\r\n        onOpenChange={(open) => {\r\n          setDialogOpen(open);\r\n          if (!open) setEditing(null);\r\n        }}\r\n        initial={editing ?? undefined}\r\n        onSuccess={() => refetch()}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentTemplateTable; ", "import DocumentTemplateTable from \"@/components/document-template/document-template-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport React from \"react\";\r\n\r\nconst DocumentTemplatePage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <DocumentTemplateTable />\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default DocumentTemplatePage;\r\n"], "names": ["DOCUMENT_TYPE_OPTIONS", "DocumentTemplateDialog", "open", "onOpenChange", "initial", "onSuccess", "register", "handleSubmit", "reset", "setValue", "watch", "control", "errors", "isSubmitting", "useForm", "file", "setFile", "React", "mutation", "useMutation", "values", "postApiIdjasDocumentTemplatesUpload", "handleFileChange", "e", "selectedFile", "jsx", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "jsxs", "Input", "Controller", "field", "Select", "val", "SelectTrigger", "SelectValue", "SelectContent", "opt", "SelectItem", "Textarea", "Checkbox", "Label", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "DocumentTemplateTable", "dialogOpen", "setDialogOpen", "useState", "editing", "setEditing", "pagination", "setPagination", "isRefreshing", "setIsRefreshing", "columns", "info", "row", "Pencil", "data", "isLoading", "refetch", "useQuery", "res", "postApiIdjasDocumentFilterList", "tableData", "totalCount", "handleRefresh", "RefreshCw", "Plus", "DataTable", "DocumentTemplatePage", "AppLayout"], "mappings": "yxBAgBA,MAAMA,EAAkE,CACtE,CAAE,MAAO,EAAG,MAAO,SAAU,EAC7B,CAAE,MAAO,EAAG,MAAO,QAAS,EAC5B,CAAE,MAAO,EAAG,MAAO,UAAW,EAC9B,CAAE,MAAO,EAAG,MAAO,QAAS,EAC5B,CAAE,MAAO,EAAG,MAAO,aAAc,EACjC,CAAE,MAAO,EAAG,MAAO,kBAAmB,EACtC,CAAE,MAAO,GAAI,MAAO,OAAQ,CAC9B,EAUMC,EAKD,CAAC,CAAE,KAAAC,EAAM,aAAAC,EAAc,QAAAC,EAAS,UAAAC,KAAgB,CACnD,KAAM,CAAE,SAAAC,EAAU,aAAAC,EAAc,MAAAC,EAAO,SAAAC,EAAU,MAAAC,EAAO,QAAAC,EAAS,UAAW,CAAE,OAAAC,EAAQ,aAAAC,CAAa,CAAA,EAAMC,EAAwC,CAC/I,cAAe,CACb,KAAMV,GAAS,MAAQ,GACvB,aAAcA,GAAS,cAAgB,EACvC,YAAaA,GAAS,aAAe,GACrC,UAAWA,GAAS,WAAa,EAAA,CACnC,CACD,EACK,CAACW,EAAMC,CAAO,EAAIC,EAAM,SAAsB,IAAI,EAExDA,EAAM,UAAU,IAAM,CACdT,EAAA,CACJ,KAAMJ,GAAS,MAAQ,GACvB,aAAcA,GAAS,cAAgB,EACvC,YAAaA,GAAS,aAAe,GACrC,UAAWA,GAAS,WAAa,EAAA,CAClC,EACDY,EAAQ,IAAI,CACX,EAAA,CAACZ,EAASF,EAAMM,CAAK,CAAC,EAEzB,MAAMU,EAAWC,EAAY,CAC3B,WAAY,MAAOC,GAA2C,CAC5D,GAAI,CAACL,EAAY,MAAA,IAAI,MAAM,kBAAkB,EAC7C,OAAOM,EAAoC,CACzC,KAAM,CACJ,KAAMD,EAAO,KACb,aAAcA,EAAO,aACrB,YAAaA,EAAO,YACpB,UAAWA,EAAO,UAClB,KAAML,CAAA,CACR,CACD,CACH,EACA,UAAW,IAAM,CACLV,EAAA,EACVF,EAAa,EAAK,CAAA,CACpB,CACD,EAEKmB,EAAoBC,GAA2C,CACnE,GAAIA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,EAAG,CAC/C,MAAMC,EAAeD,EAAE,OAAO,MAAM,CAAC,EACrC,GAAI,CAACC,EAAa,KAAK,cAAc,SAAS,OAAO,GAAK,CAACA,EAAa,KAAK,YAAc,EAAA,SAAS,MAAM,EAAG,CAC3GD,EAAE,OAAO,MAAQ,GACjBP,EAAQ,IAAI,EACZ,MAAA,CAEFA,EAAQQ,CAAY,CAAA,CAExB,EAEA,OACGC,EAAA,IAAAC,EAAA,CAAO,KAAAxB,EAAY,aAAAC,EAClB,gBAACwB,EACC,CAAA,SAAA,CAACF,EAAA,IAAAG,EAAA,CACC,gBAACC,EAAa,CAAA,SAAA,CAAAzB,EAAU,OAAS,SAAS,oBAAA,CAAA,CAAkB,CAC9D,CAAA,EACC0B,EAAAA,KAAA,OAAA,CAAK,SAAUvB,EAAca,GAAWF,EAAS,OAAOE,CAAM,CAAC,EAAG,UAAU,YAC3E,SAAA,CAAAU,OAAC,MACC,CAAA,SAAA,CAACL,EAAA,IAAA,QAAA,CAAM,UAAU,iCAAiC,SAAM,SAAA,EACxDA,EAAAA,IAACM,EAAO,CAAA,GAAGzB,EAAS,OAAQ,CAAE,SAAU,kBAAoB,CAAA,EAAG,UAAU,QAAS,CAAA,EACjFM,EAAO,MAASa,MAAA,MAAA,CAAI,UAAU,gCAAiC,SAAAb,EAAO,KAAK,OAAQ,CAAA,CAAA,EACtF,SACC,MACC,CAAA,SAAA,CAACa,EAAA,IAAA,QAAA,CAAM,UAAU,iCAAiC,SAAe,kBAAA,EACjEA,EAAA,IAACO,EAAA,CACC,KAAK,eACL,QAAArB,EACA,MAAO,CAAE,SAAU,EAAK,EACxB,OAAQ,CAAC,CAAE,MAAAsB,CAAA,IACTH,EAAA,KAACI,EAAA,CACC,MAAOD,EAAM,MAAQ,OAAOA,EAAM,KAAK,EAAI,GAC3C,cAAsBE,GAAAF,EAAM,SAAS,OAAOE,CAAG,CAAC,EAEhD,SAAA,CAAAV,EAAAA,IAACW,GAAc,UAAU,SACvB,eAACC,EAAY,CAAA,YAAY,uBAAuB,CAClD,CAAA,QACCC,EACE,CAAA,SAAAtC,EAAsB,IAAIuC,SACxBC,EAA2B,CAAA,MAAO,OAAOD,EAAI,KAAK,EAChD,SAAAA,EAAI,OADUA,EAAI,KAErB,CACD,CACH,CAAA,CAAA,CAAA,CAAA,CACF,CAAA,CAEJ,EACF,SACC,MACC,CAAA,SAAA,CAACd,EAAA,IAAA,QAAA,CAAM,UAAU,iCAAiC,SAAW,cAAA,QAC5DgB,EAAU,CAAA,GAAGnC,EAAS,aAAa,EAAG,UAAU,qBAAsB,CAAA,CAAA,EACzE,EACAwB,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAL,EAAA,IAACiB,EAAA,CAAU,GAAGpC,EAAS,WAAW,EAChC,GAAG,YACH,QAAS,CAAC,CAACI,EAAM,WAAW,EAC5B,gBAAwByB,GAAA1B,EAAS,YAAa,CAAC,CAAC0B,CAAG,CAAA,CACrD,QACC,QAAM,CAAA,QAAQ,YAAY,UAAU,UAAU,SAAU,YAAA,CAAA,CAAA,EAC3D,SACC,MACC,CAAA,SAAA,CAAAV,MAACkB,EAAM,CAAA,QAAQ,eAAe,UAAU,YAAY,SAAM,SAAA,EAC1DlB,EAAA,IAACM,EAAA,CACC,GAAG,eACH,KAAK,OACL,OAAO,aACP,SAAUT,EACV,SAAUJ,EAAS,WAAaL,CAAA,CAClC,EACCE,GACCe,EAAA,KAAC,IAAE,CAAA,UAAU,gCAAgC,SAAA,CAAA,kBAAgBf,EAAK,IAAA,CAAK,CAAA,CAAA,EAE3E,EACCG,EAAS,OAAUO,MAAA,MAAA,CAAI,UAAU,gCAAiC,SAAA,OAAOP,EAAS,iBAAiB,MAAQA,EAAS,MAAM,QAAUA,EAAS,KAAK,EAAE,SACpJ0B,EACC,CAAA,SAAA,CAAAnB,EAAA,IAACoB,GAAO,KAAK,SAAS,QAAQ,UAAU,QAAS,IAAM,CAAE1C,EAAa,EAAK,EAAGa,EAAQ,IAAI,CAAA,EAAM,SAAM,SAAA,EACrGS,EAAA,IAAAoB,EAAA,CAAO,KAAK,SAAS,UAAU,OAAO,SAAUhC,GAAgBK,EAAS,UAAY,SAASA,EAAA,UAAY,YAAc,MAAO,CAAA,CAAA,CAClI,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,EAEM4B,EAAkC,IAAM,CAC5C,KAAM,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAS,EAAK,EAC5C,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAAqC,IAAI,EACjE,CAACG,EAAYC,CAAa,EAAIJ,EAAA,SAA0B,CAAE,UAAW,EAAG,SAAU,GAAI,EACtF,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAK,EAGhDO,EAA4C,CAChD,CACE,YAAa,KACb,OAAQ,KACR,KAAOC,GAAoDA,EAAK,YAAc,GAChF,EACA,CACE,YAAa,OACb,OAAQ,OACR,KAAOA,GAAoDA,EAAK,YAAc,GAChF,EACA,CACE,YAAa,eACb,OAAQ,OACR,KAAOA,GAAoDA,EAAK,YAAc,GAChF,EACA,CACE,YAAa,cACb,OAAQ,cACR,KAAOA,GAAoDA,EAAK,YAAc,GAChF,EACA,CACE,YAAa,YACb,OAAQ,UACR,KAAOA,GAAoDA,EAAK,SAAA,EAAa,MAAQ,IACvF,EACA,CACE,GAAI,OACJ,OAAQ,GACR,KAAM,CAAC,CAAE,IAAAC,CAAA,IACPjC,EAAA,IAACoB,EAAA,CACC,QAAS,IAAM,CACbM,EAAWO,EAAI,QAAQ,EACvBV,EAAc,EAAI,CACpB,EACA,aAAW,yBACX,SAAU,EACV,QAAQ,UACR,KAAK,OACL,UAAU,eAEV,SAACvB,EAAA,IAAAkC,EAAA,CAAO,UAAU,UAAU,cAAY,MAAO,CAAA,CAAA,CACjD,EAEF,cAAe,GACf,mBAAoB,EAAA,CAExB,EAGM,CAAE,KAAAC,EAAM,UAAAC,EAAW,QAAAC,CAAA,EAAYC,EAAS,CAC5C,SAAU,CAAC,qBAAsBX,CAAU,EAC3C,QAAS,SAAY,CACb,MAAAY,EAAM,MAAMC,EAA+B,CAC/C,KAAM,CACJ,KAAMb,EAAW,UAAY,EAC7B,eAAgBA,EAAW,SAC3B,UAAWA,EAAW,UAAYA,EAAW,QAAA,CAC/C,CACD,EAEG,OAAAY,GAAO,OAAOA,GAAQ,UAAY,UAAWA,GAAO,eAAgBA,EAAYA,EAChFA,GAAO,OAAOA,GAAQ,UAAY,SAAUA,GAAOA,EAAI,KAAaA,EAAI,KACrE,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACD,EAEKE,EAAmC,MAAM,QAAQN,GAAM,KAAK,EAAIA,EAAK,MAAQ,CAAC,EAC9EO,EAAa,OAAOP,GAAM,YAAe,SAAWA,EAAK,WAAa,EAGtEQ,EAAgB,SAAY,CAChCb,EAAgB,EAAI,EAChB,GAAA,CACF,MAAMO,EAAQ,CAAA,QACd,CACAP,EAAgB,EAAK,CAAA,CAEzB,EAGE,OAAAzB,EAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAACL,EAAA,IAAA,MAAA,CAAI,UAAU,mCAAmC,SAAsB,yBAAA,EACxEK,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAL,EAAA,IAACoB,EAAA,CACC,QAASuB,EACT,QAAQ,UACR,KAAK,OACL,UAAU,YACV,SAAUP,GAAaP,EAEvB,eAACe,EAAU,CAAA,UAAW,WAAWf,EAAe,eAAiB,EAAE,EAAI,CAAA,CAAA,CACzE,EACAxB,EAAA,KAACe,EAAA,CACC,QAAS,IAAM,CAAEM,EAAW,IAAI,EAAGH,EAAc,EAAI,CAAG,EAExD,SAAA,CAACvB,EAAAA,IAAA6C,EAAA,CAAK,UAAU,SAAU,CAAA,EAAE,wBAAA,CAAA,CAAA,CAC9B,EACF,EACA7C,EAAA,IAAC8C,EAAA,CACC,MAAM,GACN,QAAAf,EACA,KAAMU,EACN,WAAAC,EACA,UAAAN,EACA,iBAAkB,GAClB,SAAUT,EAAW,SACrB,mBAAoBC,EACpB,qBAAsB,GACtB,mBAAoB,GACpB,cAAe,EAAA,CACjB,EACA5B,EAAA,IAACxB,EAAA,CACC,KAAM8C,EACN,aAAe7C,GAAS,CACtB8C,EAAc9C,CAAI,EACbA,GAAMiD,EAAW,IAAI,CAC5B,EACA,QAASD,GAAW,OACpB,UAAW,IAAMY,EAAQ,CAAA,CAAA,CAC3B,EACF,CAEJ,EClSMU,GAAiC,IAElC/C,EAAAA,IAAAgD,EAAA,CACC,SAAChD,EAAAA,IAAAqB,EAAA,CAAsB,CAAA,EACzB"}