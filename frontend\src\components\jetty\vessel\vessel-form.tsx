import { columns as hotColumns, type TableRowData } from '@/components/applications/handsontable-column';
import { renderDeleteButton, renderPreviewButton, renderSubmitButton } from '@/components/applications/handsontable-renderer';
import { Button } from '@/components/ui/button';
import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HotTable } from '@handsontable/react-wrapper';
import type { QueryClient } from '@tanstack/react-query';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
// If you have JettySelect, import it:
// import JettySelect from '@/components/ui/JettySelect';

// Dummy JettySelect for demonstration (replace with your actual JettySelect)
const JettySelect = ({ value, onValueChange }: { value: string; onValueChange: (v: string) => void }) => (
  <Input value={value} onChange={e => onValueChange(e.target.value)} />
);

type VesselFormProps = {
  mode: 'edit' | 'create';
  docNum: string;
  vesselType: string;
  vesselName: string;
  voyage: string;
  jetty: string;
  arrivalDate: string;
  departureDate: string;
  portOrigin: string;
  destinationPort: string;
  barge: string;
  tableData: TableRowData[];
  setDocNum: (v: string) => void;
  setVesselType: (v: string) => void;
  setVesselName: (v: string) => void;
  setVoyage: (v: string) => void;
  setJetty: (v: string) => void;
  setArrivalDate: (v: string) => void;
  setDepartureDate: (v: string) => void;
  setPortOrigin: (v: string) => void;
  setDestinationPort: (v: string) => void;
  setBarge: (v: string) => void;
  setTableData: (rows: TableRowData[]) => void;
  loadingStates: Map<number, boolean>;
  setLoadingState: (row: number, loading: boolean) => void;
  queryClient: QueryClient;
  id?: string;
  onSave: () => void;
  isSaving: boolean;
};

const VesselForm: React.FC<VesselFormProps> = ({
  mode,
  docNum,
  vesselType,
  vesselName,
  voyage,
  jetty,
  arrivalDate,
  departureDate,
  portOrigin,
  destinationPort,
  barge,
  tableData,
  setDocNum,
  setVesselType,
  setVesselName,
  setVoyage,
  setJetty,
  setArrivalDate,
  setDepartureDate,
  setPortOrigin,
  setDestinationPort,
  setBarge,
  setTableData,
  loadingStates,
  setLoadingState,
  queryClient,
  id,
  onSave,
  isSaving,
}) => {
  const hotTableComponent = useRef(null);
  const { t } = useTranslation();

  const columnConfig = hotColumns.map(col => {
    if (col.data === 'id') {
      return { ...col, renderer: renderPreviewButton(tableData, () => { }, loadingStates, setLoadingState) };
    }
    if (col.data === 'submit') {
      return { ...col, renderer: renderSubmitButton(tableData, vesselType, loadingStates, setLoadingState, queryClient, id ?? '') };
    }
    if (col.data === 'delete') {
      return { ...col, renderer: renderDeleteButton(tableData, setTableData, loadingStates, setLoadingState, queryClient, id ?? '') };
    }
    return col;
  });

  return (
    <div className="bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4">
      <div className="mb-6">
        <h2 className="text-lg font-bold text-gray-800 dark:text-white">{mode === 'edit' ? 'Edit Vessel' : 'Create Vessel'}</h2>
        <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6 mb-2">
        <FormSection>
          <FormField label={t('form.labels.docNum')} labelWidth="100px">
            <Input id="docNum" value={docNum} onChange={e => setDocNum(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.vesselType')} labelWidth="100px">
            <Select value={vesselType} onValueChange={setVesselType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select Vessel Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Import">Import</SelectItem>
                <SelectItem value="Export">Export</SelectItem>
                <SelectItem value="IN">Local In</SelectItem>
                <SelectItem value="OUT">Local Out</SelectItem>
              </SelectContent>
            </Select>
          </FormField>
          <FormField label={t('form.labels.vesselName')} labelWidth="100px">
            <Input id="vesselName" value={vesselName} onChange={e => setVesselName(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.voyage')} labelWidth="100px">
            <Input id="voyage" value={voyage} onChange={e => setVoyage(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.jetty')} labelWidth="100px">
            <JettySelect value={jetty} onValueChange={setJetty} />
          </FormField>
        </FormSection>
        <FormSection>
          <FormField label={t('form.labels.arrivalDate')} labelWidth="100px">
            <Input id="arrivalDate" type="datetime-local" value={arrivalDate} onChange={e => setArrivalDate(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.departureDate')} labelWidth="100px">
            <Input id="departureDate" type="datetime-local" value={departureDate} onChange={e => setDepartureDate(e.target.value)} />
          </FormField>
        </FormSection>
        <FormSection>
          <FormField label={t('form.labels.portOrigin')} labelWidth="100px">
            <Input id="portOrigin" value={portOrigin} onChange={e => setPortOrigin(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.destinationPort')} labelWidth="100px">
            <Input id="destinationPort" value={destinationPort} onChange={e => setDestinationPort(e.target.value)} />
          </FormField>
          <FormField label={t('form.labels.bargeName')} labelWidth="100px">
            <Input id="barge" value={barge} onChange={e => setBarge(e.target.value)} />
          </FormField>
        </FormSection>
      </div>
      <div style={{ maxWidth: '100%', overflowX: 'auto' }} className="mb-8">
        <HotTable
          ref={hotTableComponent}
          themeName="ht-theme-main"
          data={tableData}
          columns={columnConfig}
          colHeaders={hotColumns.map(col => col.title)}
          rowHeaders={true}
          height="50vh"
          rowHeights={27}
          currentRowClassName="currentRow"
          currentColClassName="currentCol"
          licenseKey="non-commercial-and-evaluation"
          stretchH="all"
          contextMenu={true}
          manualColumnResize={true}
          manualRowResize={true}
          autoColumnSize={false}
          autoRowSize={false}
          startRows={1}
          viewportRowRenderingOffset={1000}
          viewportColumnRenderingOffset={100}
          dropdownMenu={true}
          filters={true}
          colWidths={80}
          width="100%"
          persistentState={true}
        />
      </div>
      <div className="flex justify-end">
        <Button onClick={onSave} disabled={isSaving} className="px-6 py-2 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed">
          {isSaving ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}
        </Button>
      </div>
    </div>
  );
};

export default VesselForm; 