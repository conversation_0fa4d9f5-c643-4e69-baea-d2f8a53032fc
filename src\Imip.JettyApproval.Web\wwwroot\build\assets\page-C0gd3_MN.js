import{j as t}from"./vendor-6tJeyfYI.js";import{A as p,F as c,M as u}from"./app-layout-rNt37hVL.js";import{c as d,b as x}from"./buildApiPayloadVessel-BEaNS5GF.js";import{D as f}from"./data-grid-DZ2U-5jU.js";import{$ as y}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./badge-DWaCYvGm.js";import"./arrow-up-right-DyuQRH0Y.js";import"./checkbox-D1loOtZt.js";import"./dialog-BmEXyFlW.js";import"./input-DlXlkYlT.js";import"./popover-ChFN9yvN.js";import"./table-BKSoE52x.js";import"./useDebounce-B2N8e_3P.js";import"./index-CaiFFM4D.js";import"./TableSkeleton-CIQBoxBh.js";import"./skeleton-DAOxGMKm.js";import"./plus-PD53KOti.js";import"./arrow-up-DDQ17ADi.js";import"./chevron-left-DJFXm33k.js";const j=[{id:"docNum",desc:!0}],C=()=>{const{t:s}=c(),r=d();return t.jsx("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:t.jsx(f,{columns:r,title:s("datagrid.nonCustomArea.localVessel"),queryKey:["local-vessel-list"],manualSorting:!0,manualFiltering:!0,queryFn:async({pageIndex:e,pageSize:a,sorting:i=j,filters:l,globalFilter:m})=>{const n=x({pageIndex:e,pageSize:a,sorting:i,filters:l,globalFilter:m,isCustomArea:!1}),o=await u.filterLocalVessels(n);return{items:o.data?.items??[],totalCount:o.data?.totalCount??0}}})})},I=()=>t.jsxs(p,{children:[t.jsx(y,{title:"Local Vessel"}),t.jsx("div",{className:"flex flex-col space-y-4 p-4",children:t.jsx(C,{})})]});export{I as default};
//# sourceMappingURL=page-C0gd3_MN.js.map
