using Imip.JettyApproval.Web.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Controllers;

/// <summary>
/// Custom exception for EKB service errors with validation details
/// </summary>
public class EkbServiceException : Exception
{
    public int StatusCode { get; }
    public object ValidationErrors { get; }
    public string ErrorDetails { get; }

    public EkbServiceException(string message, int statusCode, object validationErrors = null, string errorDetails = null)
        : base(message)
    {
        StatusCode = statusCode;
        ValidationErrors = validationErrors;
        ErrorDetails = errorDetails;
    }
}

/// <summary>
/// Proxy controller for EKB API requests
/// Forwards requests to EKB service with proper authentication and domain mapping
/// </summary>
[ApiController]
[Route("api/ekb-proxy")]
[Authorize]
public class EkbProxyController : ControllerBase
{
    private readonly AppToAppService _appToAppService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<EkbProxyController> _logger;

    public EkbProxyController(
        AppToAppService appToAppService,
        IConfiguration configuration,
        ILogger<EkbProxyController> logger)
    {
        _appToAppService = appToAppService;
        _configuration = configuration;
        _logger = logger;
    }

    /// <summary>
    /// Generic proxy endpoint for GET requests
    /// </summary>
    [HttpGet("{**path}")]
    public async Task<IActionResult> ProxyGet(string path)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();
            var endpoint = path;
            if (Request.QueryString.HasValue)
            {
                endpoint += Request.QueryString.Value;
            }

            // Detect if this is a file stream request (e.g., /files/stream/)
            if (endpoint.StartsWith("files/stream/", StringComparison.OrdinalIgnoreCase) || endpoint.Contains("/files/stream/", StringComparison.OrdinalIgnoreCase))
            {
                var streamResult = await _appToAppService.StreamFileFromOtherAppAsync(ekbBaseUrl, endpoint, HttpMethod.Get);
                return streamResult;
            }

            var result = await _appToAppService.CallOtherAppAsync<object>(ekbBaseUrl, endpoint, HttpMethod.Get);
            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy GET request");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in GET request to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy GET request to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Generic proxy endpoint for POST requests
    /// </summary>
    [HttpPost("{**path}")]
    public async Task<IActionResult> ProxyPost(string path, [FromBody] object data = null)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();

            // Include query string parameters in the path
            var fullPath = path;
            if (Request.QueryString.HasValue)
            {
                fullPath += Request.QueryString.Value;
            }

            // Use CallOtherAppAsync like the working EKB controller
            var result = await _appToAppService.CallOtherAppAsync<object>(ekbBaseUrl, fullPath, HttpMethod.Post, data);

            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy POST request");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in POST request to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy POST request to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Generic proxy endpoint for PUT requests
    /// </summary>
    [HttpPut("{**path}")]
    public async Task<IActionResult> ProxyPut(string path, [FromBody] object data = null)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();

            // Include query string parameters in the path
            var fullPath = path;
            if (Request.QueryString.HasValue)
            {
                fullPath += Request.QueryString.Value;
            }

            // Use CallOtherAppAsync like the working EKB controller
            var result = await _appToAppService.CallOtherAppAsync<object>(ekbBaseUrl, fullPath, HttpMethod.Put, data);

            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy PUT request");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in PUT request to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy PUT request to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Generic proxy endpoint for PATCH requests
    /// </summary>
    [HttpPatch("{**path}")]
    public async Task<IActionResult> ProxyPatch(string path, [FromBody] object data = null)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();

            // Include query string parameters in the path
            var fullPath = path;
            if (Request.QueryString.HasValue)
            {
                fullPath += Request.QueryString.Value;
            }

            // Use CallOtherAppAsync like the working EKB controller
            var result = await _appToAppService.CallOtherAppAsync<object>(ekbBaseUrl, fullPath, new HttpMethod("PATCH"), data);

            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy PATCH request");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in PATCH request to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy PATCH request to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Generic proxy endpoint for DELETE requests
    /// </summary>
    [HttpDelete("{**path}")]
    public async Task<IActionResult> ProxyDelete(string path)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();

            // Include query string parameters in the path
            var fullPath = path;
            if (Request.QueryString.HasValue)
            {
                fullPath += Request.QueryString.Value;
            }

            // Use CallOtherAppAsync like the working EKB controller
            var result = await _appToAppService.CallOtherAppAsync<object>(ekbBaseUrl, fullPath, HttpMethod.Delete);

            return Ok(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy DELETE request");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in DELETE request to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy DELETE request to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Specialized endpoint for file uploads
    /// </summary>
    [HttpPost("upload/{**path}")]
    public async Task<IActionResult> ProxyFileUpload(string path, IFormFileCollection files)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();
            var targetUrl = $"{ekbBaseUrl}/{path.TrimStart('/')}";

            // Create multipart form data content
            using var formData = new MultipartFormDataContent();

            // Add files
            foreach (var file in files)
            {
                if (file.Length > 0)
                {
                    var fileContent = new StreamContent(file.OpenReadStream());
                    fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType ?? "application/octet-stream");
                    formData.Add(fileContent, file.Name, file.FileName);
                }
            }

            // Add form fields
            foreach (var key in Request.Form.Keys.Where(k => k != "files"))
            {
                formData.Add(new StringContent(Request.Form[key]), key);
            }

            var headers = ExtractForwardHeaders();
            var result = await _appToAppService.ProxyFileUploadAsync<string>(targetUrl, formData, headers);

            return Content(result, "application/json");
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy file upload");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in file upload to path: {Path}", path);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy file upload to path: {Path}", path);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Get EKB base URL from configuration
    /// </summary>
    private string GetEkbBaseUrl()
    {
        return _configuration["ExternalApps:EKB:BaseUrl"] ?? "https://ekb-dev.imip.co.id";
    }

    /// <summary>
    /// Specialized endpoints for vessel filter operations (POST with body)
    /// </summary>
    [HttpPost("vessel-filter/{vesselType}")]
    public async Task<IActionResult> ProxyVesselFilter(string vesselType, [FromBody] object filterData)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();
            string targetUrl = vesselType.ToLowerInvariant() switch
            {
                "import" => $"{ekbBaseUrl}/api/ekb/import-vessel/filter-list",
                "export" => $"{ekbBaseUrl}/api/ekb/export-vessel/filter-list",
                "local" => $"{ekbBaseUrl}/api/ekb/local-vessel/filter-list",
                "trading" => $"{ekbBaseUrl}/api/ekb/trading-vessel/filter-list",
                _ => throw new ArgumentException($"Invalid vessel type: {vesselType}")
            };

            var headers = ExtractForwardHeaders();
            var result = await _appToAppService.ProxyRequestAsync<string>(targetUrl, HttpMethod.Post, filterData, headers);

            return Content(result, "application/json");
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy vessel filter");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in vessel filter for type: {VesselType}", vesselType);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy vessel filter for type: {VesselType}", vesselType);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Specialized endpoints for master data filter operations (POST with body)
    /// </summary>
    [HttpPost("master-filter/{dataType}")]
    public async Task<IActionResult> ProxyMasterDataFilter(string dataType, [FromBody] object filterData)
    {
        try
        {
            var ekbBaseUrl = GetEkbBaseUrl();
            string targetUrl = dataType.ToLowerInvariant() switch
            {
                "business-partner" => $"{ekbBaseUrl}/api/ekb/business-partner/filter-list",
                "tenant" => $"{ekbBaseUrl}/api/ekb/tenant/filter-list",
                "jetty" => $"{ekbBaseUrl}/api/ekb/jetty/filter-list",
                "agent" => $"{ekbBaseUrl}/api/master/agent/filter-list",
                "surveyor" => $"{ekbBaseUrl}/api/ekb/surveyor/filter-list",
                "cargo" => $"{ekbBaseUrl}/api/ekb/cargo/filter-list",
                "destination-port" => $"{ekbBaseUrl}/api/ekb/destination-port/filter-list",
                "port-of-loading" => $"{ekbBaseUrl}/api/ekb/port-of-loading/filter-list",
                _ => throw new ArgumentException($"Invalid master data type: {dataType}")
            };

            var headers = ExtractForwardHeaders();
            var result = await _appToAppService.ProxyRequestAsync<string>(targetUrl, HttpMethod.Post, filterData, headers);

            return Content(result, "application/json");
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Unauthorized access in EKB proxy master data filter");
            return Unauthorized(new { message = ex.Message });
        }
        catch (EkbServiceException ex)
        {
            _logger.LogWarning(ex, "EKB service error in master data filter for type: {DataType}", dataType);

            // If we have error details (raw response from EKB), try to return it as-is
            if (!string.IsNullOrEmpty(ex.ErrorDetails))
            {
                try
                {
                    // Try to parse the error details as JSON and return it directly
                    var errorJson = JsonSerializer.Deserialize<JsonElement>(ex.ErrorDetails);
                    return StatusCode(ex.StatusCode, errorJson);
                }
                catch (JsonException)
                {
                    // If parsing fails, return the raw error details as content
                    return StatusCode(ex.StatusCode, ex.ErrorDetails);
                }
            }

            // Fallback to the structured response if no error details
            return StatusCode(ex.StatusCode, new
            {
                message = ex.Message,
                details = ex.ErrorDetails,
                validationErrors = ex.ValidationErrors
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in EKB proxy master data filter for type: {DataType}", dataType);
            return StatusCode(500, new { message = "Internal server error", detail = ex.Message });
        }
    }

    /// <summary>
    /// Extract headers that should be forwarded to the target service
    /// </summary>
    private Dictionary<string, string> ExtractForwardHeaders()
    {
        var headers = new Dictionary<string, string>();

        // Forward specific headers if present
        var headersToForward = new[] { "Accept", "Accept-Language", "User-Agent", "X-Requested-With" };

        foreach (var headerName in headersToForward)
        {
            if (Request.Headers.ContainsKey(headerName))
            {
                headers[headerName] = Request.Headers[headerName].ToString();
            }
        }

        return headers;
    }
}