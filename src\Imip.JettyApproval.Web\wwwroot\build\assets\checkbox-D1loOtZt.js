import{j as e}from"./vendor-6tJeyfYI.js";import{k as s,l as a,C as d,$ as n}from"./app-layout-rNt37hVL.js";import{am as o,an as c}from"./radix-e4nK4mWk.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"M5 12h14",key:"1ays0h"}]],l=s("minus",u),m=n(`
    group peer bg-background shrink-0 rounded-md border border-input ring-offset-background focus-visible:outline-none 
    focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 
    aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20
    [[data-invalid=true]_&]:border-destructive/60 [[data-invalid=true]_&]:ring-destructive/10  dark:[[data-invalid=true]_&]:border-destructive dark:[[data-invalid=true]_&]:ring-destructive/20,
    data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground data-[state=indeterminate]:bg-primary data-[state=indeterminate]:border-primary data-[state=indeterminate]:text-primary-foreground
    `,{variants:{size:{sm:"size-4.5 [&_svg]:size-3",md:"size-5 [&_svg]:size-3.5",lg:"size-5.5 [&_svg]:size-4"}},defaultVariants:{size:"md"}});function k({className:r,size:t,...i}){return e.jsx(o,{"data-slot":"checkbox",className:a(m({size:t}),r),...i,children:e.jsxs(c,{className:a("flex items-center justify-center text-current"),children:[e.jsx(d,{className:"group-data-[state=indeterminate]:hidden"}),e.jsx(l,{className:"hidden group-data-[state=indeterminate]:block"})]})})}export{k as C};
//# sourceMappingURL=checkbox-D1loOtZt.js.map
