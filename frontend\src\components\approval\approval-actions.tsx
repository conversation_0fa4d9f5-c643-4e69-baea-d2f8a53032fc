import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/lib/useToast";
import { ekbProxyService } from "@/services/ekbProxyService";
import type { QueryClient } from "@tanstack/react-query";
import React, { useState } from "react";

interface ApprovalActionsProps {
  approvalId: string;
  isOpen: boolean;
  onClose: () => void;
  action: "approve" | "reject";
  vessel?: { id?: string; vesselType?: string | null };
  onSuccess?: () => void;
  queryClient: QueryClient;
}

const ApprovalActions: React.FC<ApprovalActionsProps> = ({
  approvalId,
  isOpen,
  onClose,
  action,
  vessel,
  onSuccess,
  queryClient,
}) => {
  const { toast } = useToast();
  const [notes, setNotes] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const endpoint = action === "approve"
        ? `/api/idjas/approval/approve/${approvalId}`
        : `/api/idjas/approval/reject/${approvalId}`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} approval`);
      }

      // Update vessel status if vessel information is available
      if (vessel?.id && vessel?.vesselType) {
        try {
          const newDocStatus = action === "approve" ? "Approved" : "Rejected";
          console.log(`Updating vessel ${vessel.id} (${vessel.vesselType}) status to: ${newDocStatus}`);

          // Update vessel based on vessel type using PUT methods
          switch (vessel.vesselType) {
            case 'Import': {
              // For import vessels, get existing data first to include all required fields
              const importVesselResponse = await ekbProxyService.getImportVessel(vessel.id);
              if (importVesselResponse.data) {
                const existingVessel = importVesselResponse.data;
                const updateData = {
                  createdBy: existingVessel.createdBy || '',
                  isLocked: existingVessel.isLocked || 'N',
                  isChange: existingVessel.isChange || 'N',
                  transType: existingVessel.transType || '',
                  docType: existingVessel.docType || '',
                  deleted: existingVessel.deleted || 'N',
                  docStatus: newDocStatus,
                  statusBms: existingVessel.statusBms || '',
                  concurrencyStamp: existingVessel.concurrencyStamp || '',
                  // Include other existing fields
                  docNum: existingVessel.docNum,
                  bp: existingVessel.bp,
                  vesselName: existingVessel.vesselName,
                  shipment: existingVessel.shipment,
                  shipmentNo: existingVessel.shipmentNo,
                  vesselArrival: existingVessel.vesselArrival,
                  updatedBy: existingVessel.updatedBy,
                  createdAt: existingVessel.createdAt,
                  updatedAt: existingVessel.updatedAt,
                  postingDate: existingVessel.postingDate,
                  color: existingVessel.color,
                  flags: existingVessel.flags,
                  remarks: existingVessel.remarks,
                  status: existingVessel.status,
                  bcType: existingVessel.bcType,
                  portOrigin: existingVessel.portOrigin,
                  emailToPpjk: existingVessel.emailToPpjk,
                  matchKey: existingVessel.matchKey,
                  voyage: existingVessel.voyage,
                  grossWeight: existingVessel.grossWeight,
                  vesselFlag: existingVessel.vesselFlag,
                  vesselDeparture: existingVessel.vesselDeparture,
                  vesselStatus: existingVessel.vesselStatus,
                  jetty: existingVessel.jetty,
                  destinationPort: existingVessel.destinationPort,
                  berthingDate: existingVessel.berthingDate,
                  anchorageDate: existingVessel.anchorageDate,
                  type: existingVessel.type,
                  jettyUpdate: existingVessel.jettyUpdate,
                  reportDate: existingVessel.reportDate,
                  unloadingDate: existingVessel.unloadingDate,
                  finishUnloadingDate: existingVessel.finishUnloadingDate,
                  grtWeight: existingVessel.grtWeight,
                  invoiceStatus: existingVessel.invoiceStatus,
                  agentId: existingVessel.agentId,
                  agentName: existingVessel.agentName,
                  surveyorId: existingVessel.surveyorId,
                  tradingId: existingVessel.tradingId,
                  jettyId: existingVessel.jettyId,
                  vesselId: existingVessel.vesselId,
                  masterAgentId: existingVessel.masterAgentId,
                  masterTradingId: existingVessel.masterTradingId,
                  masterSurveyorId: existingVessel.masterSurveyorId,
                };
                await ekbProxyService.updateImportVessel(vessel.id, updateData);
              } else {
                throw new Error('Failed to fetch existing import vessel data');
              }
              break;
            }
            case 'Export': {
              // For export vessels, get existing data first to include all required fields
              const exportVesselResponse = await ekbProxyService.getExportVessel(vessel.id);
              if (exportVesselResponse.data) {
                const existingVessel = exportVesselResponse.data;
                const updateData = {
                  docNum: existingVessel.docNum || '',
                  postingDate: existingVessel.postingDate,
                  vesselName: existingVessel.vesselName,
                  vesselArrival: existingVessel.vesselArrival,
                  voyage: existingVessel.voyage || '',
                  shipment: existingVessel.shipment || '',
                  vesselQty: existingVessel.vesselQty,
                  portOrigin: existingVessel.portOrigin || '',
                  deleted: existingVessel.deleted || 'N',
                  updatedBy: existingVessel.updatedBy,
                  docStatus: newDocStatus,
                  statusBms: existingVessel.statusBms || '',
                  docType: existingVessel.docType || '',
                  concurrencyStamp: existingVessel.concurrencyStamp || '',
                  // Include other existing fields
                  vesselDeparture: existingVessel.vesselDeparture,
                  destinationPort: existingVessel.destinationPort,
                  remarks: existingVessel.remarks,
                  grossWeight: existingVessel.grossWeight,
                  vesselFlag: existingVessel.vesselFlag,
                  vesselStatus: existingVessel.vesselStatus,
                  jetty: existingVessel.jetty,
                  berthingDate: existingVessel.berthingDate,
                  anchorageDate: existingVessel.anchorageDate,
                  reportDate: existingVessel.reportDate,
                  unloadingDate: existingVessel.unloadingDate,
                  finishUnloadingDate: existingVessel.finishUnloadingDate,
                  deadWeight: existingVessel.deadWeight,
                  grtWeight: existingVessel.grtWeight,
                  invoiceStatus: existingVessel.invoiceStatus,
                  agentId: existingVessel.agentId,
                  agentName: existingVessel.agentName,
                  surveyorId: existingVessel.surveyorId,
                  tradingId: existingVessel.tradingId,
                  jettyId: existingVessel.jettyId,
                  vesselId: existingVessel.vesselId,
                  masterAgentId: existingVessel.masterAgentId,
                  masterTradingId: existingVessel.masterTradingId,
                  masterSurveyorId: existingVessel.masterSurveyorId,
                };
                await ekbProxyService.updateExportVessel(vessel.id, updateData);
              } else {
                throw new Error('Failed to fetch existing export vessel data');
              }
              break;
            }
            case 'LocalIn':
            case 'LocalOut': {
              // For local vessels, we need to get the existing data first to include all required fields
              const localVesselResponse = await ekbProxyService.getLocalVessel(vessel.id);
              if (localVesselResponse.data) {
                const existingVessel = localVesselResponse.data;
                // Update with all required fields from existing vessel plus the new status
                const updateData = {
                  docNum: existingVessel.docNum || '',
                  deleted: existingVessel.deleted || 'N',
                  shipment: existingVessel.shipment || '',
                  statusBms: existingVessel.statusBms || '',
                  transType: existingVessel.transType || '',
                  portOrigin: existingVessel.portOrigin || '',
                  vesselType: existingVessel.vesselType || 'Local',
                  destinationPort: existingVessel.destinationPort || '',
                  concurrencyStamp: existingVessel.concurrencyStamp || '',
                  docType: 'Local',
                  docStatus: newDocStatus,
                  // Include other existing fields to maintain data integrity
                  postingDate: existingVessel.postingDate,
                  vesselName: existingVessel.vesselName,
                  tongkang: existingVessel.tongkang,
                  vesselArrival: existingVessel.vesselArrival,
                  vesselDeparture: existingVessel.vesselDeparture,
                  vesselQty: existingVessel.vesselQty,
                  remark: existingVessel.remark,
                  updatedBy: existingVessel.updatedBy,
                  voyage: existingVessel.voyage,
                  grossWeight: existingVessel.grossWeight,
                  jetty: existingVessel.jetty,
                  status: existingVessel.status,
                  beratTugboat: existingVessel.beratTugboat,
                  berthingDate: existingVessel.berthingDate,
                  anchorageDate: existingVessel.anchorageDate,
                  reportDate: existingVessel.reportDate,
                  unloadingDate: existingVessel.unloadingDate,
                  finishUnloadingDate: existingVessel.finishUnloadingDate,
                  grtWeight: existingVessel.grtWeight,
                  invoiceStatus: existingVessel.invoiceStatus,
                  agentId: existingVessel.agentId,
                  agentName: existingVessel.agentName,
                  grtVessel: existingVessel.grtVessel,
                  surveyorId: existingVessel.surveyorId,
                  tradingId: existingVessel.tradingId,
                  jettyId: existingVessel.jettyId,
                  vesselId: existingVessel.vesselId,
                  bargeId: existingVessel.bargeId,
                  masterAgentId: existingVessel.masterAgentId,
                  masterTradingId: existingVessel.masterTradingId,
                  masterSurveyorId: existingVessel.masterSurveyorId,
                };
                await ekbProxyService.updateLocalVessel(vessel.id, updateData);
              } else {
                throw new Error('Failed to fetch existing local vessel data');
              }
              break;
            }
            default:
              console.warn(`Unknown vessel type: ${vessel.vesselType}`);
          }

          console.log(`Successfully updated vessel status to: ${newDocStatus}`);
        } catch (vesselError) {
          console.error('Error updating vessel status:', vesselError);
          // Don't fail the entire operation if vessel update fails
          toast({
            title: "Warning",
            description: "Approval processed but vessel status update failed",
            variant: "destructive",
          });
        }
      } else {
        console.log('No vessel information available for status update');
      }

      toast({
        title: "Success",
        description: `Approval ${action}d successfully`,
        variant: "default",
      });

      // Invalidate the approval-stages query to refetch the table
      await queryClient.invalidateQueries({ queryKey: ["approval-stages"] });

      onSuccess?.();
      onClose();
      setNotes("");
    } catch (error) {
      console.error(`Error ${action}ing approval:`, error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${action} approval`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setNotes("");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {action === "approve" ? "Approve" : "Reject"} Request
          </DialogTitle>
          <DialogDescription>
            {action === "approve"
              ? "Are you sure you want to approve this request? You can add optional notes below."
              : "Please provide a reason for rejecting this request."
            }
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="notes" className="text-sm font-medium">
              {action === "approve" ? "Notes (Optional)" : "Rejection Reason"}
            </label>
            <Textarea
              id="notes"
              placeholder={
                action === "approve"
                  ? "Add any additional notes..."
                  : "Please explain why this request is being rejected..."
              }
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              disabled={isLoading}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant={action === "approve" ? "default" : "destructive"}
            onClick={handleSubmit}
            disabled={isLoading || (action === "reject" && !notes.trim())}
          >
            {isLoading ? "Processing..." : action === "approve" ? "Approve" : "Reject"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ApprovalActions;
