import { ekbProxyService } from "@/services/ekbProxyService";
import { DataGrid } from "@/components/ui/data-grid";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { buildApiPayloadVessel } from "@/lib/queryHelper/buildApiPayloadVessel";
import React from "react";
import ErrorBoundary from "../ui/error-boundary";
import { useExportColumns, useImportColumns, useLocalColumns } from "./custom-area-jetty.columns";
import { ContentCard } from "../layout/content-card";

const DEFAULT_SORTING = [{ id: 'docNum', desc: true }];

const CustomAreaJettyContent: React.FC = () => {
  const importColumns = useImportColumns();
  const exportColumns = useExportColumns();
  const localColumns = useLocalColumns();

  return (
    <ContentCard>

      <Tabs defaultValue="import" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="import">Import Vessel</TabsTrigger>
          <TabsTrigger value="export">Export Vessel</TabsTrigger>
          <TabsTrigger value="local">Local Vessel</TabsTrigger>
        </TabsList>
        <TabsContent value="import">
          <DataGrid
            columns={importColumns}
            title="Import Vessel"
            queryKey={["import-vessel-list"]}
            manualSorting={true}
            manualFiltering={true}
            queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {
              const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter });
              const res = await ekbProxyService.filterImportVessels(payload);
              return {
                items: res.data?.items ?? [],
                totalCount: res.data?.totalCount ?? 0,
              };
            }}
          />
        </TabsContent>
        <TabsContent value="export">
          <DataGrid
            columns={exportColumns}
            title="Export Vessel"
            queryKey={["export-vessel-list"]}
            manualSorting={true}
            manualFiltering={true}
            queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {
              const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter });
              const res = await ekbProxyService.filterExportVessels(payload);
              return {
                items: res.data?.items ?? [],
                totalCount: res.data?.totalCount ?? 0,
              };
            }}
          />
        </TabsContent>
        <TabsContent value="local">
          <DataGrid
            columns={localColumns}
            title="Local Vessel"
            queryKey={["local-vessel-list"]}
            manualSorting={true}
            manualFiltering={true}
            queryFn={async ({ pageIndex, pageSize, sorting = DEFAULT_SORTING, filters, globalFilter }) => {
              const payload = buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter });
              const res = await ekbProxyService.filterLocalVessels(payload);
              return {
                items: res.data?.items ?? [],
                totalCount: res.data?.totalCount ?? 0,
              };
            }}
          />
        </TabsContent>
      </Tabs>
    </ContentCard>
  );
};

const CustomAreaJetty: React.FC = () => {
  return (
    <ErrorBoundary>
      <CustomAreaJettyContent />
    </ErrorBoundary>
  );
};

export default CustomAreaJetty; 