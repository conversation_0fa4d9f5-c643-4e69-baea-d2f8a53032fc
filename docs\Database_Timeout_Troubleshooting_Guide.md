# Database Timeout Troubleshooting Guide

## 🚨 Error Analysis

### **Primary Error**
```
SqlException: Execution Timeout Expired. The timeout period elapsed prior to completion of the operation or the server is not responding.
```

### **Secondary Error**
```
Win32Exception: The wait operation timed out.
```

## 🔍 Root Cause

The database timeout error occurs when SQL queries take longer than the configured timeout period to execute. This can happen due to:

1. **Long-running queries** - Complex joins, large data sets, missing indexes
2. **Database server performance issues** - High CPU, memory, or disk I/O
3. **Network connectivity problems** - Slow or unstable network connection
4. **Blocking/deadlocks** - Other queries blocking the current operation
5. **Insufficient timeout configuration** - Default timeout too low for complex operations

## 🛠️ Solutions Implemented

### **1. Connection String Timeout Configuration**

**Files Updated:**
- `src/Imip.JettyApproval.Web/appsettings.json`
- `src/Imip.JettyApproval.DbMigrator/appsettings.json`

**Changes:**
```json
"ConnectionStrings": {
  "Default": "Server=127.0.0.1;Database=JETTY_APPROVAL;User ID=sa;Password=*********;TrustServerCertificate=true;Encrypt=true;MultipleActiveResultSets=false;Command Timeout=120;"
}
```

**Impact:** Increased command timeout from default 30 seconds to 120 seconds (2 minutes).

### **2. Entity Framework Configuration**

**File:** `src/Imip.JettyApproval.EntityFrameworkCore/EntityFrameworkCore/JettyApprovalEntityFrameworkCoreModule.cs`

**Changes:**
```csharp
options.UseSqlServer(optionsBuilder =>
{
    // Configure command timeout to 2 minutes (120 seconds)
    optionsBuilder.CommandTimeout(120);
    
    // Enable query splitting for better performance with complex queries
    optionsBuilder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
    
    // Enable retry on failure for transient errors
    optionsBuilder.EnableRetryOnFailure(
        maxRetryCount: 3,
        maxRetryDelay: TimeSpan.FromSeconds(5),
        errorNumbersToAdd: null);
});
```

**Benefits:**
- **Command Timeout**: 120 seconds for all EF Core operations
- **Query Splitting**: Improves performance for complex queries with joins
- **Retry Logic**: Automatically retries failed operations up to 3 times

### **3. Database Configuration Section**

**File:** `src/Imip.JettyApproval.Web/appsettings.json`

**Added:**
```json
"Database": {
  "CommandTimeout": 120,
  "EnableRetryOnFailure": true,
  "MaxRetryCount": 3,
  "MaxRetryDelay": 5,
  "EnableQuerySplitting": true
}
```

## 📊 Current Timeout Configuration Summary

| Component | Timeout | Purpose |
|-----------|---------|---------|
| **SQL Connection** | 120 seconds | Database connection timeout |
| **EF Core Command** | 120 seconds | Entity Framework operations |
| **HTTP Client (EKB)** | 30 seconds | External EKB API calls |
| **HTTP Client (Auth)** | 15 seconds | External authentication |
| **SFTP Operations** | 60 seconds | File storage operations |
| **SFTP Connection** | 30 seconds | SFTP connection timeout |

## 🔧 Additional Troubleshooting Steps

### **1. Identify Slow Queries**

**SQL Server Management Studio:**
```sql
-- Find currently running queries
SELECT 
    r.session_id,
    r.start_time,
    r.status,
    r.command,
    r.wait_type,
    r.wait_time,
    r.cpu_time,
    r.total_elapsed_time,
    t.text AS query_text
FROM sys.dm_exec_requests r
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.session_id > 50
ORDER BY r.total_elapsed_time DESC;
```

### **2. Check Database Performance**

**Monitor these metrics:**
- CPU usage
- Memory usage
- Disk I/O
- Active connections
- Blocking sessions

### **3. Query Optimization**

**Common solutions:**
- Add missing indexes
- Optimize WHERE clauses
- Use query splitting for complex joins
- Implement pagination for large result sets
- Consider caching for frequently accessed data

### **4. Application-Level Solutions**

**Async Operations:**
```csharp
// Use async methods for database operations
var result = await repository.GetListAsync();

// Use cancellation tokens for long operations
var result = await repository.GetListAsync(cancellationToken);
```

**Pagination:**
```csharp
// Implement pagination for large datasets
var pagedResult = await repository.GetPagedListAsync(
    skipCount: 0, 
    maxResultCount: 20);
```

## 🚨 Emergency Response

### **If Timeout Errors Persist:**

1. **Immediate Actions:**
   - Check database server health
   - Identify and kill blocking queries
   - Restart application if necessary
   - Monitor system resources

2. **Short-term Solutions:**
   - Increase timeout values temporarily
   - Disable non-essential background jobs
   - Implement query caching
   - Add database indexes

3. **Long-term Solutions:**
   - Database performance tuning
   - Query optimization
   - Hardware upgrades
   - Database partitioning
   - Read replicas for reporting

## 📈 Monitoring and Alerts

### **Set up monitoring for:**
- Database query execution times
- Connection pool usage
- Failed query attempts
- System resource utilization

### **Alert thresholds:**
- Query execution time > 30 seconds
- Connection pool exhaustion
- High CPU/Memory usage
- Frequent timeout errors

## 🧪 Testing Recommendations

1. **Load Testing**: Simulate high concurrent user load
2. **Query Performance**: Test complex queries under load
3. **Timeout Scenarios**: Verify timeout handling works correctly
4. **Recovery Testing**: Test application recovery after database issues

## 📝 Best Practices

1. **Always use async/await** for database operations
2. **Implement proper error handling** for timeout scenarios
3. **Use connection pooling** efficiently
4. **Monitor query performance** regularly
5. **Keep statistics updated** on database tables
6. **Regular maintenance** - index rebuilding, statistics updates
7. **Implement circuit breakers** for external dependencies

## 🔄 Next Steps

1. Monitor application for timeout improvements
2. Implement query performance monitoring
3. Consider adding database performance dashboards
4. Plan for database optimization review
5. Document any recurring timeout patterns
