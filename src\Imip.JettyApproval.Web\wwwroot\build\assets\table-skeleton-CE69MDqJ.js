import{j as e}from"./vendor-6tJeyfYI.js";import{S as s}from"./skeleton-DAOxGMKm.js";const d=({columns:m,rowCount:c=10})=>{const x=Array.from({length:c},(l,a)=>a);return e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"border-b bg-muted/50",children:e.jsx("div",{className:"flex items-center h-12 px-4",children:m.map((l,a)=>e.jsx("div",{className:"flex-1 px-2",children:e.jsx(s,{className:"h-4 w-20"})},a))})}),e.jsx("div",{className:"divide-y",children:x.map(l=>e.jsx("div",{className:"flex items-center h-14 px-4 hover:bg-muted/50",children:m.map((a,r)=>e.jsx("div",{className:"flex-1 px-2",children:e.jsx(s,{className:"h-4 w-full max-w-32"})},r))},l))}),e.jsxs("div",{className:"flex items-center justify-between px-4 py-3 border-t bg-muted/30",children:[e.jsx(s,{className:"h-4 w-32"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(s,{className:"h-8 w-8"}),e.jsx(s,{className:"h-8 w-8"}),e.jsx(s,{className:"h-8 w-8"}),e.jsx(s,{className:"h-8 w-8"})]})]})]})};export{d as T};
//# sourceMappingURL=table-skeleton-CE69MDqJ.js.map
