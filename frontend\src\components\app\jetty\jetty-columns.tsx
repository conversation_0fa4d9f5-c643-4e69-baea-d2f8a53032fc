import type { JettyDto } from '@/client/types.gen';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type ColumnDef } from '@tanstack/react-table';
import { Pencil } from 'lucide-react';

export const jettyColumns = (onEdit: (row: JettyDto) => void): ColumnDef<JettyDto>[] => [
  {
    accessorKey: 'name',
    header: 'Jetty Name',
    cell: info => info.getValue() ?? '-',
    enableSorting: true,
  },
  {
    accessorKey: 'alias',
    header: 'Alias',
    cell: info => info.getValue() ?? '-',
    enableSorting: true,
  },
  {
    accessorKey: 'port',
    header: 'Port',
    cell: info => info.getValue() ?? '-',
  },
  {
    accessorKey: 'max',
    header: 'Max',
    cell: info => info.getValue() ?? '-',
  },
  {
    accessorKey: 'isCustomArea',
    header: 'Is Custom Area',
    cell: info => {
      const value = info.getValue();
      return (
        <Badge variant={value ? 'primary' : 'warning'}>
          {value ? 'True' : 'False'}
        </Badge>
      );
    },
  },
  // {
  //   accessorKey: 'createdAt',
  //   header: 'Created At',
  //   cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : '-',
  // },
  // {
  //   accessorKey: 'updatedAt',
  //   header: 'Updated At',
  //   cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : '-',
  // },
  {
    id: 'edit',
    header: '',
    cell: ({ row }) => {
      const handleEdit = () => onEdit(row.original);
      const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleEdit();
        }
      };
      return (
        <Button
          onClick={handleEdit}
          onKeyDown={handleKeyDown}
          aria-label="Edit Jetty"
          tabIndex={0}
          variant="outline"
          size="icon"
          className="ml-2 h-8 w-8"
        >
          <Pencil className="w-4 h-4" aria-hidden="true" />
        </Button>
      );
    },
    enableSorting: false,
    enableColumnFilter: false,
  },
]; 