import type { ExternalVesselHeaderDto } from '@/client/types.gen';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useVesselData } from '@/lib/hooks/useVesselData';
import { IconChevronDown } from '@tabler/icons-react';
import { Loader2 } from 'lucide-react';
import React, { useState } from 'react';

interface VesselSelectorProps {
  vesselType?: string;
  selectedVessel: string;
  onVesselSelect: (vessel: string) => void;
  trigger?: React.ReactNode;
}

const VesselSelector: React.FC<VesselSelectorProps> = ({
  vesselType,
  selectedVessel,
  onVesselSelect,
  trigger
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedVesselRows, setSelectedVesselRows] = useState<Set<string>>(new Set());
  const [vesselFilter, setVesselFilter] = useState('');

  // Fetch vessel data from API
  const vesselMutation = useVesselData();
  const vessels = vesselMutation.data?.items || [];
  const isLoading = vesselMutation.isPending;
  const error = vesselMutation.error;

  // Trigger data fetch when dialog opens or filters change
  React.useEffect(() => {
    if (isDialogOpen) {
      vesselMutation.mutate({
        vesselType,
        filterGroup: vesselFilter ? {
          operator: 'And',
          conditions: [{
            fieldName: 'vesselName',
            operator: 'Contains',
            value: vesselFilter
          }]
        } : undefined
      });
    }
  }, [isDialogOpen, vesselType, vesselFilter]);

  const [visibleColumns, setVisibleColumns] = useState<Set<keyof ExternalVesselHeaderDto>>(
    new Set(['docNum', 'vesselName', 'voyage', 'arrival', 'departure'] as (keyof ExternalVesselHeaderDto)[])
  );

  const handleSelectAllVessels = (checked: boolean) => {
    if (checked) {
      setSelectedVesselRows(new Set(vessels.map((v: ExternalVesselHeaderDto) => v.docEntry?.toString() || v.id || '')));
    } else {
      setSelectedVesselRows(new Set());
    }
  };

  const handleSelectVesselRow = (docNum: string, checked: boolean) => {
    const newSelection = new Set(selectedVesselRows);
    if (checked) {
      newSelection.add(docNum);
    } else {
      newSelection.delete(docNum);
    }
    setSelectedVesselRows(newSelection);
  };

  const handleVesselSelectConfirm = () => {
    if (selectedVesselRows.size > 0) {
      const selectedDocNum = Array.from(selectedVesselRows)[0];
      const vessel = vessels.find((v: ExternalVesselHeaderDto) => (v.docEntry?.toString() || v.id) === selectedDocNum);
      if (vessel) {
        onVesselSelect(vessel.vesselName + ' ' + vessel.voyage);
      }
    } else {
      onVesselSelect('');
    }
    setIsDialogOpen(false);
  };

  const handleToggleColumn = (columnKey: keyof ExternalVesselHeaderDto, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const defaultTrigger = (
    <Button variant="outline" className="w-full justify-start font-normal">
      {selectedVessel ? selectedVessel : "Select Vessel"}
    </Button>
  );

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="min-w-[900px] w-auto max-h-[70vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Vessel {vesselType && `(${vesselType})`}</DialogTitle>
        </DialogHeader>
        <div className="flex items-center justify-between mb-4">
          <Input
            placeholder="Filter vessels..."
            value={vesselFilter}
            onChange={(e) => setVesselFilter(e.target.value)}
            className="max-w-sm"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="ml-auto">
                Columns <IconChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {Object.keys(vessels[0] || {}).map((key) => (
                <DropdownMenuCheckboxItem
                  key={key}
                  className="capitalize"
                  checked={visibleColumns.has(key as keyof ExternalVesselHeaderDto)}
                  onCheckedChange={(checked) => handleToggleColumn(key as keyof ExternalVesselHeaderDto, checked === true)}
                >
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex-grow overflow-auto border rounded-md">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading vessels...
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-8 text-destructive">
              Error loading vessels
            </div>
          ) : vessels.length === 0 ? (
            <div className="flex items-center justify-center py-8 text-muted-foreground">
              {vesselType ? `No vessels found for ${vesselType}` : 'Please select a vessel type first'}
            </div>
          ) : (
            <Table>
              <TableHeader className="sticky top-0 bg-white">
                <TableRow>
                  <TableHead className="w-[30px]">
                    <Checkbox
                      checked={selectedVesselRows.size === vessels.length && vessels.length > 0}
                      onCheckedChange={(checked) => handleSelectAllVessels(checked === true)}
                    />
                  </TableHead>
                  {Object.keys(vessels[0] || {}).map((key) => (visibleColumns.has(key as keyof ExternalVesselHeaderDto) &&
                    <TableHead key={key} className="capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {vessels.map((vesselItem: ExternalVesselHeaderDto) => (
                  <TableRow key={vesselItem.docEntry?.toString() || vesselItem.id} className="cursor-pointer hover:bg-gray-100">
                    <TableCell>
                      <Checkbox
                        checked={selectedVesselRows.has(vesselItem.docEntry?.toString() || vesselItem.id || '')}
                        onCheckedChange={(checked) => handleSelectVesselRow(vesselItem.docEntry?.toString() || vesselItem.id || '', checked === true)}
                      />
                    </TableCell>
                    {Object.entries(vesselItem).map(([key, value]) => (visibleColumns.has(key as keyof ExternalVesselHeaderDto) &&
                      <TableCell key={key}>{typeof value === 'object' ? JSON.stringify(value) : String(value || '')}</TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-500">
            {selectedVesselRows.size} of {vessels.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button variant="outline" size="sm">Previous</Button>
            <Button variant="outline" size="sm">Next</Button>
          </div>
          <Button onClick={handleVesselSelectConfirm}>Select Vessel</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default VesselSelector; 