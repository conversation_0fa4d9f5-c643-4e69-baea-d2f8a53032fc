{"version": 3, "file": "approval-table-CZphW0_v.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js", "../../../../../frontend/src/components/approval/approval-actions.tsx", "../../../../../frontend/src/components/approval/approval-table.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"m9 12 2 2 4-4\", key: \"dzmm74\" }]\n];\nconst CircleCheck = createLucideIcon(\"circle-check\", __iconNode);\n\nexport { __iconNode, CircleCheck as default };\n//# sourceMappingURL=circle-check.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"m15 9-6 6\", key: \"1uzhvr\" }],\n  [\"path\", { d: \"m9 9 6 6\", key: \"z0biqf\" }]\n];\nconst CircleX = createLucideIcon(\"circle-x\", __iconNode);\n\nexport { __iconNode, CircleX as default };\n//# sourceMappingURL=circle-x.js.map\n", "import { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { useToast } from \"@/lib/useToast\";\nimport { ekbProxyService } from \"@/services/ekbProxyService\";\nimport type { QueryClient } from \"@tanstack/react-query\";\nimport React, { useState } from \"react\";\n\ninterface ApprovalActionsProps {\n  approvalId: string;\n  isOpen: boolean;\n  onClose: () => void;\n  action: \"approve\" | \"reject\";\n  vessel?: { id?: string; vesselType?: string | null };\n  onSuccess?: () => void;\n  queryClient: QueryClient;\n}\n\nconst ApprovalActions: React.FC<ApprovalActionsProps> = ({\n  approvalId,\n  isOpen,\n  onClose,\n  action,\n  vessel,\n  onSuccess,\n  queryClient,\n}) => {\n  const { toast } = useToast();\n  const [notes, setNotes] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSubmit = async () => {\n    setIsLoading(true);\n    try {\n      const endpoint = action === \"approve\"\n        ? `/api/idjas/approval/approve/${approvalId}`\n        : `/api/idjas/approval/reject/${approvalId}`;\n\n      const response = await fetch(endpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ notes }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || `Failed to ${action} approval`);\n      }\n\n      // Update vessel status if vessel information is available\n      if (vessel?.id && vessel?.vesselType) {\n        try {\n          const newDocStatus = action === \"approve\" ? \"Approved\" : \"Rejected\";\n          console.log(`Updating vessel ${vessel.id} (${vessel.vesselType}) status to: ${newDocStatus}`);\n\n          // Update vessel based on vessel type using PUT methods\n          switch (vessel.vesselType) {\n            case 'Import': {\n              // For import vessels, get existing data first to include all required fields\n              const importVesselResponse = await ekbProxyService.getImportVessel(vessel.id);\n              if (importVesselResponse.data) {\n                const existingVessel = importVesselResponse.data;\n                const updateData = {\n                  createdBy: existingVessel.createdBy || '',\n                  isLocked: existingVessel.isLocked || 'N',\n                  isChange: existingVessel.isChange || 'N',\n                  transType: existingVessel.transType || '',\n                  docType: existingVessel.docType || '',\n                  deleted: existingVessel.deleted || 'N',\n                  docStatus: newDocStatus,\n                  statusBms: existingVessel.statusBms || '',\n                  concurrencyStamp: existingVessel.concurrencyStamp || '',\n                  // Include other existing fields\n                  docNum: existingVessel.docNum,\n                  bp: existingVessel.bp,\n                  vesselName: existingVessel.vesselName,\n                  shipment: existingVessel.shipment,\n                  shipmentNo: existingVessel.shipmentNo,\n                  vesselArrival: existingVessel.vesselArrival,\n                  updatedBy: existingVessel.updatedBy,\n                  createdAt: existingVessel.createdAt,\n                  updatedAt: existingVessel.updatedAt,\n                  postingDate: existingVessel.postingDate,\n                  color: existingVessel.color,\n                  flags: existingVessel.flags,\n                  remarks: existingVessel.remarks,\n                  status: existingVessel.status,\n                  bcType: existingVessel.bcType,\n                  portOrigin: existingVessel.portOrigin,\n                  emailToPpjk: existingVessel.emailToPpjk,\n                  matchKey: existingVessel.matchKey,\n                  voyage: existingVessel.voyage,\n                  grossWeight: existingVessel.grossWeight,\n                  vesselFlag: existingVessel.vesselFlag,\n                  vesselDeparture: existingVessel.vesselDeparture,\n                  vesselStatus: existingVessel.vesselStatus,\n                  jetty: existingVessel.jetty,\n                  destinationPort: existingVessel.destinationPort,\n                  berthingDate: existingVessel.berthingDate,\n                  anchorageDate: existingVessel.anchorageDate,\n                  type: existingVessel.type,\n                  jettyUpdate: existingVessel.jettyUpdate,\n                  reportDate: existingVessel.reportDate,\n                  unloadingDate: existingVessel.unloadingDate,\n                  finishUnloadingDate: existingVessel.finishUnloadingDate,\n                  grtWeight: existingVessel.grtWeight,\n                  invoiceStatus: existingVessel.invoiceStatus,\n                  agentId: existingVessel.agentId,\n                  agentName: existingVessel.agentName,\n                  surveyorId: existingVessel.surveyorId,\n                  tradingId: existingVessel.tradingId,\n                  jettyId: existingVessel.jettyId,\n                  vesselId: existingVessel.vesselId,\n                  masterAgentId: existingVessel.masterAgentId,\n                  masterTradingId: existingVessel.masterTradingId,\n                  masterSurveyorId: existingVessel.masterSurveyorId,\n                };\n                await ekbProxyService.updateImportVessel(vessel.id, updateData);\n              } else {\n                throw new Error('Failed to fetch existing import vessel data');\n              }\n              break;\n            }\n            case 'Export': {\n              // For export vessels, get existing data first to include all required fields\n              const exportVesselResponse = await ekbProxyService.getExportVessel(vessel.id);\n              if (exportVesselResponse.data) {\n                const existingVessel = exportVesselResponse.data;\n                const updateData = {\n                  docNum: existingVessel.docNum || '',\n                  postingDate: existingVessel.postingDate,\n                  vesselName: existingVessel.vesselName,\n                  vesselArrival: existingVessel.vesselArrival,\n                  voyage: existingVessel.voyage || '',\n                  shipment: existingVessel.shipment || '',\n                  vesselQty: existingVessel.vesselQty,\n                  portOrigin: existingVessel.portOrigin || '',\n                  deleted: existingVessel.deleted || 'N',\n                  updatedBy: existingVessel.updatedBy,\n                  docStatus: newDocStatus,\n                  statusBms: existingVessel.statusBms || '',\n                  docType: existingVessel.docType || '',\n                  concurrencyStamp: existingVessel.concurrencyStamp || '',\n                  // Include other existing fields\n                  vesselDeparture: existingVessel.vesselDeparture,\n                  destinationPort: existingVessel.destinationPort,\n                  remarks: existingVessel.remarks,\n                  grossWeight: existingVessel.grossWeight,\n                  vesselFlag: existingVessel.vesselFlag,\n                  vesselStatus: existingVessel.vesselStatus,\n                  jetty: existingVessel.jetty,\n                  berthingDate: existingVessel.berthingDate,\n                  anchorageDate: existingVessel.anchorageDate,\n                  reportDate: existingVessel.reportDate,\n                  unloadingDate: existingVessel.unloadingDate,\n                  finishUnloadingDate: existingVessel.finishUnloadingDate,\n                  deadWeight: existingVessel.deadWeight,\n                  grtWeight: existingVessel.grtWeight,\n                  invoiceStatus: existingVessel.invoiceStatus,\n                  agentId: existingVessel.agentId,\n                  agentName: existingVessel.agentName,\n                  surveyorId: existingVessel.surveyorId,\n                  tradingId: existingVessel.tradingId,\n                  jettyId: existingVessel.jettyId,\n                  vesselId: existingVessel.vesselId,\n                  masterAgentId: existingVessel.masterAgentId,\n                  masterTradingId: existingVessel.masterTradingId,\n                  masterSurveyorId: existingVessel.masterSurveyorId,\n                };\n                await ekbProxyService.updateExportVessel(vessel.id, updateData);\n              } else {\n                throw new Error('Failed to fetch existing export vessel data');\n              }\n              break;\n            }\n            case 'LocalIn':\n            case 'LocalOut': {\n              // For local vessels, we need to get the existing data first to include all required fields\n              const localVesselResponse = await ekbProxyService.getLocalVessel(vessel.id);\n              if (localVesselResponse.data) {\n                const existingVessel = localVesselResponse.data;\n                // Update with all required fields from existing vessel plus the new status\n                const updateData = {\n                  docNum: existingVessel.docNum || '',\n                  deleted: existingVessel.deleted || 'N',\n                  shipment: existingVessel.shipment || '',\n                  statusBms: existingVessel.statusBms || '',\n                  transType: existingVessel.transType || '',\n                  portOrigin: existingVessel.portOrigin || '',\n                  vesselType: existingVessel.vesselType || 'Local',\n                  destinationPort: existingVessel.destinationPort || '',\n                  concurrencyStamp: existingVessel.concurrencyStamp || '',\n                  docType: 'Local',\n                  docStatus: newDocStatus,\n                  // Include other existing fields to maintain data integrity\n                  postingDate: existingVessel.postingDate,\n                  vesselName: existingVessel.vesselName,\n                  tongkang: existingVessel.tongkang,\n                  vesselArrival: existingVessel.vesselArrival,\n                  vesselDeparture: existingVessel.vesselDeparture,\n                  vesselQty: existingVessel.vesselQty,\n                  remark: existingVessel.remark,\n                  updatedBy: existingVessel.updatedBy,\n                  voyage: existingVessel.voyage,\n                  grossWeight: existingVessel.grossWeight,\n                  jetty: existingVessel.jetty,\n                  status: existingVessel.status,\n                  beratTugboat: existingVessel.beratTugboat,\n                  berthingDate: existingVessel.berthingDate,\n                  anchorageDate: existingVessel.anchorageDate,\n                  reportDate: existingVessel.reportDate,\n                  unloadingDate: existingVessel.unloadingDate,\n                  finishUnloadingDate: existingVessel.finishUnloadingDate,\n                  grtWeight: existingVessel.grtWeight,\n                  invoiceStatus: existingVessel.invoiceStatus,\n                  agentId: existingVessel.agentId,\n                  agentName: existingVessel.agentName,\n                  grtVessel: existingVessel.grtVessel,\n                  surveyorId: existingVessel.surveyorId,\n                  tradingId: existingVessel.tradingId,\n                  jettyId: existingVessel.jettyId,\n                  vesselId: existingVessel.vesselId,\n                  bargeId: existingVessel.bargeId,\n                  masterAgentId: existingVessel.masterAgentId,\n                  masterTradingId: existingVessel.masterTradingId,\n                  masterSurveyorId: existingVessel.masterSurveyorId,\n                };\n                await ekbProxyService.updateLocalVessel(vessel.id, updateData);\n              } else {\n                throw new Error('Failed to fetch existing local vessel data');\n              }\n              break;\n            }\n            default:\n              console.warn(`Unknown vessel type: ${vessel.vesselType}`);\n          }\n\n          console.log(`Successfully updated vessel status to: ${newDocStatus}`);\n        } catch (vesselError) {\n          console.error('Error updating vessel status:', vesselError);\n          // Don't fail the entire operation if vessel update fails\n          toast({\n            title: \"Warning\",\n            description: \"Approval processed but vessel status update failed\",\n            variant: \"destructive\",\n          });\n        }\n      } else {\n        console.log('No vessel information available for status update');\n      }\n\n      toast({\n        title: \"Success\",\n        description: `Approval ${action}d successfully`,\n        variant: \"default\",\n      });\n\n      // Invalidate the approval-stages query to refetch the table\n      await queryClient.invalidateQueries({ queryKey: [\"approval-stages\"] });\n\n      onSuccess?.();\n      onClose();\n      setNotes(\"\");\n    } catch (error) {\n      console.error(`Error ${action}ing approval:`, error);\n      toast({\n        title: \"Error\",\n        description: error instanceof Error ? error.message : `Failed to ${action} approval`,\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLoading) {\n      onClose();\n      setNotes(\"\");\n    }\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={handleClose}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>\n            {action === \"approve\" ? \"Approve\" : \"Reject\"} Request\n          </DialogTitle>\n          <DialogDescription>\n            {action === \"approve\"\n              ? \"Are you sure you want to approve this request? You can add optional notes below.\"\n              : \"Please provide a reason for rejecting this request.\"\n            }\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"grid gap-4 py-4\">\n          <div className=\"grid gap-2\">\n            <label htmlFor=\"notes\" className=\"text-sm font-medium\">\n              {action === \"approve\" ? \"Notes (Optional)\" : \"Rejection Reason\"}\n            </label>\n            <Textarea\n              id=\"notes\"\n              placeholder={\n                action === \"approve\"\n                  ? \"Add any additional notes...\"\n                  : \"Please explain why this request is being rejected...\"\n              }\n              value={notes}\n              onChange={(e) => setNotes(e.target.value)}\n              rows={4}\n              disabled={isLoading}\n            />\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={handleClose}\n            disabled={isLoading}\n          >\n            Cancel\n          </Button>\n          <Button\n            type=\"button\"\n            variant={action === \"approve\" ? \"default\" : \"destructive\"}\n            onClick={handleSubmit}\n            disabled={isLoading || (action === \"reject\" && !notes.trim())}\n          >\n            {isLoading ? \"Processing...\" : action === \"approve\" ? \"Approve\" : \"Reject\"}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\nexport default ApprovalActions;\n", "import { postApiApprovalStagesFilterList } from \"@/client/sdk.gen\";\r\nimport type { ApprovalStageDto } from \"@/client/types.gen\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { DataGrid } from '@/components/ui/data-grid';\r\nimport { Tooltip, TooltipContent, TooltipTrigger } from \"@/components/ui/tooltip\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { useToast } from \"@/lib/useToast\";\r\nimport { router } from \"@inertiajs/react\";\r\nimport {\r\n  type ColumnDef\r\n} from \"@tanstack/react-table\";\r\nimport { ArrowUpRight, CheckCircle2, XCircle } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\nimport ApprovalActions from \"./approval-actions\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { useTranslation } from \"react-i18next\";\r\n\r\ntype ApprovalTableMeta = {\r\n  setApprovalAction: (action: { isOpen: boolean; approvalId: string; action: 'approve' | 'reject'; vessel?: { id?: string; vesselType?: string | null } }) => void;\r\n};\r\n\r\nconst ApprovalTable: React.FC = () => {\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n  const { t } = useTranslation();\r\n\r\n  // State for approval action modal\r\n  const [approvalAction, setApprovalAction] = useState<{\r\n    isOpen: boolean;\r\n    approvalId: string;\r\n    action: 'approve' | 'reject';\r\n    vessel?: { id?: string; vesselType?: string | null };\r\n  }>({\r\n    isOpen: false,\r\n    approvalId: \"\",\r\n    action: \"approve\",\r\n  });\r\n\r\n  const handleApprovalAction = (action: { isOpen: boolean; approvalId: string; action: 'approve' | 'reject'; vessel?: { id?: string; vesselType?: string | null } }) => {\r\n    setApprovalAction(action);\r\n  };\r\n\r\n  const handleApprovalClose = () => {\r\n    setApprovalAction(prev => ({ ...prev, isOpen: false }));\r\n  };\r\n\r\n  const handleApprovalSuccess = () => {\r\n    // The ApprovalActions component will handle invalidating the query\r\n    handleApprovalClose();\r\n  };\r\n\r\n  // Define columns with translations\r\n  const approvalColumns: ColumnDef<ApprovalStageDto>[] = [\r\n    {\r\n      accessorKey: \"vessel.vesselName\",\r\n      header: t('table.vesselName'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.voyage\",\r\n      header: t('table.voyage'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.vesselType\",\r\n      header: t('table.vesselType'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.vesselArrival\",\r\n      header: t('table.arrival'),\r\n      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"vessel.destinationPort\",\r\n      header: t('table.destinationPort'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"requestDate\",\r\n      header: t('table.requestDate'),\r\n      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : \"-\",\r\n    },\r\n    {\r\n      accessorKey: \"status\",\r\n      header: t('table.status'),\r\n      cell: info => {\r\n        const status = info.getValue() as string | number;\r\n        let statusText: string;\r\n        let variant: \"warning\" | \"success\" | \"destructive\" | \"secondary\" | \"primary\";\r\n\r\n        switch (status) {\r\n          case 0:\r\n            statusText = t('table.statusPending');\r\n            variant = \"warning\";\r\n            break;\r\n          case 1:\r\n            statusText = t('table.statusApproved');\r\n            variant = \"success\";\r\n            break;\r\n          case 2:\r\n            statusText = t('table.statusRejected');\r\n            variant = \"destructive\";\r\n            break;\r\n          case 3:\r\n            statusText = t('table.statusCancelled');\r\n            variant = \"secondary\";\r\n            break;\r\n          default:\r\n            statusText = t('table.statusUnknown');\r\n            variant = \"primary\";\r\n            break;\r\n        }\r\n\r\n        return <Badge variant={variant} size=\"sm\">{statusText}</Badge>;\r\n      },\r\n    },\r\n    {\r\n      accessorKey: \"requesterUserName\",\r\n      header: t('table.requester'),\r\n      cell: info => info.getValue() ?? \"-\",\r\n    },\r\n    {\r\n      id: \"actions\",\r\n      header: t('table.actions'),\r\n      cell: ({ row, table }) => {\r\n        const approval = row.original;\r\n        const isPending = approval.status === 0;\r\n        const vessel = approval.vessel;\r\n        const meta = table.options.meta as ApprovalTableMeta;\r\n        return (\r\n          <div className=\"flex space-x-1\">\r\n            {isPending && (\r\n              <>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      className=\"text-green-600 hover:text-green-700\"\r\n                      onClick={() => {\r\n                        meta.setApprovalAction({\r\n                          isOpen: true,\r\n                          approvalId: approval.id || \"\",\r\n                          action: \"approve\",\r\n                          vessel: approval.vessel,\r\n                        });\r\n                      }}\r\n                      aria-label=\"Approve\"\r\n                    >\r\n                      <CheckCircle2 className=\"w-5 h-5\" />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>{t('table.approveRequest')}</TooltipContent>\r\n                </Tooltip>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"icon\"\r\n                      className=\"text-red-600 hover:text-red-700\"\r\n                      onClick={() => {\r\n                        meta.setApprovalAction({\r\n                          isOpen: true,\r\n                          approvalId: approval.id || \"\",\r\n                          action: \"reject\",\r\n                          vessel: approval.vessel,\r\n                        });\r\n                      }}\r\n                      aria-label=\"Reject\"\r\n                    >\r\n                      <XCircle className=\"w-5 h-5\" />\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>{t('table.rejectRequest')}</TooltipContent>\r\n                </Tooltip>\r\n              </>\r\n            )}\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  onClick={() => {\r\n                    if (vessel && vessel.id) {\r\n                      if (vessel.vesselType === 'Import') {\r\n                        router.visit(`/import/edit/${vessel.id}`);\r\n                      } else if (vessel.vesselType === 'Export') {\r\n                        router.visit(`/export/edit/${vessel.id}`);\r\n                      } else {\r\n                        router.visit(`/local/edit/${vessel.id}`);\r\n                      }\r\n                    }\r\n                  }}\r\n                  aria-label=\"Details\"\r\n                >\r\n                  <ArrowUpRight className=\"w-5 h-5\" />\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>{t('table.viewDetails')}</TooltipContent>\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <DataGrid\r\n        columns={approvalColumns}\r\n        title={t('datagrid.pendingApprovals')}\r\n        queryKey={[\"approval-stages\"]}\r\n        manualSorting={true}\r\n        manualFiltering={true}\r\n        autoSizeColumns={true}\r\n        meta={{\r\n          setApprovalAction: handleApprovalAction,\r\n        }}\r\n        queryFn={async ({ pageIndex, pageSize }) => {\r\n          try {\r\n            const response = await postApiApprovalStagesFilterList({\r\n              body: {\r\n                maxResultCount: pageSize,\r\n                skipCount: pageIndex * pageSize,\r\n                filterGroup: {\r\n                  operator: \"And\",\r\n                  conditions: [\r\n                    {\r\n                      fieldName: \"status\",\r\n                      operator: \"Equals\",\r\n                      value: \"0\"\r\n                    }\r\n                  ]\r\n                }\r\n              }\r\n            });\r\n            if (!response || !response.data) {\r\n              throw new Error(\"Failed to fetch approvals\");\r\n            }\r\n            return {\r\n              items: response.data.items || [],\r\n              totalCount: response.data.totalCount || 0,\r\n            };\r\n          } catch (err) {\r\n            console.error(\"Error fetching approvals:\", err);\r\n            toast({\r\n              title: \"Error\",\r\n              description: \"Failed to load approval list\",\r\n              variant: \"destructive\",\r\n            });\r\n            return { items: [], totalCount: 0 };\r\n          }\r\n        }}\r\n      />\r\n\r\n      {/* Approval Actions Modal */}\r\n      <ApprovalActions\r\n        approvalId={approvalAction.approvalId}\r\n        isOpen={approvalAction.isOpen}\r\n        onClose={handleApprovalClose}\r\n        action={approvalAction.action}\r\n        vessel={approvalAction.vessel}\r\n        onSuccess={handleApprovalSuccess}\r\n        queryClient={queryClient}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApprovalTable;\r\n"], "names": ["__iconNode", "CircleCheck", "createLucideIcon", "CircleX", "ApprovalActions", "approvalId", "isOpen", "onClose", "action", "vessel", "onSuccess", "queryClient", "toast", "useToast", "notes", "setNotes", "useState", "isLoading", "setIsLoading", "handleSubmit", "endpoint", "response", "errorData", "newDocStatus", "importVesselResponse", "ekbProxyService", "existingVessel", "updateData", "exportVesselResponse", "localVesselResponse", "error", "handleClose", "jsx", "Dialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "Textarea", "e", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "ApprovalTable", "useQueryClient", "t", "useTranslation", "approvalAction", "setApprovalAction", "handleApprovalAction", "handleApprovalClose", "prev", "handleApprovalSuccess", "approvalColumns", "info", "status", "statusText", "variant", "Badge", "row", "table", "approval", "isPending", "meta", "Fragment", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "CheckCircle2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "XCircle", "router", "ArrowUpRight", "DataGrid", "pageIndex", "pageSize", "postApiApprovalStagesFilterList"], "mappings": "8bAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,OAAQ,CAAE,EAAG,gBAAiB,IAAK,QAAU,CAAA,CAChD,EACMC,EAAcC,EAAiB,eAAgBF,CAAU,ECb/D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,OAAQ,CAAE,EAAG,YAAa,IAAK,QAAQ,CAAE,EAC1C,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMG,EAAUD,EAAiB,WAAYF,CAAU,ECWjDI,EAAkD,CAAC,CACvD,WAAAC,EACA,OAAAC,EACA,QAAAC,EACA,OAAAC,EACA,OAAAC,EACA,UAAAC,EACA,YAAAC,CACF,IAAM,CACE,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAAS,EAAE,EAC/B,CAACC,EAAWC,CAAY,EAAIF,EAAAA,SAAS,EAAK,EAE1CG,EAAe,SAAY,CAC/BD,EAAa,EAAI,EACb,GAAA,CACF,MAAME,EAAWZ,IAAW,UACxB,+BAA+BH,CAAU,GACzC,8BAA8BA,CAAU,GAEtCgB,EAAW,MAAM,MAAMD,EAAU,CACrC,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CAAE,MAAAN,CAAO,CAAA,CAAA,CAC/B,EAEG,GAAA,CAACO,EAAS,GAAI,CACV,MAAAC,EAAY,MAAMD,EAAS,KAAK,EACtC,MAAM,IAAI,MAAMC,EAAU,OAAS,aAAad,CAAM,WAAW,CAAA,CAI/D,GAAAC,GAAQ,IAAMA,GAAQ,WACpB,GAAA,CACI,MAAAc,EAAef,IAAW,UAAY,WAAa,WAIzD,OAAQC,EAAO,WAAY,CACzB,IAAK,SAAU,CAEb,MAAMe,EAAuB,MAAMC,EAAgB,gBAAgBhB,EAAO,EAAE,EAC5E,GAAIe,EAAqB,KAAM,CAC7B,MAAME,EAAiBF,EAAqB,KACtCG,EAAa,CACjB,UAAWD,EAAe,WAAa,GACvC,SAAUA,EAAe,UAAY,IACrC,SAAUA,EAAe,UAAY,IACrC,UAAWA,EAAe,WAAa,GACvC,QAASA,EAAe,SAAW,GACnC,QAASA,EAAe,SAAW,IACnC,UAAWH,EACX,UAAWG,EAAe,WAAa,GACvC,iBAAkBA,EAAe,kBAAoB,GAErD,OAAQA,EAAe,OACvB,GAAIA,EAAe,GACnB,WAAYA,EAAe,WAC3B,SAAUA,EAAe,SACzB,WAAYA,EAAe,WAC3B,cAAeA,EAAe,cAC9B,UAAWA,EAAe,UAC1B,UAAWA,EAAe,UAC1B,UAAWA,EAAe,UAC1B,YAAaA,EAAe,YAC5B,MAAOA,EAAe,MACtB,MAAOA,EAAe,MACtB,QAASA,EAAe,QACxB,OAAQA,EAAe,OACvB,OAAQA,EAAe,OACvB,WAAYA,EAAe,WAC3B,YAAaA,EAAe,YAC5B,SAAUA,EAAe,SACzB,OAAQA,EAAe,OACvB,YAAaA,EAAe,YAC5B,WAAYA,EAAe,WAC3B,gBAAiBA,EAAe,gBAChC,aAAcA,EAAe,aAC7B,MAAOA,EAAe,MACtB,gBAAiBA,EAAe,gBAChC,aAAcA,EAAe,aAC7B,cAAeA,EAAe,cAC9B,KAAMA,EAAe,KACrB,YAAaA,EAAe,YAC5B,WAAYA,EAAe,WAC3B,cAAeA,EAAe,cAC9B,oBAAqBA,EAAe,oBACpC,UAAWA,EAAe,UAC1B,cAAeA,EAAe,cAC9B,QAASA,EAAe,QACxB,UAAWA,EAAe,UAC1B,WAAYA,EAAe,WAC3B,UAAWA,EAAe,UAC1B,QAASA,EAAe,QACxB,SAAUA,EAAe,SACzB,cAAeA,EAAe,cAC9B,gBAAiBA,EAAe,gBAChC,iBAAkBA,EAAe,gBACnC,EACA,MAAMD,EAAgB,mBAAmBhB,EAAO,GAAIkB,CAAU,CAAA,KAExD,OAAA,IAAI,MAAM,6CAA6C,EAE/D,KAAA,CAEF,IAAK,SAAU,CAEb,MAAMC,EAAuB,MAAMH,EAAgB,gBAAgBhB,EAAO,EAAE,EAC5E,GAAImB,EAAqB,KAAM,CAC7B,MAAMF,EAAiBE,EAAqB,KACtCD,EAAa,CACjB,OAAQD,EAAe,QAAU,GACjC,YAAaA,EAAe,YAC5B,WAAYA,EAAe,WAC3B,cAAeA,EAAe,cAC9B,OAAQA,EAAe,QAAU,GACjC,SAAUA,EAAe,UAAY,GACrC,UAAWA,EAAe,UAC1B,WAAYA,EAAe,YAAc,GACzC,QAASA,EAAe,SAAW,IACnC,UAAWA,EAAe,UAC1B,UAAWH,EACX,UAAWG,EAAe,WAAa,GACvC,QAASA,EAAe,SAAW,GACnC,iBAAkBA,EAAe,kBAAoB,GAErD,gBAAiBA,EAAe,gBAChC,gBAAiBA,EAAe,gBAChC,QAASA,EAAe,QACxB,YAAaA,EAAe,YAC5B,WAAYA,EAAe,WAC3B,aAAcA,EAAe,aAC7B,MAAOA,EAAe,MACtB,aAAcA,EAAe,aAC7B,cAAeA,EAAe,cAC9B,WAAYA,EAAe,WAC3B,cAAeA,EAAe,cAC9B,oBAAqBA,EAAe,oBACpC,WAAYA,EAAe,WAC3B,UAAWA,EAAe,UAC1B,cAAeA,EAAe,cAC9B,QAASA,EAAe,QACxB,UAAWA,EAAe,UAC1B,WAAYA,EAAe,WAC3B,UAAWA,EAAe,UAC1B,QAASA,EAAe,QACxB,SAAUA,EAAe,SACzB,cAAeA,EAAe,cAC9B,gBAAiBA,EAAe,gBAChC,iBAAkBA,EAAe,gBACnC,EACA,MAAMD,EAAgB,mBAAmBhB,EAAO,GAAIkB,CAAU,CAAA,KAExD,OAAA,IAAI,MAAM,6CAA6C,EAE/D,KAAA,CAEF,IAAK,UACL,IAAK,WAAY,CAEf,MAAME,EAAsB,MAAMJ,EAAgB,eAAehB,EAAO,EAAE,EAC1E,GAAIoB,EAAoB,KAAM,CAC5B,MAAMH,EAAiBG,EAAoB,KAErCF,EAAa,CACjB,OAAQD,EAAe,QAAU,GACjC,QAASA,EAAe,SAAW,IACnC,SAAUA,EAAe,UAAY,GACrC,UAAWA,EAAe,WAAa,GACvC,UAAWA,EAAe,WAAa,GACvC,WAAYA,EAAe,YAAc,GACzC,WAAYA,EAAe,YAAc,QACzC,gBAAiBA,EAAe,iBAAmB,GACnD,iBAAkBA,EAAe,kBAAoB,GACrD,QAAS,QACT,UAAWH,EAEX,YAAaG,EAAe,YAC5B,WAAYA,EAAe,WAC3B,SAAUA,EAAe,SACzB,cAAeA,EAAe,cAC9B,gBAAiBA,EAAe,gBAChC,UAAWA,EAAe,UAC1B,OAAQA,EAAe,OACvB,UAAWA,EAAe,UAC1B,OAAQA,EAAe,OACvB,YAAaA,EAAe,YAC5B,MAAOA,EAAe,MACtB,OAAQA,EAAe,OACvB,aAAcA,EAAe,aAC7B,aAAcA,EAAe,aAC7B,cAAeA,EAAe,cAC9B,WAAYA,EAAe,WAC3B,cAAeA,EAAe,cAC9B,oBAAqBA,EAAe,oBACpC,UAAWA,EAAe,UAC1B,cAAeA,EAAe,cAC9B,QAASA,EAAe,QACxB,UAAWA,EAAe,UAC1B,UAAWA,EAAe,UAC1B,WAAYA,EAAe,WAC3B,UAAWA,EAAe,UAC1B,QAASA,EAAe,QACxB,SAAUA,EAAe,SACzB,QAASA,EAAe,QACxB,cAAeA,EAAe,cAC9B,gBAAiBA,EAAe,gBAChC,iBAAkBA,EAAe,gBACnC,EACA,MAAMD,EAAgB,kBAAkBhB,EAAO,GAAIkB,CAAU,CAAA,KAEvD,OAAA,IAAI,MAAM,4CAA4C,EAE9D,KAAA,CAEF,QAAA,OAKkB,CAGdf,EAAA,CACJ,MAAO,UACP,YAAa,qDACb,QAAS,aAAA,CACV,CAAA,CAMCA,EAAA,CACJ,MAAO,UACP,YAAa,YAAYJ,CAAM,iBAC/B,QAAS,SAAA,CACV,EAGD,MAAMG,EAAY,kBAAkB,CAAE,SAAU,CAAC,iBAAiB,EAAG,EAEzDD,IAAA,EACJH,EAAA,EACRQ,EAAS,EAAE,QACJe,EAAO,CAERlB,EAAA,CACJ,MAAO,QACP,YAAakB,aAAiB,MAAQA,EAAM,QAAU,aAAatB,CAAM,YACzE,QAAS,aAAA,CACV,CAAA,QACD,CACAU,EAAa,EAAK,CAAA,CAEtB,EAEMa,EAAc,IAAM,CACnBd,IACKV,EAAA,EACRQ,EAAS,EAAE,EAEf,EAGE,OAAAiB,EAAA,IAACC,GAAO,KAAM3B,EAAQ,aAAcyB,EAClC,SAAAG,EAAA,KAACC,EAAc,CAAA,UAAU,mBACvB,SAAA,CAAAD,OAACE,EACC,CAAA,SAAA,CAAAF,OAACG,EACE,CAAA,SAAA,CAAA7B,IAAW,UAAY,UAAY,SAAS,UAAA,EAC/C,EACCwB,EAAA,IAAAM,EAAA,CACE,SAAW9B,IAAA,UACR,mFACA,qDAEN,CAAA,CAAA,EACF,QAEC,MAAI,CAAA,UAAU,kBACb,SAAC0B,EAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACF,EAAAA,IAAA,QAAA,CAAM,QAAQ,QAAQ,UAAU,sBAC9B,SAAWxB,IAAA,UAAY,mBAAqB,kBAC/C,CAAA,EACAwB,EAAA,IAACO,EAAA,CACC,GAAG,QACH,YACE/B,IAAW,UACP,8BACA,uDAEN,MAAOM,EACP,SAAW0B,GAAMzB,EAASyB,EAAE,OAAO,KAAK,EACxC,KAAM,EACN,SAAUvB,CAAA,CAAA,CACZ,CAAA,CACF,CACF,CAAA,SAECwB,EACC,CAAA,SAAA,CAAAT,EAAA,IAACU,EAAA,CACC,KAAK,SACL,QAAQ,UACR,QAASX,EACT,SAAUd,EACX,SAAA,QAAA,CAED,EACAe,EAAA,IAACU,EAAA,CACC,KAAK,SACL,QAASlC,IAAW,UAAY,UAAY,cAC5C,QAASW,EACT,SAAUF,GAAcT,IAAW,UAAY,CAACM,EAAM,KAAK,EAE1D,SAAYG,EAAA,gBAAkBT,IAAW,UAAY,UAAY,QAAA,CAAA,CACpE,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ,ECtUMmC,GAA0B,IAAM,CAC9B,KAAA,CAAE,MAAA/B,CAAM,EAAIC,EAAS,EACrBF,EAAciC,EAAe,EAC7B,CAAE,EAAAC,CAAE,EAAIC,EAAe,EAGvB,CAACC,EAAgBC,CAAiB,EAAIhC,WAKzC,CACD,OAAQ,GACR,WAAY,GACZ,OAAQ,SAAA,CACT,EAEKiC,EAAwBzC,GAAwI,CACpKwC,EAAkBxC,CAAM,CAC1B,EAEM0C,EAAsB,IAAM,CAChCF,MAA2B,CAAE,GAAGG,EAAM,OAAQ,IAAQ,CACxD,EAEMC,EAAwB,IAAM,CAEdF,EAAA,CACtB,EAGMG,EAAiD,CACrD,CACE,YAAa,oBACb,OAAQR,EAAE,kBAAkB,EAC5B,KAAMS,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,gBACb,OAAQT,EAAE,cAAc,EACxB,KAAMS,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,oBACb,OAAQT,EAAE,kBAAkB,EAC5B,KAAMS,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,uBACb,OAAQT,EAAE,eAAe,EACzB,KAAMS,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,eAAA,EAAmB,GACzF,EACA,CACE,YAAa,yBACb,OAAQT,EAAE,uBAAuB,EACjC,KAAMS,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,cACb,OAAQT,EAAE,mBAAmB,EAC7B,KAAMS,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,mBAAA,EAAuB,GAC7F,EACA,CACE,YAAa,SACb,OAAQT,EAAE,cAAc,EACxB,KAAcS,GAAA,CACN,MAAAC,EAASD,EAAK,SAAS,EACzB,IAAAE,EACAC,EAEJ,OAAQF,EAAQ,CACd,IAAK,GACHC,EAAaX,EAAE,qBAAqB,EAC1BY,EAAA,UACV,MACF,IAAK,GACHD,EAAaX,EAAE,sBAAsB,EAC3BY,EAAA,UACV,MACF,IAAK,GACHD,EAAaX,EAAE,sBAAsB,EAC3BY,EAAA,cACV,MACF,IAAK,GACHD,EAAaX,EAAE,uBAAuB,EAC5BY,EAAA,YACV,MACF,QACED,EAAaX,EAAE,qBAAqB,EAC1BY,EAAA,UACV,KAAA,CAGJ,OAAQzB,EAAA,IAAA0B,EAAA,CAAM,QAAAD,EAAkB,KAAK,KAAM,SAAWD,EAAA,CAAA,CAE1D,EACA,CACE,YAAa,oBACb,OAAQX,EAAE,iBAAiB,EAC3B,KAAMS,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,GAAI,UACJ,OAAQT,EAAE,eAAe,EACzB,KAAM,CAAC,CAAE,IAAAc,EAAK,MAAAC,KAAY,CACxB,MAAMC,EAAWF,EAAI,SACfG,EAAYD,EAAS,SAAW,EAChCpD,EAASoD,EAAS,OAClBE,EAAOH,EAAM,QAAQ,KAEzB,OAAA1B,EAAA,KAAC,MAAI,CAAA,UAAU,iBACZ,SAAA,CAAA4B,GAEG5B,EAAA,KAAA8B,WAAA,CAAA,SAAA,CAAA9B,OAAC+B,EACC,CAAA,SAAA,CAACjC,EAAAA,IAAAkC,EAAA,CAAe,QAAO,GACrB,SAAAlC,EAAA,IAACU,EAAA,CACC,QAAQ,QACR,KAAK,OACL,UAAU,sCACV,QAAS,IAAM,CACbqB,EAAK,kBAAkB,CACrB,OAAQ,GACR,WAAYF,EAAS,IAAM,GAC3B,OAAQ,UACR,OAAQA,EAAS,MAAA,CAClB,CACH,EACA,aAAW,UAEX,SAAA7B,EAAAA,IAACmC,EAAa,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,EAEtC,EACCnC,EAAAA,IAAAoC,EAAA,CAAgB,SAAEvB,EAAA,sBAAsB,CAAE,CAAA,CAAA,EAC7C,SACCoB,EACC,CAAA,SAAA,CAACjC,EAAAA,IAAAkC,EAAA,CAAe,QAAO,GACrB,SAAAlC,EAAA,IAACU,EAAA,CACC,QAAQ,QACR,KAAK,OACL,UAAU,kCACV,QAAS,IAAM,CACbqB,EAAK,kBAAkB,CACrB,OAAQ,GACR,WAAYF,EAAS,IAAM,GAC3B,OAAQ,SACR,OAAQA,EAAS,MAAA,CAClB,CACH,EACA,aAAW,SAEX,SAAA7B,EAAAA,IAACqC,EAAQ,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,EAEjC,EACCrC,EAAAA,IAAAoC,EAAA,CAAgB,SAAEvB,EAAA,qBAAqB,CAAE,CAAA,CAAA,CAC5C,CAAA,CAAA,EACF,SAEDoB,EACC,CAAA,SAAA,CAACjC,EAAAA,IAAAkC,EAAA,CAAe,QAAO,GACrB,SAAAlC,EAAA,IAACU,EAAA,CACC,QAAQ,QACR,KAAK,OACL,QAAS,IAAM,CACTjC,GAAUA,EAAO,KACfA,EAAO,aAAe,SACxB6D,EAAO,MAAM,gBAAgB7D,EAAO,EAAE,EAAE,EAC/BA,EAAO,aAAe,SAC/B6D,EAAO,MAAM,gBAAgB7D,EAAO,EAAE,EAAE,EAExC6D,EAAO,MAAM,eAAe7D,EAAO,EAAE,EAAE,EAG7C,EACA,aAAW,UAEX,SAAAuB,EAAAA,IAACuC,EAAa,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,EAEtC,EACCvC,EAAAA,IAAAoC,EAAA,CAAgB,SAAEvB,EAAA,mBAAmB,CAAE,CAAA,CAAA,CAC1C,CAAA,CAAA,EACF,CAAA,CAEJ,CAEJ,EAGE,OAAAX,EAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAAAF,EAAA,IAACwC,EAAA,CACC,QAASnB,EACT,MAAOR,EAAE,2BAA2B,EACpC,SAAU,CAAC,iBAAiB,EAC5B,cAAe,GACf,gBAAiB,GACjB,gBAAiB,GACjB,KAAM,CACJ,kBAAmBI,CACrB,EACA,QAAS,MAAO,CAAE,UAAAwB,EAAW,SAAAC,KAAe,CACtC,GAAA,CACI,MAAArD,EAAW,MAAMsD,EAAgC,CACrD,KAAM,CACJ,eAAgBD,EAChB,UAAWD,EAAYC,EACvB,YAAa,CACX,SAAU,MACV,WAAY,CACV,CACE,UAAW,SACX,SAAU,SACV,MAAO,GAAA,CACT,CACF,CACF,CACF,CACD,EACD,GAAI,CAACrD,GAAY,CAACA,EAAS,KACnB,MAAA,IAAI,MAAM,2BAA2B,EAEtC,MAAA,CACL,MAAOA,EAAS,KAAK,OAAS,CAAC,EAC/B,WAAYA,EAAS,KAAK,YAAc,CAC1C,OACY,CAEN,OAAAT,EAAA,CACJ,MAAO,QACP,YAAa,+BACb,QAAS,aAAA,CACV,EACM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CACpC,CACF,CACF,EAGAoB,EAAA,IAAC5B,EAAA,CACC,WAAY2C,EAAe,WAC3B,OAAQA,EAAe,OACvB,QAASG,EACT,OAAQH,EAAe,OACvB,OAAQA,EAAe,OACvB,UAAWK,EACX,YAAAzC,CAAA,CAAA,CACF,EACF,CAEJ", "x_google_ignoreList": [0, 1]}