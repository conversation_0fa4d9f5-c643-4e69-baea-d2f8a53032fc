import { useQuery } from '@tanstack/react-query';
import { postApiIdjasJettyRequestItemFilterList } from '@/client/sdk.gen';
import { toast } from '@/lib/useToast';
import type { FilterGroup, QueryParametersDto, PagedResultDtoOfJettyRequestItemDto } from '@/client/types.gen';

export const useJettyRequestItems = (
  pageIndex: number,
  pageSize: number,
  filterGroup?: FilterGroup,
  sorting?: string
) => {
  return useQuery<PagedResultDtoOfJettyRequestItemDto, Error>({
    queryKey: ['jetty-request-items', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],
    queryFn: async (): Promise<PagedResultDtoOfJettyRequestItemDto> => {
      const payload: QueryParametersDto = {
        skipCount: pageIndex * pageSize,
        maxResultCount: pageSize,
        sorting,
        filterGroup,
      };
      
      try {
        const response = await postApiIdjasJettyRequestItemFilterList({ body: payload });
        return response.data || { items: [], totalCount: 0 };
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty Request Items';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('JettyRequestItems API Error:', error);
        toast({
          title: 'Error loading Jetty Request Items',
          description: message,
          variant: 'destructive',
        });
        
        // Return empty result instead of throwing
        return { items: [], totalCount: 0 };
      }
    },
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
  });
}; 