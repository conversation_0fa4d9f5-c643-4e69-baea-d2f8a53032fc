import React, { useState } from 'react';
import { useOidc } from '../hooks/useOidc';

export const SimpleOidcExample: React.FC = () => {
  const { fetchWithTokenRefresh, checkTokenStatus, refreshTokenSilently } = useOidc();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const handleSearchJetties = async () => {
    setLoading(true);
    try {
      const response = await fetchWithTokenRefresh('/api/Ekb/jetty', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filter: 'test',
          maxResultCount: 10,
          skipCount: 0,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setResult(`Found ${data.totalCount} jetties`);
      } else {
        setResult('Error: ' + response.statusText);
      }
    } catch (error) {
      setResult('Error: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleCheckToken = async () => {
    setLoading(true);
    try {
      const status = await checkTokenStatus();
      if (status) {
        setResult(`Token valid: ${status.has_valid_token}, Refresh expired: ${status.refresh_token_expired}`);
      } else {
        setResult('Could not check token status');
      }
    } catch (error) {
      setResult('Error checking token: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshToken = async () => {
    setLoading(true);
    try {
      const success = await refreshTokenSilently();
      setResult(success ? 'Token refreshed successfully!' : 'Token refresh failed');
    } catch (error) {
      setResult('Error refreshing token: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-xl shadow-md space-y-4">
      <h2 className="text-xl font-bold text-gray-900">OIDC Token Manager Test</h2>
      
      <div className="space-y-3">
        <button
          onClick={handleSearchJetties}
          disabled={loading}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Search Jetties'}
        </button>

        <button
          onClick={handleCheckToken}
          disabled={loading}
          className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Check Token Status'}
        </button>

        <button
          onClick={handleRefreshToken}
          disabled={loading}
          className="w-full px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh Token'}
        </button>
      </div>

      {result && (
        <div className="mt-4 p-3 bg-gray-100 rounded">
          <p className="text-sm text-gray-700">{result}</p>
        </div>
      )}

      <div className="text-xs text-gray-500">
        <p>• Automatic token refresh every 5 minutes</p>
        <p>• Activity-based refresh on user interaction</p>
        <p>• Automatic retry on 401 errors</p>
      </div>
    </div>
  );
}; 