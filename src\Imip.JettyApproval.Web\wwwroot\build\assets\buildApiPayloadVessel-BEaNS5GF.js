import{j as s}from"./vendor-6tJeyfYI.js";import{F as u,G as p,H as h,B as v,J as y}from"./app-layout-rNt37hVL.js";import{B as i}from"./badge-DWaCYvGm.js";import{m as z}from"./App-DnhJzTNn.js";import{A as f}from"./arrow-up-right-DyuQRH0Y.js";const b=a=>{if(!a)return s.jsx(i,{variant:"outline",size:"sm",children:"-"});switch(a.toLowerCase()){case"draft":return s.jsx(i,{variant:"secondary",size:"sm",children:"Draft"});case"waiting":case"pending":return s.jsx(i,{variant:"warning",size:"sm",children:a});case"approved":return s.jsx(i,{variant:"success",size:"sm",children:"Approved"});case"rejected":return s.jsx(i,{variant:"destructive",size:"sm",children:"Rejected"});case"cancelled":return s.jsx(i,{variant:"outline",size:"sm",children:"Cancelled"});case"open":return s.jsx(i,{variant:"info",size:"sm",children:"Open"});case"submit":return s.jsx(i,{variant:"primary",size:"sm",children:"Submit"});default:return s.jsx(i,{variant:"outline",size:"sm",children:a})}};function d(a,n){const e=()=>{z.visit(`/${n}/edit/${a}`)};return s.jsxs(p,{children:[s.jsx(h,{asChild:!0,children:s.jsx(v,{size:"sm",variant:"ghost","aria-label":"View Vessel",onClick:e,children:s.jsx(f,{className:"w-4 h-4 text-blue-600"})})}),s.jsx(y,{children:"View Vessel"})]})}const V=()=>{const{t:a}=u();return[{accessorKey:"docNum",header:a("table.docNum"),cell:e=>e.getValue()??"-",enableSorting:!0,size:120},{id:"vessel.name",accessorKey:"vessel.name",header:a("table.vesselName"),cell:e=>e.row.original?.vessel?.name??"-",size:200,minSize:150,maxSize:300},{accessorKey:"voyage",header:a("table.voyage"),cell:e=>e.getValue()??"-",size:100},{accessorKey:"vesselArrival",header:a("table.arrivalDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{accessorKey:"vesselDeparture",header:a("table.departureDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{id:"masterJetty.name",accessorKey:"masterJetty.name",header:a("table.jetty"),cell:e=>e.row.original?.masterJetty?.name??"-",size:120},{accessorKey:"portOrigin",header:a("table.portOrigin"),cell:e=>e.getValue()??"-",size:180,minSize:120,maxSize:250},{accessorKey:"destinationPort",header:a("table.destinationPort"),cell:e=>e.getValue()??"-",size:200,minSize:150,maxSize:300},{id:"actions",header:a("table.actions"),cell:e=>{const r=e.row.original.id;return r?d(r,"import"):null},enableSorting:!1,enableColumnFilter:!1,size:100}]},j=()=>{const{t:a}=u();return[{accessorKey:"docNum",header:a("table.docNum"),cell:e=>e.getValue()??"-",enableSorting:!0,size:120},{id:"vessel.name",accessorKey:"vessel.name",header:a("table.vesselName"),cell:e=>e.row.original?.vessel?.name??"-",size:200,minSize:150,maxSize:300},{accessorKey:"voyage",header:a("table.voyage"),cell:e=>e.getValue()??"-",size:100},{accessorKey:"vesselArrival",header:a("table.arrivalDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{accessorKey:"vesselDeparture",header:a("table.departureDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{id:"masterJetty.name",accessorKey:"masterJetty.name",header:a("table.jetty"),cell:e=>e.row.original?.masterJetty?.name??"-",size:120},{accessorKey:"portOrigin",header:a("table.portOrigin"),cell:e=>e.getValue()??"-",size:180,minSize:120,maxSize:250},{accessorKey:"destinationPort",header:a("table.destinationPort"),cell:e=>e.getValue()??"-",size:200,minSize:150,maxSize:300},{id:"actions",header:a("table.actions"),cell:e=>{const r=e.row.original.id;return r?d(r,"export"):null},enableSorting:!1,enableColumnFilter:!1,size:100}]},C=()=>{const{t:a}=u();return[{accessorKey:"docNum",header:a("table.docNum"),cell:e=>e.getValue()??"-",enableSorting:!0,size:120},{accessorKey:"docType",header:a("table.docType"),cell:e=>e.getValue()??"-",enableSorting:!0,size:100},{id:"vessel.name",accessorKey:"vessel.name",header:a("table.vesselName"),cell:e=>e.row.original?.vessel?.name??"-",size:200,minSize:150,maxSize:300},{accessorKey:"voyage",header:a("table.voyage"),cell:e=>e.getValue()??"-",size:100},{id:"barge.name",accessorKey:"barge.name",header:a("table.bargeName"),cell:e=>e.row.original?.barge?.name??"-",size:180,minSize:120,maxSize:250},{accessorKey:"vesselArrival",header:a("table.arrivalDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{accessorKey:"vesselDeparture",header:a("table.departureDate"),cell:e=>{const r=e.getValue();if(!r)return"-";const t=new Date(r);return isNaN(t.getTime())?"-":`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")} ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`},size:150},{id:"masterJetty.name",accessorKey:"masterJetty.name",header:a("table.jetty"),cell:e=>e.row.original?.masterJetty?.name??"-",size:120},{id:"docStatus",accessorKey:"docStatus",header:a("table.docStatus"),cell:e=>b(e.row.original?.docStatus),size:120},{id:"actions",header:a("table.actions"),cell:e=>{const r=e.row.original.id;return r?d(r,"local"):null},enableSorting:!1,enableColumnFilter:!1,size:100}]};function A({pageIndex:a,pageSize:n,sorting:e,filters:r,globalFilter:t,vesselType:g,isCustomArea:S=!0}){let c;Array.isArray(e)&&e.length>0?c=e.map(o=>`${o.id} ${o.desc?"desc":"asc"}`).join(", "):c="docNum desc";const l=[];if(t&&l.push({fieldName:"vessel.name",operator:"Contains",value:t}),Array.isArray(r))for(const o of r)o.value&&l.push({fieldName:o.id,operator:"Contains",value:o.value});g&&g.toLowerCase()==="export"&&l.unshift({fieldName:"docType",operator:"Equals",value:"Export"}),S?l.unshift({fieldName:"masterJetty.isCustomArea",operator:"Equals",value:"true"}):l.unshift({fieldName:"masterJetty.isCustomArea",operator:"Equals",value:"false"});const m=l.length>0?{operator:"And",conditions:l}:void 0;return{page:a+1,maxResultCount:n,...c?{sorting:c}:{},...m?{filterGroup:m}:{}}}export{V as a,A as b,C as c,j as u};
//# sourceMappingURL=buildApiPayloadVessel-BEaNS5GF.js.map
