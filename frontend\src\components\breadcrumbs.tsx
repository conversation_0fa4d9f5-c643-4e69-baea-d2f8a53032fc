import * as React from 'react'
import { usePage } from '@inertiajs/react'
import { Link } from '@inertiajs/react'
import { useQuery } from '@tanstack/react-query'
import { ekbProxyService } from '@/services/ekbProxyService'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"

// UUID regex pattern to detect IDs
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

// Hook to fetch vessel data and return a meaningful name
function useVesselName(id: string, vesselType: 'export' | 'import' | 'local') {
  // const vesselName = (vesselType === 'export') ? 'export-vessel' : (vesselType === 'import') ? 'import' : 'local'
  return useQuery({
    queryKey: ['vessel-name', id],
    queryFn: async () => {
      try {
        let response
        switch (vesselType) {
          case 'export':
            response = await ekbProxyService.getExportVesselWithItems(id)
            break
          case 'import':
            response = await ekbProxyService.getImportVesselWithItems(id)
            break
          case 'local':
            response = await ekbProxyService.getLocalVesselWithItems(id)
            break
          default:
            return null
        }

        const data = response.data
        if (!data) return null

        // Create a meaningful name from available data
        // Priority: docNum > vesselName (resolved from cargo) > voyage > shipment
        if (data.docNum) {
          return `Doc: ${data.docNum}`
        }

        // For vesselName, try to resolve the actual vessel name if it's an ID
        if (data.vesselName) {
          // If vesselName is a string, use it directly
          if (typeof data.vesselName === 'string') {
            return `Vessel: ${data.vesselName}`
          }
          // If vesselName is a number (ID), try to resolve it
          if (typeof data.vesselName === 'number' && data.vesselId) {
            try {
              const cargoResponse = await ekbProxyService.filterCargo({
                maxResultCount: 1,
                skipCount: 0,
                filterGroup: {
                  operator: 'And',
                  conditions: [
                    { fieldName: 'id', operator: 'Equals', value: data.vesselId }
                  ]
                }
              })
              const cargoData = cargoResponse.data?.items?.[0]
              if (cargoData?.name) {
                return `Vessel: ${cargoData.name}`
              }
            } catch (error) {
              console.warn('Failed to resolve vessel name from cargo:', error)
            }
          }
        }

        if (data.voyage) {
          return `Voyage: ${data.voyage}`
        }

        if (data.shipment) {
          return `Shipment: ${data.shipment}`
        }

        return null
      } catch (error) {
        console.warn('Failed to fetch vessel data for breadcrumb:', error)
        return null
      }
    },
    enabled: !!id && UUID_REGEX.test(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Don't retry on failure to avoid spam
  })
}

// Component for rendering a single breadcrumb segment
function BreadcrumbSegment({
  segment,
  index,
  pathSegments,
  isLastItem
}: {
  segment: string
  index: number
  pathSegments: string[]
  isLastItem: boolean
}) {
  const href = `/${pathSegments.slice(0, index + 1).join('/')}`

  // Detect if this is a vessel ID and what type
  const isVesselId = UUID_REGEX.test(segment)
  const vesselType = React.useMemo(() => {
    if (!isVesselId) return null

    // Determine vessel type from the path
    if (pathSegments.includes('export')) return 'export'
    if (pathSegments.includes('import')) return 'import'
    if (pathSegments.includes('local')) return 'local'
    return null
  }, [isVesselId, pathSegments])

  // Fetch vessel name if this is a vessel ID
  const { data: vesselName, isLoading } = useVesselName(
    segment,
    vesselType as 'export' | 'import' | 'local'
  )

  // Determine display name
  const displayName = React.useMemo(() => {
    if (isVesselId && vesselName) {
      return vesselName
    }
    if (isVesselId && isLoading) {
      return 'Loading...'
    }
    if (isVesselId) {
      // Fallback: show shortened ID with a more user-friendly format
      return `ID: ${segment.substring(0, 8)}...`
    }

    // Regular path segment formatting
    return segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }, [segment, isVesselId, vesselName, isLoading])

  return (
    <React.Fragment key={href}>
      <BreadcrumbItem>
        {isLastItem ? (
          <BreadcrumbPage>{displayName}</BreadcrumbPage>
        ) : (
          <BreadcrumbLink asChild>
            <Link href={href}>{displayName}</Link>
          </BreadcrumbLink>
        )}
      </BreadcrumbItem>
      {!isLastItem && <BreadcrumbSeparator />}
    </React.Fragment>
  )
}

export function DynamicBreadcrumb() {
  const { url } = usePage()

  // Skip rendering breadcrumbs on home page
  if (url === '/') {
    return null
  }

  // Create breadcrumb items from URL
  const pathSegments = url.split('/').filter(Boolean)

  return (
    <Breadcrumb>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <Link href="/">Home</Link>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator />
        {pathSegments.map((segment, index) => {
          const isLastItem = index === pathSegments.length - 1

          return (
            <BreadcrumbSegment
              key={`${segment}-${index}`}
              segment={segment}
              index={index}
              pathSegments={pathSegments}
              isLastItem={isLastItem}
            />
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}