import * as z from 'zod';

export const localVesselItemSchema = z.object({
  id: z.string().optional(),
  itemName: z.string().nullable().optional(),
  itemQty: z.number().nullable().optional(),
  unitQty: z.string().nullable().optional(),
  remarks: z.string().nullable().optional(),
  tenant: z.string().nullable().optional(),
  tenantId: z.string().nullable().optional(),
  businessPartner: z.string().nullable().optional(),
  businessPartnerId: z.string().nullable().optional(),
  letterNo: z.string().nullable().optional(),
  letterDate: z.string().nullable().optional(),
  status: z.string().nullable().optional(),
  concurrencyStamp: z.string().nullable().optional(),
  // Add more fields as needed from CreateUpdateVesselItemDto
});

export type LocalVesselItemForm = z.infer<typeof localVesselItemSchema>; 