{"version": 3, "file": "DataTable-CDIoPElA.js", "sources": ["../../../../../frontend/src/components/ui/search.tsx", "../../../../../frontend/node_modules/.pnpm/@atlaskit+motion@5.1.3_@types+react@19.1.6_react@19.1.0/node_modules/@atlaskit/motion/dist/esm/utils/durations.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-an_adf179453cabb8c2332e9e4f92143caa/node_modules/@atlaskit/pragmatic-drag-and-drop-flourish/dist/esm/trigger-post-move-flash.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/typeof.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/toPrimitive.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/toPropertyKey.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop-hitbox@1.1.0/node_modules/@atlaskit/pragmatic-drag-and-drop-hitbox/dist/esm/closest-edge.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop-hitbox@1.1.0/node_modules/@atlaskit/pragmatic-drag-and-drop-hitbox/dist/esm/get-reorder-destination-index.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop-live-region@1.3.0/node_modules/@atlaskit/pragmatic-drag-and-drop-live-region/dist/esm/constants.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop-live-region@1.3.0/node_modules/@atlaskit/pragmatic-drag-and-drop-live-region/dist/esm/index.js", "../../../../../frontend/node_modules/.pnpm/@compiled+react@0.18.5_react@19.1.0/node_modules/@compiled/react/dist/esm/runtime/ax.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-an_e4e32598bb21eb6c87f804d3a9231cac/node_modules/@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/dist/esm/presets.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-an_e4e32598bb21eb6c87f804d3a9231cac/node_modules/@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/dist/esm/internal/line.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-an_e4e32598bb21eb6c87f804d3a9231cac/node_modules/@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/dist/esm/box.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/public-utils/combine.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/arrayLikeToArray.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/unsupportedIterableToArray.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/nonIterableRest.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/slicedToArray.js", "../../../../../frontend/node_modules/.pnpm/bind-event-listener@3.0.0/node_modules/bind-event-listener/dist/bind.js", "../../../../../frontend/node_modules/.pnpm/bind-event-listener@3.0.0/node_modules/bind-event-listener/dist/bind-all.js", "../../../../../frontend/node_modules/.pnpm/bind-event-listener@3.0.0/node_modules/bind-event-listener/dist/index.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/honey-pot-fix/honey-pot-data-attribute.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/honey-pot-fix/is-honey-pot-element.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/honey-pot-fix/get-element-from-point-without-honey-pot.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/max-z-index.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/honey-pot-fix/make-honey-pot-fix.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/iterableToArray.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js", "../../../../../frontend/node_modules/.pnpm/@babel+runtime@7.27.6/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/public-utils/once.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/is-firefox.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/is-safari.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/changing-window/count-events-for-safari.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/changing-window/is-from-another-window.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/changing-window/is-leaving-window.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/detect-broken-drag.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/get-input.js", "../../../../../frontend/node_modules/.pnpm/raf-schd@4.0.3/node_modules/raf-schd/dist/raf-schd.esm.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/ledger/dispatch-consumer-event.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/ledger/lifecycle-manager.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/ledger/usage-ledger.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/add-attribute.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/make-adapter/make-drop-target.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/make-adapter/make-monitor.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/make-adapter/make-adapter.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/android.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/media-types/text-media-type.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/media-types/url-media-type.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/adapter/element-adapter-native-data-key.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/adapter/element-adapter.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/util/is-safari-on-ios.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/public-utils/element/custom-native-drag-preview/pointer-outside-of-preview.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/public-utils/element/custom-native-drag-preview/set-custom-native-drag-preview.js", "../../../../../frontend/node_modules/.pnpm/@atlaskit+pragmatic-drag-and-drop@1.7.2/node_modules/@atlaskit/pragmatic-drag-and-drop/dist/esm/public-utils/reorder.js", "../../../../../frontend/src/components/data-table/DataTableViewOptions.tsx", "../../../../../frontend/src/components/data-table/DataTableFilterbar.tsx", "../../../../../frontend/src/components/data-table/DataTablePagination.tsx", "../../../../../frontend/src/components/data-table/DataTable.tsx"], "sourcesContent": ["import { memo, type SyntheticEvent, useCallback, useEffect, useRef, useState } from 'react'\r\nimport { Input } from '@/components/ui/input'\r\nimport { useDebounce } from './useDebounce'\r\n\r\ntype SearchProps = {\r\n  onUpdate: (searchStr: string) => void\r\n  value: string\r\n}\r\nconst SearchInput = ({ onUpdate, value }: SearchProps) => {\r\n  // Initialize with the provided value\r\n  const [searchTerm, setSearchTerm] = useState<string>(value)\r\n  const ref = useRef<HTMLInputElement>(null)\r\n  const searchStr = useDebounce<string>(searchTerm)\r\n\r\n  // Track if we're handling a user input vs. prop change\r\n  const isUserInput = useRef(false)\r\n\r\n  // Handle user typing in the search box\r\n  const onSearchEvent = useCallback((e: SyntheticEvent) => {\r\n    const target = e.target as HTMLInputElement\r\n    const { value } = target\r\n    isUserInput.current = true\r\n    setSearchTerm(value)\r\n  }, [])\r\n\r\n  // Only update parent when debounced search term changes (from user input)\r\n  useEffect(() => {\r\n    if (isUserInput.current) {\r\n      onUpdate(searchStr || '')\r\n      isUserInput.current = false\r\n    }\r\n  }, [searchStr, onUpdate])\r\n\r\n  // Update internal state when prop value changes (from parent)\r\n  useEffect(() => {\r\n    // Only update if the value has actually changed and is different from current searchTerm\r\n    // And only if it's not from user input\r\n    if (value !== searchTerm && !isUserInput.current) {\r\n      setSearchTerm(value)\r\n    }\r\n\r\n    // Focus the input if there's a value\r\n    if (value) {\r\n      ref.current?.focus()\r\n    }\r\n  }, [value, searchTerm])\r\n\r\n  return (\r\n    <section className=\"search\">\r\n      <Input\r\n        ref={ref}\r\n        type=\"text\"\r\n        value={searchTerm}\r\n        placeholder=\"Search...\"\r\n        onChange={onSearchEvent}\r\n      />\r\n    </section>\r\n  )\r\n}\r\n\r\nexport const Search = memo(SearchInput)\r\n", "/**\n * Think of this as the motion equivalent of the @atlaskit/theme `grid()`.\n */\nexport var durationStep = 25;\nexport var durations = {\n  none: 0,\n  small: 100,\n  medium: 350,\n  large: 700\n};\n\n/**\n * Used to multiply the initial duration for exiting motions.\n */\nvar EXITING_MOTION_MULTIPLIER = 0.5;\nexport var exitingDurations = {\n  none: durations.none,\n  small: durations.small * EXITING_MOTION_MULTIPLIER,\n  medium: durations.medium * EXITING_MOTION_MULTIPLIER,\n  large: durations.large * EXITING_MOTION_MULTIPLIER\n};", "import { durations } from '@atlaskit/motion/durations';\n/**\n * Runs a flash animation on the background color of the provided `element`.\n *\n * This animation should be used after an element has been reordered,\n * in order to highlight where the element has moved to.\n */\nexport function triggerPostMoveFlash(element) {\n  element.animate([{\n    backgroundColor: \"var(--ds-background-selected, #E9F2FF)\"\n  }, {}], {\n    duration: durations.large,\n    /**\n     * This is equivalent to the browser default, but we are making it\n     * explicit to avoid relying on implicit behavior.\n     *\n     * This curve is not part of `@atlaskit/motion` but it was an intentional\n     * design decision to use this curve.\n     */\n    easing: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',\n    iterations: 1\n  });\n}", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n// re-exporting type to make it easy to use\n\nvar getDistanceToEdge = {\n  top: function top(rect, client) {\n    return Math.abs(client.y - rect.top);\n  },\n  right: function right(rect, client) {\n    return Math.abs(rect.right - client.x);\n  },\n  bottom: function bottom(rect, client) {\n    return Math.abs(rect.bottom - client.y);\n  },\n  left: function left(rect, client) {\n    return Math.abs(client.x - rect.left);\n  }\n};\n\n// using a symbol so we can guarantee a key with a unique value\nvar uniqueKey = Symbol('closestEdge');\n\n/**\n * Adds a unique `Symbol` to the `userData` object. Use with `extractClosestEdge()` for type safe lookups.\n */\nexport function attachClosestEdge(userData, _ref) {\n  var _entries$sort$0$edge, _entries$sort$;\n  var element = _ref.element,\n    input = _ref.input,\n    allowedEdges = _ref.allowedEdges;\n  var client = {\n    x: input.clientX,\n    y: input.clientY\n  };\n  // I tried caching the result of `getBoundingClientRect()` for a single\n  // frame in order to improve performance.\n  // However, on measurement I saw no improvement. So no longer caching\n  var rect = element.getBoundingClientRect();\n  var entries = allowedEdges.map(function (edge) {\n    return {\n      edge: edge,\n      value: getDistanceToEdge[edge](rect, client)\n    };\n  });\n\n  // edge can be `null` when `allowedEdges` is []\n  var addClosestEdge = (_entries$sort$0$edge = (_entries$sort$ = entries.sort(function (a, b) {\n    return a.value - b.value;\n  })[0]) === null || _entries$sort$ === void 0 ? void 0 : _entries$sort$.edge) !== null && _entries$sort$0$edge !== void 0 ? _entries$sort$0$edge : null;\n  return _objectSpread(_objectSpread({}, userData), {}, _defineProperty({}, uniqueKey, addClosestEdge));\n}\n\n/**\n * Returns the value added by `attachClosestEdge()` to the `userData` object. It will return `null` if there is no value.\n */\nexport function extractClosestEdge(userData) {\n  var _ref2;\n  return (_ref2 = userData[uniqueKey]) !== null && _ref2 !== void 0 ? _ref2 : null;\n}", "export function getReorderDestinationIndex(_ref) {\n  var startIndex = _ref.startIndex,\n    closestEdgeOfTarget = _ref.closestEdgeOfTarget,\n    indexOfTarget = _ref.indexOfTarget,\n    axis = _ref.axis;\n  // invalid index's\n  if (startIndex === -1 || indexOfTarget === -1) {\n    return startIndex;\n  }\n\n  // if we are targeting the same index we don't need to do anything\n  if (startIndex === indexOfTarget) {\n    return startIndex;\n  }\n  if (closestEdgeOfTarget == null) {\n    return indexOfTarget;\n  }\n  var isGoingAfter = axis === 'vertical' && closestEdgeOfTarget === 'bottom' || axis === 'horizontal' && closestEdgeOfTarget === 'right';\n  var isMovingForward = startIndex < indexOfTarget;\n  // moving forward\n  if (isMovingForward) {\n    return isGoingAfter ? indexOfTarget : indexOfTarget - 1;\n  }\n  // moving backwards\n  return isGoingAfter ? indexOfTarget + 1 : indexOfTarget;\n}", "/**\n * Delay until the text content of the live region is updated.\n *\n * This value was reached through some experimentation, and may need tweaking in the future.\n */\nexport var announceDelay = 1000;", "import { announceDelay } from './constants';\nvar node = null;\nvar size = '1px';\nvar visuallyHiddenStyles = {\n  // Standard visually hidden styles.\n  // Copied from our VisuallyHidden (react) package.\n  width: size,\n  height: size,\n  padding: '0',\n  position: 'absolute',\n  border: '0',\n  clip: \"rect(\".concat(size, \", \").concat(size, \", \").concat(size, \", \").concat(size, \")\"),\n  overflow: 'hidden',\n  whiteSpace: 'nowrap',\n  // Pulling upwards slightly to prevent the page\n  // from growing when appended to a body that contains\n  // an element with height:100%\n  marginTop: \"-\".concat(size),\n  // Just being safe and letting this element not interfere with hitboxes\n  pointerEvents: 'none'\n};\n\n/**\n * Creates a live region node, appends it to the body, and returns it.\n */\nfunction createNode() {\n  var node = document.createElement('div');\n  /**\n   * Using `role=\"status\"` instead of `role=\"alert\"` so that the message\n   * can be queued and read when able.\n   *\n   * We found with `role=\"alert\"` the message was not reliably read when\n   * focus changed.\n   */\n  node.setAttribute('role', 'status');\n  Object.assign(node.style, visuallyHiddenStyles);\n  document.body.append(node);\n  return node;\n}\n\n/**\n * Returns the live region node, creating one if necessary.\n */\nfunction getNode() {\n  if (node === null) {\n    node = createNode();\n  }\n  return node;\n}\nvar timerId = null;\nfunction tryClearTimer() {\n  if (timerId !== null) {\n    clearTimeout(timerId);\n  }\n  timerId = null;\n}\n\n/**\n * Announces the provided message to assistive technology.\n */\nexport function announce(message) {\n  /**\n   * Calling this immediately to ensure a node exists and has time to be parsed\n   * and exposed in the accessibility tree.\n   */\n  getNode();\n\n  /**\n   * Updating the message in a timeout so that it's less likely to be interrupted.\n   *\n   * This function is often called right before focus changes,\n   * because the user has just taken an action.\n   * This focus change would often cause the message to be skipped / interrupted.\n   */\n  tryClearTimer();\n  timerId = setTimeout(function () {\n    timerId = null;\n    var node = getNode();\n    node.textContent = message;\n  }, announceDelay);\n  return;\n}\n\n/**\n * Removes the created live region. If there is no live region this is a no-op.\n */\nexport function cleanup() {\n  var _node;\n  tryClearTimer();\n  (_node = node) === null || _node === void 0 || _node.remove();\n  node = null;\n}", "/**\n * This length includes the underscore,\n * e.g. `\"_1s4A\"` would be a valid atomic group hash.\n */\nconst ATOMIC_GROUP_LENGTH = 5;\n/**\n * Create a single string containing all the classnames provided, separated by a space (`\" \"`).\n * The result will only contain the _last_ atomic style classname for each atomic `group`.\n *\n * ```ts\n * ax(['_aaaabbbb', '_aaaacccc']);\n * // output\n * '_aaaacccc'\n * ```\n *\n * Format of Atomic style classnames: `_{group}{value}` (`_\\w{4}\\w{4}`)\n *\n * `ax` will preserve any non atomic style classnames (eg `\"border-red\"`)\n *\n * ```ts\n * ax(['_aaaabbbb', '_aaaacccc', 'border-red']);\n * // output\n * '_aaaacccc border-red'\n * ```\n */\nexport default function ax(classNames) {\n    // Shortcut: nothing to do\n    if (!classNames.length) {\n        return;\n    }\n    // Shortcut: don't need to do anything if we only have a single classname\n    if (classNames.length === 1 &&\n        classNames[0] &&\n        // checking to see if `classNames[0]` is a string that contains other classnames\n        !classNames[0].includes(' ')) {\n        return classNames[0];\n    }\n    // Using an object rather than a `Map` as it performed better in our benchmarks.\n    // Would be happy to move to `Map` if it proved to be better under real conditions.\n    const map = {};\n    // Note: using loops to minimize iterations over the collection\n    for (const value of classNames) {\n        // Exclude all falsy values, which leaves us with populated strings\n        if (!value) {\n            continue;\n        }\n        // a `value` can contain multiple classnames\n        const list = value.split(' ');\n        for (const className of list) {\n            /**\n             * For atomic style classnames: the `key` is the `group`\n             *\n             * - Later atomic classnames with the same `group` will override earlier ones\n             *   (which is what we want).\n             * - Assumes atomic classnames are the only things that start with `_`\n             * - Could use a regex to ensure that atomic classnames are structured how we expect,\n             *   but did not add that for now as it did slow things down a bit.\n             *\n             * For other classnames: the `key` is the whole classname\n             * - Okay to remove duplicates as doing so does not impact specificity\n             *\n             * */\n            const key = className.startsWith('_') ? className.slice(0, ATOMIC_GROUP_LENGTH) : className;\n            map[key] = className;\n        }\n    }\n    /**\n     * We are converting the `map` into a string.\n     *\n     * The simple way to do this would be `Object.values(map).join(' ')`.\n     * However, the approach below performs 10%-20% better in benchmarks.\n     *\n     * For `ax()` it feels right to squeeze as much runtime performance out as we can.\n     */\n    let result = '';\n    for (const key in map) {\n        result += map[key] + ' ';\n    }\n    // If we have an empty string, then our `map` was empty.\n    if (!result) {\n        return;\n    }\n    // remove last \" \" from the result (we added \" \" at the end of every value)\n    return result.trimEnd();\n}\n//# sourceMappingURL=ax.js.map", "export var presetStrokeColors = {\n  default: \"var(--ds-border-selected, #0C66E4)\",\n  warning: \"var(--ds-border-warning, #E56910)\"\n};\nexport var presetStrokeWidth = \"var(--ds-border-width-outline, 2px)\";", "/* line.tsx generated by @compiled/babel-plugin v0.36.1 */\n/* eslint-disable @atlaskit/ui-styling-standard/enforce-style-prop */\nimport \"./line.compiled.css\";\nimport * as React from 'react';\nimport { ax, ix } from \"@compiled/react/runtime\";\nimport { presetStrokeColors, presetStrokeWidth } from '../presets';\nvar edgeToOrientationMap = {\n  top: 'horizontal',\n  bottom: 'horizontal',\n  left: 'vertical',\n  right: 'vertical'\n};\nvar baseStyles = {\n  root: \"_1e0c1ule _kqswstnw _1pbykb7n _lcxvglyw _bfhkys7w _rfx31ssb _3l8810ly _kzdanqa1 _15m6ys7w _cfu11ld9 _1kt9b3bt _1cs8stnw _13y0usvi _1mp4vjfa _kfgtvjfa\"\n};\nvar orientationStyles = {\n  horizontal: \"_4t3i10ly _1e02fghn _rjxpidpf _z5wtuj5p\",\n  vertical: \"_1bsb10ly _154ifghn _94n5idpf _1aukuj5p\"\n};\nvar edgeStyles = {\n  top: \"_154ihv0e _1auk70hn\",\n  right: \"_1xi2hv0e _ooun70hn\",\n  bottom: \"_94n5hv0e _19wo70hn\",\n  left: \"_1ltvhv0e _qnec70hn\"\n};\nvar lineStartFrom = {\n  // - half the terminal bleeding out the containing element\n  // - half the terminal inside the containing element (we need to position the line next to this)\n  terminal: function terminal(_ref) {\n    var indent = _ref.indent;\n    return \"calc(var(--terminal-radius) + \".concat(indent, \")\");\n  },\n  // The full terminal is inside the containing element (we need to position the line next to this)\n  'terminal-no-bleed': function terminalNoBleed(_ref2) {\n    var indent = _ref2.indent;\n    return \"calc(var(--terminal-diameter) + \".concat(indent, \")\");\n  },\n  // No terminal to worry about, line should take up all the space\n  'no-terminal': function noTerminal(_ref3) {\n    var indent = _ref3.indent;\n    return indent;\n  }\n};\nexport function Line(_ref4) {\n  var edge = _ref4.edge,\n    _ref4$gap = _ref4.gap,\n    gap = _ref4$gap === void 0 ? '0px' : _ref4$gap,\n    _ref4$indent = _ref4.indent,\n    indent = _ref4$indent === void 0 ? '0px' : _ref4$indent,\n    _ref4$strokeColor = _ref4.strokeColor,\n    strokeColor = _ref4$strokeColor === void 0 ? presetStrokeColors.default : _ref4$strokeColor,\n    _ref4$strokeWidth = _ref4.strokeWidth,\n    strokeWidth = _ref4$strokeWidth === void 0 ? presetStrokeWidth : _ref4$strokeWidth,\n    _ref4$type = _ref4.type,\n    type = _ref4$type === void 0 ? 'terminal' : _ref4$type;\n  var orientation = edgeToOrientationMap[edge];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      // ## All\n\n      '--stroke-color': strokeColor,\n      '--stroke-width': strokeWidth,\n      // Shift line and terminal on the main access to account for gaps between items\n      '--main-axis-offset': \"calc(-0.5 * (\".concat(gap, \" + var(--stroke-width)))\"),\n      // ## Line\n\n      // If there is a terminal, we want the line to start from next to it\n      '--line-main-axis-start': lineStartFrom[type]({\n        indent: indent\n      }),\n      // ## Terminal\n\n      '--terminal-display': type === 'no-terminal' ? 'none' : 'block',\n      '--terminal-diameter': 'calc(var(--stroke-width) * 4)',\n      '--terminal-radius': 'calc(var(--terminal-diameter) / 2)',\n      // The line is positioned to account for the the terminal (--line-main-axis-start).\n      // The terminal is rendered relative to the line (it's a `::before`)\n      // We need to pull the terminal backwards so it sits before the start of the line\n      '--terminal-main-axis-start': 'calc(-1 * var(--terminal-diameter))',\n      // Pull the terminal backwards on the cross axis (eg \"up\" on \"vertical\")\n      // so the center of the terminal lines up with the center of the line\n      '--terminal-cross-axis-offset': 'calc(calc(var(--stroke-width) - var(--terminal-diameter)) / 2)'\n    },\n    className: ax([baseStyles.root, orientationStyles[orientation], edgeStyles[edge]])\n  });\n}\n\n// For React.lazy\nexport default Line;", "import React from 'react';\nimport { Line } from './internal/line';\nimport { presetStrokeColors } from './presets';\n/**\n * __Drop indicator__\n *\n * A drop indicator is used to communicate the intended resting place of the draggable item. The orientation of the drop indicator should always match the direction of the content flow.\n */\nexport function DropIndicator(_ref) {\n  var _ref$appearance = _ref.appearance,\n    appearance = _ref$appearance === void 0 ? 'default' : _ref$appearance,\n    edge = _ref.edge,\n    gap = _ref.gap,\n    indent = _ref.indent,\n    type = _ref.type;\n  return /*#__PURE__*/React.createElement(Line, {\n    edge: edge,\n    gap: gap,\n    strokeColor: presetStrokeColors[appearance],\n    type: type,\n    indent: indent\n  });\n}\n\n// This default export is intended for usage with React.lazy\nexport default DropIndicator;", "/** Create a new combined function that will call all the provided functions */\nexport function combine() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n  return function cleanup() {\n    fns.forEach(function (fn) {\n      return fn();\n    });\n  };\n}", "function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nexport { _iterableToArrayLimit as default };", "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };", "import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nexport { _slicedToArray as default };", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bind = void 0;\nfunction bind(target, _a) {\n    var type = _a.type, listener = _a.listener, options = _a.options;\n    target.addEventListener(type, listener, options);\n    return function unbind() {\n        target.removeEventListener(type, listener, options);\n    };\n}\nexports.bind = bind;\n", "\"use strict\";\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bindAll = void 0;\nvar bind_1 = require(\"./bind\");\nfunction toOptions(value) {\n    if (typeof value === 'undefined') {\n        return undefined;\n    }\n    if (typeof value === 'boolean') {\n        return {\n            capture: value,\n        };\n    }\n    return value;\n}\nfunction getBinding(original, sharedOptions) {\n    if (sharedOptions == null) {\n        return original;\n    }\n    var binding = __assign(__assign({}, original), { options: __assign(__assign({}, toOptions(sharedOptions)), toOptions(original.options)) });\n    return binding;\n}\nfunction bindAll(target, bindings, sharedOptions) {\n    var unbinds = bindings.map(function (original) {\n        var binding = getBinding(original, sharedOptions);\n        return (0, bind_1.bind)(target, binding);\n    });\n    return function unbindAll() {\n        unbinds.forEach(function (unbind) { return unbind(); });\n    };\n}\nexports.bindAll = bindAll;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bindAll = exports.bind = void 0;\nvar bind_1 = require(\"./bind\");\nObject.defineProperty(exports, \"bind\", { enumerable: true, get: function () { return bind_1.bind; } });\nvar bind_all_1 = require(\"./bind-all\");\nObject.defineProperty(exports, \"bindAll\", { enumerable: true, get: function () { return bind_all_1.bindAll; } });\n", "// pulling this into a separate file so adapter(s) that don't\n// need the honey pot can pay as little as possible for it.\nexport var honeyPotDataAttribute = 'data-pdnd-honey-pot';", "import { honeyPotDataAttribute } from './honey-pot-data-attribute';\nexport function isHoneyPotElement(target) {\n  return target instanceof Element && target.hasAttribute(honeyPotDataAttribute);\n}", "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { isHoneyPotElement } from './is-honey-pot-element';\nexport function getElementFromPointWithoutHoneypot(client) {\n  // eslint-disable-next-line no-restricted-syntax\n  var _document$elementsFro = document.elementsFromPoint(client.x, client.y),\n    _document$elementsFro2 = _slicedToArray(_document$elementsFro, 2),\n    top = _document$elementsFro2[0],\n    second = _document$elementsFro2[1];\n  if (!top) {\n    return null;\n  }\n  if (isHoneyPotElement(top)) {\n    return second !== null && second !== void 0 ? second : null;\n  }\n  return top;\n}", "// Maximum possible z-index\n// https://stackoverflow.com/questions/491052/minimum-and-maximum-value-of-z-index\nexport var maxZIndex = 2147483647;", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { bind, bindAll } from 'bind-event-listener';\nimport { maxZIndex } from '../util/max-z-index';\nimport { honeyPotDataAttribute } from './honey-pot-data-attribute';\nvar honeyPotSize = 2;\nvar halfHoneyPotSize = honeyPotSize / 2;\n\n/**\n * `clientX` and `clientY` can be in sub pixels (eg `2.332`)\n * However, browser hitbox testing is commonly do to the closest pixel.\n *\n * → https://issues.chromium.org/issues/40940531\n *\n * To be sure that the honey pot will be over the `client` position,\n * we `.floor()` `clientX` and`clientY` and then make it `2px` in size.\n **/\nfunction floorToClosestPixel(point) {\n  return {\n    x: Math.floor(point.x),\n    y: Math.floor(point.y)\n  };\n}\n\n/**\n * We want to make sure the honey pot sits around the users position.\n * This seemed to be the most resilient while testing.\n */\nfunction pullBackByHalfHoneyPotSize(point) {\n  return {\n    x: point.x - halfHoneyPotSize,\n    y: point.y - halfHoneyPotSize\n  };\n}\n\n/**\n * Prevent the honey pot from changing the window size.\n * This is super unlikely to occur, but just being safe.\n */\nfunction preventGoingBackwardsOffScreen(point) {\n  return {\n    x: Math.max(point.x, 0),\n    y: Math.max(point.y, 0)\n  };\n}\n\n/**\n * Prevent the honey pot from changing the window size.\n * This is super unlikely to occur, but just being safe.\n */\nfunction preventGoingForwardsOffScreen(point) {\n  return {\n    x: Math.min(point.x, window.innerWidth - honeyPotSize),\n    y: Math.min(point.y, window.innerHeight - honeyPotSize)\n  };\n}\n\n/**\n * Create a `2x2` `DOMRect` around the `client` position\n */\nfunction getHoneyPotRectFor(_ref) {\n  var client = _ref.client;\n  var point = preventGoingForwardsOffScreen(preventGoingBackwardsOffScreen(pullBackByHalfHoneyPotSize(floorToClosestPixel(client))));\n\n  // When debugging, it is helpful to\n  // make this element a bit bigger\n  return DOMRect.fromRect({\n    x: point.x,\n    y: point.y,\n    width: honeyPotSize,\n    height: honeyPotSize\n  });\n}\nfunction getRectStyles(_ref2) {\n  var clientRect = _ref2.clientRect;\n  return {\n    left: \"\".concat(clientRect.left, \"px\"),\n    top: \"\".concat(clientRect.top, \"px\"),\n    width: \"\".concat(clientRect.width, \"px\"),\n    height: \"\".concat(clientRect.height, \"px\")\n  };\n}\nfunction isWithin(_ref3) {\n  var client = _ref3.client,\n    clientRect = _ref3.clientRect;\n  return (\n    // is within horizontal bounds\n    client.x >= clientRect.x && client.x <= clientRect.x + clientRect.width &&\n    // is within vertical bounds\n    client.y >= clientRect.y && client.y <= clientRect.y + clientRect.height\n  );\n}\n/**\n * The honey pot fix is designed to get around a painful bug in all browsers.\n *\n * [Overview](https://www.youtube.com/watch?v=udE9qbFTeQg)\n *\n * **Background**\n *\n * When a drag starts, browsers incorrectly think that the users pointer is\n * still depressed where the drag started. Any element that goes under this position\n * will be entered into, causing `\"mouseenter\"` events and `\":hover\"` styles to be applied.\n *\n * _This is a violation of the spec_\n *\n * > \"From the moment that the user agent is to initiate the drag-and-drop operation,\n * > until the end \tof the drag-and-drop operation, device input events\n * > (e.g. mouse and keyboard events) must be suppressed.\"\n * >\n * > - https://html.spec.whatwg.org/multipage/dnd.html#drag-and-drop-processing-model\n *\n * _Some impacts_\n *\n * - `\":hover\"` styles being applied where they shouldn't (looks messy)\n * - components such as tooltips responding to `\"mouseenter\"` can show during a drag,\n *   and on an element the user isn't even over\n *\n * Bug: https://issues.chromium.org/issues/41129937\n *\n * **Honey pot fix**\n *\n * 1. Create an element where the browser thinks the depressed pointer is\n *    to absorb the incorrect pointer events\n * 2. Remove that element when it is no longer needed\n */\nfunction mountHoneyPot(_ref4) {\n  var initial = _ref4.initial;\n  var element = document.createElement('div');\n  element.setAttribute(honeyPotDataAttribute, 'true');\n\n  // can shift during the drag thanks to Firefox\n  var clientRect = getHoneyPotRectFor({\n    client: initial\n  });\n  Object.assign(element.style, _objectSpread(_objectSpread({\n    // Setting a background color explicitly to avoid any inherited styles.\n    // Looks like this could be `opacity: 0`, but worried that _might_\n    // cause the element to be ignored on some platforms.\n    // When debugging, set backgroundColor to something like \"red\".\n    backgroundColor: 'transparent',\n    position: 'fixed',\n    // Being explicit to avoid inheriting styles\n    padding: 0,\n    margin: 0,\n    boxSizing: 'border-box'\n  }, getRectStyles({\n    clientRect: clientRect\n  })), {}, {\n    // We want this element to absorb pointer events,\n    // it's kind of the whole point 😉\n    pointerEvents: 'auto',\n    // Want to make sure the honey pot is top of everything else.\n    // Don't need to worry about native drag previews, as they will\n    // have been rendered (and removed) before the honey pot is rendered\n    zIndex: maxZIndex\n  }));\n  document.body.appendChild(element);\n\n  /**\n   *  🦊 In firefox we can get `\"pointermove\"` events after the drag\n   * has started, which is a spec violation.\n   * The final `\"pointermove\"` will reveal where the \"depressed\" position\n   * is for our honey pot fix.\n   */\n  var unbindPointerMove = bind(window, {\n    type: 'pointermove',\n    listener: function listener(event) {\n      var client = {\n        x: event.clientX,\n        y: event.clientY\n      };\n      clientRect = getHoneyPotRectFor({\n        client: client\n      });\n      Object.assign(element.style, getRectStyles({\n        clientRect: clientRect\n      }));\n    },\n    // using capture so we are less likely to be impacted by event stopping\n    options: {\n      capture: true\n    }\n  });\n  return function finish(_ref5) {\n    var current = _ref5.current;\n    // Don't need this any more\n    unbindPointerMove();\n\n    // If the user is hover the honey pot, we remove it\n    // so that the user can continue to interact with the page normally.\n    if (isWithin({\n      client: current,\n      clientRect: clientRect\n    })) {\n      element.remove();\n      return;\n    }\n    function cleanup() {\n      unbindPostDragEvents();\n      element.remove();\n    }\n    var unbindPostDragEvents = bindAll(window, [{\n      type: 'pointerdown',\n      listener: cleanup\n    }, {\n      type: 'pointermove',\n      listener: cleanup\n    }, {\n      type: 'focusin',\n      listener: cleanup\n    }, {\n      type: 'focusout',\n      listener: cleanup\n    },\n    // a 'pointerdown' should happen before 'dragstart', but just being super safe\n    {\n      type: 'dragstart',\n      listener: cleanup\n    },\n    // if the user has dragged something out of the window\n    // and then is dragging something back into the window\n    // the first events we will see are \"dragenter\" (and then \"dragover\").\n    // So if we see any of these we need to clear the post drag fix.\n    {\n      type: 'dragenter',\n      listener: cleanup\n    }, {\n      type: 'dragover',\n      listener: cleanup\n    }\n\n    // Not adding a \"wheel\" event listener, as \"wheel\" by itself does not\n    // resolve the bug.\n    ], {\n      // Using `capture` so less likely to be impacted by other code stopping events\n      capture: true\n    });\n  };\n}\nexport function makeHoneyPotFix() {\n  var latestPointerMove = null;\n  function bindEvents() {\n    // For sanity, only collecting this value from when events are first bound.\n    // This prevents the case where a super old \"pointermove\" could be used\n    // from a prior interaction.\n    latestPointerMove = null;\n    return bind(window, {\n      type: 'pointermove',\n      listener: function listener(event) {\n        latestPointerMove = {\n          x: event.clientX,\n          y: event.clientY\n        };\n      },\n      // listening for pointer move in capture phase\n      // so we are less likely to be impacted by events being stopped.\n      options: {\n        capture: true\n      }\n    });\n  }\n  function getOnPostDispatch() {\n    var finish = null;\n    return function onPostEvent(_ref6) {\n      var eventName = _ref6.eventName,\n        payload = _ref6.payload;\n      // We are adding the honey pot `onDragStart` so we don't\n      // impact the creation of the native drag preview.\n      if (eventName === 'onDragStart') {\n        var input = payload.location.initial.input;\n\n        // Sometimes there will be no latest \"pointermove\" (eg iOS).\n        // In which case, we use the start position of the drag.\n        var initial = latestPointerMove !== null && latestPointerMove !== void 0 ? latestPointerMove : {\n          x: input.clientX,\n          y: input.clientY\n        };\n\n        // Don't need to defensively call `finish()` as `onDrop` from\n        // one interaction is guaranteed to be called before `onDragStart`\n        // of the next.\n        finish = mountHoneyPot({\n          initial: initial\n        });\n      }\n      if (eventName === 'onDrop') {\n        var _finish;\n        var _input = payload.location.current.input;\n        (_finish = finish) === null || _finish === void 0 || _finish({\n          current: {\n            x: _input.clientX,\n            y: _input.clientY\n          }\n        });\n        finish = null;\n        // this interaction is finished, we want to use\n        // the latest \"pointermove\" for each interaction\n        latestPointerMove = null;\n      }\n    };\n  }\n  return {\n    bindEvents: bindEvents,\n    getOnPostDispatch: getOnPostDispatch\n  };\n}", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };", "function _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nexport { _iterableToArray as default };", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };", "import arrayWithoutHoles from \"./arrayWithoutHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableSpread from \"./nonIterableSpread.js\";\nfunction _toConsumableArray(r) {\n  return arrayWithoutHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableSpread();\n}\nexport { _toConsumableArray as default };", "/** Provide a function that you only ever want to be called a single time */\nexport function once(fn) {\n  var cache = null;\n  return function wrapped() {\n    if (!cache) {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var result = fn.apply(this, args);\n      cache = {\n        result: result\n      };\n    }\n    return cache.result;\n  };\n}", "import { once } from '../public-utils/once';\n\n// using `cache` as our `isFirefox()` result will not change in a browser\n\n/**\n * Returns `true` if a `Firefox` browser\n * */\nexport var isFirefox = once(function isFirefox() {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n  return navigator.userAgent.includes('Firefox');\n});", "import { once } from '../public-utils/once';\n\n// using `cache` as our `isSafari()` result will not change in a browser\n\n/**\n * Returns `true` if a `Safari` browser.\n * Returns `true` if the browser is running on iOS (they are all Safari).\n *\n * Use `isSafariOnIOS` if you want to check if something is Safari + iOS\n * */\nexport var isSafari = once(function isSafari() {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n  var _navigator = navigator,\n    userAgent = _navigator.userAgent;\n  return userAgent.includes('AppleWebKit') && !userAgent.includes('Chrome');\n});", "import { bindAll } from 'bind-event-listener';\nimport { isSafari } from '../is-safari';\n\n/* For \"dragenter\" events, the browser should set `relatedTarget` to the previous element.\n * For external drag operations, our first \"dragenter\" event should have a `event.relatedTarget` of `null`.\n *\n *  Unfortunately in Safari `event.relatedTarget` is *always* set to `null`\n *  Safari bug: https://bugs.webkit.org/show_bug.cgi?id=242627\n *  To work around this we count \"dragenter\" and \"dragleave\" events */\n\n// Using symbols for event properties so we don't clash with\n// anything on the `event` object\nvar symbols = {\n  isLeavingWindow: Symbol('leaving'),\n  isEnteringWindow: Symbol('entering')\n};\nexport function isEnteringWindowInSafari(_ref) {\n  var dragEnter = _ref.dragEnter;\n  if (!isSafari()) {\n    return false;\n  }\n  return dragEnter.hasOwnProperty(symbols.isEnteringWindow);\n}\nexport function isLeavingWindowInSafari(_ref2) {\n  var dragLeave = _ref2.dragLeave;\n  if (!isSafari()) {\n    return false;\n  }\n  return dragLeave.hasOwnProperty(symbols.isLeavingWindow);\n}\n(function fixSafari() {\n  // Don't do anything when server side rendering\n  if (typeof window === 'undefined') {\n    return;\n  }\n\n  // rather than checking the userAgent for \"jsdom\" we can do this check\n  // so that the check will be removed completely in production code\n  if (process.env.NODE_ENV === 'test') {\n    return;\n  }\n  if (!isSafari()) {\n    return;\n  }\n  function getInitialState() {\n    return {\n      enterCount: 0,\n      isOverWindow: false\n    };\n  }\n  var state = getInitialState();\n  function resetState() {\n    state = getInitialState();\n  }\n\n  // These event listeners are bound _forever_ and _never_ removed\n  // We don't bother cleaning up these event listeners (for now)\n  // as this workaround is only for Safari\n\n  // This is how the event count works:\n  //\n  // lift (+1 enterCount)\n  // - dragstart(draggable) [enterCount: 0]\n  // - dragenter(draggable) [enterCount: 1]\n  // leaving draggable (+0 enterCount)\n  // - dragenter(document.body) [enterCount: 2]\n  // - dragleave(draggable) [enterCount: 1]\n  // leaving window (-1 enterCount)\n  // - dragleave(document.body) [enterCount: 0] {leaving the window}\n\n  // Things to note:\n  // - dragenter and dragleave bubble\n  // - the first dragenter when entering a window might not be on `window`\n  //   - it could be on an element that is pressed up against the window\n  //   - (so we cannot rely on `event.target` values)\n\n  bindAll(window, [{\n    type: 'dragstart',\n    listener: function listener() {\n      state.enterCount = 0;\n      // drag start occurs in the source window\n      state.isOverWindow = true;\n      // When a drag first starts it will also trigger a \"dragenter\" on the draggable element\n    }\n  }, {\n    type: 'drop',\n    listener: resetState\n  }, {\n    type: 'dragend',\n    listener: resetState\n  }, {\n    type: 'dragenter',\n    listener: function listener(event) {\n      if (!state.isOverWindow && state.enterCount === 0) {\n        // Patching the `event` object\n        // The `event` object is shared with all event listeners for the event\n        // @ts-expect-error: adding property to the event object\n        event[symbols.isEnteringWindow] = true;\n      }\n      state.isOverWindow = true;\n      state.enterCount++;\n    }\n  }, {\n    type: 'dragleave',\n    listener: function listener(event) {\n      state.enterCount--;\n      if (state.isOverWindow && state.enterCount === 0) {\n        // Patching the `event` object as it is shared with all event listeners\n        // The `event` object is shared with all event listeners for the event\n        // @ts-expect-error: adding property to the event object\n        event[symbols.isLeavingWindow] = true;\n        state.isOverWindow = false;\n      }\n    }\n  }],\n  // using `capture: true` so that adding event listeners\n  // in bubble phase will have the correct symbols\n  {\n    capture: true\n  });\n})();", "/**\n * Does the `EventTarget` look like a `Node` based on \"duck typing\".\n *\n * Helpful when the `Node` might be outside of the current document\n * so we cannot to an `target instanceof Node` check.\n */\nfunction isNodeLike(target) {\n  return 'nodeName' in target;\n}\n\n/**\n * Is an `EventTarget` a `Node` from another `window`?\n */\nexport function isFromAnotherWindow(eventTarget) {\n  return isNodeLike(eventTarget) && eventTarget.ownerDocument !== document;\n}", "import { isFirefox } from '../is-firefox';\nimport { isSafari } from '../is-safari';\nimport { isLeavingWindowInSafari } from './count-events-for-safari';\nimport { isFromAnotherWindow } from './is-from-another-window';\nexport function isLeavingWindow(_ref) {\n  var dragLeave = _ref.dragLeave;\n  var type = dragLeave.type,\n    relatedTarget = dragLeave.relatedTarget;\n  if (type !== 'dragleave') {\n    return false;\n  }\n  if (isSafari()) {\n    return isLeavingWindowInSafari({\n      dragLeave: dragLeave\n    });\n  }\n\n  // Standard check: if going to `null` we are leaving the `window`\n  if (relatedTarget == null) {\n    return true;\n  }\n\n  /**\n   * 🦊 Exception: `iframe` in Firefox (`125.0`)\n   *\n   * Case 1: parent `window` → child `iframe`\n   * `dragLeave.relatedTarget` is element _inside_ the child `iframe`\n   * (foreign element)\n   *\n   * Case 2: child `iframe` → parent `window`\n   * `dragLeave.relatedTarget` is the `iframe` in the parent `window`\n   * (foreign element)\n   */\n\n  if (isFirefox()) {\n    return isFromAnotherWindow(relatedTarget);\n  }\n\n  /**\n   * 🌏 Exception: `iframe` in Chrome (`124.0`)\n   *\n   * Case 1: parent `window` → child `iframe`\n   * `dragLeave.relatedTarget` is the `iframe` in the parent `window`\n   *\n   * Case 2: child `iframe` → parent `window`\n   * `dragLeave.relatedTarget` is `null` *(standard check)*\n   */\n\n  // Case 2\n  // Using `instanceof` check as the element will be in the same `window`\n  return relatedTarget instanceof HTMLIFrameElement;\n}", "export function getBindingsForBrokenDrags(_ref) {\n  var onDragEnd = _ref.onDragEnd;\n  return [\n  // ## Detecting drag ending for removed draggables\n  //\n  // If a draggable element is removed during a drag and the user drops:\n  // 1. if over a valid drop target: we get a \"drop\" event to know the drag is finished\n  // 2. if not over a valid drop target (or cancelled): we get nothing\n  // The \"dragend\" event will not fire on the source draggable if it has been\n  // removed from the DOM.\n  // So we need to figure out if a drag operation has finished by looking at other events\n  // We can do this by looking at other events\n\n  // ### First detection: \"pointermove\" events\n\n  // 1. \"pointermove\" events cannot fire during a drag and drop operation\n  // according to the spec. So if we get a \"pointermove\" it means that\n  // the drag and drop operations has finished. So if we get a \"pointermove\"\n  // we know that the drag is over\n  // 2. 🦊😤 Drag and drop operations are _supposed_ to suppress\n  // other pointer events. However, firefox will allow a few\n  // pointer event to get through after a drag starts.\n  // The most I've seen is 3\n  {\n    type: 'pointermove',\n    listener: function () {\n      var callCount = 0;\n      return function listener() {\n        // Using 20 as it is far bigger than the most observed (3)\n        if (callCount < 20) {\n          callCount++;\n          return;\n        }\n        onDragEnd();\n      };\n    }()\n  },\n  // ### Second detection: \"pointerdown\" events\n\n  // If we receive this event then we know that a drag operation has finished\n  // and potentially another one is about to start.\n  // Note: `pointerdown` fires on all browsers / platforms before \"dragstart\"\n  {\n    type: 'pointerdown',\n    listener: onDragEnd\n  }];\n}", "export function getInput(event) {\n  return {\n    altKey: event.altKey,\n    button: event.button,\n    buttons: event.buttons,\n    ctrlKey: event.ctrlKey,\n    metaKey: event.metaKey,\n    shiftKey: event.shiftKey,\n    clientX: event.clientX,\n    clientY: event.clientY,\n    pageX: event.pageX,\n    pageY: event.pageY\n  };\n}", "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n", "import rafSchd from 'raf-schd';\nvar scheduleOnDrag = rafSchd(function (fn) {\n  return fn();\n});\nvar dragStart = function () {\n  var scheduled = null;\n  function schedule(fn) {\n    var frameId = requestAnimationFrame(function () {\n      scheduled = null;\n      fn();\n    });\n    scheduled = {\n      frameId: frameId,\n      fn: fn\n    };\n  }\n  function flush() {\n    if (scheduled) {\n      cancelAnimationFrame(scheduled.frameId);\n      scheduled.fn();\n      scheduled = null;\n    }\n  }\n  return {\n    schedule: schedule,\n    flush: flush\n  };\n}();\nexport function makeDispatch(_ref) {\n  var source = _ref.source,\n    initial = _ref.initial,\n    dispatchEvent = _ref.dispatchEvent;\n  var previous = {\n    dropTargets: []\n  };\n  function safeDispatch(args) {\n    dispatchEvent(args);\n    previous = {\n      dropTargets: args.payload.location.current.dropTargets\n    };\n  }\n  var dispatch = {\n    start: function start(_ref2) {\n      var nativeSetDragImage = _ref2.nativeSetDragImage;\n      // Ensuring that both `onGenerateDragPreview` and `onDragStart` get the same location.\n      // We do this so that `previous` is`[]` in `onDragStart` (which is logical)\n      var location = {\n        current: initial,\n        previous: previous,\n        initial: initial\n      };\n      // a `onGenerateDragPreview` does _not_ add another entry for `previous`\n      // onDragPreview\n      safeDispatch({\n        eventName: 'onGenerateDragPreview',\n        payload: {\n          source: source,\n          location: location,\n          nativeSetDragImage: nativeSetDragImage\n        }\n      });\n      dragStart.schedule(function () {\n        safeDispatch({\n          eventName: 'onDragStart',\n          payload: {\n            source: source,\n            location: location\n          }\n        });\n      });\n    },\n    dragUpdate: function dragUpdate(_ref3) {\n      var current = _ref3.current;\n      dragStart.flush();\n      scheduleOnDrag.cancel();\n      safeDispatch({\n        eventName: 'onDropTargetChange',\n        payload: {\n          source: source,\n          location: {\n            initial: initial,\n            previous: previous,\n            current: current\n          }\n        }\n      });\n    },\n    drag: function drag(_ref4) {\n      var current = _ref4.current;\n      scheduleOnDrag(function () {\n        dragStart.flush();\n        var location = {\n          initial: initial,\n          previous: previous,\n          current: current\n        };\n        safeDispatch({\n          eventName: 'onDrag',\n          payload: {\n            source: source,\n            location: location\n          }\n        });\n      });\n    },\n    drop: function drop(_ref5) {\n      var current = _ref5.current,\n        updatedSourcePayload = _ref5.updatedSourcePayload;\n      dragStart.flush();\n      scheduleOnDrag.cancel();\n      safeDispatch({\n        eventName: 'onDrop',\n        payload: {\n          source: updatedSourcePayload !== null && updatedSourcePayload !== void 0 ? updatedSourcePayload : source,\n          location: {\n            current: current,\n            previous: previous,\n            initial: initial\n          }\n        }\n      });\n    }\n  };\n  return dispatch;\n}", "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport { bindAll } from 'bind-event-listener';\nimport { getElementFromPointWithoutHoneypot } from '../honey-pot-fix/get-element-from-point-without-honey-pot';\nimport { isHoneyPotElement } from '../honey-pot-fix/is-honey-pot-element';\nimport { isLeavingWindow } from '../util/changing-window/is-leaving-window';\nimport { getBindingsForBrokenDrags } from '../util/detect-broken-drag';\nimport { getInput } from '../util/get-input';\nimport { makeDispatch } from './dispatch-consumer-event';\nvar globalState = {\n  isActive: false\n};\nfunction canStart() {\n  return !globalState.isActive;\n}\nfunction getNativeSetDragImage(event) {\n  if (event.dataTransfer) {\n    // need to use `.bind` as `setDragImage` is required\n    // to be run with `event.dataTransfer` as the \"this\" context\n    return event.dataTransfer.setDragImage.bind(event.dataTransfer);\n  }\n  return null;\n}\nfunction hasHierarchyChanged(_ref) {\n  var current = _ref.current,\n    next = _ref.next;\n  if (current.length !== next.length) {\n    return true;\n  }\n  // not checking stickiness, data or dropEffect,\n  // just whether the hierarchy has changed\n  for (var i = 0; i < current.length; i++) {\n    if (current[i].element !== next[i].element) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction start(_ref2) {\n  var event = _ref2.event,\n    dragType = _ref2.dragType,\n    getDropTargetsOver = _ref2.getDropTargetsOver,\n    dispatchEvent = _ref2.dispatchEvent;\n  if (!canStart()) {\n    return;\n  }\n  var initial = getStartLocation({\n    event: event,\n    dragType: dragType,\n    getDropTargetsOver: getDropTargetsOver\n  });\n  globalState.isActive = true;\n  var state = {\n    current: initial\n  };\n\n  // Setting initial drop effect for the drag\n  setDropEffectOnEvent({\n    event: event,\n    current: initial.dropTargets\n  });\n  var dispatch = makeDispatch({\n    source: dragType.payload,\n    dispatchEvent: dispatchEvent,\n    initial: initial\n  });\n  function updateState(next) {\n    // only looking at whether hierarchy has changed to determine whether something as 'changed'\n    var hasChanged = hasHierarchyChanged({\n      current: state.current.dropTargets,\n      next: next.dropTargets\n    });\n\n    // Always updating the state to include latest data, dropEffect and stickiness\n    // Only updating consumers if the hierarchy has changed in some way\n    // Consumers can get the latest data by using `onDrag`\n    state.current = next;\n    if (hasChanged) {\n      dispatch.dragUpdate({\n        current: state.current\n      });\n    }\n  }\n  function onUpdateEvent(event) {\n    var input = getInput(event);\n\n    // If we are over the honey pot, we need to get the element\n    // that the user would have been over if not for the honey pot\n    var target = isHoneyPotElement(event.target) ? getElementFromPointWithoutHoneypot({\n      x: input.clientX,\n      y: input.clientY\n    }) : event.target;\n    var nextDropTargets = getDropTargetsOver({\n      target: target,\n      input: input,\n      source: dragType.payload,\n      current: state.current.dropTargets\n    });\n    if (nextDropTargets.length) {\n      // 🩸 must call `event.preventDefault()` to allow a browser drop to occur\n      event.preventDefault();\n      setDropEffectOnEvent({\n        event: event,\n        current: nextDropTargets\n      });\n    }\n    updateState({\n      dropTargets: nextDropTargets,\n      input: input\n    });\n  }\n  function cancel() {\n    // The spec behaviour is that when a drag is cancelled, or when dropping on no drop targets,\n    // a \"dragleave\" event is fired on the active drop target before a \"dragend\" event.\n    // We are replicating that behaviour in `cancel` if there are any active drop targets to\n    // ensure consistent behaviour.\n    //\n    // Note: When cancelling, or dropping on no drop targets, a \"dragleave\" event\n    // will have already cleared the dropTargets to `[]` (as that particular \"dragleave\" has a `relatedTarget` of `null`)\n\n    if (state.current.dropTargets.length) {\n      updateState({\n        dropTargets: [],\n        input: state.current.input\n      });\n    }\n    dispatch.drop({\n      current: state.current,\n      updatedSourcePayload: null\n    });\n    finish();\n  }\n  function finish() {\n    globalState.isActive = false;\n    unbindEvents();\n  }\n  var unbindEvents = bindAll(window, [{\n    // 👋 Note: we are repurposing the `dragover` event as our `drag` event\n    // this is because firefox does not publish pointer coordinates during\n    // a `drag` event, but does for every other type of drag event\n    // `dragover` fires on all elements that are being dragged over\n    // Because we are binding to `window` - our `dragover` is effectively the same as a `drag`\n    // 🦊😤\n    type: 'dragover',\n    listener: function listener(event) {\n      // We need to regularly calculate the drop targets in order to allow:\n      //  - dynamic `canDrop()` checks\n      //  - rapid updating `getData()` calls to attach data in response to user input (eg for edge detection)\n      // Sadly we cannot schedule inspecting changes resulting from this event\n      // we need to be able to conditionally cancel the event with `event.preventDefault()`\n      // to enable the correct native drop experience.\n\n      // 1. check to see if anything has changed\n      onUpdateEvent(event);\n\n      // 2. let consumers know a move has occurred\n      // This will include the latest 'input' values\n      dispatch.drag({\n        current: state.current\n      });\n    }\n  }, {\n    type: 'dragenter',\n    listener: onUpdateEvent\n  }, {\n    type: 'dragleave',\n    listener: function listener(event) {\n      if (!isLeavingWindow({\n        dragLeave: event\n      })) {\n        return;\n      }\n\n      /**\n       * At this point we don't know if a drag is being cancelled,\n       * or if a drag is leaving the `window`.\n       *\n       * Both have:\n       *   1. \"dragleave\" (with `relatedTarget: null`)\n       *   2. \"dragend\" (a \"dragend\" can occur when outside the `window`)\n       *\n       * **Clearing drop targets**\n       *\n       * For either case we are clearing the the drop targets\n       *\n       * - cancelling: we clear drop targets in `\"dragend\"` anyway\n       * - leaving the `window`: we clear the drop targets (to clear stickiness)\n       *\n       * **Leaving the window and finishing the drag**\n       *\n       * _internal drags_\n       *\n       * - The drag continues when the user is outside the `window`\n       *   and can resume if the user drags back over the `window`,\n       *   or end when the user drops in an external `window`.\n       * - We will get a `\"dragend\"`, or we can listen for other\n       *   events to determine the drag is finished when the user re-enters the `window`).\n       *\n       * _external drags_\n       *\n       * - We conclude the drag operation.\n       * - We have no idea if the user will drag back over the `window`,\n       *   or if the drag ends elsewhere.\n       * - We will create a new drag if the user re-enters the `window`.\n       *\n       * **Not updating `input`**\n       *\n       * 🐛 Bug[Chrome] the final `\"dragleave\"` has default input values (eg `clientX == 0`)\n       * Workaround: intentionally not updating `input` in \"dragleave\"\n       * rather than the users current input values\n       * - [Conversation](https://twitter.com/alexandereardon/status/1642697633864241152)\n       * - [Bug](https://bugs.chromium.org/p/chromium/issues/detail?id=1429937)\n       **/\n\n      updateState({\n        input: state.current.input,\n        dropTargets: []\n      });\n      if (dragType.startedFrom === 'external') {\n        cancel();\n      }\n    }\n  }, {\n    // A \"drop\" can only happen if the browser allowed the drop\n    type: 'drop',\n    listener: function listener(event) {\n      // Capture the final input.\n      // We are capturing the final `input` for the\n      // most accurate honey pot experience\n      state.current = {\n        dropTargets: state.current.dropTargets,\n        input: getInput(event)\n      };\n\n      /** If there are no drop targets, then we will get\n       * a \"drop\" event if:\n       * - `preventUnhandled()` is being used\n       * - there is an unmanaged drop target (eg another library)\n       * In these cases, it's up to the consumer\n       * to handle the drop if it's not over one of our drop targets\n       * - `preventUnhandled()` will cancel the \"drop\"\n       * - unmanaged drop targets can handle the \"drop\" how they want to\n       * We won't call `event.preventDefault()` in this call path */\n\n      if (!state.current.dropTargets.length) {\n        cancel();\n        return;\n      }\n      event.preventDefault();\n\n      // applying the latest drop effect to the event\n      setDropEffectOnEvent({\n        event: event,\n        current: state.current.dropTargets\n      });\n      dispatch.drop({\n        current: state.current,\n        // When dropping something native, we need to extract the latest\n        // `.items` from the \"drop\" event as it is now accessible\n        updatedSourcePayload: dragType.type === 'external' ? dragType.getDropPayload(event) : null\n      });\n      finish();\n    }\n  }, {\n    // \"dragend\" fires when on the drag source (eg a draggable element)\n    // when the drag is finished.\n    // \"dragend\" will fire after \"drop\" (if there was a successful drop)\n    // \"dragend\" does not fire if the draggable source has been removed during the drag\n    // or for external drag sources (eg files)\n\n    // This \"dragend\" listener will not fire if there was a successful drop\n    // as we will have already removed the event listener\n\n    type: 'dragend',\n    listener: function listener(event) {\n      // In firefox, the position of the \"dragend\" event can\n      // be a bit different to the last \"dragover\" event.\n      // Updating the input so we can get the best possible\n      // information for the honey pot.\n      state.current = {\n        dropTargets: state.current.dropTargets,\n        input: getInput(event)\n      };\n      cancel();\n    }\n  }].concat(_toConsumableArray(getBindingsForBrokenDrags({\n    onDragEnd: cancel\n  }))),\n  // Once we have started a managed drag operation it is important that we see / own all drag events\n  // We got one adoption bug pop up where some code was stopping (`event.stopPropagation()`)\n  // all \"drop\" events in the bubble phase on the `document.body`.\n  // This meant that we never saw the \"drop\" event.\n  {\n    capture: true\n  });\n  dispatch.start({\n    nativeSetDragImage: getNativeSetDragImage(event)\n  });\n}\nfunction setDropEffectOnEvent(_ref3) {\n  var _current$;\n  var event = _ref3.event,\n    current = _ref3.current;\n  // setting the `dropEffect` to be the innerMost drop targets dropEffect\n  var innerMost = (_current$ = current[0]) === null || _current$ === void 0 ? void 0 : _current$.dropEffect;\n  if (innerMost != null && event.dataTransfer) {\n    event.dataTransfer.dropEffect = innerMost;\n  }\n}\nfunction getStartLocation(_ref4) {\n  var event = _ref4.event,\n    dragType = _ref4.dragType,\n    getDropTargetsOver = _ref4.getDropTargetsOver;\n  var input = getInput(event);\n\n  // When dragging from outside of the browser,\n  // the drag is not being sourced from any local drop targets\n  if (dragType.startedFrom === 'external') {\n    return {\n      input: input,\n      dropTargets: []\n    };\n  }\n  var dropTargets = getDropTargetsOver({\n    input: input,\n    source: dragType.payload,\n    target: event.target,\n    current: []\n  });\n  return {\n    input: input,\n    dropTargets: dropTargets\n  };\n}\nexport var lifecycle = {\n  canStart: canStart,\n  start: start\n};", "// Extending `Map` to allow us to link Key and Values together\n\nvar ledger = new Map();\nfunction registerUsage(_ref) {\n  var typeKey = _ref.typeKey,\n    mount = _ref.mount;\n  var entry = ledger.get(typeKey);\n  if (entry) {\n    entry.usageCount++;\n    return entry;\n  }\n  var initial = {\n    typeKey: typeKey,\n    unmount: mount(),\n    usageCount: 1\n  };\n  ledger.set(typeKey, initial);\n  return initial;\n}\nexport function register(args) {\n  var entry = registerUsage(args);\n  return function unregister() {\n    entry.usageCount--;\n    if (entry.usageCount > 0) {\n      return;\n    }\n    // Only a single usage left, remove it\n    entry.unmount();\n    ledger.delete(args.typeKey);\n  };\n}", "export function addAttribute(element, _ref) {\n  var attribute = _ref.attribute,\n    value = _ref.value;\n  element.setAttribute(attribute, value);\n  return function () {\n    return element.removeAttribute(attribute);\n  };\n}", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { combine } from '../public-utils/combine';\nimport { once } from '../public-utils/once';\nimport { addAttribute } from '../util/add-attribute';\nfunction copyReverse(array) {\n  return array.slice(0).reverse();\n}\nexport function makeDropTarget(_ref) {\n  var typeKey = _ref.typeKey,\n    defaultDropEffect = _ref.defaultDropEffect;\n  var registry = new WeakMap();\n  var dropTargetDataAtt = \"data-drop-target-for-\".concat(typeKey);\n  var dropTargetSelector = \"[\".concat(dropTargetDataAtt, \"]\");\n  function addToRegistry(args) {\n    registry.set(args.element, args);\n    return function () {\n      return registry.delete(args.element);\n    };\n  }\n  function dropTargetForConsumers(args) {\n    // Guardrail: warn if the draggable element is already registered\n    if (process.env.NODE_ENV !== 'production') {\n      var existing = registry.get(args.element);\n      if (existing) {\n        // eslint-disable-next-line no-console\n        console.warn(\"You have already registered a [\".concat(typeKey, \"] dropTarget on the same element\"), {\n          existing: existing,\n          proposed: args\n        });\n      }\n      if (args.element instanceof HTMLIFrameElement) {\n        // eslint-disable-next-line no-console\n        console.warn(\"\\n            We recommend not registering <iframe> elements as drop targets\\n            as it can result in some strange browser event ordering.\\n          \" // Removing newlines and excessive whitespace\n        .replace(/\\s{2,}/g, ' ').trim());\n      }\n    }\n    var cleanup = combine(addAttribute(args.element, {\n      attribute: dropTargetDataAtt,\n      value: 'true'\n    }), addToRegistry(args));\n\n    // Wrapping in `once` to prevent unexpected side effects if consumers call\n    // the clean up function multiple times.\n    return once(cleanup);\n  }\n  function getActualDropTargets(_ref2) {\n    var _args$getData, _args$getData2, _args$getDropEffect, _args$getDropEffect2;\n    var source = _ref2.source,\n      target = _ref2.target,\n      input = _ref2.input,\n      _ref2$result = _ref2.result,\n      result = _ref2$result === void 0 ? [] : _ref2$result;\n    if (target == null) {\n      return result;\n    }\n    if (!(target instanceof Element)) {\n      // For \"text-selection\" drags, the original `target`\n      // is not an `Element`, so we need to start looking from\n      // the parent element\n      if (target instanceof Node) {\n        return getActualDropTargets({\n          source: source,\n          target: target.parentElement,\n          input: input,\n          result: result\n        });\n      }\n\n      // not sure what we are working with,\n      // so we can exit.\n      return result;\n    }\n    var closest = target.closest(dropTargetSelector);\n\n    // Cannot find anything else\n    if (closest == null) {\n      return result;\n    }\n    var args = registry.get(closest);\n\n    // error: something had a dropTargetSelector but we could not\n    // find a match. For now, failing silently\n    if (args == null) {\n      return result;\n    }\n    var feedback = {\n      input: input,\n      source: source,\n      element: args.element\n    };\n\n    // if dropping is not allowed, skip this drop target\n    // and continue looking up the DOM tree\n    if (args.canDrop && !args.canDrop(feedback)) {\n      return getActualDropTargets({\n        source: source,\n        target: args.element.parentElement,\n        input: input,\n        result: result\n      });\n    }\n\n    // calculate our new record\n    var data = (_args$getData = (_args$getData2 = args.getData) === null || _args$getData2 === void 0 ? void 0 : _args$getData2.call(args, feedback)) !== null && _args$getData !== void 0 ? _args$getData : {};\n    var dropEffect = (_args$getDropEffect = (_args$getDropEffect2 = args.getDropEffect) === null || _args$getDropEffect2 === void 0 ? void 0 : _args$getDropEffect2.call(args, feedback)) !== null && _args$getDropEffect !== void 0 ? _args$getDropEffect : defaultDropEffect;\n    var record = {\n      data: data,\n      element: args.element,\n      dropEffect: dropEffect,\n      // we are collecting _actual_ drop targets, so these are\n      // being applied _not_ due to stickiness\n      isActiveDueToStickiness: false\n    };\n    return getActualDropTargets({\n      source: source,\n      target: args.element.parentElement,\n      input: input,\n      // Using bubble ordering. Same ordering as `event.getPath()`\n      result: [].concat(_toConsumableArray(result), [record])\n    });\n  }\n  function notifyCurrent(_ref3) {\n    var eventName = _ref3.eventName,\n      payload = _ref3.payload;\n    var _iterator = _createForOfIteratorHelper(payload.location.current.dropTargets),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var _entry$eventName;\n        var record = _step.value;\n        var entry = registry.get(record.element);\n        var args = _objectSpread(_objectSpread({}, payload), {}, {\n          self: record\n        });\n        entry === null || entry === void 0 || (_entry$eventName = entry[eventName]) === null || _entry$eventName === void 0 || _entry$eventName.call(entry,\n        // I cannot seem to get the types right here.\n        // TS doesn't seem to like that one event can need `nativeSetDragImage`\n        // @ts-expect-error\n        args);\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n  }\n  var actions = {\n    onGenerateDragPreview: notifyCurrent,\n    onDrag: notifyCurrent,\n    onDragStart: notifyCurrent,\n    onDrop: notifyCurrent,\n    onDropTargetChange: function onDropTargetChange(_ref4) {\n      var payload = _ref4.payload;\n      var isCurrent = new Set(payload.location.current.dropTargets.map(function (record) {\n        return record.element;\n      }));\n      var visited = new Set();\n      var _iterator2 = _createForOfIteratorHelper(payload.location.previous.dropTargets),\n        _step2;\n      try {\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          var _entry$onDropTargetCh;\n          var record = _step2.value;\n          visited.add(record.element);\n          var entry = registry.get(record.element);\n          var isOver = isCurrent.has(record.element);\n          var args = _objectSpread(_objectSpread({}, payload), {}, {\n            self: record\n          });\n          entry === null || entry === void 0 || (_entry$onDropTargetCh = entry.onDropTargetChange) === null || _entry$onDropTargetCh === void 0 || _entry$onDropTargetCh.call(entry, args);\n\n          // if we cannot find the drop target in the current array, then it has been left\n          if (!isOver) {\n            var _entry$onDragLeave;\n            entry === null || entry === void 0 || (_entry$onDragLeave = entry.onDragLeave) === null || _entry$onDragLeave === void 0 || _entry$onDragLeave.call(entry, args);\n          }\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n      var _iterator3 = _createForOfIteratorHelper(payload.location.current.dropTargets),\n        _step3;\n      try {\n        for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n          var _entry$onDropTargetCh2, _entry$onDragEnter;\n          var _record = _step3.value;\n          // already published an update to this drop target\n          if (visited.has(_record.element)) {\n            continue;\n          }\n          // at this point we have a new drop target that is being entered into\n          var _args = _objectSpread(_objectSpread({}, payload), {}, {\n            self: _record\n          });\n          var _entry = registry.get(_record.element);\n          _entry === null || _entry === void 0 || (_entry$onDropTargetCh2 = _entry.onDropTargetChange) === null || _entry$onDropTargetCh2 === void 0 || _entry$onDropTargetCh2.call(_entry, _args);\n          _entry === null || _entry === void 0 || (_entry$onDragEnter = _entry.onDragEnter) === null || _entry$onDragEnter === void 0 || _entry$onDragEnter.call(_entry, _args);\n        }\n      } catch (err) {\n        _iterator3.e(err);\n      } finally {\n        _iterator3.f();\n      }\n    }\n  };\n  function dispatchEvent(args) {\n    actions[args.eventName](args);\n  }\n  function getIsOver(_ref5) {\n    var source = _ref5.source,\n      target = _ref5.target,\n      input = _ref5.input,\n      current = _ref5.current;\n    var actual = getActualDropTargets({\n      source: source,\n      target: target,\n      input: input\n    });\n\n    // stickiness is only relevant when we have less\n    // drop targets than we did before\n    if (actual.length >= current.length) {\n      return actual;\n    }\n\n    // less 'actual' drop targets than before,\n    // we need to see if 'stickiness' applies\n\n    // An old drop target will continue to be dropped on if:\n    // 1. it has the same parent\n    // 2. nothing exists in it's previous index\n\n    var lastCaptureOrdered = copyReverse(current);\n    var actualCaptureOrdered = copyReverse(actual);\n    var resultCaptureOrdered = [];\n    for (var index = 0; index < lastCaptureOrdered.length; index++) {\n      var _argsForLast$getIsSti;\n      var last = lastCaptureOrdered[index];\n      var fresh = actualCaptureOrdered[index];\n\n      // if a record is in the new index -> use that\n      // it will have the latest data + dropEffect\n      if (fresh != null) {\n        resultCaptureOrdered.push(fresh);\n        continue;\n      }\n\n      // At this point we have no drop target in the old spot\n      // Check to see if we can use a previous sticky drop target\n\n      // The \"parent\" is the one inside of `resultCaptureOrdered`\n      // (the parent might be a drop target that was sticky)\n      var parent = resultCaptureOrdered[index - 1];\n      var lastParent = lastCaptureOrdered[index - 1];\n\n      // Stickiness is based on parent relationships, so if the parent relationship has change\n      // then we can stop our search\n      if ((parent === null || parent === void 0 ? void 0 : parent.element) !== (lastParent === null || lastParent === void 0 ? void 0 : lastParent.element)) {\n        break;\n      }\n\n      // We need to check whether the old drop target can still be dropped on\n\n      var argsForLast = registry.get(last.element);\n\n      // We cannot drop on a drop target that is no longer mounted\n      if (!argsForLast) {\n        break;\n      }\n      var feedback = {\n        input: input,\n        source: source,\n        element: argsForLast.element\n      };\n\n      // We cannot drop on a drop target that no longer allows being dropped on\n      if (argsForLast.canDrop && !argsForLast.canDrop(feedback)) {\n        break;\n      }\n\n      // We cannot drop on a drop target that is no longer sticky\n      if (!((_argsForLast$getIsSti = argsForLast.getIsSticky) !== null && _argsForLast$getIsSti !== void 0 && _argsForLast$getIsSti.call(argsForLast, feedback))) {\n        break;\n      }\n\n      // Note: intentionally not recollecting `getData()` or `getDropEffect()`\n      // Previous values for `data` and `dropEffect` will be borrowed\n      // This is to prevent things like the 'closest edge' changing when\n      // no longer over a drop target.\n      // We could change our mind on this behaviour in the future\n\n      resultCaptureOrdered.push(_objectSpread(_objectSpread({}, last), {}, {\n        // making it clear to consumers this drop target is active due to stickiness\n        isActiveDueToStickiness: true\n      }));\n    }\n\n    // return bubble ordered result\n    return copyReverse(resultCaptureOrdered);\n  }\n  return {\n    dropTargetForConsumers: dropTargetForConsumers,\n    getIsOver: getIsOver,\n    dispatchEvent: dispatchEvent\n  };\n}", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { once } from '../public-utils/once';\nexport function makeMonitor() {\n  var registry = new Set();\n  var dragging = null;\n  function tryAddToActive(monitor) {\n    if (!dragging) {\n      return;\n    }\n    // Monitor is allowed to monitor events if:\n    // 1. It has no `canMonitor` function (default is that a monitor can listen to everything)\n    // 2. `canMonitor` returns true\n    if (!monitor.canMonitor || monitor.canMonitor(dragging.canMonitorArgs)) {\n      dragging.active.add(monitor);\n    }\n  }\n  function monitorForConsumers(args) {\n    // We are giving each `args` a new reference so that you\n    // can create multiple monitors with the same `args`.\n    var entry = _objectSpread({}, args);\n    registry.add(entry);\n\n    // if there is an active drag we need to see if this new monitor is relevant\n    tryAddToActive(entry);\n    function cleanup() {\n      registry.delete(entry);\n\n      // We need to stop publishing events during a drag to this monitor!\n      if (dragging) {\n        dragging.active.delete(entry);\n      }\n    }\n\n    // Wrapping in `once` to prevent unexpected side effects if consumers call\n    // the clean up function multiple times.\n    return once(cleanup);\n  }\n  function dispatchEvent(_ref) {\n    var eventName = _ref.eventName,\n      payload = _ref.payload;\n    if (eventName === 'onGenerateDragPreview') {\n      dragging = {\n        canMonitorArgs: {\n          initial: payload.location.initial,\n          source: payload.source\n        },\n        active: new Set()\n      };\n      var _iterator = _createForOfIteratorHelper(registry),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var monitor = _step.value;\n          tryAddToActive(monitor);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    }\n\n    // This should never happen.\n    if (!dragging) {\n      return;\n    }\n\n    // Creating an array from the set _before_ iterating\n    // This is so that monitors added during the current event will not be called.\n    // This behaviour matches native EventTargets where an event listener\n    // cannot add another event listener during an active event to the same\n    // event target in the same event (for us we have a single global event target)\n    var active = Array.from(dragging.active);\n    for (var _i = 0, _active = active; _i < _active.length; _i++) {\n      var _monitor = _active[_i];\n      // A monitor can be removed by another monitor during an event.\n      // We need to check that the monitor is still registered before calling it\n      if (dragging.active.has(_monitor)) {\n        var _monitor$eventName;\n        // @ts-expect-error: I cannot get this type working!\n        (_monitor$eventName = _monitor[eventName]) === null || _monitor$eventName === void 0 || _monitor$eventName.call(_monitor, payload);\n      }\n    }\n    if (eventName === 'onDrop') {\n      dragging.active.clear();\n      dragging = null;\n    }\n  }\n  return {\n    dispatchEvent: dispatchEvent,\n    monitorForConsumers: monitorForConsumers\n  };\n}", "import { lifecycle } from '../ledger/lifecycle-manager';\nimport { register } from '../ledger/usage-ledger';\nimport { makeDropTarget } from './make-drop-target';\nimport { makeMonitor } from './make-monitor';\nexport function makeAdapter(_ref) {\n  var typeKey = _ref.typeKey,\n    mount = _ref.mount,\n    dispatchEventToSource = _ref.dispatchEventToSource,\n    onPostDispatch = _ref.onPostDispatch,\n    defaultDropEffect = _ref.defaultDropEffect;\n  var monitorAPI = makeMonitor();\n  var dropTargetAPI = makeDropTarget({\n    typeKey: typeKey,\n    defaultDropEffect: defaultDropEffect\n  });\n  function dispatchEvent(args) {\n    // 1. forward the event to source\n    dispatchEventToSource === null || dispatchEventToSource === void 0 || dispatchEventToSource(args);\n\n    // 2. forward the event to relevant dropTargets\n    dropTargetAPI.dispatchEvent(args);\n\n    // 3. forward event to monitors\n    monitorAPI.dispatchEvent(args);\n\n    // 4. post consumer dispatch (used for honey pot fix)\n    onPostDispatch === null || onPostDispatch === void 0 || onPostDispatch(args);\n  }\n  function start(_ref2) {\n    var event = _ref2.event,\n      dragType = _ref2.dragType;\n    lifecycle.start({\n      event: event,\n      dragType: dragType,\n      getDropTargetsOver: dropTargetAPI.getIsOver,\n      dispatchEvent: dispatchEvent\n    });\n  }\n  function registerUsage() {\n    function mountAdapter() {\n      var api = {\n        canStart: lifecycle.canStart,\n        start: start\n      };\n      return mount(api);\n    }\n    return register({\n      typeKey: typeKey,\n      mount: mountAdapter\n    });\n  }\n  return {\n    registerUsage: registerUsage,\n    dropTarget: dropTargetAPI.dropTargetForConsumers,\n    monitor: monitorAPI.monitorForConsumers\n  };\n}", "import { once } from '../public-utils/once';\n\n// using `cache` as our `isAndroid()` result will not change in a browser\nexport var isAndroid = once(function isAndroid() {\n  return navigator.userAgent.toLocaleLowerCase().includes('android');\n});\nexport var androidFallbackText = 'pdnd:android-fallback';", "// Why we put the media types in their own files:\n//\n// - we are not putting them all in one file as not all adapters need all types\n// - we are not putting them in the external helpers as some things just need the\n//   types and not the external functions code\nexport var textMediaType = 'text/plain';", "// Why we put the media types in their own files:\n//\n// - we are not putting them all in one file as not all adapters need all types\n// - we are not putting them in the external helpers as some things just need the\n//   types and not the external functions code\nexport var URLMediaType = 'text/uri-list';", "/**\n * This key has been pulled into a separate module\n * so that the external adapter does not need to import\n * the element adapter\n */\nexport var elementAdapterNativeDataKey = 'application/vnd.pdnd';", "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { bind } from 'bind-event-listener';\nimport { getElementFromPointWithoutHoneypot } from '../honey-pot-fix/get-element-from-point-without-honey-pot';\nimport { makeHoneyPotFix } from '../honey-pot-fix/make-honey-pot-fix';\nimport { makeAdapter } from '../make-adapter/make-adapter';\nimport { combine } from '../public-utils/combine';\nimport { once } from '../public-utils/once';\nimport { addAttribute } from '../util/add-attribute';\nimport { androidFallbackText, isAndroid } from '../util/android';\nimport { getInput } from '../util/get-input';\nimport { textMediaType } from '../util/media-types/text-media-type';\nimport { URLMediaType } from '../util/media-types/url-media-type';\nimport { elementAdapterNativeDataKey } from './element-adapter-native-data-key';\nvar draggableRegistry = new WeakMap();\nfunction addToRegistry(args) {\n  draggableRegistry.set(args.element, args);\n  return function cleanup() {\n    draggableRegistry.delete(args.element);\n  };\n}\nvar honeyPotFix = makeHoneyPotFix();\nvar adapter = makeAdapter({\n  typeKey: 'element',\n  defaultDropEffect: 'move',\n  mount: function mount(api) {\n    /**  Binding event listeners the `document` rather than `window` so that\n     * this adapter always gets preference over the text adapter.\n     * `document` is the first `EventTarget` under `window`\n     * https://twitter.com/alexandereardon/status/1604658588311465985\n     */\n    return combine(honeyPotFix.bindEvents(), bind(document, {\n      type: 'dragstart',\n      listener: function listener(event) {\n        var _entry$dragHandle, _entry$getInitialData, _entry$getInitialData2, _entry$dragHandle2, _entry$getInitialData3, _entry$getInitialData4;\n        if (!api.canStart(event)) {\n          return;\n        }\n\n        // If the \"dragstart\" event is cancelled, then a drag won't start\n        // There will be no further drag operation events (eg no \"dragend\" event)\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        // Technically `dataTransfer` can be `null` according to the types\n        // But that behaviour does not seem to appear in the spec.\n        // If there is not `dataTransfer`, we can assume something is wrong and not\n        // start a drag\n        if (!event.dataTransfer) {\n          // Including this code on \"test\" and \"development\" environments:\n          // - Browser tests commonly run against \"development\" builds\n          // - Unit tests commonly run in \"test\"\n          if (process.env.NODE_ENV !== 'production') {\n            // eslint-disable-next-line no-console\n            console.warn(\"\\n              It appears as though you have are not testing DragEvents correctly.\\n\\n              - If you are unit testing, ensure you have polyfilled DragEvent.\\n              - If you are browser testing, ensure you are dispatching drag events correctly.\\n\\n              Please see our testing guides for more information:\\n              https://atlassian.design/components/pragmatic-drag-and-drop/core-package/testing\\n            \".replace(/ {2}/g, ''));\n          }\n          return;\n        }\n\n        // the closest parent that is a draggable element will be marked as\n        // the `event.target` for the event\n        var target = event.target;\n\n        // this source is only for elements\n        // Note: only HTMLElements can have the \"draggable\" attribute\n        if (!(target instanceof HTMLElement)) {\n          return null;\n        }\n\n        // see if the thing being dragged is owned by us\n        var entry = draggableRegistry.get(target);\n\n        // no matching element found\n        // → dragging an element with `draggable=\"true\"` that is not controlled by us\n        if (!entry) {\n          return null;\n        }\n\n        /**\n         * A text selection drag _can_ have the `draggable` element be\n         * the `event.target` if the user is dragging the text selection\n         * from the `draggable`.\n         *\n         * To know if the `draggable` is being dragged, we look at whether any\n         * `\"text/plain\"` data is being dragged. If it is, then a text selection\n         * drag is occurring.\n         *\n         * This behaviour has been validated on:\n         *\n         * - Chrome@128 on Android@14\n         * - Chrome@128 on iOS@17.6.1\n         * - Chrome@128 on Windows@11\n         * - Chrome@128 on MacOS@14.6.1\n         * - Firefox@129 on Windows@11 (not possible for user to select text in a draggable)\n         * - Firefox@129 on MacOS@14.6.1 (not possible for user to select text in a draggable)\n         *\n         * Note: Could usually just use: `event.dataTransfer.types.includes(textMediaType)`\n         * but unfortunately ProseMirror is always setting `\"\"` as the dragged text\n         *\n         * Note: Unfortunately editor is (heavily) leaning on the current functionality today\n         * and unwinding it will be a decent amount of effort. So for now, a text selection\n         * where the `event.target` is a `draggable` element will still trigger the\n         * element adapter.\n         *\n         * // Future state:\n         * if(event.dataTransfer.getData(textMediaType)) {\n         * \treturn;\n         * }\n         *\n         */\n\n        var input = getInput(event);\n        var feedback = {\n          element: entry.element,\n          dragHandle: (_entry$dragHandle = entry.dragHandle) !== null && _entry$dragHandle !== void 0 ? _entry$dragHandle : null,\n          input: input\n        };\n\n        // Check: does the draggable want to allow dragging?\n        if (entry.canDrag && !entry.canDrag(feedback)) {\n          // cancel drag operation if we cannot drag\n          event.preventDefault();\n          return null;\n        }\n\n        // Check: is there a drag handle and is the user using it?\n        if (entry.dragHandle) {\n          // technically don't need this util, but just being\n          // consistent with how we look up what is under the users\n          // cursor.\n          var over = getElementFromPointWithoutHoneypot({\n            x: input.clientX,\n            y: input.clientY\n          });\n\n          // if we are not dragging from the drag handle (or something inside the drag handle)\n          // then we will cancel the active drag\n          if (!entry.dragHandle.contains(over)) {\n            event.preventDefault();\n            return null;\n          }\n        }\n\n        /**\n         *  **Goal**\n         *  Pass information to other applications\n         *\n         * **Approach**\n         *  Put data into the native data store\n         *\n         *  **What about the native adapter?**\n         *  When the element adapter puts native data into the native data store\n         *  the native adapter is not triggered in the current window,\n         *  but a native adapter in an external window _can_ be triggered\n         *\n         *  **Why bake this into core?**\n         *  This functionality could be pulled out and exposed inside of\n         *  `onGenerateDragPreview`. But decided to make it a part of the\n         *  base API as it felt like a common enough use case and ended\n         *  up being a similar amount of code to include this function as\n         *  it was to expose the hook for it\n         */\n        var nativeData = (_entry$getInitialData = (_entry$getInitialData2 = entry.getInitialDataForExternal) === null || _entry$getInitialData2 === void 0 ? void 0 : _entry$getInitialData2.call(entry, feedback)) !== null && _entry$getInitialData !== void 0 ? _entry$getInitialData : null;\n        if (nativeData) {\n          for (var _i = 0, _Object$entries = Object.entries(nativeData); _i < _Object$entries.length; _i++) {\n            var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 2),\n              key = _Object$entries$_i[0],\n              data = _Object$entries$_i[1];\n            event.dataTransfer.setData(key, data !== null && data !== void 0 ? data : '');\n          }\n        }\n\n        /**\n         *  📱 For Android devices, a drag operation will not start unless\n         * \"text/plain\" or \"text/uri-list\" data exists in the native data store\n         * https://twitter.com/alexandereardon/status/1732189803754713424\n         *\n         * Tested on:\n         * Device: Google Pixel 5\n         * Android version: 14 (November 5, 2023)\n         * Chrome version: 120.0\n         */\n        if (isAndroid() && !event.dataTransfer.types.includes(textMediaType) && !event.dataTransfer.types.includes(URLMediaType)) {\n          event.dataTransfer.setData(textMediaType, androidFallbackText);\n        }\n\n        /**\n         * 1. Must set any media type for `iOS15` to work\n         * 2. We are also doing adding data so that the native adapter\n         * can know that the element adapter has handled this drag\n         *\n         * We used to wrap this `setData()` in a `try/catch` for Firefox,\n         * but it looks like that was not needed.\n         *\n         * Tested using: https://codesandbox.io/s/checking-firefox-throw-behaviour-on-dragstart-qt8h4f\n         *\n         * - ✅ Firefox@70.0 (Oct 2019) on macOS Sonoma\n         * - ✅ Firefox@70.0 (Oct 2019) on macOS Big Sur\n         * - ✅ Firefox@70.0 (Oct 2019) on Windows 10\n         *\n         * // just checking a few more combinations to be super safe\n         *\n         * - ✅ Chrome@78 (Oct 2019) on macOS Big Sur\n         * - ✅ Chrome@78 (Oct 2019) on Windows 10\n         * - ✅ Safari@14.1 on macOS Big Sur\n         */\n        event.dataTransfer.setData(elementAdapterNativeDataKey, '');\n        var payload = {\n          element: entry.element,\n          dragHandle: (_entry$dragHandle2 = entry.dragHandle) !== null && _entry$dragHandle2 !== void 0 ? _entry$dragHandle2 : null,\n          data: (_entry$getInitialData3 = (_entry$getInitialData4 = entry.getInitialData) === null || _entry$getInitialData4 === void 0 ? void 0 : _entry$getInitialData4.call(entry, feedback)) !== null && _entry$getInitialData3 !== void 0 ? _entry$getInitialData3 : {}\n        };\n        var dragType = {\n          type: 'element',\n          payload: payload,\n          startedFrom: 'internal'\n        };\n        api.start({\n          event: event,\n          dragType: dragType\n        });\n      }\n    }));\n  },\n  dispatchEventToSource: function dispatchEventToSource(_ref) {\n    var _draggableRegistry$ge, _draggableRegistry$ge2;\n    var eventName = _ref.eventName,\n      payload = _ref.payload;\n    // During a drag operation, a draggable can be:\n    // - remounted with different functions\n    // - removed completely\n    // So we need to get the latest entry from the registry in order\n    // to call the latest event functions\n\n    (_draggableRegistry$ge = draggableRegistry.get(payload.source.element)) === null || _draggableRegistry$ge === void 0 || (_draggableRegistry$ge2 = _draggableRegistry$ge[eventName]) === null || _draggableRegistry$ge2 === void 0 || _draggableRegistry$ge2.call(_draggableRegistry$ge,\n    // I cannot seem to get the types right here.\n    // TS doesn't seem to like that one event can need `nativeSetDragImage`\n    // @ts-expect-error\n    payload);\n  },\n  onPostDispatch: honeyPotFix.getOnPostDispatch()\n});\nexport var dropTargetForElements = adapter.dropTarget;\nexport var monitorForElements = adapter.monitor;\nexport function draggable(args) {\n  // Guardrail: warn if the drag handle is not contained in draggable element\n  if (process.env.NODE_ENV !== 'production') {\n    if (args.dragHandle && !args.element.contains(args.dragHandle)) {\n      // eslint-disable-next-line no-console\n      console.warn('Drag handle element must be contained in draggable element', {\n        element: args.element,\n        dragHandle: args.dragHandle\n      });\n    }\n  }\n  // Guardrail: warn if the draggable element is already registered\n  if (process.env.NODE_ENV !== 'production') {\n    var existing = draggableRegistry.get(args.element);\n    if (existing) {\n      // eslint-disable-next-line no-console\n      console.warn('You have already registered a `draggable` on the same element', {\n        existing: existing,\n        proposed: args\n      });\n    }\n  }\n  var cleanup = combine(\n  // making the draggable register the adapter rather than drop targets\n  // this is because you *must* have a draggable element to start a drag\n  // but you _might_ not have any drop targets immediately\n  // (You might create drop targets async)\n  adapter.registerUsage(), addToRegistry(args), addAttribute(args.element, {\n    attribute: 'draggable',\n    value: 'true'\n  }));\n\n  // Wrapping in `once` to prevent unexpected side effects if consumers call\n  // the clean up function multiple times.\n  return once(cleanup);\n}\n\n/** Common event payload for all events */\n\n/** A map containing payloads for all events */\n\n/** Common event payload for all drop target events */\n\n/** A map containing payloads for all events on drop targets */\n\n/** Arguments given to all feedback functions (eg `canDrag()`) on for a `draggable()` */\n\n/** Arguments given to all feedback functions (eg `canDrop()`) on a `dropTargetForElements()` */\n\n/** Arguments given to all monitor feedback functions (eg `canMonitor()`) for a `monitorForElements` */", "import { once } from '../public-utils/once';\nimport { isSafari } from './is-safari';\n\n// Using `cache` as our `isIOS()` result will not change in a browser\n\n/**\n * **Notes**\n *\n * All browsers on iOS (Safari, Chrome, Firefox) use the same rendering engine (Safari / Webkit).\n *\n * → https://developer.apple.com/app-store/review/guidelines/ (see 2.5.6)\n *\n * There is some ongoing change in this space, and we might see some new browser\n * engines soon on iOS (at least in Europe)\n *\n * → https://developer.apple.com/support/alternative-browser-engines/\n **/\n\n/**\n * Returns `true` if browser is Safari (WebKit) on iOS.\n */\nexport var isSafariOnIOS = once(function isSafariOnIOS() {\n  if (process.env.NODE_ENV === 'test') {\n    return false;\n  }\n  return isSafari() && 'ontouchend' in document;\n});", "import { isSafariOnIOS } from '../../../util/is-safari-on-ios';\n\n/** Any valid CSS string value\n * @example `calc(var(--grid) * 2)\n */\n\n/**\n * Position the native drag preview **in front** of the users pointer.\n *\n * **Distance**\n *\n * If the total width of your preview (including the offset applied by this function)\n * exceeds `280px` then the drag preview will have more opacity applied on Windows.\n *\n * https://atlassian.design/components/pragmatic-drag-and-drop/web-platform-design-constraints\n *\n * **Direction**\n *\n * This function will position the drag preview on the _right hand side for left to right (`ltr`) interfaces_, and on the _left hand side for right to left (`rtl`) languages_.\n *\n * The direction will be calculated based on the direction (`dir`) being applied to the `container`\n * element (which will be a child of the `body` element).\n *\n * **iOS**\n *\n * This function will not push the preview away from the users pointer on iOS due to platform limitations.\n * On iOS the preview will start the drag on the top left corner (or top right corner for right to left interfaces).\n * While dragging, iOS will shift the drag preview under the center of the users pointer, so the \"pushing away\"\n * is short lived on iOS.\n */\nexport function pointerOutsideOfPreview(point) {\n  return function (_ref) {\n    var container = _ref.container;\n    /**\n     * **Approach: transparent borders.**\n     *\n     * The only reliable cross browser technique found to push a\n     * drag preview away from the cursor is to use transparent borders on the container.\n     *\n     * **🙅📱 Not pushing the preview away on iOS**\n     *\n     * On iOS, the browser will set the transparent border color to be black. We can provide a\n     * more polished experience not using the transparent border on iOS.\n     *\n     * While dragging, iOS will shift the drag preview under the center of the users pointer,\n     * so the \"pushing away\" of the preview is short lived on iOS anyway.\n     *\n     * Notes:\n     *\n     * - Tested on `iOS@18.4.1` on May 7th 2025\n     * - If you set the background color on the `container` the border color will be that\n     * - Setting a transparent background color on the `container` still results in a black border\n     * - The `<body>` text or background color does not change the black border color\n     */\n    if (!isSafariOnIOS()) {\n      Object.assign(container.style, {\n        borderInlineStart: \"\".concat(point.x, \" solid transparent\"),\n        borderTop: \"\".concat(point.y, \" solid transparent\")\n      });\n    }\n\n    // Unfortunate that we need to use `getComputedStyle`,\n    // but it's only a single call when the drag is starting.\n    var computed = window.getComputedStyle(container);\n    if (computed.direction === 'rtl') {\n      // The DOMRect will include the new border we added\n      var box = container.getBoundingClientRect();\n\n      // Use the top right corner (including the new border) as the offset.\n      // The border will push the preview away from the pointer.\n      return {\n        x: box.width,\n        y: 0\n      };\n    }\n\n    // Use the top left corner as the offset. The border will\n    // push the preview away from the pointer.\n    return {\n      x: 0,\n      y: 0\n    };\n  };\n}", "import { monitorForElements } from '../../../adapter/element-adapter';\nimport { isSafari } from '../../../util/is-safari';\nimport { maxZIndex } from '../../../util/max-z-index';\n\n/** A function to remove the element that has been added to the `container`.\n * @example () => ReactDOM.unmountComponentAtNode(container)\n */\n\n/** A function that will render a preview element into a `container` `HTMLElement` */\n\n/** By default we use the build in values for the native drag preview: {x: 0, y: 0} */\nfunction defaultOffset() {\n  return {\n    x: 0,\n    y: 0\n  };\n}\n\n/** This function provides the ability to mount an element for it to be used as the native drag preview\n *\n * @example\n * draggable({\n *  onGenerateDragPreview: ({ nativeSetDragImage }) => {\n *    setCustomNativeDragPreview({\n *      render: ({ container }) => {\n *        ReactDOM.render(<Preview item={item} />, container);\n *        return () => ReactDOM.unmountComponentAtNode(container);\n *      },\n *      nativeSetDragImage,\n *    });\n *    },\n * });\n */\nexport function setCustomNativeDragPreview(_ref) {\n  var render = _ref.render,\n    nativeSetDragImage = _ref.nativeSetDragImage,\n    _ref$getOffset = _ref.getOffset,\n    getOffset = _ref$getOffset === void 0 ? defaultOffset : _ref$getOffset;\n  var container = document.createElement('div');\n  Object.assign(container.style, {\n    // Ensuring we don't cause reflow when adding the element to the page\n    // Using `position:fixed` rather than `position:absolute` so we are\n    // positioned on the current viewport.\n    // `position:fixed` also creates a new stacking context, so we don't need to do that here\n    position: 'fixed',\n    // According to `mdn`, the element can be offscreen:\n    // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/setDragImage#imgelement\n    //\n    // However, that  information does not appear in the specs:\n    // https://html.spec.whatwg.org/multipage/dnd.html#dom-datatransfer-setdragimage-dev\n    //\n    // If the element is _completely_ offscreen, Safari@17.1 will cancel the drag\n    top: 0,\n    left: 0,\n    // Using maximum possible z-index so that this element will always be on top\n    // https://stackoverflow.com/questions/491052/minimum-and-maximum-value-of-z-index\n    // Did not use `layers` in `@atlaskit/theme` because:\n    // 1. This element is not a 'layer' in the conventional sense, as this element\n    //    is only created for a single frame for the browser to take a photo of it,\n    //    and then it is destroyed\n    // 2. Did not want to add a dependency onto `@atlaskit/theme`\n    // 3. Want to always be on top of product UI which might have higher z-index's\n    zIndex: maxZIndex,\n    // Avoiding any additional events caused by the new element (being super safe)\n    pointerEvents: 'none'\n  });\n  document.body.append(container);\n  var unmount = render({\n    container: container\n  });\n\n  /**\n   * Some frameworks (eg `react`) don't render into the container until the next microtask.\n   * - This will run before the browser takes it's picture of the element\n   * - This will run before the animation frame that removes `container`.\n   * */\n\n  queueMicrotask(function () {\n    var previewOffset = getOffset({\n      container: container\n    });\n\n    /**\n     * **Problem**\n     * On `Safari@17.1` if a drag preview element has some opacity,\n     * Safari will include elements behind the drag preview element\n     * in the drag preview.\n     * Bug: https://bugs.webkit.org/show_bug.cgi?id=266025\n     *\n     * **Fix**\n     * We push the drag preview so it is _almost_ completely offscreen so that\n     * there won't be any elements behind the drag preview element.\n     * If the element is _completely_ offscreen, then the drag is cancelled by Safari.\n     *\n     * Using `-0.0001` so that any potential \"see through\" on the drag preview element\n     * is effectively invisible 👻\n     *\n     * **Unsuccessful alternatives**\n     * Setting a background color (eg \"white\") on the `container`\n     * → Wrecks the opacity of the drag preview element\n     *\n     * Adding a parent element of the `container` with a background color (eg \"white\")\n     * → Wrecks the opacity of the drag preview element\n     */\n    if (isSafari()) {\n      var rect = container.getBoundingClientRect();\n\n      // We cannot apply this fix if nothing has been rendered into the `container`\n      if (rect.width === 0) {\n        return;\n      }\n      container.style.left = \"-\".concat(rect.width - 0.0001, \"px\");\n    }\n    nativeSetDragImage === null || nativeSetDragImage === void 0 || nativeSetDragImage(container, previewOffset.x, previewOffset.y);\n  });\n  function cleanup() {\n    unbindMonitor();\n    unmount === null || unmount === void 0 || unmount();\n    document.body.removeChild(container);\n  }\n  var unbindMonitor = monitorForElements({\n    // Remove portal in the dragstart event so that the user will never see it\n    onDragStart: cleanup,\n    // Backup: remove portal when the drop finishes (this would be an error case)\n    onDrop: cleanup\n  });\n}", "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\n/**\n * A function that will reorder an array (`list`).\n * `reorder` returns a new array with reordered items and does not\n *  modify the original array. The items in the array are also not modified.\n */\nexport function reorder(_ref) {\n  var list = _ref.list,\n    startIndex = _ref.startIndex,\n    finishIndex = _ref.finishIndex;\n  if (startIndex === -1 || finishIndex === -1) {\n    // Making this function consistently return a new array reference.\n    // This is consistent with .toSorted() which always returns a new array\n    // even when it does not do anything\n    return Array.from(list);\n  }\n  var result = Array.from(list);\n  var _result$splice = result.splice(startIndex, 1),\n    _result$splice2 = _slicedToArray(_result$splice, 1),\n    removed = _result$splice2[0];\n  result.splice(finishIndex, 0, removed);\n  return result;\n}", "\"use client\"\r\n\r\nimport React from \"react\"\r\n\r\nimport { Pop<PERSON>, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\"\r\nimport { type Column, type Table } from \"@tanstack/react-table\"\r\n\r\nimport ReactDOM from \"react-dom\"\r\nimport invariant from \"tiny-invariant\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { Checkbox } from \"@/components/ui/checkbox\"\r\nimport { Label } from \"@/components/ui/label\"\r\nimport { cx } from \"@/lib/utils\"\r\nimport { triggerPostMoveFlash } from \"@atlaskit/pragmatic-drag-and-drop-flourish/trigger-post-move-flash\"\r\nimport {\r\n  attachClosestEdge,\r\n  extractClosestEdge,\r\n  type Edge,\r\n} from \"@atlaskit/pragmatic-drag-and-drop-hitbox/closest-edge\"\r\nimport { getReorderDestinationIndex } from \"@atlaskit/pragmatic-drag-and-drop-hitbox/util/get-reorder-destination-index\"\r\nimport * as liveRegion from \"@atlaskit/pragmatic-drag-and-drop-live-region\"\r\nimport { DropIndicator } from \"@atlaskit/pragmatic-drag-and-drop-react-drop-indicator/box\"\r\nimport { combine } from \"@atlaskit/pragmatic-drag-and-drop/combine\"\r\nimport {\r\n  draggable,\r\n  dropTargetForElements,\r\n  monitorForElements,\r\n} from \"@atlaskit/pragmatic-drag-and-drop/element/adapter\"\r\nimport { pointerOutsideOfPreview } from \"@atlaskit/pragmatic-drag-and-drop/element/pointer-outside-of-preview\"\r\nimport { setCustomNativeDragPreview } from \"@atlaskit/pragmatic-drag-and-drop/element/set-custom-native-drag-preview\"\r\nimport { reorder } from \"@atlaskit/pragmatic-drag-and-drop/reorder\"\r\nimport { RiDraggable, RiEqualizer2Line } from \"@remixicon/react\"\r\n\r\ntype CleanupFn = () => void\r\n\r\ntype ItemEntry = { itemId: string; element: HTMLElement }\r\n\r\ntype ListContextValue = {\r\n  getListLength: () => number\r\n  registerItem: (entry: ItemEntry) => CleanupFn\r\n  reorderItem: (args: {\r\n    startIndex: number\r\n    indexOfTarget: number\r\n    closestEdgeOfTarget: Edge | null\r\n  }) => void\r\n  instanceId: symbol\r\n}\r\n\r\nconst ListContext = React.createContext<ListContextValue | null>(null)\r\n\r\nfunction useListContext() {\r\n  const listContext = React.useContext(ListContext)\r\n  invariant(listContext !== null)\r\n  return listContext\r\n}\r\n\r\ntype Item = {\r\n  id: string\r\n  label: string\r\n}\r\n\r\nconst itemKey = Symbol(\"item\")\r\n\r\ntype ItemData = {\r\n  [itemKey]: true\r\n  item: Item\r\n  index: number\r\n  instanceId: symbol\r\n}\r\n\r\nfunction getItemData({\r\n  item,\r\n  index,\r\n  instanceId,\r\n}: {\r\n  item: Item\r\n  index: number\r\n  instanceId: symbol\r\n}): ItemData {\r\n  return {\r\n    [itemKey]: true,\r\n    item,\r\n    index,\r\n    instanceId,\r\n  }\r\n}\r\n\r\nfunction isItemData(data: Record<string | symbol, unknown>): data is ItemData {\r\n  return data[itemKey] === true\r\n}\r\n\r\ntype DraggableState =\r\n  | { type: \"idle\" }\r\n  | { type: \"preview\"; container: HTMLElement }\r\n  | { type: \"dragging\" }\r\n\r\nconst idleState: DraggableState = { type: \"idle\" }\r\nconst draggingState: DraggableState = { type: \"dragging\" }\r\n\r\nfunction ListItem<TData>({\r\n  item,\r\n  index,\r\n  column,\r\n}: {\r\n  item: Item\r\n  index: number\r\n  column: Column<TData, unknown> | undefined\r\n}) {\r\n  const { registerItem, instanceId } = useListContext()\r\n\r\n  const ref = React.useRef<HTMLDivElement>(null)\r\n  const [closestEdge, setClosestEdge] = React.useState<Edge | null>(null)\r\n\r\n  const dragHandleRef = React.useRef<HTMLButtonElement>(null)\r\n\r\n  const [draggableState, setDraggableState] =\r\n    React.useState<DraggableState>(idleState)\r\n\r\n  React.useEffect(() => {\r\n    const element = ref.current\r\n    const dragHandle = dragHandleRef.current\r\n    invariant(element)\r\n    invariant(dragHandle)\r\n\r\n    const data = getItemData({ item, index, instanceId })\r\n\r\n    return combine(\r\n      registerItem({ itemId: item.id, element }),\r\n      draggable({\r\n        element: dragHandle,\r\n        getInitialData: () => data,\r\n        onGenerateDragPreview({ nativeSetDragImage }) {\r\n          setCustomNativeDragPreview({\r\n            nativeSetDragImage,\r\n            getOffset: pointerOutsideOfPreview({\r\n              x: \"10px\",\r\n              y: \"10px\",\r\n            }),\r\n            render({ container }) {\r\n              setDraggableState({ type: \"preview\", container })\r\n\r\n              return () => setDraggableState(draggingState)\r\n            },\r\n          })\r\n        },\r\n        onDragStart() {\r\n          setDraggableState(draggingState)\r\n        },\r\n        onDrop() {\r\n          setDraggableState(idleState)\r\n        },\r\n      }),\r\n      dropTargetForElements({\r\n        element,\r\n        canDrop({ source }) {\r\n          return (\r\n            isItemData(source.data) && source.data.instanceId === instanceId\r\n          )\r\n        },\r\n        getData({ input }) {\r\n          return attachClosestEdge(data, {\r\n            element,\r\n            input,\r\n            allowedEdges: [\"top\", \"bottom\"],\r\n          })\r\n        },\r\n        onDrag({ self, source }) {\r\n          const isSource = source.element === element\r\n          if (isSource) {\r\n            setClosestEdge(null)\r\n            return\r\n          }\r\n\r\n          const closestEdge = extractClosestEdge(self.data)\r\n\r\n          const sourceIndex = source.data.index\r\n          invariant(typeof sourceIndex === \"number\")\r\n\r\n          const isItemBeforeSource = index === sourceIndex - 1\r\n          const isItemAfterSource = index === sourceIndex + 1\r\n\r\n          const isDropIndicatorHidden =\r\n            (isItemBeforeSource && closestEdge === \"bottom\") ||\r\n            (isItemAfterSource && closestEdge === \"top\")\r\n\r\n          if (isDropIndicatorHidden) {\r\n            setClosestEdge(null)\r\n            return\r\n          }\r\n\r\n          setClosestEdge(closestEdge)\r\n        },\r\n        onDragLeave() {\r\n          setClosestEdge(null)\r\n        },\r\n        onDrop() {\r\n          setClosestEdge(null)\r\n        },\r\n      }),\r\n    )\r\n  }, [instanceId, item, index, registerItem])\r\n\r\n  return (\r\n    <React.Fragment>\r\n      <div ref={ref} className=\"relative border-b border-transparent\">\r\n        <div\r\n          className={cx(\r\n            \"relative flex items-center justify-between gap-2\",\r\n            draggableState.type === \"dragging\" && \"opacity-50\",\r\n          )}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <Checkbox\r\n              checked={column?.getIsVisible()}\r\n              onCheckedChange={() => column?.toggleVisibility()}\r\n            />\r\n            <span>{item.label}</span>\r\n          </div>\r\n          <Button\r\n            aria-hidden=\"true\"\r\n            tabIndex={-1}\r\n            variant=\"ghost\"\r\n            className=\"-mr-1 px-0 py-1\"\r\n            ref={dragHandleRef}\r\n            aria-label={`Reorder ${item.label}`}\r\n          >\r\n            <RiDraggable className=\"size-5 text-gray-400 dark:text-gray-600\" />\r\n          </Button>\r\n        </div>\r\n        {closestEdge && <DropIndicator edge={closestEdge} gap=\"1px\" />}\r\n      </div>\r\n      {draggableState.type === \"preview\" &&\r\n        ReactDOM.createPortal(\r\n          <div>{item.label}</div>,\r\n          draggableState.container,\r\n        )}\r\n    </React.Fragment>\r\n  )\r\n}\r\n\r\nfunction getItemRegistry() {\r\n  const registry = new Map<string, HTMLElement>()\r\n\r\n  function register({ itemId, element }: ItemEntry) {\r\n    registry.set(itemId, element)\r\n\r\n    return function unregister() {\r\n      registry.delete(itemId)\r\n    }\r\n  }\r\n\r\n  function getElement(itemId: string): HTMLElement | null {\r\n    return registry.get(itemId) ?? null\r\n  }\r\n\r\n  return { register, getElement }\r\n}\r\n\r\ntype ListState = {\r\n  items: Item[]\r\n  lastCardMoved: {\r\n    item: Item\r\n    previousIndex: number\r\n    currentIndex: number\r\n    numberOfItems: number\r\n  } | null\r\n}\r\n\r\ninterface DataTableViewOptionsProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nfunction ViewOptions<TData>({ table }: DataTableViewOptionsProps<TData>) {\r\n  const tableColumns: Item[] = table.getAllColumns().map((column) => ({\r\n    id: column.id,\r\n    label: column.columnDef.meta?.displayName ?? column.id,\r\n  }))\r\n  const [{ items, lastCardMoved }, setListState] = React.useState<ListState>({\r\n    items: tableColumns,\r\n    lastCardMoved: null,\r\n  })\r\n  const [registry] = React.useState(getItemRegistry)\r\n\r\n  // Isolated instances of this component from one another\r\n  const [instanceId] = React.useState(() => Symbol(\"instance-id\"))\r\n\r\n  React.useEffect(() => {\r\n    table.setColumnOrder(items.map((item) => item.id))\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [items])\r\n\r\n  const reorderItem = React.useCallback(\r\n    ({\r\n      startIndex,\r\n      indexOfTarget,\r\n      closestEdgeOfTarget,\r\n    }: {\r\n      startIndex: number\r\n      indexOfTarget: number\r\n      closestEdgeOfTarget: Edge | null\r\n    }) => {\r\n      const finishIndex = getReorderDestinationIndex({\r\n        startIndex,\r\n        closestEdgeOfTarget,\r\n        indexOfTarget,\r\n        axis: \"vertical\",\r\n      })\r\n\r\n      if (finishIndex === startIndex) {\r\n        return\r\n      }\r\n\r\n      setListState((listState) => {\r\n        const item = listState.items[startIndex]\r\n\r\n        // Make sure item exists before proceeding\r\n        if (!item) {\r\n          return listState\r\n        }\r\n\r\n        return {\r\n          items: reorder({\r\n            list: listState.items,\r\n            startIndex,\r\n            finishIndex,\r\n          }),\r\n          lastCardMoved: {\r\n            item,\r\n            previousIndex: startIndex,\r\n            currentIndex: finishIndex,\r\n            numberOfItems: listState.items.length,\r\n          },\r\n        }\r\n      })\r\n    },\r\n    [],\r\n  )\r\n\r\n  React.useEffect(() => {\r\n    return monitorForElements({\r\n      canMonitor({ source }) {\r\n        return isItemData(source.data) && source.data.instanceId === instanceId\r\n      },\r\n      onDrop({ location, source }) {\r\n        const target = location.current.dropTargets[0]\r\n        if (!target) {\r\n          return\r\n        }\r\n\r\n        const sourceData = source.data\r\n        const targetData = target.data\r\n        if (!isItemData(sourceData) || !isItemData(targetData)) {\r\n          return\r\n        }\r\n\r\n        const indexOfTarget = items.findIndex(\r\n          (item) => item.id === targetData.item.id,\r\n        )\r\n        if (indexOfTarget < 0) {\r\n          return\r\n        }\r\n\r\n        const closestEdgeOfTarget = extractClosestEdge(targetData)\r\n\r\n        reorderItem({\r\n          startIndex: sourceData.index,\r\n          indexOfTarget,\r\n          closestEdgeOfTarget,\r\n        })\r\n      },\r\n    })\r\n  }, [instanceId, items, reorderItem])\r\n\r\n  // once a drag is finished, we have some post drop actions to take\r\n  React.useEffect(() => {\r\n    if (lastCardMoved === null) {\r\n      return\r\n    }\r\n\r\n    const { item, previousIndex, currentIndex, numberOfItems } = lastCardMoved\r\n    const element = registry.getElement(item.id)\r\n    if (element) {\r\n      triggerPostMoveFlash(element)\r\n    }\r\n\r\n    liveRegion.announce(\r\n      `You've moved ${item.label} from position ${previousIndex + 1\r\n      } to position ${currentIndex + 1} of ${numberOfItems}.`,\r\n    )\r\n  }, [lastCardMoved, registry])\r\n\r\n  // cleanup the live region when this component is finished\r\n  React.useEffect(() => {\r\n    return function cleanup() {\r\n      liveRegion.cleanup()\r\n    }\r\n  }, [])\r\n\r\n  const getListLength = React.useCallback(() => items.length, [items.length])\r\n\r\n  const contextValue: ListContextValue = React.useMemo(() => {\r\n    return {\r\n      registerItem: registry.register,\r\n      reorderItem,\r\n      instanceId,\r\n      getListLength,\r\n    }\r\n  }, [registry.register, reorderItem, instanceId, getListLength])\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-center\">\r\n        <Popover>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"secondary\"\r\n              className={cx(\r\n                \"ml-auto hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex\",\r\n              )}\r\n            >\r\n              <RiEqualizer2Line className=\"size-4\" aria-hidden=\"true\" />\r\n              View\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent\r\n            align=\"end\"\r\n            sideOffset={7}\r\n            className=\"z-50 w-fit space-y-2\"\r\n          >\r\n            <Label className=\"font-medium\">Display properties</Label>\r\n            <ListContext.Provider value={contextValue}>\r\n              <div className=\"flex flex-col\">\r\n                {items.map((item, index) => {\r\n                  const column = table.getColumn(item.id)\r\n                  if (!column) return null\r\n                  return (\r\n                    <div\r\n                      key={column.id}\r\n                      className={cx(!column.getCanHide() && \"hidden\")}\r\n                    >\r\n                      <ListItem column={column} item={item} index={index} />\r\n                    </div>\r\n                  )\r\n                })}\r\n              </div>\r\n            </ListContext.Provider>\r\n          </PopoverContent>\r\n        </Popover>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport { ViewOptions }\r\n", "\"use client\"\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\r\nimport { RiDownloadLine } from \"@remixicon/react\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { ViewOptions } from \"./DataTableViewOptions\"\r\n\r\ninterface DataTableToolbarProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function Filterbar<TData>({ table }: DataTableToolbarProps<TData>) {\r\n  // const isFiltered = table.getState().columnFilters.length > 0\r\n  // const [searchTerm, setSearchTerm] = useState<string>(\"\")\r\n\r\n  // const debouncedSetFilterValue = useDebouncedCallback((value) => {\r\n  //   table.getColumn(\"owner\")?.setFilterValue(value)\r\n  // }, 300)\r\n\r\n  // const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n  //   const value = event.target.value\r\n  //   setSearchTerm(value)\r\n  //   debouncedSetFilterValue(value)\r\n  // }\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap items-center justify-between gap-2 sm:gap-x-6\">\r\n      <div className=\"flex w-full flex-col gap-2 sm:w-fit sm:flex-row sm:items-center\">\r\n        {/* {table.getColumn(\"status\")?.getIsVisible() && (\r\n          <DataTableFilter\r\n            column={table.getColumn(\"status\")}\r\n            title=\"Status\"\r\n            options={statuses}\r\n            type=\"select\"\r\n          />\r\n        )}\r\n        {table.getColumn(\"region\")?.getIsVisible() && (\r\n          <DataTableFilter\r\n            column={table.getColumn(\"region\")}\r\n            title=\"Region\"\r\n            options={regions}\r\n            type=\"checkbox\"\r\n          />\r\n        )}\r\n        {table.getColumn(\"costs\")?.getIsVisible() && (\r\n          <DataTableFilter\r\n            column={table.getColumn(\"costs\")}\r\n            title=\"Costs\"\r\n            type=\"number\"\r\n            options={conditions}\r\n            formatter={(value) => formatters.currency({ number: Number(value) })}\r\n          />\r\n        )}\r\n        {table.getColumn(\"owner\")?.getIsVisible() && (\r\n          <Searchbar\r\n            type=\"search\"\r\n            placeholder=\"Search by owner...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchChange}\r\n            className=\"w-full sm:max-w-[250px] sm:[&>input]:h-[30px]\"\r\n          />\r\n        )}\r\n        {isFiltered && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => table.resetColumnFilters()}\r\n            className=\"border border-gray-200 px-2 font-semibold text-indigo-600 sm:border-none sm:py-1 dark:border-gray-800 dark:text-indigo-500\"\r\n          >\r\n            Clear filters\r\n          </Button>\r\n        )} */}\r\n      </div>\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button\r\n          variant=\"secondary\"\r\n          className=\"hidden gap-x-2 px-2 py-1.5 text-sm sm:text-xs lg:flex\"\r\n        >\r\n          <RiDownloadLine className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n          Export\r\n        </Button>\r\n        <ViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "import { Button } from \"@/components/ui/button\"\r\nimport { cx } from \"@/lib/utils\"\r\nimport {\r\n  RiArrowLeftDoubleLine,\r\n  RiArrowLeftSLine,\r\n  RiArrowRightDoubleLine,\r\n  RiArrowRightSLine,\r\n} from \"@remixicon/react\"\r\nimport { type Table } from \"@tanstack/react-table\"\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\r\n\r\ninterface DataTablePaginationProps<TData> {\r\n  table: Table<TData>\r\n  pageSize: number\r\n  totalCount?: number\r\n  onPageSizeChange?: (pageSize: number) => void\r\n}\r\n\r\nexport function DataTablePagination<TData>({\r\n  table,\r\n  pageSize,\r\n  totalCount,\r\n  onPageSizeChange,\r\n}: DataTablePaginationProps<TData>) {\r\n  const paginationButtons = [\r\n    {\r\n      icon: RiArrowLeftDoubleLine,\r\n      onClick: () => table.setPageIndex(0),\r\n      disabled: !table.getCanPreviousPage(),\r\n      srText: \"First page\",\r\n      mobileView: \"hidden sm:block\",\r\n    },\r\n    {\r\n      icon: RiArrowLeftSLine,\r\n      onClick: () => table.previousPage(),\r\n      disabled: !table.getCanPreviousPage(),\r\n      srText: \"Previous page\",\r\n      mobileView: \"\",\r\n    },\r\n    {\r\n      icon: RiArrowRightSLine,\r\n      onClick: () => table.nextPage(),\r\n      disabled: !table.getCanNextPage(),\r\n      srText: \"Next page\",\r\n      mobileView: \"\",\r\n    },\r\n    {\r\n      icon: RiArrowRightDoubleLine,\r\n      onClick: () => table.setPageIndex(table.getPageCount() - 1),\r\n      disabled: !table.getCanNextPage(),\r\n      srText: \"Last page\",\r\n      mobileView: \"hidden sm:block\",\r\n    },\r\n  ]\r\n\r\n  const displayedTotalRows = totalCount ?? table.getFilteredRowModel().rows.length\r\n  const currentPage = table.getState().pagination.pageIndex\r\n  const firstRowIndex = currentPage * pageSize + 1\r\n  const lastRowIndex = Math.min(displayedTotalRows, firstRowIndex + pageSize - 1)\r\n\r\n  const pageSizeOptions = [10, 25, 50, 100, \"All\"]\r\n\r\n  const handlePageSizeChange = (value: string) => {\r\n    const newSize = value === \"All\" ? displayedTotalRows : parseInt(value, 10)\r\n    table.setPageSize(newSize)\r\n\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(newSize)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <span className=\"text-sm text-gray-500\">Items per page:</span>\r\n        <Select\r\n          value={pageSize.toString()}\r\n          onValueChange={handlePageSizeChange}\r\n        >\r\n          <SelectTrigger className=\"h-8 w-[80px]\">\r\n            <SelectValue placeholder={pageSize.toString()} />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            {pageSizeOptions.map((option) => (\r\n              <SelectItem key={option} value={option.toString()}>\r\n                {option}\r\n              </SelectItem>\r\n            ))}\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      <div className=\"flex items-center justify-between w-full sm:w-auto\">\r\n        <div className=\"text-sm tabular-nums text-gray-500\">\r\n          {table.getFilteredSelectedRowModel().rows.length} of {displayedTotalRows} row(s)\r\n          selected.\r\n        </div>\r\n        <div className=\"flex items-center gap-x-6 lg:gap-x-8\">\r\n          <p className=\"hidden text-sm tabular-nums text-gray-500 sm:block\">\r\n            Showing{\" \"}\r\n            <span className=\"font-medium text-gray-900 dark:text-gray-50\">\r\n              {firstRowIndex}-{lastRowIndex}\r\n            </span>{\" \"}\r\n            of{\" \"}\r\n            <span className=\"font-medium text-gray-900 dark:text-gray-50\">\r\n              {displayedTotalRows}\r\n            </span>\r\n          </p>\r\n          <div className=\"flex items-center gap-x-1.5\">\r\n            {paginationButtons.map((button, index) => (\r\n              <Button\r\n                key={index}\r\n                variant=\"secondary\"\r\n                className={cx(button.mobileView, \"p-1.5\")}\r\n                onClick={() => {\r\n                  button.onClick()\r\n                  table.resetRowSelection()\r\n                }}\r\n                disabled={button.disabled}\r\n              >\r\n                <span className=\"sr-only\">{button.srText}</span>\r\n                <button.icon className=\"size-4 shrink-0\" aria-hidden=\"true\" />\r\n              </Button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n", "\"use client\"\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\"\r\nimport { cx } from \"@/lib/utils\"\r\nimport * as React from \"react\"\r\n\r\n// import { DataTableBulkEditor } from \"./DataTableBulkEditor\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Search } from \"@/components/ui/search\"\r\nimport { RiAddLine, RiArrowDownLine, RiArrowUpLine, RiRefreshLine } from \"@remixicon/react\"\r\nimport { Filterbar } from \"./DataTableFilterbar\"\r\nimport { DataTablePagination } from \"./DataTablePagination\"\r\n\r\nimport {\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n  type ColumnDef,\r\n  type OnChangeFn,\r\n  type PaginationState,\r\n  type Table as ReactTable,\r\n  type SortingState,\r\n} from \"@tanstack/react-table\"\r\n\r\ninterface DataTableProps<TData> {\r\n  columns: ColumnDef<TData>[]\r\n  data: TData[]\r\n  totalCount?: number\r\n  isLoading?: boolean\r\n  manualPagination?: boolean\r\n  manualSorting?: boolean\r\n  pageSize?: number\r\n  onPaginationChange?: (pagination: PaginationState) => void\r\n  onSortingChange?: (sorting: SortingState) => void\r\n  sortingState?: SortingState\r\n  onSearch?: (value: string) => void\r\n  searchValue?: string\r\n  customFilterbar?: React.ComponentType<{ table: ReactTable<TData>, onSearch?: (value: string) => void, searchValue?: string }>\r\n  hideDefaultFilterbar?: boolean\r\n  onRefresh?: () => void\r\n  title?: string\r\n  actionButton?: {\r\n    label?: string\r\n    onClick: () => void\r\n    content?: React.ReactNode\r\n  }\r\n  enableRowSelection?: boolean\r\n}\r\n\r\nexport function DataTable<TData>({\r\n  columns,\r\n  data,\r\n  totalCount,\r\n  isLoading,\r\n  manualPagination = false,\r\n  manualSorting = false,\r\n  pageSize = 20,\r\n  onPaginationChange,\r\n  onSortingChange,\r\n  sortingState,\r\n  onSearch,\r\n  searchValue = \"\",\r\n  customFilterbar: CustomFilterbar,\r\n  hideDefaultFilterbar = false,\r\n  onRefresh,\r\n  title,\r\n  actionButton,\r\n  enableRowSelection = true,\r\n}: DataTableProps<TData>) {\r\n  const [rowSelection, setRowSelection] = React.useState({})\r\n  const [pagination, setPagination] = React.useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: pageSize,\r\n  })\r\n  const [sorting, setSorting] = React.useState<SortingState>(sortingState ?? [])\r\n  const [isRefreshing, setIsRefreshing] = React.useState(false)\r\n\r\n  // Update local sorting state when prop changes\r\n  React.useEffect(() => {\r\n    if (sortingState) {\r\n      setSorting(sortingState);\r\n    }\r\n  }, [sortingState]);\r\n\r\n  // Handle pagination changes internally or pass to parent\r\n  const handlePaginationChange: OnChangeFn<PaginationState> = React.useCallback(\r\n    (updaterOrValue) => {\r\n      const newPagination = typeof updaterOrValue === 'function'\r\n        ? updaterOrValue(pagination)\r\n        : updaterOrValue;\r\n\r\n      setPagination(newPagination);\r\n      if (onPaginationChange) {\r\n        onPaginationChange(newPagination);\r\n      }\r\n    },\r\n    [onPaginationChange, pagination]\r\n  );\r\n\r\n  // Handle sorting changes internally or pass to parent\r\n  const handleSortingChange: OnChangeFn<SortingState> = React.useCallback(\r\n    (updaterOrValue) => {\r\n      const newSorting = typeof updaterOrValue === 'function'\r\n        ? updaterOrValue(sorting)\r\n        : updaterOrValue;\r\n\r\n      setSorting(newSorting);\r\n      if (onSortingChange) {\r\n        onSortingChange(newSorting);\r\n      }\r\n    },\r\n    [onSortingChange, sorting]\r\n  );\r\n\r\n  // Handle page size changes\r\n  const handlePageSizeChange = React.useCallback(\r\n    (newPageSize: number) => {\r\n      const newPagination = {\r\n        pageIndex: 0, // Reset to first page when changing page size\r\n        pageSize: newPageSize,\r\n      };\r\n\r\n      setPagination(newPagination);\r\n      if (onPaginationChange) {\r\n        onPaginationChange(newPagination);\r\n      }\r\n    },\r\n    [onPaginationChange]\r\n  );\r\n\r\n  // Handle refresh with animation\r\n  const handleRefresh = React.useCallback(() => {\r\n    if (onRefresh && !isLoading && !isRefreshing) {\r\n      setIsRefreshing(true);\r\n\r\n      // Call the provided refresh function\r\n      onRefresh();\r\n\r\n      // Reset the refreshing state after a delay\r\n      setTimeout(() => {\r\n        setIsRefreshing(false);\r\n      }, 1000);\r\n    }\r\n  }, [onRefresh, isLoading, isRefreshing]);\r\n\r\n  // Filter out selection column if row selection is disabled\r\n  const tableColumns = React.useMemo(() => {\r\n    if (!enableRowSelection) {\r\n      return columns.filter(col => col.id !== 'select');\r\n    }\r\n    return columns;\r\n  }, [columns, enableRowSelection]);\r\n\r\n  // Enhanced column headers with sort indicators\r\n  const enhancedColumns = React.useMemo(() => {\r\n    return tableColumns.map(column => {\r\n      // Skip if column is not sortable or explicitly disabled sorting\r\n      if (column.enableSorting === false) {\r\n        return column;\r\n      }\r\n\r\n      // Create a new column definition with custom header\r\n      return {\r\n        ...column,\r\n        header: (context) => {\r\n          const headerColumn = context.column;\r\n          const isSorted = headerColumn.getIsSorted();\r\n          const content = typeof column.header === 'string'\r\n            ? column.header\r\n            : column.header\r\n              ? flexRender(column.header, context)\r\n              : null;\r\n\r\n          const label = (\r\n            <>\r\n              {content}\r\n              {isSorted && (\r\n                <span className=\"inline-flex items-center\">\r\n                  {isSorted === \"asc\" ? (\r\n                    <RiArrowUpLine className=\"w-3.5 h-3.5\" />\r\n                  ) : (\r\n                    <RiArrowDownLine className=\"w-3.5 h-3.5\" />\r\n                  )}\r\n                </span>\r\n              )}\r\n            </>\r\n          );\r\n\r\n          return column.enableSorting !== false ? (\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => headerColumn.toggleSorting()}\r\n              className={cx(\r\n                \"inline-flex items-center gap-1 hover:text-primary\",\r\n                isSorted ? \"text-primary\" : \"\"\r\n              )}\r\n            >\r\n              {label}\r\n            </button>\r\n          ) : (\r\n            label\r\n          );\r\n        }\r\n      };\r\n    }) as ColumnDef<TData>[];\r\n  }, [tableColumns]);\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns: enhancedColumns,\r\n    state: {\r\n      rowSelection,\r\n      pagination,\r\n      sorting,\r\n    },\r\n    pageCount: totalCount ? Math.ceil(totalCount / pagination.pageSize) : -1,\r\n    enableRowSelection,\r\n    enableSorting: true,\r\n    manualSorting,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    onRowSelectionChange: setRowSelection,\r\n    onSortingChange: handleSortingChange,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    onPaginationChange: handlePaginationChange,\r\n    manualPagination,\r\n  })\r\n\r\n  return (\r\n    <>\r\n      <div className=\"space-y-3\">\r\n        {title && (\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h2 className=\"text-xl font-semibold\">{title}</h2>\r\n            {actionButton?.content}\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2\">\r\n          <div className=\"flex-1 w-full\">\r\n            {CustomFilterbar ? (\r\n              <CustomFilterbar table={table} onSearch={onSearch} searchValue={searchValue} />\r\n            ) : (\r\n              <>\r\n                {onSearch && (\r\n                  <Search onUpdate={onSearch} value={searchValue} />\r\n                )}\r\n                {!hideDefaultFilterbar && <Filterbar table={table} />}\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-2\">\r\n            {!title && actionButton && (\r\n              <Button\r\n                variant=\"primary\"\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-1 px-3 py-2\"\r\n                onClick={actionButton.onClick}\r\n              >\r\n                {actionButton.content ?? <RiAddLine className=\"h-4 w-4\" />}\r\n                <span>{actionButton.label}</span>\r\n              </Button>\r\n            )}\r\n\r\n            {onRefresh && (\r\n              <Button\r\n                variant=\"secondary\"\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-1\"\r\n                onClick={handleRefresh}\r\n                disabled={isLoading ?? isRefreshing}\r\n              >\r\n                <RiRefreshLine\r\n                  className={cx(\r\n                    \"h-4 w-4\",\r\n                    isRefreshing && \"animate-spin\"\r\n                  )}\r\n                />\r\n                <span className=\"hidden sm:inline\">Refresh</span>\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"relative overflow-hidden overflow-x-auto\">\r\n          <Table>\r\n            <TableHeader>\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow\r\n                  key={headerGroup.id}\r\n                  className=\"border-y border-gray-200 dark:border-gray-800\"\r\n                >\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead\r\n                      key={header.id}\r\n                      className={cx(\r\n                        \"whitespace-nowrap py-1 text-sm sm:text-xs\",\r\n                        header.column.columnDef.meta?.className,\r\n                      )}\r\n                    >\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                          header.column.columnDef.header,\r\n                          header.getContext()\r\n                        )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {isLoading ? (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={tableColumns.length}\r\n                    className=\"h-24 text-center\"\r\n                  >\r\n                    Loading...\r\n                  </TableCell>\r\n                </TableRow>\r\n              ) : table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row) => (\r\n                  <TableRow\r\n                    key={row.id}\r\n                    onClick={() => enableRowSelection && row.toggleSelected(!row.getIsSelected())}\r\n                    className={cx(\r\n                      \"group\",\r\n                      enableRowSelection ? \"select-none hover:bg-gray-50 dark:hover:bg-gray-900\" : \"\"\r\n                    )}\r\n                  >\r\n                    {row.getVisibleCells().map((cell, index) => (\r\n                      <TableCell\r\n                        key={cell.id}\r\n                        className={cx(\r\n                          row.getIsSelected()\r\n                            ? \"bg-gray-50 dark:bg-gray-900\"\r\n                            : \"\",\r\n                          \"relative whitespace-nowrap py-1 text-gray-600 first:w-10 dark:text-gray-400\",\r\n                          cell.column.columnDef.meta?.className,\r\n                        )}\r\n                      >\r\n                        {index === 0 && row.getIsSelected() && (\r\n                          <div className=\"absolute inset-y-0 left-0 w-0.5 bg-indigo-600 dark:bg-indigo-500\" />\r\n                        )}\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext(),\r\n                        )}\r\n                      </TableCell>\r\n                    ))}\r\n                  </TableRow>\r\n                ))\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={tableColumns.length}\r\n                    className=\"h-24 text-center\"\r\n                  >\r\n                    No results.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n          {/* <DataTableBulkEditor table={table} rowSelection={rowSelection} /> */}\r\n        </div>\r\n        <DataTablePagination\r\n          table={table}\r\n          pageSize={pagination.pageSize}\r\n          totalCount={totalCount}\r\n          onPageSizeChange={handlePageSizeChange}\r\n        />\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": ["SearchInput", "onUpdate", "value", "searchTerm", "setSearchTerm", "useState", "ref", "useRef", "searchStr", "useDebounce", "isUserInput", "onSearchEvent", "useCallback", "e", "target", "useEffect", "jsx", "Input", "Search", "memo", "durations", "triggerPostMoveFlash", "element", "_typeof", "o", "toPrimitive", "t", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "ownKeys", "r", "_objectSpread", "getDistanceToEdge", "rect", "client", "<PERSON><PERSON><PERSON>", "attachClosestEdge", "userData", "_ref", "_entries$sort$0$edge", "_entries$sort$", "input", "allowed<PERSON><PERSON>", "entries", "edge", "addClosestEdge", "a", "b", "extractClosestEdge", "_ref2", "getReorderDestinationIndex", "startIndex", "closestEdgeOfTarget", "indexOfTarget", "axis", "isGoingAfter", "isMovingForward", "announceDelay", "node", "size", "visuallyHiddenStyles", "createNode", "getNode", "timerId", "tryClearTimer", "announce", "message", "cleanup", "_node", "ATOMIC_GROUP_LENGTH", "ax", "classNames", "map", "list", "className", "key", "result", "presetStrokeColors", "presetStrokeWidth", "edgeToOrientationMap", "baseStyles", "orientationStyles", "edgeStyles", "lineStartFrom", "indent", "_ref3", "Line", "_ref4", "_ref4$gap", "gap", "_ref4$indent", "_ref4$strokeColor", "strokeColor", "_ref4$strokeWidth", "strokeWidth", "_ref4$type", "type", "orientation", "React.createElement", "DropIndicator", "_ref$appearance", "appearance", "React", "combine", "_len", "fns", "_key", "fn", "_arrayWithHoles", "_iterableToArrayLimit", "l", "n", "u", "f", "_arrayLikeToArray", "_unsupportedIterableToArray", "arrayLikeToArray", "_nonIterableRest", "_slicedToArray", "arrayWithHoles", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "bind_1", "bind", "_a", "listener", "options", "__assign", "this", "s", "p", "bindAll_1", "require$$0", "toOptions", "getBinding", "original", "sharedOptions", "binding", "bindAll", "bindings", "unbinds", "unbind", "exports", "bind_all_1", "require$$1", "honeyPotDataAttribute", "isHoneyPotElement", "getElementFromPointWithoutHoneypot", "_document$elementsFro", "_document$elementsFro2", "top", "second", "maxZIndex", "honeyPotSize", "halfHoneyPotSize", "floorToClosestPixel", "point", "pullBackByHalfHoneyPotSize", "preventGoingBackwardsOffScreen", "preventGoingForwardsOffScreen", "getHoneyPotRectFor", "getRectStyles", "clientRect", "<PERSON><PERSON><PERSON><PERSON>", "mountHoneyPot", "initial", "unbindPointer<PERSON>ove", "event", "_ref5", "current", "unbindPostDragEvents", "makeHoneyPotFix", "latestPointerMove", "bindEvents", "getOnPostDispatch", "finish", "_ref6", "eventName", "payload", "_finish", "_input", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_toConsumableArray", "arrayWithoutHoles", "iterableToArray", "nonIterableSpread", "once", "cache", "args", "isFirefox", "<PERSON><PERSON><PERSON><PERSON>", "_navigator", "userAgent", "symbols", "isLeavingWindowInSafari", "dragLeave", "getInitialState", "state", "resetState", "isNodeLike", "isFromAnotherWindow", "eventTarget", "isLeavingWindow", "relatedTarget", "getBindingsForBrokenDrags", "onDragEnd", "callCount", "getInput", "rafSchd", "lastArgs", "frameId", "wrapperFn", "scheduleOnDrag", "dragStart", "scheduled", "schedule", "flush", "makeDispatch", "source", "dispatchEvent", "previous", "safeDispatch", "dispatch", "nativeSetDragImage", "location", "updatedSourcePayload", "globalState", "canStart", "getNativeSetDragImage", "hasHierarchyChanged", "next", "start", "dragType", "getDropTargetsOver", "getStartLocation", "setDropEffectOnEvent", "updateState", "has<PERSON><PERSON>ed", "onUpdateEvent", "nextDropTargets", "cancel", "unbindEvents", "_current$", "innerMost", "dropTargets", "lifecycle", "ledger", "registerUsage", "typeKey", "mount", "entry", "register", "addAttribute", "attribute", "_createForOfIteratorHelper", "_n", "F", "copyReverse", "array", "makeDropTarget", "defaultDropEffect", "registry", "dropTargetDataAtt", "dropTargetSelector", "addToRegistry", "dropTargetForConsumers", "getActualDropTargets", "_args$getData", "_args$getData2", "_args$getDropEffect", "_args$getDropEffect2", "_ref2$result", "closest", "feedback", "data", "dropEffect", "record", "notify<PERSON><PERSON><PERSON>", "_iterator", "_step", "_entry$eventName", "err", "actions", "isCurrent", "visited", "_iterator2", "_step2", "_entry$onDropTargetCh", "isOver", "_entry$onDragLeave", "_iterator3", "_step3", "_entry$onDropTargetCh2", "_entry$onDragEnter", "_record", "_args", "_entry", "getIsOver", "actual", "lastCaptureOrdered", "actualCaptureOrdered", "resultCaptureOrdered", "index", "_argsForLast$getIsSti", "last", "fresh", "parent", "lastParent", "argsForLast", "makeMonitor", "dragging", "tryAddToActive", "monitor", "monitorForConsumers", "active", "_i", "_active", "_monitor", "_monitor$eventName", "makeAdapter", "dispatchEventToSource", "onPostDispatch", "monitorAPI", "dropTargetAPI", "mountAdapter", "api", "isAndroid", "androidFallbackText", "textMediaType", "URLMediaType", "elementAdapterNativeDataKey", "draggableRegistry", "honeyPotFix", "adapter", "_entry$dragHandle", "_entry$getInitialData", "_entry$getInitialData2", "_entry$dragHandle2", "_entry$getInitialData3", "_entry$getInitialData4", "over", "nativeData", "_Object$entries", "_Object$entries$_i", "_draggableRegistry$ge", "_draggableRegistry$ge2", "dropTargetForElements", "monitorForElements", "draggable", "isSafariOnIOS", "pointerOutsideOfPreview", "container", "computed", "box", "defaultOffset", "setCustomNativeDragPreview", "render", "_ref$getOffset", "getOffset", "unmount", "previewOffset", "unbindMonitor", "reorder", "finishIndex", "_result$splice", "_result$splice2", "removed", "ListContext", "useListContext", "listContext", "invariant", "itemKey", "getItemData", "item", "instanceId", "isItemData", "idleState", "draggingState", "ListItem", "column", "registerItem", "closestEdge", "setClosestEdge", "dragHandleRef", "draggableState", "setDraggableState", "dragHandle", "self", "sourceIndex", "isItemBeforeSource", "isItemAfterSource", "jsxs", "cx", "Checkbox", "<PERSON><PERSON>", "RiDraggable", "ReactDOM", "getItemRegistry", "itemId", "getElement", "ViewOptions", "table", "tableColumns", "items", "lastCardMoved", "setListState", "reorderItem", "listState", "sourceData", "targetData", "previousIndex", "currentIndex", "numberOfItems", "liveRegion.announce", "liveRegion.cleanup", "getList<PERSON><PERSON><PERSON>", "contextValue", "Popover", "PopoverTrigger", "RiEqualizer2Line", "PopoverC<PERSON>nt", "Label", "Filterbar", "RiDownloadLine", "DataTablePagination", "pageSize", "totalCount", "onPageSizeChange", "paginationButtons", "RiArrowLeftDoubleLine", "RiArrowLeftSLine", "RiArrowRightSLine", "RiArrowRightDoubleLine", "displayedTotalRows", "firstRowIndex", "lastRowIndex", "pageSizeOptions", "handlePageSizeChange", "newSize", "Select", "SelectTrigger", "SelectValue", "SelectContent", "option", "SelectItem", "button", "DataTable", "columns", "isLoading", "manualPagination", "manualSorting", "onPaginationChange", "onSortingChange", "sortingState", "onSearch", "searchValue", "CustomFilterbar", "hideDefaultFilterbar", "onRefresh", "title", "actionButton", "enableRowSelection", "rowSelection", "setRowSelection", "React.useState", "pagination", "setPagination", "sorting", "setSorting", "isRefreshing", "setIsRefreshing", "React.useEffect", "handlePaginationChange", "React.useCallback", "updaterOrValue", "newPagination", "handleSortingChange", "newSorting", "newPageSize", "handleRefresh", "React.useMemo", "col", "enhancedColumns", "context", "headerColumn", "isSorted", "content", "flexRender", "label", "Fragment", "RiArrowUpLine", "RiArrowDownLine", "useReactTable", "getFilteredRowModel", "getPaginationRowModel", "getSortedRowModel", "getCoreRowModel", "RiAddLine", "RiRefreshLine", "Table", "TableHeader", "headerGroup", "TableRow", "header", "TableHead", "TableBody", "TableCell", "row", "cell"], "mappings": "gtBAQA,MAAMA,GAAc,CAAC,CAAE,SAAAC,EAAU,MAAAC,KAAyB,CAExD,KAAM,CAACC,EAAYC,CAAa,EAAIC,EAAAA,SAAiBH,CAAK,EACpDI,EAAMC,SAAyB,IAAI,EACnCC,EAAYC,GAAoBN,CAAU,EAG1CO,EAAcH,SAAO,EAAK,EAG1BI,EAAgBC,cAAaC,GAAsB,CACvD,MAAMC,EAASD,EAAE,OACX,CAAE,MAAAX,CAAAA,EAAUY,EAClBJ,EAAY,QAAU,GACtBN,EAAcF,CAAK,CACrB,EAAG,EAAE,EAGLa,OAAAA,EAAAA,UAAU,IAAM,CACVL,EAAY,UACdT,EAASO,GAAa,EAAE,EACxBE,EAAY,QAAU,GACxB,EACC,CAACF,EAAWP,CAAQ,CAAC,EAGxBc,EAAAA,UAAU,IAAM,CAGVb,IAAUC,GAAc,CAACO,EAAY,SACvCN,EAAcF,CAAK,EAIjBA,GACFI,EAAI,SAAS,MAAM,CACrB,EACC,CAACJ,EAAOC,CAAU,CAAC,EAGpBa,EAAAA,IAAC,UAAQ,CAAA,UAAU,SACjB,SAAAA,EAAA,IAACC,GAAA,CACC,IAAAX,EACA,KAAK,OACL,MAAOH,EACP,YAAY,YACZ,SAAUQ,CAAA,CAAA,EAEd,CAEJ,EAEaO,GAASC,OAAKnB,EAAW,ECxD/B,IAAIoB,GAAY,CAIrB,MAAO,GACT,ECFO,SAASC,GAAqBC,EAAS,CAC5CA,EAAQ,QAAQ,CAAC,CACf,gBAAiB,wCAClB,EAAE,CAAE,CAAA,EAAG,CACN,SAAUF,GAAU,MAQpB,OAAQ,qCACR,WAAY,CAChB,CAAG,CACH,CCtBA,SAASG,EAAQC,EAAG,CAClB,0BAEA,OAAOD,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUC,EAAG,CAChG,OAAO,OAAOA,CACf,EAAG,SAAUA,EAAG,CACf,OAAOA,GAAmB,OAAO,QAArB,YAA+BA,EAAE,cAAgB,QAAUA,IAAM,OAAO,UAAY,SAAW,OAAOA,CACtH,EAAKD,EAAQC,CAAC,CACd,CCPA,SAASC,GAAYC,EAAG,EAAG,CACzB,GAAgBH,EAAQG,CAAC,GAArB,UAA0B,CAACA,EAAG,OAAOA,EACzC,IAAIb,EAAIa,EAAE,OAAO,WAAW,EAC5B,GAAeb,IAAX,OAAc,CAChB,IAAIc,EAAId,EAAE,KAAKa,EAAG,CAAc,EAChC,GAAgBH,EAAQI,CAAC,GAArB,SAAwB,OAAOA,EACnC,MAAM,IAAI,UAAU,8CAA8C,CACtE,CACE,OAAqB,IAAb,SAAiB,OAAS,QAAQD,CAAC,CAC7C,CCRA,SAASE,GAAcF,EAAG,CACxB,IAAIC,EAAIF,GAAYC,EAAG,QAAQ,EAC/B,OAAmBH,EAAQI,CAAC,GAArB,SAAyBA,EAAIA,EAAI,EAC1C,CCJA,SAASE,EAAgB,EAAG,EAAG,EAAG,CAChC,OAAQ,EAAID,GAAc,CAAC,KAAM,EAAI,OAAO,eAAe,EAAG,EAAG,CAC/D,MAAO,EACP,WAAY,GACZ,aAAc,GACd,SAAU,EACX,CAAA,EAAI,EAAE,CAAC,EAAI,EAAG,CACjB,CCPA,SAASE,GAAQ,EAAG,EAAG,CAAE,IAAI,EAAI,OAAO,KAAK,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIN,EAAI,OAAO,sBAAsB,CAAC,EAAG,IAAMA,EAAIA,EAAE,OAAO,SAAUO,EAAG,CAAE,OAAO,OAAO,yBAAyB,EAAGA,CAAC,EAAE,UAAa,CAAA,GAAI,EAAE,KAAK,MAAM,EAAGP,CAAC,EAAK,OAAO,CAAE,CAC7P,SAASQ,GAAc,EAAG,CAAE,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAE,IAAI,EAAY,UAAU,CAAC,GAAnB,KAAuB,UAAU,CAAC,EAAI,CAAA,EAAI,EAAI,EAAIF,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUC,EAAG,CAAEF,EAAgB,EAAGE,EAAG,EAAEA,CAAC,CAAC,CAAI,CAAA,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAID,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,EAAG,CAAE,OAAO,eAAe,EAAGA,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAI,CAAA,CAAE,CAAG,OAAO,CAAE,CAGrb,IAAIE,GAAoB,CACtB,IAAK,SAAaC,EAAMC,EAAQ,CAC9B,OAAO,KAAK,IAAIA,EAAO,EAAID,EAAK,GAAG,CACpC,EACD,MAAO,SAAeA,EAAMC,EAAQ,CAClC,OAAO,KAAK,IAAID,EAAK,MAAQC,EAAO,CAAC,CACtC,EACD,OAAQ,SAAgBD,EAAMC,EAAQ,CACpC,OAAO,KAAK,IAAID,EAAK,OAASC,EAAO,CAAC,CACvC,EACD,KAAM,SAAcD,EAAMC,EAAQ,CAChC,OAAO,KAAK,IAAIA,EAAO,EAAID,EAAK,IAAI,CACxC,CACA,EAGIE,GAAY,OAAO,aAAa,EAK7B,SAASC,GAAkBC,EAAUC,EAAM,CAChD,IAAIC,EAAsBC,EACtBnB,EAAUiB,EAAK,QACjBG,EAAQH,EAAK,MACbI,EAAeJ,EAAK,aAClBJ,EAAS,CACX,EAAGO,EAAM,QACT,EAAGA,EAAM,OACV,EAIGR,EAAOZ,EAAQ,sBAAuB,EACtCsB,EAAUD,EAAa,IAAI,SAAUE,EAAM,CAC7C,MAAO,CACL,KAAMA,EACN,MAAOZ,GAAkBY,CAAI,EAAEX,EAAMC,CAAM,CAC5C,CACL,CAAG,EAGGW,GAAkBN,GAAwBC,EAAiBG,EAAQ,KAAK,SAAUG,EAAGC,EAAG,CAC1F,OAAOD,EAAE,MAAQC,EAAE,KACpB,CAAA,EAAE,CAAC,KAAO,MAAQP,IAAmB,OAAS,OAASA,EAAe,QAAU,MAAQD,IAAyB,OAASA,EAAuB,KAClJ,OAAOR,GAAcA,GAAc,CAAE,EAAEM,CAAQ,EAAG,GAAIT,EAAgB,CAAE,EAAEO,GAAWU,CAAc,CAAC,CACtG,CAKO,SAASG,GAAmBX,EAAU,CAC3C,IAAIY,EACJ,OAAQA,EAAQZ,EAASF,EAAS,KAAO,MAAQc,IAAU,OAASA,EAAQ,IAC9E,CC3DO,SAASC,GAA2BZ,EAAM,CAC/C,IAAIa,EAAab,EAAK,WACpBc,EAAsBd,EAAK,oBAC3Be,EAAgBf,EAAK,cACrBgB,EAAOhB,EAAK,KAOd,GALIa,IAAe,IAAME,IAAkB,IAKvCF,IAAeE,EACjB,OAAOF,EAET,GAAIC,GAAuB,KACzB,OAAOC,EAET,IAAIE,EAAsCH,IAAwB,UAAYE,IAAS,aACnFE,EAAkBL,EAAaE,EAEnC,OAAIG,EACKD,EAAeF,EAAgBA,EAAgB,EAGjDE,EAAeF,EAAgB,EAAIA,CAC5C,CCpBO,IAAII,GAAgB,ICJvBC,EAAO,KACPC,EAAO,MACPC,GAAuB,CAGzB,MAAOD,EACP,OAAQA,EACR,QAAS,IACT,SAAU,WACV,OAAQ,IACR,KAAM,QAAQ,OAAOA,EAAM,IAAI,EAAE,OAAOA,EAAM,IAAI,EAAE,OAAOA,EAAM,IAAI,EAAE,OAAOA,EAAM,GAAG,EACvF,SAAU,SACV,WAAY,SAIZ,UAAW,IAAI,OAAOA,CAAI,EAE1B,cAAe,MACjB,EAKA,SAASE,IAAa,CACpB,IAAIH,EAAO,SAAS,cAAc,KAAK,EAQvC,OAAAA,EAAK,aAAa,OAAQ,QAAQ,EAClC,OAAO,OAAOA,EAAK,MAAOE,EAAoB,EAC9C,SAAS,KAAK,OAAOF,CAAI,EAClBA,CACT,CAKA,SAASI,IAAU,CACjB,OAAIJ,IAAS,OACXA,EAAOG,GAAY,GAEdH,CACT,CACA,IAAIK,EAAU,KACd,SAASC,IAAgB,CACnBD,IAAY,MACd,aAAaA,CAAO,EAEtBA,EAAU,IACZ,CAKO,SAASE,GAASC,EAAS,CAKhCJ,GAAS,EASTE,GAAe,EACfD,EAAU,WAAW,UAAY,CAC/BA,EAAU,KACV,IAAIL,EAAOI,GAAS,EACpBJ,EAAK,YAAcQ,CACpB,EAAET,EAAa,CAElB,CAKO,SAASU,IAAU,CACxB,IAAIC,EACJJ,GAAe,GACdI,EAAQV,KAAU,MAAQU,IAAU,QAAUA,EAAM,OAAQ,EAC7DV,EAAO,IACT,CCvFA,MAAMW,GAAsB,EAqBb,SAASC,GAAGC,EAAY,CAEnC,GAAI,CAACA,EAAW,OACZ,OAGJ,GAAIA,EAAW,SAAW,GACtBA,EAAW,CAAC,GAEZ,CAACA,EAAW,CAAC,EAAE,SAAS,GAAG,EAC3B,OAAOA,EAAW,CAAC,EAIvB,MAAMC,EAAM,CAAE,EAEd,UAAWvE,KAASsE,EAAY,CAE5B,GAAI,CAACtE,EACD,SAGJ,MAAMwE,EAAOxE,EAAM,MAAM,GAAG,EAC5B,UAAWyE,KAAaD,EAAM,CAc1B,MAAME,EAAMD,EAAU,WAAW,GAAG,EAAIA,EAAU,MAAM,EAAGL,EAAmB,EAAIK,EAClFF,EAAIG,CAAG,EAAID,CACvB,CACA,CASI,IAAIE,EAAS,GACb,UAAWD,KAAOH,EACdI,GAAUJ,EAAIG,CAAG,EAAI,IAGzB,GAAKC,EAIL,OAAOA,EAAO,QAAS,CAC3B,CCpFO,IAAIC,GAAqB,CAC9B,QAAS,qCACT,QAAS,mCACX,EACWC,GAAoB,sCCE3BC,GAAuB,CACzB,IAAK,aACL,OAAQ,aACR,KAAM,WACN,MAAO,UACT,EACIC,GAAa,CACf,KAAM,uJACR,EACIC,GAAoB,CACtB,WAAY,0CACZ,SAAU,yCACZ,EACIC,GAAa,CACf,IAAK,sBACL,MAAO,sBACP,OAAQ,sBACR,KAAM,qBACR,EACIC,GAAgB,CAGlB,SAAU,SAAkB7C,EAAM,CAChC,IAAI8C,EAAS9C,EAAK,OAClB,MAAO,iCAAiC,OAAO8C,EAAQ,GAAG,CAC3D,EAED,oBAAqB,SAAyBnC,EAAO,CACnD,IAAImC,EAASnC,EAAM,OACnB,MAAO,mCAAmC,OAAOmC,EAAQ,GAAG,CAC7D,EAED,cAAe,SAAoBC,EAAO,CACxC,IAAID,EAASC,EAAM,OACnB,OAAOD,CACX,CACA,EACO,SAASE,GAAKC,EAAO,CAC1B,IAAI3C,EAAO2C,EAAM,KACfC,EAAYD,EAAM,IAClBE,EAAMD,IAAc,OAAS,MAAQA,EACrCE,EAAeH,EAAM,OACrBH,EAASM,IAAiB,OAAS,MAAQA,EAC3CC,EAAoBJ,EAAM,YAC1BK,EAAcD,IAAsB,OAASd,GAAmB,QAAUc,EAC1EE,EAAoBN,EAAM,YAC1BO,EAAcD,IAAsB,OAASf,GAAoBe,EACjEE,EAAaR,EAAM,KACnBS,EAAOD,IAAe,OAAS,WAAaA,EAC1CE,EAAclB,GAAqBnC,CAAI,EAC3C,OAAoBsD,EAAAA,cAAoB,MAAO,CAC7C,MAAO,CAGL,iBAAkBN,EAClB,iBAAkBE,EAElB,qBAAsB,gBAAgB,OAAOL,EAAK,0BAA0B,EAI5E,yBAA0BN,GAAca,CAAI,EAAE,CAC5C,OAAQZ,CAChB,CAAO,EAGD,qBAAsBY,IAAS,cAAgB,OAAS,QACxD,sBAAuB,gCACvB,oBAAqB,qCAIrB,6BAA8B,sCAG9B,+BAAgC,gEACjC,EACD,UAAW1B,GAAG,CAACU,GAAW,KAAMC,GAAkBgB,CAAW,EAAGf,GAAWtC,CAAI,CAAC,CAAC,CACrF,CAAG,CACH,CC7EO,SAASuD,GAAc7D,EAAM,CAClC,IAAI8D,EAAkB9D,EAAK,WACzB+D,EAAaD,IAAoB,OAAS,UAAYA,EACtDxD,EAAON,EAAK,KACZmD,EAAMnD,EAAK,IACX8C,EAAS9C,EAAK,OACd0D,EAAO1D,EAAK,KACd,OAAoBgE,EAAM,cAAchB,GAAM,CAC5C,KAAM1C,EACN,IAAK6C,EACL,YAAaZ,GAAmBwB,CAAU,EAC1C,KAAML,EACN,OAAQZ,CACZ,CAAG,CACH,CCrBO,SAASmB,IAAU,CACxB,QAASC,EAAO,UAAU,OAAQC,EAAM,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC9ED,EAAIC,CAAI,EAAI,UAAUA,CAAI,EAE5B,OAAO,UAAmB,CACxBD,EAAI,QAAQ,SAAUE,EAAI,CACxB,OAAOA,EAAI,CACjB,CAAK,CACF,CACH,CCVA,SAASC,GAAgB9E,EAAG,CAC1B,GAAI,MAAM,QAAQA,CAAC,EAAG,OAAOA,CAC/B,CCFA,SAAS+E,GAAsB/E,EAAGgF,EAAG,CACnC,IAAI,EAAYhF,GAAR,KAAY,KAAsB,OAAO,OAAtB,KAAgCA,EAAE,OAAO,QAAQ,GAAKA,EAAE,YAAY,EAC/F,GAAY,GAAR,KAAW,CACb,IAAIlB,EACFmG,EACArF,EACAsF,EACAlE,EAAI,CAAE,EACNmE,EAAI,GACJ1F,EAAI,GACN,GAAI,CACF,GAAIG,GAAK,EAAI,EAAE,KAAKI,CAAC,GAAG,KAAYgF,IAAN,EAAS,CACrC,GAAI,OAAO,CAAC,IAAM,EAAG,OACrBG,EAAI,EACZ,KAAa,MAAO,EAAEA,GAAKrG,EAAIc,EAAE,KAAK,CAAC,GAAG,QAAUoB,EAAE,KAAKlC,EAAE,KAAK,EAAGkC,EAAE,SAAWgE,GAAIG,EAAI,GAAG,CACxF,OAAQnF,EAAG,CACVP,EAAI,GAAIwF,EAAIjF,CAClB,QAAc,CACR,GAAI,CACF,GAAI,CAACmF,GAAa,EAAE,QAAV,OAAwBD,EAAI,EAAE,OAAS,EAAI,OAAOA,CAAC,IAAMA,GAAI,MAC/E,QAAgB,CACR,GAAIzF,EAAG,MAAMwF,CACrB,CACA,CACI,OAAOjE,CACX,CACA,CC1BA,SAASoE,GAAkBpF,EAAGgB,EAAG,EACtBA,GAAR,MAAaA,EAAIhB,EAAE,UAAYgB,EAAIhB,EAAE,QACtC,QAASlB,EAAI,EAAG,EAAI,MAAMkC,CAAC,EAAGlC,EAAIkC,EAAGlC,IAAK,EAAEA,CAAC,EAAIkB,EAAElB,CAAC,EACpD,OAAO,CACT,CCHA,SAASuG,GAA4BrF,EAAGgB,EAAG,CACzC,GAAIhB,EAAG,CACL,GAAgB,OAAOA,GAAnB,SAAsB,OAAOsF,GAAiBtF,EAAGgB,CAAC,EACtD,IAAI,EAAI,CAAE,EAAC,SAAS,KAAKhB,CAAC,EAAE,MAAM,EAAG,EAAE,EACvC,OAAoB,IAAb,UAAkBA,EAAE,cAAgB,EAAIA,EAAE,YAAY,MAAiB,IAAV,OAAyB,IAAV,MAAc,MAAM,KAAKA,CAAC,EAAoB,IAAhB,aAAqB,2CAA2C,KAAK,CAAC,EAAIsF,GAAiBtF,EAAGgB,CAAC,EAAI,MACxN,CACA,CCPA,SAASuE,IAAmB,CAC1B,MAAM,IAAI,UAAU;AAAA,mFAA2I,CACjK,CCEA,SAASC,GAAexF,EAAGlB,EAAG,CAC5B,OAAO2G,GAAezF,CAAC,GAAK0F,GAAqB1F,EAAGlB,CAAC,GAAK6G,GAA2B3F,EAAGlB,CAAC,GAAK8G,GAAiB,CACjH,qDCLA,OAAO,eAAeC,EAAS,aAAc,CAAE,MAAO,GAAM,EAChDA,EAAA,KAAG,OACf,SAASC,EAAK/G,EAAQgH,EAAI,CACtB,IAAI7B,EAAO6B,EAAG,KAAMC,EAAWD,EAAG,SAAUE,EAAUF,EAAG,QACzD,OAAAhH,EAAO,iBAAiBmF,EAAM8B,EAAUC,CAAO,EACxC,UAAkB,CACrBlH,EAAO,oBAAoBmF,EAAM8B,EAAUC,CAAO,CACrD,CACL,CACAJ,OAAAA,EAAA,KAAeC,kDCTf,IAAII,EAAYC,GAAQA,EAAK,UAAa,UAAY,CAClD,OAAAD,EAAW,OAAO,QAAU,SAASvG,EAAG,CACpC,QAASyG,EAAGxG,EAAI,EAAGqF,EAAI,UAAU,OAAQrF,EAAIqF,EAAGrF,IAAK,CACjDwG,EAAI,UAAUxG,CAAC,EACf,QAASyG,KAAKD,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGC,CAAC,IAC1D1G,EAAE0G,CAAC,EAAID,EAAEC,CAAC,EAC1B,CACQ,OAAO1G,CACV,EACMuG,EAAS,MAAM,KAAM,SAAS,CACxC,EACD,OAAO,eAAeI,EAAS,aAAc,CAAE,MAAO,GAAM,EAC7CA,EAAA,QAAG,OAClB,IAAIT,EAA0BU,GAAA,EAC9B,SAASC,EAAUrI,EAAO,CACtB,GAAI,SAAOA,EAAU,KAGrB,OAAI,OAAOA,GAAU,UACV,CACH,QAASA,CACZ,EAEEA,CACX,CACA,SAASsI,EAAWC,EAAUC,EAAe,CACzC,GAAIA,GAAiB,KACjB,OAAOD,EAEX,IAAIE,EAAUV,EAASA,EAAS,CAAE,EAAEQ,CAAQ,EAAG,CAAE,QAASR,EAASA,EAAS,GAAIM,EAAUG,CAAa,CAAC,EAAGH,EAAUE,EAAS,OAAO,CAAC,EAAG,EACzI,OAAOE,CACX,CACA,SAASC,EAAQ9H,EAAQ+H,EAAUH,EAAe,CAC9C,IAAII,EAAUD,EAAS,IAAI,SAAUJ,EAAU,CAC3C,IAAIE,EAAUH,EAAWC,EAAUC,CAAa,EAChD,SAAWd,EAAO,MAAM9G,EAAQ6H,CAAO,CAC/C,CAAK,EACD,OAAO,UAAqB,CACxBG,EAAQ,QAAQ,SAAUC,EAAQ,CAAE,OAAOA,EAAQ,EAAG,CACzD,CACL,CACAV,OAAAA,EAAA,QAAkBO,sDCzClB,OAAO,eAAcI,EAAU,aAAc,CAAE,MAAO,GAAM,EAC5DA,EAAkB,QAAAA,EAAA,KAAe,OACjC,IAAIpB,EAA0BU,GAAA,EAC9B,OAAO,eAAeU,EAAS,OAAQ,CAAE,WAAY,GAAM,IAAK,UAAY,CAAE,OAAOpB,EAAO,IAAO,CAAA,CAAE,EACrG,IAAIqB,EAAkCC,GAAA,EACtC,OAAO,eAAeF,EAAS,UAAW,CAAE,WAAY,GAAM,IAAK,UAAY,CAAE,OAAOC,EAAW,OAAQ,CAAI,CAAA,sBCJpGE,GAAwB,sBCD5B,SAASC,GAAkBtI,EAAQ,CACxC,OAAOA,aAAkB,SAAWA,EAAO,aAAaqI,EAAqB,CAC/E,CCDO,SAASE,GAAmClH,EAAQ,CAEzD,IAAImH,EAAwB,SAAS,kBAAkBnH,EAAO,EAAGA,EAAO,CAAC,EACvEoH,EAAyBhC,GAAe+B,EAAuB,CAAC,EAChEE,EAAMD,EAAuB,CAAC,EAC9BE,EAASF,EAAuB,CAAC,EACnC,OAAKC,EAGDJ,GAAkBI,CAAG,EAChBC,GAAgD,KAElDD,EALE,IAMX,CCbO,IAAIE,GAAY,WCDvB,SAAS5H,GAAQ,EAAG,EAAG,CAAE,IAAI,EAAI,OAAO,KAAK,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIN,EAAI,OAAO,sBAAsB,CAAC,EAAG,IAAMA,EAAIA,EAAE,OAAO,SAAUO,EAAG,CAAE,OAAO,OAAO,yBAAyB,EAAGA,CAAC,EAAE,UAAa,CAAA,GAAI,EAAE,KAAK,MAAM,EAAGP,CAAC,EAAK,OAAO,CAAE,CAC7P,SAASQ,GAAc,EAAG,CAAE,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAE,IAAI,EAAY,UAAU,CAAC,GAAnB,KAAuB,UAAU,CAAC,EAAI,CAAA,EAAI,EAAI,EAAIF,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUC,EAAG,CAAEF,EAAgB,EAAGE,EAAG,EAAEA,CAAC,CAAC,CAAI,CAAA,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAID,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,EAAG,CAAE,OAAO,eAAe,EAAGA,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAI,CAAA,CAAE,CAAG,OAAO,CAAE,CAIrb,IAAI4H,EAAe,EACfC,GAAmBD,EAAe,EAWtC,SAASE,GAAoBC,EAAO,CAClC,MAAO,CACL,EAAG,KAAK,MAAMA,EAAM,CAAC,EACrB,EAAG,KAAK,MAAMA,EAAM,CAAC,CACtB,CACH,CAMA,SAASC,GAA2BD,EAAO,CACzC,MAAO,CACL,EAAGA,EAAM,EAAIF,GACb,EAAGE,EAAM,EAAIF,EACd,CACH,CAMA,SAASI,GAA+BF,EAAO,CAC7C,MAAO,CACL,EAAG,KAAK,IAAIA,EAAM,EAAG,CAAC,EACtB,EAAG,KAAK,IAAIA,EAAM,EAAG,CAAC,CACvB,CACH,CAMA,SAASG,GAA8BH,EAAO,CAC5C,MAAO,CACL,EAAG,KAAK,IAAIA,EAAM,EAAG,OAAO,WAAaH,CAAY,EACrD,EAAG,KAAK,IAAIG,EAAM,EAAG,OAAO,YAAcH,CAAY,CACvD,CACH,CAKA,SAASO,GAAmB3H,EAAM,CAChC,IAAIJ,EAASI,EAAK,OACduH,EAAQG,GAA8BD,GAA+BD,GAA2BF,GAAoB1H,CAAM,CAAC,CAAC,CAAC,EAIjI,OAAO,QAAQ,SAAS,CACtB,EAAG2H,EAAM,EACT,EAAGA,EAAM,EACT,MAAOH,EACP,OAAQA,CACZ,CAAG,CACH,CACA,SAASQ,GAAcjH,EAAO,CAC5B,IAAIkH,EAAalH,EAAM,WACvB,MAAO,CACL,KAAM,GAAG,OAAOkH,EAAW,KAAM,IAAI,EACrC,IAAK,GAAG,OAAOA,EAAW,IAAK,IAAI,EACnC,MAAO,GAAG,OAAOA,EAAW,MAAO,IAAI,EACvC,OAAQ,GAAG,OAAOA,EAAW,OAAQ,IAAI,CAC1C,CACH,CACA,SAASC,GAAS/E,EAAO,CACvB,IAAInD,EAASmD,EAAM,OACjB8E,EAAa9E,EAAM,WACrB,OAEEnD,EAAO,GAAKiI,EAAW,GAAKjI,EAAO,GAAKiI,EAAW,EAAIA,EAAW,OAElEjI,EAAO,GAAKiI,EAAW,GAAKjI,EAAO,GAAKiI,EAAW,EAAIA,EAAW,MAEtE,CAkCA,SAASE,GAAc9E,EAAO,CAC5B,IAAI+E,EAAU/E,EAAM,QAChBlE,EAAU,SAAS,cAAc,KAAK,EAC1CA,EAAQ,aAAa6H,GAAuB,MAAM,EAGlD,IAAIiB,EAAaF,GAAmB,CAClC,OAAQK,CACZ,CAAG,EACD,OAAO,OAAOjJ,EAAQ,MAAOU,GAAcA,GAAc,CAKvD,gBAAiB,cACjB,SAAU,QAEV,QAAS,EACT,OAAQ,EACR,UAAW,YACZ,EAAEmI,GAAc,CACf,WAAYC,CAChB,CAAG,CAAC,EAAG,CAAA,EAAI,CAGP,cAAe,OAIf,OAAQV,EACZ,CAAG,CAAC,EACF,SAAS,KAAK,YAAYpI,CAAO,EAQjC,IAAIkJ,EAAoB3C,EAAI,KAAC,OAAQ,CACnC,KAAM,cACN,SAAU,SAAkB4C,EAAO,CACjC,IAAItI,EAAS,CACX,EAAGsI,EAAM,QACT,EAAGA,EAAM,OACV,EACDL,EAAaF,GAAmB,CAC9B,OAAQ/H,CAChB,CAAO,EACD,OAAO,OAAOb,EAAQ,MAAO6I,GAAc,CACzC,WAAYC,CACpB,CAAO,CAAC,CACH,EAED,QAAS,CACP,QAAS,EACf,CACA,CAAG,EACD,OAAO,SAAgBM,EAAO,CAC5B,IAAIC,EAAUD,EAAM,QAMpB,GAJAF,EAAmB,EAIfH,GAAS,CACX,OAAQM,EACR,WAAYP,CAClB,CAAK,EAAG,CACF9I,EAAQ,OAAQ,EAChB,MACN,CACI,SAAS8C,GAAU,CACjBwG,EAAsB,EACtBtJ,EAAQ,OAAQ,CACtB,CACI,IAAIsJ,EAAuBhC,UAAQ,OAAQ,CAAC,CAC1C,KAAM,cACN,SAAUxE,CAChB,EAAO,CACD,KAAM,cACN,SAAUA,CAChB,EAAO,CACD,KAAM,UACN,SAAUA,CAChB,EAAO,CACD,KAAM,WACN,SAAUA,CACX,EAED,CACE,KAAM,YACN,SAAUA,CACX,EAKD,CACE,KAAM,YACN,SAAUA,CAChB,EAAO,CACD,KAAM,WACN,SAAUA,CAChB,CAIA,EAAO,CAED,QAAS,EACf,CAAK,CACF,CACH,CACO,SAASyG,IAAkB,CAChC,IAAIC,EAAoB,KACxB,SAASC,GAAa,CAIpB,OAAAD,EAAoB,KACbjD,EAAAA,KAAK,OAAQ,CAClB,KAAM,cACN,SAAU,SAAkB4C,EAAO,CACjCK,EAAoB,CAClB,EAAGL,EAAM,QACT,EAAGA,EAAM,OACV,CACF,EAGD,QAAS,CACP,QAAS,EACjB,CACA,CAAK,CACL,CACE,SAASO,GAAoB,CAC3B,IAAIC,EAAS,KACb,OAAO,SAAqBC,EAAO,CACjC,IAAIC,EAAYD,EAAM,UACpBE,EAAUF,EAAM,QAGlB,GAAIC,IAAc,cAAe,CAC/B,IAAIzI,EAAQ0I,EAAQ,SAAS,QAAQ,MAIjCb,EAAUO,GAAiF,CAC7F,EAAGpI,EAAM,QACT,EAAGA,EAAM,OACV,EAKDuI,EAASX,GAAc,CACrB,QAASC,CACnB,CAAS,CACT,CACM,GAAIY,IAAc,SAAU,CAC1B,IAAIE,EACAC,EAASF,EAAQ,SAAS,QAAQ,OACrCC,EAAUJ,KAAY,MAAQI,IAAY,QAAUA,EAAQ,CAC3D,QAAS,CACP,EAAGC,EAAO,QACV,EAAGA,EAAO,OACtB,CACA,CAAS,EACDL,EAAS,KAGTH,EAAoB,IAC5B,CACK,CACL,CACE,MAAO,CACL,WAAYC,EACZ,kBAAmBC,CACpB,CACH,CCjTA,SAASO,GAAmBxJ,EAAG,CAC7B,GAAI,MAAM,QAAQA,CAAC,EAAG,OAAOsF,GAAiBtF,CAAC,CACjD,CCHA,SAASyJ,GAAiBzJ,EAAG,CAC3B,GAAmB,OAAO,OAAtB,KAAwCA,EAAE,OAAO,QAAQ,GAAzB,MAAsCA,EAAE,YAAY,GAAtB,KAAyB,OAAO,MAAM,KAAKA,CAAC,CAChH,CCFA,SAAS0J,IAAqB,CAC5B,MAAM,IAAI,UAAU;AAAA,mFAAsI,CAC5J,CCEA,SAASC,GAAmB3J,EAAG,CAC7B,OAAO4J,GAAkB5J,CAAC,GAAK6J,GAAgB7J,CAAC,GAAK2F,GAA2B3F,CAAC,GAAK8J,GAAmB,CAC3G,CCLO,SAASC,EAAKlF,EAAI,CACvB,IAAImF,EAAQ,KACZ,OAAO,UAAmB,CACxB,GAAI,CAACA,EAAO,CACV,QAAStF,EAAO,UAAU,OAAQuF,EAAO,IAAI,MAAMvF,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/EqF,EAAKrF,CAAI,EAAI,UAAUA,CAAI,EAE7B,IAAI9B,EAAS+B,EAAG,MAAM,KAAMoF,CAAI,EAChCD,EAAQ,CACN,OAAQlH,CACT,CACP,CACI,OAAOkH,EAAM,MACd,CACH,CCRW,IAAAE,GAAYH,EAAK,UAAqB,CAIxC,OAAA,UAAU,UAAU,SAAS,SAAS,CAC/C,CAAC,ECFUI,EAAWJ,EAAK,UAAoB,CAIzC,IAAAK,EAAa,UACfC,EAAYD,EAAW,UACzB,OAAOC,EAAU,SAAS,aAAa,GAAK,CAACA,EAAU,SAAS,QAAQ,CAC1E,CAAC,ECLGC,GAAU,CACZ,gBAAiB,OAAO,SAAS,EACjC,iBAAkB,OAAO,UAAU,CACrC,EAQO,SAASC,GAAwBpJ,EAAO,CAC7C,IAAIqJ,EAAYrJ,EAAM,UAClB,OAACgJ,IAGEK,EAAU,eAAeF,GAAQ,eAAe,EAF9C,EAGX,EACC,UAAqB,CAWhB,GATA,OAAO,OAAW,KASlB,CAACH,IACH,OAEF,SAASM,GAAkB,CAClB,MAAA,CACL,WAAY,EACZ,aAAc,EAChB,CAAA,CAEF,IAAIC,EAAQD,EAAgB,EAC5B,SAASE,GAAa,CACpBD,EAAQD,EAAgB,CAAA,CAwB1B5D,EAAA,QAAQ,OAAQ,CAAC,CACf,KAAM,YACN,SAAU,UAAoB,CAC5B6D,EAAM,WAAa,EAEnBA,EAAM,aAAe,EAAA,CAEvB,EACC,CACD,KAAM,OACN,SAAUC,CAAA,EACT,CACD,KAAM,UACN,SAAUA,CAAA,EACT,CACD,KAAM,YACN,SAAU,SAAkBjC,EAAO,CAC7B,CAACgC,EAAM,cAAgBA,EAAM,aAAe,IAIxChC,EAAA4B,GAAQ,gBAAgB,EAAI,IAEpCI,EAAM,aAAe,GACfA,EAAA,YAAA,CACR,EACC,CACD,KAAM,YACN,SAAU,SAAkBhC,EAAO,CAC3BgC,EAAA,aACFA,EAAM,cAAgBA,EAAM,aAAe,IAIvChC,EAAA4B,GAAQ,eAAe,EAAI,GACjCI,EAAM,aAAe,GACvB,CACF,CACD,EAGD,CACE,QAAS,EAAA,CACV,CACH,GAAG,EClHH,SAASE,GAAW7L,EAAQ,CAC1B,MAAO,aAAcA,CACvB,CAKO,SAAS8L,GAAoBC,EAAa,CAC/C,OAAOF,GAAWE,CAAW,GAAKA,EAAY,gBAAkB,QAClE,CCXO,SAASC,GAAgBvK,EAAM,CACpC,IAAIgK,EAAYhK,EAAK,UACjB0D,EAAOsG,EAAU,KACnBQ,EAAgBR,EAAU,cAC5B,OAAItG,IAAS,YACJ,GAELiG,EAAQ,EACHI,GAAwB,CAC7B,UAAWC,CACjB,CAAK,EAICQ,GAAiB,KACZ,GAeLd,GAAS,EACJW,GAAoBG,CAAa,EAenCA,aAAyB,iBAClC,CCnDO,SAASC,GAA0BzK,EAAM,CAC9C,IAAI0K,EAAY1K,EAAK,UACrB,MAAO,CAqBP,CACE,KAAM,cACN,SAAU,UAAY,CACpB,IAAI2K,EAAY,EAChB,OAAO,UAAoB,CAEzB,GAAIA,EAAY,GAAI,CAClBA,IACA,MACV,CACQD,EAAW,CACZ,CACF,EAAA,CACF,EAMD,CACE,KAAM,cACN,SAAUA,CACd,CAAG,CACH,CC9CO,SAASE,EAAS1C,EAAO,CAC9B,MAAO,CACL,OAAQA,EAAM,OACd,OAAQA,EAAM,OACd,QAASA,EAAM,QACf,QAASA,EAAM,QACf,QAASA,EAAM,QACf,SAAUA,EAAM,SAChB,QAASA,EAAM,QACf,QAASA,EAAM,QACf,MAAOA,EAAM,MACb,MAAOA,EAAM,KACd,CACH,CCbA,IAAI2C,GAAU,SAAiBxG,EAAI,CACjC,IAAIyG,EAAW,CAAE,EACbC,EAAU,KAEVC,EAAY,UAAqB,CACnC,QAAS9G,EAAO,UAAU,OAAQuF,EAAO,IAAI,MAAMvF,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/EqF,EAAKrF,CAAI,EAAI,UAAUA,CAAI,EAG7B0G,EAAWrB,EAEP,CAAAsB,IAIJA,EAAU,sBAAsB,UAAY,CAC1CA,EAAU,KACV1G,EAAG,MAAM,OAAQyG,CAAQ,CAC/B,CAAK,EACF,EAED,OAAAE,EAAU,OAAS,UAAY,CACxBD,IAIL,qBAAqBA,CAAO,EAC5BA,EAAU,KACX,EAEMC,CACT,EC9BIC,GAAiBJ,GAAQ,SAAUxG,EAAI,CACzC,OAAOA,EAAI,CACb,CAAC,EACG6G,GAAY,UAAY,CAC1B,IAAIC,EAAY,KAChB,SAASC,EAAS/G,EAAI,CACpB,IAAI0G,EAAU,sBAAsB,UAAY,CAC9CI,EAAY,KACZ9G,EAAI,CACV,CAAK,EACD8G,EAAY,CACV,QAASJ,EACT,GAAI1G,CACL,CACL,CACE,SAASgH,GAAQ,CACXF,IACF,qBAAqBA,EAAU,OAAO,EACtCA,EAAU,GAAI,EACdA,EAAY,KAElB,CACE,MAAO,CACL,SAAUC,EACV,MAAOC,CACR,CACH,EAAG,EACI,SAASC,GAAatL,EAAM,CACjC,IAAIuL,EAASvL,EAAK,OAChBgI,EAAUhI,EAAK,QACfwL,EAAgBxL,EAAK,cACnByL,EAAW,CACb,YAAa,CAAA,CACd,EACD,SAASC,EAAajC,EAAM,CAC1B+B,EAAc/B,CAAI,EAClBgC,EAAW,CACT,YAAahC,EAAK,QAAQ,SAAS,QAAQ,WAC5C,CACL,CACE,IAAIkC,EAAW,CACb,MAAO,SAAehL,EAAO,CAC3B,IAAIiL,EAAqBjL,EAAM,mBAG3BkL,EAAW,CACb,QAAS7D,EACT,SAAUyD,EACV,QAASzD,CACV,EAGD0D,EAAa,CACX,UAAW,wBACX,QAAS,CACP,OAAQH,EACR,SAAUM,EACV,mBAAoBD,CAC9B,CACA,CAAO,EACDV,GAAU,SAAS,UAAY,CAC7BQ,EAAa,CACX,UAAW,cACX,QAAS,CACP,OAAQH,EACR,SAAUM,CACtB,CACA,CAAS,CACT,CAAO,CACF,EACD,WAAY,SAAoB9I,EAAO,CACrC,IAAIqF,EAAUrF,EAAM,QACpBmI,GAAU,MAAO,EACjBD,GAAe,OAAQ,EACvBS,EAAa,CACX,UAAW,qBACX,QAAS,CACP,OAAQH,EACR,SAAU,CACR,QAASvD,EACT,SAAUyD,EACV,QAASrD,CACrB,CACA,CACA,CAAO,CACF,EACD,KAAM,SAAcnF,EAAO,CACzB,IAAImF,EAAUnF,EAAM,QACpBgI,GAAe,UAAY,CACzBC,GAAU,MAAO,EACjB,IAAIW,EAAW,CACb,QAAS7D,EACT,SAAUyD,EACV,QAASrD,CACV,EACDsD,EAAa,CACX,UAAW,SACX,QAAS,CACP,OAAQH,EACR,SAAUM,CACtB,CACA,CAAS,CACT,CAAO,CACF,EACD,KAAM,SAAc1D,EAAO,CACzB,IAAIC,EAAUD,EAAM,QAClB2D,EAAuB3D,EAAM,qBAC/B+C,GAAU,MAAO,EACjBD,GAAe,OAAQ,EACvBS,EAAa,CACX,UAAW,SACX,QAAS,CACP,OAAQI,GAA0FP,EAClG,SAAU,CACR,QAASnD,EACT,SAAUqD,EACV,QAASzD,CACrB,CACA,CACA,CAAO,CACP,CACG,EACD,OAAO2D,CACT,CCpHA,IAAII,GAAc,CAChB,SAAU,EACZ,EACA,SAASC,IAAW,CAClB,MAAO,CAACD,GAAY,QACtB,CACA,SAASE,GAAsB/D,EAAO,CACpC,OAAIA,EAAM,aAGDA,EAAM,aAAa,aAAa,KAAKA,EAAM,YAAY,EAEzD,IACT,CACA,SAASgE,GAAoBlM,EAAM,CACjC,IAAIoI,EAAUpI,EAAK,QACjBmM,EAAOnM,EAAK,KACd,GAAIoI,EAAQ,SAAW+D,EAAK,OAC1B,MAAO,GAIT,QAAS/M,EAAI,EAAGA,EAAIgJ,EAAQ,OAAQhJ,IAClC,GAAIgJ,EAAQhJ,CAAC,EAAE,UAAY+M,EAAK/M,CAAC,EAAE,QACjC,MAAO,GAGX,MAAO,EACT,CACA,SAASgN,GAAMzL,EAAO,CACpB,IAAIuH,EAAQvH,EAAM,MAChB0L,EAAW1L,EAAM,SACjB2L,EAAqB3L,EAAM,mBAC3B6K,EAAgB7K,EAAM,cACxB,GAAI,CAACqL,GAAQ,EACX,OAEF,IAAIhE,EAAUuE,GAAiB,CAC7B,MAAOrE,EACP,SAAUmE,EACV,mBAAoBC,CACxB,CAAG,EACDP,GAAY,SAAW,GACvB,IAAI7B,EAAQ,CACV,QAASlC,CACV,EAGDwE,GAAqB,CACnB,MAAOtE,EACP,QAASF,EAAQ,WACrB,CAAG,EACD,IAAI2D,EAAWL,GAAa,CAC1B,OAAQe,EAAS,QACjB,cAAeb,EACf,QAASxD,CACb,CAAG,EACD,SAASyE,EAAYN,EAAM,CAEzB,IAAIO,EAAaR,GAAoB,CACnC,QAAShC,EAAM,QAAQ,YACvB,KAAMiC,EAAK,WACjB,CAAK,EAKDjC,EAAM,QAAUiC,EACZO,GACFf,EAAS,WAAW,CAClB,QAASzB,EAAM,OACvB,CAAO,CAEP,CACE,SAASyC,EAAczE,EAAO,CAC5B,IAAI/H,EAAQyK,EAAS1C,CAAK,EAItB3J,EAASsI,GAAkBqB,EAAM,MAAM,EAAIpB,GAAmC,CAChF,EAAG3G,EAAM,QACT,EAAGA,EAAM,OACf,CAAK,EAAI+H,EAAM,OACP0E,EAAkBN,EAAmB,CACvC,OAAQ/N,EACR,MAAO4B,EACP,OAAQkM,EAAS,QACjB,QAASnC,EAAM,QAAQ,WAC7B,CAAK,EACG0C,EAAgB,SAElB1E,EAAM,eAAgB,EACtBsE,GAAqB,CACnB,MAAOtE,EACP,QAAS0E,CACjB,CAAO,GAEHH,EAAY,CACV,YAAaG,EACb,MAAOzM,CACb,CAAK,CACL,CACE,SAAS0M,GAAS,CASZ3C,EAAM,QAAQ,YAAY,QAC5BuC,EAAY,CACV,YAAa,CAAE,EACf,MAAOvC,EAAM,QAAQ,KAC7B,CAAO,EAEHyB,EAAS,KAAK,CACZ,QAASzB,EAAM,QACf,qBAAsB,IAC5B,CAAK,EACDxB,EAAQ,CACZ,CACE,SAASA,GAAS,CAChBqD,GAAY,SAAW,GACvBe,EAAc,CAClB,CACE,IAAIA,EAAezG,UAAQ,OAAQ,CAAC,CAOlC,KAAM,WACN,SAAU,SAAkB6B,EAAO,CASjCyE,EAAczE,CAAK,EAInByD,EAAS,KAAK,CACZ,QAASzB,EAAM,OACvB,CAAO,CACP,CACA,EAAK,CACD,KAAM,YACN,SAAUyC,CACd,EAAK,CACD,KAAM,YACN,SAAU,SAAkBzE,EAAO,CAC5BqC,GAAgB,CACnB,UAAWrC,CACnB,CAAO,IA6CDuE,EAAY,CACV,MAAOvC,EAAM,QAAQ,MACrB,YAAa,CAAA,CACrB,CAAO,EACGmC,EAAS,cAAgB,YAC3BQ,EAAQ,EAEhB,CACA,EAAK,CAED,KAAM,OACN,SAAU,SAAkB3E,EAAO,CAmBjC,GAfAgC,EAAM,QAAU,CACd,YAAaA,EAAM,QAAQ,YAC3B,MAAOU,EAAS1C,CAAK,CACtB,EAYG,CAACgC,EAAM,QAAQ,YAAY,OAAQ,CACrC2C,EAAQ,EACR,MACR,CACM3E,EAAM,eAAgB,EAGtBsE,GAAqB,CACnB,MAAOtE,EACP,QAASgC,EAAM,QAAQ,WAC/B,CAAO,EACDyB,EAAS,KAAK,CACZ,QAASzB,EAAM,QAGf,qBAAsBmC,EAAS,OAAS,WAAaA,EAAS,eAAenE,CAAK,EAAI,IAC9F,CAAO,EACDQ,EAAQ,CACd,CACA,EAAK,CAUD,KAAM,UACN,SAAU,SAAkBR,EAAO,CAKjCgC,EAAM,QAAU,CACd,YAAaA,EAAM,QAAQ,YAC3B,MAAOU,EAAS1C,CAAK,CACtB,EACD2E,EAAQ,CACd,CACA,CAAG,EAAE,OAAO1D,GAAmBsB,GAA0B,CACrD,UAAWoC,CACZ,CAAA,CAAC,CAAC,EAKH,CACE,QAAS,EACb,CAAG,EACDlB,EAAS,MAAM,CACb,mBAAoBM,GAAsB/D,CAAK,CACnD,CAAG,CACH,CACA,SAASsE,GAAqBzJ,EAAO,CACnC,IAAIgK,EACA7E,EAAQnF,EAAM,MAChBqF,EAAUrF,EAAM,QAEdiK,GAAaD,EAAY3E,EAAQ,CAAC,KAAO,MAAQ2E,IAAc,OAAS,OAASA,EAAU,WAC3FC,GAAa,MAAQ9E,EAAM,eAC7BA,EAAM,aAAa,WAAa8E,EAEpC,CACA,SAAST,GAAiBtJ,EAAO,CAC/B,IAAIiF,EAAQjF,EAAM,MAChBoJ,EAAWpJ,EAAM,SACjBqJ,EAAqBrJ,EAAM,mBACzB9C,EAAQyK,EAAS1C,CAAK,EAI1B,GAAImE,EAAS,cAAgB,WAC3B,MAAO,CACL,MAAOlM,EACP,YAAa,CAAA,CACd,EAEH,IAAI8M,EAAcX,EAAmB,CACnC,MAAOnM,EACP,OAAQkM,EAAS,QACjB,OAAQnE,EAAM,OACd,QAAS,CAAA,CACb,CAAG,EACD,MAAO,CACL,MAAO/H,EACP,YAAa8M,CACd,CACH,CACO,IAAIC,GAAY,CACrB,SAAUlB,GACV,MAAOI,EACT,EC9UIe,GAAS,IAAI,IACjB,SAASC,GAAcpN,EAAM,CAC3B,IAAIqN,EAAUrN,EAAK,QACjBsN,EAAQtN,EAAK,MACXuN,EAAQJ,GAAO,IAAIE,CAAO,EAC9B,GAAIE,EACF,OAAAA,EAAM,aACCA,EAET,IAAIvF,EAAU,CACZ,QAASqF,EACT,QAASC,EAAO,EAChB,WAAY,CACb,EACD,OAAAH,GAAO,IAAIE,EAASrF,CAAO,EACpBA,CACT,CACO,SAASwF,GAAS/D,EAAM,CAC7B,IAAI8D,EAAQH,GAAc3D,CAAI,EAC9B,OAAO,UAAsB,CAC3B8D,EAAM,aACF,EAAAA,EAAM,WAAa,KAIvBA,EAAM,QAAS,EACfJ,GAAO,OAAO1D,EAAK,OAAO,EAC3B,CACH,CC9BO,SAASgE,GAAa1O,EAASiB,EAAM,CAC1C,IAAI0N,EAAY1N,EAAK,UACnBrC,EAAQqC,EAAK,MACf,OAAAjB,EAAQ,aAAa2O,EAAW/P,CAAK,EAC9B,UAAY,CACjB,OAAOoB,EAAQ,gBAAgB2O,CAAS,CACzC,CACH,CCLA,SAASnO,GAAQ,EAAG,EAAG,CAAM,IAAA,EAAI,OAAO,KAAK,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAM,IAAAN,EAAI,OAAO,sBAAsB,CAAC,EAAG,IAAMA,EAAIA,EAAE,OAAO,SAAUO,EAAG,CAAE,OAAO,OAAO,yBAAyB,EAAGA,CAAC,EAAE,UAAA,CAAa,GAAI,EAAE,KAAK,MAAM,EAAGP,CAAC,CAAA,CAAY,OAAA,CAAG,CAC9P,SAASQ,EAAc,EAAG,CAAE,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAM,IAAA,EAAY,UAAU,CAAC,GAAnB,KAAuB,UAAU,CAAC,EAAI,CAAC,EAAO,EAAA,EAAIF,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUC,EAAG,CAAEF,EAAgB,EAAGE,EAAG,EAAEA,CAAC,CAAC,CAAA,CAAI,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAID,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,EAAG,CAAE,OAAO,eAAe,EAAGA,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAA,CAAI,CAAA,CAAY,OAAA,CAAG,CACtb,SAASmO,GAA2BnO,EAAGlB,EAAG,CAAM,IAAA,EAAmB,OAAO,OAAtB,KAAgCkB,EAAE,OAAO,QAAQ,GAAKA,EAAE,YAAY,EAAG,GAAI,CAAC,EAAG,CAAE,GAAI,MAAM,QAAQA,CAAC,IAAM,EAAIqF,GAA4BrF,CAAC,IAAMlB,EAAuC,CAAE,IAAMkB,EAAI,GAAI,IAAIoO,EAAK,EAAGC,EAAI,UAAa,CAAC,EAAG,MAAO,CAAE,EAAGA,EAAG,EAAG,UAAa,CAAE,OAAOD,GAAMpO,EAAE,OAAS,CAAE,KAAM,EAAA,EAAO,CAAE,KAAM,GAAI,MAAOA,EAAEoO,GAAI,CAAE,CAAA,EAAM,EAAG,SAAWpO,EAAG,CAAQA,MAAAA,CAAA,EAAM,EAAGqO,CAAE,CAAA,CAAW,MAAA,IAAI,UAAU;AAAA,mFAAuI,CAAA,CAAS,IAAA5O,EAAGuB,EAAI,GAAIkE,EAAI,GAAW,MAAA,CAAE,EAAG,UAAa,CAAM,EAAA,EAAE,KAAKlF,CAAC,CAAA,EAAM,EAAG,UAAa,CAAMA,IAAAA,EAAI,EAAE,KAAK,EAAU,OAAAgB,EAAIhB,EAAE,KAAMA,CAAA,EAAM,EAAG,SAAWA,EAAG,CAAEkF,EAAI,GAAIzF,EAAIO,CAAA,EAAM,EAAG,UAAa,CAAM,GAAA,CAAEgB,GAAa,EAAE,QAAV,MAAoB,EAAE,OAAO,CAAA,QAAK,CAAU,GAAIkE,EAAS,MAAAzF,CAAA,CAAG,CAAI,CAAG,CACr1B,SAAS4F,GAA4BrF,EAAGgB,EAAG,CAAE,GAAIhB,EAAG,CAAE,GAAgB,OAAOA,GAAnB,SAA6B,OAAAoF,GAAkBpF,EAAGgB,CAAC,EAAO,IAAA,EAAI,CAAG,EAAA,SAAS,KAAKhB,CAAC,EAAE,MAAM,EAAG,EAAE,EAAU,OAAa,IAAb,UAAkBA,EAAE,cAAgB,EAAIA,EAAE,YAAY,MAAiB,IAAV,OAAyB,IAAV,MAAc,MAAM,KAAKA,CAAC,EAAoB,IAAhB,aAAqB,2CAA2C,KAAK,CAAC,EAAIoF,GAAkBpF,EAAGgB,CAAC,EAAI,MAAA,CAAU,CACzX,SAASoE,GAAkBpF,EAAGgB,EAAG,EAAWA,GAAR,MAAaA,EAAIhB,EAAE,UAAYgB,EAAIhB,EAAE,QAAS,QAASlB,EAAI,EAAG,EAAI,MAAMkC,CAAC,EAAGlC,EAAIkC,EAAGlC,IAAK,EAAEA,CAAC,EAAIkB,EAAElB,CAAC,EAAU,OAAA,CAAG,CAInJ,SAASwP,GAAYC,EAAO,CAC1B,OAAOA,EAAM,MAAM,CAAC,EAAE,QAAQ,CAChC,CACO,SAASC,GAAehO,EAAM,CACnC,IAAIqN,EAAUrN,EAAK,QACjBiO,EAAoBjO,EAAK,kBACvBkO,MAAe,QACfC,EAAoB,wBAAwB,OAAOd,CAAO,EAC1De,EAAqB,IAAI,OAAOD,EAAmB,GAAG,EAC1D,SAASE,EAAc5E,EAAM,CAClB,OAAAyE,EAAA,IAAIzE,EAAK,QAASA,CAAI,EACxB,UAAY,CACV,OAAAyE,EAAS,OAAOzE,EAAK,OAAO,CACrC,CAAA,CAEF,SAAS6E,EAAuB7E,EAAM,CAiBpC,IAAI5H,EAAUoC,GAAQwJ,GAAahE,EAAK,QAAS,CAC/C,UAAW0E,EACX,MAAO,MAAA,CACR,EAAGE,EAAc5E,CAAI,CAAC,EAIvB,OAAOF,EAAK1H,CAAO,CAAA,CAErB,SAAS0M,EAAqB5N,EAAO,CAC/B,IAAA6N,EAAeC,EAAgBC,EAAqBC,EACpDpD,EAAS5K,EAAM,OACjBpC,EAASoC,EAAM,OACfR,EAAQQ,EAAM,MACdiO,EAAejO,EAAM,OACrB2B,EAASsM,IAAiB,OAAS,CAAA,EAAKA,EAC1C,GAAIrQ,GAAU,KACL,OAAA+D,EAEL,GAAA,EAAE/D,aAAkB,SAItB,OAAIA,aAAkB,KACbgQ,EAAqB,CAC1B,OAAAhD,EACA,OAAQhN,EAAO,cACf,MAAA4B,EACA,OAAAmC,CAAA,CACD,EAKIA,EAEL,IAAAuM,EAAUtQ,EAAO,QAAQ6P,CAAkB,EAG/C,GAAIS,GAAW,KACN,OAAAvM,EAEL,IAAAmH,EAAOyE,EAAS,IAAIW,CAAO,EAI/B,GAAIpF,GAAQ,KACH,OAAAnH,EAET,IAAIwM,EAAW,CACb,MAAA3O,EACA,OAAAoL,EACA,QAAS9B,EAAK,OAChB,EAIA,GAAIA,EAAK,SAAW,CAACA,EAAK,QAAQqF,CAAQ,EACxC,OAAOP,EAAqB,CAC1B,OAAAhD,EACA,OAAQ9B,EAAK,QAAQ,cACrB,MAAAtJ,EACA,OAAAmC,CAAA,CACD,EAIH,IAAIyM,GAAQP,GAAiBC,EAAiBhF,EAAK,WAAa,MAAQgF,IAAmB,OAAS,OAASA,EAAe,KAAKhF,EAAMqF,CAAQ,KAAO,MAAQN,IAAkB,OAASA,EAAgB,CAAC,EACtMQ,GAAcN,GAAuBC,EAAuBlF,EAAK,iBAAmB,MAAQkF,IAAyB,OAAS,OAASA,EAAqB,KAAKlF,EAAMqF,CAAQ,KAAO,MAAQJ,IAAwB,OAASA,EAAsBT,EACrPgB,EAAS,CACX,KAAAF,EACA,QAAStF,EAAK,QACd,WAAAuF,EAGA,wBAAyB,EAC3B,EACA,OAAOT,EAAqB,CAC1B,OAAAhD,EACA,OAAQ9B,EAAK,QAAQ,cACrB,MAAAtJ,EAEA,OAAQ,CAAG,EAAA,OAAOgJ,GAAmB7G,CAAM,EAAG,CAAC2M,CAAM,CAAC,CAAA,CACvD,CAAA,CAEH,SAASC,EAAcnM,EAAO,CAC5B,IAAI6F,EAAY7F,EAAM,UACpB8F,EAAU9F,EAAM,QACdoM,EAAYxB,GAA2B9E,EAAQ,SAAS,QAAQ,WAAW,EAC7EuG,EACE,GAAA,CACG,IAAAD,EAAU,IAAK,EAAEC,EAAQD,EAAU,KAAK,MAAO,CAC9C,IAAAE,EACAJ,EAASG,EAAM,MACf7B,EAAQW,EAAS,IAAIe,EAAO,OAAO,EACnCxF,EAAOhK,EAAcA,EAAc,CAAA,EAAIoJ,CAAO,EAAG,GAAI,CACvD,KAAMoG,CAAA,CACP,EACS1B,GAAA,OAA6B8B,EAAmB9B,EAAM3E,CAAS,KAAO,MAAQyG,IAAqB,QAAUA,EAAiB,KAAK9B,EAI7I9D,CAAI,CAAA,QAEC6F,EAAK,CACZH,EAAU,EAAEG,CAAG,CAAA,QACf,CACAH,EAAU,EAAE,CAAA,CACd,CAEF,IAAII,EAAU,CACZ,sBAAuBL,EACvB,OAAQA,EACR,YAAaA,EACb,OAAQA,EACR,mBAAoB,SAA4BjM,EAAO,CACrD,IAAI4F,EAAU5F,EAAM,QAChBuM,EAAY,IAAI,IAAI3G,EAAQ,SAAS,QAAQ,YAAY,IAAI,SAAUoG,EAAQ,CACjF,OAAOA,EAAO,OAAA,CACf,CAAC,EACEQ,MAAc,IACdC,EAAa/B,GAA2B9E,EAAQ,SAAS,SAAS,WAAW,EAC/E8G,EACE,GAAA,CACG,IAAAD,EAAW,IAAK,EAAEC,EAASD,EAAW,KAAK,MAAO,CACjD,IAAAE,EACAX,EAASU,EAAO,MACZF,EAAA,IAAIR,EAAO,OAAO,EAC1B,IAAI1B,EAAQW,EAAS,IAAIe,EAAO,OAAO,EACnCY,EAASL,EAAU,IAAIP,EAAO,OAAO,EACrCxF,EAAOhK,EAAcA,EAAc,CAAA,EAAIoJ,CAAO,EAAG,GAAI,CACvD,KAAMoG,CAAA,CACP,EAID,GAHA1B,GAAU,OAA6BqC,EAAwBrC,EAAM,sBAAwB,MAAQqC,IAA0B,QAAUA,EAAsB,KAAKrC,EAAO9D,CAAI,EAG3K,CAACoG,EAAQ,CACP,IAAAC,EACJvC,GAAU,OAA6BuC,EAAqBvC,EAAM,eAAiB,MAAQuC,IAAuB,QAAUA,EAAmB,KAAKvC,EAAO9D,CAAI,CAAA,CACjK,QAEK6F,EAAK,CACZI,EAAW,EAAEJ,CAAG,CAAA,QAChB,CACAI,EAAW,EAAE,CAAA,CAEf,IAAIK,EAAapC,GAA2B9E,EAAQ,SAAS,QAAQ,WAAW,EAC9EmH,EACE,GAAA,CACG,IAAAD,EAAW,IAAK,EAAEC,EAASD,EAAW,KAAK,MAAO,CACrD,IAAIE,EAAwBC,EACxBC,EAAUH,EAAO,MAErB,GAAI,CAAAP,EAAQ,IAAIU,EAAQ,OAAO,EAI3B,KAAAC,EAAQ3Q,EAAcA,EAAc,CAAA,EAAIoJ,CAAO,EAAG,GAAI,CACxD,KAAMsH,CAAA,CACP,EACGE,EAASnC,EAAS,IAAIiC,EAAQ,OAAO,EACzCE,GAAW,OAA8BJ,EAAyBI,EAAO,sBAAwB,MAAQJ,IAA2B,QAAUA,EAAuB,KAAKI,EAAQD,CAAK,EACvLC,GAAW,OAA8BH,EAAqBG,EAAO,eAAiB,MAAQH,IAAuB,QAAUA,EAAmB,KAAKG,EAAQD,CAAK,EAAA,QAE/Jd,EAAK,CACZS,EAAW,EAAET,CAAG,CAAA,QAChB,CACAS,EAAW,EAAE,CAAA,CACf,CAEJ,EACA,SAASvE,EAAc/B,EAAM,CACnB8F,EAAA9F,EAAK,SAAS,EAAEA,CAAI,CAAA,CAE9B,SAAS6G,EAAUnI,EAAO,CACpB,IAAAoD,EAASpD,EAAM,OACjB5J,EAAS4J,EAAM,OACfhI,EAAQgI,EAAM,MACdC,EAAUD,EAAM,QACdoI,EAAShC,EAAqB,CAChC,OAAAhD,EACA,OAAAhN,EACA,MAAA4B,CAAA,CACD,EAIG,GAAAoQ,EAAO,QAAUnI,EAAQ,OACpB,OAAAmI,EAaT,QAHIC,EAAqB1C,GAAY1F,CAAO,EACxCqI,EAAuB3C,GAAYyC,CAAM,EACzCG,EAAuB,CAAC,EACnBC,EAAQ,EAAGA,EAAQH,EAAmB,OAAQG,IAAS,CAC1D,IAAAC,EACAC,EAAOL,EAAmBG,CAAK,EAC/BG,EAAQL,EAAqBE,CAAK,EAItC,GAAIG,GAAS,KAAM,CACjBJ,EAAqB,KAAKI,CAAK,EAC/B,QAAA,CAQE,IAAAC,EAASL,EAAqBC,EAAQ,CAAC,EACvCK,EAAaR,EAAmBG,EAAQ,CAAC,EAI7C,GAAqDI,GAAO,UAAsEC,GAAW,QAC3I,MAKF,IAAIC,EAAc/C,EAAS,IAAI2C,EAAK,OAAO,EAG3C,GAAI,CAACI,EACH,MAEF,IAAInC,EAAW,CACb,MAAA3O,EACA,OAAAoL,EACA,QAAS0F,EAAY,OACvB,EAQI,GALAA,EAAY,SAAW,CAACA,EAAY,QAAQnC,CAAQ,GAKpD,GAAG8B,EAAwBK,EAAY,eAAiB,MAAQL,IAA0B,QAAUA,EAAsB,KAAKK,EAAanC,CAAQ,GACtJ,MASmB4B,EAAA,KAAKjR,EAAcA,EAAc,CAAA,EAAIoR,CAAI,EAAG,GAAI,CAEnE,wBAAyB,EAAA,CAC1B,CAAC,CAAA,CAIJ,OAAO/C,GAAY4C,CAAoB,CAAA,CAElC,MAAA,CACL,uBAAApC,EACA,UAAAgC,EACA,cAAA9E,CACF,CACF,CCxTA,SAASmC,GAA2BnO,EAAGlB,EAAG,CAAE,IAAI,EAAmB,OAAO,OAAtB,KAAgCkB,EAAE,OAAO,QAAQ,GAAKA,EAAE,YAAY,EAAG,GAAI,CAAC,EAAG,CAAE,GAAI,MAAM,QAAQA,CAAC,IAAM,EAAIqF,GAA4BrF,CAAC,IAAMlB,EAAuC,CAAE,IAAMkB,EAAI,GAAI,IAAIoO,EAAK,EAAGC,EAAI,UAAa,CAAA,EAAI,MAAO,CAAE,EAAGA,EAAG,EAAG,UAAa,CAAE,OAAOD,GAAMpO,EAAE,OAAS,CAAE,KAAM,EAAE,EAAK,CAAE,KAAM,GAAI,MAAOA,EAAEoO,GAAI,CAAC,CAAG,EAAI,EAAG,SAAWpO,EAAG,CAAE,MAAMA,CAAI,EAAE,EAAGqO,CAAC,CAAG,CAAG,MAAM,IAAI,UAAU;AAAA,mFAAuI,CAAI,CAAC,IAAI5O,EAAGuB,EAAI,GAAIkE,EAAI,GAAI,MAAO,CAAE,EAAG,UAAa,CAAE,EAAI,EAAE,KAAKlF,CAAC,CAAE,EAAI,EAAG,UAAa,CAAE,IAAIA,EAAI,EAAE,KAAI,EAAI,OAAOgB,EAAIhB,EAAE,KAAMA,CAAE,EAAI,EAAG,SAAWA,EAAG,CAAEkF,EAAI,GAAIzF,EAAIO,CAAE,EAAI,EAAG,UAAa,CAAE,GAAI,CAAEgB,GAAa,EAAE,QAAV,MAAoB,EAAE,OAAQ,CAAG,QAAA,CAAW,GAAIkE,EAAG,MAAMzF,CAAE,CAAI,CAAA,CAAG,CACp1B,SAAS4F,GAA4BrF,EAAGgB,EAAG,CAAE,GAAIhB,EAAG,CAAE,GAAgB,OAAOA,GAAnB,SAAsB,OAAOoF,GAAkBpF,EAAGgB,CAAC,EAAG,IAAI,EAAI,GAAG,SAAS,KAAKhB,CAAC,EAAE,MAAM,EAAG,EAAE,EAAG,OAAoB,IAAb,UAAkBA,EAAE,cAAgB,EAAIA,EAAE,YAAY,MAAiB,IAAV,OAAyB,IAAV,MAAc,MAAM,KAAKA,CAAC,EAAoB,IAAhB,aAAqB,2CAA2C,KAAK,CAAC,EAAIoF,GAAkBpF,EAAGgB,CAAC,EAAI,MAAS,CAAA,CACxX,SAASoE,GAAkBpF,EAAGgB,EAAG,EAAWA,GAAR,MAAaA,EAAIhB,EAAE,UAAYgB,EAAIhB,EAAE,QAAS,QAASlB,EAAI,EAAG,EAAI,MAAMkC,CAAC,EAAGlC,EAAIkC,EAAGlC,IAAK,EAAEA,CAAC,EAAIkB,EAAElB,CAAC,EAAG,OAAO,CAAE,CAClJ,SAASiB,GAAQ,EAAG,EAAG,CAAE,IAAI,EAAI,OAAO,KAAK,CAAC,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIN,EAAI,OAAO,sBAAsB,CAAC,EAAG,IAAMA,EAAIA,EAAE,OAAO,SAAUO,EAAG,CAAE,OAAO,OAAO,yBAAyB,EAAGA,CAAC,EAAE,UAAa,CAAA,GAAI,EAAE,KAAK,MAAM,EAAGP,CAAC,EAAK,OAAO,CAAE,CAC7P,SAASQ,GAAc,EAAG,CAAE,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAE,IAAI,EAAY,UAAU,CAAC,GAAnB,KAAuB,UAAU,CAAC,EAAI,CAAA,EAAI,EAAI,EAAIF,GAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,QAAQ,SAAUC,EAAG,CAAEF,EAAgB,EAAGE,EAAG,EAAEA,CAAC,CAAC,CAAI,CAAA,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAID,GAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAUC,EAAG,CAAE,OAAO,eAAe,EAAGA,EAAG,OAAO,yBAAyB,EAAGA,CAAC,CAAC,CAAI,CAAA,CAAE,CAAG,OAAO,CAAE,CAE9a,SAAS0R,IAAc,CAC5B,IAAIhD,EAAW,IAAI,IACfiD,EAAW,KACf,SAASC,EAAeC,EAAS,CAC1BF,IAMD,CAACE,EAAQ,YAAcA,EAAQ,WAAWF,EAAS,cAAc,IACnEA,EAAS,OAAO,IAAIE,CAAO,CAEjC,CACE,SAASC,EAAoB7H,EAAM,CAGjC,IAAI8D,EAAQ9N,GAAc,CAAE,EAAEgK,CAAI,EAClCyE,EAAS,IAAIX,CAAK,EAGlB6D,EAAe7D,CAAK,EACpB,SAAS1L,GAAU,CACjBqM,EAAS,OAAOX,CAAK,EAGjB4D,GACFA,EAAS,OAAO,OAAO5D,CAAK,CAEpC,CAII,OAAOhE,EAAK1H,CAAO,CACvB,CACE,SAAS2J,EAAcxL,EAAM,CAC3B,IAAI4I,EAAY5I,EAAK,UACnB6I,EAAU7I,EAAK,QACjB,GAAI4I,IAAc,wBAAyB,CACzCuI,EAAW,CACT,eAAgB,CACd,QAAStI,EAAQ,SAAS,QAC1B,OAAQA,EAAQ,MACjB,EACD,OAAQ,IAAI,GACb,EACD,IAAIsG,EAAYxB,GAA2BO,CAAQ,EACjDkB,EACF,GAAI,CACF,IAAKD,EAAU,EAAC,EAAI,EAAEC,EAAQD,EAAU,EAAG,GAAE,MAAO,CAClD,IAAIkC,EAAUjC,EAAM,MACpBgC,EAAeC,CAAO,CAChC,CACO,OAAQ/B,EAAK,CACZH,EAAU,EAAEG,CAAG,CACvB,QAAgB,CACRH,EAAU,EAAG,CACrB,CACA,CAGI,GAAKgC,EAUL,SADII,EAAS,MAAM,KAAKJ,EAAS,MAAM,EAC9BK,EAAK,EAAGC,EAAUF,EAAQC,EAAKC,EAAQ,OAAQD,IAAM,CAC5D,IAAIE,EAAWD,EAAQD,CAAE,EAGzB,GAAIL,EAAS,OAAO,IAAIO,CAAQ,EAAG,CACjC,IAAIC,GAEHA,EAAqBD,EAAS9I,CAAS,KAAO,MAAQ+I,IAAuB,QAAUA,EAAmB,KAAKD,EAAU7I,CAAO,CACzI,CACA,CACQD,IAAc,WAChBuI,EAAS,OAAO,MAAO,EACvBA,EAAW,MAEjB,CACE,MAAO,CACL,cAAe3F,EACf,oBAAqB8F,CACtB,CACH,CC7FO,SAASM,GAAY5R,EAAM,CAChC,IAAIqN,EAAUrN,EAAK,QACjBsN,EAAQtN,EAAK,MACb6R,EAAwB7R,EAAK,sBAC7B8R,EAAiB9R,EAAK,eACtBiO,EAAoBjO,EAAK,kBACvB+R,EAAab,GAAa,EAC1Bc,EAAgBhE,GAAe,CACjC,QAASX,EACT,kBAAmBY,CACvB,CAAG,EACD,SAASzC,EAAc/B,EAAM,CAE2CoI,IAAsBpI,CAAI,EAGhGuI,EAAc,cAAcvI,CAAI,EAGhCsI,EAAW,cAActI,CAAI,EAG2BqI,IAAerI,CAAI,CAC/E,CACE,SAAS2C,EAAMzL,EAAO,CACpB,IAAIuH,EAAQvH,EAAM,MAChB0L,EAAW1L,EAAM,SACnBuM,GAAU,MAAM,CACd,MAAOhF,EACP,SAAUmE,EACV,mBAAoB2F,EAAc,UAClC,cAAexG,CACrB,CAAK,CACL,CACE,SAAS4B,GAAgB,CACvB,SAAS6E,GAAe,CACtB,IAAIC,EAAM,CACR,SAAUhF,GAAU,SACpB,MAAOd,CACR,EACD,OAAOkB,EAAM4E,CAAG,CACtB,CACI,OAAO1E,GAAS,CACd,QAASH,EACT,MAAO4E,CACb,CAAK,CACL,CACE,MAAO,CACL,cAAe7E,EACf,WAAY4E,EAAc,uBAC1B,QAASD,EAAW,mBACrB,CACH,CCrDO,IAAII,GAAY5I,EAAK,UAAqB,CAC/C,OAAO,UAAU,UAAU,kBAAiB,EAAG,SAAS,SAAS,CACnE,CAAC,EACU6I,GAAsB,wBCDtBC,GAAgB,aCAhBC,GAAe,gBCAfC,GAA8B,uBCQrCC,OAAwB,QAC5B,SAASnE,GAAc5E,EAAM,CACT,OAAA+I,GAAA,IAAI/I,EAAK,QAASA,CAAI,EACjC,UAAmB,CACN+I,GAAA,OAAO/I,EAAK,OAAO,CACvC,CACF,CACA,IAAIgJ,GAAcnK,GAAgB,EAC9BoK,GAAUd,GAAY,CACxB,QAAS,UACT,kBAAmB,OACnB,MAAO,SAAeM,EAAK,CAMzB,OAAOjO,GAAQwO,GAAY,WAAW,EAAGnN,EAAAA,KAAK,SAAU,CACtD,KAAM,YACN,SAAU,SAAkB4C,EAAO,CACjC,IAAIyK,EAAmBC,EAAuBC,EAAwBC,EAAoBC,EAAwBC,EAClH,GAAKd,EAAI,SAAShK,CAAK,GAMnB,CAAAA,EAAM,kBAQLA,EAAM,aAaX,KAAI3J,EAAS2J,EAAM,OAIf,GAAA,EAAE3J,aAAkB,aACf,OAAA,KAIL,IAAAgP,EAAQiF,GAAkB,IAAIjU,CAAM,EAIxC,GAAI,CAACgP,EACI,OAAA,KAoCL,IAAApN,EAAQyK,EAAS1C,CAAK,EACtB4G,EAAW,CACb,QAASvB,EAAM,QACf,YAAaoF,EAAoBpF,EAAM,cAAgB,MAAQoF,IAAsB,OAASA,EAAoB,KAClH,MAAAxS,CACF,EAGA,GAAIoN,EAAM,SAAW,CAACA,EAAM,QAAQuB,CAAQ,EAE1C,OAAA5G,EAAM,eAAe,EACd,KAIT,GAAIqF,EAAM,WAAY,CAIpB,IAAI0F,EAAOnM,GAAmC,CAC5C,EAAG3G,EAAM,QACT,EAAGA,EAAM,OAAA,CACV,EAID,GAAI,CAACoN,EAAM,WAAW,SAAS0F,CAAI,EACjC,OAAA/K,EAAM,eAAe,EACd,IACT,CAsBF,IAAIgL,GAAcN,GAAyBC,EAAyBtF,EAAM,6BAA+B,MAAQsF,IAA2B,OAAS,OAASA,EAAuB,KAAKtF,EAAOuB,CAAQ,KAAO,MAAQ8D,IAA0B,OAASA,EAAwB,KACnR,GAAIM,EACO,QAAA1B,EAAK,EAAG2B,EAAkB,OAAO,QAAQD,CAAU,EAAG1B,EAAK2B,EAAgB,OAAQ3B,IAAM,CAChG,IAAI4B,EAAqBpO,GAAemO,EAAgB3B,CAAE,EAAG,CAAC,EAC5DnP,EAAM+Q,EAAmB,CAAC,EAC1BrE,EAAOqE,EAAmB,CAAC,EACvBlL,EAAA,aAAa,QAAQ7F,EAAK0M,GAA0C,EAAE,CAAA,CAc5EoD,GAAU,GAAK,CAACjK,EAAM,aAAa,MAAM,SAASmK,EAAa,GAAK,CAACnK,EAAM,aAAa,MAAM,SAASoK,EAAY,GAC/GpK,EAAA,aAAa,QAAQmK,GAAeD,EAAmB,EAuBzDlK,EAAA,aAAa,QAAQqK,GAA6B,EAAE,EAC1D,IAAI1J,EAAU,CACZ,QAAS0E,EAAM,QACf,YAAauF,EAAqBvF,EAAM,cAAgB,MAAQuF,IAAuB,OAASA,EAAqB,KACrH,MAAOC,GAA0BC,EAAyBzF,EAAM,kBAAoB,MAAQyF,IAA2B,OAAS,OAASA,EAAuB,KAAKzF,EAAOuB,CAAQ,KAAO,MAAQiE,IAA2B,OAASA,EAAyB,CAAA,CAClQ,EACI1G,EAAW,CACb,KAAM,UACN,QAAAxD,EACA,YAAa,UACf,EACAqJ,EAAI,MAAM,CACR,MAAAhK,EACA,SAAAmE,CAAA,CACD,EAAA,CACH,CACD,CAAC,CACJ,EACA,sBAAuB,SAA+BrM,EAAM,CAC1D,IAAIqT,EAAuBC,EACvB1K,EAAY5I,EAAK,UACnB6I,EAAU7I,EAAK,SAOhBqT,EAAwBb,GAAkB,IAAI3J,EAAQ,OAAO,OAAO,KAAO,MAAQwK,IAA0B,SAAWC,EAAyBD,EAAsBzK,CAAS,KAAO,MAAQ0K,IAA2B,QAAUA,EAAuB,KAAKD,EAIjQxK,CAAO,CACT,EACA,eAAgB4J,GAAY,kBAAkB,CAChD,CAAC,EACUc,GAAwBb,GAAQ,WAChCc,GAAqBd,GAAQ,QACjC,SAASe,GAAUhK,EAAM,CAsB9B,IAAI5H,EAAUoC,GAKdyO,GAAQ,cAAc,EAAGrE,GAAc5E,CAAI,EAAGgE,GAAahE,EAAK,QAAS,CACvE,UAAW,YACX,MAAO,MACR,CAAA,CAAC,EAIF,OAAOF,EAAK1H,CAAO,CACrB,CClQW,IAAA6R,GAAgBnK,EAAK,UAAyB,CAIhD,OAAAI,EAAA,GAAc,eAAgB,QACvC,CAAC,ECIM,SAASgK,GAAwBpM,EAAO,CAC7C,OAAO,SAAUvH,EAAM,CACrB,IAAI4T,EAAY5T,EAAK,UAsBhB0T,GAAa,GAChB,OAAO,OAAOE,EAAU,MAAO,CAC7B,kBAAmB,GAAG,OAAOrM,EAAM,EAAG,oBAAoB,EAC1D,UAAW,GAAG,OAAOA,EAAM,EAAG,oBAAoB,CAC1D,CAAO,EAKH,IAAIsM,EAAW,OAAO,iBAAiBD,CAAS,EAChD,GAAIC,EAAS,YAAc,MAAO,CAEhC,IAAIC,EAAMF,EAAU,sBAAuB,EAI3C,MAAO,CACL,EAAGE,EAAI,MACP,EAAG,CACJ,CACP,CAII,MAAO,CACL,EAAG,EACH,EAAG,CACJ,CACF,CACH,CCxEA,SAASC,IAAgB,CACvB,MAAO,CACL,EAAG,EACH,EAAG,CACJ,CACH,CAiBO,SAASC,GAA2BhU,EAAM,CAC/C,IAAIiU,EAASjU,EAAK,OAChB4L,EAAqB5L,EAAK,mBAC1BkU,EAAiBlU,EAAK,UACtBmU,EAAYD,IAAmB,OAASH,GAAgBG,EACtDN,EAAY,SAAS,cAAc,KAAK,EAC5C,OAAO,OAAOA,EAAU,MAAO,CAK7B,SAAU,QAQV,IAAK,EACL,KAAM,EASN,OAAQzM,GAER,cAAe,MACnB,CAAG,EACD,SAAS,KAAK,OAAOyM,CAAS,EAC9B,IAAIQ,EAAUH,EAAO,CACnB,UAAWL,CACf,CAAG,EAQD,eAAe,UAAY,CACzB,IAAIS,EAAgBF,EAAU,CAC5B,UAAWP,CACjB,CAAK,EAwBD,GAAIjK,EAAQ,EAAI,CACd,IAAIhK,EAAOiU,EAAU,sBAAuB,EAG5C,GAAIjU,EAAK,QAAU,EACjB,OAEFiU,EAAU,MAAM,KAAO,IAAI,OAAOjU,EAAK,MAAQ,KAAQ,IAAI,CACjE,CACoEiM,IAAmBgI,EAAWS,EAAc,EAAGA,EAAc,CAAC,CAClI,CAAG,EACD,SAASxS,GAAU,CACjByS,EAAe,EAC2BF,IAAS,EACnD,SAAS,KAAK,YAAYR,CAAS,CACvC,CACE,IAAIU,EAAgBd,GAAmB,CAErC,YAAa3R,EAEb,OAAQA,CACZ,CAAG,CACH,CCxHO,SAAS0S,GAAQvU,EAAM,CAC5B,IAAImC,EAAOnC,EAAK,KACda,EAAab,EAAK,WAClBwU,EAAcxU,EAAK,YACrB,GAAIa,IAAe,IAAM2T,IAAgB,GAIvC,OAAO,MAAM,KAAKrS,CAAI,EAExB,IAAIG,EAAS,MAAM,KAAKH,CAAI,EACxBsS,EAAiBnS,EAAO,OAAOzB,EAAY,CAAC,EAC9C6T,EAAkB1P,GAAeyP,EAAgB,CAAC,EAClDE,EAAUD,EAAgB,CAAC,EAC7B,OAAApS,EAAO,OAAOkS,EAAa,EAAGG,CAAO,EAC9BrS,CACT,CC2BA,MAAMsS,GAAc5Q,EAAM,cAAuC,IAAI,EAErE,SAAS6Q,IAAiB,CAClB,MAAAC,EAAc9Q,EAAM,WAAW4Q,EAAW,EAChD,OAAAG,GAAUD,IAAgB,IAAI,EACvBA,CACT,CAOA,MAAME,GAAU,OAAO,MAAM,EAS7B,SAASC,GAAY,CACnB,KAAAC,EACA,MAAAvE,EACA,WAAAwE,CACF,EAIa,CACJ,MAAA,CACL,CAACH,EAAO,EAAG,GACX,KAAAE,EACA,MAAAvE,EACA,WAAAwE,CACF,CACF,CAEA,SAASC,GAAWrG,EAA0D,CACrE,OAAAA,EAAKiG,EAAO,IAAM,EAC3B,CAOA,MAAMK,GAA4B,CAAE,KAAM,MAAO,EAC3CC,GAAgC,CAAE,KAAM,UAAW,EAEzD,SAASC,GAAgB,CACvB,KAAAL,EACA,MAAAvE,EACA,OAAA6E,CACF,EAIG,CACD,KAAM,CAAE,aAAAC,EAAc,WAAAN,CAAW,EAAIN,GAAe,EAE9C9W,EAAMiG,EAAM,OAAuB,IAAI,EACvC,CAAC0R,EAAaC,CAAc,EAAI3R,EAAM,SAAsB,IAAI,EAEhE4R,EAAgB5R,EAAM,OAA0B,IAAI,EAEpD,CAAC6R,EAAgBC,CAAiB,EACtC9R,EAAM,SAAyBqR,EAAS,EAE1CrR,OAAAA,EAAM,UAAU,IAAM,CACpB,MAAMjF,EAAUhB,EAAI,QACdgY,EAAaH,EAAc,QACjCb,GAAUhW,CAAO,EACjBgW,GAAUgB,CAAU,EAEpB,MAAMhH,EAAOkG,GAAY,CAAE,KAAAC,EAAM,MAAAvE,EAAO,WAAAwE,EAAY,EAE7C,OAAAlR,GACLwR,EAAa,CAAE,OAAQP,EAAK,GAAI,QAAAnW,EAAS,EACzC0U,GAAU,CACR,QAASsC,EACT,eAAgB,IAAMhH,EACtB,sBAAsB,CAAE,mBAAAnD,GAAsB,CACjBoI,GAAA,CACzB,mBAAApI,EACA,UAAW+H,GAAwB,CACjC,EAAG,OACH,EAAG,MAAA,CACJ,EACD,OAAO,CAAE,UAAAC,GAAa,CACpB,OAAAkC,EAAkB,CAAE,KAAM,UAAW,UAAAlC,CAAA,CAAW,EAEzC,IAAMkC,EAAkBR,EAAa,CAAA,CAC9C,CACD,CACH,EACA,aAAc,CACZQ,EAAkBR,EAAa,CACjC,EACA,QAAS,CACPQ,EAAkBT,EAAS,CAAA,CAC7B,CACD,EACD9B,GAAsB,CACpB,QAAAxU,EACA,QAAQ,CAAE,OAAAwM,GAAU,CAClB,OACE6J,GAAW7J,EAAO,IAAI,GAAKA,EAAO,KAAK,aAAe4J,CAE1D,EACA,QAAQ,CAAE,MAAAhV,GAAS,CACjB,OAAOL,GAAkBiP,EAAM,CAC7B,QAAAhQ,EACA,MAAAoB,EACA,aAAc,CAAC,MAAO,QAAQ,CAAA,CAC/B,CACH,EACA,OAAO,CAAE,KAAA6V,EAAM,OAAAzK,GAAU,CAEvB,GADiBA,EAAO,UAAYxM,EACtB,CACZ4W,EAAe,IAAI,EACnB,MAAA,CAGID,MAAAA,EAAchV,GAAmBsV,EAAK,IAAI,EAE1CC,EAAc1K,EAAO,KAAK,MACtBwJ,GAAA,OAAOkB,GAAgB,QAAQ,EAEnC,MAAAC,EAAqBvF,IAAUsF,EAAc,EAC7CE,EAAoBxF,IAAUsF,EAAc,EAMlD,GAHGC,GAAsBR,IAAgB,UACtCS,GAAqBT,IAAgB,MAEb,CACzBC,EAAe,IAAI,EACnB,MAAA,CAGFA,EAAeD,CAAW,CAC5B,EACA,aAAc,CACZC,EAAe,IAAI,CACrB,EACA,QAAS,CACPA,EAAe,IAAI,CAAA,CAEtB,CAAA,CACH,GACC,CAACR,EAAYD,EAAMvE,EAAO8E,CAAY,CAAC,EAGxCW,OAACpS,EAAM,SAAN,CACC,SAAA,CAACoS,EAAA,KAAA,MAAA,CAAI,IAAArY,EAAU,UAAU,uCACvB,SAAA,CAAAqY,EAAA,KAAC,MAAA,CACC,UAAWC,EACT,mDACAR,EAAe,OAAS,YAAc,YACxC,EAEA,SAAA,CAACO,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAAA3X,EAAA,IAAC6X,GAAA,CACC,QAASd,GAAQ,aAAa,EAC9B,gBAAiB,IAAMA,GAAQ,iBAAiB,CAAA,CAClD,EACA/W,EAAAA,IAAC,OAAM,CAAA,SAAAyW,EAAK,KAAM,CAAA,CAAA,EACpB,EACAzW,EAAA,IAAC8X,EAAA,CACC,cAAY,OACZ,SAAU,GACV,QAAQ,QACR,UAAU,kBACV,IAAKX,EACL,aAAY,WAAWV,EAAK,KAAK,GAEjC,SAAAzW,EAAAA,IAAC+X,GAAY,CAAA,UAAU,yCAA0C,CAAA,CAAA,CAAA,CACnE,CAAA,CACF,EACCd,GAAgBjX,EAAA,IAAAoF,GAAA,CAAc,KAAM6R,EAAa,IAAI,KAAM,CAAA,CAAA,EAC9D,EACCG,EAAe,OAAS,WACvBY,GAAS,aACPhY,EAAAA,IAAC,MAAK,CAAA,SAAAyW,EAAK,KAAM,CAAA,EACjBW,EAAe,SAAA,CACjB,EACJ,CAEJ,CAEA,SAASa,IAAkB,CACnB,MAAAxI,MAAe,IAErB,SAASV,EAAS,CAAE,OAAAmJ,EAAQ,QAAA5X,GAAsB,CACvC,OAAAmP,EAAA,IAAIyI,EAAQ5X,CAAO,EAErB,UAAsB,CAC3BmP,EAAS,OAAOyI,CAAM,CACxB,CAAA,CAGF,SAASC,EAAWD,EAAoC,CAC/C,OAAAzI,EAAS,IAAIyI,CAAM,GAAK,IAAA,CAG1B,MAAA,CAAE,SAAAnJ,EAAU,WAAAoJ,CAAW,CAChC,CAgBA,SAASC,GAAmB,CAAE,MAAAC,GAA2C,CACvE,MAAMC,EAAuBD,EAAM,cAAgB,EAAA,IAAKtB,IAAY,CAClE,GAAIA,EAAO,GACX,MAAOA,EAAO,UAAU,MAAM,aAAeA,EAAO,EAAA,EACpD,EACI,CAAC,CAAE,MAAAwB,EAAO,cAAAC,CAAA,EAAiBC,CAAY,EAAIlT,EAAM,SAAoB,CACzE,MAAO+S,EACP,cAAe,IAAA,CAChB,EACK,CAAC7I,CAAQ,EAAIlK,EAAM,SAAS0S,EAAe,EAG3C,CAACvB,CAAU,EAAInR,EAAM,SAAS,IAAM,OAAO,aAAa,CAAC,EAE/DA,EAAM,UAAU,IAAM,CACpB8S,EAAM,eAAeE,EAAM,IAAK9B,GAASA,EAAK,EAAE,CAAC,CAAA,EAEhD,CAAC8B,CAAK,CAAC,EAEV,MAAMG,EAAcnT,EAAM,YACxB,CAAC,CACC,WAAAnD,EACA,cAAAE,EACA,oBAAAD,CAAA,IAKI,CACJ,MAAM0T,EAAc5T,GAA2B,CAC7C,WAAAC,EACA,oBAAAC,EACA,cAAAC,EACA,KAAM,UAAA,CACP,EAEGyT,IAAgB3T,GAIpBqW,EAAcE,GAAc,CACpB,MAAAlC,EAAOkC,EAAU,MAAMvW,CAAU,EAGvC,OAAKqU,EAIE,CACL,MAAOX,GAAQ,CACb,KAAM6C,EAAU,MAChB,WAAAvW,EACA,YAAA2T,CAAA,CACD,EACD,cAAe,CACb,KAAAU,EACA,cAAerU,EACf,aAAc2T,EACd,cAAe4C,EAAU,MAAM,MAAA,CAEnC,EAfSA,CAeT,CACD,CACH,EACA,CAAA,CACF,EAEApT,EAAM,UAAU,IACPwP,GAAmB,CACxB,WAAW,CAAE,OAAAjI,GAAU,CACrB,OAAO6J,GAAW7J,EAAO,IAAI,GAAKA,EAAO,KAAK,aAAe4J,CAC/D,EACA,OAAO,CAAE,SAAAtJ,EAAU,OAAAN,GAAU,CAC3B,MAAMhN,EAASsN,EAAS,QAAQ,YAAY,CAAC,EAC7C,GAAI,CAACtN,EACH,OAGF,MAAM8Y,EAAa9L,EAAO,KACpB+L,EAAa/Y,EAAO,KAC1B,GAAI,CAAC6W,GAAWiC,CAAU,GAAK,CAACjC,GAAWkC,CAAU,EACnD,OAGF,MAAMvW,EAAgBiW,EAAM,UACzB9B,GAASA,EAAK,KAAOoC,EAAW,KAAK,EACxC,EACA,GAAIvW,EAAgB,EAClB,OAGI,MAAAD,EAAsBJ,GAAmB4W,CAAU,EAE7CH,EAAA,CACV,WAAYE,EAAW,MACvB,cAAAtW,EACA,oBAAAD,CAAA,CACD,CAAA,CACH,CACD,EACA,CAACqU,EAAY6B,EAAOG,CAAW,CAAC,EAGnCnT,EAAM,UAAU,IAAM,CACpB,GAAIiT,IAAkB,KACpB,OAGF,KAAM,CAAE,KAAA/B,EAAM,cAAAqC,EAAe,aAAAC,EAAc,cAAAC,CAAkB,EAAAR,EACvDlY,EAAUmP,EAAS,WAAWgH,EAAK,EAAE,EACvCnW,GACFD,GAAqBC,CAAO,EAGnB2Y,GACT,gBAAgBxC,EAAK,KAAK,kBAAkBqC,EAAgB,CAC5D,gBAAgBC,EAAe,CAAC,OAAOC,CAAa,GACtD,CAAA,EACC,CAACR,EAAe/I,CAAQ,CAAC,EAG5BlK,EAAM,UAAU,IACP,UAAmB,CACxB2T,GAAmB,CACrB,EACC,EAAE,EAEC,MAAAC,EAAgB5T,EAAM,YAAY,IAAMgT,EAAM,OAAQ,CAACA,EAAM,MAAM,CAAC,EAEpEa,EAAiC7T,EAAM,QAAQ,KAC5C,CACL,aAAckK,EAAS,SACvB,YAAAiJ,EACA,WAAAhC,EACA,cAAAyC,CACF,GACC,CAAC1J,EAAS,SAAUiJ,EAAahC,EAAYyC,CAAa,CAAC,EAE9D,aACG,MACC,CAAA,SAAAnZ,EAAA,IAAC,OAAI,UAAU,sBACb,gBAACqZ,GACC,CAAA,SAAA,CAACrZ,EAAAA,IAAAsZ,GAAA,CAAe,QAAO,GACrB,SAAA3B,EAAA,KAACG,EAAA,CACC,QAAQ,YACR,UAAWF,EACT,+DACF,EAEA,SAAA,CAAA5X,EAAA,IAACuZ,GAAiB,CAAA,UAAU,SAAS,cAAY,OAAO,EAAE,MAAA,CAAA,CAAA,EAG9D,EACA5B,EAAA,KAAC6B,GAAA,CACC,MAAM,MACN,WAAY,EACZ,UAAU,uBAEV,SAAA,CAACxZ,EAAA,IAAAyZ,GAAA,CAAM,UAAU,cAAc,SAAkB,qBAAA,EAChDzZ,EAAA,IAAAmW,GAAY,SAAZ,CAAqB,MAAOiD,EAC3B,SAAApZ,MAAC,MAAI,CAAA,UAAU,gBACZ,SAAAuY,EAAM,IAAI,CAAC9B,EAAMvE,IAAU,CAC1B,MAAM6E,EAASsB,EAAM,UAAU5B,EAAK,EAAE,EAClC,OAACM,EAEH/W,EAAA,IAAC,MAAA,CAEC,UAAW4X,EAAG,CAACb,EAAO,WAAA,GAAgB,QAAQ,EAE9C,SAAC/W,EAAA,IAAA8W,GAAA,CAAS,OAAAC,EAAgB,KAAAN,EAAY,MAAAvE,CAAc,CAAA,CAAA,EAH/C6E,EAAO,EAId,EAPkB,IASrB,CAAA,CACH,CAAA,CACF,CAAA,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CACF,CAAA,EACF,CAEJ,CCzbgB,SAAA2C,GAAiB,CAAE,MAAArB,GAAuC,CAetE,OAAAV,EAAA,KAAC,MAAI,CAAA,UAAU,+DACb,SAAA,CAAC3X,EAAAA,IAAA,MAAA,CAAI,UAAU,iEA4Cf,CAAA,EACA2X,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACb,SAAA,CAAAA,EAAA,KAACG,EAAA,CACC,QAAQ,YACR,UAAU,wDAEV,SAAA,CAAA9X,EAAA,IAAC2Z,GAAe,CAAA,UAAU,kBAAkB,cAAY,OAAO,EAAE,QAAA,CAAA,CAEnE,EACA3Z,MAACoY,IAAY,MAAAC,CAAc,CAAA,CAAA,CAC7B,CAAA,CAAA,EACF,CAEJ,CClEO,SAASuB,GAA2B,CACzC,MAAAvB,EACA,SAAAwB,EACA,WAAAC,EACA,iBAAAC,CACF,EAAoC,CAClC,MAAMC,EAAoB,CACxB,CACE,KAAMC,GACN,QAAS,IAAM5B,EAAM,aAAa,CAAC,EACnC,SAAU,CAACA,EAAM,mBAAmB,EACpC,OAAQ,aACR,WAAY,iBACd,EACA,CACE,KAAM6B,GACN,QAAS,IAAM7B,EAAM,aAAa,EAClC,SAAU,CAACA,EAAM,mBAAmB,EACpC,OAAQ,gBACR,WAAY,EACd,EACA,CACE,KAAM8B,GACN,QAAS,IAAM9B,EAAM,SAAS,EAC9B,SAAU,CAACA,EAAM,eAAe,EAChC,OAAQ,YACR,WAAY,EACd,EACA,CACE,KAAM+B,GACN,QAAS,IAAM/B,EAAM,aAAaA,EAAM,eAAiB,CAAC,EAC1D,SAAU,CAACA,EAAM,eAAe,EAChC,OAAQ,YACR,WAAY,iBAAA,CAEhB,EAEMgC,EAAqBP,GAAczB,EAAM,sBAAsB,KAAK,OAEpEiC,EADcjC,EAAM,SAAS,EAAE,WAAW,UACZwB,EAAW,EACzCU,EAAe,KAAK,IAAIF,EAAoBC,EAAgBT,EAAW,CAAC,EAExEW,EAAkB,CAAC,GAAI,GAAI,GAAI,IAAK,KAAK,EAEzCC,EAAwBvb,GAAkB,CAC9C,MAAMwb,EAAUxb,IAAU,MAAQmb,EAAqB,SAASnb,EAAO,EAAE,EACzEmZ,EAAM,YAAYqC,CAAO,EAErBX,GACFA,EAAiBW,CAAO,CAE5B,EAGE,OAAA/C,EAAA,KAAC,MAAI,CAAA,UAAU,8EACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0BACb,SAAA,CAAC3X,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAe,kBAAA,EACvD2X,EAAA,KAACgD,GAAA,CACC,MAAOd,EAAS,SAAS,EACzB,cAAeY,EAEf,SAAA,CAACza,EAAAA,IAAA4a,GAAA,CAAc,UAAU,eACvB,SAAA5a,EAAAA,IAAC6a,IAAY,YAAahB,EAAS,SAAS,CAAA,CAAG,CACjD,CAAA,EACC7Z,EAAA,IAAA8a,GAAA,CACE,SAAgBN,EAAA,IAAKO,GACpB/a,MAACgb,GAAwB,CAAA,MAAOD,EAAO,SAAS,EAC7C,SADcA,CAAA,EAAAA,CAEjB,CACD,CACH,CAAA,CAAA,CAAA,CAAA,CACF,EACF,EAEApD,EAAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qCACZ,SAAA,CAAMU,EAAA,8BAA8B,KAAK,OAAO,OAAKgC,EAAmB,mBAAA,EAE3E,EACA1C,EAAAA,KAAC,MAAI,CAAA,UAAU,uCACb,SAAA,CAACA,EAAAA,KAAA,IAAA,CAAE,UAAU,qDAAqD,SAAA,CAAA,UACxD,IACRA,EAAAA,KAAC,OAAK,CAAA,UAAU,8CACb,SAAA,CAAA2C,EAAc,IAAEC,CAAA,EACnB,EAAQ,IAAI,KACT,IACFva,EAAA,IAAA,OAAA,CAAK,UAAU,8CACb,SACHqa,CAAA,CAAA,CAAA,EACF,EACAra,EAAAA,IAAC,OAAI,UAAU,8BACZ,WAAkB,IAAI,CAACib,EAAQ/I,IAC9ByF,EAAA,KAACG,EAAA,CAEC,QAAQ,YACR,UAAWF,EAAGqD,EAAO,WAAY,OAAO,EACxC,QAAS,IAAM,CACbA,EAAO,QAAQ,EACf5C,EAAM,kBAAkB,CAC1B,EACA,SAAU4C,EAAO,SAEjB,SAAA,CAAAjb,EAAA,IAAC,OAAK,CAAA,UAAU,UAAW,SAAAib,EAAO,OAAO,QACxCA,EAAO,KAAP,CAAY,UAAU,kBAAkB,cAAY,MAAO,CAAA,CAAA,CAAA,EAVvD/I,CAAA,CAYR,CACH,CAAA,CAAA,CACF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ,CCtEO,SAASgJ,GAAiB,CAC/B,QAAAC,EACA,KAAA7K,EACA,WAAAwJ,EACA,UAAAsB,EACA,iBAAAC,EAAmB,GACnB,cAAAC,EAAgB,GAChB,SAAAzB,EAAW,GACX,mBAAA0B,EACA,gBAAAC,EACA,aAAAC,EACA,SAAAC,EACA,YAAAC,EAAc,GACd,gBAAiBC,EACjB,qBAAAC,EAAuB,GACvB,UAAAC,EACA,MAAAC,EACA,aAAAC,EACA,mBAAAC,EAAqB,EACvB,EAA0B,CACxB,KAAM,CAACC,EAAcC,CAAe,EAAIC,EAAAA,SAAe,CAAA,CAAE,EACnD,CAACC,EAAYC,CAAa,EAAIF,WAAgC,CAClE,UAAW,EACX,SAAAvC,CAAA,CACD,EACK,CAAC0C,EAASC,CAAU,EAAIJ,EAAAA,SAA6BX,GAAgB,CAAA,CAAE,EACvE,CAACgB,EAAcC,CAAe,EAAIN,EAAAA,SAAe,EAAK,EAG5DO,EAAAA,UAAgB,IAAM,CAChBlB,GACFe,EAAWf,CAAY,CACzB,EACC,CAACA,CAAY,CAAC,EAGjB,MAAMmB,EAAsDC,EAAM,YAC/DC,GAAmB,CAClB,MAAMC,EAAgB,OAAOD,GAAmB,WAC5CA,EAAeT,CAAU,EACzBS,EAEJR,EAAcS,CAAa,EACvBxB,GACFA,EAAmBwB,CAAa,CAEpC,EACA,CAACxB,EAAoBc,CAAU,CACjC,EAGMW,EAAgDH,EAAM,YACzDC,GAAmB,CAClB,MAAMG,EAAa,OAAOH,GAAmB,WACzCA,EAAeP,CAAO,EACtBO,EAEJN,EAAWS,CAAU,EACjBzB,GACFA,EAAgByB,CAAU,CAE9B,EACA,CAACzB,EAAiBe,CAAO,CAC3B,EAGM9B,EAAuBoC,EAAM,YAChCK,GAAwB,CACvB,MAAMH,EAAgB,CACpB,UAAW,EACX,SAAUG,CACZ,EAEAZ,EAAcS,CAAa,EACvBxB,GACFA,EAAmBwB,CAAa,CAEpC,EACA,CAACxB,CAAkB,CACrB,EAGM4B,EAAgBN,EAAAA,YAAkB,IAAM,CACxCf,GAAa,CAACV,GAAa,CAACqB,IAC9BC,EAAgB,EAAI,EAGVZ,EAAA,EAGV,WAAW,IAAM,CACfY,EAAgB,EAAK,GACpB,GAAI,EAER,EAAA,CAACZ,EAAWV,EAAWqB,CAAY,CAAC,EAGjCnE,EAAe8E,EAAAA,QAAc,IAC5BnB,EAGEd,EAFEA,EAAQ,OAAckC,GAAAA,EAAI,KAAO,QAAQ,EAGjD,CAAClC,EAASc,CAAkB,CAAC,EAG1BqB,EAAkBF,EAAAA,QAAc,IAC7B9E,EAAa,IAAcvB,GAE5BA,EAAO,gBAAkB,GACpBA,EAIF,CACL,GAAGA,EACH,OAASwG,GAAY,CACnB,MAAMC,EAAeD,EAAQ,OACvBE,GAAWD,EAAa,YAAY,EACpCE,GAAU,OAAO3G,EAAO,QAAW,SACrCA,EAAO,OACPA,EAAO,OACL4G,GAAW5G,EAAO,OAAQwG,CAAO,EACjC,KAEAK,GAEDjG,EAAAA,KAAAkG,EAAA,SAAA,CAAA,SAAA,CAAAH,GACAD,IACEzd,EAAAA,IAAA,OAAA,CAAK,UAAU,2BACb,cAAa,MACZA,EAAA,IAAC8d,GAAc,CAAA,UAAU,cAAc,EAEvC9d,EAAAA,IAAC+d,GAAgB,CAAA,UAAU,cAAc,CAE7C,CAAA,CAAA,EAEJ,EAGK,OAAAhH,EAAO,gBAAkB,GAC9B/W,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAMwd,EAAa,cAAc,EAC1C,UAAW5F,EACT,oDACA6F,GAAW,eAAiB,EAC9B,EAEC,SAAAG,EAAA,CAAA,EAGHA,EAAA,CAGN,CACD,EACA,CAACtF,CAAY,CAAC,EAEXD,EAAQ2F,GAAc,CAC1B,KAAA1N,EACA,QAASgN,EACT,MAAO,CACL,aAAApB,EACA,WAAAG,EACA,QAAAE,CACF,EACA,UAAWzC,EAAa,KAAK,KAAKA,EAAauC,EAAW,QAAQ,EAAI,GACtE,mBAAAJ,EACA,cAAe,GACf,cAAAX,EACA,oBAAqB2C,GAAoB,EACzC,sBAAuBC,GAAsB,EAC7C,kBAAmBC,GAAkB,EACrC,qBAAsBhC,EACtB,gBAAiBa,EACjB,gBAAiBoB,GAAgB,EACjC,mBAAoBxB,EACpB,iBAAAvB,CAAA,CACD,EAED,OAEIrb,EAAA,IAAA6d,WAAA,CAAA,SAAAlG,EAAAA,KAAC,MAAI,CAAA,UAAU,YACZ,SAAA,CACCoE,GAAApE,EAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAAC3X,EAAA,IAAA,KAAA,CAAG,UAAU,wBAAyB,SAAM+b,EAAA,EAC5CC,GAAc,OAAA,EACjB,EAGFrE,EAAAA,KAAC,MAAI,CAAA,UAAU,8EACb,SAAA,CAAC3X,EAAA,IAAA,MAAA,CAAI,UAAU,gBACZ,SACC4b,EAAA5b,MAAC4b,GAAgB,MAAAvD,EAAc,SAAAqD,EAAoB,YAAAC,CAA0B,CAAA,EAG1EhE,EAAAA,KAAAkG,EAAAA,SAAA,CAAA,SAAA,CAAAnC,GACE1b,EAAA,IAAAE,GAAA,CAAO,SAAUwb,EAAU,MAAOC,EAAa,EAEjD,CAACE,GAAyB7b,EAAAA,IAAA0Z,GAAA,CAAU,MAAArB,CAAc,CAAA,CAAA,CAAA,CACrD,CAEJ,CAAA,EAEAV,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACZ,SAAA,CAAA,CAACoE,GAASC,GACTrE,EAAA,KAACG,EAAA,CACC,QAAQ,UACR,KAAK,KACL,UAAU,oCACV,QAASkE,EAAa,QAErB,SAAA,CAAAA,EAAa,SAAWhc,EAAA,IAACqe,GAAU,CAAA,UAAU,UAAU,EACxDre,EAAAA,IAAC,OAAM,CAAA,SAAAgc,EAAa,KAAM,CAAA,CAAA,CAAA,CAC5B,EAGDF,GACCnE,EAAA,KAACG,EAAA,CACC,QAAQ,YACR,KAAK,KACL,UAAU,0BACV,QAASqF,EACT,SAAU/B,GAAaqB,EAEvB,SAAA,CAAAzc,EAAA,IAACse,GAAA,CACC,UAAW1G,EACT,UACA6E,GAAgB,cAAA,CAClB,CACF,EACCzc,EAAA,IAAA,OAAA,CAAK,UAAU,mBAAmB,SAAO,SAAA,CAAA,CAAA,CAAA,CAAA,CAC5C,CAEJ,CAAA,CAAA,EACF,EAECA,MAAA,MAAA,CAAI,UAAU,2CACb,gBAACue,GACC,CAAA,SAAA,CAAAve,MAACwe,IACE,SAAMnG,EAAA,kBAAkB,IAAKoG,GAC5Bze,EAAA,IAAC0e,GAAA,CAEC,UAAU,gDAET,SAAYD,EAAA,QAAQ,IAAKE,GACxB3e,EAAA,IAAC4e,GAAA,CAEC,UAAWhH,EACT,4CACA+G,EAAO,OAAO,UAAU,MAAM,SAChC,EAEC,SAAAA,EAAO,cACJ,KACAhB,GACAgB,EAAO,OAAO,UAAU,OACxBA,EAAO,WAAW,CAAA,CACpB,EAXGA,EAAO,EAaf,CAAA,CAAA,EAlBIF,EAAY,EAoBpB,CAAA,EACH,EACCze,MAAA6e,GAAA,CACE,SACCzD,EAAApb,EAAAA,IAAC0e,GACC,CAAA,SAAA1e,EAAA,IAAC8e,GAAA,CACC,QAASxG,EAAa,OACtB,UAAU,mBACX,SAAA,YAAA,CAGH,CAAA,CAAA,EACED,EAAM,YAAc,EAAA,MAAM,OAC5BA,EAAM,YAAY,EAAE,KAAK,IAAK0G,GAC5B/e,EAAA,IAAC0e,GAAA,CAEC,QAAS,IAAMzC,GAAsB8C,EAAI,eAAe,CAACA,EAAI,eAAe,EAC5E,UAAWnH,EACT,QACAqE,EAAqB,sDAAwD,EAC/E,EAEC,WAAI,gBAAgB,EAAE,IAAI,CAAC+C,EAAM9M,IAChCyF,EAAA,KAACmH,GAAA,CAEC,UAAWlH,EACTmH,EAAI,gBACA,8BACA,GACJ,8EACAC,EAAK,OAAO,UAAU,MAAM,SAC9B,EAEC,SAAA,CAAA9M,IAAU,GAAK6M,EAAI,iBACjB/e,EAAA,IAAA,MAAA,CAAI,UAAU,mEAAmE,EAEnF2d,GACCqB,EAAK,OAAO,UAAU,KACtBA,EAAK,WAAW,CAAA,CAClB,CAAA,EAfKA,EAAK,EAiBb,CAAA,CAAA,EA1BID,EAAI,EA4BZ,CAAA,EAED/e,EAAA,IAAC0e,GACC,CAAA,SAAA1e,EAAA,IAAC8e,GAAA,CACC,QAASxG,EAAa,OACtB,UAAU,mBACX,SAAA,aAAA,GAGH,CAEJ,CAAA,CAAA,CAAA,CACF,CAEF,CAAA,EACAtY,EAAA,IAAC4Z,GAAA,CACC,MAAAvB,EACA,SAAUgE,EAAW,SACrB,WAAAvC,EACA,iBAAkBW,CAAA,CAAA,CACpB,CAAA,CACF,CACF,CAAA,CAEJ", "x_google_ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]}