{"version": 3, "file": "DocumentPreviewDialog-C1lfiEeE.js", "sources": ["../../../../../frontend/src/components/ui/DocumentPreviewDialog.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';\r\n\r\ninterface DocumentPreviewDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  documentSrc: string;\r\n}\r\n\r\nexport const DocumentPreviewDialog: React.FC<DocumentPreviewDialogProps> = ({ isOpen, onOpenChange, documentSrc }) => {\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"min-w-[800px] w-auto h-[90vh] flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle>Preview Document</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex-grow overflow-auto p-4\">\r\n          <iframe src={documentSrc} title=\"Document Preview\" className=\"w-full h-full border-none\"></iframe>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}; "], "names": ["DocumentPreviewDialog", "isOpen", "onOpenChange", "documentSrc", "jsx", "Dialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle"], "mappings": "uGASO,MAAMA,EAA8D,CAAC,CAAE,OAAAC,EAAQ,aAAAC,EAAc,YAAAC,KAEhGC,EAAA,IAACC,GAAO,KAAMJ,EAAQ,aAAAC,EACpB,SAACI,EAAAA,KAAAC,EAAA,CAAc,UAAU,8CACvB,SAAA,CAAAH,MAACI,EACC,CAAA,SAAAJ,EAAA,IAACK,EAAY,CAAA,SAAA,kBAAgB,CAAA,EAC/B,EACCL,EAAA,IAAA,MAAA,CAAI,UAAU,8BACb,SAACA,EAAAA,IAAA,SAAA,CAAO,IAAKD,EAAa,MAAM,mBAAmB,UAAU,2BAAA,CAA4B,CAC3F,CAAA,CAAA,CAAA,CACF,CACF,CAAA"}