{"version": 3, "file": "sdk.gen-BHIMY_5K.js", "sources": ["../../../../../frontend/src/clientEkb/sdk.gen.ts"], "sourcesContent": ["// This file is auto-generated by @hey-api/openapi-ts\n\nimport { type Options as ClientOptions, type TDataShape, type Client, formDataBodySerializer } from '@hey-api/client-fetch';\nimport type { GetApiAbpApplicationConfigurationData, GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, GetApiAbpMultiTenancyTenantsByNameByNameData, GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, GetApiAbpMultiTenancyTenantsByIdByIdData, GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, PostApiAccountRegisterData, PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, PostApiAccountSendPasswordResetCodeData, PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, PostApiAccountVerifyPasswordResetTokenData, PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, PostApiAccountResetPasswordData, PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, PostApiMasterAgentFilterListData, PostApiMasterAgentFilterListResponses, PostApiMasterAgentFilterListErrors, GetApiMasterAgentData, GetApiMasterAgentResponses, GetApiMasterAgentErrors, PostApiMasterAgentData, PostApiMasterAgentResponses, PostApiMasterAgentErrors, DeleteApiMasterAgentByIdData, DeleteApiMasterAgentByIdResponses, DeleteApiMasterAgentByIdErrors, GetApiMasterAgentByIdData, GetApiMasterAgentByIdResponses, GetApiMasterAgentByIdErrors, PutApiMasterAgentByIdData, PutApiMasterAgentByIdResponses, PutApiMasterAgentByIdErrors, GetApiAuthLogoutData, GetApiAuthLogoutResponses, GetApiEkbBcTypeData, GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, PostApiEkbBcTypeData, PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, DeleteApiEkbBcTypeByIdData, DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, GetApiEkbBcTypeByIdData, GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, PutApiEkbBcTypeByIdData, PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, PostApiEkbBcTypeFilterListData, PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, PostApiEkbBoundedZoneListData, PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, PostApiEkbBoundedZoneVesselHeadersData, PostApiEkbBoundedZoneVesselHeadersResponses, PostApiEkbBoundedZoneVesselHeadersErrors, GetApiEkbBoundedZoneData, GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, PostApiEkbBoundedZoneData, PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, DeleteApiEkbBoundedZoneByIdData, DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, GetApiEkbBoundedZoneByIdData, GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, PutApiEkbBoundedZoneByIdData, PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, PostApiEkbBoundedZoneFilterListData, PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, GetApiEkbBoundedZoneQueryableWithVesselHeadersData, GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses, GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoData, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors, GetApiEkbBusinessPartnerData, GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, PostApiEkbBusinessPartnerData, PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, DeleteApiEkbBusinessPartnerByIdData, DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, GetApiEkbBusinessPartnerByIdData, GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, PutApiEkbBusinessPartnerByIdData, PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, PostApiEkbBusinessPartnerFilterListData, PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, GetApiEkbCargoData, GetApiEkbCargoResponses, GetApiEkbCargoErrors, PostApiEkbCargoData, PostApiEkbCargoResponses, PostApiEkbCargoErrors, DeleteApiEkbCargoByIdData, DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, GetApiEkbCargoByIdData, GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, PutApiEkbCargoByIdData, PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, PostApiEkbCargoFilterListData, PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, GetApiEkbDestinationPortData, GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, PostApiEkbDestinationPortData, PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, DeleteApiEkbDestinationPortByIdData, DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, GetApiEkbDestinationPortByIdData, GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, PutApiEkbDestinationPortByIdData, PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, PostApiEkbDestinationPortFilterListData, PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, PostApiEkbDocAttachmentsListData, PostApiEkbDocAttachmentsListResponses, PostApiEkbDocAttachmentsListErrors, DeleteApiEkbDocAttachmentsByIdData, DeleteApiEkbDocAttachmentsByIdResponses, DeleteApiEkbDocAttachmentsByIdErrors, GetApiEkbDocAttachmentsByIdData, GetApiEkbDocAttachmentsByIdResponses, GetApiEkbDocAttachmentsByIdErrors, PutApiEkbDocAttachmentsByIdData, PutApiEkbDocAttachmentsByIdResponses, PutApiEkbDocAttachmentsByIdErrors, PostApiEkbDocAttachmentsData, PostApiEkbDocAttachmentsResponses, PostApiEkbDocAttachmentsErrors, PostApiEkbDocAttachmentsUploadData, PostApiEkbDocAttachmentsUploadResponses, PostApiEkbDocAttachmentsUploadErrors, PostApiEkbDocAttachmentsUploadWithContentData, PostApiEkbDocAttachmentsUploadWithContentResponses, PostApiEkbDocAttachmentsUploadWithContentErrors, GetApiEkbDocAttachmentsDownloadByIdData, GetApiEkbDocAttachmentsDownloadByIdErrors, GetApiEkbDocAttachmentsByReferenceByReferenceIdData, GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses, GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors, DeleteApiEkbDocAttachmentsFileByIdData, DeleteApiEkbDocAttachmentsFileByIdResponses, DeleteApiEkbDocAttachmentsFileByIdErrors, GetApiEkbDocAttachmentsInfoByIdData, GetApiEkbDocAttachmentsInfoByIdResponses, GetApiEkbDocAttachmentsInfoByIdErrors, PostApiAccountDynamicClaimsRefreshData, PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, GetApiEkbExportVesselData, GetApiEkbExportVesselResponses, GetApiEkbExportVesselErrors, PostApiEkbExportVesselData, PostApiEkbExportVesselResponses, PostApiEkbExportVesselErrors, PostApiEkbExportVesselGenerateNextDocNumData, PostApiEkbExportVesselGenerateNextDocNumResponses, PostApiEkbExportVesselGenerateNextDocNumErrors, DeleteApiEkbExportVesselByIdData, DeleteApiEkbExportVesselByIdResponses, DeleteApiEkbExportVesselByIdErrors, GetApiEkbExportVesselByIdData, GetApiEkbExportVesselByIdResponses, GetApiEkbExportVesselByIdErrors, PutApiEkbExportVesselByIdData, PutApiEkbExportVesselByIdResponses, PutApiEkbExportVesselByIdErrors, PostApiEkbExportVesselFilterListData, PostApiEkbExportVesselFilterListResponses, PostApiEkbExportVesselFilterListErrors, GetApiEkbExportVesselByIdWithItemsData, GetApiEkbExportVesselByIdWithItemsResponses, GetApiEkbExportVesselByIdWithItemsErrors, GetApiEkbExportVesselBillingData, GetApiEkbExportVesselBillingResponses, GetApiEkbExportVesselBillingErrors, PostApiEkbExportVesselBillingData, PostApiEkbExportVesselBillingResponses, PostApiEkbExportVesselBillingErrors, PostApiEkbExportVesselBillingFilterListData, PostApiEkbExportVesselBillingFilterListResponses, PostApiEkbExportVesselBillingFilterListErrors, DeleteApiEkbExportVesselBillingByIdData, DeleteApiEkbExportVesselBillingByIdResponses, DeleteApiEkbExportVesselBillingByIdErrors, GetApiEkbExportVesselBillingByIdData, GetApiEkbExportVesselBillingByIdResponses, GetApiEkbExportVesselBillingByIdErrors, PutApiEkbExportVesselBillingByIdData, PutApiEkbExportVesselBillingByIdResponses, PutApiEkbExportVesselBillingByIdErrors, GetApiEkbExportVesselBillingByIdWithItemsData, GetApiEkbExportVesselBillingByIdWithItemsResponses, GetApiEkbExportVesselBillingByIdWithItemsErrors, GetApiEkbExportVesselBillingItemsData, GetApiEkbExportVesselBillingItemsResponses, GetApiEkbExportVesselBillingItemsErrors, GetApiEkbFilesDownloadByIdData, GetApiEkbFilesDownloadByIdErrors, GetApiEkbFilesStreamByIdData, GetApiEkbFilesStreamByIdErrors, GetApiEkbFilesStreamByFilePathData, GetApiEkbFilesStreamByFilePathErrors, GetApiEkbFilesInfoByIdData, GetApiEkbFilesInfoByIdErrors, GetApiHealthKubernetesData, GetApiHealthKubernetesResponses, GetApiEkbImportVesselData, GetApiEkbImportVesselResponses, GetApiEkbImportVesselErrors, PostApiEkbImportVesselData, PostApiEkbImportVesselResponses, PostApiEkbImportVesselErrors, PostApiEkbImportVesselGenerateNextDocNumData, PostApiEkbImportVesselGenerateNextDocNumResponses, PostApiEkbImportVesselGenerateNextDocNumErrors, DeleteApiEkbImportVesselByIdData, DeleteApiEkbImportVesselByIdResponses, DeleteApiEkbImportVesselByIdErrors, GetApiEkbImportVesselByIdData, GetApiEkbImportVesselByIdResponses, GetApiEkbImportVesselByIdErrors, PutApiEkbImportVesselByIdData, PutApiEkbImportVesselByIdResponses, PutApiEkbImportVesselByIdErrors, GetApiEkbImportVesselByIdWithItemsData, GetApiEkbImportVesselByIdWithItemsResponses, GetApiEkbImportVesselByIdWithItemsErrors, PostApiEkbImportVesselFilterListData, PostApiEkbImportVesselFilterListResponses, PostApiEkbImportVesselFilterListErrors, GetApiEkbImportVesselBillingData, GetApiEkbImportVesselBillingResponses, GetApiEkbImportVesselBillingErrors, PostApiEkbImportVesselBillingData, PostApiEkbImportVesselBillingResponses, PostApiEkbImportVesselBillingErrors, PostApiEkbImportVesselBillingFilterListData, PostApiEkbImportVesselBillingFilterListResponses, PostApiEkbImportVesselBillingFilterListErrors, DeleteApiEkbImportVesselBillingByIdData, DeleteApiEkbImportVesselBillingByIdResponses, DeleteApiEkbImportVesselBillingByIdErrors, GetApiEkbImportVesselBillingByIdData, GetApiEkbImportVesselBillingByIdResponses, GetApiEkbImportVesselBillingByIdErrors, PutApiEkbImportVesselBillingByIdData, PutApiEkbImportVesselBillingByIdResponses, PutApiEkbImportVesselBillingByIdErrors, GetApiEkbImportVesselBillingByIdWithItemsData, GetApiEkbImportVesselBillingByIdWithItemsResponses, GetApiEkbImportVesselBillingByIdWithItemsErrors, GetApiEkbImportVesselBillingItemsData, GetApiEkbImportVesselBillingItemsResponses, GetApiEkbImportVesselBillingItemsErrors, GetApiEkbItemClassificationData, GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, PostApiEkbItemClassificationData, PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, DeleteApiEkbItemClassificationByIdData, DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, GetApiEkbItemClassificationByIdData, GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, PutApiEkbItemClassificationByIdData, PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, PostApiEkbItemClassificationFilterListData, PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, GetApiEkbJettyData, GetApiEkbJettyResponses, GetApiEkbJettyErrors, PostApiEkbJettyData, PostApiEkbJettyResponses, PostApiEkbJettyErrors, DeleteApiEkbJettyByIdData, DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, GetApiEkbJettyByIdData, GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, PutApiEkbJettyByIdData, PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, PostApiEkbJettyFilterListData, PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, GetApiEkbLocalVesselData, GetApiEkbLocalVesselResponses, GetApiEkbLocalVesselErrors, PostApiEkbLocalVesselData, PostApiEkbLocalVesselResponses, PostApiEkbLocalVesselErrors, PostApiEkbLocalVesselGenerateNextDocNumData, PostApiEkbLocalVesselGenerateNextDocNumResponses, PostApiEkbLocalVesselGenerateNextDocNumErrors, DeleteApiEkbLocalVesselByIdData, DeleteApiEkbLocalVesselByIdResponses, DeleteApiEkbLocalVesselByIdErrors, GetApiEkbLocalVesselByIdData, GetApiEkbLocalVesselByIdResponses, GetApiEkbLocalVesselByIdErrors, PutApiEkbLocalVesselByIdData, PutApiEkbLocalVesselByIdResponses, PutApiEkbLocalVesselByIdErrors, GetApiEkbLocalVesselByIdWithItemsData, GetApiEkbLocalVesselByIdWithItemsResponses, GetApiEkbLocalVesselByIdWithItemsErrors, PostApiEkbLocalVesselFilterListData, PostApiEkbLocalVesselFilterListResponses, PostApiEkbLocalVesselFilterListErrors, GetApiEkbLocalVesselBillingData, GetApiEkbLocalVesselBillingResponses, GetApiEkbLocalVesselBillingErrors, PostApiEkbLocalVesselBillingData, PostApiEkbLocalVesselBillingResponses, PostApiEkbLocalVesselBillingErrors, PostApiEkbLocalVesselBillingFilterListData, PostApiEkbLocalVesselBillingFilterListResponses, PostApiEkbLocalVesselBillingFilterListErrors, DeleteApiEkbLocalVesselBillingByIdData, DeleteApiEkbLocalVesselBillingByIdResponses, DeleteApiEkbLocalVesselBillingByIdErrors, GetApiEkbLocalVesselBillingByIdData, GetApiEkbLocalVesselBillingByIdResponses, GetApiEkbLocalVesselBillingByIdErrors, PutApiEkbLocalVesselBillingByIdData, PutApiEkbLocalVesselBillingByIdResponses, PutApiEkbLocalVesselBillingByIdErrors, GetApiEkbLocalVesselBillingByIdWithItemsData, GetApiEkbLocalVesselBillingByIdWithItemsResponses, GetApiEkbLocalVesselBillingByIdWithItemsErrors, GetApiEkbLocalVesselBillingItemsData, GetApiEkbLocalVesselBillingItemsResponses, GetApiEkbLocalVesselBillingItemsErrors, PostApiAccountLoginData, PostApiAccountLoginResponses, PostApiAccountLoginErrors, GetApiAccountLogoutData, GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, PostApiAccountCheckPasswordData, PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, GetApiEkbPortOfLoadingData, GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, PostApiEkbPortOfLoadingData, PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, DeleteApiEkbPortOfLoadingByIdData, DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, GetApiEkbPortOfLoadingByIdData, GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, PutApiEkbPortOfLoadingByIdData, PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, PostApiEkbPortOfLoadingFilterListData, PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, GetApiEkbPortServiceData, GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, PostApiEkbPortServiceData, PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, DeleteApiEkbPortServiceByIdData, DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, GetApiEkbPortServiceByIdData, GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, PutApiEkbPortServiceByIdData, PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, PostApiEkbPortServiceFilterListData, PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, GetApiAccountMyProfileData, GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, PutApiAccountMyProfileData, PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, PostApiAccountMyProfileChangePasswordData, PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, GetApiEkbSurveyorData, GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, PostApiEkbSurveyorData, PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, DeleteApiEkbSurveyorByIdData, DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, GetApiEkbSurveyorByIdData, GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, PutApiEkbSurveyorByIdData, PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, PostApiEkbSurveyorFilterListData, PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, DeleteApiMultiTenancyTenantsByIdData, DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsByIdData, GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, PutApiMultiTenancyTenantsByIdData, PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, GetApiMultiTenancyTenantsData, GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, PostApiMultiTenancyTenantsData, PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, GetApiEkbTenantData, GetApiEkbTenantResponses, GetApiEkbTenantErrors, PostApiEkbTenantData, PostApiEkbTenantResponses, PostApiEkbTenantErrors, DeleteApiEkbTenantByIdData, DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, GetApiEkbTenantByIdData, GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, PutApiEkbTenantByIdData, PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, PostApiEkbTenantFilterListData, PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, GetApiTestAuthStatusData, GetApiTestAuthStatusResponses, GetApiTestTokenInfoData, GetApiTestTokenInfoResponses, GetApiEkbTradingInvoiceData, GetApiEkbTradingInvoiceResponses, GetApiEkbTradingInvoiceErrors, PostApiEkbTradingInvoiceData, PostApiEkbTradingInvoiceResponses, PostApiEkbTradingInvoiceErrors, DeleteApiEkbTradingInvoiceByIdData, DeleteApiEkbTradingInvoiceByIdResponses, DeleteApiEkbTradingInvoiceByIdErrors, GetApiEkbTradingInvoiceByIdData, GetApiEkbTradingInvoiceByIdResponses, GetApiEkbTradingInvoiceByIdErrors, PutApiEkbTradingInvoiceByIdData, PutApiEkbTradingInvoiceByIdResponses, PutApiEkbTradingInvoiceByIdErrors, GetApiEkbTradingVesselData, GetApiEkbTradingVesselResponses, GetApiEkbTradingVesselErrors, PostApiEkbTradingVesselData, PostApiEkbTradingVesselResponses, PostApiEkbTradingVesselErrors, DeleteApiEkbTradingVesselByIdData, DeleteApiEkbTradingVesselByIdResponses, DeleteApiEkbTradingVesselByIdErrors, GetApiEkbTradingVesselByIdData, GetApiEkbTradingVesselByIdResponses, GetApiEkbTradingVesselByIdErrors, PutApiEkbTradingVesselByIdData, PutApiEkbTradingVesselByIdResponses, PutApiEkbTradingVesselByIdErrors, GetApiEkbTradingVesselByIdWithItemsData, GetApiEkbTradingVesselByIdWithItemsResponses, GetApiEkbTradingVesselByIdWithItemsErrors, GetApiEkbTradingVesselItemsData, GetApiEkbTradingVesselItemsResponses, GetApiEkbTradingVesselItemsErrors, PostApiEkbTradingVesselFilterListData, PostApiEkbTradingVesselFilterListResponses, PostApiEkbTradingVesselFilterListErrors, PostApiEkbVesselVesselHeadersData, PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, PostApiEkbVesselVesselItemsData, PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, PostApiEkbVesselByIdVesselHeaderData, PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, PostApiEkbVesselByIdVesselItemData, PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors, GetApiEkbZoneDetailInvoiceData, GetApiEkbZoneDetailInvoiceResponses, GetApiEkbZoneDetailInvoiceErrors, PostApiEkbZoneDetailInvoiceData, PostApiEkbZoneDetailInvoiceResponses, PostApiEkbZoneDetailInvoiceErrors, GetApiEkbZoneDetailInvoiceByIdByZoneDetailData, GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses, GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors, DeleteApiEkbZoneDetailInvoiceByIdData, DeleteApiEkbZoneDetailInvoiceByIdResponses, DeleteApiEkbZoneDetailInvoiceByIdErrors, GetApiEkbZoneDetailInvoiceByIdData, GetApiEkbZoneDetailInvoiceByIdResponses, GetApiEkbZoneDetailInvoiceByIdErrors, PutApiEkbZoneDetailInvoiceByIdData, PutApiEkbZoneDetailInvoiceByIdResponses, PutApiEkbZoneDetailInvoiceByIdErrors } from './types.gen';\nimport { client as _heyApiClient } from './client.gen';\n\nexport type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {\n    /**\n     * You can provide a client instance returned by `createClient()` instead of\n     * individual options. This might be also useful if you want to implement a\n     * custom client.\n     */\n    client?: Client;\n    /**\n     * You can pass arbitrary values through the `meta` object. This can be\n     * used to access values that aren't defined as part of the SDK function.\n     */\n    meta?: Record<string, unknown>;\n};\n\nexport const getApiAbpApplicationConfiguration = <ThrowOnError extends boolean = false>(options?: Options<GetApiAbpApplicationConfigurationData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiAbpApplicationConfigurationResponses, GetApiAbpApplicationConfigurationErrors, ThrowOnError>({\n        url: '/api/abp/application-configuration',\n        ...options\n    });\n};\n\nexport const getApiAbpMultiTenancyTenantsByNameByName = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByNameByNameData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByNameByNameResponses, GetApiAbpMultiTenancyTenantsByNameByNameErrors, ThrowOnError>({\n        url: '/api/abp/multi-tenancy/tenants/by-name/{name}',\n        ...options\n    });\n};\n\nexport const getApiAbpMultiTenancyTenantsByIdById = <ThrowOnError extends boolean = false>(options: Options<GetApiAbpMultiTenancyTenantsByIdByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiAbpMultiTenancyTenantsByIdByIdResponses, GetApiAbpMultiTenancyTenantsByIdByIdErrors, ThrowOnError>({\n        url: '/api/abp/multi-tenancy/tenants/by-id/{id}',\n        ...options\n    });\n};\n\nexport const postApiAccountRegister = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountRegisterData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountRegisterResponses, PostApiAccountRegisterErrors, ThrowOnError>({\n        url: '/api/account/register',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiAccountSendPasswordResetCode = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountSendPasswordResetCodeData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountSendPasswordResetCodeResponses, PostApiAccountSendPasswordResetCodeErrors, ThrowOnError>({\n        url: '/api/account/send-password-reset-code',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiAccountVerifyPasswordResetToken = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountVerifyPasswordResetTokenData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountVerifyPasswordResetTokenResponses, PostApiAccountVerifyPasswordResetTokenErrors, ThrowOnError>({\n        url: '/api/account/verify-password-reset-token',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiAccountResetPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountResetPasswordData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountResetPasswordResponses, PostApiAccountResetPasswordErrors, ThrowOnError>({\n        url: '/api/account/reset-password',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiMasterAgentFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiMasterAgentFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiMasterAgentFilterListResponses, PostApiMasterAgentFilterListErrors, ThrowOnError>({\n        url: '/api/master/agent/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiMasterAgent = <ThrowOnError extends boolean = false>(options?: Options<GetApiMasterAgentData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiMasterAgentResponses, GetApiMasterAgentErrors, ThrowOnError>({\n        url: '/api/master/agent',\n        ...options\n    });\n};\n\nexport const postApiMasterAgent = <ThrowOnError extends boolean = false>(options?: Options<PostApiMasterAgentData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiMasterAgentResponses, PostApiMasterAgentErrors, ThrowOnError>({\n        url: '/api/master/agent',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMasterAgentByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiMasterAgentByIdResponses, DeleteApiMasterAgentByIdErrors, ThrowOnError>({\n        url: '/api/master/agent/{id}',\n        ...options\n    });\n};\n\nexport const getApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<GetApiMasterAgentByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiMasterAgentByIdResponses, GetApiMasterAgentByIdErrors, ThrowOnError>({\n        url: '/api/master/agent/{id}',\n        ...options\n    });\n};\n\nexport const putApiMasterAgentById = <ThrowOnError extends boolean = false>(options: Options<PutApiMasterAgentByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiMasterAgentByIdResponses, PutApiMasterAgentByIdErrors, ThrowOnError>({\n        url: '/api/master/agent/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiAuthLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAuthLogoutData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiAuthLogoutResponses, unknown, ThrowOnError>({\n        url: '/api/Auth/logout',\n        ...options\n    });\n};\n\nexport const getApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBcTypeData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbBcTypeResponses, GetApiEkbBcTypeErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type',\n        ...options\n    });\n};\n\nexport const postApiEkbBcType = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeResponses, PostApiEkbBcTypeErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBcTypeByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBcTypeByIdResponses, DeleteApiEkbBcTypeByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBcTypeByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbBcTypeByIdResponses, GetApiEkbBcTypeByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbBcTypeById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBcTypeByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbBcTypeByIdResponses, PutApiEkbBcTypeByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbBcTypeFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBcTypeFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBcTypeFilterListResponses, PostApiEkbBcTypeFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/bc-type/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbBoundedZoneList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneListResponses, PostApiEkbBoundedZoneListErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbBoundedZoneVesselHeaders = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneVesselHeadersData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneVesselHeadersResponses, PostApiEkbBoundedZoneVesselHeadersErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/vessel-headers',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBoundedZoneData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbBoundedZoneResponses, GetApiEkbBoundedZoneErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone',\n        ...options\n    });\n};\n\nexport const postApiEkbBoundedZone = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneResponses, PostApiEkbBoundedZoneErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBoundedZoneByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBoundedZoneByIdResponses, DeleteApiEkbBoundedZoneByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBoundedZoneByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbBoundedZoneByIdResponses, GetApiEkbBoundedZoneByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbBoundedZoneById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBoundedZoneByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbBoundedZoneByIdResponses, PutApiEkbBoundedZoneByIdErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbBoundedZoneFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneFilterListResponses, PostApiEkbBoundedZoneFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbBoundedZoneQueryableWithVesselHeaders = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBoundedZoneQueryableWithVesselHeadersData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbBoundedZoneQueryableWithVesselHeadersResponses, GetApiEkbBoundedZoneQueryableWithVesselHeadersErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/queryable-with-vessel-headers',\n        ...options\n    });\n};\n\nexport const postApiEkbBoundedZoneQueryableWithVesselHeadersDto = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoResponses, PostApiEkbBoundedZoneQueryableWithVesselHeadersDtoErrors, ThrowOnError>({\n        url: '/api/ekb/bounded-zone/queryable-with-vessel-headers-dto',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbBusinessPartnerData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerResponses, GetApiEkbBusinessPartnerErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner',\n        ...options\n    });\n};\n\nexport const postApiEkbBusinessPartner = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerResponses, PostApiEkbBusinessPartnerErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbBusinessPartnerByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbBusinessPartnerByIdResponses, DeleteApiEkbBusinessPartnerByIdErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbBusinessPartnerByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbBusinessPartnerByIdResponses, GetApiEkbBusinessPartnerByIdErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbBusinessPartnerById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbBusinessPartnerByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbBusinessPartnerByIdResponses, PutApiEkbBusinessPartnerByIdErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbBusinessPartnerFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbBusinessPartnerFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbBusinessPartnerFilterListResponses, PostApiEkbBusinessPartnerFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/business-partner/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbCargoData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbCargoResponses, GetApiEkbCargoErrors, ThrowOnError>({\n        url: '/api/ekb/cargo',\n        ...options\n    });\n};\n\nexport const postApiEkbCargo = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoResponses, PostApiEkbCargoErrors, ThrowOnError>({\n        url: '/api/ekb/cargo',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbCargoByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbCargoByIdResponses, DeleteApiEkbCargoByIdErrors, ThrowOnError>({\n        url: '/api/ekb/cargo/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbCargoByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbCargoByIdResponses, GetApiEkbCargoByIdErrors, ThrowOnError>({\n        url: '/api/ekb/cargo/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbCargoById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbCargoByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbCargoByIdResponses, PutApiEkbCargoByIdErrors, ThrowOnError>({\n        url: '/api/ekb/cargo/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbCargoFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbCargoFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbCargoFilterListResponses, PostApiEkbCargoFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/cargo/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbDestinationPortData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbDestinationPortResponses, GetApiEkbDestinationPortErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port',\n        ...options\n    });\n};\n\nexport const postApiEkbDestinationPort = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortResponses, PostApiEkbDestinationPortErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDestinationPortByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDestinationPortByIdResponses, DeleteApiEkbDestinationPortByIdErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDestinationPortByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbDestinationPortByIdResponses, GetApiEkbDestinationPortByIdErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbDestinationPortById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbDestinationPortByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbDestinationPortByIdResponses, PutApiEkbDestinationPortByIdErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbDestinationPortFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDestinationPortFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDestinationPortFilterListResponses, PostApiEkbDestinationPortFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/destination-port/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbDocAttachmentsList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsListResponses, PostApiEkbDocAttachmentsListErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDocAttachmentsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDocAttachmentsByIdResponses, DeleteApiEkbDocAttachmentsByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsByIdResponses, GetApiEkbDocAttachmentsByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbDocAttachmentsById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbDocAttachmentsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbDocAttachmentsByIdResponses, PutApiEkbDocAttachmentsByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbDocAttachments = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsResponses, PostApiEkbDocAttachmentsErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbDocAttachmentsUpload = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsUploadData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsUploadResponses, PostApiEkbDocAttachmentsUploadErrors, ThrowOnError>({\n        ...formDataBodySerializer,\n        url: '/api/ekb/doc-attachments/upload',\n        ...options,\n        headers: {\n            'Content-Type': null,\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbDocAttachmentsUploadWithContent = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbDocAttachmentsUploadWithContentData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbDocAttachmentsUploadWithContentResponses, PostApiEkbDocAttachmentsUploadWithContentErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/upload-with-content',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbDocAttachmentsDownloadById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsDownloadByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbDocAttachmentsDownloadByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/download/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbDocAttachmentsByReferenceByReferenceId = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsByReferenceByReferenceIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsByReferenceByReferenceIdResponses, GetApiEkbDocAttachmentsByReferenceByReferenceIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/by-reference/{referenceId}',\n        ...options\n    });\n};\n\nexport const deleteApiEkbDocAttachmentsFileById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbDocAttachmentsFileByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbDocAttachmentsFileByIdResponses, DeleteApiEkbDocAttachmentsFileByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/file/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbDocAttachmentsInfoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbDocAttachmentsInfoByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbDocAttachmentsInfoByIdResponses, GetApiEkbDocAttachmentsInfoByIdErrors, ThrowOnError>({\n        url: '/api/ekb/doc-attachments/info/{id}',\n        ...options\n    });\n};\n\nexport const postApiAccountDynamicClaimsRefresh = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountDynamicClaimsRefreshData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountDynamicClaimsRefreshResponses, PostApiAccountDynamicClaimsRefreshErrors, ThrowOnError>({\n        url: '/api/account/dynamic-claims/refresh',\n        ...options\n    });\n};\n\nexport const getApiEkbExportVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselResponses, GetApiEkbExportVesselErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel',\n        ...options\n    });\n};\n\nexport const postApiEkbExportVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselResponses, PostApiEkbExportVesselErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbExportVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselGenerateNextDocNumData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselGenerateNextDocNumResponses, PostApiEkbExportVesselGenerateNextDocNumErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/generate-next-doc-num',\n        ...options\n    });\n};\n\nexport const deleteApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbExportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbExportVesselByIdResponses, DeleteApiEkbExportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselByIdResponses, GetApiEkbExportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbExportVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbExportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbExportVesselByIdResponses, PutApiEkbExportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbExportVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselFilterListResponses, PostApiEkbExportVesselFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbExportVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselByIdWithItemsResponses, GetApiEkbExportVesselByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel/{id}/with-items',\n        ...options\n    });\n};\n\nexport const getApiEkbExportVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingResponses, GetApiEkbExportVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing',\n        ...options\n    });\n};\n\nexport const postApiEkbExportVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselBillingData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselBillingResponses, PostApiEkbExportVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbExportVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbExportVesselBillingFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbExportVesselBillingFilterListResponses, PostApiEkbExportVesselBillingFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbExportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbExportVesselBillingByIdResponses, DeleteApiEkbExportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingByIdResponses, GetApiEkbExportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbExportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbExportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbExportVesselBillingByIdResponses, PutApiEkbExportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbExportVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbExportVesselBillingByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingByIdWithItemsResponses, GetApiEkbExportVesselBillingByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/{id}/with-items',\n        ...options\n    });\n};\n\nexport const getApiEkbExportVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbExportVesselBillingItemsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbExportVesselBillingItemsResponses, GetApiEkbExportVesselBillingItemsErrors, ThrowOnError>({\n        url: '/api/ekb/export-vessel-billing/items',\n        ...options\n    });\n};\n\nexport const getApiEkbFilesDownloadById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesDownloadByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesDownloadByIdErrors, ThrowOnError>({\n        url: '/api/ekb/files/download/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbFilesStreamById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesStreamByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesStreamByIdErrors, ThrowOnError>({\n        url: '/api/ekb/files/stream/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbFilesStreamByFilePath = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesStreamByFilePathData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesStreamByFilePathErrors, ThrowOnError>({\n        url: '/api/ekb/files/stream/{filePath}',\n        ...options\n    });\n};\n\nexport const getApiEkbFilesInfoById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbFilesInfoByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<unknown, GetApiEkbFilesInfoByIdErrors, ThrowOnError>({\n        url: '/api/ekb/files/info/{id}',\n        ...options\n    });\n};\n\nexport const getApiHealthKubernetes = <ThrowOnError extends boolean = false>(options?: Options<GetApiHealthKubernetesData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiHealthKubernetesResponses, unknown, ThrowOnError>({\n        url: '/api/health/kubernetes',\n        ...options\n    });\n};\n\nexport const getApiEkbImportVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselResponses, GetApiEkbImportVesselErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel',\n        ...options\n    });\n};\n\nexport const postApiEkbImportVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselResponses, PostApiEkbImportVesselErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbImportVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselGenerateNextDocNumData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselGenerateNextDocNumResponses, PostApiEkbImportVesselGenerateNextDocNumErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/generate-next-doc-num',\n        ...options\n    });\n};\n\nexport const deleteApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbImportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbImportVesselByIdResponses, DeleteApiEkbImportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselByIdResponses, GetApiEkbImportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbImportVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbImportVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbImportVesselByIdResponses, PutApiEkbImportVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbImportVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselByIdWithItemsResponses, GetApiEkbImportVesselByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/{id}/with-items',\n        ...options\n    });\n};\n\nexport const postApiEkbImportVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselFilterListResponses, PostApiEkbImportVesselFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbImportVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingResponses, GetApiEkbImportVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing',\n        ...options\n    });\n};\n\nexport const postApiEkbImportVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselBillingData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselBillingResponses, PostApiEkbImportVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbImportVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbImportVesselBillingFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbImportVesselBillingFilterListResponses, PostApiEkbImportVesselBillingFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbImportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbImportVesselBillingByIdResponses, DeleteApiEkbImportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingByIdResponses, GetApiEkbImportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbImportVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbImportVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbImportVesselBillingByIdResponses, PutApiEkbImportVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbImportVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbImportVesselBillingByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingByIdWithItemsResponses, GetApiEkbImportVesselBillingByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/{id}/with-items',\n        ...options\n    });\n};\n\nexport const getApiEkbImportVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbImportVesselBillingItemsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbImportVesselBillingItemsResponses, GetApiEkbImportVesselBillingItemsErrors, ThrowOnError>({\n        url: '/api/ekb/import-vessel-billing/items',\n        ...options\n    });\n};\n\nexport const getApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbItemClassificationData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbItemClassificationResponses, GetApiEkbItemClassificationErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification',\n        ...options\n    });\n};\n\nexport const postApiEkbItemClassification = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationResponses, PostApiEkbItemClassificationErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbItemClassificationByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbItemClassificationByIdResponses, DeleteApiEkbItemClassificationByIdErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbItemClassificationByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbItemClassificationByIdResponses, GetApiEkbItemClassificationByIdErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbItemClassificationById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbItemClassificationByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbItemClassificationByIdResponses, PutApiEkbItemClassificationByIdErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbItemClassificationFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbItemClassificationFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbItemClassificationFilterListResponses, PostApiEkbItemClassificationFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/item-classification/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbJettyData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbJettyResponses, GetApiEkbJettyErrors, ThrowOnError>({\n        url: '/api/ekb/jetty',\n        ...options\n    });\n};\n\nexport const postApiEkbJetty = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyResponses, PostApiEkbJettyErrors, ThrowOnError>({\n        url: '/api/ekb/jetty',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbJettyByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbJettyByIdResponses, DeleteApiEkbJettyByIdErrors, ThrowOnError>({\n        url: '/api/ekb/jetty/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbJettyByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbJettyByIdResponses, GetApiEkbJettyByIdErrors, ThrowOnError>({\n        url: '/api/ekb/jetty/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbJettyById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbJettyByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbJettyByIdResponses, PutApiEkbJettyByIdErrors, ThrowOnError>({\n        url: '/api/ekb/jetty/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbJettyFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbJettyFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbJettyFilterListResponses, PostApiEkbJettyFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/jetty/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbLocalVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselResponses, GetApiEkbLocalVesselErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel',\n        ...options\n    });\n};\n\nexport const postApiEkbLocalVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselResponses, PostApiEkbLocalVesselErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbLocalVesselGenerateNextDocNum = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselGenerateNextDocNumData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselGenerateNextDocNumResponses, PostApiEkbLocalVesselGenerateNextDocNumErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/generate-next-doc-num',\n        ...options\n    });\n};\n\nexport const deleteApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbLocalVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbLocalVesselByIdResponses, DeleteApiEkbLocalVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselByIdResponses, GetApiEkbLocalVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbLocalVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbLocalVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbLocalVesselByIdResponses, PutApiEkbLocalVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbLocalVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselByIdWithItemsResponses, GetApiEkbLocalVesselByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/{id}/with-items',\n        ...options\n    });\n};\n\nexport const postApiEkbLocalVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselFilterListResponses, PostApiEkbLocalVesselFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbLocalVesselBilling = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingResponses, GetApiEkbLocalVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing',\n        ...options\n    });\n};\n\nexport const postApiEkbLocalVesselBilling = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselBillingData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselBillingResponses, PostApiEkbLocalVesselBillingErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbLocalVesselBillingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbLocalVesselBillingFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbLocalVesselBillingFilterListResponses, PostApiEkbLocalVesselBillingFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbLocalVesselBillingByIdResponses, DeleteApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingByIdResponses, GetApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbLocalVesselBillingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbLocalVesselBillingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbLocalVesselBillingByIdResponses, PutApiEkbLocalVesselBillingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbLocalVesselBillingByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbLocalVesselBillingByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingByIdWithItemsResponses, GetApiEkbLocalVesselBillingByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/{id}/with-items',\n        ...options\n    });\n};\n\nexport const getApiEkbLocalVesselBillingItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbLocalVesselBillingItemsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbLocalVesselBillingItemsResponses, GetApiEkbLocalVesselBillingItemsErrors, ThrowOnError>({\n        url: '/api/ekb/local-vessel-billing/items',\n        ...options\n    });\n};\n\nexport const postApiAccountLogin = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountLoginData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountLoginResponses, PostApiAccountLoginErrors, ThrowOnError>({\n        url: '/api/account/login',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiAccountLogout = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountLogoutData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiAccountLogoutResponses, GetApiAccountLogoutErrors, ThrowOnError>({\n        url: '/api/account/logout',\n        ...options\n    });\n};\n\nexport const postApiAccountCheckPassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountCheckPasswordData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountCheckPasswordResponses, PostApiAccountCheckPasswordErrors, ThrowOnError>({\n        url: '/api/account/check-password',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortOfLoadingData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingResponses, GetApiEkbPortOfLoadingErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading',\n        ...options\n    });\n};\n\nexport const postApiEkbPortOfLoading = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingResponses, PostApiEkbPortOfLoadingErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortOfLoadingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortOfLoadingByIdResponses, DeleteApiEkbPortOfLoadingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortOfLoadingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbPortOfLoadingByIdResponses, GetApiEkbPortOfLoadingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbPortOfLoadingById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortOfLoadingByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbPortOfLoadingByIdResponses, PutApiEkbPortOfLoadingByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbPortOfLoadingFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortOfLoadingFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbPortOfLoadingFilterListResponses, PostApiEkbPortOfLoadingFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/port-of-loading/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbPortServiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbPortServiceResponses, GetApiEkbPortServiceErrors, ThrowOnError>({\n        url: '/api/ekb/port-service',\n        ...options\n    });\n};\n\nexport const postApiEkbPortService = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceResponses, PostApiEkbPortServiceErrors, ThrowOnError>({\n        url: '/api/ekb/port-service',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbPortServiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbPortServiceByIdResponses, DeleteApiEkbPortServiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-service/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbPortServiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbPortServiceByIdResponses, GetApiEkbPortServiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-service/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbPortServiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbPortServiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbPortServiceByIdResponses, PutApiEkbPortServiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/port-service/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbPortServiceFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbPortServiceFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbPortServiceFilterListResponses, PostApiEkbPortServiceFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/port-service/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<GetApiAccountMyProfileData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiAccountMyProfileResponses, GetApiAccountMyProfileErrors, ThrowOnError>({\n        url: '/api/account/my-profile',\n        ...options\n    });\n};\n\nexport const putApiAccountMyProfile = <ThrowOnError extends boolean = false>(options?: Options<PutApiAccountMyProfileData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).put<PutApiAccountMyProfileResponses, PutApiAccountMyProfileErrors, ThrowOnError>({\n        url: '/api/account/my-profile',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiAccountMyProfileChangePassword = <ThrowOnError extends boolean = false>(options?: Options<PostApiAccountMyProfileChangePasswordData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiAccountMyProfileChangePasswordResponses, PostApiAccountMyProfileChangePasswordErrors, ThrowOnError>({\n        url: '/api/account/my-profile/change-password',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbSurveyorData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbSurveyorResponses, GetApiEkbSurveyorErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor',\n        ...options\n    });\n};\n\nexport const postApiEkbSurveyor = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorResponses, PostApiEkbSurveyorErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbSurveyorByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbSurveyorByIdResponses, DeleteApiEkbSurveyorByIdErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbSurveyorByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbSurveyorByIdResponses, GetApiEkbSurveyorByIdErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbSurveyorById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbSurveyorByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbSurveyorByIdResponses, PutApiEkbSurveyorByIdErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbSurveyorFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbSurveyorFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbSurveyorFilterListResponses, PostApiEkbSurveyorFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/surveyor/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdResponses, DeleteApiMultiTenancyTenantsByIdErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}',\n        ...options\n    });\n};\n\nexport const getApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdResponses, GetApiMultiTenancyTenantsByIdErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}',\n        ...options\n    });\n};\n\nexport const putApiMultiTenancyTenantsById = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdResponses, PutApiMultiTenancyTenantsByIdErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<GetApiMultiTenancyTenantsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsResponses, GetApiMultiTenancyTenantsErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants',\n        ...options\n    });\n};\n\nexport const postApiMultiTenancyTenants = <ThrowOnError extends boolean = false>(options?: Options<PostApiMultiTenancyTenantsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiMultiTenancyTenantsResponses, PostApiMultiTenancyTenantsErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, DeleteApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',\n        ...options\n    });\n};\n\nexport const getApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<GetApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, GetApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',\n        ...options\n    });\n};\n\nexport const putApiMultiTenancyTenantsByIdDefaultConnectionString = <ThrowOnError extends boolean = false>(options: Options<PutApiMultiTenancyTenantsByIdDefaultConnectionStringData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiMultiTenancyTenantsByIdDefaultConnectionStringResponses, PutApiMultiTenancyTenantsByIdDefaultConnectionStringErrors, ThrowOnError>({\n        url: '/api/multi-tenancy/tenants/{id}/default-connection-string',\n        ...options\n    });\n};\n\nexport const getApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTenantData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbTenantResponses, GetApiEkbTenantErrors, ThrowOnError>({\n        url: '/api/ekb/tenant',\n        ...options\n    });\n};\n\nexport const postApiEkbTenant = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantResponses, PostApiEkbTenantErrors, ThrowOnError>({\n        url: '/api/ekb/tenant',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTenantByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTenantByIdResponses, DeleteApiEkbTenantByIdErrors, ThrowOnError>({\n        url: '/api/ekb/tenant/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTenantByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbTenantByIdResponses, GetApiEkbTenantByIdErrors, ThrowOnError>({\n        url: '/api/ekb/tenant/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbTenantById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTenantByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbTenantByIdResponses, PutApiEkbTenantByIdErrors, ThrowOnError>({\n        url: '/api/ekb/tenant/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const postApiEkbTenantFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTenantFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbTenantFilterListResponses, PostApiEkbTenantFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/tenant/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiTestAuthStatus = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestAuthStatusData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiTestAuthStatusResponses, unknown, ThrowOnError>({\n        url: '/api/test/auth-status',\n        ...options\n    });\n};\n\nexport const getApiTestTokenInfo = <ThrowOnError extends boolean = false>(options?: Options<GetApiTestTokenInfoData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiTestTokenInfoResponses, unknown, ThrowOnError>({\n        url: '/api/test/token-info',\n        ...options\n    });\n};\n\nexport const getApiEkbTradingInvoice = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTradingInvoiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbTradingInvoiceResponses, GetApiEkbTradingInvoiceErrors, ThrowOnError>({\n        url: '/api/ekb/trading-invoice',\n        ...options\n    });\n};\n\nexport const postApiEkbTradingInvoice = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingInvoiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingInvoiceResponses, PostApiEkbTradingInvoiceErrors, ThrowOnError>({\n        url: '/api/ekb/trading-invoice',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTradingInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTradingInvoiceByIdResponses, DeleteApiEkbTradingInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-invoice/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbTradingInvoiceByIdResponses, GetApiEkbTradingInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-invoice/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbTradingInvoiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTradingInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbTradingInvoiceByIdResponses, PutApiEkbTradingInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-invoice/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbTradingVessel = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselResponses, GetApiEkbTradingVesselErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel',\n        ...options\n    });\n};\n\nexport const postApiEkbTradingVessel = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingVesselData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingVesselResponses, PostApiEkbTradingVesselErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const deleteApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbTradingVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbTradingVesselByIdResponses, DeleteApiEkbTradingVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselByIdResponses, GetApiEkbTradingVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbTradingVesselById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbTradingVesselByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbTradingVesselByIdResponses, PutApiEkbTradingVesselByIdErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};\n\nexport const getApiEkbTradingVesselByIdWithItems = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbTradingVesselByIdWithItemsData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbTradingVesselByIdWithItemsResponses, GetApiEkbTradingVesselByIdWithItemsErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/{id}/with-items',\n        ...options\n    });\n};\n\nexport const getApiEkbTradingVesselItems = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbTradingVesselItemsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbTradingVesselItemsResponses, GetApiEkbTradingVesselItemsErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/items',\n        ...options\n    });\n};\n\nexport const postApiEkbTradingVesselFilterList = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbTradingVesselFilterListData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbTradingVesselFilterListResponses, PostApiEkbTradingVesselFilterListErrors, ThrowOnError>({\n        url: '/api/ekb/trading-vessel/filter-list',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbVesselVesselHeaders = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselHeadersData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselHeadersResponses, PostApiEkbVesselVesselHeadersErrors, ThrowOnError>({\n        url: '/api/ekb/vessel/vessel-headers',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbVesselVesselItems = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbVesselVesselItemsData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbVesselVesselItemsResponses, PostApiEkbVesselVesselItemsErrors, ThrowOnError>({\n        url: '/api/ekb/vessel/vessel-items',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const postApiEkbVesselByIdVesselHeader = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselHeaderData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselHeaderResponses, PostApiEkbVesselByIdVesselHeaderErrors, ThrowOnError>({\n        url: '/api/ekb/vessel/{id}/vessel-header',\n        ...options\n    });\n};\n\nexport const postApiEkbVesselByIdVesselItem = <ThrowOnError extends boolean = false>(options: Options<PostApiEkbVesselByIdVesselItemData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).post<PostApiEkbVesselByIdVesselItemResponses, PostApiEkbVesselByIdVesselItemErrors, ThrowOnError>({\n        url: '/api/ekb/vessel/{id}/vessel-item',\n        ...options\n    });\n};\n\nexport const getApiEkbZoneDetailInvoice = <ThrowOnError extends boolean = false>(options?: Options<GetApiEkbZoneDetailInvoiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceResponses, GetApiEkbZoneDetailInvoiceErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice',\n        ...options\n    });\n};\n\nexport const postApiEkbZoneDetailInvoice = <ThrowOnError extends boolean = false>(options?: Options<PostApiEkbZoneDetailInvoiceData, ThrowOnError>) => {\n    return (options?.client ?? _heyApiClient).post<PostApiEkbZoneDetailInvoiceResponses, PostApiEkbZoneDetailInvoiceErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options?.headers\n        }\n    });\n};\n\nexport const getApiEkbZoneDetailInvoiceByIdByZoneDetail = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbZoneDetailInvoiceByIdByZoneDetailData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceByIdByZoneDetailResponses, GetApiEkbZoneDetailInvoiceByIdByZoneDetailErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice/{id}/by-zone-detail',\n        ...options\n    });\n};\n\nexport const deleteApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<DeleteApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).delete<DeleteApiEkbZoneDetailInvoiceByIdResponses, DeleteApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice/{id}',\n        ...options\n    });\n};\n\nexport const getApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<GetApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).get<GetApiEkbZoneDetailInvoiceByIdResponses, GetApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice/{id}',\n        ...options\n    });\n};\n\nexport const putApiEkbZoneDetailInvoiceById = <ThrowOnError extends boolean = false>(options: Options<PutApiEkbZoneDetailInvoiceByIdData, ThrowOnError>) => {\n    return (options.client ?? _heyApiClient).put<PutApiEkbZoneDetailInvoiceByIdResponses, PutApiEkbZoneDetailInvoiceByIdErrors, ThrowOnError>({\n        url: '/api/ekb/zone-detail-invoice/{id}',\n        ...options,\n        headers: {\n            'Content-Type': 'application/json',\n            ...options.headers\n        }\n    });\n};"], "names": ["putApiEkbBoundedZoneById", "options", "_heyApiClient", "postApiEkbJetty", "putApiEkbJettyById", "postApiEkbJettyFilterList"], "mappings": "mEA8Pa,MAAAA,EAAkEC,IACnEA,EAAQ,QAAUC,GAAe,IAAqF,CAC1H,IAAK,6BACL,GAAGD,EACH,QAAS,CACL,eAAgB,mBAChB,GAAGA,EAAQ,OAAA,CACf,CACH,EA2pBQE,EAAyDF,IAC1DA,GAAS,QAAUC,GAAe,KAAoE,CAC1G,IAAK,iBACL,GAAGD,EACH,QAAS,CACL,eAAgB,mBAChB,GAAGA,GAAS,OAAA,CAChB,CACH,EAiBQG,EAA4DH,IAC7DA,EAAQ,QAAUC,GAAe,IAAyE,CAC9G,IAAK,sBACL,GAAGD,EACH,QAAS,CACL,eAAgB,mBAChB,GAAGA,EAAQ,OAAA,CACf,CACH,EAGQI,EAAmEJ,IACpEA,GAAS,QAAUC,GAAe,KAAwF,CAC9H,IAAK,6BACL,GAAGD,EACH,QAAS,CACL,eAAgB,mBAChB,GAAGA,GAAS,OAAA,CAChB,CACH"}