{"version": 3, "file": "IconChevronDown-DtNUJLVx.js", "sources": ["../../../../../frontend/node_modules/.pnpm/@tabler+icons-react@3.34.0_react@19.1.0/node_modules/@tabler/icons-react/dist/esm/icons/IconChevronDown.mjs"], "sourcesContent": ["/**\n * @license @tabler/icons-react v3.34.0 - MIT\n *\n * This source code is licensed under the MIT license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createReactComponent from '../createReactComponent.mjs';\n\nvar IconChevronDown = createReactComponent(\"outline\", \"chevron-down\", \"IconChevronDown\", [[\"path\", { \"d\": \"M6 9l6 6l6 -6\", \"key\": \"svg-0\" }]]);\n\nexport { IconChevronDown as default };\n//# sourceMappingURL=IconChevronDown.mjs.map\n"], "names": ["IconChevronDown", "createReactComponent"], "mappings": "6CAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASG,IAACA,EAAkBC,EAAqB,UAAW,eAAgB,kBAAmB,CAAC,CAAC,OAAQ,CAAE,EAAK,gBAAiB,IAAO,OAAS,CAAA,CAAC,CAAC", "x_google_ignoreList": [0]}