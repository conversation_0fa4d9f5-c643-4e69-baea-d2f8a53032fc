# Token Refresh Optimization

## Problem
The application was experiencing frequent redirects to the identity server due to overly aggressive token refresh mechanisms. This was causing:
- Multiple authorization requests in the browser network tab
- Poor user experience with frequent redirects
- Unnecessary load on the identity server

## Root Causes
1. **Frontend OIDC Token Manager**: Refreshing tokens every 5 minutes + on every user activity
2. **Backend Silent Token Refresh Service**: Refreshing tokens every 5 minutes
3. **OIDC Configuration**: Automatic refresh interval set to 5 minutes
4. **No Rate Limiting**: Multiple simultaneous refresh attempts possible

## Solutions Implemented

### 1. Frontend Token Manager Optimization (`frontend/src/lib/oidc-init.ts`)

**Changes Made:**
- Added rate limiting: Maximum one refresh attempt per minute
- Added refresh state tracking to prevent simultaneous attempts
- Reduced activity-based refresh triggers (only click and keypress events)
- Increased activity debounce delay from 1 second to 30 seconds
- Changed periodic refresh interval from 5 minutes to 15 minutes
- Added visibility change optimization (only refresh if page was hidden for 5+ minutes)
- Added token status check before attempting refresh

**Key Improvements:**
```typescript
// Rate limiting
if (now - this.lastRefreshAttempt < 60000) {
  console.log('Token refresh rate limited, skipping');
  return false;
}

// Check if refresh is actually needed
const status = await this.checkTokenStatus();
if (status?.has_valid_token) {
  console.log('Token is still valid, no refresh needed');
  return true;
}
```

### 2. Backend Silent Token Refresh Service (`src/Imip.JettyApproval.Web/Services/SilentTokenRefreshService.cs`)

**Changes Made:**
- Increased refresh interval from 5 minutes to 15 minutes
- Added rate limiting: Maximum one refresh attempt per 5 minutes
- Added thread-safe refresh state tracking
- Enhanced logging for better debugging

**Key Improvements:**
```csharp
// Rate limiting with thread safety
lock (_refreshLock)
{
    if (DateTime.UtcNow - _lastRefreshAttempt < TimeSpan.FromMinutes(5))
    {
        _logger.LogDebug("Token refresh rate limited, skipping");
        return;
    }
    _lastRefreshAttempt = DateTime.UtcNow;
}
```

### 3. OIDC Configuration Optimization (`src/Imip.JettyApproval.Web/Services/AuthenticationConfigurationService.cs`)

**Changes Made:**
- Increased OIDC refresh interval from 5 minutes to 15 minutes
- Removed duplicate refresh interval configuration

### 4. JSON Deserialization Fix (`src/Imip.JettyApproval.Web/Services/AppToAppService.cs`)

**Additional Fix Applied:**
- Added `SanitizeJsonNumbers` method to handle invalid JSON with leading zeros
- Applied sanitization to all JSON deserialization points
- Added more permissive JSON serializer options

## Configuration Summary

| Component | Before | After | Improvement |
|-----------|--------|-------|-------------|
| Frontend Periodic Refresh | 5 minutes | 15 minutes | 3x less frequent |
| Frontend Activity Refresh | Every activity (1s delay) | Click/keypress only (30s delay) | ~10x less frequent |
| Backend Silent Refresh | 5 minutes | 15 minutes + rate limiting | 3x less frequent + protection |
| OIDC Auto Refresh | 5 minutes | 15 minutes | 3x less frequent |

## Expected Results

1. **Reduced Network Traffic**: ~70% reduction in token refresh requests
2. **Better User Experience**: Fewer unexpected redirects to identity server
3. **Improved Performance**: Less load on both client and server
4. **Maintained Security**: Tokens still refresh proactively before expiration

## Monitoring

To monitor the effectiveness of these changes:

1. **Browser Network Tab**: Should see significantly fewer `/api/token/refresh` and authorization requests
2. **Application Logs**: Look for "Token refresh rate limited" messages
3. **Identity Server Logs**: Should see reduced token endpoint requests
4. **User Reports**: Fewer complaints about unexpected logouts/redirects

## Additional Recommendations

1. **Token Lifetime Configuration**: Consider increasing access token lifetime on the identity server if security policies allow
2. **Session Monitoring**: Implement user activity monitoring to only refresh tokens for active users
3. **Error Handling**: Add better error handling for network failures during token refresh
4. **Metrics**: Add application metrics to track token refresh success/failure rates

## Rollback Plan

If issues arise, the changes can be easily reverted by:
1. Changing refresh intervals back to 5 minutes
2. Removing rate limiting logic
3. Restoring original activity event listeners

All changes are backward compatible and don't affect the core authentication flow.
