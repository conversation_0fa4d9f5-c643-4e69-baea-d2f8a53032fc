import{a as m,j as e}from"./vendor-6tJeyfYI.js";import{A as R,o as I,q as M,r as O,s as k,D as p,v as w,B as c,w as C,x as z,I as E,y as L,z as x}from"./app-layout-rNt37hVL.js";import{C as v}from"./checkbox-D1loOtZt.js";import{I as V}from"./input-DlXlkYlT.js";import{T as B,a as P,b,c as j,d as $,e as u}from"./table-BKSoE52x.js";import{I as q}from"./IconChevronDown-DtNUJLVx.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";const d=Array(10).fill(null).map((o,l)=>({id:`app_${l+1}`,vesselName:`MV. GOLDEN ACE V. 00${l+1}`,arrivalDate:"2025-05-01",departureDate:"2025-05-02",itemName:"STEEL PRODUCT 10MT",requestBy:"USER1"}));function Q(){const[o,l]=m.useState(""),[r,h]=m.useState(new Set),[i,N]=m.useState(new Set(Object.keys(d[0]))),n=d.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(o.toLowerCase()))),f=s=>{h(s?new Set(n.map(a=>a.id)):new Set)},g=(s,a)=>{const t=new Set(r);a?t.add(s):t.delete(s),h(t)},S=(s,a)=>{const t=new Set(i);a?t.add(s):t.delete(s),N(t)},D=s=>{},A=s=>{},T=s=>{};return e.jsx(R,{children:e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(I,{children:[e.jsx(M,{children:e.jsx(O,{className:"text-2xl font-bold",children:"Approval Requests"})}),e.jsxs(k,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(V,{placeholder:"Filter lines...",value:o,onChange:s=>l(s.target.value),className:"max-w-sm"}),e.jsxs(p,{children:[e.jsx(w,{asChild:!0,children:e.jsxs(c,{variant:"outline",className:"ml-auto",children:["Columns ",e.jsx(q,{className:"ml-2 h-4 w-4"})]})}),e.jsx(C,{align:"end",children:Object.keys(d[0]).map(s=>e.jsx(z,{className:"capitalize",checked:i.has(s),onCheckedChange:a=>S(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(B,{children:[e.jsx(P,{children:e.jsxs(b,{children:[e.jsx(j,{className:"w-[30px]",children:e.jsx(v,{checked:r.size===n.length&&n.length>0,onCheckedChange:s=>f(s===!0)})}),Object.keys(d[0]).map(s=>i.has(s)&&s!=="id"&&e.jsx(j,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(j,{className:"text-right",children:"Actions"})]})}),e.jsx($,{children:n.map(s=>e.jsxs(b,{children:[e.jsx(u,{children:e.jsx(v,{checked:r.has(s.id),onCheckedChange:a=>g(s.id,a===!0)})}),Object.entries(s).map(([a,t])=>i.has(a)&&a!=="id"&&e.jsx(u,{children:t},a)),e.jsx(u,{className:"text-right",children:e.jsxs(p,{children:[e.jsx(w,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(E,{className:"h-4 w-4"})]})}),e.jsxs(C,{align:"end",children:[e.jsx(L,{children:"Actions"}),e.jsx(x,{onClick:()=>D(s.id),children:"Preview"}),e.jsx(x,{onClick:()=>A(s.id),children:"Approve"}),e.jsx(x,{onClick:()=>T(s.id),children:"Reject"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[r.size," of ",n.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})})})}export{Q as default};
//# sourceMappingURL=page-CnjKP3d3.js.map
