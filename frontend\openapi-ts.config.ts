import { defineConfig, defaultPlugins } from '@hey-api/openapi-ts';

const isEkb = process.env.OPENAPI_TYPE === 'ekb';

export default defineConfig({
  input: isEkb ? 'swagger-ekb.json' : 'swagger.json',
  output: isEkb ? 'src/clientEkb' : 'src/client',
  plugins: [
    ...defaultPlugins,
    '@hey-api/client-fetch',
    {
      name: '@hey-api/typescript',
      readOnlyWriteOnlyBehavior: 'off',
    },
  ],
});