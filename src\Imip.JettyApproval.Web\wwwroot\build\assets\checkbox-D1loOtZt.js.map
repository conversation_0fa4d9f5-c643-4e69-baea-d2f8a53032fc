{"version": 3, "file": "checkbox-D1loOtZt.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/minus.js", "../../../../../frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [[\"path\", { d: \"M5 12h14\", key: \"1ays0h\" }]];\nconst Minus = createLucideIcon(\"minus\", __iconNode);\n\nexport { __iconNode, Minus as default };\n//# sourceMappingURL=minus.js.map\n", "'use client';\n\nimport { cn } from '@/lib/utils';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { Check, Minus } from 'lucide-react';\nimport { Checkbox as CheckboxPrimitive } from 'radix-ui';\nimport * as React from 'react';\n\n// Define the variants for the Checkbox using cva.\nconst checkboxVariants = cva(\n  `\n    group peer bg-background shrink-0 rounded-md border border-input ring-offset-background focus-visible:outline-none \n    focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 \n    aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20\n    [[data-invalid=true]_&]:border-destructive/60 [[data-invalid=true]_&]:ring-destructive/10  dark:[[data-invalid=true]_&]:border-destructive dark:[[data-invalid=true]_&]:ring-destructive/20,\n    data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=checked]:text-primary-foreground data-[state=indeterminate]:bg-primary data-[state=indeterminate]:border-primary data-[state=indeterminate]:text-primary-foreground\n    `,\n  {\n    variants: {\n      size: {\n        sm: 'size-4.5 [&_svg]:size-3',\n        md: 'size-5 [&_svg]:size-3.5',\n        lg: 'size-5.5 [&_svg]:size-4',\n      },\n    },\n    defaultVariants: {\n      size: 'md',\n    },\n  },\n);\n\nfunction Checkbox({\n  className,\n  size,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root> & VariantProps<typeof checkboxVariants>) {\n  return (\n    <CheckboxPrimitive.Root data-slot=\"checkbox\" className={cn(checkboxVariants({ size }), className)} {...props}>\n      <CheckboxPrimitive.Indicator className={cn('flex items-center justify-center text-current')}>\n        <Check className=\"group-data-[state=indeterminate]:hidden\" />\n        <Minus className=\"hidden group-data-[state=indeterminate]:block\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  );\n}\n\nexport { Checkbox };\n"], "names": ["__iconNode", "Minus", "createLucideIcon", "checkboxVariants", "cva", "Checkbox", "className", "size", "props", "jsx", "CheckboxPrimitive.Root", "cn", "jsxs", "CheckboxPrimitive.Indicator", "Check"], "mappings": "4JAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CAAC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,CAAC,EACxDC,EAAQC,EAAiB,QAASF,CAAU,ECD5CG,EAAmBC,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,CACE,SAAU,CACR,KAAM,CACJ,GAAI,0BACJ,GAAI,0BACJ,GAAI,yBAAA,CAER,EACA,gBAAiB,CACf,KAAM,IAAA,CACR,CAEJ,EAEA,SAASC,EAAS,CAChB,UAAAC,EACA,KAAAC,EACA,GAAGC,CACL,EAAgG,CAE5F,OAAAC,EAAA,IAACC,EAAA,CAAuB,YAAU,WAAW,UAAWC,EAAGR,EAAiB,CAAE,KAAAI,EAAM,EAAGD,CAAS,EAAI,GAAGE,EACrG,SAACI,OAAAC,EAAA,CAA4B,UAAWF,EAAG,+CAA+C,EACxF,SAAA,CAACF,EAAAA,IAAAK,EAAA,CAAM,UAAU,yCAA0C,CAAA,EAC3DL,EAAAA,IAACR,EAAM,CAAA,UAAU,+CAAgD,CAAA,CAAA,CAAA,CACnE,CACF,CAAA,CAEJ", "x_google_ignoreList": [0]}