import * as z from 'zod';

export const localVesselHeaderSchema = z.object({
  docNum: z.string().min(1, 'DocNum is required'),
  vesselId: z.string().min(1, 'Vessel Name is required'),
  voyage: z.string().min(1, 'Voyage is required'),
  postingDate: z.string().min(1, 'Posting Date is required'),
  vesselArrival: z.string().min(1, 'Arrival Date is required'),
  vesselDeparture: z.string().min(1, 'Departure Date is required'),
  portOriginId: z.string().optional(),
  destinationPortId: z.string().optional(),
  deleted: z.string().optional(),
  docType: z.string().optional(),
  isChange: z.string().optional(),
  isLocked: z.string().optional(),
  createdBy: z.string().optional(),
  docStatus: z.string().optional(),
  statusBms: z.string().optional(),
  transType: z.string().min(1, 'TransType is required'),
  concurrencyStamp: z.string().optional(),
  jettyId: z.string().min(1, 'Jetty is required'),
  status: z.string().optional(),
  vesselType: z.string().optional(),
  shipment: z.string().optional(),
  portOrigin: z.string().optional(),
  destinationPort: z.string().optional(),
  // Add more fields as needed from CreateUpdateLocalVesselDto
  bargeId: z.string().optional(),
  asideDate: z.string().optional(),
  castOfDate: z.string().optional(),
});

export type LocalVesselHeaderForm = z.infer<typeof localVesselHeaderSchema>; 