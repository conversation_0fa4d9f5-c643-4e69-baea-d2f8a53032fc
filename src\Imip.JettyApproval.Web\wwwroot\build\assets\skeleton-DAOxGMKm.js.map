{"version": 3, "file": "skeleton-DAOxGMKm.js", "sources": ["../../../../../frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\n  return <div data-slot=\"skeleton\" className={cn('animate-pulse rounded-md bg-accent', className)} {...props} />;\n}\n\nexport { Skeleton };\n"], "names": ["Skeleton", "className", "props", "jsx", "cn"], "mappings": "sFAGA,SAASA,EAAS,CAAE,UAAAC,EAAW,GAAGC,GAAsC,CAC/D,OAAAC,MAAC,MAAI,CAAA,YAAU,WAAW,UAAWC,EAAG,qCAAsCH,CAAS,EAAI,GAAGC,CAAO,CAAA,CAC9G"}