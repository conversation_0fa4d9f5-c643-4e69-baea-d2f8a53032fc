var Yr=Object.defineProperty;var Qr=(e,t,r)=>t in e?Yr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r;var Ve=(e,t,r)=>Qr(e,typeof t!="symbol"?t+"":t,r);import{a as d,j as a,R as Lt,h as Dt,$ as bt}from"./vendor-6tJeyfYI.js";import{P as J,e as Zr,f as Ot,g as zt,h as lt,i as V,j as Re,k as _e,B as es,r as ts,l as rs,V as $t,m as Vt,n as ss,o as as,p as Ft,u as os,q as ns,A as is,s as ls,D as cs,t as ds,v as us,w as ps,S as pe,x as ms,y as fs,I as hs,z as gs,E as bs,F as xs,G as vs,H as ys,J as ws,K as ks,M as js,N as Ts,Q as Cs,U as Es,W as Ns,X as Ss,Y as As,Z as Is,a as Ms,c as Rs,_ as Ps,a0 as _s,a1 as Ls,b as Ds,O as Os,a2 as zs,a3 as $s,a4 as Vs,a5 as Fs,a6 as Gs,a7 as Us,a8 as Bs,a9 as qs,aa as Hs,ab as Ws,ac as Ks,ad as Js,ae as Xs}from"./radix-e4nK4mWk.js";import{a as Ys,b as Qs,d as L,k as Zs,q as Gt,Y as Pe}from"./App-DnhJzTNn.js";const ea=(e,t,r,s)=>{const o=[r,{code:t,...s||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);te(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),e?.services?.logger?.warn&&e.services.logger.warn(...o)},xt={},Xe=(e,t,r,s)=>{te(r)&&xt[r]||(te(r)&&(xt[r]=new Date),ea(e,t,r,s))},Ut=(e,t)=>()=>{if(e.isInitialized)t();else{const r=()=>{setTimeout(()=>{e.off("initialized",r)},0),t()};e.on("initialized",r)}},Ye=(e,t,r)=>{e.loadNamespaces(t,Ut(e,r))},vt=(e,t,r,s)=>{if(te(r)&&(r=[r]),e.options.preload&&e.options.preload.indexOf(t)>-1)return Ye(e,r,s);r.forEach(o=>{e.options.ns.indexOf(o)<0&&e.options.ns.push(o)}),e.loadLanguages(t,Ut(e,s))},ta=(e,t,r={})=>!t.languages||!t.languages.length?(Xe(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0):t.hasLoadedNamespace(e,{lng:r.lng,precheck:(s,o)=>{if(r.bindI18n?.indexOf("languageChanging")>-1&&s.services.backendConnector.backend&&s.isLanguageChangingTo&&!o(s.isLanguageChangingTo,e))return!1}}),te=e=>typeof e=="string",ra=e=>typeof e=="object"&&e!==null,sa=d.createContext();class aa{constructor(){this.usedNamespaces={}}addUsedNamespaces(t){t.forEach(r=>{this.usedNamespaces[r]||(this.usedNamespaces[r]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}const oa=(e,t)=>{const r=d.useRef();return d.useEffect(()=>{r.current=e},[e,t]),r.current},Bt=(e,t,r,s)=>e.getFixedT(t,r,s),na=(e,t,r,s)=>d.useCallback(Bt(e,t,r,s),[e,t,r,s]),qt=(e,t={})=>{const{i18n:r}=t,{i18n:s,defaultNS:o}=d.useContext(sa)||{},n=r||s||Ys();if(n&&!n.reportNamespaces&&(n.reportNamespaces=new aa),!n){Xe(n,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const N=(M,R)=>te(R)?R:ra(R)&&te(R.defaultValue)?R.defaultValue:Array.isArray(M)?M[M.length-1]:M,I=[N,{},!1];return I.t=N,I.i18n={},I.ready=!1,I}n.options.react?.wait&&Xe(n,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const i={...Qs(),...n.options.react,...t},{useSuspense:u,keyPrefix:c}=i;let l=o||n.options?.defaultNS;l=te(l)?[l]:l||["translation"],n.reportNamespaces.addUsedNamespaces?.(l);const p=(n.isInitialized||n.initializedStoreOnce)&&l.every(N=>ta(N,n,i)),b=na(n,t.lng||null,i.nsMode==="fallback"?l:l[0],c),w=()=>b,j=()=>Bt(n,t.lng||null,i.nsMode==="fallback"?l:l[0],c),[T,x]=d.useState(w);let h=l.join();t.lng&&(h=`${t.lng}${h}`);const C=oa(h),v=d.useRef(!0);d.useEffect(()=>{const{bindI18n:N,bindI18nStore:I}=i;v.current=!0,!p&&!u&&(t.lng?vt(n,t.lng,l,()=>{v.current&&x(j)}):Ye(n,l,()=>{v.current&&x(j)})),p&&C&&C!==h&&v.current&&x(j);const M=()=>{v.current&&x(j)};return N&&n?.on(N,M),I&&n?.store.on(I,M),()=>{v.current=!1,n&&N?.split(" ").forEach(R=>n.off(R,M)),I&&n&&I.split(" ").forEach(R=>n.store.off(R,M))}},[n,h]),d.useEffect(()=>{v.current&&p&&x(w)},[n,c,p]);const A=[T,n,p];if(A.t=T,A.i18n=n,A.ready=p,p||!p&&!u)return A;throw new Promise(N=>{t.lng?vt(n,t.lng,l,()=>N()):Ye(n,l,()=>N())})},Ht=e=>(e?.client??L).get({url:"/api/abp/application-configuration",...e}),ia=e=>(e?.client??L).post({url:"/api/idjas/application-document/generate-application-document-as-attachment",...e,headers:{"Content-Type":"application/json",...e?.headers}}),la=e=>(e?.client??L).post({url:"/api/idjas/application-document/generate-application-document-with-payload-as-attachment",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ca=e=>(e?.client??L).post({url:"/api/idjas/approval/submit",...e,headers:{"Content-Type":"application/json",...e?.headers}}),da=e=>(e?.client??L).post({url:"/api/approval-stages/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ua=e=>(e?.client??L).post({url:"/api/idjas/approval-template",...e,headers:{"Content-Type":"application/json",...e?.headers}}),pa=e=>(e.client??L).put({url:"/api/idjas/approval-template/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),ma=e=>(e?.client??L).post({url:"/api/idjas/approval-template/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),fa=e=>(e?.client??L).post({...Zs,url:"/api/idjas/document/templates/upload",...e,headers:{"Content-Type":null,...e?.headers}}),ha=e=>(e?.client??L).post({url:"/api/idjas/document/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ga=e=>(e?.client??L).post({url:"/api/Ekb/vessel-header",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ba=e=>(e?.client??L).post({url:"/api/Ekb/jetty-filter",...e,headers:{"Content-Type":"application/json",...e?.headers}}),xa=e=>(e?.client??L).post({url:"/api/IdentityServer/user-query",...e,headers:{"Content-Type":"application/json",...e?.headers}}),va=e=>(e?.client??L).post({url:"/api/idjas/jetty-request",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ya=e=>(e.client??L).get({url:"/api/idjas/jetty-request/{id}",...e}),wa=e=>(e.client??L).put({url:"/api/idjas/jetty-request/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),ka=e=>(e?.client??L).post({url:"/api/idjas/jetty-request/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),ja=e=>(e.client??L).delete({url:"/api/idjas/jetty-request-item/{id}",...e}),Ta=e=>(e?.client??L).post({url:"/api/idjas/jetty-request-item/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}}),Ll=Object.freeze(Object.defineProperty({__proto__:null,deleteApiIdjasJettyRequestItemById:ja,getApiAbpApplicationConfiguration:Ht,getApiIdjasJettyRequestById:ya,postApiApprovalStagesFilterList:da,postApiEkbJettyFilter:ba,postApiEkbVesselHeader:ga,postApiIdentityServerUserQuery:xa,postApiIdjasApplicationDocumentGenerateApplicationDocumentAsAttachment:ia,postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment:la,postApiIdjasApprovalSubmit:ca,postApiIdjasApprovalTemplate:ua,postApiIdjasApprovalTemplateFilterList:ma,postApiIdjasDocumentFilterList:ha,postApiIdjasDocumentTemplatesUpload:fa,postApiIdjasJettyRequest:va,postApiIdjasJettyRequestFilterList:ka,postApiIdjasJettyRequestItemFilterList:Ta,putApiIdjasApprovalTemplateById:pa,putApiIdjasJettyRequestById:wa},Symbol.toStringTag,{value:"Module"}));function Wt(e){var t,r,s="";if(typeof e=="string"||typeof e=="number")s+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=Wt(e[t]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}function ct(){for(var e,t,r=0,s="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=Wt(e))&&(s&&(s+=" "),s+=t);return s}const dt="-",Ca=e=>{const t=Na(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:i=>{const u=i.split(dt);return u[0]===""&&u.length!==1&&u.shift(),Kt(u,t)||Ea(i)},getConflictingClassGroupIds:(i,u)=>{const c=r[i]||[];return u&&s[i]?[...c,...s[i]]:c}}},Kt=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],s=t.nextPart.get(r),o=s?Kt(e.slice(1),s):void 0;if(o)return o;if(t.validators.length===0)return;const n=e.join(dt);return t.validators.find(({validator:i})=>i(n))?.classGroupId},yt=/^\[(.+)\]$/,Ea=e=>{if(yt.test(e)){const t=yt.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Na=e=>{const{theme:t,classGroups:r}=e,s={nextPart:new Map,validators:[]};for(const o in r)Qe(r[o],s,o,t);return s},Qe=(e,t,r,s)=>{e.forEach(o=>{if(typeof o=="string"){const n=o===""?t:wt(t,o);n.classGroupId=r;return}if(typeof o=="function"){if(Sa(o)){Qe(o(s),t,r,s);return}t.validators.push({validator:o,classGroupId:r});return}Object.entries(o).forEach(([n,i])=>{Qe(i,wt(t,n),r,s)})})},wt=(e,t)=>{let r=e;return t.split(dt).forEach(s=>{r.nextPart.has(s)||r.nextPart.set(s,{nextPart:new Map,validators:[]}),r=r.nextPart.get(s)}),r},Sa=e=>e.isThemeGetter,Aa=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,s=new Map;const o=(n,i)=>{r.set(n,i),t++,t>e&&(t=0,s=r,r=new Map)};return{get(n){let i=r.get(n);if(i!==void 0)return i;if((i=s.get(n))!==void 0)return o(n,i),i},set(n,i){r.has(n)?r.set(n,i):o(n,i)}}},Ze="!",et=":",Ia=et.length,Ma=e=>{const{prefix:t,experimentalParseClassName:r}=e;let s=o=>{const n=[];let i=0,u=0,c=0,l;for(let T=0;T<o.length;T++){let x=o[T];if(i===0&&u===0){if(x===et){n.push(o.slice(c,T)),c=T+Ia;continue}if(x==="/"){l=T;continue}}x==="["?i++:x==="]"?i--:x==="("?u++:x===")"&&u--}const p=n.length===0?o:o.substring(c),b=Ra(p),w=b!==p,j=l&&l>c?l-c:void 0;return{modifiers:n,hasImportantModifier:w,baseClassName:b,maybePostfixModifierPosition:j}};if(t){const o=t+et,n=s;s=i=>i.startsWith(o)?n(i.substring(o.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const o=s;s=n=>r({className:n,parseClassName:o})}return s},Ra=e=>e.endsWith(Ze)?e.substring(0,e.length-1):e.startsWith(Ze)?e.substring(1):e,Pa=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(s=>[s,!0]));return s=>{if(s.length<=1)return s;const o=[];let n=[];return s.forEach(i=>{i[0]==="["||t[i]?(o.push(...n.sort(),i),n=[]):n.push(i)}),o.push(...n.sort()),o}},_a=e=>({cache:Aa(e.cacheSize),parseClassName:Ma(e),sortModifiers:Pa(e),...Ca(e)}),La=/\s+/,Da=(e,t)=>{const{parseClassName:r,getClassGroupId:s,getConflictingClassGroupIds:o,sortModifiers:n}=t,i=[],u=e.trim().split(La);let c="";for(let l=u.length-1;l>=0;l-=1){const p=u[l],{isExternal:b,modifiers:w,hasImportantModifier:j,baseClassName:T,maybePostfixModifierPosition:x}=r(p);if(b){c=p+(c.length>0?" "+c:c);continue}let h=!!x,C=s(h?T.substring(0,x):T);if(!C){if(!h){c=p+(c.length>0?" "+c:c);continue}if(C=s(T),!C){c=p+(c.length>0?" "+c:c);continue}h=!1}const v=n(w).join(":"),A=j?v+Ze:v,N=A+C;if(i.includes(N))continue;i.push(N);const I=o(C,h);for(let M=0;M<I.length;++M){const R=I[M];i.push(A+R)}c=p+(c.length>0?" "+c:c)}return c};function Oa(){let e=0,t,r,s="";for(;e<arguments.length;)(t=arguments[e++])&&(r=Jt(t))&&(s&&(s+=" "),s+=r);return s}const Jt=e=>{if(typeof e=="string")return e;let t,r="";for(let s=0;s<e.length;s++)e[s]&&(t=Jt(e[s]))&&(r&&(r+=" "),r+=t);return r};function za(e,...t){let r,s,o,n=i;function i(c){const l=t.reduce((p,b)=>b(p),e());return r=_a(l),s=r.cache.get,o=r.cache.set,n=u,u(c)}function u(c){const l=s(c);if(l)return l;const p=Da(c,r);return o(c,p),p}return function(){return n(Oa.apply(null,arguments))}}const _=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},Xt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Yt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,$a=/^\d+\/\d+$/,Va=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Fa=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ga=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ua=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Ba=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,re=e=>$a.test(e),E=e=>!!e&&!Number.isNaN(Number(e)),K=e=>!!e&&Number.isInteger(Number(e)),Fe=e=>e.endsWith("%")&&E(e.slice(0,-1)),W=e=>Va.test(e),qa=()=>!0,Ha=e=>Fa.test(e)&&!Ga.test(e),Qt=()=>!1,Wa=e=>Ua.test(e),Ka=e=>Ba.test(e),Ja=e=>!m(e)&&!f(e),Xa=e=>oe(e,tr,Qt),m=e=>Xt.test(e),Z=e=>oe(e,rr,Ha),Ge=e=>oe(e,to,E),kt=e=>oe(e,Zt,Qt),Ya=e=>oe(e,er,Ka),ke=e=>oe(e,sr,Wa),f=e=>Yt.test(e),le=e=>ne(e,rr),Qa=e=>ne(e,ro),jt=e=>ne(e,Zt),Za=e=>ne(e,tr),eo=e=>ne(e,er),je=e=>ne(e,sr,!0),oe=(e,t,r)=>{const s=Xt.exec(e);return s?s[1]?t(s[1]):r(s[2]):!1},ne=(e,t,r=!1)=>{const s=Yt.exec(e);return s?s[1]?t(s[1]):r:!1},Zt=e=>e==="position"||e==="percentage",er=e=>e==="image"||e==="url",tr=e=>e==="length"||e==="size"||e==="bg-size",rr=e=>e==="length",to=e=>e==="number",ro=e=>e==="family-name",sr=e=>e==="shadow",so=()=>{const e=_("color"),t=_("font"),r=_("text"),s=_("font-weight"),o=_("tracking"),n=_("leading"),i=_("breakpoint"),u=_("container"),c=_("spacing"),l=_("radius"),p=_("shadow"),b=_("inset-shadow"),w=_("text-shadow"),j=_("drop-shadow"),T=_("blur"),x=_("perspective"),h=_("aspect"),C=_("ease"),v=_("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],N=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],I=()=>[...N(),f,m],M=()=>["auto","hidden","clip","visible","scroll"],R=()=>["auto","contain","none"],k=()=>[f,m,c],G=()=>[re,"full","auto",...k()],X=()=>[K,"none","subgrid",f,m],B=()=>["auto",{span:["full",K,f,m]},K,f,m],H=()=>[K,"auto",f,m],ie=()=>["auto","min","max","fr",f,m],S=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],z=()=>["start","end","center","stretch","center-safe","end-safe"],D=()=>["auto",...k()],U=()=>[re,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...k()],y=()=>[e,f,m],Y=()=>[...N(),jt,kt,{position:[f,m]}],ge=()=>["no-repeat",{repeat:["","x","y","space","round"]}],be=()=>["auto","cover","contain",Za,Xa,{size:[f,m]}],Q=()=>[Fe,le,Z],P=()=>["","none","full",l,f,m],$=()=>["",E,le,Z],xe=()=>["solid","dashed","dotted","double"],ht=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],O=()=>[E,Fe,jt,kt],gt=()=>["","none",T,f,m],ve=()=>["none",E,f,m],ye=()=>["none",E,f,m],$e=()=>[E,f,m],we=()=>[re,"full",...k()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[W],breakpoint:[W],color:[qa],container:[W],"drop-shadow":[W],ease:["in","out","in-out"],font:[Ja],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[W],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[W],shadow:[W],spacing:["px",E],text:[W],"text-shadow":[W],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",re,m,f,h]}],container:["container"],columns:[{columns:[E,m,f,u]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:I()}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:G()}],"inset-x":[{"inset-x":G()}],"inset-y":[{"inset-y":G()}],start:[{start:G()}],end:[{end:G()}],top:[{top:G()}],right:[{right:G()}],bottom:[{bottom:G()}],left:[{left:G()}],visibility:["visible","invisible","collapse"],z:[{z:[K,"auto",f,m]}],basis:[{basis:[re,"full","auto",u,...k()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[E,re,"auto","initial","none",m]}],grow:[{grow:["",E,f,m]}],shrink:[{shrink:["",E,f,m]}],order:[{order:[K,"first","last","none",f,m]}],"grid-cols":[{"grid-cols":X()}],"col-start-end":[{col:B()}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":X()}],"row-start-end":[{row:B()}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ie()}],"auto-rows":[{"auto-rows":ie()}],gap:[{gap:k()}],"gap-x":[{"gap-x":k()}],"gap-y":[{"gap-y":k()}],"justify-content":[{justify:[...S(),"normal"]}],"justify-items":[{"justify-items":[...z(),"normal"]}],"justify-self":[{"justify-self":["auto",...z()]}],"align-content":[{content:["normal",...S()]}],"align-items":[{items:[...z(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...z(),{baseline:["","last"]}]}],"place-content":[{"place-content":S()}],"place-items":[{"place-items":[...z(),"baseline"]}],"place-self":[{"place-self":["auto",...z()]}],p:[{p:k()}],px:[{px:k()}],py:[{py:k()}],ps:[{ps:k()}],pe:[{pe:k()}],pt:[{pt:k()}],pr:[{pr:k()}],pb:[{pb:k()}],pl:[{pl:k()}],m:[{m:D()}],mx:[{mx:D()}],my:[{my:D()}],ms:[{ms:D()}],me:[{me:D()}],mt:[{mt:D()}],mr:[{mr:D()}],mb:[{mb:D()}],ml:[{ml:D()}],"space-x":[{"space-x":k()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":k()}],"space-y-reverse":["space-y-reverse"],size:[{size:U()}],w:[{w:[u,"screen",...U()]}],"min-w":[{"min-w":[u,"screen","none",...U()]}],"max-w":[{"max-w":[u,"screen","none","prose",{screen:[i]},...U()]}],h:[{h:["screen","lh",...U()]}],"min-h":[{"min-h":["screen","lh","none",...U()]}],"max-h":[{"max-h":["screen","lh",...U()]}],"font-size":[{text:["base",r,le,Z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[s,f,Ge]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Fe,m]}],"font-family":[{font:[Qa,m,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,f,m]}],"line-clamp":[{"line-clamp":[E,"none",f,Ge]}],leading:[{leading:[n,...k()]}],"list-image":[{"list-image":["none",f,m]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",f,m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:y()}],"text-color":[{text:y()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...xe(),"wavy"]}],"text-decoration-thickness":[{decoration:[E,"from-font","auto",f,Z]}],"text-decoration-color":[{decoration:y()}],"underline-offset":[{"underline-offset":[E,"auto",f,m]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:k()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",f,m]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",f,m]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:Y()}],"bg-repeat":[{bg:ge()}],"bg-size":[{bg:be()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},K,f,m],radial:["",f,m],conic:[K,f,m]},eo,Ya]}],"bg-color":[{bg:y()}],"gradient-from-pos":[{from:Q()}],"gradient-via-pos":[{via:Q()}],"gradient-to-pos":[{to:Q()}],"gradient-from":[{from:y()}],"gradient-via":[{via:y()}],"gradient-to":[{to:y()}],rounded:[{rounded:P()}],"rounded-s":[{"rounded-s":P()}],"rounded-e":[{"rounded-e":P()}],"rounded-t":[{"rounded-t":P()}],"rounded-r":[{"rounded-r":P()}],"rounded-b":[{"rounded-b":P()}],"rounded-l":[{"rounded-l":P()}],"rounded-ss":[{"rounded-ss":P()}],"rounded-se":[{"rounded-se":P()}],"rounded-ee":[{"rounded-ee":P()}],"rounded-es":[{"rounded-es":P()}],"rounded-tl":[{"rounded-tl":P()}],"rounded-tr":[{"rounded-tr":P()}],"rounded-br":[{"rounded-br":P()}],"rounded-bl":[{"rounded-bl":P()}],"border-w":[{border:$()}],"border-w-x":[{"border-x":$()}],"border-w-y":[{"border-y":$()}],"border-w-s":[{"border-s":$()}],"border-w-e":[{"border-e":$()}],"border-w-t":[{"border-t":$()}],"border-w-r":[{"border-r":$()}],"border-w-b":[{"border-b":$()}],"border-w-l":[{"border-l":$()}],"divide-x":[{"divide-x":$()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":$()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...xe(),"hidden","none"]}],"divide-style":[{divide:[...xe(),"hidden","none"]}],"border-color":[{border:y()}],"border-color-x":[{"border-x":y()}],"border-color-y":[{"border-y":y()}],"border-color-s":[{"border-s":y()}],"border-color-e":[{"border-e":y()}],"border-color-t":[{"border-t":y()}],"border-color-r":[{"border-r":y()}],"border-color-b":[{"border-b":y()}],"border-color-l":[{"border-l":y()}],"divide-color":[{divide:y()}],"outline-style":[{outline:[...xe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[E,f,m]}],"outline-w":[{outline:["",E,le,Z]}],"outline-color":[{outline:y()}],shadow:[{shadow:["","none",p,je,ke]}],"shadow-color":[{shadow:y()}],"inset-shadow":[{"inset-shadow":["none",b,je,ke]}],"inset-shadow-color":[{"inset-shadow":y()}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:y()}],"ring-offset-w":[{"ring-offset":[E,Z]}],"ring-offset-color":[{"ring-offset":y()}],"inset-ring-w":[{"inset-ring":$()}],"inset-ring-color":[{"inset-ring":y()}],"text-shadow":[{"text-shadow":["none",w,je,ke]}],"text-shadow-color":[{"text-shadow":y()}],opacity:[{opacity:[E,f,m]}],"mix-blend":[{"mix-blend":[...ht(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ht()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[E]}],"mask-image-linear-from-pos":[{"mask-linear-from":O()}],"mask-image-linear-to-pos":[{"mask-linear-to":O()}],"mask-image-linear-from-color":[{"mask-linear-from":y()}],"mask-image-linear-to-color":[{"mask-linear-to":y()}],"mask-image-t-from-pos":[{"mask-t-from":O()}],"mask-image-t-to-pos":[{"mask-t-to":O()}],"mask-image-t-from-color":[{"mask-t-from":y()}],"mask-image-t-to-color":[{"mask-t-to":y()}],"mask-image-r-from-pos":[{"mask-r-from":O()}],"mask-image-r-to-pos":[{"mask-r-to":O()}],"mask-image-r-from-color":[{"mask-r-from":y()}],"mask-image-r-to-color":[{"mask-r-to":y()}],"mask-image-b-from-pos":[{"mask-b-from":O()}],"mask-image-b-to-pos":[{"mask-b-to":O()}],"mask-image-b-from-color":[{"mask-b-from":y()}],"mask-image-b-to-color":[{"mask-b-to":y()}],"mask-image-l-from-pos":[{"mask-l-from":O()}],"mask-image-l-to-pos":[{"mask-l-to":O()}],"mask-image-l-from-color":[{"mask-l-from":y()}],"mask-image-l-to-color":[{"mask-l-to":y()}],"mask-image-x-from-pos":[{"mask-x-from":O()}],"mask-image-x-to-pos":[{"mask-x-to":O()}],"mask-image-x-from-color":[{"mask-x-from":y()}],"mask-image-x-to-color":[{"mask-x-to":y()}],"mask-image-y-from-pos":[{"mask-y-from":O()}],"mask-image-y-to-pos":[{"mask-y-to":O()}],"mask-image-y-from-color":[{"mask-y-from":y()}],"mask-image-y-to-color":[{"mask-y-to":y()}],"mask-image-radial":[{"mask-radial":[f,m]}],"mask-image-radial-from-pos":[{"mask-radial-from":O()}],"mask-image-radial-to-pos":[{"mask-radial-to":O()}],"mask-image-radial-from-color":[{"mask-radial-from":y()}],"mask-image-radial-to-color":[{"mask-radial-to":y()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":N()}],"mask-image-conic-pos":[{"mask-conic":[E]}],"mask-image-conic-from-pos":[{"mask-conic-from":O()}],"mask-image-conic-to-pos":[{"mask-conic-to":O()}],"mask-image-conic-from-color":[{"mask-conic-from":y()}],"mask-image-conic-to-color":[{"mask-conic-to":y()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:Y()}],"mask-repeat":[{mask:ge()}],"mask-size":[{mask:be()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",f,m]}],filter:[{filter:["","none",f,m]}],blur:[{blur:gt()}],brightness:[{brightness:[E,f,m]}],contrast:[{contrast:[E,f,m]}],"drop-shadow":[{"drop-shadow":["","none",j,je,ke]}],"drop-shadow-color":[{"drop-shadow":y()}],grayscale:[{grayscale:["",E,f,m]}],"hue-rotate":[{"hue-rotate":[E,f,m]}],invert:[{invert:["",E,f,m]}],saturate:[{saturate:[E,f,m]}],sepia:[{sepia:["",E,f,m]}],"backdrop-filter":[{"backdrop-filter":["","none",f,m]}],"backdrop-blur":[{"backdrop-blur":gt()}],"backdrop-brightness":[{"backdrop-brightness":[E,f,m]}],"backdrop-contrast":[{"backdrop-contrast":[E,f,m]}],"backdrop-grayscale":[{"backdrop-grayscale":["",E,f,m]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[E,f,m]}],"backdrop-invert":[{"backdrop-invert":["",E,f,m]}],"backdrop-opacity":[{"backdrop-opacity":[E,f,m]}],"backdrop-saturate":[{"backdrop-saturate":[E,f,m]}],"backdrop-sepia":[{"backdrop-sepia":["",E,f,m]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":k()}],"border-spacing-x":[{"border-spacing-x":k()}],"border-spacing-y":[{"border-spacing-y":k()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",f,m]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[E,"initial",f,m]}],ease:[{ease:["linear","initial",C,f,m]}],delay:[{delay:[E,f,m]}],animate:[{animate:["none",v,f,m]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[x,f,m]}],"perspective-origin":[{"perspective-origin":I()}],rotate:[{rotate:ve()}],"rotate-x":[{"rotate-x":ve()}],"rotate-y":[{"rotate-y":ve()}],"rotate-z":[{"rotate-z":ve()}],scale:[{scale:ye()}],"scale-x":[{"scale-x":ye()}],"scale-y":[{"scale-y":ye()}],"scale-z":[{"scale-z":ye()}],"scale-3d":["scale-3d"],skew:[{skew:$e()}],"skew-x":[{"skew-x":$e()}],"skew-y":[{"skew-y":$e()}],transform:[{transform:[f,m,"","none","gpu","cpu"]}],"transform-origin":[{origin:I()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:we()}],"translate-x":[{"translate-x":we()}],"translate-y":[{"translate-y":we()}],"translate-z":[{"translate-z":we()}],"translate-none":["translate-none"],accent:[{accent:y()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:y()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",f,m]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":k()}],"scroll-mx":[{"scroll-mx":k()}],"scroll-my":[{"scroll-my":k()}],"scroll-ms":[{"scroll-ms":k()}],"scroll-me":[{"scroll-me":k()}],"scroll-mt":[{"scroll-mt":k()}],"scroll-mr":[{"scroll-mr":k()}],"scroll-mb":[{"scroll-mb":k()}],"scroll-ml":[{"scroll-ml":k()}],"scroll-p":[{"scroll-p":k()}],"scroll-px":[{"scroll-px":k()}],"scroll-py":[{"scroll-py":k()}],"scroll-ps":[{"scroll-ps":k()}],"scroll-pe":[{"scroll-pe":k()}],"scroll-pt":[{"scroll-pt":k()}],"scroll-pr":[{"scroll-pr":k()}],"scroll-pb":[{"scroll-pb":k()}],"scroll-pl":[{"scroll-pl":k()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",f,m]}],fill:[{fill:["none",...y()]}],"stroke-w":[{stroke:[E,le,Z,Ge]}],stroke:[{stroke:["none",...y()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},ar=za(so);function g(...e){return ar(ct(e))}function Dl(...e){return ar(ct(...e))}const Tt=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ct=ct,Le=(e,t)=>r=>{var s;if(t?.variants==null)return Ct(e,r?.class,r?.className);const{variants:o,defaultVariants:n}=t,i=Object.keys(o).map(l=>{const p=r?.[l],b=n?.[l];if(p===null)return null;const w=Tt(p)||Tt(b);return o[l][w]}),u=r&&Object.entries(r).reduce((l,p)=>{let[b,w]=p;return w===void 0||(l[b]=w),l},{}),c=t==null||(s=t.compoundVariants)===null||s===void 0?void 0:s.reduce((l,p)=>{let{class:b,className:w,...j}=p;return Object.entries(j).every(T=>{let[x,h]=T;return Array.isArray(h)?h.includes({...n,...u}[x]):{...n,...u}[x]===h})?[...l,b,w]:l},[]);return Ct(e,i,c,r?.class,r?.className)};var ao="Separator",Et="horizontal",oo=["horizontal","vertical"],or=d.forwardRef((e,t)=>{const{decorative:r,orientation:s=Et,...o}=e,n=no(s)?s:Et,u=r?{role:"none"}:{"aria-orientation":n==="vertical"?n:void 0,role:"separator"};return a.jsx(J.div,{"data-orientation":n,...u,...o,ref:t})});or.displayName=ao;function no(e){return oo.includes(e)}var io=or,nr="ToastProvider",[ut,lo,co]=Zr("Toast"),[ir,Ol]=Ot("Toast",[co]),[uo,De]=ir(nr),lr=e=>{const{__scopeToast:t,label:r="Notification",duration:s=5e3,swipeDirection:o="right",swipeThreshold:n=50,children:i}=e,[u,c]=d.useState(null),[l,p]=d.useState(0),b=d.useRef(!1),w=d.useRef(!1);return r.trim(),a.jsx(ut.Provider,{scope:t,children:a.jsx(uo,{scope:t,label:r,duration:s,swipeDirection:o,swipeThreshold:n,toastCount:l,viewport:u,onViewportChange:c,onToastAdd:d.useCallback(()=>p(j=>j+1),[]),onToastRemove:d.useCallback(()=>p(j=>j-1),[]),isFocusedToastEscapeKeyDownRef:b,isClosePausedRef:w,children:i})})};lr.displayName=nr;var cr="ToastViewport",po=["F8"],tt="toast.viewportPause",rt="toast.viewportResume",dr=d.forwardRef((e,t)=>{const{__scopeToast:r,hotkey:s=po,label:o="Notifications ({hotkey})",...n}=e,i=De(cr,r),u=lo(r),c=d.useRef(null),l=d.useRef(null),p=d.useRef(null),b=d.useRef(null),w=_e(t,b,i.onViewportChange),j=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),T=i.toastCount>0;d.useEffect(()=>{const h=C=>{s.length!==0&&s.every(A=>C[A]||C.code===A)&&b.current?.focus()};return document.addEventListener("keydown",h),()=>document.removeEventListener("keydown",h)},[s]),d.useEffect(()=>{const h=c.current,C=b.current;if(T&&h&&C){const v=()=>{if(!i.isClosePausedRef.current){const M=new CustomEvent(tt);C.dispatchEvent(M),i.isClosePausedRef.current=!0}},A=()=>{if(i.isClosePausedRef.current){const M=new CustomEvent(rt);C.dispatchEvent(M),i.isClosePausedRef.current=!1}},N=M=>{!h.contains(M.relatedTarget)&&A()},I=()=>{h.contains(document.activeElement)||A()};return h.addEventListener("focusin",v),h.addEventListener("focusout",N),h.addEventListener("pointermove",v),h.addEventListener("pointerleave",I),window.addEventListener("blur",v),window.addEventListener("focus",A),()=>{h.removeEventListener("focusin",v),h.removeEventListener("focusout",N),h.removeEventListener("pointermove",v),h.removeEventListener("pointerleave",I),window.removeEventListener("blur",v),window.removeEventListener("focus",A)}}},[T,i.isClosePausedRef]);const x=d.useCallback(({tabbingDirection:h})=>{const v=u().map(A=>{const N=A.ref.current,I=[N,...No(N)];return h==="forwards"?I:I.reverse()});return(h==="forwards"?v.reverse():v).flat()},[u]);return d.useEffect(()=>{const h=b.current;if(h){const C=v=>{const A=v.altKey||v.ctrlKey||v.metaKey;if(v.key==="Tab"&&!A){const I=document.activeElement,M=v.shiftKey;if(v.target===h&&M){l.current?.focus();return}const G=x({tabbingDirection:M?"backwards":"forwards"}),X=G.findIndex(B=>B===I);Ue(G.slice(X+1))?v.preventDefault():M?l.current?.focus():p.current?.focus()}};return h.addEventListener("keydown",C),()=>h.removeEventListener("keydown",C)}},[u,x]),a.jsxs(es,{ref:c,role:"region","aria-label":o.replace("{hotkey}",j),tabIndex:-1,style:{pointerEvents:T?void 0:"none"},children:[T&&a.jsx(st,{ref:l,onFocusFromOutsideViewport:()=>{const h=x({tabbingDirection:"forwards"});Ue(h)}}),a.jsx(ut.Slot,{scope:r,children:a.jsx(J.ol,{tabIndex:-1,...n,ref:w})}),T&&a.jsx(st,{ref:p,onFocusFromOutsideViewport:()=>{const h=x({tabbingDirection:"backwards"});Ue(h)}})]})});dr.displayName=cr;var ur="ToastFocusProxy",st=d.forwardRef((e,t)=>{const{__scopeToast:r,onFocusFromOutsideViewport:s,...o}=e,n=De(ur,r);return a.jsx($t,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{const u=i.relatedTarget;!n.viewport?.contains(u)&&s()}})});st.displayName=ur;var me="Toast",mo="toast.swipeStart",fo="toast.swipeMove",ho="toast.swipeCancel",go="toast.swipeEnd",pr=d.forwardRef((e,t)=>{const{forceMount:r,open:s,defaultOpen:o,onOpenChange:n,...i}=e,[u,c]=zt({prop:s,defaultProp:o??!0,onChange:n,caller:me});return a.jsx(lt,{present:r||u,children:a.jsx(vo,{open:u,...i,ref:t,onClose:()=>c(!1),onPause:Re(e.onPause),onResume:Re(e.onResume),onSwipeStart:V(e.onSwipeStart,l=>{l.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:V(e.onSwipeMove,l=>{const{x:p,y:b}=l.detail.delta;l.currentTarget.setAttribute("data-swipe","move"),l.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${p}px`),l.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${b}px`)}),onSwipeCancel:V(e.onSwipeCancel,l=>{l.currentTarget.setAttribute("data-swipe","cancel"),l.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),l.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),l.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),l.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:V(e.onSwipeEnd,l=>{const{x:p,y:b}=l.detail.delta;l.currentTarget.setAttribute("data-swipe","end"),l.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),l.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),l.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${p}px`),l.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${b}px`),c(!1)})})})});pr.displayName=me;var[bo,xo]=ir(me,{onClose(){}}),vo=d.forwardRef((e,t)=>{const{__scopeToast:r,type:s="foreground",duration:o,open:n,onClose:i,onEscapeKeyDown:u,onPause:c,onResume:l,onSwipeStart:p,onSwipeMove:b,onSwipeCancel:w,onSwipeEnd:j,...T}=e,x=De(me,r),[h,C]=d.useState(null),v=_e(t,S=>C(S)),A=d.useRef(null),N=d.useRef(null),I=o||x.duration,M=d.useRef(0),R=d.useRef(I),k=d.useRef(0),{onToastAdd:G,onToastRemove:X}=x,B=Re(()=>{h?.contains(document.activeElement)&&x.viewport?.focus(),i()}),H=d.useCallback(S=>{!S||S===1/0||(window.clearTimeout(k.current),M.current=new Date().getTime(),k.current=window.setTimeout(B,S))},[B]);d.useEffect(()=>{const S=x.viewport;if(S){const z=()=>{H(R.current),l?.()},D=()=>{const U=new Date().getTime()-M.current;R.current=R.current-U,window.clearTimeout(k.current),c?.()};return S.addEventListener(tt,D),S.addEventListener(rt,z),()=>{S.removeEventListener(tt,D),S.removeEventListener(rt,z)}}},[x.viewport,I,c,l,H]),d.useEffect(()=>{n&&!x.isClosePausedRef.current&&H(I)},[n,I,x.isClosePausedRef,H]),d.useEffect(()=>(G(),()=>X()),[G,X]);const ie=d.useMemo(()=>h?br(h):null,[h]);return x.viewport?a.jsxs(a.Fragment,{children:[ie&&a.jsx(yo,{__scopeToast:r,role:"status","aria-live":s==="foreground"?"assertive":"polite","aria-atomic":!0,children:ie}),a.jsx(bo,{scope:r,onClose:B,children:ts.createPortal(a.jsx(ut.ItemSlot,{scope:r,children:a.jsx(rs,{asChild:!0,onEscapeKeyDown:V(u,()=>{x.isFocusedToastEscapeKeyDownRef.current||B(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:a.jsx(J.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":n?"open":"closed","data-swipe-direction":x.swipeDirection,...T,ref:v,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:V(e.onKeyDown,S=>{S.key==="Escape"&&(u?.(S.nativeEvent),S.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:V(e.onPointerDown,S=>{S.button===0&&(A.current={x:S.clientX,y:S.clientY})}),onPointerMove:V(e.onPointerMove,S=>{if(!A.current)return;const z=S.clientX-A.current.x,D=S.clientY-A.current.y,U=!!N.current,y=["left","right"].includes(x.swipeDirection),Y=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,ge=y?Y(0,z):0,be=y?0:Y(0,D),Q=S.pointerType==="touch"?10:2,P={x:ge,y:be},$={originalEvent:S,delta:P};U?(N.current=P,Te(fo,b,$,{discrete:!1})):Nt(P,x.swipeDirection,Q)?(N.current=P,Te(mo,p,$,{discrete:!1}),S.target.setPointerCapture(S.pointerId)):(Math.abs(z)>Q||Math.abs(D)>Q)&&(A.current=null)}),onPointerUp:V(e.onPointerUp,S=>{const z=N.current,D=S.target;if(D.hasPointerCapture(S.pointerId)&&D.releasePointerCapture(S.pointerId),N.current=null,A.current=null,z){const U=S.currentTarget,y={originalEvent:S,delta:z};Nt(z,x.swipeDirection,x.swipeThreshold)?Te(go,j,y,{discrete:!0}):Te(ho,w,y,{discrete:!0}),U.addEventListener("click",Y=>Y.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),yo=e=>{const{__scopeToast:t,children:r,...s}=e,o=De(me,t),[n,i]=d.useState(!1),[u,c]=d.useState(!1);return Co(()=>i(!0)),d.useEffect(()=>{const l=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(l)},[]),u?null:a.jsx(Vt,{asChild:!0,children:a.jsx($t,{...s,children:n&&a.jsxs(a.Fragment,{children:[o.label," ",r]})})})},wo="ToastTitle",mr=d.forwardRef((e,t)=>{const{__scopeToast:r,...s}=e;return a.jsx(J.div,{...s,ref:t})});mr.displayName=wo;var ko="ToastDescription",fr=d.forwardRef((e,t)=>{const{__scopeToast:r,...s}=e;return a.jsx(J.div,{...s,ref:t})});fr.displayName=ko;var jo="ToastAction",To=d.forwardRef((e,t)=>{const{altText:r,...s}=e;return r.trim()?a.jsx(gr,{altText:r,asChild:!0,children:a.jsx(pt,{...s,ref:t})}):null});To.displayName=jo;var hr="ToastClose",pt=d.forwardRef((e,t)=>{const{__scopeToast:r,...s}=e,o=xo(hr,r);return a.jsx(gr,{asChild:!0,children:a.jsx(J.button,{type:"button",...s,ref:t,onClick:V(e.onClick,o.onClose)})})});pt.displayName=hr;var gr=d.forwardRef((e,t)=>{const{__scopeToast:r,altText:s,...o}=e;return a.jsx(J.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":s||void 0,...o,ref:t})});function br(e){const t=[];return Array.from(e.childNodes).forEach(s=>{if(s.nodeType===s.TEXT_NODE&&s.textContent&&t.push(s.textContent),Eo(s)){const o=s.ariaHidden||s.hidden||s.style.display==="none",n=s.dataset.radixToastAnnounceExclude==="";if(!o)if(n){const i=s.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...br(s))}}),t}function Te(e,t,r,{discrete:s}){const o=r.originalEvent.currentTarget,n=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&o.addEventListener(e,t,{once:!0}),s?ss(o,n):o.dispatchEvent(n)}var Nt=(e,t,r=0)=>{const s=Math.abs(e.x),o=Math.abs(e.y),n=s>o;return t==="left"||t==="right"?n&&s>r:!n&&o>r};function Co(e=()=>{}){const t=Re(e);as(()=>{let r=0,s=0;return r=window.requestAnimationFrame(()=>s=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(s)}},[t])}function Eo(e){return e.nodeType===e.ELEMENT_NODE}function No(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:s=>{const o=s.tagName==="INPUT"&&s.type==="hidden";return s.disabled||s.hidden||o?NodeFilter.FILTER_SKIP:s.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Ue(e){const t=document.activeElement;return e.some(r=>r===t?!0:(r.focus(),document.activeElement!==t))}var So=lr,Ao=dr,Io=pr,Mo=mr,Ro=fr,Po=pt,[Oe,zl]=Ot("Tooltip",[Ft]),ze=Ft(),xr="TooltipProvider",_o=700,at="tooltip.open",[Lo,mt]=Oe(xr),vr=e=>{const{__scopeTooltip:t,delayDuration:r=_o,skipDelayDuration:s=300,disableHoverableContent:o=!1,children:n}=e,i=d.useRef(!0),u=d.useRef(!1),c=d.useRef(0);return d.useEffect(()=>{const l=c.current;return()=>window.clearTimeout(l)},[]),a.jsx(Lo,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:d.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:d.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,s)},[s]),isPointerInTransitRef:u,onPointerInTransitChange:d.useCallback(l=>{u.current=l},[]),disableHoverableContent:o,children:n})};vr.displayName=xr;var ue="Tooltip",[Do,fe]=Oe(ue),yr=e=>{const{__scopeTooltip:t,children:r,open:s,defaultOpen:o,onOpenChange:n,disableHoverableContent:i,delayDuration:u}=e,c=mt(ue,e.__scopeTooltip),l=ze(t),[p,b]=d.useState(null),w=os(),j=d.useRef(0),T=i??c.disableHoverableContent,x=u??c.delayDuration,h=d.useRef(!1),[C,v]=zt({prop:s,defaultProp:o??!1,onChange:R=>{R?(c.onOpen(),document.dispatchEvent(new CustomEvent(at))):c.onClose(),n?.(R)},caller:ue}),A=d.useMemo(()=>C?h.current?"delayed-open":"instant-open":"closed",[C]),N=d.useCallback(()=>{window.clearTimeout(j.current),j.current=0,h.current=!1,v(!0)},[v]),I=d.useCallback(()=>{window.clearTimeout(j.current),j.current=0,v(!1)},[v]),M=d.useCallback(()=>{window.clearTimeout(j.current),j.current=window.setTimeout(()=>{h.current=!0,v(!0),j.current=0},x)},[x,v]);return d.useEffect(()=>()=>{j.current&&(window.clearTimeout(j.current),j.current=0)},[]),a.jsx(ns,{...l,children:a.jsx(Do,{scope:t,contentId:w,open:C,stateAttribute:A,trigger:p,onTriggerChange:b,onTriggerEnter:d.useCallback(()=>{c.isOpenDelayedRef.current?M():N()},[c.isOpenDelayedRef,M,N]),onTriggerLeave:d.useCallback(()=>{T?I():(window.clearTimeout(j.current),j.current=0)},[I,T]),onOpen:N,onClose:I,disableHoverableContent:T,children:r})})};yr.displayName=ue;var ot="TooltipTrigger",wr=d.forwardRef((e,t)=>{const{__scopeTooltip:r,...s}=e,o=fe(ot,r),n=mt(ot,r),i=ze(r),u=d.useRef(null),c=_e(t,u,o.onTriggerChange),l=d.useRef(!1),p=d.useRef(!1),b=d.useCallback(()=>l.current=!1,[]);return d.useEffect(()=>()=>document.removeEventListener("pointerup",b),[b]),a.jsx(is,{asChild:!0,...i,children:a.jsx(J.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...s,ref:c,onPointerMove:V(e.onPointerMove,w=>{w.pointerType!=="touch"&&!p.current&&!n.isPointerInTransitRef.current&&(o.onTriggerEnter(),p.current=!0)}),onPointerLeave:V(e.onPointerLeave,()=>{o.onTriggerLeave(),p.current=!1}),onPointerDown:V(e.onPointerDown,()=>{o.open&&o.onClose(),l.current=!0,document.addEventListener("pointerup",b,{once:!0})}),onFocus:V(e.onFocus,()=>{l.current||o.onOpen()}),onBlur:V(e.onBlur,o.onClose),onClick:V(e.onClick,o.onClose)})})});wr.displayName=ot;var ft="TooltipPortal",[Oo,zo]=Oe(ft,{forceMount:void 0}),kr=e=>{const{__scopeTooltip:t,forceMount:r,children:s,container:o}=e,n=fe(ft,t);return a.jsx(Oo,{scope:t,forceMount:r,children:a.jsx(lt,{present:r||n.open,children:a.jsx(Vt,{asChild:!0,container:o,children:s})})})};kr.displayName=ft;var se="TooltipContent",jr=d.forwardRef((e,t)=>{const r=zo(se,e.__scopeTooltip),{forceMount:s=r.forceMount,side:o="top",...n}=e,i=fe(se,e.__scopeTooltip);return a.jsx(lt,{present:s||i.open,children:i.disableHoverableContent?a.jsx(Tr,{side:o,...n,ref:t}):a.jsx($o,{side:o,...n,ref:t})})}),$o=d.forwardRef((e,t)=>{const r=fe(se,e.__scopeTooltip),s=mt(se,e.__scopeTooltip),o=d.useRef(null),n=_e(t,o),[i,u]=d.useState(null),{trigger:c,onClose:l}=r,p=o.current,{onPointerInTransitChange:b}=s,w=d.useCallback(()=>{u(null),b(!1)},[b]),j=d.useCallback((T,x)=>{const h=T.currentTarget,C={x:T.clientX,y:T.clientY},v=Uo(C,h.getBoundingClientRect()),A=Bo(C,v),N=qo(x.getBoundingClientRect()),I=Wo([...A,...N]);u(I),b(!0)},[b]);return d.useEffect(()=>()=>w(),[w]),d.useEffect(()=>{if(c&&p){const T=h=>j(h,p),x=h=>j(h,c);return c.addEventListener("pointerleave",T),p.addEventListener("pointerleave",x),()=>{c.removeEventListener("pointerleave",T),p.removeEventListener("pointerleave",x)}}},[c,p,j,w]),d.useEffect(()=>{if(i){const T=x=>{const h=x.target,C={x:x.clientX,y:x.clientY},v=c?.contains(h)||p?.contains(h),A=!Ho(C,i);v?w():A&&(w(),l())};return document.addEventListener("pointermove",T),()=>document.removeEventListener("pointermove",T)}},[c,p,i,l,w]),a.jsx(Tr,{...e,ref:n})}),[Vo,Fo]=Oe(ue,{isInside:!1}),Go=us("TooltipContent"),Tr=d.forwardRef((e,t)=>{const{__scopeTooltip:r,children:s,"aria-label":o,onEscapeKeyDown:n,onPointerDownOutside:i,...u}=e,c=fe(se,r),l=ze(r),{onClose:p}=c;return d.useEffect(()=>(document.addEventListener(at,p),()=>document.removeEventListener(at,p)),[p]),d.useEffect(()=>{if(c.trigger){const b=w=>{w.target?.contains(c.trigger)&&p()};return window.addEventListener("scroll",b,{capture:!0}),()=>window.removeEventListener("scroll",b,{capture:!0})}},[c.trigger,p]),a.jsx(cs,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:n,onPointerDownOutside:i,onFocusOutside:b=>b.preventDefault(),onDismiss:p,children:a.jsxs(ds,{"data-state":c.stateAttribute,...l,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[a.jsx(Go,{children:s}),a.jsx(Vo,{scope:r,isInside:!0,children:a.jsx(ps,{id:c.contentId,role:"tooltip",children:o||s})})]})})});jr.displayName=se;var Cr="TooltipArrow",Er=d.forwardRef((e,t)=>{const{__scopeTooltip:r,...s}=e,o=ze(r);return Fo(Cr,r).isInside?null:a.jsx(ls,{...o,...s,ref:t})});Er.displayName=Cr;function Uo(e,t){const r=Math.abs(t.top-e.y),s=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),n=Math.abs(t.left-e.x);switch(Math.min(r,s,o,n)){case n:return"left";case o:return"right";case r:return"top";case s:return"bottom";default:throw new Error("unreachable")}}function Bo(e,t,r=5){const s=[];switch(t){case"top":s.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":s.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":s.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":s.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return s}function qo(e){const{top:t,right:r,bottom:s,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:s},{x:o,y:s}]}function Ho(e,t){const{x:r,y:s}=e;let o=!1;for(let n=0,i=t.length-1;n<t.length;i=n++){const u=t[n],c=t[i],l=u.x,p=u.y,b=c.x,w=c.y;p>s!=w>s&&r<(b-l)*(s-p)/(w-p)+l&&(o=!o)}return o}function Wo(e){const t=e.slice();return t.sort((r,s)=>r.x<s.x?-1:r.x>s.x?1:r.y<s.y?-1:r.y>s.y?1:0),Ko(t)}function Ko(e){if(e.length<=1)return e.slice();const t=[];for(let s=0;s<e.length;s++){const o=e[s];for(;t.length>=2;){const n=t[t.length-1],i=t[t.length-2];if((n.x-i.x)*(o.y-i.y)>=(n.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const r=[];for(let s=e.length-1;s>=0;s--){const o=e[s];for(;r.length>=2;){const n=r[r.length-1],i=r[r.length-2];if((n.x-i.x)*(o.y-i.y)>=(n.y-i.y)*(o.x-i.x))r.pop();else break}r.push(o)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var Jo=vr,Xo=yr,Yo=wr,Qo=kr,Zo=jr,en=Er;const tn=Le("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",primary:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white shadow-xs hover:bg-green-700 focus-visible:ring-green-600/20 dark:focus-visible:ring-green-400/40 dark:bg-green-600/80",warning:"bg-yellow-600 text-white shadow-xs hover:bg-yellow-700 focus-visible:ring-yellow-600/20 dark:focus-visible:ring-yellow-400/40 dark:bg-yellow-600/80",info:"bg-blue-600 text-white shadow-xs hover:bg-blue-700 focus-visible:ring-blue-600/20 dark:focus-visible:ring-blue-400/40 dark:bg-blue-600/80",error:"bg-red-600 text-white shadow-xs hover:bg-red-700 focus-visible:ring-red-600/20 dark:focus-visible:ring-red-400/40 dark:bg-red-600/80"},size:{xs:"h-5 rounded gap-1 px-2 text-xs has-[>svg]:px-1.5",default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ae({className:e,variant:t,size:r,asChild:s=!1,...o}){const n=s?pe:"button";return a.jsx(n,{"data-slot":"button",className:g(tn({variant:t,size:r,className:e})),...o})}/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rn=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),sn=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,r,s)=>s?s.toUpperCase():r.toLowerCase()),St=e=>{const t=sn(e);return t.charAt(0).toUpperCase()+t.slice(1)},Nr=(...e)=>e.filter((t,r,s)=>!!t&&t.trim()!==""&&s.indexOf(t)===r).join(" ").trim(),an=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var on={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nn=d.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:o="",children:n,iconNode:i,...u},c)=>d.createElement("svg",{ref:c,...on,width:t,height:t,stroke:e,strokeWidth:s?Number(r)*24/Number(t):r,className:Nr("lucide",o),...!n&&!an(u)&&{"aria-hidden":"true"},...u},[...i.map(([l,p])=>d.createElement(l,p)),...Array.isArray(n)?n:[n]]));/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=(e,t)=>{const r=d.forwardRef(({className:s,...o},n)=>d.createElement(nn,{ref:n,iconNode:t,className:Nr(`lucide-${rn(St(e))}`,`lucide-${e}`,s),...o}));return r.displayName=St(e),r};/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ln=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Sr=q("check",ln);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Ar=q("chevron-down",cn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dn=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],un=q("chevron-right",dn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pn=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],mn=q("chevron-up",pn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=[["path",{d:"M21.54 15H17a2 2 0 0 0-2 2v4.54",key:"1djwo0"}],["path",{d:"M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17",key:"1tzkfa"}],["path",{d:"M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05",key:"14pb5j"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],hn=q("earth",fn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],bn=q("panel-left",gn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],vn=q("refresh-cw",xn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]],wn=q("shield-alert",yn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kn=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],jn=q("triangle-alert",kn);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tn=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ir=q("x",Tn),Mr=d.createContext({indicatorPosition:"left",indicator:null,indicatorVisibility:!0}),Cn=({indicatorPosition:e="left",indicatorVisibility:t=!0,indicator:r,...s})=>a.jsx(Mr.Provider,{value:{indicatorPosition:e,indicatorVisibility:t,indicator:r},children:a.jsx(ms,{...s})});function Be({...e}){return a.jsx(Cs,{"data-slot":"select-group",...e})}function En({...e}){return a.jsx(gs,{"data-slot":"select-value",...e})}const Nn=Le(`
    flex bg-background w-full items-center justify-between outline-none border border-input shadow-xs shadow-black/5 transition-shadow 
    text-foreground data-placeholder:text-muted-foreground focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] 
    focus-visible:ring-ring/30 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 
    aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20
    [[data-invalid=true]_&]:border-destructive/60 [[data-invalid=true]_&]:ring-destructive/10  dark:[[data-invalid=true]_&]:border-destructive dark:[[data-invalid=true]_&]:ring-destructive/20
  `,{variants:{size:{sm:"h-7 px-2.5 text-xs gap-1 rounded",md:"h-8.5 px-3 text-[0.8125rem] leading-(--text-sm--line-height) gap-1 rounded-md",lg:"h-10 px-4 text-sm gap-1.5 rounded-md"}},defaultVariants:{size:"md"}});function Sn({className:e,children:t,size:r,...s}){return a.jsxs(fs,{"data-slot":"select-trigger",className:g(Nn({size:r}),e),...s,children:[t,a.jsx(hs,{asChild:!0,children:a.jsx(Ar,{className:"h-4 w-4 opacity-60 -me-0.5"})})]})}function An({className:e,...t}){return a.jsx(js,{"data-slot":"select-scroll-up-button",className:g("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(mn,{className:"h-4 w-4"})})}function In({className:e,...t}){return a.jsx(Ts,{"data-slot":"select-scroll-down-button",className:g("flex cursor-default items-center justify-center py-1",e),...t,children:a.jsx(Ar,{className:"h-4 w-4"})})}function Mn({className:e,children:t,position:r="popper",...s}){return a.jsx(bs,{children:a.jsxs(xs,{"data-slot":"select-content",className:g("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border border-border bg-popover shadow-md shadow-black/5 text-secondary-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r==="popper"&&"data-[side=bottom]:translate-y-1.5 data-[side=left]:-translate-x-1.5 data-[side=right]:translate-x-1.5 data-[side=top]:-translate-y-1.5",e),position:r,...s,children:[a.jsx(An,{}),a.jsx(vs,{className:g("p-1.5",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(In,{})]})})}function qe({className:e,...t}){return a.jsx(Es,{"data-slot":"select-label",className:g("py-1.5 ps-8 pe-2 text-xs text-muted-foreground font-medium",e),...t})}function He({className:e,children:t,...r}){const{indicatorPosition:s,indicatorVisibility:o,indicator:n}=d.useContext(Mr);return a.jsxs(ys,{"data-slot":"select-item",className:g("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 text-sm outline-hidden text-foreground hover:bg-accent focus:bg-accent data-disabled:pointer-events-none data-disabled:opacity-50",s==="left"?"ps-8 pe-2":"pe-8 ps-2",e),...r,children:[o&&(n&&d.isValidElement(n)?n:a.jsx("span",{className:g("absolute flex h-3.5 w-3.5 items-center justify-center",s==="left"?"start-2":"end-2"),children:a.jsx(ws,{children:a.jsx(Sr,{className:"h-4 w-4 text-primary"})})})),a.jsx(ks,{children:t})]})}function Rn({className:e,...t}){return a.jsx(Ns,{"data-slot":"select-separator",className:g("-mx-1.5 my-1.5 h-px bg-border",e),...t})}const Pn=1,_n=1e6;let We=0;function Ln(){return We=(We+1)%Number.MAX_SAFE_INTEGER,We.toString()}const Ke=new Map,At=e=>{if(Ke.has(e))return;const t=setTimeout(()=>{Ke.delete(e),ce({type:"REMOVE_TOAST",toastId:e})},_n);Ke.set(e,t)},Rr=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,Pn)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(r=>r.id===t.toast.id?{...r,...t.toast}:r)};case"DISMISS_TOAST":{const{toastId:r}=t;return r?At(r):e.toasts.forEach(s=>{At(s.id)}),{...e,toasts:e.toasts.map(s=>s.id===r||r===void 0?{...s,open:!1}:s)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(r=>r.id!==t.toastId)}}},Ne=[];let Se={toasts:[]};function ce(e){Se=Rr(Se,e),Ne.forEach(t=>{t(Se)})}function Pr({...e}){const t=Ln(),r=o=>ce({type:"UPDATE_TOAST",toast:{...o,id:t}}),s=()=>ce({type:"DISMISS_TOAST",toastId:t});return ce({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||s()}}}),{id:t,dismiss:s,update:r}}function _r(){const[e,t]=d.useState(Se);return d.useEffect(()=>(Ne.push(t),()=>{const r=Ne.indexOf(t);r>-1&&Ne.splice(r,1)}),[e]),{...e,toast:Pr,dismiss:r=>ce({type:"DISMISS_TOAST",toastId:r})}}const $l=Object.freeze(Object.defineProperty({__proto__:null,reducer:Rr,toast:Pr,useToast:_r},Symbol.toStringTag,{value:"Module"}));function Lr({delayDuration:e=0,...t}){return a.jsx(Jo,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Dn({...e}){return a.jsx(Lr,{children:a.jsx(Xo,{"data-slot":"tooltip",...e})})}function On({...e}){return a.jsx(Yo,{"data-slot":"tooltip-trigger",...e})}function zn({className:e,sideOffset:t=0,children:r,...s}){return a.jsx(Qo,{children:a.jsxs(Zo,{"data-slot":"tooltip-content",sideOffset:t,className:g("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...s,children:[r,a.jsx(en,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}function $n({className:e,...t}){return a.jsx("div",{"data-slot":"card",className:g("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function Vn({className:e,...t}){return a.jsx("div",{"data-slot":"card-header",className:g("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function Fn({className:e,...t}){return a.jsx("div",{"data-slot":"card-title",className:g("leading-none font-semibold",e),...t})}function Gn({className:e,...t}){return a.jsx("div",{"data-slot":"card-description",className:g("text-muted-foreground text-sm",e),...t})}function Vl({className:e,...t}){return a.jsx("div",{"data-slot":"card-action",className:g("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t})}function Un({className:e,...t}){return a.jsx("div",{"data-slot":"card-content",className:g("px-6",e),...t})}function Fl({className:e,...t}){return a.jsx("div",{"data-slot":"card-footer",className:g("flex items-center px-6 [.border-t]:pt-6",e),...t})}const{useContext:Bn}=Lt,qn={theme:"system",setTheme:()=>null,resolvedTheme:void 0},Dr=d.createContext(qn);function Hn(){const e=Bn(Dr);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}const{useEffect:It,useState:Mt}=Lt;function Wn({children:e,defaultTheme:t="system",storageKey:r="vite-ui-theme",...s}){const[o,n]=Mt(()=>localStorage.getItem(r)||t),[i,u]=Mt(()=>window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light");It(()=>{const l=window.document.documentElement;if(l.classList.remove("light","dark"),o==="system"){l.classList.add(i);return}l.classList.add(o)},[o,i]),It(()=>{const l=window.matchMedia("(prefers-color-scheme: dark)"),p=()=>{const b=window.document.documentElement;b.classList.remove("light","dark");const w=l.matches?"dark":"light";u(w),o==="system"&&b.classList.add(w)};return l.addEventListener("change",p),()=>l.removeEventListener("change",p)},[o]);const c={theme:o,setTheme:l=>{localStorage.setItem(r,l),n(l)},resolvedTheme:o==="system"?i:o};return a.jsx(Dr.Provider,{...s,value:c,children:e})}class Kn extends d.Component{constructor(r){super(r);Ve(this,"handleRetry",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})});this.state={hasError:!1}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,s){this.setState({error:r,errorInfo:s})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:a.jsxs($n,{className:"w-full max-w-md",children:[a.jsxs(Vn,{className:"text-center",children:[a.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10",children:a.jsx(jn,{className:"h-6 w-6 text-destructive"})}),a.jsx(Fn,{className:"text-xl",children:"Something went wrong"}),a.jsx(Gn,{children:"An unexpected error occurred. Please try again or contact support if the problem persists."})]}),a.jsxs(Un,{className:"space-y-4",children:[!1,a.jsxs("div",{className:"flex flex-col space-y-2",children:[a.jsxs(ae,{onClick:this.handleRetry,className:"w-full",children:[a.jsx(vn,{className:"mr-2 h-4 w-4"}),"Try Again"]}),a.jsx(ae,{variant:"outline",onClick:()=>window.location.reload(),className:"w-full",children:"Reload Page"})]})]})]})}):this.props.children}}const Jn=So;function Xn({className:e,...t}){return a.jsx(Ao,{className:g("fixed top-0 right-0 z-50 flex max-h-screen w-full flex-col-reverse p-4 sm:top-auto sm:bottom-0 sm:flex-col md:max-w-[400px]",e),...t})}const Yn=Le("group pointer-events-auto relative flex w-full items-center justify-between overflow-hidden rounded-md border p-4 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:data-[swipe-direction=left]:slide-out-to-left-full data-[state=closed]:data-[swipe-direction=right]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"border-t-4 border-t-destructive border bg-background text-destructive",success:"border-t-4 border-t-green-500 border bg-background text-green-900 dark:text-green-100",error:"border-t-4 border-t-red-500 border bg-background text-red-900 dark:text-red-100",info:"border-t-4 border-t-blue-500 border bg-background text-blue-900 dark:text-blue-100",warning:"border-t-4 border-t-yellow-500 border bg-background text-yellow-900 dark:text-yellow-100"}},defaultVariants:{variant:"default"}});function Qn({className:e,variant:t,...r}){return a.jsx(Io,{className:g(Yn({variant:t}),e),...r})}function Zn({className:e,asChild:t=!1,...r}){return a.jsx(Po,{className:g(!t&&"group focus-visible:border-ring focus-visible:ring-ring/50 absolute top-3 right-3 flex size-7 items-center justify-center rounded transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:pointer-events-none",e),"toast-close":"",asChild:t,...r,children:t?r.children:a.jsx(Ir,{size:16,className:"opacity-60 transition-opacity group-hover:opacity-100","aria-hidden":"true"})})}function ei({className:e,...t}){return a.jsx(Mo,{className:g("text-sm font-medium",e),...t})}function ti({className:e,...t}){return a.jsx(Ro,{className:g("text-muted-foreground text-sm",e),...t})}function Or(){const{toasts:e}=_r();return a.jsxs(Jn,{swipeDirection:"right",children:[e.map(function({id:t,title:r,description:s,action:o,...n}){return a.jsx(Qn,{...n,children:a.jsxs("div",{className:"flex w-full justify-between gap-2",children:[a.jsxs("div",{className:"flex flex-col gap-3",children:[a.jsxs("div",{className:"space-y-1",children:[r&&a.jsx(ei,{children:r}),s&&a.jsx(ti,{children:s})]}),a.jsx("div",{children:o})]}),a.jsx("div",{children:a.jsx(Zn,{})})]})},t)}),a.jsx(Xn,{asChild:!0,children:a.jsx("div",{style:{position:"fixed",top:"1rem",right:"1rem",left:"auto",bottom:"auto",zIndex:9999,display:"flex",flexDirection:"column",alignItems:"flex-end"}})})]})}/*! js-cookie v3.0.5 | MIT */function Ce(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)e[s]=r[s]}return e}var ri={read:function(e){return e[0]==='"'&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function nt(e,t){function r(o,n,i){if(!(typeof document>"u")){i=Ce({},t,i),typeof i.expires=="number"&&(i.expires=new Date(Date.now()+i.expires*864e5)),i.expires&&(i.expires=i.expires.toUTCString()),o=encodeURIComponent(o).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var u="";for(var c in i)i[c]&&(u+="; "+c,i[c]!==!0&&(u+="="+i[c].split(";")[0]));return document.cookie=o+"="+e.write(n,o)+u}}function s(o){if(!(typeof document>"u"||arguments.length&&!o)){for(var n=document.cookie?document.cookie.split("; "):[],i={},u=0;u<n.length;u++){var c=n[u].split("="),l=c.slice(1).join("=");try{var p=decodeURIComponent(c[0]);if(i[p]=e.read(l,p),o===p)break}catch{}}return o?i[o]:i}}return Object.create({set:r,get:s,remove:function(o,n){r(o,"",Ce({},n,{expires:-1}))},withAttributes:function(o){return nt(this.converter,Ce({},this.attributes,o))},withConverter:function(o){return nt(Ce({},this.converter,o),this.attributes)}},{attributes:{value:Object.freeze(t)},converter:{value:Object.freeze(e)}})}var de=nt(ri,{path:"/"});const Ae="active_theme",si="green-scaled";function ai(e){if(!(typeof window>"u"))try{de.remove(Ae,{path:"/"}),de.set(Ae,e,{path:"/",expires:365,sameSite:"lax"});const t=de.get(Ae)}catch{}}const zr=d.createContext(void 0);function oi({children:e,initialTheme:t}){const[r,s]=d.useState(()=>{if(typeof window<"u"){const o=de.get(Ae);if(o)return o}return t||si});return d.useEffect(()=>{ai(r);const o=document.body;Array.from(o.classList).filter(i=>i.startsWith("theme-")).forEach(i=>o.classList.remove(i)),o.classList.add(`theme-${r}`),r.endsWith("-scaled")&&o.classList.add("theme-scaled")},[r]),a.jsx(zr.Provider,{value:{activeTheme:r,setActiveTheme:s},children:e})}function $r(){const e=d.useContext(zr);if(e===void 0)throw new Error("useThemeConfig must be used within an ActiveThemeProvider");return e}const ni={GetAppConfig:"GetAppConfig",GetDashboardStatistics:"GetDashboardStatistics",GetPendingApprovals:"GetPendingApprovals",GetJettyStatus:"GetJettyStatus"},Vr=()=>Dt({queryKey:[ni.GetAppConfig],queryFn:async()=>{const{data:e}=await Ht();return e},staleTime:60*60*1e3}),Fr=()=>{const{data:e}=Vr();return{can:d.useCallback(r=>!!(e?.auth?.grantedPolicies&&e.auth.grantedPolicies[r]),[e?.auth?.grantedPolicies])}};function ii({message:e="You don't have permission to access this page.",showBackButton:t=!0}){return a.jsx("div",{className:"flex flex-col items-center justify-center min-h-[50vh] p-6",children:a.jsxs("div",{className:"flex flex-col items-center text-center space-y-4 max-w-md",children:[a.jsx(wn,{className:"h-12 w-12 text-gray-400"}),a.jsx("h2",{className:"text-2xl font-semibold tracking-tight",children:"Access Restricted"}),a.jsx("p",{className:"text-gray-500",children:e}),t&&a.jsx(ae,{onClick:()=>window.history.back(),variant:"outline",className:"mt-4",children:"Go Back"})]})})}function li({policy:e,children:t,fallback:r,message:s="You don't have permission to access this page."}){const{can:o}=Fr();return o(e)?a.jsx(a.Fragment,{children:t}):r?a.jsx(a.Fragment,{children:r}):a.jsx(ii,{message:s})}function ci({...e}){return a.jsx(Ss,{"data-slot":"collapsible",...e})}function di({...e}){return a.jsx(As,{"data-slot":"collapsible-trigger",...e})}function ui({...e}){return a.jsx(Is,{"data-slot":"collapsible-content",...e})}const Je=768;function pi(){const[e,t]=d.useState(void 0);return d.useEffect(()=>{const r=window.matchMedia(`(max-width: ${Je-1}px)`),s=()=>{t(window.innerWidth<Je)};return r.addEventListener("change",s),t(window.innerWidth<Je),()=>r.removeEventListener("change",s)},[]),!!e}function mi({className:e,orientation:t="horizontal",decorative:r=!0,...s}){return a.jsx(io,{"data-slot":"separator",decorative:r,orientation:t,className:g("shrink-0 bg-border",t==="horizontal"?"h-px w-full":"h-full w-px",e),...s})}function fi({...e}){return a.jsx(Ms,{"data-slot":"sheet",...e})}function hi({...e}){return a.jsx(Ds,{"data-slot":"sheet-portal",...e})}function gi({className:e,...t}){return a.jsx(Os,{"data-slot":"sheet-overlay",className:g("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function bi({className:e,children:t,side:r="right",...s}){return a.jsxs(hi,{children:[a.jsx(gi,{}),a.jsxs(Rs,{"data-slot":"sheet-content",className:g("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",r==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",r==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",r==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",r==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...s,children:[t,a.jsxs(Ps,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[a.jsx(Ir,{className:"size-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function xi({className:e,...t}){return a.jsx("div",{"data-slot":"sheet-header",className:g("flex flex-col gap-1.5 p-4",e),...t})}function vi({className:e,...t}){return a.jsx(_s,{"data-slot":"sheet-title",className:g("text-foreground font-semibold",e),...t})}function yi({className:e,...t}){return a.jsx(Ls,{"data-slot":"sheet-description",className:g("text-muted-foreground text-sm",e),...t})}const wi="sidebar_state",ki=60*60*24*7,ji="16rem",Ti="18rem",Ci="3rem",Ei="b",Gr=d.createContext(null);function he(){const e=d.useContext(Gr);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Ni({defaultOpen:e=!0,open:t,onOpenChange:r,className:s,style:o,children:n,...i}){const u=pi(),[c,l]=d.useState(!1),[p,b]=d.useState(e),w=t??p,j=d.useCallback(C=>{const v=typeof C=="function"?C(w):C;r?r(v):b(v),document.cookie=`${wi}=${v}; path=/; max-age=${ki}`},[r,w]),T=d.useCallback(()=>u?l(C=>!C):j(C=>!C),[u,j,l]);d.useEffect(()=>{const C=v=>{v.key===Ei&&(v.metaKey||v.ctrlKey)&&(v.preventDefault(),T())};return window.addEventListener("keydown",C),()=>window.removeEventListener("keydown",C)},[T]);const x=w?"expanded":"collapsed",h=d.useMemo(()=>({state:x,open:w,setOpen:j,isMobile:u,openMobile:c,setOpenMobile:l,toggleSidebar:T}),[x,w,j,u,c,l,T]);return a.jsx(Gr.Provider,{value:h,children:a.jsx(Lr,{delayDuration:0,children:a.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":ji,"--sidebar-width-icon":Ci,...o},className:g("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...i,children:n})})})}function Si({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:s,children:o,...n}){const{isMobile:i,state:u,openMobile:c,setOpenMobile:l}=he();return r==="none"?a.jsx("div",{"data-slot":"sidebar",className:g("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",s),...n,children:o}):i?a.jsx(fi,{open:c,onOpenChange:l,...n,children:a.jsxs(bi,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Ti},side:e,children:[a.jsxs(xi,{className:"sr-only",children:[a.jsx(vi,{children:"Sidebar"}),a.jsx(yi,{children:"Displays the mobile sidebar."})]}),a.jsx("div",{className:"flex h-full w-full flex-col",children:o})]})}):a.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":u,"data-collapsible":u==="collapsed"?r:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[a.jsx("div",{"data-slot":"sidebar-gap",className:g("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),a.jsx("div",{"data-slot":"sidebar-container",className:g("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...n,children:a.jsx("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function Ai({className:e,onClick:t,...r}){const{toggleSidebar:s}=he();return a.jsxs(ae,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:g("size-7",e),onClick:o=>{t?.(o),s()},...r,children:[a.jsx(bn,{}),a.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function Ii({className:e,...t}){const{toggleSidebar:r}=he();return a.jsx("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:g("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function Mi({className:e,...t}){return a.jsx("main",{"data-slot":"sidebar-inset",className:g("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t})}function Ri({className:e,...t}){return a.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:g("flex flex-col gap-2 p-2",e),...t})}function Pi({className:e,...t}){return a.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:g("flex flex-col gap-2 p-2",e),...t})}function _i({className:e,...t}){return a.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:g("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function Li({className:e,...t}){return a.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:g("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Di({className:e,asChild:t=!1,...r}){const s=t?pe:"div";return a.jsx(s,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:g("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}function it({className:e,...t}){return a.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:g("flex w-full min-w-0 flex-col gap-1",e),...t})}function Ie({className:e,...t}){return a.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:g("group/menu-item relative",e),...t})}const Oi=Le("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Me({asChild:e=!1,isActive:t=!1,variant:r="default",size:s="default",tooltip:o,className:n,...i}){const u=e?pe:"button",{isMobile:c,state:l}=he(),p=a.jsx(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":s,"data-active":t,className:g(Oi({variant:r,size:s}),n),...i});return o?(typeof o=="string"&&(o={children:o}),a.jsxs(Dn,{children:[a.jsx(On,{asChild:!0,children:p}),a.jsx(zn,{side:"right",align:"center",hidden:l!=="collapsed"||c,...o})]})):p}function zi({className:e,...t}){return a.jsx("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:g("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t})}function $i({className:e,...t}){return a.jsx("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:g("group/menu-sub-item relative",e),...t})}function Vi({asChild:e=!1,size:t="md",isActive:r=!1,className:s,...o}){const n=e?pe:"a";return a.jsx(n,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:g("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",s),...o})}/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Fi={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=(e,t,r,s)=>{const o=d.forwardRef(({color:n="currentColor",size:i=24,stroke:u=2,title:c,className:l,children:p,...b},w)=>d.createElement("svg",{ref:w,...Fi[e],width:i,height:i,className:["tabler-icon",`tabler-icon-${t}`,l].join(" "),strokeWidth:u,stroke:n,...b},[c&&d.createElement("title",{key:"svg-title"},c),...s.map(([j,T])=>d.createElement(j,T)),...Array.isArray(p)?p:[p]]));return o.displayName=`${r}`,o};/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Gi=F("outline","brightness","IconBrightness",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 3l0 18",key:"svg-1"}],["path",{d:"M12 9l4.65 -4.65",key:"svg-2"}],["path",{d:"M12 14.3l7.37 -7.37",key:"svg-3"}],["path",{d:"M12 19.6l8.85 -8.85",key:"svg-4"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ui=F("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Bi=F("outline","clipboard-check","IconClipboardCheck",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 14l2 2l4 -4",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var qi=F("outline","clipboard-list","IconClipboardList",[["path",{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2",key:"svg-0"}],["path",{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-1"}],["path",{d:"M9 12l.01 0",key:"svg-2"}],["path",{d:"M13 12l2 0",key:"svg-3"}],["path",{d:"M9 16l.01 0",key:"svg-4"}],["path",{d:"M13 16l2 0",key:"svg-5"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Hi=F("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Wi=F("outline","dashboard","IconDashboard",[["path",{d:"M12 13m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-0"}],["path",{d:"M13.45 11.55l2.05 -2.05",key:"svg-1"}],["path",{d:"M6.4 20a9 9 0 1 1 11.2 0z",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ki=F("outline","database","IconDatabase",[["path",{d:"M12 6m-8 0a8 3 0 1 0 16 0a8 3 0 1 0 -16 0",key:"svg-0"}],["path",{d:"M4 6v6a8 3 0 0 0 16 0v-6",key:"svg-1"}],["path",{d:"M4 12v6a8 3 0 0 0 16 0v-6",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ji=F("outline","dots-vertical","IconDotsVertical",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Xi=F("outline","file-description","IconFileDescription",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M9 17h6",key:"svg-2"}],["path",{d:"M9 13h6",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Yi=F("outline","inner-shadow-top","IconInnerShadowTop",[["path",{d:"M5.636 5.636a9 9 0 1 0 12.728 12.728a9 9 0 0 0 -12.728 -12.728z",key:"svg-0"}],["path",{d:"M16.243 7.757a6 6 0 0 0 -8.486 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Qi=F("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var Zi=F("outline","notification","IconNotification",[["path",{d:"M10 6h-3a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-3",key:"svg-0"}],["path",{d:"M17 7m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var el=F("outline","sailboat","IconSailboat",[["path",{d:"M2 20a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1",key:"svg-0"}],["path",{d:"M4 18l-1 -3h18l-1 3",key:"svg-1"}],["path",{d:"M11 12h7l-7 -9v9",key:"svg-2"}],["path",{d:"M8 7l-2 5",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var tl=F("outline","ship-off","IconShipOff",[["path",{d:"M2 20a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1",key:"svg-0"}],["path",{d:"M4 18l-1 -5h10m4 0h4l-1.334 2.668",key:"svg-1"}],["path",{d:"M5 13v-6h2m4 0h2l4 6",key:"svg-2"}],["path",{d:"M3 3l18 18",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var rl=F("outline","ship","IconShip",[["path",{d:"M2 20a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1a2.4 2.4 0 0 1 2 -1a2.4 2.4 0 0 1 2 1a2.4 2.4 0 0 0 2 1a2.4 2.4 0 0 0 2 -1",key:"svg-0"}],["path",{d:"M4 18l-1 -5h18l-2 4",key:"svg-1"}],["path",{d:"M5 13v-6h8l4 6",key:"svg-2"}],["path",{d:"M7 7v-4h-1",key:"svg-3"}]]);/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */var sl=F("outline","user-circle","IconUserCircle",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]]);const al={name:"JETTY APPROVAL"},ol=[{title:"menu.dashboard",url:"/",isActive:!1,icon:Wi},{title:"menu.approvalManagement",url:"#",isActive:!1,icon:Bi,permission:"JettyApprovalApp.ApprovalStages",items:[{title:"menu.incomingApproval",url:"/approval",permission:"JettyApprovalApp.ApprovalStages.View"},{title:"menu.history",url:"/approval/history",permission:"JettyApprovalApp.ApprovalStages.View"}]},{title:"menu.exportVessel",url:"/custom-area/export",isActive:!1,icon:rl,permission:"JettyApprovalApp.ExportVesselCustomArea.View"},{title:"menu.importVessel",url:"/custom-area/import",isActive:!1,icon:el,permission:"JettyApprovalApp.ImportVesselCustomArea.View"},{title:"menu.localVessel",url:"/custom-area/local",isActive:!1,icon:tl,permission:"JettyApprovalApp.LocalVesselCustomArea.View"},{title:"menu.masterData",url:"#",isActive:!1,icon:Ki,permission:"JettyApprovalApp.JettyManage.View",items:[{title:"menu.manageJetty",url:"/manage-jetty",permission:"JettyApprovalApp.JettyManage.View"}]},{title:"menu.documentTemplate",url:"/document-template",isActive:!1,icon:Xi,permission:"JettyApprovalApp.DocumentTemplate.Create"},{title:"menu.approvalTemplate",url:"/approval-template",isActive:!1,icon:qi,permission:"JettyApprovalApp.ApprovalTemplate.Create"}];function nl(){const[e,t]=d.useState(!1);return d.useEffect(()=>{const r=window.matchMedia("(max-width: 768px)");t(r.matches);const s=o=>{t(o.matches)};return r.addEventListener("change",s),()=>r.removeEventListener("change",s)},[]),{isOpen:e}}const il=()=>{const{data:e}=Vr();return e?.currentUser};function Rt({className:e,...t}){return a.jsx(zs,{"data-slot":"avatar",className:g("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function Pt({className:e,...t}){return a.jsx($s,{"data-slot":"avatar-fallback",className:g("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function Ur({...e}){return a.jsx(Vs,{"data-slot":"dropdown-menu",...e})}function Br({...e}){return a.jsx(Fs,{className:"select-none","data-slot":"dropdown-menu-trigger",...e})}function qr({className:e,sideOffset:t=4,container:r,...s}){return a.jsx(Gs,{container:r,children:a.jsx(Us,{"data-slot":"dropdown-menu-content",sideOffset:t,className:g("space-y-0.5 z-50 min-w-[8rem] overflow-hidden rounded-md border border-border bg-popover p-2 text-popover-foreground shadow-md shadow-black/5 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})})}function ll({...e}){return a.jsx(Js,{"data-slot":"dropdown-menu-group",...e})}function ee({className:e,inset:t,variant:r,...s}){return a.jsx(Ws,{"data-slot":"dropdown-menu-item",className:g("text-foreground relative flex cursor-default select-none items-center gap-2 rounded-md px-2 py-1.5 text-sm outline-hidden transition-colors data-disabled:pointer-events-none data-disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([role=img]):not([class*=text-])]:opacity-60 [&_svg:not([class*=size-])]:size-4 [&_svg]:shrink-0","focus:bg-accent focus:text-foreground","data-[active=true]:bg-accent data-[active=true]:text-accent-foreground",t&&"ps-8",r==="destructive"&&"text-destructive hover:text-destructive focus:text-destructive hover:bg-destructive/5 focus:bg-destructive/5 data-[active=true]:bg-destructive/5",e),...s})}function Gl({className:e,children:t,checked:r,...s}){return a.jsxs(Bs,{"data-slot":"dropdown-menu-checkbox-item",className:g("relative flex cursor-default select-none items-center rounded-md py-1.5 ps-8 pe-2 text-sm outline-hidden transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",e),checked:r,...s,children:[a.jsx("span",{className:"absolute start-2 flex h-3.5 w-3.5 items-center text-muted-foreground justify-center",children:a.jsx(qs,{children:a.jsx(Sr,{className:"h-4 w-4 text-primary"})})}),t]})}function cl({className:e,inset:t,...r}){return a.jsx(Hs,{"data-slot":"dropdown-menu-label",className:g("px-2 py-1.5 text-xs text-muted-foreground font-medium",t&&"ps-8",e),...r})}function _t({className:e,...t}){return a.jsx(Ks,{"data-slot":"dropdown-menu-separator",className:g("-mx-2 my-1.5 h-px bg-muted",e),...t})}function Ul({className:e,...t}){return a.jsx("span",{"data-slot":"dropdown-menu-shortcut",className:g("ms-auto text-xs tracking-widest opacity-60",e),...t})}function dl({user:e}){const{isMobile:t}=he(),r=o=>o?o.slice(0,2).toUpperCase():"",s=async()=>{try{window.location.href="/Account/LogoutRedirect"}catch{window.location.href="/Account/LogoutRedirect"}};return a.jsx(it,{children:a.jsx(Ie,{children:a.jsxs(Ur,{children:[a.jsx(Br,{asChild:!0,children:a.jsxs(Me,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[a.jsx(Rt,{className:"h-8 w-8 rounded-lg grayscale",children:a.jsx(Pt,{className:"rounded-lg",children:r(e?.name??"")})}),a.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[a.jsx("span",{className:"truncate font-medium",children:e.name}),a.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]}),a.jsx(Ji,{className:"ml-auto size-4"})]})}),a.jsxs(qr,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[a.jsx(cl,{className:"p-0 font-normal",children:a.jsxs("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[a.jsx(Rt,{className:"h-8 w-8 rounded-lg",children:a.jsx(Pt,{className:"rounded-lg",children:r(e?.name??"")})}),a.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[a.jsx("span",{className:"truncate font-medium",children:e.name}),a.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}),a.jsx(_t,{}),a.jsxs(ll,{children:[a.jsxs(ee,{children:[a.jsx(sl,{}),"Account"]}),a.jsxs(ee,{children:[a.jsx(Hi,{}),"Billing"]}),a.jsxs(ee,{children:[a.jsx(Zi,{}),"Notifications"]})]}),a.jsx(_t,{}),a.jsxs(ee,{onClick:s,children:[a.jsx(Qi,{}),"Log out"]})]})]})})})}function ul(){const{can:e}=Fr(),t=il(),{url:r}=Gt(),s=r,{isOpen:o}=nl(),{t:n}=qt();return d.useEffect(()=>{},[o]),a.jsxs(Si,{collapsible:"icon",children:[a.jsx(Ri,{children:a.jsx(it,{children:a.jsx(Ie,{children:a.jsx(Me,{asChild:!0,className:"data-[slot=sidebar-menu-button]:!p-1.5",children:a.jsxs("a",{href:"#",children:[a.jsx(Yi,{className:"!size-5"}),a.jsx("span",{className:"text-base font-semibold",children:al.name})]})})})})}),a.jsx(_i,{className:"overflow-x-hidden",children:a.jsxs(Li,{children:[a.jsx(Di,{children:n("menu.dashboard")}),a.jsx(it,{children:ol.map(i=>{const u=i.items?.some(c=>s.startsWith(c.url));return i?.items&&i?.items?.length>0&&(!i.permission||e(i.permission))?a.jsx(ci,{asChild:!0,defaultOpen:u||i.isActive,className:"group/collapsible",children:a.jsxs(Ie,{children:[a.jsx(di,{asChild:!0,children:a.jsxs(Me,{tooltip:n(i.title),isActive:s===i.url,children:[i.icon&&a.jsx(i.icon,{}),a.jsx("span",{children:n(i.title)}),a.jsx(Ui,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),a.jsx(ui,{children:a.jsx(zi,{children:i.items?.map(c=>a.jsx($i,{children:a.jsx(Vi,{asChild:!0,isActive:s===c.url,children:a.jsx(Pe,{href:c.url,children:a.jsx("span",{children:n(c.title)})})})},c.title))})})]})},i.title):(!i.permission||e(i.permission))&&a.jsx(Ie,{children:a.jsx(Me,{asChild:!0,tooltip:n(i.title),isActive:s===i.url,children:a.jsxs(Pe,{href:i.url,children:[a.jsx(i.icon,{}),a.jsx("span",{children:n(i.title)})]})})},i.title)})})]})}),a.jsx(Pi,{children:t&&a.jsx(dl,{user:t})}),a.jsx(Ii,{})]})}class pl{constructor(){Ve(this,"baseUrl","/api/ekb-proxy")}async makeRequest(t,r="GET",s,o){try{const n=`${this.baseUrl}/${t.replace(/^\//,"")}`,i={method:r,headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest",...o},credentials:"include"};s&&r!=="GET"&&r!=="DELETE"&&(i.body=JSON.stringify(s));const u=await fetch(n,i),c=await u.text();let l;try{l=JSON.parse(c)}catch{l=c}return{data:u.ok?l:void 0,error:u.ok?void 0:l?.message||`HTTP ${u.status}`,status:u.status}}catch(n){return{error:n instanceof Error?n.message:"Network error",status:0}}}async get(t,r){return this.makeRequest(t,"GET",void 0,r)}async post(t,r,s){return this.makeRequest(t,"POST",r,s)}async put(t,r,s){return this.makeRequest(t,"PUT",r,s)}async patch(t,r,s){return this.makeRequest(t,"PATCH",r,s)}async delete(t,r){return this.makeRequest(t,"DELETE",void 0,r)}async uploadFile(t,r,s,o){try{const n=`${this.baseUrl}/upload/${t.replace(/^\//,"")}`,i=new FormData;r.forEach((b,w)=>{i.append(`file${w}`,b,b.name)}),s&&Object.entries(s).forEach(([b,w])=>{i.append(b,w)});const u={method:"POST",headers:{"X-Requested-With":"XMLHttpRequest",...o},credentials:"include",body:i},c=await fetch(n,u),l=await c.text();let p;try{p=JSON.parse(l)}catch{p=l}return{data:c.ok?p:void 0,error:c.ok?void 0:p?.message||`HTTP ${c.status}`,status:c.status}}catch(n){return{error:n instanceof Error?n.message:"Network error",status:0}}}async getExportVessels(t){const r=t?`?${new URLSearchParams(t).toString()}`:"";return this.get(`/api/ekb/export-vessel${r}`)}async getExportVessel(t){return this.get(`/api/ekb/export-vessel/${t}`)}async createExportVessel(t){return this.post("/api/ekb/export-vessel",t)}async updateExportVessel(t,r){return this.put(`/api/ekb/export-vessel/${t}`,r)}async deleteExportVessel(t){return this.delete(`/api/ekb/export-vessel/${t}`)}async generateNextExportVesselDocNum(t){const r=t?`?postDate=${encodeURIComponent(t)}`:"";return this.post(`/api/ekb/export-vessel/generate-next-doc-num${r}`)}async getExportVesselWithItems(t){return this.get(`/api/ekb/export-vessel/${t}/with-items`)}async getImportVessels(t){const r=t?`?${new URLSearchParams(t).toString()}`:"";return this.get(`/api/ekb/import-vessel${r}`)}async getImportVessel(t){return this.get(`/api/ekb/import-vessel/${t}`)}async createImportVessel(t){return this.post("/api/ekb/import-vessel",t)}async updateImportVessel(t,r){return this.put(`/api/ekb/import-vessel/${t}`,r)}async generateNextImportVesselDocNum(){return this.post("/api/ekb/import-vessel/generate-next-doc-num")}async getImportVesselWithItems(t){return this.get(`/api/ekb/import-vessel/${t}/with-items`)}async getLocalVessels(t){const r=t?`?${new URLSearchParams(t).toString()}`:"";return this.get(`/api/ekb/local-vessel${r}`)}async getLocalVessel(t){return this.get(`/api/ekb/local-vessel/${t}`)}async createLocalVessel(t){return this.post("/api/ekb/local-vessel",t)}async updateLocalVessel(t,r){return this.put(`/api/ekb/local-vessel/${t}`,r)}async getLocalVesselWithItems(t){return this.get(`/api/ekb/local-vessel/${t}/with-items`)}async generateNextLocalVesselDocNum(t){const r=t?`?postDate=${encodeURIComponent(t)}`:"";return this.post(`/api/ekb/local-vessel/generate-next-doc-num${r}`)}async getTradingVessels(t){const r=t?`?${new URLSearchParams(t).toString()}`:"";return this.get(`/api/ekb/trading-vessel${r}`)}async getAgents(t){const r=new URLSearchParams(t).toString();return this.get(`/api/master/agent/filter-list?${r}`)}async getBusinessPartners(t){const r=new URLSearchParams(t).toString();return this.get(`/api/ekb/business-partner/filter-list?${r}`)}async getJetties(t){const r=new URLSearchParams(t).toString();return this.get(`/api/ekb/jetty/filter-list?${r}`)}async getTenants(t){const r=new URLSearchParams(t).toString();return this.get(`/api/ekb/tenant/filter-list?${r}`)}async getApplicationConfiguration(){return this.get("/api/abp/application-configuration")}async uploadVesselDocuments(t,r,s){return this.uploadFile(`/api/ekb/vessel/${t}/documents`,r,s)}async filterImportVessels(t){return this.post("/api/ekb/import-vessel/filter-list",t)}async filterExportVessels(t){return this.post("/api/ekb/export-vessel/filter-list",t)}async filterLocalVessels(t){return this.post("/api/ekb/local-vessel/filter-list",t)}async filterTradingVessels(t){return this.post("/api/ekb/trading-vessel/filter-list",t)}async filterBusinessPartners(t){return this.post("/api/ekb/business-partner/filter-list",t)}async filterTenants(t){return this.post("/api/ekb/tenant/filter-list",t)}async filterJetties(t){return this.post("/api/ekb/jetty/filter-list",t)}async filterAgents(t){return this.post("/api/master/agent/filter-list",t)}async filterCargo(t){return this.post("/api/ekb/cargo/filter-list",t)}async filterDestinationPorts(t){return this.post("/api/ekb/destination-port/filter-list",t)}async filterPortOfLoading(t){return this.post("/api/ekb/port-of-loading/filter-list",t)}async getFileStream(t){try{const r=`${this.baseUrl}/api/ekb/files/stream/${t}`,s=await fetch(r,{method:"GET",credentials:"include",headers:{"X-Requested-With":"XMLHttpRequest"}});return s.ok?{data:await s.blob(),status:s.status}:{error:await s.text()||`HTTP ${s.status}`,status:s.status}}catch(r){return{error:r instanceof Error?r.message:"Network error",status:0}}}async deleteDocAttachment(t){return this.delete(`/api/ekb/doc-attachments/${t}`)}}const Ee=new pl;function ml({...e}){return a.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function fl({className:e,...t}){return a.jsx("ol",{"data-slot":"breadcrumb-list",className:g("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function Hr({className:e,...t}){return a.jsx("li",{"data-slot":"breadcrumb-item",className:g("inline-flex items-center gap-1.5",e),...t})}function Wr({asChild:e,className:t,...r}){const s=e?pe:"a";return a.jsx(s,{"data-slot":"breadcrumb-link",className:g("hover:text-foreground transition-colors",t),...r})}function hl({className:e,...t}){return a.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:g("text-foreground font-normal",e),...t})}function Kr({children:e,className:t,...r}){return a.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:g("[&>svg]:size-3.5",t),...r,children:e??a.jsx(un,{})})}const Jr=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function gl(e,t){return Dt({queryKey:["vessel-name",e],queryFn:async()=>{try{let r;switch(t){case"export":r=await Ee.getExportVesselWithItems(e);break;case"import":r=await Ee.getImportVesselWithItems(e);break;case"local":r=await Ee.getLocalVesselWithItems(e);break;default:return null}const s=r.data;if(!s)return null;if(s.docNum)return`Doc: ${s.docNum}`;if(s.vesselName){if(typeof s.vesselName=="string")return`Vessel: ${s.vesselName}`;if(typeof s.vesselName=="number"&&s.vesselId)try{const n=(await Ee.filterCargo({maxResultCount:1,skipCount:0,filterGroup:{operator:"And",conditions:[{fieldName:"id",operator:"Equals",value:s.vesselId}]}})).data?.items?.[0];if(n?.name)return`Vessel: ${n.name}`}catch{}}return s.voyage?`Voyage: ${s.voyage}`:s.shipment?`Shipment: ${s.shipment}`:null}catch{return null}},enabled:!!e&&Jr.test(e),staleTime:5*60*1e3,retry:!1})}function bl({segment:e,index:t,pathSegments:r,isLastItem:s}){const o=`/${r.slice(0,t+1).join("/")}`,n=Jr.test(e),i=d.useMemo(()=>n?r.includes("export")?"export":r.includes("import")?"import":r.includes("local")?"local":null:null,[n,r]),{data:u,isLoading:c}=gl(e,i),l=d.useMemo(()=>n&&u?u:n&&c?"Loading...":n?`ID: ${e.substring(0,8)}...`:e.split("-").map(p=>p.charAt(0).toUpperCase()+p.slice(1)).join(" "),[e,n,u,c]);return a.jsxs(d.Fragment,{children:[a.jsx(Hr,{children:s?a.jsx(hl,{children:l}):a.jsx(Wr,{asChild:!0,children:a.jsx(Pe,{href:o,children:l})})}),!s&&a.jsx(Kr,{})]},o)}function xl(){const{url:e}=Gt();if(e==="/")return null;const t=e.split("/").filter(Boolean);return a.jsx(ml,{children:a.jsxs(fl,{children:[a.jsx(Hr,{children:a.jsx(Wr,{asChild:!0,children:a.jsx(Pe,{href:"/",children:"Home"})})}),a.jsx(Kr,{}),t.map((r,s)=>{const o=s===t.length-1;return a.jsx(bl,{segment:r,index:s,pathSegments:t,isLastItem:o},`${r}-${s}`)})]})})}function vl({className:e,...t}){return a.jsx(Xs,{"data-slot":"label",className:g("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}const yl=[{name:"Default",value:"default"},{name:"Blue",value:"blue"},{name:"Green",value:"green"},{name:"Amber",value:"amber"}],wl=[{name:"Default",value:"default-scaled"},{name:"Blue",value:"blue-scaled"},{name:"Green",value:"green-scaled"}],kl=[{name:"Mono",value:"mono-scaled"}];function jl(){const{activeTheme:e,setActiveTheme:t}=$r(),r=s=>{t(s)};return a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(vl,{htmlFor:"theme-selector",className:"sr-only",children:"Theme"}),a.jsxs(Cn,{value:e,onValueChange:r,children:[a.jsxs(Sn,{id:"theme-selector",className:"justify-start *:data-[slot=select-value]:w-12",children:[a.jsx("span",{className:"text-muted-foreground hidden sm:block",children:"Select a theme:"}),a.jsx("span",{className:"text-muted-foreground block sm:hidden",children:"Theme"}),a.jsx(En,{placeholder:"Select a theme"})]}),a.jsxs(Mn,{align:"end",children:[a.jsxs(Be,{children:[a.jsx(qe,{children:"Default"}),yl.map(s=>a.jsx(He,{value:s.value,children:s.name},s.name))]}),a.jsx(Rn,{}),a.jsxs(Be,{children:[a.jsx(qe,{children:"Scaled"}),wl.map(s=>a.jsx(He,{value:s.value,children:s.name},s.name))]}),a.jsxs(Be,{children:[a.jsx(qe,{children:"Monospaced"}),kl.map(s=>a.jsx(He,{value:s.value,children:s.name},s.name))]})]})]})]})}function Tl(){const{setTheme:e,resolvedTheme:t}=Hn(),r=d.useCallback(s=>{const o=t==="dark"?"light":"dark",n=document.documentElement;if(!document.startViewTransition){e(o);return}s&&(n.style.setProperty("--x",`${s.clientX}px`),n.style.setProperty("--y",`${s.clientY}px`)),document.startViewTransition(()=>{e(o)})},[t,e]);return a.jsxs(ae,{variant:"secondary",size:"icon",className:"group/toggle size-8",onClick:r,children:[a.jsx(Gi,{}),a.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}const Xr="lang";function Cl(e){document.cookie=`${Xr}=${e}; path=/; max-age=31536000`}function El(){const e=document.cookie.match(new RegExp("(^| )"+Xr+"=([^;]+)"));return e?e[2]:void 0}const Nl={id:"ID",en:"EN","zh-CN":"简体"};function Sl(){const{i18n:e,t}=qt(),[r,s]=bt.useState(!1),[o,n]=bt.useState(e.language);d.useEffect(()=>{const u=El();u&&u!==e.language&&(e.changeLanguage(u),n(u))},[]);const i=u=>{e.changeLanguage(u),n(u),Cl(u),s(!1)};return a.jsxs(Ur,{open:r,onOpenChange:s,children:[a.jsx(Br,{asChild:!0,children:a.jsxs(ae,{size:"sm","aria-label":t("Select language"),variant:"outline",children:[a.jsx(hn,{className:"w-5 h-5"}),a.jsx("span",{className:"uppercase font-semibold",children:Nl[o]||o.toUpperCase()})]})}),a.jsxs(qr,{align:"end",className:"w-48",children:[a.jsx(ee,{onClick:()=>i("id"),className:o==="id"?"font-semibold":"",children:"Bahasa Indonesia"}),a.jsx(ee,{onClick:()=>i("en"),className:o==="en"?"font-semibold":"",children:"English"}),a.jsx(ee,{onClick:()=>i("zh-CN"),className:o==="zh-CN"?"font-semibold":"",children:"简体中文"})]})]})}function Al(){return a.jsxs("header",{className:"sticky top-0 z-50 flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 bg-background border-b",children:[a.jsxs("div",{className:"flex items-center gap-2 px-4",children:[a.jsx(Ai,{className:"-ml-1"}),a.jsx(mi,{orientation:"vertical",className:"mr-2 h-4"}),a.jsx(xl,{})]}),a.jsxs("div",{className:"flex items-center gap-2 px-4",children:[a.jsx(Sl,{}),a.jsx(Tl,{}),a.jsx(jl,{})]})]})}function Il({children:e,policy:t,message:r}){const s=de.get("sidebar_state")==="true",{activeTheme:o}=$r(),n=o.endsWith("-scaled");return a.jsxs("div",{className:g("bg-background font-sans antialiased",`theme-${o}`,n?"theme-scaled":""),children:[a.jsx(Or,{}),a.jsxs(Ni,{defaultOpen:s,children:[a.jsx(ul,{}),a.jsxs(Mi,{children:[a.jsx(Al,{}),a.jsx("div",{className:"flex flex-1 flex-col overflow-auto",children:a.jsx("div",{className:"@container/main flex flex-1 flex-col gap-2 bg-muted/60",children:a.jsx("div",{className:"flex flex-col gap-4 py-2 px-2 md:gap-4 md:py-4 md:px-4",children:t!==void 0?a.jsx(li,{policy:t,message:r,children:e}):e})})})]})]})]})}function Bl(e){return a.jsxs(d.StrictMode,{children:[a.jsx("div",{id:"portal-root"}),a.jsx(Kn,{children:a.jsx(Wn,{defaultTheme:"system",storageKey:"vite-ui-theme",children:a.jsxs(oi,{children:[a.jsx(Or,{}),a.jsx(Il,{...e})]})})})]})}export{Le as $,Bl as A,ae as B,Sr as C,Ur as D,Kn as E,qt as F,Dn as G,On as H,Ji as I,zn as J,da as K,vl as L,Ee as M,F as N,ca as O,ua as P,pa as Q,vn as R,Cn as S,jn as T,xa as U,ma as V,Lr as W,Ir as X,fa as Y,ha as Z,Dl as _,wa as a,de as a0,un as a1,ni as a2,ct as a3,Fl as a4,Gn as a5,Vl as a6,tn as a7,Ar as a8,Ul as a9,Ll as aa,$l as ab,ga as b,Sn as c,En as d,Mn as e,He as f,ya as g,ia as h,ja as i,Ta as j,q as k,g as l,ba as m,ka as n,$n as o,va as p,Vn as q,Fn as r,Un as s,Pr as t,_r as u,Br as v,qr as w,Gl as x,cl as y,ee as z};
//# sourceMappingURL=app-layout-rNt37hVL.js.map
