import { ekbProxyService } from '@/services/ekbProxyService';
import type { CreateUpdateLocalVesselDto, CreateUpdateVesselItemDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { LocalVesselFormWithData } from '@/components/jetty/vessel/local/local-vessel-form';
import type { LocalVesselHeaderForm } from '@/components/jetty/vessel/local/local-vessel-header-schema';
import type { LocalVesselItemForm } from '@/components/jetty/vessel/local/local-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { toDatetimeLocalString } from '@/lib/utils/date-convert';
import { Head, usePage } from '@inertiajs/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const LocalVesselEditPage = () => {
  const { t } = useTranslation();
  const { props } = usePage();
  const { toast } = useToast();
  const id = typeof props.id === 'string' ? props.id : undefined;
  const queryClient = useQueryClient();

  const {
    data: vesselData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['local-vessel', id],
    queryFn: async () => {
      if (!id) return null;
      const response = await ekbProxyService.getLocalVesselWithItems(id);
      return response.data;
    },
    enabled: !!id,
  });

  const initialHeader: Partial<Record<keyof CreateUpdateLocalVesselDto, string>> = vesselData
    ? {
      docNum: vesselData.docNum ?? '',
      voyage: vesselData.voyage ?? '',
      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? '') ?? '',
      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? '') ?? '',
      asideDate: toDatetimeLocalString(vesselData.asideDate ?? '') ?? '',
      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? '') ?? '',
      vesselId: vesselData.vesselId ?? '',
      bargeId: vesselData.bargeId ?? '',
      jettyId: vesselData.jettyId ?? '',
      portOriginId: vesselData.portOriginId ?? '',
      destinationPortId: vesselData.destinationPortId ?? '',
      postingDate: vesselData.postingDate ?? '',
      ...(vesselData.concurrencyStamp ? { concurrencyStamp: vesselData.concurrencyStamp } : {}),
      deleted: vesselData.deleted ?? '',
      docType: vesselData.docType ?? '',
      docStatus: vesselData.docStatus ?? '',
      statusBms: vesselData.statusBms ?? '',
      transType: vesselData.transType ?? '',
      status: vesselData.status ?? '',
      vesselType: vesselData.vesselType ?? '',
      shipment: vesselData.shipment ?? '',
      portOrigin: vesselData.portOrigin ?? '',
      destinationPort: vesselData.destinationPort ?? '',
    }
    : {
      deleted: '',
      docType: '',
      docStatus: '',
      statusBms: '',
      transType: '',
      status: '',
      vesselType: '',
      shipment: '',
      portOrigin: '',
      destinationPort: '',
      concurrencyStamp: '',
    };

  const initialItems: CreateUpdateVesselItemDto[] = vesselData?.items
    ? vesselData.items.map(item => {
      let unitWeight: string | null | undefined = null;
      if (item.unitWeight != null) {
        if (typeof item.unitWeight === 'string') {
          unitWeight = item.unitWeight !== '' ? item.unitWeight : null;
        } else {
          unitWeight = String(item.unitWeight);
        }
      }
      let grossWeight: number | null = null;
      if (item.grossWeight != null) {
        if (typeof item.grossWeight === 'string') {
          grossWeight = item.grossWeight !== '' ? Number(item.grossWeight) : null;
        } else {
          grossWeight = item.grossWeight;
        }
      }
      return {
        itemName: item.itemName ?? null,
        itemQty: item.itemQty ?? 0,
        unitQty: item.unitQty ?? null,
        remarks: item.remarks ?? null,
        ajuNo: item.ajuNo ?? null,
        regDate: item.regDate ? item.regDate : null,
        regNo: item.regNo ?? null,
        grossWeight,
        unitWeight,
        shippingInstructionNo: item.shippingInstructionNo ?? null,
        shippingInstructionDate: item.shippingInstructionDate ?? null,
        letterNo: item.letterNo ?? null,
        letterDate: item.letterDate ?? null,
        status: item.status ?? null,
        regType: item.regType ?? null,
        attachments: item.attachments ?? [],
        tenant: item.tenantName ?? null,
        tenantId: item.tenantId ?? '',
        businessPartner: item.businessPartner?.name ?? null,
        businessPartnerId: item.businessPartnerId ?? null,
        concurrencyStamp: item.concurrencyStamp ?? undefined,
        // Only include concurrencyStamp if present and non-empty
        ...(item.concurrencyStamp ? { concurrencyStamp: item.concurrencyStamp } : {}),
        id: item.id ?? '',
      };
    })
    : [];

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: LocalVesselHeaderForm; items: LocalVesselItemForm[] }) => {
      if (!id) throw new Error('No ID provided');
      const response = await ekbProxyService.updateLocalVessel(id, {
        ...header,
        docNum: header.docNum ?? '',
        docType: 'Local',
        deleted: header.deleted ?? '',
        docStatus: header.docStatus ?? 'Open',
        statusBms: header.statusBms ?? '',
        transType: header.transType ?? '',
        status: header.status ?? '',
        vesselType: header.vesselType ?? '',
        shipment: header.shipment ?? '',
        portOrigin: header.portOrigin ?? '',
        destinationPort: header.destinationPort ?? '',
        concurrencyStamp: header.concurrencyStamp ?? '',
        items: items.map(item => ({
          ...item,
          createdBy: '',
          docType: '',
          isScan: '',
          isOriginal: '',
          isActive: true,
          isDeleted: false,
          isSend: '',
          isFeOri: '',
          isFeSend: '',
          isChange: '',
          isFeChange: '',
          isFeActive: '',
          deleted: '',
          isUrgent: '',
          tenantId: item.tenantId || '',
          concurrencyStamp: item.concurrencyStamp || '',
          businessPartnerId: item.businessPartnerId || '',
        })),
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Local vessel updated.', variant: 'success' });
      queryClient.invalidateQueries({ queryKey: ['local-vessel', id] });
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;

  return (
    <LocalVesselFormWithData
      mode="edit"
      title={t('pages.vessel.edit.local')}
      initialHeader={initialHeader}
      initialItems={initialItems}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
      queryClient={queryClient}
      vesselData={vesselData || undefined}
      jettyList={vesselData?.masterJetty ? [{ id: vesselData.masterJetty.id ?? '', isCustomArea: !!vesselData.masterJetty.isCustomArea }] : []}
    />
  );
};

export default function LocalVesselEdit() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.edit.local')} />
      <LocalVesselEditPage />
    </AppLayout>
  );
}
