import { useQuery } from '@tanstack/react-query';
import { toast } from '@/lib/useToast';
import { QueryNames } from './QueryConstants';
import type { PagedResultDtoOfApprovalStageDto } from '@/client/types.gen';

export const usePendingApprovals = (maxResults: number = 10) => {
  return useQuery<PagedResultDtoOfApprovalStageDto, Error>({
    queryKey: [QueryNames.GetPendingApprovals, maxResults],
    queryFn: async (): Promise<PagedResultDtoOfApprovalStageDto> => {
      try {
        const response = await fetch(`/api/dashboard/pending-approvals?maxResults=${maxResults}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading pending approvals';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }
        
        console.error('Pending Approvals API Error:', error);
        toast({
          title: 'Error loading pending approvals',
          description: message,
          variant: 'destructive',
        });
        
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
};
