{"version": 3, "file": "page-DdC_CrSA.js", "sources": ["../../../../../frontend/src/pages/local/create/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { LocalVesselFormWithData } from '@/components/jetty/vessel/local/local-vessel-form';\r\nimport type { LocalVesselHeaderForm } from '@/components/jetty/vessel/local/local-vessel-header-schema';\r\nimport type { LocalVesselItemForm } from '@/components/jetty/vessel/local/local-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { Head, router } from '@inertiajs/react';\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst LocalVesselCreatePage = () => {\r\n  const { t } = useTranslation();\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: LocalVesselHeaderForm; items: LocalVesselItemForm[] }) => {\r\n      const response = await ekbProxyService.createLocalVessel({\r\n        ...header,\r\n        docType: 'Local',\r\n        vesselId: header.vesselId ? String(header.vesselId) : '',\r\n        jettyId: header.jettyId ? String(header.jettyId) : '',\r\n        concurrencyStamp: header.concurrencyStamp ? String(header.concurrencyStamp) : '',\r\n        items: items.map(item => ({\r\n          ...item,\r\n          createdBy: '',\r\n          docType: '',\r\n          isScan: '',\r\n          isOriginal: '',\r\n          isActive: true,\r\n          isDeleted: false,\r\n          isSend: '',\r\n          isFeOri: '',\r\n          isFeSend: '',\r\n          isChange: '',\r\n          isFeChange: '',\r\n          isFeActive: '',\r\n          deleted: '',\r\n          isUrgent: '',\r\n          tenantId: item.tenantId || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n          concurrencyStamp: item.concurrencyStamp || '',\r\n        }))\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: (data) => {\r\n      toast({ title: 'Success', description: 'Local vessel created.', variant: 'success' });\r\n      if (data && data.id) {\r\n        router.visit(`/local/edit/${data.id}`);\r\n      }\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  return (\r\n    <LocalVesselFormWithData\r\n      mode=\"create\"\r\n      title={t('pages.vessel.create.local')}\r\n      initialHeader={{}}\r\n      initialItems={[]}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n      queryClient={queryClient}\r\n      jettyList={[]}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function LocalVesselCreate() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.create.local')} />\r\n      <LocalVesselCreatePage />\r\n    </AppLayout>\r\n  );\r\n} "], "names": ["LocalVesselCreatePage", "t", "useTranslation", "toast", "useToast", "queryClient", "useQueryClient", "mutation", "useMutation", "header", "items", "response", "ekbProxyService", "item", "data", "router", "err", "handleSubmit", "jsx", "LocalVesselFormWithData", "LocalVesselCreate", "AppLayout", "Head"], "mappings": "qsBAWA,MAAMA,EAAwB,IAAM,CAC5B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAE7BC,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA6E,CAClG,MAAAC,EAAW,MAAMC,EAAgB,kBAAkB,CACvD,GAAGH,EACH,QAAS,QACT,SAAUA,EAAO,SAAW,OAAOA,EAAO,QAAQ,EAAI,GACtD,QAASA,EAAO,QAAU,OAAOA,EAAO,OAAO,EAAI,GACnD,iBAAkBA,EAAO,iBAAmB,OAAOA,EAAO,gBAAgB,EAAI,GAC9E,MAAOC,EAAM,IAAaG,IAAA,CACxB,GAAGA,EACH,UAAW,GACX,QAAS,GACT,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,UAAW,GACX,OAAQ,GACR,QAAS,GACT,SAAU,GACV,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,QAAS,GACT,SAAU,GACV,SAAUA,EAAK,UAAY,GAC3B,kBAAmBA,EAAK,mBAAqB,GAC7C,iBAAkBA,EAAK,kBAAoB,EAAA,EAC3C,CAAA,CACH,EACD,GAAIF,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAYG,GAAS,CACnBX,EAAM,CAAE,MAAO,UAAW,YAAa,wBAAyB,QAAS,UAAW,EAChFW,GAAQA,EAAK,IACfC,EAAO,MAAM,eAAeD,EAAK,EAAE,EAAE,CAEzC,EACA,QAAUE,GAAoC,CACtCb,EAAA,CACJ,MAAOa,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOR,EAA+BC,IAAiC,CAC1F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAGE,OAAAQ,EAAA,IAACC,EAAA,CACC,KAAK,SACL,MAAOlB,EAAE,2BAA2B,EACpC,cAAe,CAAC,EAChB,aAAc,CAAC,EACf,SAAUgB,EACV,aAAcV,EAAS,UACvB,YAAAF,EACA,UAAW,CAAA,CAAC,CACd,CAEJ,EAEA,SAAwBe,GAAoB,CACpC,KAAA,CAAE,EAAAnB,CAAE,EAAIC,EAAe,EAC7B,cACGmB,EACC,CAAA,SAAA,CAAAH,EAAA,IAACI,EAAK,CAAA,MAAOrB,EAAE,2BAA2B,CAAG,CAAA,QAC5CD,EAAsB,CAAA,CAAA,CAAA,EACzB,CAEJ"}