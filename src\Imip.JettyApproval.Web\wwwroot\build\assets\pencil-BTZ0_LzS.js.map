{"version": 3, "file": "pencil-BTZ0_LzS.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/pencil.js"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z\",\n      key: \"1a8usu\"\n    }\n  ],\n  [\"path\", { d: \"m15 5 4 4\", key: \"1mk7zo\" }]\n];\nconst Pencil = createLucideIcon(\"pencil\", __iconNode);\n\nexport { __iconNode, Pencil as default };\n//# sourceMappingURL=pencil.js.map\n"], "names": ["__iconNode", "Pencil", "createLucideIcon"], "mappings": "6CAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,EAAa,CACjB,CACE,OACA,CACE,EAAG,mIACH,IAAK,QACX,CACG,EACD,CAAC,OAAQ,CAAE,EAAG,YAAa,IAAK,QAAU,CAAA,CAC5C,EACMC,EAASC,EAAiB,SAAUF,CAAU", "x_google_ignoreList": [0]}