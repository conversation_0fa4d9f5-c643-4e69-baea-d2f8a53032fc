import{j as e}from"./vendor-6tJeyfYI.js";import{D as s,b as a,c as i,d as n}from"./dialog-BmEXyFlW.js";const m=({isOpen:o,onOpenChange:l,documentSrc:r})=>e.jsx(s,{open:o,onOpenChange:l,children:e.jsxs(a,{className:"min-w-[800px] w-auto h-[90vh] flex flex-col",children:[e.jsx(i,{children:e.jsx(n,{children:"Preview Document"})}),e.jsx("div",{className:"flex-grow overflow-auto p-4",children:e.jsx("iframe",{src:r,title:"Document Preview",className:"w-full h-full border-none"})})]})});export{m as D};
//# sourceMappingURL=DocumentPreviewDialog-C1lfiEeE.js.map
