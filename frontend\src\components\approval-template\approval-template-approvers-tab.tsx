import type { PagedResultDtoOfExtendedIdentityUserDto } from "@/client/types.gen";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import type { MultiSelectOption } from "@/components/ui/multi-select";
import { MultiSelect } from "@/components/ui/multi-select";
import type { UseMutationResult } from "@tanstack/react-query";
import { Trash2 } from "lucide-react";
import React from "react";
import type { Control, FieldArrayWithId, UseFieldArrayAppend, UseFormSetValue, UseFormWatch } from "react-hook-form";
import type { FormValues } from "./approval-template-dialog";

type ApproversTabProps = {
  fields: FieldArrayWithId<FormValues, "approvers", "id">[];
  append: UseFieldArrayAppend<FormValues, "approvers">;
  remove: (index: number) => void;
  control: Control<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watch: UseFormWatch<FormValues>;
  searchValue: string;
  setSearchValue: (val: string) => void;
  userOptions: MultiSelectOption[];
  setUserOptions: (opts: MultiSelectOption[]) => void;
  userSearchMutation: UseMutationResult<PagedResultDtoOfExtendedIdentityUserDto, Error, string>;
};

const ApproversTab: React.FC<ApproversTabProps> = ({ fields, append, remove, control, setValue, watch, searchValue, setSearchValue, userOptions, setUserOptions, userSearchMutation }) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <div className="font-semibold">Approvers</div>
        <Button type="button" onClick={() => append({ approvalId: null, approverId: "", sequence: fields.length + 1, status: undefined, id: "00000000-0000-0000-0000-000000000000" })} size="sm">
          Add Approver
        </Button>
      </div>
      <div className="space-y-2">
        {fields.length === 0 && <div className="text-muted-foreground text-sm">No approvers added.</div>}
        {fields.map((field, idx) => {
          const currentApproverId = watch(`approvers.${idx}.approverId`);
          const selectedOption = userOptions.find(opt => opt.value === currentApproverId);

          // Create enhanced options that include selected user even if not in main options
          const enhancedOptions = currentApproverId && !selectedOption
            ? [{
                value: currentApproverId,
                label: `Loading user... (${currentApproverId.substring(0, 8)}...)`
              }, ...userOptions]
            : [...userOptions];



          return (
            <div key={field.id} className="flex flex-col md:flex-row items-center gap-2 border rounded p-2">
              <span className="font-semibold text-sm">Approver #{idx + 1}</span>
              <input type="hidden" {...control.register(`approvers.${idx}.id`)} />
              <input type="hidden" {...control.register(`approvers.${idx}.approvalId`)} />
              <div className="w-110">
                <MultiSelect
                  options={enhancedOptions}
                  value={currentApproverId ? [currentApproverId] : []}
                  onChange={vals => setValue(`approvers.${idx}.approverId`, vals[0] ?? "", { shouldDirty: true })}
                  placeholder="Select user..."
                  mode="single"
                  maxHeight={220}
                  searchValue={searchValue}
                onSearchValueChange={val => {
                  setSearchValue(val);
                  // Debounce search to avoid too many API calls
                  if ((val.length >= 2 || val.length === 0) && !userSearchMutation.isPending) {
                    userSearchMutation.mutate(val, {
                      onSuccess: users => {
                        const newOptions = (users.items ?? []).map(u => ({
                          value: u.id ?? "",
                          label: u.name || u.userName || u.email || "(no name)"
                        }));

                        // Simply replace with new options for now (selected users are loaded separately)
                        setUserOptions(newOptions);
                      }
                    });
                  }
                }}
              />
            </div>
            <Input
              type="number"
              {...control.register(`approvers.${idx}.sequence`)}
              placeholder="Sequence"
              className="w-24"
            />
            {/* <Input
              {...control.register(`approvers.${idx}.status`)}
              placeholder="Status"
              className="w-24"
            /> */}
            <Button type="button" variant="ghost" size="icon" onClick={() => remove(idx)} aria-label="Remove Approver">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        );
        })}
      </div>
    </div>
  );
};

export default ApproversTab; 