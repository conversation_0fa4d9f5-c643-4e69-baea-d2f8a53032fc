import{a as d,$ as S,j as T}from"./vendor-6tJeyfYI.js";import{k as dd,a3 as cd,a1 as fd,l as Et,A as md,o as nn,q as un,r as ln,s as on,L as sn,S as dn,c as cn,d as fn,e as mn,f as $n,B as $d}from"./app-layout-rNt37hVL.js";import{I as bd}from"./input-DlXlkYlT.js";import{r as vd,$ as hd}from"./radix-e4nK4mWk.js";import{C as pd}from"./chevron-left-DJFXm33k.js";import{H as Dd,r as yd}from"./ht-theme-main.min-DuylQxQp.js";import"./App-DnhJzTNn.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gd=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],xd=dd("calendar",gd);d.createContext(null);d.createContext(null);d.createContext(null);d.createContext(null);d.createContext(null);const Sr=d.createContext({}),_=typeof document<"u"?S.useLayoutEffect:()=>{};function te(e){const t=d.useRef(null);return _(()=>{t.current=e},[e]),d.useCallback((...a)=>{const r=t.current;return r?.(...a)},[])}function Ed(e){let[t,a]=d.useState(e),r=d.useRef(null),n=te(()=>{if(!r.current)return;let i=r.current.next();if(i.done){r.current=null;return}t===i.value?n():a(i.value)});_(()=>{r.current&&n()});let u=te(i=>{r.current=i(t),n()});return[t,u]}const yu={prefix:String(Math.round(Math.random()*1e10)),current:0},gu=S.createContext(yu),Cd=S.createContext(!1);let Aa=new WeakMap;function wd(e=!1){let t=d.useContext(gu),a=d.useRef(null);if(a.current===null&&!e){var r,n;let u=(n=S.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)===null||n===void 0||(r=n.ReactCurrentOwner)===null||r===void 0?void 0:r.current;if(u){let i=Aa.get(u);i==null?Aa.set(u,{id:t.current,state:u.memoizedState}):u.memoizedState!==i.state&&(t.current=i.id,Aa.delete(u))}a.current=++t.current}return a.current}function Pd(e){let t=d.useContext(gu),a=wd(!!e),r=`react-aria${t.prefix}`;return e||`${r}-${a}`}function Bd(e){let t=S.useId(),[a]=d.useState(Fr()),r=a?"react-aria":`react-aria${yu.prefix}`;return e||`${r}-${t}`}const Sd=typeof S.useId=="function"?Bd:Pd;function Fd(){return!1}function Rd(){return!0}function kd(e){return()=>{}}function Fr(){return typeof S.useSyncExternalStore=="function"?S.useSyncExternalStore(kd,Fd,Rd):d.useContext(Cd)}let Ad=!!(typeof window<"u"&&window.document&&window.document.createElement),et=new Map,ht;typeof FinalizationRegistry<"u"&&(ht=new FinalizationRegistry(e=>{et.delete(e)}));function xe(e){let[t,a]=d.useState(e),r=d.useRef(null),n=Sd(t),u=d.useRef(null);if(ht&&ht.register(u,n),Ad){const i=et.get(n);i&&!i.includes(r)?i.push(r):et.set(n,[r])}return _(()=>{let i=n;return()=>{ht&&ht.unregister(u),et.delete(i)}},[n]),d.useEffect(()=>{let i=r.current;return i&&a(i),()=>{i&&(r.current=null)}}),n}function Td(e,t){if(e===t)return e;let a=et.get(e);if(a)return a.forEach(n=>n.current=t),t;let r=et.get(t);return r?(r.forEach(n=>n.current=e),e):t}function ta(e=[]){let t=xe(),[a,r]=Ed(t),n=d.useCallback(()=>{r(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,r]);return _(n,[t,n,...e]),a}function Ct(...e){return(...t)=>{for(let a of e)typeof a=="function"&&a(...t)}}const K=e=>{var t;return(t=e?.ownerDocument)!==null&&t!==void 0?t:document},ve=e=>e&&"window"in e&&e.window===e?e:K(e).defaultView||window;function Md(e){return e!==null&&typeof e=="object"&&"nodeType"in e&&typeof e.nodeType=="number"}function Nd(e){return Md(e)&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}let Id=!1;function ba(){return Id}function oe(e,t){if(!ba())return t&&e?e.contains(t):!1;if(!e||!t)return!1;let a=t;for(;a!==null;){if(a===e)return!0;a.tagName==="SLOT"&&a.assignedSlot?a=a.assignedSlot.parentNode:Nd(a)?a=a.host:a=a.parentNode}return!1}const ne=(e=document)=>{var t;if(!ba())return e.activeElement;let a=e.activeElement;for(;a&&"shadowRoot"in a&&(!((t=a.shadowRoot)===null||t===void 0)&&t.activeElement);)a=a.shadowRoot.activeElement;return a};function J(e){return ba()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}class Vd{get currentNode(){return this._currentNode}set currentNode(t){if(!oe(this.root,t))throw new Error("Cannot set currentNode to a node that is not contained by the root node.");const a=[];let r=t,n=t;for(this._currentNode=t;r&&r!==this.root;)if(r.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const i=r,l=this._doc.createTreeWalker(i,this.whatToShow,{acceptNode:this._acceptNode});a.push(l),l.currentNode=n,this._currentSetFor.add(l),r=n=i.host}else r=r.parentNode;const u=this._doc.createTreeWalker(this.root,this.whatToShow,{acceptNode:this._acceptNode});a.push(u),u.currentNode=n,this._currentSetFor.add(u),this._walkerStack=a}get doc(){return this._doc}firstChild(){let t=this.currentNode,a=this.nextNode();return oe(t,a)?(a&&(this.currentNode=a),a):(this.currentNode=t,null)}lastChild(){let a=this._walkerStack[0].lastChild();return a&&(this.currentNode=a),a}nextNode(){const t=this._walkerStack[0].nextNode();if(t){if(t.shadowRoot){var a;let n;if(typeof this.filter=="function"?n=this.filter(t):!((a=this.filter)===null||a===void 0)&&a.acceptNode&&(n=this.filter.acceptNode(t)),n===NodeFilter.FILTER_ACCEPT)return this.currentNode=t,t;let u=this.nextNode();return u&&(this.currentNode=u),u}return t&&(this.currentNode=t),t}else if(this._walkerStack.length>1){this._walkerStack.shift();let r=this.nextNode();return r&&(this.currentNode=r),r}else return null}previousNode(){const t=this._walkerStack[0];if(t.currentNode===t.root){if(this._currentSetFor.has(t))if(this._currentSetFor.delete(t),this._walkerStack.length>1){this._walkerStack.shift();let n=this.previousNode();return n&&(this.currentNode=n),n}else return null;return null}const a=t.previousNode();if(a){if(a.shadowRoot){var r;let u;if(typeof this.filter=="function"?u=this.filter(a):!((r=this.filter)===null||r===void 0)&&r.acceptNode&&(u=this.filter.acceptNode(a)),u===NodeFilter.FILTER_ACCEPT)return a&&(this.currentNode=a),a;let i=this.lastChild();return i&&(this.currentNode=i),i}return a&&(this.currentNode=a),a}else if(this._walkerStack.length>1){this._walkerStack.shift();let n=this.previousNode();return n&&(this.currentNode=n),n}else return null}nextSibling(){return null}previousSibling(){return null}parentNode(){return null}constructor(t,a,r,n){this._walkerStack=[],this._currentSetFor=new Set,this._acceptNode=i=>{if(i.nodeType===Node.ELEMENT_NODE){const o=i.shadowRoot;if(o){const s=this._doc.createTreeWalker(o,this.whatToShow,{acceptNode:this._acceptNode});return this._walkerStack.unshift(s),NodeFilter.FILTER_ACCEPT}else{var l;if(typeof this.filter=="function")return this.filter(i);if(!((l=this.filter)===null||l===void 0)&&l.acceptNode)return this.filter.acceptNode(i);if(this.filter===null)return NodeFilter.FILTER_ACCEPT}}return NodeFilter.FILTER_SKIP},this._doc=t,this.root=a,this.filter=n??null,this.whatToShow=r??NodeFilter.SHOW_ALL,this._currentNode=a,this._walkerStack.unshift(t.createTreeWalker(a,r,this._acceptNode));const u=a.shadowRoot;if(u){const i=this._doc.createTreeWalker(u,this.whatToShow,{acceptNode:this._acceptNode});this._walkerStack.unshift(i)}}}function Ld(e,t,a,r){return ba()?new Vd(e,t,a,r):e.createTreeWalker(t,a,r)}function Z(...e){let t={...e[0]};for(let a=1;a<e.length;a++){let r=e[a];for(let n in r){let u=t[n],i=r[n];typeof u=="function"&&typeof i=="function"&&n[0]==="o"&&n[1]==="n"&&n.charCodeAt(2)>=65&&n.charCodeAt(2)<=90?t[n]=Ct(u,i):(n==="className"||n==="UNSAFE_className")&&typeof u=="string"&&typeof i=="string"?t[n]=cd(u,i):n==="id"&&u&&i?t.id=Td(u,i):t[n]=i!==void 0?i:u}}return t}function Od(...e){return e.length===1&&e[0]?e[0]:t=>{let a=!1;const r=e.map(n=>{const u=bn(n,t);return a||(a=typeof u=="function"),u});if(a)return()=>{r.forEach((n,u)=>{typeof n=="function"?n():bn(e[u],null)})}}}function bn(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}const jd=new Set(["id"]),zd=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),Hd=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),_d=/^(data-.*)$/;function ue(e,t={}){let{labelable:a,isLink:r,propNames:n}=t,u={};for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&(jd.has(i)||a&&zd.has(i)||r&&Hd.has(i)||n?.has(i)||_d.test(i))&&(u[i]=e[i]);return u}function Ke(e){if(Kd())e.focus({preventScroll:!0});else{let t=Ud(e);e.focus(),Wd(t)}}let zt=null;function Kd(){if(zt==null){zt=!1;try{document.createElement("div").focus({get preventScroll(){return zt=!0,!0}})}catch{}}return zt}function Ud(e){let t=e.parentNode,a=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&a.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&a.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),a}function Wd(e){for(let{element:t,scrollTop:a,scrollLeft:r}of e)t.scrollTop=a,t.scrollLeft=r}function va(e){var t;return typeof window>"u"||window.navigator==null?!1:((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.brands.some(a=>e.test(a.brand)))||e.test(window.navigator.userAgent)}function Rr(e){var t;return typeof window<"u"&&window.navigator!=null?e.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function Ne(e){let t=null;return()=>(t==null&&(t=e()),t)}const lt=Ne(function(){return Rr(/^Mac/i)}),Zd=Ne(function(){return Rr(/^iPhone/i)}),xu=Ne(function(){return Rr(/^iPad/i)||lt()&&navigator.maxTouchPoints>1}),ha=Ne(function(){return Zd()||xu()}),Eu=Ne(function(){return va(/AppleWebKit/i)&&!Cu()}),Cu=Ne(function(){return va(/Chrome/i)}),kr=Ne(function(){return va(/Android/i)}),Gd=Ne(function(){return va(/Firefox/i)});function wt(e,t,a=!0){var r,n;let{metaKey:u,ctrlKey:i,altKey:l,shiftKey:o}=t;Gd()&&(!((n=window.event)===null||n===void 0||(r=n.type)===null||r===void 0)&&r.startsWith("key"))&&e.target==="_blank"&&(lt()?u=!0:i=!0);let s=Eu()&&lt()&&!xu()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:u,ctrlKey:i,altKey:l,shiftKey:o}):new MouseEvent("click",{metaKey:u,ctrlKey:i,altKey:l,shiftKey:o,bubbles:!0,cancelable:!0});wt.isOpening=a,Ke(e),e.dispatchEvent(s),wt.isOpening=!1}wt.isOpening=!1;let ke=new Map,Xa=new Set;function vn(){if(typeof window>"u")return;function e(r){return"propertyName"in r}let t=r=>{if(!e(r)||!r.target)return;let n=ke.get(r.target);n||(n=new Set,ke.set(r.target,n),r.target.addEventListener("transitioncancel",a,{once:!0})),n.add(r.propertyName)},a=r=>{if(!e(r)||!r.target)return;let n=ke.get(r.target);if(n&&(n.delete(r.propertyName),n.size===0&&(r.target.removeEventListener("transitioncancel",a),ke.delete(r.target)),ke.size===0)){for(let u of Xa)u();Xa.clear()}};document.body.addEventListener("transitionrun",t),document.body.addEventListener("transitionend",a)}typeof document<"u"&&(document.readyState!=="loading"?vn():document.addEventListener("DOMContentLoaded",vn));function Yd(){for(const[e]of ke)"isConnected"in e&&!e.isConnected&&ke.delete(e)}function wu(e){requestAnimationFrame(()=>{Yd(),ke.size===0?e():Xa.add(e)})}function pa(){let e=d.useRef(new Map),t=d.useCallback((n,u,i,l)=>{let o=l?.once?(...s)=>{e.current.delete(i),i(...s)}:i;e.current.set(i,{type:u,eventTarget:n,fn:o,options:l}),n.addEventListener(u,o,l)},[]),a=d.useCallback((n,u,i,l)=>{var o;let s=((o=e.current.get(i))===null||o===void 0?void 0:o.fn)||i;n.removeEventListener(u,s,l),e.current.delete(i)},[]),r=d.useCallback(()=>{e.current.forEach((n,u)=>{a(n.eventTarget,n.type,u,n.options)})},[a]);return d.useEffect(()=>r,[r]),{addGlobalListener:t,removeGlobalListener:a,removeAllGlobalListeners:r}}function Mt(e,t){let{id:a,"aria-label":r,"aria-labelledby":n}=e;return a=xe(a),n&&r?n=[...new Set([a,...n.trim().split(/\s+/)])].join(" "):n&&(n=n.trim().split(/\s+/).join(" ")),!r&&!n&&t&&(r=t),{id:a,"aria-label":r,"aria-labelledby":n}}function Pu(e){const t=d.useRef(null),a=d.useRef(void 0),r=d.useCallback(n=>{if(typeof e=="function"){const u=e,i=u(n);return()=>{typeof i=="function"?i():u(null)}}else if(e)return e.current=n,()=>{e.current=null}},[e]);return d.useMemo(()=>({get current(){return t.current},set current(n){t.current=n,a.current&&(a.current(),a.current=void 0),n!=null&&(a.current=r(n))}}),[r])}function hn(e,t){const a=d.useRef(!0),r=d.useRef(null);d.useEffect(()=>(a.current=!0,()=>{a.current=!1}),[]),d.useEffect(()=>{let n=r.current;a.current?a.current=!1:(!n||t.some((u,i)=>!Object.is(u,n[i])))&&e(),r.current=t},t)}function Jd(){return typeof window.ResizeObserver<"u"}function er(e){const{ref:t,box:a,onResize:r}=e;d.useEffect(()=>{let n=t?.current;if(n)if(Jd()){const u=new window.ResizeObserver(i=>{i.length&&r()});return u.observe(n,{box:a}),()=>{n&&u.unobserve(n)}}else return window.addEventListener("resize",r,!1),()=>{window.removeEventListener("resize",r,!1)}},[r,t,a])}function Bu(e,t){_(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}function tr(e,t){if(!e)return!1;let a=window.getComputedStyle(e),r=/(auto|scroll)/.test(a.overflow+a.overflowX+a.overflowY);return r&&t&&(r=e.scrollHeight!==e.clientHeight||e.scrollWidth!==e.clientWidth),r}function Da(e,t){let a=e;for(tr(a,t)&&(a=a.parentElement);a&&!tr(a,t);)a=a.parentElement;return a||document.scrollingElement||document.documentElement}function qd(e,t){const a=[];for(;e&&e!==document.documentElement;)tr(e,t)&&a.push(e),e=e.parentElement;return a}let Qd=0;const Ta=new Map;function Ar(e){let[t,a]=d.useState();return _(()=>{if(!e)return;let r=Ta.get(e);if(r)a(r.element.id);else{let n=`react-aria-description-${Qd++}`;a(n);let u=document.createElement("div");u.id=n,u.style.display="none",u.textContent=e,document.body.appendChild(u),r={refCount:0,element:u},Ta.set(e,r)}return r.refCount++,()=>{r&&--r.refCount===0&&(r.element.remove(),Ta.delete(e))}},[e]),{"aria-describedby":e?t:void 0}}function Ma(e,t,a,r){let n=te(a),u=a==null;d.useEffect(()=>{if(u||!e.current)return;let i=e.current;return i.addEventListener(t,n,r),()=>{i.removeEventListener(t,n,r)}},[e,t,r,u,n])}function Xd(e,t){let a=pn(e,t,"left"),r=pn(e,t,"top"),n=t.offsetWidth,u=t.offsetHeight,i=e.scrollLeft,l=e.scrollTop,{borderTopWidth:o,borderLeftWidth:s,scrollPaddingTop:f,scrollPaddingRight:c,scrollPaddingBottom:m,scrollPaddingLeft:b}=getComputedStyle(e),v=i+parseInt(s,10),h=l+parseInt(o,10),p=v+e.clientWidth,g=h+e.clientHeight,C=parseInt(f,10)||0,F=parseInt(m,10)||0,D=parseInt(c,10)||0,A=parseInt(b,10)||0;a<=i+A?i=a-parseInt(s,10)-A:a+n>p-D&&(i+=a+n-p+D),r<=h+C?l=r-parseInt(o,10)-C:r+u>g-F&&(l+=r+u-g+F),e.scrollLeft=i,e.scrollTop=l}function pn(e,t,a){const r=a==="left"?"offsetLeft":"offsetTop";let n=0;for(;t.offsetParent&&(n+=t[r],t.offsetParent!==e);){if(t.offsetParent.contains(e)){n-=e[r];break}t=t.offsetParent}return n}function Su(e,t){if(e&&document.contains(e)){let i=document.scrollingElement||document.documentElement;if(window.getComputedStyle(i).overflow==="hidden"){let o=qd(e);for(let s of o)Xd(s,e)}else{var a;let{left:o,top:s}=e.getBoundingClientRect();e==null||(a=e.scrollIntoView)===null||a===void 0||a.call(e,{block:"nearest"});let{left:f,top:c}=e.getBoundingClientRect();if(Math.abs(o-f)>1||Math.abs(s-c)>1){var r,n,u;t==null||(n=t.containingElement)===null||n===void 0||(r=n.scrollIntoView)===null||r===void 0||r.call(n,{block:"center",inline:"center"}),(u=e.scrollIntoView)===null||u===void 0||u.call(e,{block:"nearest"})}}}}function Fu(e){return e.mozInputSource===0&&e.isTrusted?!0:kr()&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function ec(e){return!kr()&&e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType==="mouse"}function Ru(e,t){let a=d.useRef(null);return e&&a.current&&t(e,a.current)&&(e=a.current),a.current=e,e}function tc(e,t,a){let r=d.useRef(t),n=te(()=>{a&&a(r.current)});d.useEffect(()=>{var u;let i=e==null||(u=e.current)===null||u===void 0?void 0:u.form;return i?.addEventListener("reset",n),()=>{i?.removeEventListener("reset",n)}},[e,n])}function ac(e,t=!0){let[a,r]=d.useState(!0),n=a&&t;return _(()=>{if(n&&e.current&&"getAnimations"in e.current)for(let u of e.current.getAnimations())u instanceof CSSTransition&&u.cancel()},[e,n]),ku(e,n,d.useCallback(()=>r(!1),[])),n}function rc(e,t){let[a,r]=d.useState(t?"open":"closed");switch(a){case"open":t||r("exiting");break;case"closed":case"exiting":t&&r("open");break}let n=a==="exiting";return ku(e,n,d.useCallback(()=>{r(u=>u==="exiting"?"closed":u)},[])),n}function ku(e,t,a){_(()=>{if(t&&e.current){if(!("getAnimations"in e.current)){a();return}let r=e.current.getAnimations();if(r.length===0){a();return}let n=!1;return Promise.all(r.map(u=>u.finished)).then(()=>{n||vd.flushSync(()=>{a()})}).catch(()=>{}),()=>{n=!0}}},[e,t,a])}const Tr=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],nc=Tr.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";Tr.push('[tabindex]:not([tabindex="-1"]):not([disabled])');const uc=Tr.join(':not([hidden]):not([tabindex="-1"]),');function Au(e){return e.matches(nc)}function ic(e){return e.matches(uc)}function Pt(e,t,a){let[r,n]=d.useState(e||t),u=d.useRef(e!==void 0),i=e!==void 0;d.useEffect(()=>{u.current,u.current=i},[i]);let l=i?e:r,o=d.useCallback((s,...f)=>{let c=(m,...b)=>{a&&(Object.is(l,m)||a(m,...b)),i||(l=m)};typeof s=="function"?n((b,...v)=>{let h=s(i?l:b,...v);return c(h,...f),i?b:h}):(i||n(s),c(s,...f))},[i,l,a]);return[l,o]}function ar(e,t=-1/0,a=1/0){return Math.min(Math.max(e,t),a)}const rr=Symbol("default");function ya({values:e,children:t}){for(let[a,r]of e)t=S.createElement(a.Provider,{value:r},t);return t}function Se(e){let{className:t,style:a,children:r,defaultClassName:n,defaultChildren:u,defaultStyle:i,values:l}=e;return d.useMemo(()=>{let o,s,f;return typeof t=="function"?o=t({...l,defaultClassName:n}):o=t,typeof a=="function"?s=a({...l,defaultStyle:i||{}}):s=a,typeof r=="function"?f=r({...l,defaultChildren:u}):r==null?f=u:f=r,{className:o??n,style:s||i?{...i,...s}:void 0,children:f??u,"data-rac":""}},[t,a,r,n,u,i,l])}function Mr(e,t){return a=>t(typeof e=="function"?e(a):e,a)}function aa(e,t){let a=d.useContext(e);if(t===null)return null;if(a&&typeof a=="object"&&"slots"in a&&a.slots){let r=t||rr;if(!a.slots[r]){let n=new Intl.ListFormat().format(Object.keys(a.slots).map(i=>`"${i}"`)),u=t?`Invalid slot "${t}".`:"A slot prop is required.";throw new Error(`${u} Valid slot names are ${n}.`)}return a.slots[r]}return a}function Fe(e,t,a){let r=aa(a,e.slot)||{},{ref:n,...u}=r,i=Pu(d.useMemo(()=>Od(t,n),[t,n])),l=Z(u,e);return"style"in u&&u.style&&"style"in e&&e.style&&(typeof u.style=="function"||typeof e.style=="function"?l.style=o=>{let s=typeof u.style=="function"?u.style(o):u.style,f={...o.defaultStyle,...s},c=typeof e.style=="function"?e.style({...o,defaultStyle:f}):e.style;return{...f,...c}}:l.style={...u.style,...e.style}),[l,i]}function lc(e=!0){let[t,a]=d.useState(e),r=d.useRef(!1),n=d.useCallback(u=>{r.current=!0,a(!!u)},[]);return _(()=>{r.current||a(!1)},[]),[n,t]}function oc(e){const t=/^(data-.*)$/;let a={};for(const r in e)t.test(r)||(a[r]=e[r]);return a}if(typeof HTMLTemplateElement<"u"){const e=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild").get;Object.defineProperty(HTMLTemplateElement.prototype,"firstChild",{configurable:!0,enumerable:!0,get:function(){return this.dataset.reactAriaHidden?this.content.firstChild:e.call(this)}})}const Tu=d.createContext(!1);function Mu(e){let t=(a,r)=>d.useContext(Tu)?null:e(a,r);return t.displayName=e.displayName||e.name,d.forwardRef(t)}function sc(){return d.useContext(Tu)}function Nr(e){let t=e;return t.nativeEvent=e,t.isDefaultPrevented=()=>t.defaultPrevented,t.isPropagationStopped=()=>t.cancelBubble,t.persist=()=>{},t}function Nu(e,t){Object.defineProperty(e,"target",{value:t}),Object.defineProperty(e,"currentTarget",{value:t})}function Iu(e){let t=d.useRef({isFocused:!1,observer:null});_(()=>{const r=t.current;return()=>{r.observer&&(r.observer.disconnect(),r.observer=null)}},[]);let a=te(r=>{e?.(r)});return d.useCallback(r=>{if(r.target instanceof HTMLButtonElement||r.target instanceof HTMLInputElement||r.target instanceof HTMLTextAreaElement||r.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=r.target,u=i=>{if(t.current.isFocused=!1,n.disabled){let l=Nr(i);a(l)}t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)};n.addEventListener("focusout",u,{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var i;(i=t.current.observer)===null||i===void 0||i.disconnect();let l=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:l})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:l}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[a])}let ra=!1;function dc(e){for(;e&&!Au(e);)e=e.parentElement;let t=ve(e),a=t.document.activeElement;if(!a||a===e)return;ra=!0;let r=!1,n=f=>{(f.target===a||r)&&f.stopImmediatePropagation()},u=f=>{(f.target===a||r)&&(f.stopImmediatePropagation(),!e&&!r&&(r=!0,Ke(a),o()))},i=f=>{(f.target===e||r)&&f.stopImmediatePropagation()},l=f=>{(f.target===e||r)&&(f.stopImmediatePropagation(),r||(r=!0,Ke(a),o()))};t.addEventListener("blur",n,!0),t.addEventListener("focusout",u,!0),t.addEventListener("focusin",l,!0),t.addEventListener("focus",i,!0);let o=()=>{cancelAnimationFrame(s),t.removeEventListener("blur",n,!0),t.removeEventListener("focusout",u,!0),t.removeEventListener("focusin",l,!0),t.removeEventListener("focus",i,!0),ra=!1,r=!1},s=requestAnimationFrame(o);return o}let Xe="default",nr="",Yt=new WeakMap;function cc(e){if(ha()){if(Xe==="default"){const t=K(e);nr=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}Xe="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";Yt.set(e,e.style[t]),e.style[t]="none"}}function Dn(e){if(ha()){if(Xe!=="disabled")return;Xe="restoring",setTimeout(()=>{wu(()=>{if(Xe==="restoring"){const t=K(e);t.documentElement.style.webkitUserSelect==="none"&&(t.documentElement.style.webkitUserSelect=nr||""),nr="",Xe="default"}})},300)}else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&Yt.has(e)){let t=Yt.get(e),a="userSelect"in e.style?"userSelect":"webkitUserSelect";e.style[a]==="none"&&(e.style[a]=t),e.getAttribute("style")===""&&e.removeAttribute("style"),Yt.delete(e)}}const Ir=S.createContext({register:()=>{}});Ir.displayName="PressResponderContext";function fc(e,t){return t.get?t.get.call(e):t.value}function Vu(e,t,a){if(!t.has(e))throw new TypeError("attempted to "+a+" private field on non-instance");return t.get(e)}function mc(e,t){var a=Vu(e,t,"get");return fc(e,a)}function $c(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Nt(e,t,a){$c(e,t),t.set(e,a)}function bc(e,t,a){if(t.set)t.set.call(e,a);else{if(!t.writable)throw new TypeError("attempted to set read only private field");t.value=a}}function yn(e,t,a){var r=Vu(e,t,"set");return bc(e,r,a),a}function vc(e){let t=d.useContext(Ir);if(t){let{register:a,...r}=t;e=Z(r,e),a()}return Bu(t,e.ref),e}var Ht=new WeakMap;class _t{continuePropagation(){yn(this,Ht,!1)}get shouldStopPropagation(){return mc(this,Ht)}constructor(t,a,r,n){Nt(this,Ht,{writable:!0,value:void 0}),yn(this,Ht,!0);var u;let i=(u=n?.target)!==null&&u!==void 0?u:r.currentTarget;const l=i?.getBoundingClientRect();let o,s=0,f,c=null;r.clientX!=null&&r.clientY!=null&&(f=r.clientX,c=r.clientY),l&&(f!=null&&c!=null?(o=f-l.left,s=c-l.top):(o=l.width/2,s=l.height/2)),this.type=t,this.pointerType=a,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=o,this.y=s}}const gn=Symbol("linkClicked"),xn="react-aria-pressable-style",En="data-react-aria-pressable";function Vr(e){let{onPress:t,onPressChange:a,onPressStart:r,onPressEnd:n,onPressUp:u,onClick:i,isDisabled:l,isPressed:o,preventFocusOnPress:s,shouldCancelOnPointerExit:f,allowTextSelectionOnPress:c,ref:m,...b}=vc(e),[v,h]=d.useState(!1),p=d.useRef({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:g,removeAllGlobalListeners:C}=pa(),F=te(($,x)=>{let w=p.current;if(l||w.didFirePressStart)return!1;let E=!0;if(w.isTriggeringEvent=!0,r){let z=new _t("pressstart",x,$);r(z),E=z.shouldStopPropagation}return a&&a(!0),w.isTriggeringEvent=!1,w.didFirePressStart=!0,h(!0),E}),D=te(($,x,w=!0)=>{let E=p.current;if(!E.didFirePressStart)return!1;E.didFirePressStart=!1,E.isTriggeringEvent=!0;let z=!0;if(n){let P=new _t("pressend",x,$);n(P),z=P.shouldStopPropagation}if(a&&a(!1),h(!1),t&&w&&!l){let P=new _t("press",x,$);t(P),z&&(z=P.shouldStopPropagation)}return E.isTriggeringEvent=!1,z}),A=te(($,x)=>{let w=p.current;if(l)return!1;if(u){w.isTriggeringEvent=!0;let E=new _t("pressup",x,$);return u(E),w.isTriggeringEvent=!1,E.shouldStopPropagation}return!0}),k=te($=>{let x=p.current;if(x.isPressed&&x.target){x.didFirePressStart&&x.pointerType!=null&&D(Je(x.target,$),x.pointerType,!1),x.isPressed=!1,x.isOverTarget=!1,x.activePointerId=null,x.pointerType=null,C(),c||Dn(x.target);for(let w of x.disposables)w();x.disposables=[]}}),V=te($=>{f&&k($)}),N=te($=>{i?.($)}),L=te(($,x)=>{if(i){let w=new MouseEvent("click",$);Nu(w,x),i(Nr(w))}}),y=d.useMemo(()=>{let $=p.current,x={onKeyDown(E){if(Na(E.nativeEvent,E.currentTarget)&&oe(E.currentTarget,J(E.nativeEvent))){var z;Cn(J(E.nativeEvent),E.key)&&E.preventDefault();let P=!0;if(!$.isPressed&&!E.repeat){$.target=E.currentTarget,$.isPressed=!0,$.pointerType="keyboard",P=F(E,"keyboard");let I=E.currentTarget,O=H=>{Na(H,I)&&!H.repeat&&oe(I,J(H))&&$.target&&A(Je($.target,H),"keyboard")};g(K(E.currentTarget),"keyup",Ct(O,w),!0)}P&&E.stopPropagation(),E.metaKey&&lt()&&((z=$.metaKeyEvents)===null||z===void 0||z.set(E.key,E.nativeEvent))}else E.key==="Meta"&&($.metaKeyEvents=new Map)},onClick(E){if(!(E&&!oe(E.currentTarget,J(E.nativeEvent)))&&E&&E.button===0&&!$.isTriggeringEvent&&!wt.isOpening){let z=!0;if(l&&E.preventDefault(),!$.ignoreEmulatedMouseEvents&&!$.isPressed&&($.pointerType==="virtual"||Fu(E.nativeEvent))){let P=F(E,"virtual"),I=A(E,"virtual"),O=D(E,"virtual");N(E),z=P&&I&&O}else if($.isPressed&&$.pointerType!=="keyboard"){let P=$.pointerType||E.nativeEvent.pointerType||"virtual";z=D(Je(E.currentTarget,E),P,!0),$.isOverTarget=!1,N(E),k(E)}$.ignoreEmulatedMouseEvents=!1,z&&E.stopPropagation()}}},w=E=>{var z;if($.isPressed&&$.target&&Na(E,$.target)){var P;Cn(J(E),E.key)&&E.preventDefault();let O=J(E),H=oe($.target,J(E));D(Je($.target,E),"keyboard",H),H&&L(E,$.target),C(),E.key!=="Enter"&&Lr($.target)&&oe($.target,O)&&!E[gn]&&(E[gn]=!0,wt($.target,E,!1)),$.isPressed=!1,(P=$.metaKeyEvents)===null||P===void 0||P.delete(E.key)}else if(E.key==="Meta"&&(!((z=$.metaKeyEvents)===null||z===void 0)&&z.size)){var I;let O=$.metaKeyEvents;$.metaKeyEvents=void 0;for(let H of O.values())(I=$.target)===null||I===void 0||I.dispatchEvent(new KeyboardEvent("keyup",H))}};if(typeof PointerEvent<"u"){x.onPointerDown=P=>{if(P.button!==0||!oe(P.currentTarget,J(P.nativeEvent)))return;if(ec(P.nativeEvent)){$.pointerType="virtual";return}$.pointerType=P.pointerType;let I=!0;if(!$.isPressed){$.isPressed=!0,$.isOverTarget=!0,$.activePointerId=P.pointerId,$.target=P.currentTarget,c||cc($.target),I=F(P,$.pointerType);let O=J(P.nativeEvent);"releasePointerCapture"in O&&O.releasePointerCapture(P.pointerId),g(K(P.currentTarget),"pointerup",E,!1),g(K(P.currentTarget),"pointercancel",z,!1)}I&&P.stopPropagation()},x.onMouseDown=P=>{if(oe(P.currentTarget,J(P.nativeEvent))&&P.button===0){if(s){let I=dc(P.target);I&&$.disposables.push(I)}P.stopPropagation()}},x.onPointerUp=P=>{!oe(P.currentTarget,J(P.nativeEvent))||$.pointerType==="virtual"||P.button===0&&A(P,$.pointerType||P.pointerType)},x.onPointerEnter=P=>{P.pointerId===$.activePointerId&&$.target&&!$.isOverTarget&&$.pointerType!=null&&($.isOverTarget=!0,F(Je($.target,P),$.pointerType))},x.onPointerLeave=P=>{P.pointerId===$.activePointerId&&$.target&&$.isOverTarget&&$.pointerType!=null&&($.isOverTarget=!1,D(Je($.target,P),$.pointerType,!1),V(P))};let E=P=>{if(P.pointerId===$.activePointerId&&$.isPressed&&P.button===0&&$.target){if(oe($.target,J(P))&&$.pointerType!=null){let I=!1,O=setTimeout(()=>{$.isPressed&&$.target instanceof HTMLElement&&(I?k(P):(Ke($.target),$.target.click()))},80);g(P.currentTarget,"click",()=>I=!0,!0),$.disposables.push(()=>clearTimeout(O))}else k(P);$.isOverTarget=!1}},z=P=>{k(P)};x.onDragStart=P=>{oe(P.currentTarget,J(P.nativeEvent))&&k(P)}}return x},[g,l,s,C,c,k,V,D,F,A,N,L]);return d.useEffect(()=>{if(!m)return;const $=K(m.current);if(!$||!$.head||$.getElementById(xn))return;const x=$.createElement("style");x.id=xn,x.textContent=`
@layer {
  [${En}] {
    touch-action: pan-x pan-y pinch-zoom;
  }
}
    `.trim(),$.head.prepend(x)},[m]),d.useEffect(()=>{let $=p.current;return()=>{var x;c||Dn((x=$.target)!==null&&x!==void 0?x:void 0);for(let w of $.disposables)w();$.disposables=[]}},[c]),{isPressed:o||v,pressProps:Z(b,y,{[En]:!0})}}function Lr(e){return e.tagName==="A"&&e.hasAttribute("href")}function Na(e,t){const{key:a,code:r}=e,n=t,u=n.getAttribute("role");return(a==="Enter"||a===" "||a==="Spacebar"||r==="Space")&&!(n instanceof ve(n).HTMLInputElement&&!Lu(n,a)||n instanceof ve(n).HTMLTextAreaElement||n.isContentEditable)&&!((u==="link"||!u&&Lr(n))&&a!=="Enter")}function Je(e,t){let a=t.clientX,r=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:a,clientY:r}}function hc(e){return e instanceof HTMLInputElement?!1:e instanceof HTMLButtonElement?e.type!=="submit"&&e.type!=="reset":!Lr(e)}function Cn(e,t){return e instanceof HTMLInputElement?!Lu(e,t):hc(e)}const pc=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Lu(e,t){return e.type==="checkbox"||e.type==="radio"?t===" ":pc.has(e.type)}let Ge=null,ur=new Set,gt=new Map,Ue=!1,ir=!1;const Dc={Tab:!0,Escape:!0};function ga(e,t){for(let a of ur)a(e,t)}function yc(e){return!(e.metaKey||!lt()&&e.altKey||e.ctrlKey||e.key==="Control"||e.key==="Shift"||e.key==="Meta")}function na(e){Ue=!0,yc(e)&&(Ge="keyboard",ga("keyboard",e))}function tt(e){Ge="pointer",(e.type==="mousedown"||e.type==="pointerdown")&&(Ue=!0,ga("pointer",e))}function Ou(e){Fu(e)&&(Ue=!0,Ge="virtual")}function ju(e){e.target===window||e.target===document||ra||!e.isTrusted||(!Ue&&!ir&&(Ge="virtual",ga("virtual",e)),Ue=!1,ir=!1)}function zu(){ra||(Ue=!1,ir=!0)}function lr(e){if(typeof window>"u"||typeof document>"u"||gt.get(ve(e)))return;const t=ve(e),a=K(e);let r=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){Ue=!0,r.apply(this,arguments)},a.addEventListener("keydown",na,!0),a.addEventListener("keyup",na,!0),a.addEventListener("click",Ou,!0),t.addEventListener("focus",ju,!0),t.addEventListener("blur",zu,!1),typeof PointerEvent<"u"&&(a.addEventListener("pointerdown",tt,!0),a.addEventListener("pointermove",tt,!0),a.addEventListener("pointerup",tt,!0)),t.addEventListener("beforeunload",()=>{Hu(e)},{once:!0}),gt.set(t,{focus:r})}const Hu=(e,t)=>{const a=ve(e),r=K(e);t&&r.removeEventListener("DOMContentLoaded",t),gt.has(a)&&(a.HTMLElement.prototype.focus=gt.get(a).focus,r.removeEventListener("keydown",na,!0),r.removeEventListener("keyup",na,!0),r.removeEventListener("click",Ou,!0),a.removeEventListener("focus",ju,!0),a.removeEventListener("blur",zu,!1),typeof PointerEvent<"u"&&(r.removeEventListener("pointerdown",tt,!0),r.removeEventListener("pointermove",tt,!0),r.removeEventListener("pointerup",tt,!0)),gt.delete(a))};function gc(e){const t=K(e);let a;return t.readyState!=="loading"?lr(e):(a=()=>{lr(e)},t.addEventListener("DOMContentLoaded",a)),()=>Hu(e,a)}typeof document<"u"&&gc();function _u(){return Ge!=="pointer"}function Or(){return Ge}function xc(e){Ge=e,ga(e,null)}const Ec=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Cc(e,t,a){let r=K(a?.target);const n=typeof window<"u"?ve(a?.target).HTMLInputElement:HTMLInputElement,u=typeof window<"u"?ve(a?.target).HTMLTextAreaElement:HTMLTextAreaElement,i=typeof window<"u"?ve(a?.target).HTMLElement:HTMLElement,l=typeof window<"u"?ve(a?.target).KeyboardEvent:KeyboardEvent;return e=e||r.activeElement instanceof n&&!Ec.has(r.activeElement.type)||r.activeElement instanceof u||r.activeElement instanceof i&&r.activeElement.isContentEditable,!(e&&t==="keyboard"&&a instanceof l&&!Dc[a.key])}function wc(e,t,a){lr(),d.useEffect(()=>{let r=(n,u)=>{Cc(!!a?.isTextInput,n,u)&&e(_u())};return ur.add(r),()=>{ur.delete(r)}},t)}function Bt(e){const t=K(e),a=ne(t);if(Or()==="virtual"){let r=a;wu(()=>{ne(t)===r&&e.isConnected&&Ke(e)})}else Ke(e)}function Ku(e){let{isDisabled:t,onFocus:a,onBlur:r,onFocusChange:n}=e;const u=d.useCallback(o=>{if(o.target===o.currentTarget)return r&&r(o),n&&n(!1),!0},[r,n]),i=Iu(u),l=d.useCallback(o=>{const s=K(o.target),f=s?ne(s):ne();o.target===o.currentTarget&&f===J(o.nativeEvent)&&(a&&a(o),n&&n(!0),i(o))},[n,a,i]);return{focusProps:{onFocus:!t&&(a||n||r)?l:void 0,onBlur:!t&&(r||n)?u:void 0}}}function wn(e){if(!e)return;let t=!0;return a=>{let r={...a,preventDefault(){a.preventDefault()},isDefaultPrevented(){return a.isDefaultPrevented()},stopPropagation(){t=!0},continuePropagation(){t=!1},isPropagationStopped(){return t}};e(r),t&&a.stopPropagation()}}function Pc(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:wn(e.onKeyDown),onKeyUp:wn(e.onKeyUp)}}}let Bc=S.createContext(null);function Sc(e){let t=d.useContext(Bc)||{};Bu(t,e);let{ref:a,...r}=t;return r}function Fc(e,t){let{focusProps:a}=Ku(e),{keyboardProps:r}=Pc(e),n=Z(a,r),u=Sc(t),i=e.isDisabled?{}:u,l=d.useRef(e.autoFocus);d.useEffect(()=>{l.current&&t.current&&Bt(t.current),l.current=!1},[t]);let o=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(o=void 0),{focusableProps:Z({...n,tabIndex:o},i)}}function Rc({children:e}){let t=d.useMemo(()=>({register:()=>{}}),[]);return S.createElement(Ir.Provider,{value:t},e)}function It(e){let{isDisabled:t,onBlurWithin:a,onFocusWithin:r,onFocusWithinChange:n}=e,u=d.useRef({isFocusWithin:!1}),{addGlobalListener:i,removeAllGlobalListeners:l}=pa(),o=d.useCallback(c=>{c.currentTarget.contains(c.target)&&u.current.isFocusWithin&&!c.currentTarget.contains(c.relatedTarget)&&(u.current.isFocusWithin=!1,l(),a&&a(c),n&&n(!1))},[a,n,u,l]),s=Iu(o),f=d.useCallback(c=>{if(!c.currentTarget.contains(c.target))return;const m=K(c.target),b=ne(m);if(!u.current.isFocusWithin&&b===J(c.nativeEvent)){r&&r(c),n&&n(!0),u.current.isFocusWithin=!0,s(c);let v=c.currentTarget;i(m,"focus",h=>{if(u.current.isFocusWithin&&!oe(v,h.target)){let p=new m.defaultView.FocusEvent("blur",{relatedTarget:h.target});Nu(p,v);let g=Nr(p);o(g)}},{capture:!0})}},[r,n,s,i,o]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:f,onBlur:o}}}let or=!1,Ia=0;function kc(){or=!0,setTimeout(()=>{or=!1},50)}function Pn(e){e.pointerType==="touch"&&kc()}function Ac(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",Pn),Ia++,()=>{Ia--,!(Ia>0)&&typeof PointerEvent<"u"&&document.removeEventListener("pointerup",Pn)}}function Vt(e){let{onHoverStart:t,onHoverChange:a,onHoverEnd:r,isDisabled:n}=e,[u,i]=d.useState(!1),l=d.useRef({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;d.useEffect(Ac,[]);let{addGlobalListener:o,removeAllGlobalListeners:s}=pa(),{hoverProps:f,triggerHoverEnd:c}=d.useMemo(()=>{let m=(h,p)=>{if(l.pointerType=p,n||p==="touch"||l.isHovered||!h.currentTarget.contains(h.target))return;l.isHovered=!0;let g=h.currentTarget;l.target=g,o(K(h.target),"pointerover",C=>{l.isHovered&&l.target&&!oe(l.target,C.target)&&b(C,C.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:g,pointerType:p}),a&&a(!0),i(!0)},b=(h,p)=>{let g=l.target;l.pointerType="",l.target=null,!(p==="touch"||!l.isHovered||!g)&&(l.isHovered=!1,s(),r&&r({type:"hoverend",target:g,pointerType:p}),a&&a(!1),i(!1))},v={};return typeof PointerEvent<"u"&&(v.onPointerEnter=h=>{or&&h.pointerType==="mouse"||m(h,h.pointerType)},v.onPointerLeave=h=>{!n&&h.currentTarget.contains(h.target)&&b(h,h.pointerType)}),{hoverProps:v,triggerHoverEnd:b}},[t,a,r,n,l,o,s]);return d.useEffect(()=>{n&&c({currentTarget:l.target},l.pointerType)},[n]),{hoverProps:f,isHovered:u}}function Tc(e){let{ref:t,onInteractOutside:a,isDisabled:r,onInteractOutsideStart:n}=e,u=d.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),i=te(o=>{a&&Bn(o,t)&&(n&&n(o),u.current.isPointerDown=!0)}),l=te(o=>{a&&a(o)});d.useEffect(()=>{let o=u.current;if(r)return;const s=t.current,f=K(s);if(typeof PointerEvent<"u"){let c=m=>{o.isPointerDown&&Bn(m,t)&&l(m),o.isPointerDown=!1};return f.addEventListener("pointerdown",i,!0),f.addEventListener("click",c,!0),()=>{f.removeEventListener("pointerdown",i,!0),f.removeEventListener("click",c,!0)}}},[t,r,i,l])}function Bn(e,t){if(e.button>0)return!1;if(e.target){const a=e.target.ownerDocument;if(!a||!a.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current?!e.composedPath().includes(t.current):!1}const Mc=new Set(["Arab","Syrc","Samr","Mand","Thaa","Mend","Nkoo","Adlm","Rohg","Hebr"]),Nc=new Set(["ae","ar","arc","bcc","bqi","ckb","dv","fa","glk","he","ku","mzn","nqo","pnb","ps","sd","ug","ur","yi"]);function Ic(e){if(Intl.Locale){let a=new Intl.Locale(e).maximize(),r=typeof a.getTextInfo=="function"?a.getTextInfo():a.textInfo;if(r)return r.direction==="rtl";if(a.script)return Mc.has(a.script)}let t=e.split("-")[0];return Nc.has(t)}const Vc=Symbol.for("react-aria.i18n.locale");function Uu(){let e=typeof window<"u"&&window[Vc]||typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US";try{Intl.DateTimeFormat.supportedLocalesOf([e])}catch{e="en-US"}return{locale:e,direction:Ic(e)?"rtl":"ltr"}}let sr=Uu(),pt=new Set;function Sn(){sr=Uu();for(let e of pt)e(sr)}function Lc(){let e=Fr(),[t,a]=d.useState(sr);return d.useEffect(()=>(pt.size===0&&window.addEventListener("languagechange",Sn),pt.add(a),()=>{pt.delete(a),pt.size===0&&window.removeEventListener("languagechange",Sn)}),[]),e?{locale:"en-US",direction:"ltr"}:t}const Oc=S.createContext(null);function ce(){let e=Lc();return d.useContext(Oc)||e}const jc=Symbol.for("react-aria.i18n.locale"),zc=Symbol.for("react-aria.i18n.strings");let qe;class Ye{getStringForLocale(t,a){let n=this.getStringsForLocale(a)[t];if(!n)throw new Error(`Could not find intl message ${t} in ${a} locale`);return n}getStringsForLocale(t){let a=this.strings[t];return a||(a=Hc(t,this.strings,this.defaultLocale),this.strings[t]=a),a}static getGlobalDictionaryForPackage(t){if(typeof window>"u")return null;let a=window[jc];if(qe===void 0){let n=window[zc];if(!n)return null;qe={};for(let u in n)qe[u]=new Ye({[a]:n[u]},a)}let r=qe?.[t];if(!r)throw new Error(`Strings for package "${t}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return r}constructor(t,a="en-US"){this.strings=Object.fromEntries(Object.entries(t).filter(([,r])=>r)),this.defaultLocale=a}}function Hc(e,t,a="en-US"){if(t[e])return t[e];let r=_c(e);if(t[r])return t[r];for(let n in t)if(n.startsWith(r+"-"))return t[n];return t[a]}function _c(e){return Intl.Locale?new Intl.Locale(e).language:e.split("-")[0]}const Fn=new Map,Rn=new Map;class Wu{format(t,a){let r=this.strings.getStringForLocale(t,this.locale);return typeof r=="function"?r(a,this):r}plural(t,a,r="cardinal"){let n=a["="+t];if(n)return typeof n=="function"?n():n;let u=this.locale+":"+r,i=Fn.get(u);i||(i=new Intl.PluralRules(this.locale,{type:r}),Fn.set(u,i));let l=i.select(t);return n=a[l]||a.other,typeof n=="function"?n():n}number(t){let a=Rn.get(this.locale);return a||(a=new Intl.NumberFormat(this.locale),Rn.set(this.locale,a)),a.format(t)}select(t,a){let r=t[a]||t.other;return typeof r=="function"?r():r}constructor(t,a){this.locale=t,this.strings=a}}const kn=new WeakMap;function Kc(e){let t=kn.get(e);return t||(t=new Ye(e),kn.set(e,t)),t}function Zu(e,t){return t&&Ye.getGlobalDictionaryForPackage(t)||Kc(e)}function Ie(e,t){let{locale:a}=ce(),r=Zu(e,t);return d.useMemo(()=>new Wu(a,r),[a,r])}function at(e,t){return e-t*Math.floor(e/t)}const Gu=1721426;function je(e,t,a,r){t=Lt(e,t);let n=t-1,u=-2;return a<=2?u=0:Ae(t)&&(u=-1),Gu-1+365*n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400)+Math.floor((367*a-362)/12+u+r)}function Ae(e){return e%4===0&&(e%100!==0||e%400===0)}function Lt(e,t){return e==="BC"?1-t:t}function xa(e){let t="AD";return e<=0&&(t="BC",e=1-e),[t,e]}const Uc={standard:[31,28,31,30,31,30,31,31,30,31,30,31],leapyear:[31,29,31,30,31,30,31,31,30,31,30,31]};class fe{fromJulianDay(t){let a=t,r=a-Gu,n=Math.floor(r/146097),u=at(r,146097),i=Math.floor(u/36524),l=at(u,36524),o=Math.floor(l/1461),s=at(l,1461),f=Math.floor(s/365),c=n*400+i*100+o*4+f+(i!==4&&f!==4?1:0),[m,b]=xa(c),v=a-je(m,b,1,1),h=2;a<je(m,b,3,1)?h=0:Ae(b)&&(h=1);let p=Math.floor(((v+h)*12+373)/367),g=a-je(m,b,p,1)+1;return new ee(m,b,p,g)}toJulianDay(t){return je(t.era,t.year,t.month,t.day)}getDaysInMonth(t){return Uc[Ae(t.year)?"leapyear":"standard"][t.month-1]}getMonthsInYear(t){return 12}getDaysInYear(t){return Ae(t.year)?366:365}getYearsInEra(t){return 9999}getEras(){return["BC","AD"]}isInverseEra(t){return t.era==="BC"}balanceDate(t){t.year<=0&&(t.era=t.era==="BC"?"AD":"BC",t.year=1-t.year)}constructor(){this.identifier="gregory"}}const Wc={"001":1,AD:1,AE:6,AF:6,AI:1,AL:1,AM:1,AN:1,AR:1,AT:1,AU:1,AX:1,AZ:1,BA:1,BE:1,BG:1,BH:6,BM:1,BN:1,BY:1,CH:1,CL:1,CM:1,CN:1,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DZ:6,EC:1,EE:1,EG:6,ES:1,FI:1,FJ:1,FO:1,FR:1,GB:1,GE:1,GF:1,GP:1,GR:1,HR:1,HU:1,IE:1,IQ:6,IR:6,IS:1,IT:1,JO:6,KG:1,KW:6,KZ:1,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MK:1,MN:1,MQ:1,MV:5,MY:1,NL:1,NO:1,NZ:1,OM:6,PL:1,QA:6,RE:1,RO:1,RS:1,RU:1,SD:6,SE:1,SI:1,SK:1,SM:1,SY:6,TJ:1,TM:1,TR:1,UA:1,UY:1,UZ:1,VA:1,VN:1,XK:1};function q(e,t){return t=G(t,e.calendar),e.era===t.era&&e.year===t.year&&e.month===t.month&&e.day===t.day}function Zc(e,t){return t=G(t,e.calendar),e=We(e),t=We(t),e.era===t.era&&e.year===t.year&&e.month===t.month}function Gc(e,t){return Ea(e.calendar,t.calendar)&&q(e,t)}function Ea(e,t){var a,r,n,u;return(u=(n=(a=e.isEqual)===null||a===void 0?void 0:a.call(e,t))!==null&&n!==void 0?n:(r=t.isEqual)===null||r===void 0?void 0:r.call(t,e))!==null&&u!==void 0?u:e.identifier===t.identifier}function Yc(e,t){return q(e,Ca(t))}const Jc={sun:0,mon:1,tue:2,wed:3,thu:4,fri:5,sat:6};function jr(e,t,a){let r=e.calendar.toJulianDay(e),n=a?Jc[a]:af(t),u=Math.ceil(r+1-n)%7;return u<0&&(u+=7),u}function Yu(e){return we(Date.now(),e)}function Ca(e){return Ee(Yu(e))}function Ju(e,t){return e.calendar.toJulianDay(e)-t.calendar.toJulianDay(t)}function qu(e,t){return An(e)-An(t)}function An(e){return e.hour*36e5+e.minute*6e4+e.second*1e3+e.millisecond}let Va=null;function wa(){return Va==null&&(Va=new Intl.DateTimeFormat().resolvedOptions().timeZone),Va}function We(e){return e.subtract({days:e.day-1})}function ua(e){return e.add({days:e.calendar.getDaysInMonth(e)-e.day})}function qc(e){return We(e.subtract({months:e.month-1}))}function Qc(e){return e.calendar.getMinimumMonthInYear?e.calendar.getMinimumMonthInYear(e):1}function Xc(e){return e.calendar.getMinimumDayInMonth?e.calendar.getMinimumDayInMonth(e):1}function St(e,t,a){let r=jr(e,t,a);return e.subtract({days:r})}function ef(e,t,a){return St(e,t,a).add({days:6})}const Tn=new Map;function tf(e){if(Intl.Locale){let a=Tn.get(e);return a||(a=new Intl.Locale(e).maximize().region,a&&Tn.set(e,a)),a}let t=e.split("-")[1];return t==="u"?void 0:t}function af(e){let t=tf(e);return t&&Wc[t]||0}function rf(e,t,a){let r=e.calendar.getDaysInMonth(e);return Math.ceil((jr(We(e),t,a)+r)/7)}function Qu(e,t){return e&&t?e.compare(t)<=0?e:t:e||t}function Xu(e,t){return e&&t?e.compare(t)>=0?e:t:e||t}function ot(e){e=G(e,new fe);let t=Lt(e.era,e.year);return ei(t,e.month,e.day,e.hour,e.minute,e.second,e.millisecond)}function ei(e,t,a,r,n,u,i){let l=new Date;return l.setUTCHours(r,n,u,i),l.setUTCFullYear(e,t-1,a),l.getTime()}function dr(e,t){if(t==="UTC")return 0;if(e>0&&t===wa())return new Date(e).getTimezoneOffset()*-6e4;let{year:a,month:r,day:n,hour:u,minute:i,second:l}=ti(e,t);return ei(a,r,n,u,i,l,0)-Math.floor(e/1e3)*1e3}const Mn=new Map;function ti(e,t){let a=Mn.get(t);a||(a=new Intl.DateTimeFormat("en-US",{timeZone:t,hour12:!1,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}),Mn.set(t,a));let r=a.formatToParts(new Date(e)),n={};for(let u of r)u.type!=="literal"&&(n[u.type]=u.value);return{year:n.era==="BC"||n.era==="B"?-n.year+1:+n.year,month:+n.month,day:+n.day,hour:n.hour==="24"?0:+n.hour,minute:+n.minute,second:+n.second}}const Nn=864e5;function nf(e,t,a,r){return(a===r?[a]:[a,r]).filter(u=>uf(e,t,u))}function uf(e,t,a){let r=ti(a,t);return e.year===r.year&&e.month===r.month&&e.day===r.day&&e.hour===r.hour&&e.minute===r.minute&&e.second===r.second}function Ce(e,t,a="compatible"){let r=Me(e);if(t==="UTC")return ot(r);if(t===wa()&&a==="compatible"){r=G(r,new fe);let o=new Date,s=Lt(r.era,r.year);return o.setFullYear(s,r.month-1,r.day),o.setHours(r.hour,r.minute,r.second,r.millisecond),o.getTime()}let n=ot(r),u=dr(n-Nn,t),i=dr(n+Nn,t),l=nf(r,t,n-u,n-i);if(l.length===1)return l[0];if(l.length>1)switch(a){case"compatible":case"earlier":return l[0];case"later":return l[l.length-1];case"reject":throw new RangeError("Multiple possible absolute times found")}switch(a){case"earlier":return Math.min(n-u,n-i);case"compatible":case"later":return Math.max(n-u,n-i);case"reject":throw new RangeError("No such absolute time found")}}function ai(e,t,a="compatible"){return new Date(Ce(e,t,a))}function we(e,t){let a=dr(e,t),r=new Date(e+a),n=r.getUTCFullYear(),u=r.getUTCMonth()+1,i=r.getUTCDate(),l=r.getUTCHours(),o=r.getUTCMinutes(),s=r.getUTCSeconds(),f=r.getUTCMilliseconds();return new Rt(n<1?"BC":"AD",n<1?-n+1:n,u,i,t,a,l,o,s,f)}function Ee(e){return new ee(e.calendar,e.era,e.year,e.month,e.day)}function Me(e,t){let a=0,r=0,n=0,u=0;if("timeZone"in e)({hour:a,minute:r,second:n,millisecond:u}=e);else if("hour"in e&&!t)return e;return t&&({hour:a,minute:r,second:n,millisecond:u}=t),new ia(e.calendar,e.era,e.year,e.month,e.day,a,r,n,u)}function G(e,t){if(Ea(e.calendar,t))return e;let a=t.fromJulianDay(e.calendar.toJulianDay(e)),r=e.copy();return r.calendar=t,r.era=a.era,r.year=a.year,r.month=a.month,r.day=a.day,Ze(r),r}function lf(e,t,a){if(e instanceof Rt)return e.timeZone===t?e:sf(e,t);let r=Ce(e,t,a);return we(r,t)}function of(e){let t=ot(e)-e.offset;return new Date(t)}function sf(e,t){let a=ot(e)-e.offset;return G(we(a,t),e.calendar)}const ct=36e5;function Pa(e,t){let a=e.copy(),r="hour"in a?ii(a,t):0;cr(a,t.years||0),a.calendar.balanceYearMonth&&a.calendar.balanceYearMonth(a,e),a.month+=t.months||0,fr(a),ri(a),a.day+=(t.weeks||0)*7,a.day+=t.days||0,a.day+=r,df(a),a.calendar.balanceDate&&a.calendar.balanceDate(a),a.year<1&&(a.year=1,a.month=1,a.day=1);let n=a.calendar.getYearsInEra(a);if(a.year>n){var u,i;let o=(u=(i=a.calendar).isInverseEra)===null||u===void 0?void 0:u.call(i,a);a.year=n,a.month=o?1:a.calendar.getMonthsInYear(a),a.day=o?1:a.calendar.getDaysInMonth(a)}a.month<1&&(a.month=1,a.day=1);let l=a.calendar.getMonthsInYear(a);return a.month>l&&(a.month=l,a.day=a.calendar.getDaysInMonth(a)),a.day=Math.max(1,Math.min(a.calendar.getDaysInMonth(a),a.day)),a}function cr(e,t){var a,r;!((a=(r=e.calendar).isInverseEra)===null||a===void 0)&&a.call(r,e)&&(t=-t),e.year+=t}function fr(e){for(;e.month<1;)cr(e,-1),e.month+=e.calendar.getMonthsInYear(e);let t=0;for(;e.month>(t=e.calendar.getMonthsInYear(e));)e.month-=t,cr(e,1)}function df(e){for(;e.day<1;)e.month--,fr(e),e.day+=e.calendar.getDaysInMonth(e);for(;e.day>e.calendar.getDaysInMonth(e);)e.day-=e.calendar.getDaysInMonth(e),e.month++,fr(e)}function ri(e){e.month=Math.max(1,Math.min(e.calendar.getMonthsInYear(e),e.month)),e.day=Math.max(1,Math.min(e.calendar.getDaysInMonth(e),e.day))}function Ze(e){e.calendar.constrainDate&&e.calendar.constrainDate(e),e.year=Math.max(1,Math.min(e.calendar.getYearsInEra(e),e.year)),ri(e)}function zr(e){let t={};for(let a in e)typeof e[a]=="number"&&(t[a]=-e[a]);return t}function ni(e,t){return Pa(e,zr(t))}function Hr(e,t){let a=e.copy();return t.era!=null&&(a.era=t.era),t.year!=null&&(a.year=t.year),t.month!=null&&(a.month=t.month),t.day!=null&&(a.day=t.day),Ze(a),a}function Ft(e,t){let a=e.copy();return t.hour!=null&&(a.hour=t.hour),t.minute!=null&&(a.minute=t.minute),t.second!=null&&(a.second=t.second),t.millisecond!=null&&(a.millisecond=t.millisecond),ui(a),a}function cf(e){e.second+=Math.floor(e.millisecond/1e3),e.millisecond=Kt(e.millisecond,1e3),e.minute+=Math.floor(e.second/60),e.second=Kt(e.second,60),e.hour+=Math.floor(e.minute/60),e.minute=Kt(e.minute,60);let t=Math.floor(e.hour/24);return e.hour=Kt(e.hour,24),t}function ui(e){e.millisecond=Math.max(0,Math.min(e.millisecond,1e3)),e.second=Math.max(0,Math.min(e.second,59)),e.minute=Math.max(0,Math.min(e.minute,59)),e.hour=Math.max(0,Math.min(e.hour,23))}function Kt(e,t){let a=e%t;return a<0&&(a+=t),a}function ii(e,t){return e.hour+=t.hours||0,e.minute+=t.minutes||0,e.second+=t.seconds||0,e.millisecond+=t.milliseconds||0,cf(e)}function li(e,t){let a=e.copy();return ii(a,t),a}function ff(e,t){return li(e,zr(t))}function _r(e,t,a,r){let n=e.copy();switch(t){case"era":{let l=e.calendar.getEras(),o=l.indexOf(e.era);if(o<0)throw new Error("Invalid era: "+e.era);o=Pe(o,a,0,l.length-1,r?.round),n.era=l[o],Ze(n);break}case"year":var u,i;!((u=(i=n.calendar).isInverseEra)===null||u===void 0)&&u.call(i,n)&&(a=-a),n.year=Pe(e.year,a,-1/0,9999,r?.round),n.year===-1/0&&(n.year=1),n.calendar.balanceYearMonth&&n.calendar.balanceYearMonth(n,e);break;case"month":n.month=Pe(e.month,a,1,e.calendar.getMonthsInYear(e),r?.round);break;case"day":n.day=Pe(e.day,a,1,e.calendar.getDaysInMonth(e),r?.round);break;default:throw new Error("Unsupported field "+t)}return e.calendar.balanceDate&&e.calendar.balanceDate(n),Ze(n),n}function Kr(e,t,a,r){let n=e.copy();switch(t){case"hour":{let u=e.hour,i=0,l=23;if(r?.hourCycle===12){let o=u>=12;i=o?12:0,l=o?23:11}n.hour=Pe(u,a,i,l,r?.round);break}case"minute":n.minute=Pe(e.minute,a,0,59,r?.round);break;case"second":n.second=Pe(e.second,a,0,59,r?.round);break;case"millisecond":n.millisecond=Pe(e.millisecond,a,0,999,r?.round);break;default:throw new Error("Unsupported field "+t)}return n}function Pe(e,t,a,r,n=!1){if(n){e+=Math.sign(t),e<a&&(e=r);let u=Math.abs(t);t>0?e=Math.ceil(e/u)*u:e=Math.floor(e/u)*u,e>r&&(e=a)}else e+=t,e<a?e=r-(a-e-1):e>r&&(e=a+(e-r-1));return e}function oi(e,t){let a;if(t.years!=null&&t.years!==0||t.months!=null&&t.months!==0||t.weeks!=null&&t.weeks!==0||t.days!=null&&t.days!==0){let n=Pa(Me(e),{years:t.years,months:t.months,weeks:t.weeks,days:t.days});a=Ce(n,e.timeZone)}else a=ot(e)-e.offset;a+=t.milliseconds||0,a+=(t.seconds||0)*1e3,a+=(t.minutes||0)*6e4,a+=(t.hours||0)*36e5;let r=we(a,e.timeZone);return G(r,e.calendar)}function mf(e,t){return oi(e,zr(t))}function $f(e,t,a,r){switch(t){case"hour":{let n=0,u=23;if(r?.hourCycle===12){let v=e.hour>=12;n=v?12:0,u=v?23:11}let i=Me(e),l=G(Ft(i,{hour:n}),new fe),o=[Ce(l,e.timeZone,"earlier"),Ce(l,e.timeZone,"later")].filter(v=>we(v,e.timeZone).day===l.day)[0],s=G(Ft(i,{hour:u}),new fe),f=[Ce(s,e.timeZone,"earlier"),Ce(s,e.timeZone,"later")].filter(v=>we(v,e.timeZone).day===s.day).pop(),c=ot(e)-e.offset,m=Math.floor(c/ct),b=c%ct;return c=Pe(m,a,Math.floor(o/ct),Math.floor(f/ct),r?.round)*ct+b,G(we(c,e.timeZone),e.calendar)}case"minute":case"second":case"millisecond":return Kr(e,t,a,r);case"era":case"year":case"month":case"day":{let n=_r(Me(e),t,a,r),u=Ce(n,e.timeZone);return G(we(u,e.timeZone),e.calendar)}default:throw new Error("Unsupported field "+t)}}function bf(e,t,a){let r=Me(e),n=Ft(Hr(r,t),t);if(n.compare(r)===0)return e;let u=Ce(n,e.timeZone,a);return G(we(u,e.timeZone),e.calendar)}function si(e){return`${String(e.hour).padStart(2,"0")}:${String(e.minute).padStart(2,"0")}:${String(e.second).padStart(2,"0")}${e.millisecond?String(e.millisecond/1e3).slice(1):""}`}function di(e){let t=G(e,new fe),a;return t.era==="BC"?a=t.year===1?"0000":"-"+String(Math.abs(1-t.year)).padStart(6,"00"):a=String(t.year).padStart(4,"0"),`${a}-${String(t.month).padStart(2,"0")}-${String(t.day).padStart(2,"0")}`}function ci(e){return`${di(e)}T${si(e)}`}function vf(e){let t=Math.sign(e)<0?"-":"+";e=Math.abs(e);let a=Math.floor(e/36e5),r=e%36e5/6e4;return`${t}${String(a).padStart(2,"0")}:${String(r).padStart(2,"0")}`}function hf(e){return`${ci(e)}${vf(e.offset)}[${e.timeZone}]`}function Ur(e){let t=typeof e[0]=="object"?e.shift():new fe,a;if(typeof e[0]=="string")a=e.shift();else{let i=t.getEras();a=i[i.length-1]}let r=e.shift(),n=e.shift(),u=e.shift();return[t,a,r,n,u]}var pf=new WeakMap;class ee{copy(){return this.era?new ee(this.calendar,this.era,this.year,this.month,this.day):new ee(this.calendar,this.year,this.month,this.day)}add(t){return Pa(this,t)}subtract(t){return ni(this,t)}set(t){return Hr(this,t)}cycle(t,a,r){return _r(this,t,a,r)}toDate(t){return ai(this,t)}toString(){return di(this)}compare(t){return Ju(this,t)}constructor(...t){Nt(this,pf,{writable:!0,value:void 0});let[a,r,n,u,i]=Ur(t);this.calendar=a,this.era=r,this.year=n,this.month=u,this.day=i,Ze(this)}}var Df=new WeakMap;class Wr{copy(){return new Wr(this.hour,this.minute,this.second,this.millisecond)}add(t){return li(this,t)}subtract(t){return ff(this,t)}set(t){return Ft(this,t)}cycle(t,a,r){return Kr(this,t,a,r)}toString(){return si(this)}compare(t){return qu(this,t)}constructor(t=0,a=0,r=0,n=0){Nt(this,Df,{writable:!0,value:void 0}),this.hour=t,this.minute=a,this.second=r,this.millisecond=n,ui(this)}}var yf=new WeakMap;class ia{copy(){return this.era?new ia(this.calendar,this.era,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond):new ia(this.calendar,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond)}add(t){return Pa(this,t)}subtract(t){return ni(this,t)}set(t){return Hr(Ft(this,t),t)}cycle(t,a,r){switch(t){case"era":case"year":case"month":case"day":return _r(this,t,a,r);default:return Kr(this,t,a,r)}}toDate(t,a){return ai(this,t,a)}toString(){return ci(this)}compare(t){let a=Ju(this,t);return a===0?qu(this,Me(t)):a}constructor(...t){Nt(this,yf,{writable:!0,value:void 0});let[a,r,n,u,i]=Ur(t);this.calendar=a,this.era=r,this.year=n,this.month=u,this.day=i,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,Ze(this)}}var gf=new WeakMap;class Rt{copy(){return this.era?new Rt(this.calendar,this.era,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond):new Rt(this.calendar,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond)}add(t){return oi(this,t)}subtract(t){return mf(this,t)}set(t,a){return bf(this,t,a)}cycle(t,a,r){return $f(this,t,a,r)}toDate(){return of(this)}toString(){return hf(this)}toAbsoluteString(){return this.toDate().toISOString()}compare(t){return this.toDate().getTime()-lf(t,this.timeZone).toDate().getTime()}constructor(...t){Nt(this,gf,{writable:!0,value:void 0});let[a,r,n,u,i]=Ur(t),l=t.shift(),o=t.shift();this.calendar=a,this.era=r,this.year=n,this.month=u,this.day=i,this.timeZone=l,this.offset=o,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,Ze(this)}}const rt=[[1868,9,8],[1912,7,30],[1926,12,25],[1989,1,8],[2019,5,1]],xf=[[1912,7,29],[1926,12,24],[1989,1,7],[2019,4,30]],Jt=[1867,1911,1925,1988,2018],Re=["meiji","taisho","showa","heisei","reiwa"];function In(e){const t=rt.findIndex(([a,r,n])=>e.year<a||e.year===a&&e.month<r||e.year===a&&e.month===r&&e.day<n);return t===-1?rt.length-1:t===0?0:t-1}function La(e){let t=Jt[Re.indexOf(e.era)];if(!t)throw new Error("Unknown era: "+e.era);return new ee(e.year+t,e.month,e.day)}class Ef extends fe{fromJulianDay(t){let a=super.fromJulianDay(t),r=In(a);return new ee(this,Re[r],a.year-Jt[r],a.month,a.day)}toJulianDay(t){return super.toJulianDay(La(t))}balanceDate(t){let a=La(t),r=In(a);Re[r]!==t.era&&(t.era=Re[r],t.year=a.year-Jt[r]),this.constrainDate(t)}constrainDate(t){let a=Re.indexOf(t.era),r=xf[a];if(r!=null){let[n,u,i]=r,l=n-Jt[a];t.year=Math.max(1,Math.min(l,t.year)),t.year===l&&(t.month=Math.min(u,t.month),t.month===u&&(t.day=Math.min(i,t.day)))}if(t.year===1&&a>=0){let[,n,u]=rt[a];t.month=Math.max(n,t.month),t.month===n&&(t.day=Math.max(u,t.day))}}getEras(){return Re}getYearsInEra(t){let a=Re.indexOf(t.era),r=rt[a],n=rt[a+1];if(n==null)return 9999-r[0]+1;let u=n[0]-r[0];return(t.month<n[1]||t.month===n[1]&&t.day<n[2])&&u++,u}getDaysInMonth(t){return super.getDaysInMonth(La(t))}getMinimumMonthInYear(t){let a=Vn(t);return a?a[1]:1}getMinimumDayInMonth(t){let a=Vn(t);return a&&t.month===a[1]?a[2]:1}constructor(...t){super(...t),this.identifier="japanese"}}function Vn(e){if(e.year===1){let t=Re.indexOf(e.era);return rt[t]}}const fi=-543;class Cf extends fe{fromJulianDay(t){let a=super.fromJulianDay(t),r=Lt(a.era,a.year);return new ee(this,r-fi,a.month,a.day)}toJulianDay(t){return super.toJulianDay(Ln(t))}getEras(){return["BE"]}getDaysInMonth(t){return super.getDaysInMonth(Ln(t))}balanceDate(){}constructor(...t){super(...t),this.identifier="buddhist"}}function Ln(e){let[t,a]=xa(e.year+fi);return new ee(t,a,e.month,e.day)}const la=1911;function mi(e){return e.era==="minguo"?e.year+la:1-e.year+la}function On(e){let t=e-la;return t>0?["minguo",t]:["before_minguo",1-t]}class wf extends fe{fromJulianDay(t){let a=super.fromJulianDay(t),r=Lt(a.era,a.year),[n,u]=On(r);return new ee(this,n,u,a.month,a.day)}toJulianDay(t){return super.toJulianDay(jn(t))}getEras(){return["before_minguo","minguo"]}balanceDate(t){let[a,r]=On(mi(t));t.era=a,t.year=r}isInverseEra(t){return t.era==="before_minguo"}getDaysInMonth(t){return super.getDaysInMonth(jn(t))}getYearsInEra(t){return t.era==="before_minguo"?9999:9999-la}constructor(...t){super(...t),this.identifier="roc"}}function jn(e){let[t,a]=xa(mi(e));return new ee(t,a,e.month,e.day)}const zn=1948320,Hn=[0,31,62,93,124,155,186,216,246,276,306,336];class Pf{fromJulianDay(t){let a=t-zn,r=1+Math.floor((33*a+3)/12053),n=365*(r-1)+Math.floor((8*r+21)/33),u=a-n,i=u<216?Math.floor(u/31):Math.floor((u-6)/30),l=u-Hn[i]+1;return new ee(this,r,i+1,l)}toJulianDay(t){let a=zn-1+365*(t.year-1)+Math.floor((8*t.year+21)/33);return a+=Hn[t.month-1],a+=t.day,a}getMonthsInYear(){return 12}getDaysInMonth(t){return t.month<=6?31:t.month<=11||at(25*t.year+11,33)<8?30:29}getEras(){return["AP"]}getYearsInEra(){return 9377}constructor(){this.identifier="persian"}}const Oa=78,_n=80;class Bf extends fe{fromJulianDay(t){let a=super.fromJulianDay(t),r=a.year-Oa,n=t-je(a.era,a.year,1,1),u;n<_n?(r--,u=Ae(a.year-1)?31:30,n+=u+155+90+10):(u=Ae(a.year)?31:30,n-=_n);let i,l;if(n<u)i=1,l=n+1;else{let o=n-u;o<155?(i=Math.floor(o/31)+2,l=o%31+1):(o-=155,i=Math.floor(o/30)+7,l=o%30+1)}return new ee(this,r,i,l)}toJulianDay(t){let a=t.year+Oa,[r,n]=xa(a),u,i;return Ae(n)?(u=31,i=je(r,n,3,21)):(u=30,i=je(r,n,3,22)),t.month===1?i+t.day-1:(i+=u+Math.min(t.month-2,5)*31,t.month>=8&&(i+=(t.month-7)*30),i+=t.day-1,i)}getDaysInMonth(t){return t.month===1&&Ae(t.year+Oa)||t.month>=2&&t.month<=6?31:30}getYearsInEra(){return 9919}getEras(){return["saka"]}balanceDate(){}constructor(...t){super(...t),this.identifier="indian"}}const oa=1948440,Kn=1948439,$e=1300,Qe=1600,Sf=460322;function sa(e,t,a,r){return r+Math.ceil(29.5*(a-1))+(t-1)*354+Math.floor((3+11*t)/30)+e-1}function $i(e,t,a){let r=Math.floor((30*(a-t)+10646)/10631),n=Math.min(12,Math.ceil((a-(29+sa(t,r,1,1)))/29.5)+1),u=a-sa(t,r,n,1)+1;return new ee(e,r,n,u)}function Un(e){return(14+11*e)%30<11}class Zr{fromJulianDay(t){return $i(this,oa,t)}toJulianDay(t){return sa(oa,t.year,t.month,t.day)}getDaysInMonth(t){let a=29+t.month%2;return t.month===12&&Un(t.year)&&a++,a}getMonthsInYear(){return 12}getDaysInYear(t){return Un(t.year)?355:354}getYearsInEra(){return 9665}getEras(){return["AH"]}constructor(){this.identifier="islamic-civil"}}class Ff extends Zr{fromJulianDay(t){return $i(this,Kn,t)}toJulianDay(t){return sa(Kn,t.year,t.month,t.day)}constructor(...t){super(...t),this.identifier="islamic-tbla"}}const Rf="qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=";let mr,nt;function qt(e){return Sf+nt[e-$e]}function Dt(e,t){let a=e-$e,r=1<<11-(t-1);return(mr[a]&r)===0?29:30}function Wn(e,t){let a=qt(e);for(let r=1;r<t;r++)a+=Dt(e,r);return a}function Zn(e){return nt[e+1-$e]-nt[e-$e]}class kf extends Zr{fromJulianDay(t){let a=t-oa,r=qt($e),n=qt(Qe);if(a<r||a>n)return super.fromJulianDay(t);{let u=$e-1,i=1,l=1;for(;l>0;){u++,l=a-qt(u)+1;let o=Zn(u);if(l===o){i=12;break}else if(l<o){let s=Dt(u,i);for(i=1;l>s;)l-=s,i++,s=Dt(u,i);break}}return new ee(this,u,i,a-Wn(u,i)+1)}}toJulianDay(t){return t.year<$e||t.year>Qe?super.toJulianDay(t):oa+Wn(t.year,t.month)+(t.day-1)}getDaysInMonth(t){return t.year<$e||t.year>Qe?super.getDaysInMonth(t):Dt(t.year,t.month)}getDaysInYear(t){return t.year<$e||t.year>Qe?super.getDaysInYear(t):Zn(t.year)}constructor(){if(super(),this.identifier="islamic-umalqura",mr||(mr=new Uint16Array(Uint8Array.from(atob(Rf),t=>t.charCodeAt(0)).buffer)),!nt){nt=new Uint32Array(Qe-$e+1);let t=0;for(let a=$e;a<=Qe;a++){nt[a-$e]=t;for(let r=1;r<=12;r++)t+=Dt(a,r)}}}}const Gn=347997,bi=1080,vi=24*bi,Af=29,Tf=12*bi+793,Mf=Af*vi+Tf;function Oe(e){return at(e*7+1,19)<7}function Qt(e){let t=Math.floor((235*e-234)/19),a=12084+13753*t,r=t*29+Math.floor(a/25920);return at(3*(r+1),7)<3&&(r+=1),r}function Nf(e){let t=Qt(e-1),a=Qt(e);return Qt(e+1)-a===356?2:a-t===382?1:0}function xt(e){return Qt(e)+Nf(e)}function hi(e){return xt(e+1)-xt(e)}function If(e){let t=hi(e);switch(t>380&&(t-=30),t){case 353:return 0;case 354:return 1;case 355:return 2}}function Ut(e,t){if(t>=6&&!Oe(e)&&t++,t===4||t===7||t===9||t===11||t===13)return 29;let a=If(e);return t===2?a===2?30:29:t===3?a===0?29:30:t===6?Oe(e)?30:0:30}class Vf{fromJulianDay(t){let a=t-Gn,r=a*vi/Mf,n=Math.floor((19*r+234)/235)+1,u=xt(n),i=Math.floor(a-u);for(;i<1;)n--,u=xt(n),i=Math.floor(a-u);let l=1,o=0;for(;o<i;)o+=Ut(n,l),l++;l--,o-=Ut(n,l);let s=i-o;return new ee(this,n,l,s)}toJulianDay(t){let a=xt(t.year);for(let r=1;r<t.month;r++)a+=Ut(t.year,r);return a+t.day+Gn}getDaysInMonth(t){return Ut(t.year,t.month)}getMonthsInYear(t){return Oe(t.year)?13:12}getDaysInYear(t){return hi(t.year)}getYearsInEra(){return 9999}getEras(){return["AM"]}balanceYearMonth(t,a){a.year!==t.year&&(Oe(a.year)&&!Oe(t.year)&&a.month>6?t.month--:!Oe(a.year)&&Oe(t.year)&&a.month>6&&t.month++)}constructor(){this.identifier="hebrew"}}const $r=1723856,Yn=1824665,br=5500;function da(e,t,a,r){return e+365*t+Math.floor(t/4)+30*(a-1)+r-1}function Gr(e,t){let a=Math.floor(4*(t-e)/1461),r=1+Math.floor((t-da(e,a,1,1))/30),n=t+1-da(e,a,r,1);return[a,r,n]}function pi(e){return Math.floor(e%4/3)}function Di(e,t){return t%13!==0?30:pi(e)+5}class Yr{fromJulianDay(t){let[a,r,n]=Gr($r,t),u="AM";return a<=0&&(u="AA",a+=br),new ee(this,u,a,r,n)}toJulianDay(t){let a=t.year;return t.era==="AA"&&(a-=br),da($r,a,t.month,t.day)}getDaysInMonth(t){return Di(t.year,t.month)}getMonthsInYear(){return 13}getDaysInYear(t){return 365+pi(t.year)}getYearsInEra(t){return t.era==="AA"?9999:9991}getEras(){return["AA","AM"]}constructor(){this.identifier="ethiopic"}}class Lf extends Yr{fromJulianDay(t){let[a,r,n]=Gr($r,t);return a+=br,new ee(this,"AA",a,r,n)}getEras(){return["AA"]}getYearsInEra(){return 9999}constructor(...t){super(...t),this.identifier="ethioaa"}}class Of extends Yr{fromJulianDay(t){let[a,r,n]=Gr(Yn,t),u="CE";return a<=0&&(u="BCE",a=1-a),new ee(this,u,a,r,n)}toJulianDay(t){let a=t.year;return t.era==="BCE"&&(a=1-a),da(Yn,a,t.month,t.day)}getDaysInMonth(t){let a=t.year;return t.era==="BCE"&&(a=1-a),Di(a,t.month)}isInverseEra(t){return t.era==="BCE"}balanceDate(t){t.year<=0&&(t.era=t.era==="BCE"?"CE":"BCE",t.year=1-t.year)}getEras(){return["BCE","CE"]}getYearsInEra(t){return t.era==="BCE"?9999:9715}constructor(...t){super(...t),this.identifier="coptic"}}function yi(e){switch(e){case"buddhist":return new Cf;case"ethiopic":return new Yr;case"ethioaa":return new Lf;case"coptic":return new Of;case"hebrew":return new Vf;case"indian":return new Bf;case"islamic-civil":return new Zr;case"islamic-tbla":return new Ff;case"islamic-umalqura":return new kf;case"japanese":return new Ef;case"persian":return new Pf;case"roc":return new wf;case"gregory":default:return new fe}}let ja=new Map;class Be{format(t){return this.formatter.format(t)}formatToParts(t){return this.formatter.formatToParts(t)}formatRange(t,a){if(typeof this.formatter.formatRange=="function")return this.formatter.formatRange(t,a);if(a<t)throw new RangeError("End date must be >= start date");return`${this.formatter.format(t)} – ${this.formatter.format(a)}`}formatRangeToParts(t,a){if(typeof this.formatter.formatRangeToParts=="function")return this.formatter.formatRangeToParts(t,a);if(a<t)throw new RangeError("End date must be >= start date");let r=this.formatter.formatToParts(t),n=this.formatter.formatToParts(a);return[...r.map(u=>({...u,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...n.map(u=>({...u,source:"endRange"}))]}resolvedOptions(){let t=this.formatter.resolvedOptions();return Hf()&&(this.resolvedHourCycle||(this.resolvedHourCycle=_f(t.locale,this.options)),t.hourCycle=this.resolvedHourCycle,t.hour12=this.resolvedHourCycle==="h11"||this.resolvedHourCycle==="h12"),t.calendar==="ethiopic-amete-alem"&&(t.calendar="ethioaa"),t}constructor(t,a={}){this.formatter=gi(t,a),this.options=a}}const jf={true:{ja:"h11"},false:{}};function gi(e,t={}){if(typeof t.hour12=="boolean"&&zf()){t={...t};let n=jf[String(t.hour12)][e.split("-")[0]],u=t.hour12?"h12":"h23";t.hourCycle=n??u,delete t.hour12}let a=e+(t?Object.entries(t).sort((n,u)=>n[0]<u[0]?-1:1).join():"");if(ja.has(a))return ja.get(a);let r=new Intl.DateTimeFormat(e,t);return ja.set(a,r),r}let za=null;function zf(){return za==null&&(za=new Intl.DateTimeFormat("en-US",{hour:"numeric",hour12:!1}).format(new Date(2020,2,3,0))==="24"),za}let Ha=null;function Hf(){return Ha==null&&(Ha=new Intl.DateTimeFormat("fr",{hour:"numeric",hour12:!1}).resolvedOptions().hourCycle==="h12"),Ha}function _f(e,t){if(!t.timeStyle&&!t.hour)return;e=e.replace(/(-u-)?-nu-[a-zA-Z0-9]+/,""),e+=(e.includes("-u-")?"":"-u")+"-nu-latn";let a=gi(e,{...t,timeZone:void 0}),r=parseInt(a.formatToParts(new Date(2020,2,3,0)).find(u=>u.type==="hour").value,10),n=parseInt(a.formatToParts(new Date(2020,2,3,23)).find(u=>u.type==="hour").value,10);if(r===0&&n===23)return"h23";if(r===24&&n===23)return"h24";if(r===0&&n===11)return"h11";if(r===12&&n===11)return"h12";throw new Error("Unexpected hour cycle result")}function ye(e){e=Ru(e??{},Kf);let{locale:t}=ce();return d.useMemo(()=>new Be(t,e),[t,e])}function Kf(e,t){if(e===t)return!0;let a=Object.keys(e),r=Object.keys(t);if(a.length!==r.length)return!1;for(let n of a)if(t[n]!==e[n])return!1;return!0}let _a=new Map,vr=!1;try{vr=new Intl.NumberFormat("de-DE",{signDisplay:"exceptZero"}).resolvedOptions().signDisplay==="exceptZero"}catch{}let ca=!1;try{ca=new Intl.NumberFormat("de-DE",{style:"unit",unit:"degree"}).resolvedOptions().style==="unit"}catch{}const xi={degree:{narrow:{default:"°","ja-JP":" 度","zh-TW":"度","sl-SI":" °"}}};class Uf{format(t){let a="";if(!vr&&this.options.signDisplay!=null?a=Zf(this.numberFormatter,this.options.signDisplay,t):a=this.numberFormatter.format(t),this.options.style==="unit"&&!ca){var r;let{unit:n,unitDisplay:u="short",locale:i}=this.resolvedOptions();if(!n)return a;let l=(r=xi[n])===null||r===void 0?void 0:r[u];a+=l[i]||l.default}return a}formatToParts(t){return this.numberFormatter.formatToParts(t)}formatRange(t,a){if(typeof this.numberFormatter.formatRange=="function")return this.numberFormatter.formatRange(t,a);if(a<t)throw new RangeError("End date must be >= start date");return`${this.format(t)} – ${this.format(a)}`}formatRangeToParts(t,a){if(typeof this.numberFormatter.formatRangeToParts=="function")return this.numberFormatter.formatRangeToParts(t,a);if(a<t)throw new RangeError("End date must be >= start date");let r=this.numberFormatter.formatToParts(t),n=this.numberFormatter.formatToParts(a);return[...r.map(u=>({...u,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...n.map(u=>({...u,source:"endRange"}))]}resolvedOptions(){let t=this.numberFormatter.resolvedOptions();return!vr&&this.options.signDisplay!=null&&(t={...t,signDisplay:this.options.signDisplay}),!ca&&this.options.style==="unit"&&(t={...t,style:"unit",unit:this.options.unit,unitDisplay:this.options.unitDisplay}),t}constructor(t,a={}){this.numberFormatter=Wf(t,a),this.options=a}}function Wf(e,t={}){let{numberingSystem:a}=t;if(a&&e.includes("-nu-")&&(e.includes("-u-")||(e+="-u-"),e+=`-nu-${a}`),t.style==="unit"&&!ca){var r;let{unit:i,unitDisplay:l="short"}=t;if(!i)throw new Error('unit option must be provided with style: "unit"');if(!(!((r=xi[i])===null||r===void 0)&&r[l]))throw new Error(`Unsupported unit ${i} with unitDisplay = ${l}`);t={...t,style:"decimal"}}let n=e+(t?Object.entries(t).sort((i,l)=>i[0]<l[0]?-1:1).join():"");if(_a.has(n))return _a.get(n);let u=new Intl.NumberFormat(e,t);return _a.set(n,u),u}function Zf(e,t,a){if(t==="auto")return e.format(a);if(t==="never")return e.format(Math.abs(a));{let r=!1;if(t==="always"?r=a>0||Object.is(a,0):t==="exceptZero"&&(Object.is(a,-0)||Object.is(a,0)?a=Math.abs(a):r=a>0),r){let n=e.format(-a),u=e.format(a),i=n.replace(u,"").replace(/\u200e|\u061C/,"");return[...i].length,n.replace(u,"!!!").replace(i,"+").replace("!!!",u)}else return e.format(a)}}const Gf=new RegExp("^.*\\(.*\\).*$"),Yf=["latn","arab","hanidec","deva","beng"];class Ei{parse(t){return Ka(this.locale,this.options,t).parse(t)}isValidPartialNumber(t,a,r){return Ka(this.locale,this.options,t).isValidPartialNumber(t,a,r)}getNumberingSystem(t){return Ka(this.locale,this.options,t).options.numberingSystem}constructor(t,a={}){this.locale=t,this.options=a}}const Jn=new Map;function Ka(e,t,a){let r=qn(e,t);if(!e.includes("-nu-")&&!r.isValidPartialNumber(a)){for(let n of Yf)if(n!==r.options.numberingSystem){let u=qn(e+(e.includes("-u-")?"-nu-":"-u-nu-")+n,t);if(u.isValidPartialNumber(a))return u}}return r}function qn(e,t){let a=e+(t?Object.entries(t).sort((n,u)=>n[0]<u[0]?-1:1).join():""),r=Jn.get(a);return r||(r=new Jf(e,t),Jn.set(a,r)),r}class Jf{parse(t){let a=this.sanitize(t);if(this.symbols.group&&(a=ft(a,this.symbols.group,"")),this.symbols.decimal&&(a=a.replace(this.symbols.decimal,".")),this.symbols.minusSign&&(a=a.replace(this.symbols.minusSign,"-")),a=a.replace(this.symbols.numeral,this.symbols.index),this.options.style==="percent"){let i=a.indexOf("-");a=a.replace("-",""),a=a.replace("+","");let l=a.indexOf(".");l===-1&&(l=a.length),a=a.replace(".",""),l-2===0?a=`0.${a}`:l-2===-1?a=`0.0${a}`:l-2===-2?a="0.00":a=`${a.slice(0,l-2)}.${a.slice(l-2)}`,i>-1&&(a=`-${a}`)}let r=a?+a:NaN;if(isNaN(r))return NaN;if(this.options.style==="percent"){var n,u;let i={...this.options,style:"decimal",minimumFractionDigits:Math.min(((n=this.options.minimumFractionDigits)!==null&&n!==void 0?n:0)+2,20),maximumFractionDigits:Math.min(((u=this.options.maximumFractionDigits)!==null&&u!==void 0?u:0)+2,20)};return new Ei(this.locale,i).parse(new Uf(this.locale,i).format(r))}return this.options.currencySign==="accounting"&&Gf.test(t)&&(r=-1*r),r}sanitize(t){return t=t.replace(this.symbols.literals,""),this.symbols.minusSign&&(t=t.replace("-",this.symbols.minusSign)),this.options.numberingSystem==="arab"&&(this.symbols.decimal&&(t=t.replace(",",this.symbols.decimal),t=t.replace("،",this.symbols.decimal)),this.symbols.group&&(t=ft(t,".",this.symbols.group))),this.options.locale==="fr-FR"&&this.symbols.group&&(t=ft(t," ",this.symbols.group),t=ft(t,/\u00A0/g,this.symbols.group)),t}isValidPartialNumber(t,a=-1/0,r=1/0){return t=this.sanitize(t),this.symbols.minusSign&&t.startsWith(this.symbols.minusSign)&&a<0?t=t.slice(this.symbols.minusSign.length):this.symbols.plusSign&&t.startsWith(this.symbols.plusSign)&&r>0&&(t=t.slice(this.symbols.plusSign.length)),this.symbols.group&&t.startsWith(this.symbols.group)||this.symbols.decimal&&t.indexOf(this.symbols.decimal)>-1&&this.options.maximumFractionDigits===0?!1:(this.symbols.group&&(t=ft(t,this.symbols.group,"")),t=t.replace(this.symbols.numeral,""),this.symbols.decimal&&(t=t.replace(this.symbols.decimal,"")),t.length===0)}constructor(t,a={}){this.locale=t,a.roundingIncrement!==1&&a.roundingIncrement!=null&&(a.maximumFractionDigits==null&&a.minimumFractionDigits==null?(a.maximumFractionDigits=0,a.minimumFractionDigits=0):a.maximumFractionDigits==null?a.maximumFractionDigits=a.minimumFractionDigits:a.minimumFractionDigits==null&&(a.minimumFractionDigits=a.maximumFractionDigits)),this.formatter=new Intl.NumberFormat(t,a),this.options=this.formatter.resolvedOptions(),this.symbols=Qf(t,this.formatter,this.options,a);var r,n;this.options.style==="percent"&&(((r=this.options.minimumFractionDigits)!==null&&r!==void 0?r:0)>18||((n=this.options.maximumFractionDigits)!==null&&n!==void 0?n:0)>18)}}const Qn=new Set(["decimal","fraction","integer","minusSign","plusSign","group"]),qf=[0,4,2,1,11,20,3,7,100,21,.1,1.1];function Qf(e,t,a,r){var n,u,i,l;let o=new Intl.NumberFormat(e,{...a,minimumSignificantDigits:1,maximumSignificantDigits:21,roundingIncrement:1,roundingPriority:"auto",roundingMode:"halfExpand"}),s=o.formatToParts(-10000.111),f=o.formatToParts(10000.111),c=qf.map(y=>o.formatToParts(y));var m;let b=(m=(n=s.find(y=>y.type==="minusSign"))===null||n===void 0?void 0:n.value)!==null&&m!==void 0?m:"-",v=(u=f.find(y=>y.type==="plusSign"))===null||u===void 0?void 0:u.value;!v&&(r?.signDisplay==="exceptZero"||r?.signDisplay==="always")&&(v="+");let p=(i=new Intl.NumberFormat(e,{...a,minimumFractionDigits:2,maximumFractionDigits:2}).formatToParts(.001).find(y=>y.type==="decimal"))===null||i===void 0?void 0:i.value,g=(l=s.find(y=>y.type==="group"))===null||l===void 0?void 0:l.value,C=s.filter(y=>!Qn.has(y.type)).map(y=>Xn(y.value)),F=c.flatMap(y=>y.filter($=>!Qn.has($.type)).map($=>Xn($.value))),D=[...new Set([...C,...F])].sort((y,$)=>$.length-y.length),A=D.length===0?new RegExp("[\\p{White_Space}]","gu"):new RegExp(`${D.join("|")}|[\\p{White_Space}]`,"gu"),k=[...new Intl.NumberFormat(a.locale,{useGrouping:!1}).format(9876543210)].reverse(),V=new Map(k.map((y,$)=>[y,$])),N=new RegExp(`[${k.join("")}]`,"g");return{minusSign:b,plusSign:v,decimal:p,group:g,literals:A,numeral:N,index:y=>String(V.get(y))}}function ft(e,t,a){return e.replaceAll?e.replaceAll(t,a):e.split(t).join(a)}function Xn(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}let Ua=new Map;function Xf(e){let{locale:t}=ce(),a=t+(e?Object.entries(e).sort((n,u)=>n[0]<u[0]?-1:1).join():"");if(Ua.has(a))return Ua.get(a);let r=new Intl.Collator(t,e);return Ua.set(a,r),r}function e4(e){let t=Xf({usage:"search",...e}),a=d.useCallback((u,i)=>i.length===0?!0:(u=u.normalize("NFC"),i=i.normalize("NFC"),t.compare(u.slice(0,i.length),i)===0),[t]),r=d.useCallback((u,i)=>i.length===0?!0:(u=u.normalize("NFC"),i=i.normalize("NFC"),t.compare(u.slice(-i.length),i)===0),[t]),n=d.useCallback((u,i)=>{if(i.length===0)return!0;u=u.normalize("NFC"),i=i.normalize("NFC");let l=0,o=i.length;for(;l+o<=u.length;l++){let s=u.slice(l,l+o);if(t.compare(i,s)===0)return!0}return!1},[t]);return d.useMemo(()=>({startsWith:a,endsWith:r,contains:n}),[a,r,n])}function t4(e,t){let{elementType:a="button",isDisabled:r,onPress:n,onPressStart:u,onPressEnd:i,onPressUp:l,onPressChange:o,preventFocusOnPress:s,allowFocusWhenDisabled:f,onClick:c,href:m,target:b,rel:v,type:h="button"}=e,p;a==="button"?p={type:h,disabled:r}:p={role:"button",href:a==="a"&&!r?m:void 0,target:a==="a"?b:void 0,type:a==="input"?h:void 0,disabled:a==="input"?r:void 0,"aria-disabled":!r||a==="input"?void 0:r,rel:a==="a"?v:void 0};let{pressProps:g,isPressed:C}=Vr({onPressStart:u,onPressEnd:i,onPressChange:o,onPress:n,onPressUp:l,onClick:c,isDisabled:r,preventFocusOnPress:s,ref:t}),{focusableProps:F}=Fc(e,t);f&&(F.tabIndex=r?-1:F.tabIndex);let D=Z(F,g,ue(e,{labelable:!0}));return{isPressed:C,buttonProps:Z(p,D,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],"aria-current":e["aria-current"]})}}function a4(e){const t=ve(e);if(!(e instanceof t.HTMLElement)&&!(e instanceof t.SVGElement))return!1;let{display:a,visibility:r}=e.style,n=a!=="none"&&r!=="hidden"&&r!=="collapse";if(n){const{getComputedStyle:u}=e.ownerDocument.defaultView;let{display:i,visibility:l}=u(e);n=i!=="none"&&l!=="hidden"&&l!=="collapse"}return n}function r4(e,t){return!e.hasAttribute("hidden")&&!e.hasAttribute("data-react-aria-prevent-focus")&&(e.nodeName==="DETAILS"&&t&&t.nodeName!=="SUMMARY"?e.hasAttribute("open"):!0)}function Ci(e,t){return e.nodeName!=="#comment"&&a4(e)&&r4(e,t)&&(!e.parentElement||Ci(e.parentElement,e))}const eu=S.createContext(null),hr="react-aria-focus-scope-restore";let W=null;function n4(e){let{children:t,contain:a,restoreFocus:r,autoFocus:n}=e,u=d.useRef(null),i=d.useRef(null),l=d.useRef([]),{parentNode:o}=d.useContext(eu)||{},s=d.useMemo(()=>new Dr({scopeRef:l}),[l]);_(()=>{let m=o||X.root;if(X.getTreeNode(m.scopeRef)&&W&&!fa(W,m.scopeRef)){let b=X.getTreeNode(W);b&&(m=b)}m.addChild(s),X.addNode(s)},[s,o]),_(()=>{let m=X.getTreeNode(l);m&&(m.contain=!!a)},[a]),_(()=>{var m;let b=(m=u.current)===null||m===void 0?void 0:m.nextSibling,v=[],h=p=>p.stopPropagation();for(;b&&b!==i.current;)v.push(b),b.addEventListener(hr,h),b=b.nextSibling;return l.current=v,()=>{for(let p of v)p.removeEventListener(hr,h)}},[t]),s4(l,r,a),i4(l,a),d4(l,r,a),o4(l,n),d.useEffect(()=>{const m=ne(K(l.current?l.current[0]:void 0));let b=null;if(be(m,l.current)){for(let v of X.traverse())v.scopeRef&&be(m,v.scopeRef.current)&&(b=v);b===X.getTreeNode(l)&&(W=b.scopeRef)}},[l]),_(()=>()=>{var m,b,v;let h=(v=(b=X.getTreeNode(l))===null||b===void 0||(m=b.parent)===null||m===void 0?void 0:m.scopeRef)!==null&&v!==void 0?v:null;(l===W||fa(l,W))&&(!h||X.getTreeNode(h))&&(W=h),X.removeTreeNode(l)},[l]);let f=d.useMemo(()=>u4(l),[]),c=d.useMemo(()=>({focusManager:f,parentNode:s}),[s,f]);return S.createElement(eu.Provider,{value:c},S.createElement("span",{"data-focus-scope-start":!0,hidden:!0,ref:u}),t,S.createElement("span",{"data-focus-scope-end":!0,hidden:!0,ref:i}))}function u4(e){return{focusNext(t={}){let a=e.current,{from:r,tabbable:n,wrap:u,accept:i}=t;var l;let o=r||ne(K((l=a[0])!==null&&l!==void 0?l:void 0)),s=a[0].previousElementSibling,f=ze(a),c=de(f,{tabbable:n,accept:i},a);c.currentNode=be(o,a)?o:s;let m=c.nextNode();return!m&&u&&(c.currentNode=s,m=c.nextNode()),m&&se(m,!0),m},focusPrevious(t={}){let a=e.current,{from:r,tabbable:n,wrap:u,accept:i}=t;var l;let o=r||ne(K((l=a[0])!==null&&l!==void 0?l:void 0)),s=a[a.length-1].nextElementSibling,f=ze(a),c=de(f,{tabbable:n,accept:i},a);c.currentNode=be(o,a)?o:s;let m=c.previousNode();return!m&&u&&(c.currentNode=s,m=c.previousNode()),m&&se(m,!0),m},focusFirst(t={}){let a=e.current,{tabbable:r,accept:n}=t,u=ze(a),i=de(u,{tabbable:r,accept:n},a);i.currentNode=a[0].previousElementSibling;let l=i.nextNode();return l&&se(l,!0),l},focusLast(t={}){let a=e.current,{tabbable:r,accept:n}=t,u=ze(a),i=de(u,{tabbable:r,accept:n},a);i.currentNode=a[a.length-1].nextElementSibling;let l=i.previousNode();return l&&se(l,!0),l}}}function ze(e){return e[0].parentElement}function yt(e){let t=X.getTreeNode(W);for(;t&&t.scopeRef!==e;){if(t.contain)return!1;t=t.parent}return!0}function i4(e,t){let a=d.useRef(void 0),r=d.useRef(void 0);_(()=>{let n=e.current;if(!t){r.current&&(cancelAnimationFrame(r.current),r.current=void 0);return}const u=K(n?n[0]:void 0);let i=s=>{if(s.key!=="Tab"||s.altKey||s.ctrlKey||s.metaKey||!yt(e)||s.isComposing)return;let f=ne(u),c=e.current;if(!c||!be(f,c))return;let m=ze(c),b=de(m,{tabbable:!0},c);if(!f)return;b.currentNode=f;let v=s.shiftKey?b.previousNode():b.nextNode();v||(b.currentNode=s.shiftKey?c[c.length-1].nextElementSibling:c[0].previousElementSibling,v=s.shiftKey?b.previousNode():b.nextNode()),s.preventDefault(),v&&se(v,!0)},l=s=>{(!W||fa(W,e))&&be(J(s),e.current)?(W=e,a.current=J(s)):yt(e)&&!Te(J(s),e)?a.current?a.current.focus():W&&W.current&&pr(W.current):yt(e)&&(a.current=J(s))},o=s=>{r.current&&cancelAnimationFrame(r.current),r.current=requestAnimationFrame(()=>{let f=Or(),c=(f==="virtual"||f===null)&&kr()&&Cu(),m=ne(u);if(!c&&m&&yt(e)&&!Te(m,e)){W=e;let v=J(s);if(v&&v.isConnected){var b;a.current=v,(b=a.current)===null||b===void 0||b.focus()}else W.current&&pr(W.current)}})};return u.addEventListener("keydown",i,!1),u.addEventListener("focusin",l,!1),n?.forEach(s=>s.addEventListener("focusin",l,!1)),n?.forEach(s=>s.addEventListener("focusout",o,!1)),()=>{u.removeEventListener("keydown",i,!1),u.removeEventListener("focusin",l,!1),n?.forEach(s=>s.removeEventListener("focusin",l,!1)),n?.forEach(s=>s.removeEventListener("focusout",o,!1))}},[e,t]),_(()=>()=>{r.current&&cancelAnimationFrame(r.current)},[r])}function wi(e){return Te(e)}function be(e,t){return!e||!t?!1:t.some(a=>a.contains(e))}function Te(e,t=null){if(e instanceof Element&&e.closest("[data-react-aria-top-layer]"))return!0;for(let{scopeRef:a}of X.traverse(X.getTreeNode(t)))if(a&&be(e,a.current))return!0;return!1}function l4(e){return Te(e,W)}function fa(e,t){var a;let r=(a=X.getTreeNode(t))===null||a===void 0?void 0:a.parent;for(;r;){if(r.scopeRef===e)return!0;r=r.parent}return!1}function se(e,t=!1){if(e!=null&&!t)try{Bt(e)}catch{}else if(e!=null)try{e.focus()}catch{}}function Pi(e,t=!0){let a=e[0].previousElementSibling,r=ze(e),n=de(r,{tabbable:t},e);n.currentNode=a;let u=n.nextNode();return t&&!u&&(r=ze(e),n=de(r,{tabbable:!1},e),n.currentNode=a,u=n.nextNode()),u}function pr(e,t=!0){se(Pi(e,t))}function o4(e,t){const a=S.useRef(t);d.useEffect(()=>{if(a.current){W=e;const r=K(e.current?e.current[0]:void 0);!be(ne(r),W.current)&&e.current&&pr(e.current)}a.current=!1},[e])}function s4(e,t,a){_(()=>{if(t||a)return;let r=e.current;const n=K(r?r[0]:void 0);let u=i=>{let l=J(i);be(l,e.current)?W=e:wi(l)||(W=null)};return n.addEventListener("focusin",u,!1),r?.forEach(i=>i.addEventListener("focusin",u,!1)),()=>{n.removeEventListener("focusin",u,!1),r?.forEach(i=>i.removeEventListener("focusin",u,!1))}},[e,t,a])}function tu(e){let t=X.getTreeNode(W);for(;t&&t.scopeRef!==e;){if(t.nodeToRestore)return!1;t=t.parent}return t?.scopeRef===e}function d4(e,t,a){const r=d.useRef(typeof document<"u"?ne(K(e.current?e.current[0]:void 0)):null);_(()=>{let n=e.current;const u=K(n?n[0]:void 0);if(!t||a)return;let i=()=>{(!W||fa(W,e))&&be(ne(u),e.current)&&(W=e)};return u.addEventListener("focusin",i,!1),n?.forEach(l=>l.addEventListener("focusin",i,!1)),()=>{u.removeEventListener("focusin",i,!1),n?.forEach(l=>l.removeEventListener("focusin",i,!1))}},[e,a]),_(()=>{const n=K(e.current?e.current[0]:void 0);if(!t)return;let u=i=>{if(i.key!=="Tab"||i.altKey||i.ctrlKey||i.metaKey||!yt(e)||i.isComposing)return;let l=n.activeElement;if(!Te(l,e)||!tu(e))return;let o=X.getTreeNode(e);if(!o)return;let s=o.nodeToRestore,f=de(n.body,{tabbable:!0});f.currentNode=l;let c=i.shiftKey?f.previousNode():f.nextNode();if((!s||!s.isConnected||s===n.body)&&(s=void 0,o.nodeToRestore=void 0),(!c||!Te(c,e))&&s){f.currentNode=s;do c=i.shiftKey?f.previousNode():f.nextNode();while(Te(c,e));i.preventDefault(),i.stopPropagation(),c?se(c,!0):wi(s)?se(s,!0):l.blur()}};return a||n.addEventListener("keydown",u,!0),()=>{a||n.removeEventListener("keydown",u,!0)}},[e,t,a]),_(()=>{const n=K(e.current?e.current[0]:void 0);if(!t)return;let u=X.getTreeNode(e);if(u){var i;return u.nodeToRestore=(i=r.current)!==null&&i!==void 0?i:void 0,()=>{let l=X.getTreeNode(e);if(!l)return;let o=l.nodeToRestore,s=ne(n);if(t&&o&&(s&&Te(s,e)||s===n.body&&tu(e))){let f=X.clone();requestAnimationFrame(()=>{if(n.activeElement===n.body){let c=f.getTreeNode(e);for(;c;){if(c.nodeToRestore&&c.nodeToRestore.isConnected){au(c.nodeToRestore);return}c=c.parent}for(c=f.getTreeNode(e);c;){if(c.scopeRef&&c.scopeRef.current&&X.getTreeNode(c.scopeRef)){let m=Pi(c.scopeRef.current,!0);au(m);return}c=c.parent}}})}}}},[e,t])}function au(e){e.dispatchEvent(new CustomEvent(hr,{bubbles:!0,cancelable:!0}))&&se(e)}function de(e,t,a){let r=t?.tabbable?ic:Au,n=e?.nodeType===Node.ELEMENT_NODE?e:null,u=K(n),i=Ld(u,e||u,NodeFilter.SHOW_ELEMENT,{acceptNode(l){var o;return!(t==null||(o=t.from)===null||o===void 0)&&o.contains(l)?NodeFilter.FILTER_REJECT:r(l)&&Ci(l)&&(!a||be(l,a))&&(!t?.accept||t.accept(l))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});return t?.from&&(i.currentNode=t.from),i}function Jr(e,t={}){return{focusNext(a={}){let r=e.current;if(!r)return null;let{from:n,tabbable:u=t.tabbable,wrap:i=t.wrap,accept:l=t.accept}=a,o=n||ne(K(r)),s=de(r,{tabbable:u,accept:l});r.contains(o)&&(s.currentNode=o);let f=s.nextNode();return!f&&i&&(s.currentNode=r,f=s.nextNode()),f&&se(f,!0),f},focusPrevious(a=t){let r=e.current;if(!r)return null;let{from:n,tabbable:u=t.tabbable,wrap:i=t.wrap,accept:l=t.accept}=a,o=n||ne(K(r)),s=de(r,{tabbable:u,accept:l});if(r.contains(o))s.currentNode=o;else{let c=Wa(s);return c&&se(c,!0),c??null}let f=s.previousNode();if(!f&&i){s.currentNode=r;let c=Wa(s);if(!c)return null;f=c}return f&&se(f,!0),f??null},focusFirst(a=t){let r=e.current;if(!r)return null;let{tabbable:n=t.tabbable,accept:u=t.accept}=a,l=de(r,{tabbable:n,accept:u}).nextNode();return l&&se(l,!0),l},focusLast(a=t){let r=e.current;if(!r)return null;let{tabbable:n=t.tabbable,accept:u=t.accept}=a,i=de(r,{tabbable:n,accept:u}),l=Wa(i);return l&&se(l,!0),l??null}}}function Wa(e){let t,a;do a=e.lastChild(),a&&(t=a);while(a);return t}class qr{get size(){return this.fastMap.size}getTreeNode(t){return this.fastMap.get(t)}addTreeNode(t,a,r){let n=this.fastMap.get(a??null);if(!n)return;let u=new Dr({scopeRef:t});n.addChild(u),u.parent=n,this.fastMap.set(t,u),r&&(u.nodeToRestore=r)}addNode(t){this.fastMap.set(t.scopeRef,t)}removeTreeNode(t){if(t===null)return;let a=this.fastMap.get(t);if(!a)return;let r=a.parent;for(let u of this.traverse())u!==a&&a.nodeToRestore&&u.nodeToRestore&&a.scopeRef&&a.scopeRef.current&&be(u.nodeToRestore,a.scopeRef.current)&&(u.nodeToRestore=a.nodeToRestore);let n=a.children;r&&(r.removeChild(a),n.size>0&&n.forEach(u=>r&&r.addChild(u))),this.fastMap.delete(a.scopeRef)}*traverse(t=this.root){if(t.scopeRef!=null&&(yield t),t.children.size>0)for(let a of t.children)yield*this.traverse(a)}clone(){var t;let a=new qr;var r;for(let n of this.traverse())a.addTreeNode(n.scopeRef,(r=(t=n.parent)===null||t===void 0?void 0:t.scopeRef)!==null&&r!==void 0?r:null,n.nodeToRestore);return a}constructor(){this.fastMap=new Map,this.root=new Dr({scopeRef:null}),this.fastMap.set(null,this.root)}}class Dr{addChild(t){this.children.add(t),t.parent=this}removeChild(t){this.children.delete(t),t.parent=void 0}constructor(t){this.children=new Set,this.contain=!1,this.scopeRef=t.scopeRef}}let X=new qr;function st(e={}){let{autoFocus:t=!1,isTextInput:a,within:r}=e,n=d.useRef({isFocused:!1,isFocusVisible:t||_u()}),[u,i]=d.useState(!1),[l,o]=d.useState(()=>n.current.isFocused&&n.current.isFocusVisible),s=d.useCallback(()=>o(n.current.isFocused&&n.current.isFocusVisible),[]),f=d.useCallback(b=>{n.current.isFocused=b,i(b),s()},[s]);wc(b=>{n.current.isFocusVisible=b,s()},[],{isTextInput:a});let{focusProps:c}=Ku({isDisabled:r,onFocusChange:f}),{focusWithinProps:m}=It({isDisabled:!r,onFocusWithinChange:f});return{isFocused:u,isFocusVisible:l,focusProps:r?m:c}}var Bi={};Bi={dateRange:e=>`${e.startDate} إلى ${e.endDate}`,dateSelected:e=>`${e.date} المحدد`,finishRangeSelectionPrompt:"انقر لإنهاء عملية تحديد نطاق التاريخ",maximumDate:"آخر تاريخ متاح",minimumDate:"أول تاريخ متاح",next:"التالي",previous:"السابق",selectedDateDescription:e=>`تاريخ محدد: ${e.date}`,selectedRangeDescription:e=>`المدى الزمني المحدد: ${e.dateRange}`,startRangeSelectionPrompt:"انقر لبدء عملية تحديد نطاق التاريخ",todayDate:e=>`اليوم، ${e.date}`,todayDateSelected:e=>`اليوم، ${e.date} محدد`};var Si={};Si={dateRange:e=>`${e.startDate} до ${e.endDate}`,dateSelected:e=>`Избрано е ${e.date}`,finishRangeSelectionPrompt:"Натиснете, за да довършите избора на времеви интервал",maximumDate:"Последна налична дата",minimumDate:"Първа налична дата",next:"Напред",previous:"Назад",selectedDateDescription:e=>`Избрана дата: ${e.date}`,selectedRangeDescription:e=>`Избран диапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Натиснете, за да пристъпите към избора на времеви интервал",todayDate:e=>`Днес, ${e.date}`,todayDateSelected:e=>`Днес, ${e.date} са избрани`};var Fi={};Fi={dateRange:e=>`${e.startDate} až ${e.endDate}`,dateSelected:e=>`Vybráno ${e.date}`,finishRangeSelectionPrompt:"Kliknutím dokončíte výběr rozsahu dat",maximumDate:"Poslední dostupné datum",minimumDate:"První dostupné datum",next:"Další",previous:"Předchozí",selectedDateDescription:e=>`Vybrané datum: ${e.date}`,selectedRangeDescription:e=>`Vybrané období: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknutím zahájíte výběr rozsahu dat",todayDate:e=>`Dnes, ${e.date}`,todayDateSelected:e=>`Dnes, vybráno ${e.date}`};var Ri={};Ri={dateRange:e=>`${e.startDate} til ${e.endDate}`,dateSelected:e=>`${e.date} valgt`,finishRangeSelectionPrompt:"Klik for at fuldføre valg af datoområde",maximumDate:"Sidste ledige dato",minimumDate:"Første ledige dato",next:"Næste",previous:"Forrige",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt interval: ${e.dateRange}`,startRangeSelectionPrompt:"Klik for at starte valg af datoområde",todayDate:e=>`I dag, ${e.date}`,todayDateSelected:e=>`I dag, ${e.date} valgt`};var ki={};ki={dateRange:e=>`${e.startDate} bis ${e.endDate}`,dateSelected:e=>`${e.date} ausgewählt`,finishRangeSelectionPrompt:"Klicken, um die Auswahl des Datumsbereichs zu beenden",maximumDate:"Letztes verfügbares Datum",minimumDate:"Erstes verfügbares Datum",next:"Weiter",previous:"Zurück",selectedDateDescription:e=>`Ausgewähltes Datum: ${e.date}`,selectedRangeDescription:e=>`Ausgewählter Bereich: ${e.dateRange}`,startRangeSelectionPrompt:"Klicken, um die Auswahl des Datumsbereichs zu beginnen",todayDate:e=>`Heute, ${e.date}`,todayDateSelected:e=>`Heute, ${e.date} ausgewählt`};var Ai={};Ai={dateRange:e=>`${e.startDate} έως ${e.endDate}`,dateSelected:e=>`Επιλέχθηκε ${e.date}`,finishRangeSelectionPrompt:"Κάντε κλικ για να ολοκληρώσετε την επιλογή εύρους ημερομηνιών",maximumDate:"Τελευταία διαθέσιμη ημερομηνία",minimumDate:"Πρώτη διαθέσιμη ημερομηνία",next:"Επόμενο",previous:"Προηγούμενο",selectedDateDescription:e=>`Επιλεγμένη ημερομηνία: ${e.date}`,selectedRangeDescription:e=>`Επιλεγμένο εύρος: ${e.dateRange}`,startRangeSelectionPrompt:"Κάντε κλικ για να ξεκινήσετε την επιλογή εύρους ημερομηνιών",todayDate:e=>`Σήμερα, ${e.date}`,todayDateSelected:e=>`Σήμερα, επιλέχτηκε ${e.date}`};var Ti={};Ti={previous:"Previous",next:"Next",selectedDateDescription:e=>`Selected Date: ${e.date}`,selectedRangeDescription:e=>`Selected Range: ${e.dateRange}`,todayDate:e=>`Today, ${e.date}`,todayDateSelected:e=>`Today, ${e.date} selected`,dateSelected:e=>`${e.date} selected`,startRangeSelectionPrompt:"Click to start selecting date range",finishRangeSelectionPrompt:"Click to finish selecting date range",minimumDate:"First available date",maximumDate:"Last available date",dateRange:e=>`${e.startDate} to ${e.endDate}`};var Mi={};Mi={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} seleccionado`,finishRangeSelectionPrompt:"Haga clic para terminar de seleccionar rango de fechas",maximumDate:"Última fecha disponible",minimumDate:"Primera fecha disponible",next:"Siguiente",previous:"Anterior",selectedDateDescription:e=>`Fecha seleccionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo seleccionado: ${e.dateRange}`,startRangeSelectionPrompt:"Haga clic para comenzar a seleccionar un rango de fechas",todayDate:e=>`Hoy, ${e.date}`,todayDateSelected:e=>`Hoy, ${e.date} seleccionado`};var Ni={};Ni={dateRange:e=>`${e.startDate} kuni ${e.endDate}`,dateSelected:e=>`${e.date} valitud`,finishRangeSelectionPrompt:"Klõpsake kuupäevavahemiku valimise lõpetamiseks",maximumDate:"Viimane saadaolev kuupäev",minimumDate:"Esimene saadaolev kuupäev",next:"Järgmine",previous:"Eelmine",selectedDateDescription:e=>`Valitud kuupäev: ${e.date}`,selectedRangeDescription:e=>`Valitud vahemik: ${e.dateRange}`,startRangeSelectionPrompt:"Klõpsake kuupäevavahemiku valimiseks",todayDate:e=>`Täna, ${e.date}`,todayDateSelected:e=>`Täna, ${e.date} valitud`};var Ii={};Ii={dateRange:e=>`${e.startDate} – ${e.endDate}`,dateSelected:e=>`${e.date} valittu`,finishRangeSelectionPrompt:"Lopeta päivämääräalueen valinta napsauttamalla tätä.",maximumDate:"Viimeinen varattavissa oleva päivämäärä",minimumDate:"Ensimmäinen varattavissa oleva päivämäärä",next:"Seuraava",previous:"Edellinen",selectedDateDescription:e=>`Valittu päivämäärä: ${e.date}`,selectedRangeDescription:e=>`Valittu aikaväli: ${e.dateRange}`,startRangeSelectionPrompt:"Aloita päivämääräalueen valinta napsauttamalla tätä.",todayDate:e=>`Tänään, ${e.date}`,todayDateSelected:e=>`Tänään, ${e.date} valittu`};var Vi={};Vi={dateRange:e=>`${e.startDate} à ${e.endDate}`,dateSelected:e=>`${e.date} sélectionné`,finishRangeSelectionPrompt:"Cliquer pour finir de sélectionner la plage de dates",maximumDate:"Dernière date disponible",minimumDate:"Première date disponible",next:"Suivant",previous:"Précédent",selectedDateDescription:e=>`Date sélectionnée : ${e.date}`,selectedRangeDescription:e=>`Plage sélectionnée : ${e.dateRange}`,startRangeSelectionPrompt:"Cliquer pour commencer à sélectionner la plage de dates",todayDate:e=>`Aujourd'hui, ${e.date}`,todayDateSelected:e=>`Aujourd’hui, ${e.date} sélectionné`};var Li={};Li={dateRange:e=>`${e.startDate} עד ${e.endDate}`,dateSelected:e=>`${e.date} נבחר`,finishRangeSelectionPrompt:"חץ כדי לסיים את בחירת טווח התאריכים",maximumDate:"תאריך פנוי אחרון",minimumDate:"תאריך פנוי ראשון",next:"הבא",previous:"הקודם",selectedDateDescription:e=>`תאריך נבחר: ${e.date}`,selectedRangeDescription:e=>`טווח נבחר: ${e.dateRange}`,startRangeSelectionPrompt:"לחץ כדי להתחיל בבחירת טווח התאריכים",todayDate:e=>`היום, ${e.date}`,todayDateSelected:e=>`היום, ${e.date} נבחר`};var Oi={};Oi={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} odabran`,finishRangeSelectionPrompt:"Kliknite da dovršite raspon odabranih datuma",maximumDate:"Posljednji raspoloživi datum",minimumDate:"Prvi raspoloživi datum",next:"Sljedeći",previous:"Prethodni",selectedDateDescription:e=>`Odabrani datum: ${e.date}`,selectedRangeDescription:e=>`Odabrani raspon: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite da započnete raspon odabranih datuma",todayDate:e=>`Danas, ${e.date}`,todayDateSelected:e=>`Danas, odabran ${e.date}`};var ji={};ji={dateRange:e=>`${e.startDate}–${e.endDate}`,dateSelected:e=>`${e.date} kiválasztva`,finishRangeSelectionPrompt:"Kattintson a dátumtartomány kijelölésének befejezéséhez",maximumDate:"Utolsó elérhető dátum",minimumDate:"Az első elérhető dátum",next:"Következő",previous:"Előző",selectedDateDescription:e=>`Kijelölt dátum: ${e.date}`,selectedRangeDescription:e=>`Kijelölt tartomány: ${e.dateRange}`,startRangeSelectionPrompt:"Kattintson a dátumtartomány kijelölésének indításához",todayDate:e=>`Ma, ${e.date}`,todayDateSelected:e=>`Ma, ${e.date} kijelölve`};var zi={};zi={dateRange:e=>`Da ${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selezionata`,finishRangeSelectionPrompt:"Fai clic per completare la selezione dell’intervallo di date",maximumDate:"Ultima data disponibile",minimumDate:"Prima data disponibile",next:"Successivo",previous:"Precedente",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: ${e.dateRange}`,startRangeSelectionPrompt:"Fai clic per selezionare l’intervallo di date",todayDate:e=>`Oggi, ${e.date}`,todayDateSelected:e=>`Oggi, ${e.date} selezionata`};var Hi={};Hi={dateRange:e=>`${e.startDate} から ${e.endDate}`,dateSelected:e=>`${e.date} を選択`,finishRangeSelectionPrompt:"クリックして日付範囲の選択を終了",maximumDate:"最終利用可能日",minimumDate:"最初の利用可能日",next:"次へ",previous:"前へ",selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.dateRange}`,startRangeSelectionPrompt:"クリックして日付範囲の選択を開始",todayDate:e=>`本日、${e.date}`,todayDateSelected:e=>`本日、${e.date} を選択`};var _i={};_i={dateRange:e=>`${e.startDate} ~ ${e.endDate}`,dateSelected:e=>`${e.date} 선택됨`,finishRangeSelectionPrompt:"날짜 범위 선택을 완료하려면 클릭하십시오.",maximumDate:"마지막으로 사용 가능한 일자",minimumDate:"처음으로 사용 가능한 일자",next:"다음",previous:"이전",selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.dateRange}`,startRangeSelectionPrompt:"날짜 범위 선택을 시작하려면 클릭하십시오.",todayDate:e=>`오늘, ${e.date}`,todayDateSelected:e=>`오늘, ${e.date} 선택됨`};var Ki={};Ki={dateRange:e=>`Nuo ${e.startDate} iki ${e.endDate}`,dateSelected:e=>`Pasirinkta ${e.date}`,finishRangeSelectionPrompt:"Spustelėkite, kad baigtumėte pasirinkti datų intervalą",maximumDate:"Paskutinė galima data",minimumDate:"Pirmoji galima data",next:"Paskesnis",previous:"Ankstesnis",selectedDateDescription:e=>`Pasirinkta data: ${e.date}`,selectedRangeDescription:e=>`Pasirinktas intervalas: ${e.dateRange}`,startRangeSelectionPrompt:"Spustelėkite, kad pradėtumėte pasirinkti datų intervalą",todayDate:e=>`Šiandien, ${e.date}`,todayDateSelected:e=>`Šiandien, pasirinkta ${e.date}`};var Ui={};Ui={dateRange:e=>`No ${e.startDate} līdz ${e.endDate}`,dateSelected:e=>`Atlasīts: ${e.date}`,finishRangeSelectionPrompt:"Noklikšķiniet, lai pabeigtu datumu diapazona atlasi",maximumDate:"Pēdējais pieejamais datums",minimumDate:"Pirmais pieejamais datums",next:"Tālāk",previous:"Atpakaļ",selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: ${e.dateRange}`,startRangeSelectionPrompt:"Noklikšķiniet, lai sāktu datumu diapazona atlasi",todayDate:e=>`Šodien, ${e.date}`,todayDateSelected:e=>`Atlasīta šodiena, ${e.date}`};var Wi={};Wi={dateRange:e=>`${e.startDate} til ${e.endDate}`,dateSelected:e=>`${e.date} valgt`,finishRangeSelectionPrompt:"Klikk for å fullføre valg av datoområde",maximumDate:"Siste tilgjengelige dato",minimumDate:"Første tilgjengelige dato",next:"Neste",previous:"Forrige",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt område: ${e.dateRange}`,startRangeSelectionPrompt:"Klikk for å starte valg av datoområde",todayDate:e=>`I dag, ${e.date}`,todayDateSelected:e=>`I dag, ${e.date} valgt`};var Zi={};Zi={dateRange:e=>`${e.startDate} tot ${e.endDate}`,dateSelected:e=>`${e.date} geselecteerd`,finishRangeSelectionPrompt:"Klik om de selectie van het datumbereik te voltooien",maximumDate:"Laatste beschikbare datum",minimumDate:"Eerste beschikbare datum",next:"Volgende",previous:"Vorige",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.dateRange}`,startRangeSelectionPrompt:"Klik om het datumbereik te selecteren",todayDate:e=>`Vandaag, ${e.date}`,todayDateSelected:e=>`Vandaag, ${e.date} geselecteerd`};var Gi={};Gi={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`Wybrano ${e.date}`,finishRangeSelectionPrompt:"Kliknij, aby zakończyć wybór zakresu dat",maximumDate:"Ostatnia dostępna data",minimumDate:"Pierwsza dostępna data",next:"Dalej",previous:"Wstecz",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknij, aby rozpocząć wybór zakresu dat",todayDate:e=>`Dzisiaj, ${e.date}`,todayDateSelected:e=>`Dzisiaj wybrano ${e.date}`};var Yi={};Yi={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selecionado`,finishRangeSelectionPrompt:"Clique para concluir a seleção do intervalo de datas",maximumDate:"Última data disponível",minimumDate:"Primeira data disponível",next:"Próximo",previous:"Anterior",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.dateRange}`,startRangeSelectionPrompt:"Clique para iniciar a seleção do intervalo de datas",todayDate:e=>`Hoje, ${e.date}`,todayDateSelected:e=>`Hoje, ${e.date} selecionado`};var Ji={};Ji={dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selecionado`,finishRangeSelectionPrompt:"Clique para terminar de selecionar o intervalo de datas",maximumDate:"Última data disponível",minimumDate:"Primeira data disponível",next:"Próximo",previous:"Anterior",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.dateRange}`,startRangeSelectionPrompt:"Clique para começar a selecionar o intervalo de datas",todayDate:e=>`Hoje, ${e.date}`,todayDateSelected:e=>`Hoje, ${e.date} selecionado`};var qi={};qi={dateRange:e=>`De la ${e.startDate} până la ${e.endDate}`,dateSelected:e=>`${e.date} selectată`,finishRangeSelectionPrompt:"Apăsaţi pentru a finaliza selecţia razei pentru dată",maximumDate:"Ultima dată disponibilă",minimumDate:"Prima dată disponibilă",next:"Următorul",previous:"Înainte",selectedDateDescription:e=>`Dată selectată: ${e.date}`,selectedRangeDescription:e=>`Interval selectat: ${e.dateRange}`,startRangeSelectionPrompt:"Apăsaţi pentru a începe selecţia razei pentru dată",todayDate:e=>`Astăzi, ${e.date}`,todayDateSelected:e=>`Azi, ${e.date} selectată`};var Qi={};Qi={dateRange:e=>`С ${e.startDate} по ${e.endDate}`,dateSelected:e=>`Выбрано ${e.date}`,finishRangeSelectionPrompt:"Щелкните, чтобы завершить выбор диапазона дат",maximumDate:"Последняя доступная дата",minimumDate:"Первая доступная дата",next:"Далее",previous:"Назад",selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Щелкните, чтобы начать выбор диапазона дат",todayDate:e=>`Сегодня, ${e.date}`,todayDateSelected:e=>`Сегодня, выбрано ${e.date}`};var Xi={};Xi={dateRange:e=>`Od ${e.startDate} do ${e.endDate}`,dateSelected:e=>`Vybratý dátum ${e.date}`,finishRangeSelectionPrompt:"Kliknutím dokončíte výber rozsahu dátumov",maximumDate:"Posledný dostupný dátum",minimumDate:"Prvý dostupný dátum",next:"Nasledujúce",previous:"Predchádzajúce",selectedDateDescription:e=>`Vybratý dátum: ${e.date}`,selectedRangeDescription:e=>`Vybratý rozsah: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknutím spustíte výber rozsahu dátumov",todayDate:e=>`Dnes ${e.date}`,todayDateSelected:e=>`Vybratý dnešný dátum ${e.date}`};var el={};el={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izbrano`,finishRangeSelectionPrompt:"Kliknite za dokončanje izbire datumskega obsega",maximumDate:"Zadnji razpoložljivi datum",minimumDate:"Prvi razpoložljivi datum",next:"Naprej",previous:"Nazaj",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite za začetek izbire datumskega obsega",todayDate:e=>`Danes, ${e.date}`,todayDateSelected:e=>`Danes, ${e.date} izbrano`};var tl={};tl={dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izabran`,finishRangeSelectionPrompt:"Kliknite da dovršite opseg izabranih datuma",maximumDate:"Zadnji raspoloživi datum",minimumDate:"Prvi raspoloživi datum",next:"Sledeći",previous:"Prethodni",selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani period: ${e.dateRange}`,startRangeSelectionPrompt:"Kliknite da započnete opseg izabranih datuma",todayDate:e=>`Danas, ${e.date}`,todayDateSelected:e=>`Danas, izabran ${e.date}`};var al={};al={dateRange:e=>`${e.startDate} till ${e.endDate}`,dateSelected:e=>`${e.date} har valts`,finishRangeSelectionPrompt:"Klicka för att avsluta val av datumintervall",maximumDate:"Sista tillgängliga datum",minimumDate:"Första tillgängliga datum",next:"Nästa",previous:"Föregående",selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.dateRange}`,startRangeSelectionPrompt:"Klicka för att välja datumintervall",todayDate:e=>`Idag, ${e.date}`,todayDateSelected:e=>`Idag, ${e.date} har valts`};var rl={};rl={dateRange:e=>`${e.startDate} - ${e.endDate}`,dateSelected:e=>`${e.date} seçildi`,finishRangeSelectionPrompt:"Tarih aralığı seçimini tamamlamak için tıklayın",maximumDate:"Son müsait tarih",minimumDate:"İlk müsait tarih",next:"Sonraki",previous:"Önceki",selectedDateDescription:e=>`Seçilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Seçilen Aralık: ${e.dateRange}`,startRangeSelectionPrompt:"Tarih aralığı seçimini başlatmak için tıklayın",todayDate:e=>`Bugün, ${e.date}`,todayDateSelected:e=>`Bugün, ${e.date} seçildi`};var nl={};nl={dateRange:e=>`${e.startDate} — ${e.endDate}`,dateSelected:e=>`Вибрано ${e.date}`,finishRangeSelectionPrompt:"Натисніть, щоб завершити вибір діапазону дат",maximumDate:"Остання доступна дата",minimumDate:"Перша доступна дата",next:"Наступний",previous:"Попередній",selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.dateRange}`,startRangeSelectionPrompt:"Натисніть, щоб почати вибір діапазону дат",todayDate:e=>`Сьогодні, ${e.date}`,todayDateSelected:e=>`Сьогодні, вибрано ${e.date}`};var ul={};ul={dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已选择 ${e.date}`,finishRangeSelectionPrompt:"单击以完成选择日期范围",maximumDate:"最后一个可用日期",minimumDate:"第一个可用日期",next:"下一页",previous:"上一页",selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.dateRange}`,startRangeSelectionPrompt:"单击以开始选择日期范围",todayDate:e=>`今天，即 ${e.date}`,todayDateSelected:e=>`已选择今天，即 ${e.date}`};var il={};il={dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已選取 ${e.date}`,finishRangeSelectionPrompt:"按一下以完成選取日期範圍",maximumDate:"最後一個可用日期",minimumDate:"第一個可用日期",next:"下一頁",previous:"上一頁",selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.dateRange}`,startRangeSelectionPrompt:"按一下以開始選取日期範圍",todayDate:e=>`今天，${e.date}`,todayDateSelected:e=>`已選取今天，${e.date}`};var Ot={};Ot={"ar-AE":Bi,"bg-BG":Si,"cs-CZ":Fi,"da-DK":Ri,"de-DE":ki,"el-GR":Ai,"en-US":Ti,"es-ES":Mi,"et-EE":Ni,"fi-FI":Ii,"fr-FR":Vi,"he-IL":Li,"hr-HR":Oi,"hu-HU":ji,"it-IT":zi,"ja-JP":Hi,"ko-KR":_i,"lt-LT":Ki,"lv-LV":Ui,"nb-NO":Wi,"nl-NL":Zi,"pl-PL":Gi,"pt-BR":Yi,"pt-PT":Ji,"ro-RO":qi,"ru-RU":Qi,"sk-SK":Xi,"sl-SI":el,"sr-SP":tl,"sv-SE":al,"tr-TR":rl,"uk-UA":nl,"zh-CN":ul,"zh-TW":il};function ll(e){return e&&e.__esModule?e.default:e}const Qr=new WeakMap;function kt(e){return e?.calendar.identifier==="gregory"&&e.era==="BC"?"short":void 0}function c4(e){let t=Ie(ll(Ot),"@react-aria/calendar"),a,r;var n;"highlightedRange"in e?{start:a,end:r}=e.highlightedRange||{}:a=r=(n=e.value)!==null&&n!==void 0?n:void 0;let u=ye({weekday:"long",month:"long",year:"numeric",day:"numeric",era:kt(a)||kt(r),timeZone:e.timeZone}),i="anchorDate"in e?e.anchorDate:null;return d.useMemo(()=>{if(!i&&a&&r)if(q(a,r)){let l=u.format(a.toDate(e.timeZone));return t.format("selectedDateDescription",{date:l})}else{let l=gr(u,t,a,r,e.timeZone);return t.format("selectedRangeDescription",{dateRange:l})}return""},[a,r,i,e.timeZone,t,u])}function yr(e,t,a,r){let n=Ie(ll(Ot),"@react-aria/calendar"),u=kt(e)||kt(t),i=ye({month:"long",year:"numeric",era:u,calendar:e.calendar.identifier,timeZone:a}),l=ye({month:"long",year:"numeric",day:"numeric",era:u,calendar:e.calendar.identifier,timeZone:a});return d.useMemo(()=>{if(q(e,We(e))){let o=e,s=t;if(e.calendar.getFormattableMonth&&(o=e.calendar.getFormattableMonth(e)),t.calendar.getFormattableMonth&&(s=t.calendar.getFormattableMonth(t)),q(t,ua(e)))return i.format(o.toDate(a));if(q(t,ua(t)))return r?gr(i,n,o,s,a):i.formatRange(o.toDate(a),s.toDate(a))}return r?gr(l,n,e,t,a):l.formatRange(e.toDate(a),t.toDate(a))},[e,t,i,l,n,a,r])}function gr(e,t,a,r,n){let u=e.formatRangeToParts(a.toDate(n),r.toDate(n)),i=-1;for(let s=0;s<u.length;s++){let f=u[s];if(f.source==="shared"&&f.type==="literal")i=s;else if(f.source==="endRange")break}let l="",o="";for(let s=0;s<u.length;s++)s<i?l+=u[s].value:s>i&&(o+=u[s].value);return t.format("dateRange",{startDate:l,endDate:o})}const ol=7e3;let me=null;function At(e,t="assertive",a=ol){me?me.announce(e,t,a):(me=new m4,(typeof IS_REACT_ACT_ENVIRONMENT=="boolean"?IS_REACT_ACT_ENVIRONMENT:typeof jest<"u")?me.announce(e,t,a):setTimeout(()=>{me?.isAttached()&&me?.announce(e,t,a)},100))}function f4(e){me&&me.clear(e)}class m4{isAttached(){var t;return(t=this.node)===null||t===void 0?void 0:t.isConnected}createLog(t){let a=document.createElement("div");return a.setAttribute("role","log"),a.setAttribute("aria-live",t),a.setAttribute("aria-relevant","additions"),a}destroy(){this.node&&(document.body.removeChild(this.node),this.node=null)}announce(t,a="assertive",r=ol){var n,u;if(!this.node)return;let i=document.createElement("div");typeof t=="object"?(i.setAttribute("role","img"),i.setAttribute("aria-labelledby",t["aria-labelledby"])):i.textContent=t,a==="assertive"?(n=this.assertiveLog)===null||n===void 0||n.appendChild(i):(u=this.politeLog)===null||u===void 0||u.appendChild(i),t!==""&&setTimeout(()=>{i.remove()},r)}clear(t){this.node&&((!t||t==="assertive")&&this.assertiveLog&&(this.assertiveLog.innerHTML=""),(!t||t==="polite")&&this.politeLog&&(this.politeLog.innerHTML=""))}constructor(){this.node=null,this.assertiveLog=null,this.politeLog=null,typeof document<"u"&&(this.node=document.createElement("div"),this.node.dataset.liveAnnouncer="true",Object.assign(this.node.style,{border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"}),this.assertiveLog=this.createLog("assertive"),this.node.appendChild(this.assertiveLog),this.politeLog=this.createLog("polite"),this.node.appendChild(this.politeLog),document.body.prepend(this.node))}}function $4(e){return e&&e.__esModule?e.default:e}function b4(e,t){let a=Ie($4(Ot),"@react-aria/calendar"),r=ue(e),n=yr(t.visibleRange.start,t.visibleRange.end,t.timeZone,!1),u=yr(t.visibleRange.start,t.visibleRange.end,t.timeZone,!0);hn(()=>{t.isFocused||At(u)},[u]);let i=c4(t);hn(()=>{i&&At(i,"polite",4e3)},[i]);let l=ta([!!e.errorMessage,e.isInvalid,e.validationState]);Qr.set(t,{ariaLabel:e["aria-label"],ariaLabelledBy:e["aria-labelledby"],errorMessageId:l,selectedDateDescription:i});let[o,s]=d.useState(!1),f=e.isDisabled||t.isNextVisibleRangeInvalid();f&&o&&(s(!1),t.setFocused(!0));let[c,m]=d.useState(!1),b=e.isDisabled||t.isPreviousVisibleRangeInvalid();b&&c&&(m(!1),t.setFocused(!0));let v=Mt({id:e.id,"aria-label":[e["aria-label"],u].filter(Boolean).join(", "),"aria-labelledby":e["aria-labelledby"]});return{calendarProps:Z(r,v,{role:"application","aria-details":e["aria-details"]||void 0,"aria-describedby":e["aria-describedby"]||void 0}),nextButtonProps:{onPress:()=>t.focusNextPage(),"aria-label":a.format("next"),isDisabled:f,onFocusChange:s},prevButtonProps:{onPress:()=>t.focusPreviousPage(),"aria-label":a.format("previous"),isDisabled:b,onFocusChange:m},errorMessageProps:{id:l},title:n}}function v4(e,t){return b4(e,t)}function h4(e,t){let{startDate:a=t.visibleRange.start,endDate:r=t.visibleRange.end,firstDayOfWeek:n}=e,{direction:u}=ce(),i=h=>{switch(h.key){case"Enter":case" ":h.preventDefault(),t.selectFocusedDate();break;case"PageUp":h.preventDefault(),h.stopPropagation(),t.focusPreviousSection(h.shiftKey);break;case"PageDown":h.preventDefault(),h.stopPropagation(),t.focusNextSection(h.shiftKey);break;case"End":h.preventDefault(),h.stopPropagation(),t.focusSectionEnd();break;case"Home":h.preventDefault(),h.stopPropagation(),t.focusSectionStart();break;case"ArrowLeft":h.preventDefault(),h.stopPropagation(),u==="rtl"?t.focusNextDay():t.focusPreviousDay();break;case"ArrowUp":h.preventDefault(),h.stopPropagation(),t.focusPreviousRow();break;case"ArrowRight":h.preventDefault(),h.stopPropagation(),u==="rtl"?t.focusPreviousDay():t.focusNextDay();break;case"ArrowDown":h.preventDefault(),h.stopPropagation(),t.focusNextRow();break;case"Escape":"setAnchorDate"in t&&(h.preventDefault(),t.setAnchorDate(null));break}},l=yr(a,r,t.timeZone,!0),{ariaLabel:o,ariaLabelledBy:s}=Qr.get(t),f=Mt({"aria-label":[o,l].filter(Boolean).join(", "),"aria-labelledby":s}),c=ye({weekday:e.weekdayStyle||"narrow",timeZone:t.timeZone}),{locale:m}=ce(),b=d.useMemo(()=>{let h=St(Ca(t.timeZone),m,n);return[...new Array(7).keys()].map(p=>{let C=h.add({days:p}).toDate(t.timeZone);return c.format(C)})},[m,t.timeZone,c,n]),v=rf(a,m,n);return{gridProps:Z(f,{role:"grid","aria-readonly":t.isReadOnly||void 0,"aria-disabled":t.isDisabled||void 0,"aria-multiselectable":"highlightedRange"in t||void 0,onKeyDown:i,onFocus:()=>t.setFocused(!0),onBlur:()=>t.setFocused(!1)}),headerProps:{"aria-hidden":!0},weekDays:b,weeksInMonth:v}}function p4(e){return e&&e.__esModule?e.default:e}function D4(e,t,a){let{date:r,isDisabled:n}=e,{errorMessageId:u,selectedDateDescription:i}=Qr.get(t),l=Ie(p4(Ot),"@react-aria/calendar"),o=ye({weekday:"long",day:"numeric",month:"long",year:"numeric",era:kt(r),timeZone:t.timeZone}),s=t.isSelected(r),f=t.isCellFocused(r);n=n||t.isCellDisabled(r);let c=t.isCellUnavailable(r),m=!n&&!c,b=t.isValueInvalid&&!!("highlightedRange"in t?!t.anchorDate&&t.highlightedRange&&r.compare(t.highlightedRange.start)>=0&&r.compare(t.highlightedRange.end)<=0:t.value&&q(t.value,r));b&&(s=!0),r=Ru(r,Gc);let v=d.useMemo(()=>r.toDate(t.timeZone),[r,t.timeZone]),h=Yc(r,t.timeZone),p=d.useMemo(()=>{let $="";return"highlightedRange"in t&&t.value&&!t.anchorDate&&(q(r,t.value.start)||q(r,t.value.end))&&($=i+", "),$+=o.format(v),h?$=l.format(s?"todayDateSelected":"todayDate",{date:$}):s&&($=l.format("dateSelected",{date:$})),t.minValue&&q(r,t.minValue)?$+=", "+l.format("minimumDate"):t.maxValue&&q(r,t.maxValue)&&($+=", "+l.format("maximumDate")),$},[o,v,l,s,h,r,t,i]),g="";"anchorDate"in t&&f&&!t.isReadOnly&&m&&(t.anchorDate?g=l.format("finishRangeSelectionPrompt"):g=l.format("startRangeSelectionPrompt"));let C=Ar(g),F=d.useRef(!1),D=d.useRef(!1),A=d.useRef(void 0),{pressProps:k,isPressed:V}=Vr({shouldCancelOnPointerExit:"anchorDate"in t&&!!t.anchorDate,preventFocusOnPress:!0,isDisabled:!m||t.isReadOnly,onPressStart($){if(t.isReadOnly){t.setFocusedDate(r);return}if("highlightedRange"in t&&!t.anchorDate&&($.pointerType==="mouse"||$.pointerType==="touch")){if(t.highlightedRange&&!b){if(q(r,t.highlightedRange.start)){t.setAnchorDate(t.highlightedRange.end),t.setFocusedDate(r),t.setDragging(!0),D.current=!0;return}else if(q(r,t.highlightedRange.end)){t.setAnchorDate(t.highlightedRange.start),t.setFocusedDate(r),t.setDragging(!0),D.current=!0;return}}let x=()=>{t.setDragging(!0),A.current=void 0,t.selectDate(r),t.setFocusedDate(r),F.current=!0};$.pointerType==="touch"?A.current=setTimeout(x,200):x()}},onPressEnd(){D.current=!1,F.current=!1,clearTimeout(A.current),A.current=void 0},onPress(){!("anchorDate"in t)&&!t.isReadOnly&&(t.selectDate(r),t.setFocusedDate(r))},onPressUp($){if(!t.isReadOnly&&("anchorDate"in t&&A.current&&(t.selectDate(r),t.setFocusedDate(r)),"anchorDate"in t))if(D.current)t.setAnchorDate(r);else if(t.anchorDate&&!F.current)t.selectDate(r),t.setFocusedDate(r);else if($.pointerType==="keyboard"&&!t.anchorDate){t.selectDate(r);let x=r.add({days:1});t.isInvalid(x)&&(x=r.subtract({days:1})),t.isInvalid(x)||t.setFocusedDate(x)}else $.pointerType==="virtual"&&(t.selectDate(r),t.setFocusedDate(r))}}),N;n||(N=q(r,t.focusedDate)?0:-1),d.useEffect(()=>{f&&a.current&&(Ke(a.current),Or()!=="pointer"&&document.activeElement===a.current&&Su(a.current,{containingElement:Da(a.current)}))},[f,a]);let L=ye({day:"numeric",timeZone:t.timeZone,calendar:r.calendar.identifier}),y=d.useMemo(()=>L.formatToParts(v).find($=>$.type==="day").value,[L,v]);return{cellProps:{role:"gridcell","aria-disabled":!m||void 0,"aria-selected":s||void 0,"aria-invalid":b||void 0},buttonProps:Z(k,{onFocus(){n||t.setFocusedDate(r)},tabIndex:N,role:"button","aria-disabled":!m||void 0,"aria-label":p,"aria-invalid":b||void 0,"aria-describedby":[b?u:void 0,C["aria-describedby"]].filter(Boolean).join(" ")||void 0,onPointerEnter($){"highlightDate"in t&&($.pointerType!=="touch"||t.isDragging)&&m&&t.highlightDate(r)},onPointerDown($){"releasePointerCapture"in $.target&&$.target.releasePointerCapture($.pointerId)},onContextMenu($){$.preventDefault()}}),isPressed:V,isFocused:f,isSelected:s,isDisabled:n,isUnavailable:c,isOutsideVisibleRange:r.compare(t.visibleRange.start)<0||r.compare(t.visibleRange.end)>0,isInvalid:b,formattedDate:y}}const sl={badInput:!1,customError:!1,patternMismatch:!1,rangeOverflow:!1,rangeUnderflow:!1,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!0},dl={...sl,customError:!0,valid:!1},mt={isInvalid:!1,validationDetails:sl,validationErrors:[]},y4=d.createContext({}),xr="__formValidationState"+Date.now();function cl(e){if(e[xr]){let{realtimeValidation:t,displayValidation:a,updateValidation:r,resetValidation:n,commitValidation:u}=e[xr];return{realtimeValidation:t,displayValidation:a,updateValidation:r,resetValidation:n,commitValidation:u}}return g4(e)}function g4(e){let{isInvalid:t,validationState:a,name:r,value:n,builtinValidation:u,validate:i,validationBehavior:l="aria"}=e;a&&(t||(t=a==="invalid"));let o=t!==void 0?{isInvalid:t,validationErrors:[],validationDetails:dl}:null,s=d.useMemo(()=>{if(!i||n==null)return null;let y=x4(i,n);return ru(y)},[i,n]);u?.validationDetails.valid&&(u=void 0);let f=d.useContext(y4),c=d.useMemo(()=>r?Array.isArray(r)?r.flatMap(y=>Er(f[y])):Er(f[r]):[],[f,r]),[m,b]=d.useState(f),[v,h]=d.useState(!1);f!==m&&(b(f),h(!1));let p=d.useMemo(()=>ru(v?[]:c),[v,c]),g=d.useRef(mt),[C,F]=d.useState(mt),D=d.useRef(mt),A=()=>{if(!k)return;V(!1);let y=s||u||g.current;Za(y,D.current)||(D.current=y,F(y))},[k,V]=d.useState(!1);return d.useEffect(A),{realtimeValidation:o||p||s||u||mt,displayValidation:l==="native"?o||p||C:o||p||s||u||C,updateValidation(y){l==="aria"&&!Za(C,y)?F(y):g.current=y},resetValidation(){let y=mt;Za(y,D.current)||(D.current=y,F(y)),l==="native"&&V(!1),h(!0)},commitValidation(){l==="native"&&V(!0),h(!0)}}}function Er(e){return e?Array.isArray(e)?e:[e]:[]}function x4(e,t){if(typeof e=="function"){let a=e(t);if(a&&typeof a!="boolean")return Er(a)}return[]}function ru(e){return e.length?{isInvalid:!0,validationErrors:e,validationDetails:dl}:null}function Za(e,t){return e===t?!0:!!e&&!!t&&e.isInvalid===t.isInvalid&&e.validationErrors.length===t.validationErrors.length&&e.validationErrors.every((a,r)=>a===t.validationErrors[r])&&Object.entries(e.validationDetails).every(([a,r])=>t.validationDetails[a]===r)}function E4(e,t,a){let{validationBehavior:r,focus:n}=e;_(()=>{if(r==="native"&&a?.current&&!a.current.disabled){let o=t.realtimeValidation.isInvalid?t.realtimeValidation.validationErrors.join(" ")||"Invalid value.":"";a.current.setCustomValidity(o),a.current.hasAttribute("title")||(a.current.title=""),t.realtimeValidation.isInvalid||t.updateValidation(w4(a.current))}});let u=te(()=>{t.resetValidation()}),i=te(o=>{var s;t.displayValidation.isInvalid||t.commitValidation();let f=a==null||(s=a.current)===null||s===void 0?void 0:s.form;if(!o.defaultPrevented&&a&&f&&P4(f)===a.current){var c;n?n():(c=a.current)===null||c===void 0||c.focus(),xc("keyboard")}o.preventDefault()}),l=te(()=>{t.commitValidation()});d.useEffect(()=>{let o=a?.current;if(!o)return;let s=o.form;return o.addEventListener("invalid",i),o.addEventListener("change",l),s?.addEventListener("reset",u),()=>{o.removeEventListener("invalid",i),o.removeEventListener("change",l),s?.removeEventListener("reset",u)}},[a,i,l,u,r])}function C4(e){let t=e.validity;return{badInput:t.badInput,customError:t.customError,patternMismatch:t.patternMismatch,rangeOverflow:t.rangeOverflow,rangeUnderflow:t.rangeUnderflow,stepMismatch:t.stepMismatch,tooLong:t.tooLong,tooShort:t.tooShort,typeMismatch:t.typeMismatch,valueMissing:t.valueMissing,valid:t.valid}}function w4(e){return{isInvalid:!e.validity.valid,validationDetails:C4(e),validationErrors:e.validationMessage?[e.validationMessage]:[]}}function P4(e){for(let t=0;t<e.elements.length;t++){let a=e.elements[t];if(!a.validity.valid)return a}return null}function B4(e){let{id:t,label:a,"aria-labelledby":r,"aria-label":n,labelElementType:u="label"}=e;t=xe(t);let i=xe(),l={};a&&(r=r?`${i} ${r}`:i,l={id:i,htmlFor:u==="label"?t:void 0});let o=Mt({id:t,"aria-label":n,"aria-labelledby":r});return{labelProps:l,fieldProps:o}}function fl(e){let{description:t,errorMessage:a,isInvalid:r,validationState:n}=e,{labelProps:u,fieldProps:i}=B4(e),l=ta([!!t,!!a,r,n]),o=ta([!!t,!!a,r,n]);return i=Z(i,{"aria-describedby":[l,o,e["aria-describedby"]].filter(Boolean).join(" ")||void 0}),{labelProps:u,fieldProps:i,descriptionProps:{id:l},errorMessageProps:{id:o}}}const nu={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function S4(e={}){let{style:t,isFocusable:a}=e,[r,n]=d.useState(!1),{focusWithinProps:u}=It({isDisabled:!a,onFocusWithinChange:l=>n(l)}),i=d.useMemo(()=>r?t:t?{...nu,...t}:nu,[r]);return{visuallyHiddenProps:{...u,style:i}}}function Cr(e){let{children:t,elementType:a="div",isFocusable:r,style:n,...u}=e,{visuallyHiddenProps:i}=S4(e);return S.createElement(a,Z(u,i),t)}var ml={};ml={Empty:"فارغ"};var $l={};$l={Empty:"Изпразни"};var bl={};bl={Empty:"Prázdné"};var vl={};vl={Empty:"Tom"};var hl={};hl={Empty:"Leer"};var pl={};pl={Empty:"Άδειο"};var Dl={};Dl={Empty:"Empty"};var yl={};yl={Empty:"Vacío"};var gl={};gl={Empty:"Tühjenda"};var xl={};xl={Empty:"Tyhjä"};var El={};El={Empty:"Vide"};var Cl={};Cl={Empty:"ריק"};var wl={};wl={Empty:"Prazno"};var Pl={};Pl={Empty:"Üres"};var Bl={};Bl={Empty:"Vuoto"};var Sl={};Sl={Empty:"空"};var Fl={};Fl={Empty:"비어 있음"};var Rl={};Rl={Empty:"Tuščias"};var kl={};kl={Empty:"Tukšs"};var Al={};Al={Empty:"Tom"};var Tl={};Tl={Empty:"Leeg"};var Ml={};Ml={Empty:"Pusty"};var Nl={};Nl={Empty:"Vazio"};var Il={};Il={Empty:"Vazio"};var Vl={};Vl={Empty:"Gol"};var Ll={};Ll={Empty:"Не заполнено"};var Ol={};Ol={Empty:"Prázdne"};var jl={};jl={Empty:"Prazen"};var zl={};zl={Empty:"Prazno"};var Hl={};Hl={Empty:"Tomt"};var _l={};_l={Empty:"Boş"};var Kl={};Kl={Empty:"Пусто"};var Ul={};Ul={Empty:"空"};var Wl={};Wl={Empty:"空白"};var Zl={};Zl={"ar-AE":ml,"bg-BG":$l,"cs-CZ":bl,"da-DK":vl,"de-DE":hl,"el-GR":pl,"en-US":Dl,"es-ES":yl,"et-EE":gl,"fi-FI":xl,"fr-FR":El,"he-IL":Cl,"hr-HR":wl,"hu-HU":Pl,"it-IT":Bl,"ja-JP":Sl,"ko-KR":Fl,"lt-LT":Rl,"lv-LV":kl,"nb-NO":Al,"nl-NL":Tl,"pl-PL":Ml,"pt-BR":Nl,"pt-PT":Il,"ro-RO":Vl,"ru-RU":Ll,"sk-SK":Ol,"sl-SI":jl,"sr-SP":zl,"sv-SE":Hl,"tr-TR":_l,"uk-UA":Kl,"zh-CN":Ul,"zh-TW":Wl};function F4(e){return e&&e.__esModule?e.default:e}function R4(e){const t=d.useRef(void 0);let{value:a,textValue:r,minValue:n,maxValue:u,isDisabled:i,isReadOnly:l,isRequired:o,onIncrement:s,onIncrementPage:f,onDecrement:c,onDecrementPage:m,onDecrementToMin:b,onIncrementToMax:v}=e;const h=Ie(F4(Zl),"@react-aria/spinbutton"),p=()=>clearTimeout(t.current);d.useEffect(()=>()=>p(),[]);let g=$=>{if(!($.ctrlKey||$.metaKey||$.shiftKey||$.altKey||l))switch($.key){case"PageUp":if(f){$.preventDefault(),f?.();break}case"ArrowUp":case"Up":s&&($.preventDefault(),s?.());break;case"PageDown":if(m){$.preventDefault(),m?.();break}case"ArrowDown":case"Down":c&&($.preventDefault(),c?.());break;case"Home":b&&($.preventDefault(),b?.());break;case"End":v&&($.preventDefault(),v?.());break}},C=d.useRef(!1),F=()=>{C.current=!0},D=()=>{C.current=!1},A=r===""?h.format("Empty"):(r||`${a}`).replace("-","−");d.useEffect(()=>{C.current&&(f4("assertive"),At(A,"assertive"))},[A]);const k=te($=>{p(),s?.(),t.current=window.setTimeout(()=>{(u===void 0||isNaN(u)||a===void 0||isNaN(a)||a<u)&&k(60)},$)}),V=te($=>{p(),c?.(),t.current=window.setTimeout(()=>{(n===void 0||isNaN(n)||a===void 0||isNaN(a)||a>n)&&V(60)},$)});let N=$=>{$.preventDefault()},{addGlobalListener:L,removeAllGlobalListeners:y}=pa();return{spinButtonProps:{role:"spinbutton","aria-valuenow":a!==void 0&&!isNaN(a)?a:void 0,"aria-valuetext":A,"aria-valuemin":n,"aria-valuemax":u,"aria-disabled":i||void 0,"aria-readonly":l||void 0,"aria-required":o||void 0,onKeyDown:g,onFocus:F,onBlur:D},incrementButtonProps:{onPressStart:()=>{k(400),L(window,"contextmenu",N)},onPressEnd:()=>{p(),y()},onFocus:F,onBlur:D},decrementButtonProps:{onPressStart:()=>{V(400),L(window,"contextmenu",N)},onPressEnd:()=>{p(),y()},onFocus:F,onBlur:D}}}const ge={top:"top",bottom:"top",left:"left",right:"left"},ma={top:"bottom",bottom:"top",left:"right",right:"left"},k4={top:"left",left:"top"},wr={top:"height",left:"width"},Gl={width:"totalWidth",height:"totalHeight"},Wt={};let re=typeof document<"u"?window.visualViewport:null;function uu(e){let t=0,a=0,r=0,n=0,u=0,i=0,l={};var o;let s=((o=re?.scale)!==null&&o!==void 0?o:1)>1;if(e.tagName==="BODY"){let v=document.documentElement;r=v.clientWidth,n=v.clientHeight;var f;t=(f=re?.width)!==null&&f!==void 0?f:r;var c;a=(c=re?.height)!==null&&c!==void 0?c:n,l.top=v.scrollTop||e.scrollTop,l.left=v.scrollLeft||e.scrollLeft,re&&(u=re.offsetTop,i=re.offsetLeft)}else({width:t,height:a,top:u,left:i}=ut(e)),l.top=e.scrollTop,l.left=e.scrollLeft,r=t,n=a;if(Eu()&&(e.tagName==="BODY"||e.tagName==="HTML")&&s){l.top=0,l.left=0;var m;u=(m=re?.pageTop)!==null&&m!==void 0?m:0;var b;i=(b=re?.pageLeft)!==null&&b!==void 0?b:0}return{width:t,height:a,totalWidth:r,totalHeight:n,scroll:l,top:u,left:i}}function A4(e){return{top:e.scrollTop,left:e.scrollLeft,width:e.scrollWidth,height:e.scrollHeight}}function iu(e,t,a,r,n,u,i){var l;let o=(l=n.scroll[e])!==null&&l!==void 0?l:0,s=r[wr[e]],f=r.scroll[ge[e]]+u,c=s+r.scroll[ge[e]]-u,m=t-o+i[e]-r[ge[e]],b=t-o+a+i[e]-r[ge[e]];return m<f?f-m:b>c?Math.max(c-b,f-m):0}function T4(e){let t=window.getComputedStyle(e);return{top:parseInt(t.marginTop,10)||0,bottom:parseInt(t.marginBottom,10)||0,left:parseInt(t.marginLeft,10)||0,right:parseInt(t.marginRight,10)||0}}function lu(e){if(Wt[e])return Wt[e];let[t,a]=e.split(" "),r=ge[t]||"right",n=k4[r];ge[a]||(a="center");let u=wr[r],i=wr[n];return Wt[e]={placement:t,crossPlacement:a,axis:r,crossAxis:n,size:u,crossSize:i},Wt[e]}function Ga(e,t,a,r,n,u,i,l,o,s){let{placement:f,crossPlacement:c,axis:m,crossAxis:b,size:v,crossSize:h}=r,p={};var g;p[b]=(g=e[b])!==null&&g!==void 0?g:0;var C,F,D,A;c==="center"?p[b]+=(((C=e[h])!==null&&C!==void 0?C:0)-((F=a[h])!==null&&F!==void 0?F:0))/2:c!==b&&(p[b]+=((D=e[h])!==null&&D!==void 0?D:0)-((A=a[h])!==null&&A!==void 0?A:0)),p[b]+=u;const k=e[b]-a[h]+o+s,V=e[b]+e[h]-o-s;if(p[b]=ar(p[b],k,V),f===m){const N=l?i[v]:t[Gl[v]];p[ma[m]]=Math.floor(N-e[m]+n)}else p[m]=Math.floor(e[m]+e[v]+n);return p}function M4(e,t,a,r,n,u,i,l){const o=r?a.height:t[Gl.height];var s;let f=e.top!=null?a.top+e.top:a.top+(o-((s=e.bottom)!==null&&s!==void 0?s:0)-i);var c,m,b,v,h,p;let g=l!=="top"?Math.max(0,t.height+t.top+((c=t.scroll.top)!==null&&c!==void 0?c:0)-f-(((m=n.top)!==null&&m!==void 0?m:0)+((b=n.bottom)!==null&&b!==void 0?b:0)+u)):Math.max(0,f+i-(t.top+((v=t.scroll.top)!==null&&v!==void 0?v:0))-(((h=n.top)!==null&&h!==void 0?h:0)+((p=n.bottom)!==null&&p!==void 0?p:0)+u));return Math.min(t.height-u*2,g)}function ou(e,t,a,r,n,u){let{placement:i,axis:l,size:o}=u;var s,f;if(i===l)return Math.max(0,a[l]-e[l]-((s=e.scroll[l])!==null&&s!==void 0?s:0)+t[l]-((f=r[l])!==null&&f!==void 0?f:0)-r[ma[l]]-n);var c;return Math.max(0,e[o]+e[l]+e.scroll[l]-t[l]-a[l]-a[o]-((c=r[l])!==null&&c!==void 0?c:0)-r[ma[l]]-n)}function N4(e,t,a,r,n,u,i,l,o,s,f,c,m,b,v,h){let p=lu(e),{size:g,crossAxis:C,crossSize:F,placement:D,crossPlacement:A}=p,k=Ga(t,l,a,p,f,c,s,m,v,h),V=f,N=ou(l,s,t,n,u+f,p);if(i&&r[g]>N){let ae=lu(`${ma[D]} ${A}`),he=Ga(t,l,a,ae,f,c,s,m,v,h);ou(l,s,t,n,u+f,ae)>N&&(p=ae,k=he,V=f)}let L="bottom";p.axis==="top"?p.placement==="top"?L="top":p.placement==="bottom"&&(L="bottom"):p.crossAxis==="top"&&(p.crossPlacement==="top"?L="bottom":p.crossPlacement==="bottom"&&(L="top"));let y=iu(C,k[C],a[F],l,o,u,s);k[C]+=y;let $=M4(k,l,s,m,n,u,a.height,L);b&&b<$&&($=b),a.height=Math.min(a.height,$),k=Ga(t,l,a,p,V,c,s,m,v,h),y=iu(C,k[C],a[F],l,o,u,s),k[C]+=y;let x={},w=t[C]+.5*t[F]-k[C]-n[ge[C]];const E=v/2+h;var z,P,I,O;const H=ge[C]==="left"?((z=n.left)!==null&&z!==void 0?z:0)+((P=n.right)!==null&&P!==void 0?P:0):((I=n.top)!==null&&I!==void 0?I:0)+((O=n.bottom)!==null&&O!==void 0?O:0),R=a[F]-H-v/2-h,B=t[C]+v/2-(k[C]+n[ge[C]]),j=t[C]+t[F]-v/2-(k[C]+n[ge[C]]),U=ar(w,B,j);return x[C]=ar(U,E,R),{position:k,maxHeight:$,arrowOffsetLeft:x.left,arrowOffsetTop:x.top,placement:p.placement}}function I4(e){let{placement:t,targetNode:a,overlayNode:r,scrollNode:n,padding:u,shouldFlip:i,boundaryElement:l,offset:o,crossOffset:s,maxHeight:f,arrowSize:c=0,arrowBoundaryOffset:m=0}=e,b=r instanceof HTMLElement?V4(r):document.documentElement,v=b===document.documentElement;const h=window.getComputedStyle(b).position;let p=!!h&&h!=="static",g=v?ut(a):su(a,b);if(!v){let{marginTop:x,marginLeft:w}=window.getComputedStyle(a);g.top+=parseInt(x,10)||0,g.left+=parseInt(w,10)||0}let C=ut(r),F=T4(r);var D,A;C.width+=((D=F.left)!==null&&D!==void 0?D:0)+((A=F.right)!==null&&A!==void 0?A:0);var k,V;C.height+=((k=F.top)!==null&&k!==void 0?k:0)+((V=F.bottom)!==null&&V!==void 0?V:0);let N=A4(n),L=uu(l),y=uu(b),$=l.tagName==="BODY"?ut(b):su(b,l);return b.tagName==="HTML"&&l.tagName==="BODY"&&(y.scroll.top=0,y.scroll.left=0),N4(t,g,C,N,F,u,i,L,y,$,o,s,p,f,c,m)}function ut(e){let{top:t,left:a,width:r,height:n}=e.getBoundingClientRect(),{scrollTop:u,scrollLeft:i,clientTop:l,clientLeft:o}=document.documentElement;return{top:t+u-l,left:a+i-o,width:r,height:n}}function su(e,t){let a=window.getComputedStyle(e),r;if(a.position==="fixed"){let{top:n,left:u,width:i,height:l}=e.getBoundingClientRect();r={top:n,left:u,width:i,height:l}}else{r=ut(e);let n=ut(t),u=window.getComputedStyle(t);n.top+=(parseInt(u.borderTopWidth,10)||0)-t.scrollTop,n.left+=(parseInt(u.borderLeftWidth,10)||0)-t.scrollLeft,r.top-=n.top,r.left-=n.left}return r.top-=parseInt(a.marginTop,10)||0,r.left-=parseInt(a.marginLeft,10)||0,r}function V4(e){let t=e.offsetParent;if(t&&t===document.body&&window.getComputedStyle(t).position==="static"&&!du(t)&&(t=document.documentElement),t==null)for(t=e.parentElement;t&&!du(t);)t=t.parentElement;return t||document.documentElement}function du(e){let t=window.getComputedStyle(e);return t.transform!=="none"||/transform|perspective/.test(t.willChange)||t.filter!=="none"||t.contain==="paint"||"backdropFilter"in t&&t.backdropFilter!=="none"||"WebkitBackdropFilter"in t&&t.WebkitBackdropFilter!=="none"}const L4=new WeakMap;function O4(e){let{triggerRef:t,isOpen:a,onClose:r}=e;d.useEffect(()=>{if(!a||r===null)return;let n=u=>{let i=u.target;if(!t.current||i instanceof Node&&!i.contains(t.current)||u.target instanceof HTMLInputElement||u.target instanceof HTMLTextAreaElement)return;let l=r||L4.get(t.current);l&&l()};return window.addEventListener("scroll",n,!0),()=>{window.removeEventListener("scroll",n,!0)}},[a,r,t])}let Y=typeof document<"u"?window.visualViewport:null;function j4(e){let{direction:t}=ce(),{arrowSize:a=0,targetRef:r,overlayRef:n,scrollRef:u=n,placement:i="bottom",containerPadding:l=12,shouldFlip:o=!0,boundaryElement:s=typeof document<"u"?document.body:null,offset:f=0,crossOffset:c=0,shouldUpdatePosition:m=!0,isOpen:b=!0,onClose:v,maxHeight:h,arrowBoundaryOffset:p=0}=e,[g,C]=d.useState(null),F=[m,i,n.current,r.current,u.current,l,o,s,f,c,b,t,h,p,a],D=d.useRef(Y?.scale);d.useEffect(()=>{b&&(D.current=Y?.scale)},[b]);let A=d.useCallback(()=>{if(m===!1||!b||!n.current||!r.current||!s||Y?.scale!==D.current)return;let y=null;if(u.current&&u.current.contains(document.activeElement)){var $;let O=($=document.activeElement)===null||$===void 0?void 0:$.getBoundingClientRect(),H=u.current.getBoundingClientRect();var x;if(y={type:"top",offset:((x=O?.top)!==null&&x!==void 0?x:0)-H.top},y.offset>H.height/2){y.type="bottom";var w;y.offset=((w=O?.bottom)!==null&&w!==void 0?w:0)-H.bottom}}let E=n.current;if(!h&&n.current){var z;E.style.top="0px",E.style.bottom="";var P;E.style.maxHeight=((P=(z=window.visualViewport)===null||z===void 0?void 0:z.height)!==null&&P!==void 0?P:window.innerHeight)+"px"}let I=I4({placement:H4(i,t),overlayNode:n.current,targetNode:r.current,scrollNode:u.current||n.current,padding:l,shouldFlip:o,boundaryElement:s,offset:f,crossOffset:c,maxHeight:h,arrowSize:a,arrowBoundaryOffset:p});if(I.position){if(E.style.top="",E.style.bottom="",E.style.left="",E.style.right="",Object.keys(I.position).forEach(O=>E.style[O]=I.position[O]+"px"),E.style.maxHeight=I.maxHeight!=null?I.maxHeight+"px":"",y&&document.activeElement&&u.current){let O=document.activeElement.getBoundingClientRect(),H=u.current.getBoundingClientRect(),R=O[y.type]-H[y.type];u.current.scrollTop+=R-y.offset}C(I)}},F);_(A,F),z4(A),er({ref:n,onResize:A}),er({ref:r,onResize:A});let k=d.useRef(!1);_(()=>{let y,$=()=>{k.current=!0,clearTimeout(y),y=setTimeout(()=>{k.current=!1},500),A()},x=()=>{k.current&&$()};return Y?.addEventListener("resize",$),Y?.addEventListener("scroll",x),()=>{Y?.removeEventListener("resize",$),Y?.removeEventListener("scroll",x)}},[A]);let V=d.useCallback(()=>{k.current||v?.()},[v,k]);O4({triggerRef:r,isOpen:b,onClose:v&&V});var N,L;return{overlayProps:{style:{position:"absolute",zIndex:1e5,...g?.position,maxHeight:(N=g?.maxHeight)!==null&&N!==void 0?N:"100vh"}},placement:(L=g?.placement)!==null&&L!==void 0?L:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:g?.arrowOffsetLeft,top:g?.arrowOffsetTop}},updatePosition:A}}function z4(e){_(()=>(window.addEventListener("resize",e,!1),()=>{window.removeEventListener("resize",e,!1)}),[e])}function H4(e,t){return t==="rtl"?e.replace("start","right").replace("end","left"):e.replace("start","left").replace("end","right")}const De=[];function _4(e,t){let{onClose:a,shouldCloseOnBlur:r,isOpen:n,isDismissable:u=!1,isKeyboardDismissDisabled:i=!1,shouldCloseOnInteractOutside:l}=e;d.useEffect(()=>{if(n&&!De.includes(t))return De.push(t),()=>{let v=De.indexOf(t);v>=0&&De.splice(v,1)}},[n,t]);let o=()=>{De[De.length-1]===t&&a&&a()},s=v=>{(!l||l(v.target))&&De[De.length-1]===t&&(v.stopPropagation(),v.preventDefault())},f=v=>{(!l||l(v.target))&&(De[De.length-1]===t&&(v.stopPropagation(),v.preventDefault()),o())},c=v=>{v.key==="Escape"&&!i&&!v.nativeEvent.isComposing&&(v.stopPropagation(),v.preventDefault(),o())};Tc({ref:t,onInteractOutside:u&&n?f:void 0,onInteractOutsideStart:s});let{focusWithinProps:m}=It({isDisabled:!r,onBlurWithin:v=>{!v.relatedTarget||l4(v.relatedTarget)||(!l||l(v.relatedTarget))&&a?.()}}),b=v=>{v.target===v.currentTarget&&v.preventDefault()};return{overlayProps:{onKeyDown:c,...m},underlayProps:{onPointerDown:b}}}const Ya=typeof document<"u"&&window.visualViewport,K4=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);let Zt=0,Ja;function U4(e={}){let{isDisabled:t}=e;_(()=>{if(!t)return Zt++,Zt===1&&(ha()?Ja=Z4():Ja=W4()),()=>{Zt--,Zt===0&&Ja()}},[t])}function W4(){let e=window.innerWidth-document.documentElement.clientWidth;return Ct(e>0&&("scrollbarGutter"in document.documentElement.style?He(document.documentElement,"scrollbarGutter","stable"):He(document.documentElement,"paddingRight",`${e}px`)),He(document.documentElement,"overflow","hidden"))}function Z4(){let e,t,a=s=>{e=Da(s.target,!0),!(e===document.documentElement&&e===document.body)&&e instanceof HTMLElement&&window.getComputedStyle(e).overscrollBehavior==="auto"&&(t=He(e,"overscrollBehavior","contain"))},r=s=>{if(!e||e===document.documentElement||e===document.body){s.preventDefault();return}e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&s.preventDefault()},n=()=>{t&&t()},u=s=>{let f=s.target;G4(f)&&(l(),f.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{f.style.transform="",Ya&&(Ya.height<window.innerHeight?requestAnimationFrame(()=>{cu(f)}):Ya.addEventListener("resize",()=>cu(f),{once:!0}))}))},i=null,l=()=>{if(i)return;let s=()=>{window.scrollTo(0,0)},f=window.pageXOffset,c=window.pageYOffset;i=Ct($t(window,"scroll",s),He(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),He(document.documentElement,"overflow","hidden"),He(document.body,"marginTop",`-${c}px`),()=>{window.scrollTo(f,c)}),window.scrollTo(0,0)},o=Ct($t(document,"touchstart",a,{passive:!1,capture:!0}),$t(document,"touchmove",r,{passive:!1,capture:!0}),$t(document,"touchend",n,{passive:!1,capture:!0}),$t(document,"focus",u,!0));return()=>{t?.(),i?.(),o()}}function He(e,t,a){let r=e.style[t];return e.style[t]=a,()=>{e.style[t]=r}}function $t(e,t,a,r){return e.addEventListener(t,a,r),()=>{e.removeEventListener(t,a,r)}}function cu(e){let t=document.scrollingElement||document.documentElement,a=e;for(;a&&a!==t;){let r=Da(a);if(r!==document.documentElement&&r!==document.body&&r!==a){let n=r.getBoundingClientRect().top,u=a.getBoundingClientRect().top;u>n+a.clientHeight&&(r.scrollTop+=u-n)}a=r.parentElement}}function G4(e){return e instanceof HTMLInputElement&&!K4.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}const Y4=d.createContext({});function J4(){var e;return(e=d.useContext(Y4))!==null&&e!==void 0?e:{}}var Yl={};Yl={dismiss:"تجاهل"};var Jl={};Jl={dismiss:"Отхвърляне"};var ql={};ql={dismiss:"Odstranit"};var Ql={};Ql={dismiss:"Luk"};var Xl={};Xl={dismiss:"Schließen"};var eo={};eo={dismiss:"Απόρριψη"};var to={};to={dismiss:"Dismiss"};var ao={};ao={dismiss:"Descartar"};var ro={};ro={dismiss:"Lõpeta"};var no={};no={dismiss:"Hylkää"};var uo={};uo={dismiss:"Rejeter"};var io={};io={dismiss:"התעלם"};var lo={};lo={dismiss:"Odbaci"};var oo={};oo={dismiss:"Elutasítás"};var so={};so={dismiss:"Ignora"};var co={};co={dismiss:"閉じる"};var fo={};fo={dismiss:"무시"};var mo={};mo={dismiss:"Atmesti"};var $o={};$o={dismiss:"Nerādīt"};var bo={};bo={dismiss:"Lukk"};var vo={};vo={dismiss:"Negeren"};var ho={};ho={dismiss:"Zignoruj"};var po={};po={dismiss:"Descartar"};var Do={};Do={dismiss:"Dispensar"};var yo={};yo={dismiss:"Revocare"};var go={};go={dismiss:"Пропустить"};var xo={};xo={dismiss:"Zrušiť"};var Eo={};Eo={dismiss:"Opusti"};var Co={};Co={dismiss:"Odbaci"};var wo={};wo={dismiss:"Avvisa"};var Po={};Po={dismiss:"Kapat"};var Bo={};Bo={dismiss:"Скасувати"};var So={};So={dismiss:"取消"};var Fo={};Fo={dismiss:"關閉"};var Ro={};Ro={"ar-AE":Yl,"bg-BG":Jl,"cs-CZ":ql,"da-DK":Ql,"de-DE":Xl,"el-GR":eo,"en-US":to,"es-ES":ao,"et-EE":ro,"fi-FI":no,"fr-FR":uo,"he-IL":io,"hr-HR":lo,"hu-HU":oo,"it-IT":so,"ja-JP":co,"ko-KR":fo,"lt-LT":mo,"lv-LV":$o,"nb-NO":bo,"nl-NL":vo,"pl-PL":ho,"pt-BR":po,"pt-PT":Do,"ro-RO":yo,"ru-RU":go,"sk-SK":xo,"sl-SI":Eo,"sr-SP":Co,"sv-SE":wo,"tr-TR":Po,"uk-UA":Bo,"zh-CN":So,"zh-TW":Fo};function q4(e){return e&&e.__esModule?e.default:e}function fu(e){let{onDismiss:t,...a}=e,r=Ie(q4(Ro),"@react-aria/overlays"),n=Mt(a,r.format("dismiss")),u=()=>{t&&t()};return S.createElement(Cr,null,S.createElement("button",{...n,tabIndex:-1,onClick:u,style:{width:1,height:1}}))}let bt=new WeakMap,le=[];function Q4(e,t=document.body){let a=new Set(e),r=new Set,n=o=>{for(let m of o.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))a.add(m);let s=m=>{if(a.has(m)||m.parentElement&&r.has(m.parentElement)&&m.parentElement.getAttribute("role")!=="row")return NodeFilter.FILTER_REJECT;for(let b of a)if(m.contains(b))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},f=document.createTreeWalker(o,NodeFilter.SHOW_ELEMENT,{acceptNode:s}),c=s(o);if(c===NodeFilter.FILTER_ACCEPT&&u(o),c!==NodeFilter.FILTER_REJECT){let m=f.nextNode();for(;m!=null;)u(m),m=f.nextNode()}},u=o=>{var s;let f=(s=bt.get(o))!==null&&s!==void 0?s:0;o.getAttribute("aria-hidden")==="true"&&f===0||(f===0&&o.setAttribute("aria-hidden","true"),r.add(o),bt.set(o,f+1))};le.length&&le[le.length-1].disconnect(),n(t);let i=new MutationObserver(o=>{for(let s of o)if(!(s.type!=="childList"||s.addedNodes.length===0)&&![...a,...r].some(f=>f.contains(s.target))){for(let f of s.removedNodes)f instanceof Element&&(a.delete(f),r.delete(f));for(let f of s.addedNodes)(f instanceof HTMLElement||f instanceof SVGElement)&&(f.dataset.liveAnnouncer==="true"||f.dataset.reactAriaTopLayer==="true")?a.add(f):f instanceof Element&&n(f)}});i.observe(t,{childList:!0,subtree:!0});let l={visibleNodes:a,hiddenNodes:r,observe(){i.observe(t,{childList:!0,subtree:!0})},disconnect(){i.disconnect()}};return le.push(l),()=>{i.disconnect();for(let o of r){let s=bt.get(o);s!=null&&(s===1?(o.removeAttribute("aria-hidden"),bt.delete(o)):bt.set(o,s-1))}l===le[le.length-1]?(le.pop(),le.length&&le[le.length-1].observe()):le.splice(le.indexOf(l),1)}}function X4(e){let t=le[le.length-1];if(t&&!t.visibleNodes.has(e))return t.visibleNodes.add(e),()=>{t.visibleNodes.delete(e)}}function e3(e,t){let{triggerRef:a,popoverRef:r,groupRef:n,isNonModal:u,isKeyboardDismissDisabled:i,shouldCloseOnInteractOutside:l,...o}=e,s=o.trigger==="SubmenuTrigger",{overlayProps:f,underlayProps:c}=_4({isOpen:t.isOpen,onClose:t.close,shouldCloseOnBlur:!0,isDismissable:!u||s,isKeyboardDismissDisabled:i,shouldCloseOnInteractOutside:l},n??r),{overlayProps:m,arrowProps:b,placement:v}=j4({...o,targetRef:a,overlayRef:r,isOpen:t.isOpen,onClose:u&&!s?t.close:null});return U4({isDisabled:u||!t.isOpen}),_(()=>{if(t.isOpen&&r.current){var h,p;return u?X4((h=n?.current)!==null&&h!==void 0?h:r.current):Q4([(p=n?.current)!==null&&p!==void 0?p:r.current])}},[u,t.isOpen,r,n]),{popoverProps:Z(f,m),arrowProps:b,underlayProps:c,placement:v}}const ko=S.createContext(null);function mu(e){let t=Fr(),{portalContainer:a=t?null:document.body,isExiting:r}=e,[n,u]=d.useState(!1),i=d.useMemo(()=>({contain:n,setContain:u}),[n,u]),{getContainer:l}=J4();if(!e.portalContainer&&l&&(a=l()),!a)return null;let o=e.children;return e.disableFocusManagement||(o=S.createElement(n4,{restoreFocus:!0,contain:(e.shouldContainFocus||n)&&!r},o)),o=S.createElement(ko.Provider,{value:i},S.createElement(Rc,null,o)),hd.createPortal(o,a)}function t3(){let e=d.useContext(ko),t=e?.setContain;_(()=>{t?.(!0)},[t])}var Ao={};Ao={calendar:"التقويم",day:"يوم",dayPeriod:"ص/م",endDate:"تاريخ الانتهاء",era:"العصر",hour:"الساعات",minute:"الدقائق",month:"الشهر",second:"الثواني",selectedDateDescription:e=>`تاريخ محدد: ${e.date}`,selectedRangeDescription:e=>`المدى الزمني المحدد: ${e.startDate} إلى ${e.endDate}`,selectedTimeDescription:e=>`الوقت المحدد: ${e.time}`,startDate:"تاريخ البدء",timeZoneName:"التوقيت",weekday:"اليوم",year:"السنة"};var To={};To={calendar:"Календар",day:"ден",dayPeriod:"пр.об./сл.об.",endDate:"Крайна дата",era:"ера",hour:"час",minute:"минута",month:"месец",second:"секунда",selectedDateDescription:e=>`Избрана дата: ${e.date}`,selectedRangeDescription:e=>`Избран диапазон: ${e.startDate} до ${e.endDate}`,selectedTimeDescription:e=>`Избрано време: ${e.time}`,startDate:"Начална дата",timeZoneName:"часова зона",weekday:"ден от седмицата",year:"година"};var Mo={};Mo={calendar:"Kalendář",day:"den",dayPeriod:"část dne",endDate:"Konečné datum",era:"letopočet",hour:"hodina",minute:"minuta",month:"měsíc",second:"sekunda",selectedDateDescription:e=>`Vybrané datum: ${e.date}`,selectedRangeDescription:e=>`Vybrané období: ${e.startDate} až ${e.endDate}`,selectedTimeDescription:e=>`Vybraný čas: ${e.time}`,startDate:"Počáteční datum",timeZoneName:"časové pásmo",weekday:"den v týdnu",year:"rok"};var No={};No={calendar:"Kalender",day:"dag",dayPeriod:"AM/PM",endDate:"Slutdato",era:"æra",hour:"time",minute:"minut",month:"måned",second:"sekund",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt interval: ${e.startDate} til ${e.endDate}`,selectedTimeDescription:e=>`Valgt tidspunkt: ${e.time}`,startDate:"Startdato",timeZoneName:"tidszone",weekday:"ugedag",year:"år"};var Io={};Io={calendar:"Kalender",day:"Tag",dayPeriod:"Tageshälfte",endDate:"Enddatum",era:"Epoche",hour:"Stunde",minute:"Minute",month:"Monat",second:"Sekunde",selectedDateDescription:e=>`Ausgewähltes Datum: ${e.date}`,selectedRangeDescription:e=>`Ausgewählter Bereich: ${e.startDate} bis ${e.endDate}`,selectedTimeDescription:e=>`Ausgewählte Zeit: ${e.time}`,startDate:"Anfangsdatum",timeZoneName:"Zeitzone",weekday:"Wochentag",year:"Jahr"};var Vo={};Vo={calendar:"Ημερολόγιο",day:"ημέρα",dayPeriod:"π.μ./μ.μ.",endDate:"Ημερομηνία λήξης",era:"περίοδος",hour:"ώρα",minute:"λεπτό",month:"μήνας",second:"δευτερόλεπτο",selectedDateDescription:e=>`Επιλεγμένη ημερομηνία: ${e.date}`,selectedRangeDescription:e=>`Επιλεγμένο εύρος: ${e.startDate} έως ${e.endDate}`,selectedTimeDescription:e=>`Επιλεγμένη ώρα: ${e.time}`,startDate:"Ημερομηνία έναρξης",timeZoneName:"ζώνη ώρας",weekday:"καθημερινή",year:"έτος"};var Lo={};Lo={era:"era",year:"year",month:"month",day:"day",hour:"hour",minute:"minute",second:"second",dayPeriod:"AM/PM",calendar:"Calendar",startDate:"Start Date",endDate:"End Date",weekday:"day of the week",timeZoneName:"time zone",selectedDateDescription:e=>`Selected Date: ${e.date}`,selectedRangeDescription:e=>`Selected Range: ${e.startDate} to ${e.endDate}`,selectedTimeDescription:e=>`Selected Time: ${e.time}`};var Oo={};Oo={calendar:"Calendario",day:"día",dayPeriod:"a. m./p. m.",endDate:"Fecha final",era:"era",hour:"hora",minute:"minuto",month:"mes",second:"segundo",selectedDateDescription:e=>`Fecha seleccionada: ${e.date}`,selectedRangeDescription:e=>`Rango seleccionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora seleccionada: ${e.time}`,startDate:"Fecha de inicio",timeZoneName:"zona horaria",weekday:"día de la semana",year:"año"};var jo={};jo={calendar:"Kalender",day:"päev",dayPeriod:"enne/pärast lõunat",endDate:"Lõppkuupäev",era:"ajastu",hour:"tund",minute:"minut",month:"kuu",second:"sekund",selectedDateDescription:e=>`Valitud kuupäev: ${e.date}`,selectedRangeDescription:e=>`Valitud vahemik: ${e.startDate} kuni ${e.endDate}`,selectedTimeDescription:e=>`Valitud aeg: ${e.time}`,startDate:"Alguskuupäev",timeZoneName:"ajavöönd",weekday:"nädalapäev",year:"aasta"};var zo={};zo={calendar:"Kalenteri",day:"päivä",dayPeriod:"vuorokaudenaika",endDate:"Päättymispäivä",era:"aikakausi",hour:"tunti",minute:"minuutti",month:"kuukausi",second:"sekunti",selectedDateDescription:e=>`Valittu päivämäärä: ${e.date}`,selectedRangeDescription:e=>`Valittu aikaväli: ${e.startDate} – ${e.endDate}`,selectedTimeDescription:e=>`Valittu aika: ${e.time}`,startDate:"Alkamispäivä",timeZoneName:"aikavyöhyke",weekday:"viikonpäivä",year:"vuosi"};var Ho={};Ho={calendar:"Calendrier",day:"jour",dayPeriod:"cadran",endDate:"Date de fin",era:"ère",hour:"heure",minute:"minute",month:"mois",second:"seconde",selectedDateDescription:e=>`Date sélectionnée : ${e.date}`,selectedRangeDescription:e=>`Plage sélectionnée : ${e.startDate} au ${e.endDate}`,selectedTimeDescription:e=>`Heure choisie : ${e.time}`,startDate:"Date de début",timeZoneName:"fuseau horaire",weekday:"jour de la semaine",year:"année"};var _o={};_o={calendar:"לוח שנה",day:"יום",dayPeriod:"לפנה״צ/אחה״צ",endDate:"תאריך סיום",era:"תקופה",hour:"שעה",minute:"דקה",month:"חודש",second:"שנייה",selectedDateDescription:e=>`תאריך נבחר: ${e.date}`,selectedRangeDescription:e=>`טווח נבחר: ${e.startDate} עד ${e.endDate}`,selectedTimeDescription:e=>`זמן נבחר: ${e.time}`,startDate:"תאריך התחלה",timeZoneName:"אזור זמן",weekday:"יום בשבוע",year:"שנה"};var Ko={};Ko={calendar:"Kalendar",day:"dan",dayPeriod:"AM/PM",endDate:"Datum završetka",era:"era",hour:"sat",minute:"minuta",month:"mjesec",second:"sekunda",selectedDateDescription:e=>`Odabrani datum: ${e.date}`,selectedRangeDescription:e=>`Odabrani raspon: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Odabrano vrijeme: ${e.time}`,startDate:"Datum početka",timeZoneName:"vremenska zona",weekday:"dan u tjednu",year:"godina"};var Uo={};Uo={calendar:"Naptár",day:"nap",dayPeriod:"napszak",endDate:"Befejező dátum",era:"éra",hour:"óra",minute:"perc",month:"hónap",second:"másodperc",selectedDateDescription:e=>`Kijelölt dátum: ${e.date}`,selectedRangeDescription:e=>`Kijelölt tartomány: ${e.startDate}–${e.endDate}`,selectedTimeDescription:e=>`Kijelölt idő: ${e.time}`,startDate:"Kezdő dátum",timeZoneName:"időzóna",weekday:"hét napja",year:"év"};var Wo={};Wo={calendar:"Calendario",day:"giorno",dayPeriod:"AM/PM",endDate:"Data finale",era:"era",hour:"ora",minute:"minuto",month:"mese",second:"secondo",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: da ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Ora selezionata: ${e.time}`,startDate:"Data iniziale",timeZoneName:"fuso orario",weekday:"giorno della settimana",year:"anno"};var Zo={};Zo={calendar:"カレンダー",day:"日",dayPeriod:"午前/午後",endDate:"終了日",era:"時代",hour:"時",minute:"分",month:"月",second:"秒",selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.startDate} から ${e.endDate}`,selectedTimeDescription:e=>`選択した時間 : ${e.time}`,startDate:"開始日",timeZoneName:"タイムゾーン",weekday:"曜日",year:"年"};var Go={};Go={calendar:"달력",day:"일",dayPeriod:"오전/오후",endDate:"종료일",era:"연호",hour:"시",minute:"분",month:"월",second:"초",selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.startDate} ~ ${e.endDate}`,selectedTimeDescription:e=>`선택 시간: ${e.time}`,startDate:"시작일",timeZoneName:"시간대",weekday:"요일",year:"년"};var Yo={};Yo={calendar:"Kalendorius",day:"diena",dayPeriod:"iki pietų / po pietų",endDate:"Pabaigos data",era:"era",hour:"valanda",minute:"minutė",month:"mėnuo",second:"sekundė",selectedDateDescription:e=>`Pasirinkta data: ${e.date}`,selectedRangeDescription:e=>`Pasirinktas intervalas: nuo ${e.startDate} iki ${e.endDate}`,selectedTimeDescription:e=>`Pasirinktas laikas: ${e.time}`,startDate:"Pradžios data",timeZoneName:"laiko juosta",weekday:"savaitės diena",year:"metai"};var Jo={};Jo={calendar:"Kalendārs",day:"diena",dayPeriod:"priekšpusdienā/pēcpusdienā",endDate:"Beigu datums",era:"ēra",hour:"stundas",minute:"minūtes",month:"mēnesis",second:"sekundes",selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: no ${e.startDate} līdz ${e.endDate}`,selectedTimeDescription:e=>`Atlasītais laiks: ${e.time}`,startDate:"Sākuma datums",timeZoneName:"laika josla",weekday:"nedēļas diena",year:"gads"};var qo={};qo={calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Sluttdato",era:"tidsalder",hour:"time",minute:"minutt",month:"måned",second:"sekund",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt område: ${e.startDate} til ${e.endDate}`,selectedTimeDescription:e=>`Valgt tid: ${e.time}`,startDate:"Startdato",timeZoneName:"tidssone",weekday:"ukedag",year:"år"};var Qo={};Qo={calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Einddatum",era:"tijdperk",hour:"uur",minute:"minuut",month:"maand",second:"seconde",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.startDate} tot ${e.endDate}`,selectedTimeDescription:e=>`Geselecteerde tijd: ${e.time}`,startDate:"Startdatum",timeZoneName:"tijdzone",weekday:"dag van de week",year:"jaar"};var Xo={};Xo={calendar:"Kalendarz",day:"dzień",dayPeriod:"rano / po południu / wieczorem",endDate:"Data końcowa",era:"era",hour:"godzina",minute:"minuta",month:"miesiąc",second:"sekunda",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Wybrany czas: ${e.time}`,startDate:"Data początkowa",timeZoneName:"strefa czasowa",weekday:"dzień tygodnia",year:"rok"};var es={};es={calendar:"Calendário",day:"dia",dayPeriod:"AM/PM",endDate:"Data final",era:"era",hour:"hora",minute:"minuto",month:"mês",second:"segundo",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora selecionada: ${e.time}`,startDate:"Data inicial",timeZoneName:"fuso horário",weekday:"dia da semana",year:"ano"};var ts={};ts={calendar:"Calendário",day:"dia",dayPeriod:"am/pm",endDate:"Data de Término",era:"era",hour:"hora",minute:"minuto",month:"mês",second:"segundo",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora selecionada: ${e.time}`,startDate:"Data de Início",timeZoneName:"fuso horário",weekday:"dia da semana",year:"ano"};var as={};as={calendar:"Calendar",day:"zi",dayPeriod:"a.m/p.m.",endDate:"Dată final",era:"eră",hour:"oră",minute:"minut",month:"lună",second:"secundă",selectedDateDescription:e=>`Dată selectată: ${e.date}`,selectedRangeDescription:e=>`Interval selectat: de la ${e.startDate} până la ${e.endDate}`,selectedTimeDescription:e=>`Ora selectată: ${e.time}`,startDate:"Dată început",timeZoneName:"fus orar",weekday:"ziua din săptămână",year:"an"};var rs={};rs={calendar:"Календарь",day:"день",dayPeriod:"AM/PM",endDate:"Дата окончания",era:"эра",hour:"час",minute:"минута",month:"месяц",second:"секунда",selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: с ${e.startDate} по ${e.endDate}`,selectedTimeDescription:e=>`Выбранное время: ${e.time}`,startDate:"Дата начала",timeZoneName:"часовой пояс",weekday:"день недели",year:"год"};var ns={};ns={calendar:"Kalendár",day:"deň",dayPeriod:"AM/PM",endDate:"Dátum ukončenia",era:"letopočet",hour:"hodina",minute:"minúta",month:"mesiac",second:"sekunda",selectedDateDescription:e=>`Vybratý dátum: ${e.date}`,selectedRangeDescription:e=>`Vybratý rozsah: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Vybratý čas: ${e.time}`,startDate:"Dátum začatia",timeZoneName:"časové pásmo",weekday:"deň týždňa",year:"rok"};var us={};us={calendar:"Koledar",day:"dan",dayPeriod:"dop/pop",endDate:"Datum konca",era:"doba",hour:"ura",minute:"minuta",month:"mesec",second:"sekunda",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izbrani čas: ${e.time}`,startDate:"Datum začetka",timeZoneName:"časovni pas",weekday:"dan v tednu",year:"leto"};var is={};is={calendar:"Kalendar",day:"дан",dayPeriod:"пре подне/по подне",endDate:"Datum završetka",era:"ера",hour:"сат",minute:"минут",month:"месец",second:"секунд",selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani opseg: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izabrano vreme: ${e.time}`,startDate:"Datum početka",timeZoneName:"временска зона",weekday:"дан у недељи",year:"година"};var ls={};ls={calendar:"Kalender",day:"dag",dayPeriod:"fm/em",endDate:"Slutdatum",era:"era",hour:"timme",minute:"minut",month:"månad",second:"sekund",selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.startDate} till ${e.endDate}`,selectedTimeDescription:e=>`Vald tid: ${e.time}`,startDate:"Startdatum",timeZoneName:"tidszon",weekday:"veckodag",year:"år"};var os={};os={calendar:"Takvim",day:"gün",dayPeriod:"ÖÖ/ÖS",endDate:"Bitiş Tarihi",era:"çağ",hour:"saat",minute:"dakika",month:"ay",second:"saniye",selectedDateDescription:e=>`Seçilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Seçilen Aralık: ${e.startDate} - ${e.endDate}`,selectedTimeDescription:e=>`Seçilen Zaman: ${e.time}`,startDate:"Başlangıç Tarihi",timeZoneName:"saat dilimi",weekday:"haftanın günü",year:"yıl"};var ss={};ss={calendar:"Календар",day:"день",dayPeriod:"дп/пп",endDate:"Дата завершення",era:"ера",hour:"година",minute:"хвилина",month:"місяць",second:"секунда",selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.startDate} — ${e.endDate}`,selectedTimeDescription:e=>`Вибраний час: ${e.time}`,startDate:"Дата початку",timeZoneName:"часовий пояс",weekday:"день тижня",year:"рік"};var ds={};ds={calendar:"日历",day:"日",dayPeriod:"上午/下午",endDate:"结束日期",era:"纪元",hour:"小时",minute:"分钟",month:"月",second:"秒",selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`选定的时间：${e.time}`,startDate:"开始日期",timeZoneName:"时区",weekday:"工作日",year:"年"};var cs={};cs={calendar:"日曆",day:"日",dayPeriod:"上午/下午",endDate:"結束日期",era:"纪元",hour:"小时",minute:"分钟",month:"月",second:"秒",selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`選定的時間：${e.time}`,startDate:"開始日期",timeZoneName:"时区",weekday:"工作日",year:"年"};var Ba={};Ba={"ar-AE":Ao,"bg-BG":To,"cs-CZ":Mo,"da-DK":No,"de-DE":Io,"el-GR":Vo,"en-US":Lo,"es-ES":Oo,"et-EE":jo,"fi-FI":zo,"fr-FR":Ho,"he-IL":_o,"hr-HR":Ko,"hu-HU":Uo,"it-IT":Wo,"ja-JP":Zo,"ko-KR":Go,"lt-LT":Yo,"lv-LV":Jo,"nb-NO":qo,"nl-NL":Qo,"pl-PL":Xo,"pt-BR":es,"pt-PT":ts,"ro-RO":as,"ru-RU":rs,"sk-SK":ns,"sl-SI":us,"sr-SP":is,"sv-SE":ls,"tr-TR":os,"uk-UA":ss,"zh-CN":ds,"zh-TW":cs};function fs(e,t,a){let{direction:r}=ce(),n=d.useMemo(()=>Jr(t),[t]),u=o=>{if(o.currentTarget.contains(o.target)&&(o.altKey&&(o.key==="ArrowDown"||o.key==="ArrowUp")&&"setOpen"in e&&(o.preventDefault(),o.stopPropagation(),e.setOpen(!0)),!a))switch(o.key){case"ArrowLeft":if(o.preventDefault(),o.stopPropagation(),r==="rtl"){if(t.current){let s=o.target,f=$u(t.current,s.getBoundingClientRect().left,-1);f&&f.focus()}}else n.focusPrevious();break;case"ArrowRight":if(o.preventDefault(),o.stopPropagation(),r==="rtl"){if(t.current){let s=o.target,f=$u(t.current,s.getBoundingClientRect().left,1);f&&f.focus()}}else n.focusNext();break}},i=()=>{var o;if(!t.current)return;let s=(o=window.event)===null||o===void 0?void 0:o.target,f=de(t.current,{tabbable:!0});if(s&&(f.currentNode=s,s=f.previousNode()),!s){let c;do c=f.lastChild(),c&&(s=c);while(c)}for(;s?.hasAttribute("data-placeholder");){let c=f.previousNode();if(c&&c.hasAttribute("data-placeholder"))s=c;else break}s&&s.focus()},{pressProps:l}=Vr({preventFocusOnPress:!0,allowTextSelectionOnPress:!0,onPressStart(o){o.pointerType==="mouse"&&i()},onPress(o){(o.pointerType==="touch"||o.pointerType==="pen")&&i()}});return Z(l,{onKeyDown:u})}function $u(e,t,a){let r=de(e,{tabbable:!0}),n=r.nextNode(),u=null,i=1/0;for(;n;){let o=n.getBoundingClientRect().left-t,s=Math.abs(o);Math.sign(o)===a&&s<i&&(u=n,i=s),n=r.nextNode()}return u}function a3(e){return e&&e.__esModule?e.default:e}const ms=new WeakMap,Xt="__role_"+Date.now(),r3="__focusManager_"+Date.now();function n3(e,t,a){var r;let{isInvalid:n,validationErrors:u,validationDetails:i}=t.displayValidation,{labelProps:l,fieldProps:o,descriptionProps:s,errorMessageProps:f}=fl({...e,labelElementType:"span",isInvalid:n,errorMessage:e.errorMessage||u}),c=d.useRef(null),{focusWithinProps:m}=It({...e,onFocusWithin(y){var $;c.current=t.value,($=e.onFocus)===null||$===void 0||$.call(e,y)},onBlurWithin:y=>{var $;t.confirmPlaceholder(),t.value!==c.current&&t.commitValidation(),($=e.onBlur)===null||$===void 0||$.call(e,y)},onFocusWithinChange:e.onFocusChange}),b=Ie(a3(Ba),"@react-aria/datepicker"),v=t.maxGranularity==="hour"?"selectedTimeDescription":"selectedDateDescription",h=t.maxGranularity==="hour"?"time":"date",p=t.value?b.format(v,{[h]:t.formatValue({month:"long"})}):"",g=Ar(p),C=e[Xt]==="presentation"?o["aria-describedby"]:[g["aria-describedby"],o["aria-describedby"]].filter(Boolean).join(" ")||void 0,F=e[r3],D=d.useMemo(()=>F||Jr(a),[F,a]),A=fs(t,a,e[Xt]==="presentation");ms.set(t,{ariaLabel:e["aria-label"],ariaLabelledBy:[l.id,e["aria-labelledby"]].filter(Boolean).join(" ")||void 0,ariaDescribedBy:C,focusManager:D});let k=d.useRef(e.autoFocus),V;e[Xt]==="presentation"?V={role:"presentation"}:V=Z(o,{role:"group","aria-disabled":e.isDisabled||void 0,"aria-describedby":C}),d.useEffect(()=>{k.current&&D.focusFirst(),k.current=!1},[D]),tc(e.inputRef,t.value,t.setValue),E4({...e,focus(){D.focusFirst()}},t,e.inputRef);let N={type:"hidden",name:e.name,value:((r=t.value)===null||r===void 0?void 0:r.toString())||"",disabled:e.isDisabled};e.validationBehavior==="native"&&(N.type="text",N.hidden=!0,N.required=e.isRequired,N.onChange=()=>{});let L=ue(e);return{labelProps:{...l,onClick:()=>{D.focusFirst()}},fieldProps:Z(L,V,A,m,{onKeyDown(y){e.onKeyDown&&e.onKeyDown(y)},onKeyUp(y){e.onKeyUp&&e.onKeyUp(y)},style:{unicodeBidi:"isolate"}}),inputProps:N,descriptionProps:s,errorMessageProps:f,isInvalid:n,validationErrors:u,validationDetails:i}}function u3(e){return e&&e.__esModule?e.default:e}function i3(e,t,a){let r=xe(),n=xe(),u=xe(),i=Ie(u3(Ba),"@react-aria/datepicker"),{isInvalid:l,validationErrors:o,validationDetails:s}=t.displayValidation,{labelProps:f,fieldProps:c,descriptionProps:m,errorMessageProps:b}=fl({...e,labelElementType:"span",isInvalid:l,errorMessage:e.errorMessage||o}),v=fs(t,a),h=c["aria-labelledby"]||c.id,{locale:p}=ce(),g=t.formatValue(p,{month:"long"}),C=g?i.format("selectedDateDescription",{date:g}):"",F=Ar(C),D=[F["aria-describedby"],c["aria-describedby"]].filter(Boolean).join(" ")||void 0,A=ue(e),k=d.useMemo(()=>Jr(a),[a]),V=d.useRef(!1),{focusWithinProps:N}=It({...e,isDisabled:t.isOpen,onBlurWithin:L=>{let y=document.getElementById(n);if(!y?.contains(L.relatedTarget)){var $,x;V.current=!1,($=e.onBlur)===null||$===void 0||$.call(e,L),(x=e.onFocusChange)===null||x===void 0||x.call(e,!1)}},onFocusWithin:L=>{if(!V.current){var y,$;V.current=!0,(y=e.onFocus)===null||y===void 0||y.call(e,L),($=e.onFocusChange)===null||$===void 0||$.call(e,!0)}}});return{groupProps:Z(A,v,c,F,N,{role:"group","aria-disabled":e.isDisabled||null,"aria-labelledby":h,"aria-describedby":D,onKeyDown(L){t.isOpen||e.onKeyDown&&e.onKeyDown(L)},onKeyUp(L){t.isOpen||e.onKeyUp&&e.onKeyUp(L)}}),labelProps:{...f,onClick:()=>{k.focusFirst()}},fieldProps:{...c,id:u,[Xt]:"presentation","aria-describedby":D,value:t.value,onChange:t.setValue,placeholderValue:e.placeholderValue,hideTimeZone:e.hideTimeZone,hourCycle:e.hourCycle,shouldForceLeadingZeros:e.shouldForceLeadingZeros,granularity:e.granularity,isDisabled:e.isDisabled,isReadOnly:e.isReadOnly,isRequired:e.isRequired,validationBehavior:e.validationBehavior,[xr]:t,autoFocus:e.autoFocus,name:e.name},descriptionProps:m,errorMessageProps:b,buttonProps:{...F,id:r,"aria-haspopup":"dialog","aria-label":i.format("calendar"),"aria-labelledby":`${r} ${h}`,"aria-describedby":D,"aria-expanded":t.isOpen,isDisabled:e.isDisabled||e.isReadOnly,onPress:()=>t.setOpen(!0)},dialogProps:{id:n,"aria-labelledby":`${r} ${h}`},calendarProps:{autoFocus:!0,value:t.dateValue,onChange:t.setDateValue,minValue:e.minValue,maxValue:e.maxValue,isDisabled:e.isDisabled,isReadOnly:e.isReadOnly,isDateUnavailable:e.isDateUnavailable,defaultFocusedValue:t.dateValue?void 0:e.placeholderValue,isInvalid:t.isInvalid,errorMessage:typeof e.errorMessage=="function"?e.errorMessage(t.displayValidation):e.errorMessage||t.displayValidation.validationErrors.join(" ")},isInvalid:l,validationErrors:o,validationDetails:s}}function l3(e){return e&&e.__esModule?e.default:e}function o3(){let{locale:e}=ce(),t=Zu(l3(Ba),"@react-aria/datepicker");return d.useMemo(()=>{try{return new Intl.DisplayNames(e,{type:"dateTimeField"})}catch{return new s3(e,t)}},[e,t])}class s3{of(t){return this.dictionary.getStringForLocale(t,this.locale)}constructor(t,a){this.locale=t,this.dictionary=a}}function d3(e,t,a){let r=d.useRef(""),{locale:n,direction:u}=ce(),i=o3(),{ariaLabel:l,ariaLabelledBy:o,ariaDescribedBy:s,focusManager:f}=ms.get(t),c=e.isPlaceholder?"":e.text,m=d.useMemo(()=>t.dateFormatter.resolvedOptions(),[t.dateFormatter]),b=ye({month:"long",timeZone:m.timeZone}),v=ye({hour:"numeric",hour12:m.hour12,timeZone:m.timeZone});if(e.type==="month"&&!e.isPlaceholder){let R=b.format(t.dateValue);c=R!==c?`${c} – ${R}`:R}else e.type==="hour"&&!e.isPlaceholder&&(c=v.format(t.dateValue));let{spinButtonProps:h}=R4({value:e.value,textValue:c,minValue:e.minValue,maxValue:e.maxValue,isDisabled:t.isDisabled,isReadOnly:t.isReadOnly||!e.isEditable,isRequired:t.isRequired,onIncrement:()=>{r.current="",t.increment(e.type)},onDecrement:()=>{r.current="",t.decrement(e.type)},onIncrementPage:()=>{r.current="",t.incrementPage(e.type)},onDecrementPage:()=>{r.current="",t.decrementPage(e.type)},onIncrementToMax:()=>{r.current="",e.maxValue!==void 0&&t.setSegment(e.type,e.maxValue)},onDecrementToMin:()=>{r.current="",e.minValue!==void 0&&t.setSegment(e.type,e.minValue)}}),p=d.useMemo(()=>new Ei(n,{maximumFractionDigits:0}),[n]),g=()=>{if(e.text===e.placeholder&&f.focusPrevious(),p.isValidPartialNumber(e.text)&&!t.isReadOnly&&!e.isPlaceholder){let R=e.text.slice(0,-1),B=p.parse(R);R=B===0?"":R,R.length===0||B===0?t.clearSegment(e.type):t.setSegment(e.type,B),r.current=R}else e.type==="dayPeriod"&&t.clearSegment(e.type)},C=R=>{if(R.key==="a"&&(lt()?R.metaKey:R.ctrlKey)&&R.preventDefault(),!(R.ctrlKey||R.metaKey||R.shiftKey||R.altKey))switch(R.key){case"Backspace":case"Delete":R.preventDefault(),R.stopPropagation(),g();break}},{startsWith:F}=e4({sensitivity:"base"}),D=ye({hour:"numeric",hour12:!0}),A=d.useMemo(()=>{let R=new Date;return R.setHours(0),D.formatToParts(R).find(B=>B.type==="dayPeriod").value},[D]),k=d.useMemo(()=>{let R=new Date;return R.setHours(12),D.formatToParts(R).find(B=>B.type==="dayPeriod").value},[D]),V=ye({year:"numeric",era:"narrow",timeZone:"UTC"}),N=d.useMemo(()=>{if(e.type!=="era")return[];let R=G(new ee(1,1,1),t.calendar),B=t.calendar.getEras().map(U=>{let ae=R.set({year:1,month:1,day:1,era:U}).toDate("UTC"),pe=V.formatToParts(ae).find(dt=>dt.type==="era").value;return{era:U,formatted:pe}}),j=c3(B.map(U=>U.formatted));if(j)for(let U of B)U.formatted=U.formatted.slice(j);return B},[V,t.calendar,e.type]),L=R=>{if(t.isDisabled||t.isReadOnly)return;let B=r.current+R;switch(e.type){case"dayPeriod":if(F(A,R))t.setSegment("dayPeriod",0);else if(F(k,R))t.setSegment("dayPeriod",12);else break;f.focusNext();break;case"era":{let j=N.find(U=>F(U.formatted,R));j&&(t.setSegment("era",j.era),f.focusNext());break}case"day":case"hour":case"minute":case"second":case"month":case"year":{if(!p.isValidPartialNumber(B))return;let j=p.parse(B),U=j,ae=e.minValue===0;if(e.type==="hour"&&t.dateFormatter.resolvedOptions().hour12){switch(t.dateFormatter.resolvedOptions().hourCycle){case"h11":j>11&&(U=p.parse(R));break;case"h12":ae=!1,j>12&&(U=p.parse(R));break}e.value!==void 0&&e.value>=12&&j>1&&(j+=12)}else e.maxValue!==void 0&&j>e.maxValue&&(U=p.parse(R));if(isNaN(j))return;let he=U!==0||ae;he&&t.setSegment(e.type,U),e.maxValue!==void 0&&(+(j+"0")>e.maxValue||B.length>=String(e.maxValue).length)?(r.current="",he&&f.focusNext()):r.current=B;break}}},y=()=>{r.current="",a.current&&Su(a.current,{containingElement:Da(a.current)});let R=window.getSelection();R?.collapse(a.current)},$=d.useRef(typeof document<"u"?document:null);Ma($,"selectionchange",()=>{var R;let B=window.getSelection();B?.anchorNode&&(!((R=a.current)===null||R===void 0)&&R.contains(B?.anchorNode))&&B.collapse(a.current)});let x=d.useRef("");Ma(a,"beforeinput",R=>{if(a.current)switch(R.preventDefault(),R.inputType){case"deleteContentBackward":case"deleteContentForward":p.isValidPartialNumber(e.text)&&!t.isReadOnly&&g();break;case"insertCompositionText":x.current=a.current.textContent,a.current.textContent=a.current.textContent;break;default:R.data!=null&&L(R.data);break}}),Ma(a,"input",R=>{let{inputType:B,data:j}=R;switch(B){case"insertCompositionText":a.current&&(a.current.textContent=x.current),j!=null&&(F(A,j)||F(k,j))&&L(j);break}}),_(()=>{let R=a.current;return()=>{document.activeElement===R&&(f.focusPrevious()||f.focusNext())}},[a,f]);let w=ha()||e.type==="timeZoneName"?{role:"textbox","aria-valuemax":null,"aria-valuemin":null,"aria-valuetext":null,"aria-valuenow":null}:{},E=d.useMemo(()=>t.segments.find(R=>R.isEditable),[t.segments]);e!==E&&!t.isInvalid&&(s=void 0);let z=xe(),P=!t.isDisabled&&!t.isReadOnly&&e.isEditable,I=e.type==="literal"?"":i.of(e.type),O=Mt({"aria-label":`${I}${l?`, ${l}`:""}${o?", ":""}`,"aria-labelledby":o});if(e.type==="literal")return{segmentProps:{"aria-hidden":!0}};let H={caretColor:"transparent"};if(u==="rtl"){H.unicodeBidi="embed";let R=m[e.type];(R==="numeric"||R==="2-digit")&&(H.direction="ltr")}return{segmentProps:Z(h,O,{id:z,...w,"aria-invalid":t.isInvalid?"true":void 0,"aria-describedby":s,"aria-readonly":t.isReadOnly||!e.isEditable?"true":void 0,"data-placeholder":e.isPlaceholder||void 0,contentEditable:P,suppressContentEditableWarning:P,spellCheck:P?"false":void 0,autoCorrect:P?"off":void 0,[parseInt(S.version,10)>=17?"enterKeyHint":"enterkeyhint"]:P?"next":void 0,inputMode:t.isDisabled||e.type==="dayPeriod"||e.type==="era"||!P?void 0:"numeric",tabIndex:t.isDisabled?void 0:0,onKeyDown:C,onFocus:y,style:H,onPointerDown(R){R.stopPropagation()},onMouseDown(R){R.stopPropagation()}})}}function c3(e){e.sort();let t=e[0],a=e[e.length-1];for(let r=0;r<t.length;r++)if(t[r]!==a[r])return r;return 0}function f3(e,t){let{role:a="dialog"}=e,r=ta();r=e["aria-label"]?void 0:r;let n=d.useRef(!1);return d.useEffect(()=>{if(t.current&&!t.current.contains(document.activeElement)){Bt(t.current);let u=setTimeout(()=>{document.activeElement===t.current&&(n.current=!0,t.current&&(t.current.blur(),Bt(t.current)),n.current=!1)},500);return()=>{clearTimeout(u)}}},[t]),t3(),{dialogProps:{...ue(e,{labelable:!0}),role:a,tabIndex:-1,"aria-labelledby":e["aria-labelledby"]||r,onBlur:u=>{n.current&&u.stopPropagation()}},titleProps:{id:r}}}const $s=d.createContext({});let m3=e=>{let{onHoverStart:t,onHoverChange:a,onHoverEnd:r,...n}=e;return n};const $3=Mu(function(t,a){[t,a]=Fe(t,a,$s);let{hoverProps:r,isHovered:n}=Vt(t),{isFocused:u,isFocusVisible:i,focusProps:l}=st({isTextInput:!0,autoFocus:t.autoFocus}),o=!!t["aria-invalid"]&&t["aria-invalid"]!=="false",s=Se({...t,values:{isHovered:n,isFocused:u,isFocusVisible:i,isDisabled:t.disabled||!1,isInvalid:o},defaultClassName:"react-aria-Input"});return S.createElement("input",{...Z(m3(t),l,r),...s,ref:a,"data-focused":u||void 0,"data-disabled":t.disabled||void 0,"data-hovered":n||void 0,"data-focus-visible":i||void 0,"data-invalid":o||void 0})}),bs=d.createContext({}),b3=d.createContext(null),v3=new Set(["form","formAction","formEncType","formMethod","formNoValidate","formTarget","name","value"]),jt=d.createContext({}),Pr=Mu(function(t,a){[t,a]=Fe(t,a,jt),t=h3(t);let r=t,{isPending:n}=r,{buttonProps:u,isPressed:i}=t4(t,a),{focusProps:l,isFocused:o,isFocusVisible:s}=st(t),{hoverProps:f,isHovered:c}=Vt({...t,isDisabled:t.isDisabled||n}),m={isHovered:c,isPressed:(r.isPressed||i)&&!n,isFocused:o,isFocusVisible:s,isDisabled:t.isDisabled||!1,isPending:n??!1},b=Se({...t,values:m,defaultClassName:"react-aria-Button"}),v=xe(u.id),h=xe(),p=u["aria-labelledby"];n&&(p?p=`${p} ${h}`:u["aria-label"]&&(p=`${v} ${h}`));let g=d.useRef(n);return d.useEffect(()=>{let C={"aria-labelledby":p||v};(!g.current&&o&&n||g.current&&o&&!n)&&At(C,"assertive"),g.current=n},[n,o,p,v]),S.createElement("button",{...ue(t,{propNames:v3}),...Z(u,l,f),...b,type:u.type==="submit"&&n?"button":u.type,id:v,ref:a,"aria-labelledby":p,slot:t.slot||void 0,"aria-disabled":n?"true":u["aria-disabled"],"data-disabled":t.isDisabled||void 0,"data-pressed":m.isPressed||void 0,"data-hovered":c||void 0,"data-focused":o||void 0,"data-pending":n||void 0,"data-focus-visible":s||void 0},S.createElement(b3.Provider,{value:{id:h}},b.children))});function h3(e){return e.isPending&&(e.onPress=void 0,e.onPressStart=void 0,e.onPressEnd=void 0,e.onPressChange=void 0,e.onPressUp=void 0,e.onKeyDown=void 0,e.onKeyUp=void 0,e.onClick=void 0,e.href=void 0),e}const Xr=d.createContext({}),p3=d.createContext(null);function qa(e,t,a){return t!=null&&e.compare(t)<0||a!=null&&e.compare(a)>0}function bu(e,t,a,r,n){let u={};for(let l in t)u[l]=Math.floor(t[l]/2),u[l]>0&&t[l]%2===0&&u[l]--;let i=_e(e,t,a).subtract(u);return Tt(e,i,t,a,r,n)}function _e(e,t,a,r,n){let u=e;return t.years?u=qc(e):t.months?u=We(e):t.weeks&&(u=St(e,a)),Tt(e,u,t,a,r,n)}function Br(e,t,a,r,n){let u={...t};u.days?u.days--:u.weeks?u.weeks--:u.months?u.months--:u.years&&u.years--;let i=_e(e,t,a).subtract(u);return Tt(e,i,t,a,r,n)}function Tt(e,t,a,r,n,u){if(n&&e.compare(n)>=0){let i=Xu(t,_e(Ee(n),a,r));i&&(t=i)}if(u&&e.compare(u)<=0){let i=Qu(t,Br(Ee(u),a,r));i&&(t=i)}return t}function Le(e,t,a){if(t){let r=Xu(e,Ee(t));r&&(e=r)}if(a){let r=Qu(e,Ee(a));r&&(e=r)}return e}function D3(e,t,a){if(!a)return e;for(;e.compare(t)>=0&&a(e);)e=e.subtract({days:1});return e.compare(t)>=0?e:null}function y3(e){let t=d.useMemo(()=>new Be(e.locale),[e.locale]),a=d.useMemo(()=>t.resolvedOptions(),[t]),{locale:r,createCalendar:n,visibleDuration:u={months:1},minValue:i,maxValue:l,selectionAlignment:o,isDateUnavailable:s,pageBehavior:f="visible",firstDayOfWeek:c}=e,m=d.useMemo(()=>n(a.calendar),[n,a.calendar]);var b;let[v,h]=Pt(e.value,(b=e.defaultValue)!==null&&b!==void 0?b:null,e.onChange),p=d.useMemo(()=>v?G(Ee(v),m):null,[v,m]),g=d.useMemo(()=>v&&"timeZone"in v?v.timeZone:a.timeZone,[v,a.timeZone]),C=d.useMemo(()=>e.focusedValue?Le(G(Ee(e.focusedValue),m),i,l):void 0,[e.focusedValue,m,i,l]),F=d.useMemo(()=>Le(e.defaultFocusedValue?G(Ee(e.defaultFocusedValue),m):p||G(Ca(g),m),i,l),[e.defaultFocusedValue,p,g,m,i,l]),[D,A]=Pt(C,F,e.onFocusChange),[k,V]=d.useState(()=>{switch(o){case"start":return _e(D,u,r,i,l);case"end":return Br(D,u,r,i,l);case"center":default:return bu(D,u,r,i,l)}}),[N,L]=d.useState(e.autoFocus||!1),y=d.useMemo(()=>{let B={...u};return B.days?B.days--:B.days=-1,k.add(B)},[k,u]),[$,x]=d.useState(m);if(!Ea(m,$)){let B=G(D,m);V(bu(B,u,r,i,l)),A(B),x(m)}qa(D,i,l)?A(Le(D,i,l)):D.compare(k)<0?V(Br(D,u,r,i,l)):D.compare(y)>0&&V(_e(D,u,r,i,l));function w(B){B=Le(B,i,l),A(B)}function E(B){if(!e.isDisabled&&!e.isReadOnly){let j=B;if(j===null){h(null);return}if(j=Le(j,i,l),j=D3(j,k,s),!j)return;j=G(j,v?.calendar||new fe),v&&"hour"in v?h(v.set(j)):h(j)}}let z=d.useMemo(()=>p?s&&s(p)?!0:qa(p,i,l):!1,[p,s,i,l]),P=e.isInvalid||e.validationState==="invalid"||z,I=P?"invalid":null,O=d.useMemo(()=>f==="visible"?u:Qa(u),[f,u]);var H,R;return{isDisabled:(H=e.isDisabled)!==null&&H!==void 0?H:!1,isReadOnly:(R=e.isReadOnly)!==null&&R!==void 0?R:!1,value:p,setValue:E,visibleRange:{start:k,end:y},minValue:i,maxValue:l,focusedDate:D,timeZone:g,validationState:I,isValueInvalid:P,setFocusedDate(B){w(B),L(!0)},focusNextDay(){w(D.add({days:1}))},focusPreviousDay(){w(D.subtract({days:1}))},focusNextRow(){u.days?this.focusNextPage():(u.weeks||u.months||u.years)&&w(D.add({weeks:1}))},focusPreviousRow(){u.days?this.focusPreviousPage():(u.weeks||u.months||u.years)&&w(D.subtract({weeks:1}))},focusNextPage(){let B=k.add(O);A(Le(D.add(O),i,l)),V(_e(Tt(D,B,O,r,i,l),O,r))},focusPreviousPage(){let B=k.subtract(O);A(Le(D.subtract(O),i,l)),V(_e(Tt(D,B,O,r,i,l),O,r))},focusSectionStart(){u.days?w(k):u.weeks?w(St(D,r)):(u.months||u.years)&&w(We(D))},focusSectionEnd(){u.days?w(y):u.weeks?w(ef(D,r)):(u.months||u.years)&&w(ua(D))},focusNextSection(B){if(!B&&!u.days){w(D.add(Qa(u)));return}u.days?this.focusNextPage():u.weeks?w(D.add({months:1})):(u.months||u.years)&&w(D.add({years:1}))},focusPreviousSection(B){if(!B&&!u.days){w(D.subtract(Qa(u)));return}u.days?this.focusPreviousPage():u.weeks?w(D.subtract({months:1})):(u.months||u.years)&&w(D.subtract({years:1}))},selectFocusedDate(){s&&s(D)||E(D)},selectDate(B){E(B)},isFocused:N,setFocused:L,isInvalid(B){return qa(B,i,l)},isSelected(B){return p!=null&&q(B,p)&&!this.isCellDisabled(B)&&!this.isCellUnavailable(B)},isCellFocused(B){return N&&D&&q(B,D)},isCellDisabled(B){return e.isDisabled||B.compare(k)<0||B.compare(y)>0||this.isInvalid(B)},isCellUnavailable(B){return e.isDateUnavailable?e.isDateUnavailable(B):!1},isPreviousVisibleRangeInvalid(){let B=k.subtract({days:1});return q(B,k)||this.isInvalid(B)},isNextVisibleRangeInvalid(){let B=y.add({days:1});return q(B,y)||this.isInvalid(B)},getDatesInWeek(B,j=k){let U=j.add({weeks:B}),ae=[];U=St(U,r,c);let he=jr(U,r,c);for(let pe=0;pe<he;pe++)ae.push(null);for(;ae.length<7;){ae.push(U);let pe=U.add({days:1});if(q(U,pe))break;U=pe}for(;ae.length<7;)ae.push(null);return ae}}}function Qa(e){let t={...e};for(let a in e)t[a]=1;return t}function vs(e){let[t,a]=Pt(e.isOpen,e.defaultOpen||!1,e.onOpenChange);const r=d.useCallback(()=>{a(!0)},[a]),n=d.useCallback(()=>{a(!1)},[a]),u=d.useCallback(()=>{a(!t)},[a,t]);return{isOpen:t,setOpen:a,open:r,close:n,toggle:u}}var hs={};hs={rangeOverflow:e=>`يجب أن تكون القيمة ${e.maxValue} أو قبل ذلك.`,rangeReversed:"تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء.",rangeUnderflow:e=>`يجب أن تكون القيمة ${e.minValue} أو بعد ذلك.`,unavailableDate:"البيانات المحددة غير متاحة."};var ps={};ps={rangeOverflow:e=>`Стойността трябва да е ${e.maxValue} или по-ранна.`,rangeReversed:"Началната дата трябва да е преди крайната.",rangeUnderflow:e=>`Стойността трябва да е ${e.minValue} или по-късно.`,unavailableDate:"Избраната дата не е налична."};var Ds={};Ds={rangeOverflow:e=>`Hodnota musí být ${e.maxValue} nebo dřívější.`,rangeReversed:"Datum zahájení musí předcházet datu ukončení.",rangeUnderflow:e=>`Hodnota musí být ${e.minValue} nebo pozdější.`,unavailableDate:"Vybrané datum není k dispozici."};var ys={};ys={rangeOverflow:e=>`Værdien skal være ${e.maxValue} eller tidligere.`,rangeReversed:"Startdatoen skal være før slutdatoen.",rangeUnderflow:e=>`Værdien skal være ${e.minValue} eller nyere.`,unavailableDate:"Den valgte dato er ikke tilgængelig."};var gs={};gs={rangeOverflow:e=>`Der Wert muss ${e.maxValue} oder früher sein.`,rangeReversed:"Das Anfangsdatum muss vor dem Enddatum liegen.",rangeUnderflow:e=>`Der Wert muss ${e.minValue} oder später sein.`,unavailableDate:"Das ausgewählte Datum ist nicht verfügbar."};var xs={};xs={rangeOverflow:e=>`Η τιμή πρέπει να είναι ${e.maxValue} ή παλαιότερη.`,rangeReversed:"Η ημερομηνία έναρξης πρέπει να είναι πριν από την ημερομηνία λήξης.",rangeUnderflow:e=>`Η τιμή πρέπει να είναι ${e.minValue} ή μεταγενέστερη.`,unavailableDate:"Η επιλεγμένη ημερομηνία δεν είναι διαθέσιμη."};var Es={};Es={rangeUnderflow:e=>`Value must be ${e.minValue} or later.`,rangeOverflow:e=>`Value must be ${e.maxValue} or earlier.`,rangeReversed:"Start date must be before end date.",unavailableDate:"Selected date unavailable."};var Cs={};Cs={rangeOverflow:e=>`El valor debe ser ${e.maxValue} o anterior.`,rangeReversed:"La fecha de inicio debe ser anterior a la fecha de finalización.",rangeUnderflow:e=>`El valor debe ser ${e.minValue} o posterior.`,unavailableDate:"Fecha seleccionada no disponible."};var ws={};ws={rangeOverflow:e=>`Väärtus peab olema ${e.maxValue} või varasem.`,rangeReversed:"Alguskuupäev peab olema enne lõppkuupäeva.",rangeUnderflow:e=>`Väärtus peab olema ${e.minValue} või hilisem.`,unavailableDate:"Valitud kuupäev pole saadaval."};var Ps={};Ps={rangeOverflow:e=>`Arvon on oltava ${e.maxValue} tai sitä aikaisempi.`,rangeReversed:"Aloituspäivän on oltava ennen lopetuspäivää.",rangeUnderflow:e=>`Arvon on oltava ${e.minValue} tai sitä myöhäisempi.`,unavailableDate:"Valittu päivämäärä ei ole käytettävissä."};var Bs={};Bs={rangeOverflow:e=>`La valeur doit être ${e.maxValue} ou antérieure.`,rangeReversed:"La date de début doit être antérieure à la date de fin.",rangeUnderflow:e=>`La valeur doit être ${e.minValue} ou ultérieure.`,unavailableDate:"La date sélectionnée n’est pas disponible."};var Ss={};Ss={rangeOverflow:e=>`הערך חייב להיות ${e.maxValue} או מוקדם יותר.`,rangeReversed:"תאריך ההתחלה חייב להיות לפני תאריך הסיום.",rangeUnderflow:e=>`הערך חייב להיות ${e.minValue} או מאוחר יותר.`,unavailableDate:"התאריך הנבחר אינו זמין."};var Fs={};Fs={rangeOverflow:e=>`Vrijednost mora biti ${e.maxValue} ili ranije.`,rangeReversed:"Datum početka mora biti prije datuma završetka.",rangeUnderflow:e=>`Vrijednost mora biti ${e.minValue} ili kasnije.`,unavailableDate:"Odabrani datum nije dostupan."};var Rs={};Rs={rangeOverflow:e=>`Az értéknek ${e.maxValue} vagy korábbinak kell lennie.`,rangeReversed:"A kezdő dátumnak a befejező dátumnál korábbinak kell lennie.",rangeUnderflow:e=>`Az értéknek ${e.minValue} vagy későbbinek kell lennie.`,unavailableDate:"A kiválasztott dátum nem érhető el."};var ks={};ks={rangeOverflow:e=>`Il valore deve essere ${e.maxValue} o precedente.`,rangeReversed:"La data di inizio deve essere antecedente alla data di fine.",rangeUnderflow:e=>`Il valore deve essere ${e.minValue} o successivo.`,unavailableDate:"Data selezionata non disponibile."};var As={};As={rangeOverflow:e=>`値は ${e.maxValue} 以下にする必要があります。`,rangeReversed:"開始日は終了日より前にする必要があります。",rangeUnderflow:e=>`値は ${e.minValue} 以上にする必要があります。`,unavailableDate:"選択した日付は使用できません。"};var Ts={};Ts={rangeOverflow:e=>`값은 ${e.maxValue} 이전이어야 합니다.`,rangeReversed:"시작일은 종료일 이전이어야 합니다.",rangeUnderflow:e=>`값은 ${e.minValue} 이상이어야 합니다.`,unavailableDate:"선택한 날짜를 사용할 수 없습니다."};var Ms={};Ms={rangeOverflow:e=>`Reikšmė turi būti ${e.maxValue} arba ankstesnė.`,rangeReversed:"Pradžios data turi būti ankstesnė nei pabaigos data.",rangeUnderflow:e=>`Reikšmė turi būti ${e.minValue} arba naujesnė.`,unavailableDate:"Pasirinkta data nepasiekiama."};var Ns={};Ns={rangeOverflow:e=>`Vērtībai ir jābūt ${e.maxValue} vai agrākai.`,rangeReversed:"Sākuma datumam ir jābūt pirms beigu datuma.",rangeUnderflow:e=>`Vērtībai ir jābūt ${e.minValue} vai vēlākai.`,unavailableDate:"Atlasītais datums nav pieejams."};var Is={};Is={rangeOverflow:e=>`Verdien må være ${e.maxValue} eller tidligere.`,rangeReversed:"Startdatoen må være før sluttdatoen.",rangeUnderflow:e=>`Verdien må være ${e.minValue} eller senere.`,unavailableDate:"Valgt dato utilgjengelig."};var Vs={};Vs={rangeOverflow:e=>`Waarde moet ${e.maxValue} of eerder zijn.`,rangeReversed:"De startdatum moet voor de einddatum liggen.",rangeUnderflow:e=>`Waarde moet ${e.minValue} of later zijn.`,unavailableDate:"Geselecteerde datum niet beschikbaar."};var Ls={};Ls={rangeOverflow:e=>`Wartość musi mieć wartość ${e.maxValue} lub wcześniejszą.`,rangeReversed:"Data rozpoczęcia musi być wcześniejsza niż data zakończenia.",rangeUnderflow:e=>`Wartość musi mieć wartość ${e.minValue} lub późniejszą.`,unavailableDate:"Wybrana data jest niedostępna."};var Os={};Os={rangeOverflow:e=>`O valor deve ser ${e.maxValue} ou anterior.`,rangeReversed:"A data inicial deve ser anterior à data final.",rangeUnderflow:e=>`O valor deve ser ${e.minValue} ou posterior.`,unavailableDate:"Data selecionada indisponível."};var js={};js={rangeOverflow:e=>`O valor tem de ser ${e.maxValue} ou anterior.`,rangeReversed:"A data de início deve ser anterior à data de fim.",rangeUnderflow:e=>`O valor tem de ser ${e.minValue} ou posterior.`,unavailableDate:"Data selecionada indisponível."};var zs={};zs={rangeOverflow:e=>`Valoarea trebuie să fie ${e.maxValue} sau anterioară.`,rangeReversed:"Data de început trebuie să fie anterioară datei de sfârșit.",rangeUnderflow:e=>`Valoarea trebuie să fie ${e.minValue} sau ulterioară.`,unavailableDate:"Data selectată nu este disponibilă."};var Hs={};Hs={rangeOverflow:e=>`Значение должно быть не позже ${e.maxValue}.`,rangeReversed:"Дата начала должна предшествовать дате окончания.",rangeUnderflow:e=>`Значение должно быть не раньше ${e.minValue}.`,unavailableDate:"Выбранная дата недоступна."};var _s={};_s={rangeOverflow:e=>`Hodnota musí byť ${e.maxValue} alebo skoršia.`,rangeReversed:"Dátum začiatku musí byť skorší ako dátum konca.",rangeUnderflow:e=>`Hodnota musí byť ${e.minValue} alebo neskoršia.`,unavailableDate:"Vybratý dátum je nedostupný."};var Ks={};Ks={rangeOverflow:e=>`Vrednost mora biti ${e.maxValue} ali starejša.`,rangeReversed:"Začetni datum mora biti pred končnim datumom.",rangeUnderflow:e=>`Vrednost mora biti ${e.minValue} ali novejša.`,unavailableDate:"Izbrani datum ni na voljo."};var Us={};Us={rangeOverflow:e=>`Vrednost mora da bude ${e.maxValue} ili starija.`,rangeReversed:"Datum početka mora biti pre datuma završetka.",rangeUnderflow:e=>`Vrednost mora da bude ${e.minValue} ili novija.`,unavailableDate:"Izabrani datum nije dostupan."};var Ws={};Ws={rangeOverflow:e=>`Värdet måste vara ${e.maxValue} eller tidigare.`,rangeReversed:"Startdatumet måste vara före slutdatumet.",rangeUnderflow:e=>`Värdet måste vara ${e.minValue} eller senare.`,unavailableDate:"Det valda datumet är inte tillgängligt."};var Zs={};Zs={rangeOverflow:e=>`Değer, ${e.maxValue} veya öncesi olmalıdır.`,rangeReversed:"Başlangıç tarihi bitiş tarihinden önce olmalıdır.",rangeUnderflow:e=>`Değer, ${e.minValue} veya sonrası olmalıdır.`,unavailableDate:"Seçilen tarih kullanılamıyor."};var Gs={};Gs={rangeOverflow:e=>`Значення має бути не пізніше ${e.maxValue}.`,rangeReversed:"Дата початку має передувати даті завершення.",rangeUnderflow:e=>`Значення має бути не раніше ${e.minValue}.`,unavailableDate:"Вибрана дата недоступна."};var Ys={};Ys={rangeOverflow:e=>`值必须是 ${e.maxValue} 或更早日期。`,rangeReversed:"开始日期必须早于结束日期。",rangeUnderflow:e=>`值必须是 ${e.minValue} 或更晚日期。`,unavailableDate:"所选日期不可用。"};var Js={};Js={rangeOverflow:e=>`值必須是 ${e.maxValue} 或更早。`,rangeReversed:"開始日期必須在結束日期之前。",rangeUnderflow:e=>`值必須是 ${e.minValue} 或更晚。`,unavailableDate:"所選日期無法使用。"};var qs={};qs={"ar-AE":hs,"bg-BG":ps,"cs-CZ":Ds,"da-DK":ys,"de-DE":gs,"el-GR":xs,"en-US":Es,"es-ES":Cs,"et-EE":ws,"fi-FI":Ps,"fr-FR":Bs,"he-IL":Ss,"hr-HR":Fs,"hu-HU":Rs,"it-IT":ks,"ja-JP":As,"ko-KR":Ts,"lt-LT":Ms,"lv-LV":Ns,"nb-NO":Is,"nl-NL":Vs,"pl-PL":Ls,"pt-BR":Os,"pt-PT":js,"ro-RO":zs,"ru-RU":Hs,"sk-SK":_s,"sl-SI":Ks,"sr-SP":Us,"sv-SE":Ws,"tr-TR":Zs,"uk-UA":Gs,"zh-CN":Ys,"zh-TW":Js};function g3(e){return e&&e.__esModule?e.default:e}const x3=new Ye(g3(qs));function E3(){return typeof navigator<"u"&&(navigator.language||navigator.userLanguage)||"en-US"}function Qs(e,t,a,r,n){let u=e!=null&&a!=null&&e.compare(a)>0,i=e!=null&&t!=null&&e.compare(t)<0,l=e!=null&&r?.(e)||!1,o=u||i||l,s=[];if(o){let f=E3(),c=Ye.getGlobalDictionaryForPackage("@react-stately/datepicker")||x3,m=new Wu(f,c),b=new Be(f,it({},n)),v=b.resolvedOptions().timeZone;i&&t!=null&&s.push(m.format("rangeUnderflow",{minValue:b.format(t.toDate(v))})),u&&a!=null&&s.push(m.format("rangeOverflow",{maxValue:b.format(a.toDate(v))})),l&&s.push(m.format("unavailableDate"))}return{isInvalid:o,validationErrors:s,validationDetails:{badInput:l,customError:!1,patternMismatch:!1,rangeOverflow:u,rangeUnderflow:i,stepMismatch:!1,tooLong:!1,tooShort:!1,typeMismatch:!1,valueMissing:!1,valid:!o}}}const C3={year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"2-digit",second:"2-digit"},w3={year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"};function it(e,t){e={...t.shouldForceLeadingZeros?w3:C3,...e};let r=t.granularity||"minute",n=Object.keys(e);var u;let i=n.indexOf((u=t.maxGranularity)!==null&&u!==void 0?u:"year");i<0&&(i=0);let l=n.indexOf(r);if(l<0&&(l=2),i>l)throw new Error("maxGranularity must be greater than granularity");let o=n.slice(i,l+1).reduce((f,c)=>(f[c]=e[c],f),{});return t.hourCycle!=null&&(o.hour12=t.hourCycle===12),o.timeZone=t.timeZone||"UTC",(r==="hour"||r==="minute"||r==="second")&&t.timeZone&&!t.hideTimeZone&&(o.timeZoneName="short"),t.showEra&&i===0&&(o.era="short"),o}function vu(e){return e&&"hour"in e?e:new Wr}function Xs(e,t){if(e===null)return null;if(e)return G(e,t)}function vt(e,t,a,r){if(e)return Xs(e,a);let n=G(Yu(r??wa()).set({hour:0,minute:0,second:0,millisecond:0}),a);return t==="year"||t==="month"||t==="day"?Ee(n):r?n:Me(n)}function ed(e,t){let a=e&&"timeZone"in e?e.timeZone:void 0,r=e&&"minute"in e?"minute":"day";if(e&&t&&!(t in e))throw new Error("Invalid granularity "+t+" for value "+e.toString());let[n,u]=d.useState([r,a]);e&&(n[0]!==r||n[1]!==a)&&u([r,a]),t||(t=e?r:n[0]);let i=e?a:n[1];return[t,i]}function P3(e){let t=vs(e),[a,r]=Pt(e.value,e.defaultValue||null,e.onChange),n=a||e.placeholderValue||null,[u,i]=ed(n,e.granularity),l=a!=null?a.toDate(i??"UTC"):null,o=u==="hour"||u==="minute"||u==="second";var s;let f=(s=e.shouldCloseOnSelect)!==null&&s!==void 0?s:!0,[c,m]=d.useState(null),[b,v]=d.useState(null);if(a&&(c=a,"hour"in a&&(b=a)),n&&!(u in n))throw new Error("Invalid granularity "+u+" for value "+n.toString());let h=a?.calendar.identifier==="gregory"&&a.era==="BC",p=d.useMemo(()=>({granularity:u,timeZone:i,hideTimeZone:e.hideTimeZone,hourCycle:e.hourCycle,shouldForceLeadingZeros:e.shouldForceLeadingZeros,showEra:h}),[u,e.hourCycle,e.shouldForceLeadingZeros,i,e.hideTimeZone,h]),{minValue:g,maxValue:C,isDateUnavailable:F}=e,D=d.useMemo(()=>Qs(a,g,C,F,p),[a,g,C,F,p]),A=cl({...e,value:a,builtinValidation:D}),k=A.displayValidation.isInvalid,V=e.validationState||(k?"invalid":null),N=($,x)=>{r("timeZone"in x?x.set(Ee($)):Me($,x)),m(null),v(null),A.commitValidation()};return{...A,value:a,setValue:r,dateValue:c,timeValue:b,setDateValue:$=>{let x=typeof f=="function"?f():f;o?b||x?N($,b||vu(e.defaultValue||e.placeholderValue)):m($):(r($),A.commitValidation()),x&&t.setOpen(!1)},setTimeValue:$=>{c&&$?N(c,$):v($)},granularity:u,hasTime:o,...t,setOpen($){!$&&!a&&c&&o&&N(c,b||vu(e.defaultValue||e.placeholderValue)),t.setOpen($)},validationState:V,isInvalid:k,formatValue($,x){if(!l)return"";let w=it(x,p);return new Be($,w).format(l)},getDateFormatter($,x){let w={...p,...x},E=it({},w);return new Be($,E)}}}const B3=new Ye({ach:{year:"mwaka",month:"dwe",day:"nino"},af:{year:"jjjj",month:"mm",day:"dd"},am:{year:"ዓዓዓዓ",month:"ሚሜ",day:"ቀቀ"},an:{year:"aaaa",month:"mm",day:"dd"},ar:{year:"سنة",month:"شهر",day:"يوم"},ast:{year:"aaaa",month:"mm",day:"dd"},az:{year:"iiii",month:"aa",day:"gg"},be:{year:"гггг",month:"мм",day:"дд"},bg:{year:"гггг",month:"мм",day:"дд"},bn:{year:"yyyy",month:"মিমি",day:"dd"},br:{year:"bbbb",month:"mm",day:"dd"},bs:{year:"gggg",month:"mm",day:"dd"},ca:{year:"aaaa",month:"mm",day:"dd"},cak:{year:"jjjj",month:"ii",day:"q'q'"},ckb:{year:"ساڵ",month:"مانگ",day:"ڕۆژ"},cs:{year:"rrrr",month:"mm",day:"dd"},cy:{year:"bbbb",month:"mm",day:"dd"},da:{year:"åååå",month:"mm",day:"dd"},de:{year:"jjjj",month:"mm",day:"tt"},dsb:{year:"llll",month:"mm",day:"źź"},el:{year:"εεεε",month:"μμ",day:"ηη"},en:{year:"yyyy",month:"mm",day:"dd"},eo:{year:"jjjj",month:"mm",day:"tt"},es:{year:"aaaa",month:"mm",day:"dd"},et:{year:"aaaa",month:"kk",day:"pp"},eu:{year:"uuuu",month:"hh",day:"ee"},fa:{year:"سال",month:"ماه",day:"روز"},ff:{year:"hhhh",month:"ll",day:"ññ"},fi:{year:"vvvv",month:"kk",day:"pp"},fr:{year:"aaaa",month:"mm",day:"jj"},fy:{year:"jjjj",month:"mm",day:"dd"},ga:{year:"bbbb",month:"mm",day:"ll"},gd:{year:"bbbb",month:"mm",day:"ll"},gl:{year:"aaaa",month:"mm",day:"dd"},he:{year:"שנה",month:"חודש",day:"יום"},hr:{year:"gggg",month:"mm",day:"dd"},hsb:{year:"llll",month:"mm",day:"dd"},hu:{year:"éééé",month:"hh",day:"nn"},ia:{year:"aaaa",month:"mm",day:"dd"},id:{year:"tttt",month:"bb",day:"hh"},it:{year:"aaaa",month:"mm",day:"gg"},ja:{year:"年",month:"月",day:"日"},ka:{year:"წწწწ",month:"თთ",day:"რრ"},kk:{year:"жжжж",month:"аа",day:"кк"},kn:{year:"ವವವವ",month:"ಮಿಮೀ",day:"ದಿದಿ"},ko:{year:"연도",month:"월",day:"일"},lb:{year:"jjjj",month:"mm",day:"dd"},lo:{year:"ປປປປ",month:"ດດ",day:"ວວ"},lt:{year:"mmmm",month:"mm",day:"dd"},lv:{year:"gggg",month:"mm",day:"dd"},meh:{year:"aaaa",month:"mm",day:"dd"},ml:{year:"വർഷം",month:"മാസം",day:"തീയതി"},ms:{year:"tttt",month:"mm",day:"hh"},nl:{year:"jjjj",month:"mm",day:"dd"},nn:{year:"åååå",month:"mm",day:"dd"},no:{year:"åååå",month:"mm",day:"dd"},oc:{year:"aaaa",month:"mm",day:"jj"},pl:{year:"rrrr",month:"mm",day:"dd"},pt:{year:"aaaa",month:"mm",day:"dd"},rm:{year:"oooo",month:"mm",day:"dd"},ro:{year:"aaaa",month:"ll",day:"zz"},ru:{year:"гггг",month:"мм",day:"дд"},sc:{year:"aaaa",month:"mm",day:"dd"},scn:{year:"aaaa",month:"mm",day:"jj"},sk:{year:"rrrr",month:"mm",day:"dd"},sl:{year:"llll",month:"mm",day:"dd"},sr:{year:"гггг",month:"мм",day:"дд"},sv:{year:"åååå",month:"mm",day:"dd"},szl:{year:"rrrr",month:"mm",day:"dd"},tg:{year:"сссс",month:"мм",day:"рр"},th:{year:"ปปปป",month:"ดด",day:"วว"},tr:{year:"yyyy",month:"aa",day:"gg"},uk:{year:"рррр",month:"мм",day:"дд"},"zh-CN":{year:"年",month:"月",day:"日"},"zh-TW":{year:"年",month:"月",day:"日"}},"en");function S3(e,t,a){return e==="era"||e==="dayPeriod"?t:e==="year"||e==="month"||e==="day"?B3.getStringForLocale(e,a):"––"}const ea={year:!0,month:!0,day:!0,hour:!0,minute:!0,second:!0,dayPeriod:!0,era:!0},hu={year:5,month:2,day:7,hour:2,minute:15,second:15},td={dayperiod:"dayPeriod",relatedYear:"year",yearName:"literal",unknown:"literal"};function F3(e){let{locale:t,createCalendar:a,hideTimeZone:r,isDisabled:n=!1,isReadOnly:u=!1,isRequired:i=!1,minValue:l,maxValue:o,isDateUnavailable:s}=e,f=e.value||e.defaultValue||e.placeholderValue||null,[c,m]=ed(f,e.granularity),b=m||"UTC";if(f&&!(c in f))throw new Error("Invalid granularity "+c+" for value "+f.toString());let v=d.useMemo(()=>new Be(t),[t]),h=d.useMemo(()=>a(v.resolvedOptions().calendar),[a,v]);var p;let[g,C]=Pt(e.value,(p=e.defaultValue)!==null&&p!==void 0?p:null,e.onChange),F=d.useMemo(()=>{var M;return(M=Xs(g,h))!==null&&M!==void 0?M:null},[g,h]),[D,A]=d.useState(()=>vt(e.placeholderValue,c,h,m)),k=F||D,V=h.identifier==="gregory"&&k.era==="BC",N=d.useMemo(()=>{var M;return{granularity:c,maxGranularity:(M=e.maxGranularity)!==null&&M!==void 0?M:"year",timeZone:m,hideTimeZone:r,hourCycle:e.hourCycle,showEra:V,shouldForceLeadingZeros:e.shouldForceLeadingZeros}},[e.maxGranularity,c,e.hourCycle,e.shouldForceLeadingZeros,m,r,V]),L=d.useMemo(()=>it({},N),[N]),y=d.useMemo(()=>new Be(t,L),[t,L]),$=d.useMemo(()=>y.resolvedOptions(),[y]),x=d.useMemo(()=>y.formatToParts(new Date).filter(M=>ea[M.type]).reduce((M,Q)=>(M[td[Q.type]||Q.type]=!0,M),{}),[y]),[w,E]=d.useState(()=>e.value||e.defaultValue?{...x}:{}),z=d.useRef(null),P=d.useRef(h);d.useEffect(()=>{Ea(h,P.current)||(P.current=h,A(M=>Object.keys(w).length>0?G(M,h):vt(e.placeholderValue,c,h,m)))},[h,c,w,m,e.placeholderValue]),g&&Object.keys(w).length<Object.keys(x).length&&(w={...x},E(w)),g==null&&Object.keys(w).length===Object.keys(x).length&&(w={},E(w),A(vt(e.placeholderValue,c,h,m)));let I=F&&Object.keys(w).length>=Object.keys(x).length?F:D,O=M=>{if(e.isDisabled||e.isReadOnly)return;let Q=Object.keys(w),ie=Object.keys(x);M==null?(C(null),A(vt(e.placeholderValue,c,h,m)),E({})):Q.length>=ie.length||Q.length===ie.length-1&&x.dayPeriod&&!w.dayPeriod&&z.current!=="dayPeriod"?(M=G(M,f?.calendar||new fe),C(M)):A(M),z.current=null},H=d.useMemo(()=>I.toDate(b),[I,b]),R=d.useMemo(()=>R3(H,w,y,$,I,h,t,c),[H,w,y,$,I,h,t,c]);x.era&&w.year&&!w.era?(w.era=!0,E({...w})):!x.era&&w.era&&(delete w.era,E({...w}));let B=M=>{w[M]=!0,M==="year"&&x.era&&(w.era=!0),E({...w})},j=(M,Q)=>{if(w[M])O(k3(I,M,Q,$));else{B(M);let ie=Object.keys(w),Ve=Object.keys(x);(ie.length>=Ve.length||ie.length===Ve.length-1&&x.dayPeriod&&!w.dayPeriod)&&O(I)}},U=d.useMemo(()=>Qs(g,l,o,s,N),[g,l,o,s,N]),ae=cl({...e,value:g,builtinValidation:U}),he=ae.displayValidation.isInvalid,pe=e.validationState||(he?"invalid":null);var dt;return{...ae,value:F,dateValue:H,calendar:h,setValue:O,segments:R,dateFormatter:y,validationState:pe,isInvalid:he,granularity:c,maxGranularity:(dt=e.maxGranularity)!==null&&dt!==void 0?dt:"year",isDisabled:n,isReadOnly:u,isRequired:i,increment(M){j(M,1)},decrement(M){j(M,-1)},incrementPage(M){j(M,hu[M]||1)},decrementPage(M){j(M,-(hu[M]||1))},setSegment(M,Q){B(M),O(A3(I,M,Q,$))},confirmPlaceholder(){if(e.isDisabled||e.isReadOnly)return;let M=Object.keys(w),Q=Object.keys(x);M.length===Q.length-1&&x.dayPeriod&&!w.dayPeriod&&(w={...x},E(w),O(I.copy()))},clearSegment(M){delete w[M],z.current=M,E({...w});let Q=vt(e.placeholderValue,c,h,m),ie=I;if(M==="dayPeriod"&&"hour"in I&&"hour"in Q){let Ve=I.hour>=12,rn=Q.hour>=12;Ve&&!rn?ie=I.set({hour:I.hour-12}):!Ve&&rn&&(ie=I.set({hour:I.hour+12}))}else M==="hour"&&"hour"in I&&I.hour>=12&&w.dayPeriod?ie=I.set({hour:Q.hour+12}):M in I&&(ie=I.set({[M]:Q[M]}));C(null),O(ie)},formatValue(M){if(!F)return"";let Q=it(M,N);return new Be(t,Q).format(H)},getDateFormatter(M,Q){let ie={...N,...Q},Ve=it({},ie);return new Be(M,Ve)}}}function R3(e,t,a,r,n,u,i,l){let o=["hour","minute","second"],s=a.formatToParts(e),f=[];for(let c of s){let m=td[c.type]||c.type,b=ea[m];m==="era"&&u.getEras().length===1&&(b=!1);let v=ea[m]&&!t[m],h=ea[m]?S3(m,c.value,i):null,p={type:m,text:v?h:c.value,...Gt(n,m,r),isPlaceholder:v,placeholder:h,isEditable:b};m==="hour"?(f.push({type:"literal",text:"⁦",...Gt(n,"literal",r),isPlaceholder:!1,placeholder:"",isEditable:!1}),f.push(p),m===l&&f.push({type:"literal",text:"⁩",...Gt(n,"literal",r),isPlaceholder:!1,placeholder:"",isEditable:!1})):o.includes(m)&&m===l?(f.push(p),f.push({type:"literal",text:"⁩",...Gt(n,"literal",r),isPlaceholder:!1,placeholder:"",isEditable:!1})):f.push(p)}return f}function Gt(e,t,a){switch(t){case"era":{let r=e.calendar.getEras();return{value:r.indexOf(e.era),minValue:0,maxValue:r.length-1}}case"year":return{value:e.year,minValue:1,maxValue:e.calendar.getYearsInEra(e)};case"month":return{value:e.month,minValue:Qc(e),maxValue:e.calendar.getMonthsInYear(e)};case"day":return{value:e.day,minValue:Xc(e),maxValue:e.calendar.getDaysInMonth(e)}}if("hour"in e)switch(t){case"dayPeriod":return{value:e.hour>=12?12:0,minValue:0,maxValue:12};case"hour":if(a.hour12){let r=e.hour>=12;return{value:e.hour,minValue:r?12:0,maxValue:r?23:11}}return{value:e.hour,minValue:0,maxValue:23};case"minute":return{value:e.minute,minValue:0,maxValue:59};case"second":return{value:e.second,minValue:0,maxValue:59}}return{}}function k3(e,t,a,r){switch(t){case"era":case"year":case"month":case"day":return e.cycle(t,a,{round:t==="year"})}if("hour"in e)switch(t){case"dayPeriod":{let n=e.hour,u=n>=12;return e.set({hour:u?n-12:n+12})}case"hour":case"minute":case"second":return e.cycle(t,a,{round:t!=="hour",hourCycle:r.hour12?12:24})}throw new Error("Unknown segment: "+t)}function A3(e,t,a,r){switch(t){case"day":case"month":case"year":case"era":return e.set({[t]:a})}if("hour"in e&&typeof a=="number")switch(t){case"dayPeriod":{let n=e.hour,u=n>=12;return a>=12===u?e:e.set({hour:u?n-12:n+12})}case"hour":if(r.hour12){let u=e.hour>=12;!u&&a===12&&(a=0),u&&a<12&&(a+=12)}case"minute":case"second":return e.set({[t]:a})}throw new Error("Unknown segment: "+t)}const T3=d.createContext(null),Sa=d.createContext({}),ad=d.forwardRef(function(t,a){[t,a]=Fe(t,a,Sa);let{isDisabled:r,isInvalid:n,onHoverStart:u,onHoverChange:i,onHoverEnd:l,...o}=t,{hoverProps:s,isHovered:f}=Vt({onHoverStart:u,onHoverChange:i,onHoverEnd:l,isDisabled:r}),{isFocused:c,isFocusVisible:m,focusProps:b}=st({within:!0});r??(r=!!t["aria-disabled"]&&t["aria-disabled"]!=="false"),n??(n=!!t["aria-invalid"]&&t["aria-invalid"]!=="false");let v=Se({...t,values:{isHovered:f,isFocusWithin:c,isFocusVisible:m,isDisabled:r,isInvalid:n},defaultClassName:"react-aria-Group"});var h,p;return S.createElement("div",{...Z(o,b,s),...v,ref:a,role:(h=t.role)!==null&&h!==void 0?h:"group",slot:(p=t.slot)!==null&&p!==void 0?p:void 0,"data-focus-within":c||void 0,"data-hovered":f||void 0,"data-focus-visible":m||void 0,"data-disabled":r||void 0,"data-invalid":n||void 0},v.children)}),$a=d.createContext(null),M3=d.createContext(null),Fa=d.createContext(null),en=d.createContext(null),N3=d.forwardRef(function(t,a){[t,a]=Fe(t,a,$a);let{locale:r}=ce(),n=y3({...t,locale:r,createCalendar:t.createCalendar||yi}),{calendarProps:u,prevButtonProps:i,nextButtonProps:l,errorMessageProps:o,title:s}=v4(t,n),f=Se({...t,values:{state:n,isDisabled:t.isDisabled||!1,isInvalid:n.isValueInvalid},defaultClassName:"react-aria-Calendar"});return S.createElement("div",{...f,...u,ref:a,slot:t.slot||void 0,"data-disabled":t.isDisabled||void 0,"data-invalid":n.isValueInvalid||void 0},S.createElement(ya,{values:[[jt,{slots:{previous:i,next:l}}],[Sr,{"aria-hidden":!0,level:2,children:s}],[Fa,n],[$a,t],[Xr,{slots:{errorMessage:o}}]]},S.createElement(Cr,null,S.createElement("h2",null,u["aria-label"])),f.children,S.createElement(Cr,null,S.createElement("button",{"aria-label":l["aria-label"],disabled:l.isDisabled,onClick:()=>n.focusNextPage(),tabIndex:-1}))))}),Ra=d.createContext(null),I3=d.forwardRef(function(t,a){let r=d.useContext(Fa),n=d.useContext(en),u=aa($a),i=aa(M3),l=r??n,o=l.visibleRange.start;t.offset&&(o=o.add(t.offset));var s;let f=(s=u?.firstDayOfWeek)!==null&&s!==void 0?s:i?.firstDayOfWeek,{gridProps:c,headerProps:m,weekDays:b,weeksInMonth:v}=h4({startDate:o,endDate:ua(o),weekdayStyle:t.weekdayStyle,firstDayOfWeek:f},l);var h;return S.createElement(Ra.Provider,{value:{headerProps:m,weekDays:b,startDate:o,weeksInMonth:v}},S.createElement("table",{...ue(t),...c,ref:a,style:t.style,className:(h=t.className)!==null&&h!==void 0?h:"react-aria-CalendarGrid"},typeof t.children!="function"?t.children:S.createElement(S.Fragment,null,S.createElement(rd,null,p=>S.createElement(nd,null,p)),S.createElement(ud,null,t.children))))});function V3(e,t){let{children:a,style:r,className:n}=e,{headerProps:u,weekDays:i}=d.useContext(Ra);return S.createElement("thead",{...ue(e),...u,ref:t,style:r,className:n||"react-aria-CalendarGridHeader"},S.createElement("tr",null,i.map((l,o)=>S.cloneElement(a(l),{key:o}))))}const rd=d.forwardRef(V3);function L3(e,t){let{children:a,style:r,className:n}=e;return S.createElement("th",{...ue(e),ref:t,style:r,className:n||"react-aria-CalendarHeaderCell"},a)}const nd=d.forwardRef(L3);function O3(e,t){let{children:a,style:r,className:n}=e,u=d.useContext(Fa),i=d.useContext(en),l=u??i,{startDate:o,weeksInMonth:s}=d.useContext(Ra);return S.createElement("tbody",{...ue(e),ref:t,style:r,className:n||"react-aria-CalendarGridBody"},[...new Array(s).keys()].map(f=>S.createElement("tr",{key:f},l.getDatesInWeek(f,o).map((c,m)=>c?S.cloneElement(a(c),{key:m}):S.createElement("td",{key:m})))))}const ud=d.forwardRef(O3),j3=d.forwardRef(function({date:t,...a},r){let n=d.useContext(Fa),u=d.useContext(en),i=n??u;var l;let{startDate:o}=(l=d.useContext(Ra))!==null&&l!==void 0?l:{startDate:i.visibleRange.start},s=d.useRef(null),{cellProps:f,buttonProps:c,...m}=D4({date:t},i,s),{hoverProps:b,isHovered:v}=Vt({...a,isDisabled:m.isDisabled}),{focusProps:h,isFocusVisible:p}=st();p&&(p=m.isFocused);let g=!Zc(o,t),C=!1,F=!1;"highlightedRange"in i&&i.highlightedRange&&(C=q(t,i.highlightedRange.start),F=q(t,i.highlightedRange.end));let D=Se({...a,defaultChildren:m.formattedDate,defaultClassName:"react-aria-CalendarCell",values:{date:t,isHovered:v,isOutsideMonth:g,isFocusVisible:p,isSelectionStart:C,isSelectionEnd:F,...m}}),A={"data-focused":m.isFocused||void 0,"data-hovered":v||void 0,"data-pressed":m.isPressed||void 0,"data-unavailable":m.isUnavailable||void 0,"data-disabled":m.isDisabled||void 0,"data-focus-visible":p||void 0,"data-outside-visible-range":m.isOutsideVisibleRange||void 0,"data-outside-month":g||void 0,"data-selected":m.isSelected||void 0,"data-selection-start":C||void 0,"data-selection-end":F||void 0,"data-invalid":m.isInvalid||void 0};return S.createElement("td",{...f,ref:r},S.createElement("div",{...Z(ue(a),c,h,b,A,D),ref:s}))}),z3=d.createContext({placement:"bottom"}),id=d.createContext(null),pu=d.createContext(null),H3=d.forwardRef(function(t,a){[t,a]=Fe(t,a,id);let r=d.useContext(tn),n=vs(t),u=t.isOpen!=null||t.defaultOpen!=null||!r?n:r,i=rc(a,u.isOpen)||t.isExiting||!1,l=sc(),{direction:o}=ce();if(l){let s=t.children;return typeof s=="function"&&(s=s({trigger:t.trigger||null,placement:"bottom",isEntering:!1,isExiting:!1,defaultChildren:null})),S.createElement(S.Fragment,null,s)}return u&&!u.isOpen&&!i?null:S.createElement(_3,{...t,triggerRef:t.triggerRef,state:u,popoverRef:a,isExiting:i,dir:o})});function _3({state:e,isExiting:t,UNSTABLE_portalContainer:a,clearContexts:r,...n}){let u=d.useRef(null),[i,l]=d.useState(0),o=d.useRef(null),s=d.useContext(pu),f=s&&n.trigger==="SubmenuTrigger";_(()=>{u.current&&e.isOpen&&l(u.current.getBoundingClientRect().width)},[e.isOpen,u]);var c;let{popoverProps:m,underlayProps:b,arrowProps:v,placement:h}=e3({...n,offset:(c=n.offset)!==null&&c!==void 0?c:8,arrowSize:i,groupRef:f?s:o},e),p=n.popoverRef,g=ac(p,!!h)||n.isEntering||!1,C=Se({...n,defaultClassName:"react-aria-Popover",values:{trigger:n.trigger||null,placement:h,isEntering:g,isExiting:t}}),F=!n.isNonModal||n.trigger==="SubmenuTrigger",[D,A]=d.useState(!1);_(()=>{p.current&&A(F&&!p.current.querySelector("[role=dialog]"))},[p,F]),d.useEffect(()=>{D&&p.current&&!p.current.contains(document.activeElement)&&Bt(p.current)},[D,p]);let k=d.useMemo(()=>{let y=C.children;if(r)for(let $ of r)y=S.createElement($.Provider,{value:null},y);return y},[C.children,r]),V={...m.style,...C.style},N=S.createElement("div",{...Z(ue(n),m),...C,role:D?"dialog":void 0,tabIndex:D?-1:void 0,"aria-label":n["aria-label"],"aria-labelledby":n["aria-labelledby"],ref:p,slot:n.slot||void 0,style:V,dir:n.dir,"data-trigger":n.trigger,"data-placement":h,"data-entering":g||void 0,"data-exiting":t||void 0},!n.isNonModal&&S.createElement(fu,{onDismiss:e.close}),S.createElement(z3.Provider,{value:{...v,placement:h,ref:u}},k),S.createElement(fu,{onDismiss:e.close}));if(!f)return S.createElement(mu,{...n,shouldContainFocus:D,isExiting:t,portalContainer:a},!n.isNonModal&&e.isOpen&&S.createElement("div",{"data-testid":"underlay",...b,style:{position:"fixed",inset:0}}),S.createElement("div",{ref:o,style:{display:"contents"}},S.createElement(pu.Provider,{value:o},N)));var L;return S.createElement(mu,{...n,shouldContainFocus:D,isExiting:t,portalContainer:(L=a??s?.current)!==null&&L!==void 0?L:void 0},N)}const ld=d.createContext(null),tn=d.createContext(null),K3=d.forwardRef(function(t,a){let r=t["aria-labelledby"];[t,a]=Fe(t,a,ld);let{dialogProps:n,titleProps:u}=f3({...t,"aria-labelledby":r},a),i=d.useContext(tn);!n["aria-label"]&&!n["aria-labelledby"]&&t["aria-labelledby"]&&(n["aria-labelledby"]=t["aria-labelledby"]);let l=Se({defaultClassName:"react-aria-Dialog",className:t.className,style:t.style,children:t.children,values:{close:i?.close||(()=>{})}});return S.createElement("section",{...ue(t),...n,...l,ref:a,slot:t.slot||void 0},S.createElement(ya,{values:[[Sr,{slots:{[rr]:{},title:{...u,level:2}}}],[jt,{slots:{[rr]:{},close:{onPress:()=>i?.close()}}}]]},l.children))}),od=d.createContext(null),ka=d.createContext(null),an=d.createContext(null),U3=d.forwardRef(function(t,a){let r=d.useContext(ka),n=d.useContext(an);return r||n?S.createElement(sd,{...t,ref:a}):S.createElement(W3,{...t,ref:a})}),W3=d.forwardRef((e,t)=>{let[a,r]=Fe({slot:e.slot},t,od),{locale:n}=ce(),u=F3({...a,locale:n,createCalendar:yi}),i=d.useRef(null),{fieldProps:l,inputProps:o}=n3({...a,inputRef:i},u,r);return S.createElement(ya,{values:[[ka,u],[$s,{...o,ref:i}],[Sa,{...l,ref:r,isInvalid:u.isInvalid,isDisabled:u.isDisabled}]]},S.createElement(sd,e))}),sd=d.forwardRef((e,t)=>{let{className:a,children:r}=e,n=d.useContext(ka),u=d.useContext(an),i=n??u;return S.createElement(S.Fragment,null,S.createElement(ad,{...e,ref:t,slot:e.slot||void 0,className:a??"react-aria-DateInput",isInvalid:i.isInvalid,isDisabled:i.isDisabled},i.segments.map((l,o)=>d.cloneElement(r(l),{key:o}))),S.createElement($3,null))}),Z3=d.forwardRef(function({segment:t,...a},r){let n=d.useContext(ka),u=d.useContext(an),i=n??u,l=Pu(r),{segmentProps:o}=d3(t,i,l),{focusProps:s,isFocused:f,isFocusVisible:c}=st(),{hoverProps:m,isHovered:b}=Vt({...a,isDisabled:i.isDisabled||t.type==="literal"}),{isEditable:v,...h}=t,p=Se({...a,values:{...h,isReadOnly:!v,isInvalid:i.isInvalid,isDisabled:i.isDisabled,isHovered:b,isFocused:f,isFocusVisible:c},defaultChildren:t.text,defaultClassName:"react-aria-DateSegment"});return S.createElement("span",{...Z(ue(a),o,s,m),...p,style:o.style,ref:l,"data-placeholder":t.isPlaceholder||void 0,"data-invalid":i.isInvalid||void 0,"data-readonly":!v||void 0,"data-disabled":i.isDisabled||void 0,"data-type":t.type,"data-hovered":b||void 0,"data-focused":f||void 0,"data-focus-visible":c||void 0})}),G3=d.createContext(null),Y3=d.createContext(null),J3=[Sa,jt,bs,Xr],q3=d.forwardRef(function(t,a){[t,a]=Fe(t,a,G3);let{validationBehavior:r}=aa(T3)||{};var n,u;let i=(u=(n=t.validationBehavior)!==null&&n!==void 0?n:r)!==null&&u!==void 0?u:"native",l=P3({...t,validationBehavior:i}),o=d.useRef(null),[s,f]=lc(!t["aria-label"]&&!t["aria-labelledby"]),{groupProps:c,labelProps:m,fieldProps:b,buttonProps:v,dialogProps:h,calendarProps:p,descriptionProps:g,errorMessageProps:C,...F}=i3({...oc(t),label:f,validationBehavior:i},l,o),[D,A]=d.useState(null),k=d.useCallback(()=>{o.current&&A(o.current.offsetWidth+"px")},[]);er({ref:o,onResize:k});let{focusProps:V,isFocused:N,isFocusVisible:L}=st({within:!0}),y=Se({...t,values:{state:l,isFocusWithin:N,isFocusVisible:L,isDisabled:t.isDisabled||!1,isInvalid:l.isInvalid,isOpen:l.isOpen},defaultClassName:"react-aria-DatePicker"}),$=ue(t);return delete $.id,S.createElement(ya,{values:[[Y3,l],[Sa,{...c,ref:o,isInvalid:l.isInvalid}],[od,b],[jt,{...v,isPressed:l.isOpen}],[bs,{...m,ref:s,elementType:"span"}],[$a,p],[tn,l],[id,{trigger:"DatePicker",triggerRef:o,placement:"bottom start",style:{"--trigger-width":D},clearContexts:J3}],[ld,h],[Xr,{slots:{description:g,errorMessage:C}}],[p3,F]]},S.createElement("div",{...V,...$,...y,ref:a,slot:t.slot||void 0,"data-focus-within":N||void 0,"data-invalid":l.isInvalid||void 0,"data-focus-visible":L||void 0,"data-disabled":t.isDisabled||void 0,"data-open":l.isOpen||void 0}))}),Q3=d.forwardRef(function(t,a){[t,a]=Fe(t,a,Sr);let{children:r,level:n=3,className:u,...i}=t,l=`h${n}`;return S.createElement(l,{...i,ref:a,className:u??"react-aria-Heading"},r)});function X3(){return T.jsxs("header",{className:"flex w-full items-center gap-1 pb-1",children:[T.jsx(Pr,{slot:"previous",className:"text-muted-foreground/80 hover:bg-accent hover:text-foreground focus-visible:ring-ring/50 flex size-9 items-center justify-center rounded-md transition-[color,box-shadow] outline-none focus-visible:ring-[3px]",children:T.jsx(pd,{size:16})}),T.jsx(Q3,{className:"grow text-center text-sm font-medium"}),T.jsx(Pr,{slot:"next",className:"text-muted-foreground/80 hover:bg-accent hover:text-foreground focus-visible:ring-ring/50 flex size-9 items-center justify-center rounded-md transition-[color,box-shadow] outline-none focus-visible:ring-[3px]",children:T.jsx(fd,{size:16})})]})}function em({isRange:e=!1}){const t=Ca(wa());return T.jsxs(I3,{children:[T.jsx(rd,{children:a=>T.jsx(nd,{className:"text-muted-foreground/80 size-9 rounded-md p-0 text-xs font-medium",children:a})}),T.jsx(ud,{className:"[&_td]:px-0 [&_td]:py-px",children:a=>T.jsx(j3,{date:a,className:Et("text-foreground data-hovered:bg-accent data-selected:bg-primary data-hovered:text-foreground data-selected:text-primary-foreground data-focus-visible:ring-ring/50 relative flex size-9 items-center justify-center rounded-md p-0 text-sm font-normal whitespace-nowrap [transition-property:color,background-color,border-radius,box-shadow] duration-150 outline-none data-disabled:pointer-events-none data-disabled:opacity-30 data-focus-visible:z-10 data-focus-visible:ring-[3px] data-unavailable:pointer-events-none data-unavailable:line-through data-unavailable:opacity-30",e&&"data-selected:bg-accent data-selected:text-foreground data-invalid:data-selection-end:bg-destructive data-invalid:data-selection-start:bg-destructive data-selection-end:bg-primary data-selection-start:bg-primary data-selection-end:text-primary-foreground data-selection-start:text-primary-foreground data-invalid:bg-red-100 data-selected:rounded-none data-selection-end:rounded-e-md data-invalid:data-selection-end:text-white data-selection-start:rounded-s-md data-invalid:data-selection-start:text-white",a.compare(t)===0&&Et("after:bg-primary after:pointer-events-none after:absolute after:start-1/2 after:bottom-1 after:z-10 after:size-[3px] after:-translate-x-1/2 after:rounded-full",e?"data-selection-end:after:bg-background data-selection-start:after:bg-background":"data-selected:after:bg-background"))})})]})}function tm({className:e,...t}){return T.jsxs(N3,{...t,className:Mr(e,a=>Et("w-fit",a)),children:[T.jsx(X3,{}),T.jsx(em,{})]})}function am({className:e,...t}){return T.jsx(Z3,{className:Mr(e,a=>Et("text-foreground data-focused:bg-accent data-invalid:data-focused:bg-destructive data-focused:data-placeholder:text-foreground data-focused:text-foreground data-invalid:data-placeholder:text-destructive data-invalid:text-destructive data-placeholder:text-muted-foreground/70 data-[type=literal]:text-muted-foreground/70 inline rounded p-0.5 caret-transparent outline-hidden data-disabled:cursor-not-allowed data-disabled:opacity-50 data-invalid:data-focused:text-white data-invalid:data-focused:data-placeholder:text-white data-[type=literal]:px-0",a)),...t,"data-invalid":!0})}const rm="relative inline-flex h-9 w-full items-center overflow-hidden whitespace-nowrap rounded-md border border-input bg-background px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none data-focus-within:border-ring data-focus-within:ring-ring/50 data-focus-within:ring-[3px] data-focus-within:has-aria-invalid:ring-destructive/20 dark:data-focus-within:has-aria-invalid:ring-destructive/40 data-focus-within:has-aria-invalid:border-destructive";function nm({className:e,unstyled:t=!1,...a}){return T.jsx(U3,{className:Mr(e,r=>Et(!t&&rm,r)),...a,children:r=>T.jsx(am,{segment:r})})}function um({value:e,onChange:t}){return T.jsxs(q3,{className:"*:not-first:mt-2",value:e,onChange:t,children:[T.jsxs("div",{className:"flex",children:[T.jsx(ad,{className:"w-full",children:T.jsx(nm,{className:"pe-9"})}),T.jsx(Pr,{className:"text-muted-foreground/80 hover:text-foreground data-focus-visible:border-ring data-focus-visible:ring-ring/50 z-10 -ms-9 -me-px flex w-9 items-center justify-center rounded-e-md transition-[color,box-shadow] outline-none data-focus-visible:ring-[3px]",children:T.jsx(xd,{size:16})})]}),T.jsx(H3,{className:"bg-background text-popover-foreground data-entering:animate-in data-exiting:animate-out data-[entering]:fade-in-0 data-[exiting]:fade-out-0 data-[entering]:zoom-in-95 data-[exiting]:zoom-out-95 data-[placement=bottom]:slide-in-from-top-2 data-[placement=left]:slide-in-from-right-2 data-[placement=right]:slide-in-from-left-2 data-[placement=top]:slide-in-from-bottom-2 z-50 rounded-lg border shadow-lg outline-hidden",offset:4,children:T.jsx(K3,{className:"max-h-[inherit] overflow-auto p-2",children:T.jsx(tm,{})})})]})}yd();const Du=[{id:"jetty_schedule_report",name:"Jetty Schedule Report",parameters:[{name:"startDate",label:"Start Date",type:"date"},{name:"endDate",label:"End Date",type:"date"},{name:"jetty",label:"Jetty",type:"select",options:[{value:"all",label:"All Jetties"},{value:"jetty_a",label:"Jetty A"},{value:"jetty_b",label:"Jetty B"},{value:"jetty_c",label:"Jetty C"}]},{name:"status",label:"Status",type:"select",options:[{value:"all",label:"All Status"},{value:"scheduled",label:"Scheduled"},{value:"in_progress",label:"In Progress"},{value:"completed",label:"Completed"}]}],mockDataGenerator:e=>{const t=[];for(let a=0;a<10;a++)t.push([`Vessel_${a+1}`,`2024-03-${10+a}`,`2024-03-${12+a}`,e.jetty==="all"?`Jetty ${String.fromCharCode(65+a%3)}`:e.jetty,["scheduled","in_progress","completed"][a%3],`Cargo Type ${a%3+1}`,`${Math.floor(Math.random()*1e3)} tons`]);return t},columns:[{data:0,title:"Vessel Name"},{data:1,title:"Arrival Date"},{data:2,title:"Departure Date"},{data:3,title:"Jetty"},{data:4,title:"Status"},{data:5,title:"Cargo Type"},{data:6,title:"Cargo Volume"}]},{id:"bounded_zone_report",name:"Bounded Zone Report",parameters:[{name:"date",label:"Date",type:"date"},{name:"zone",label:"Zone",type:"select",options:[{value:"all",label:"All Zones"},{value:"zone_a",label:"Zone A"},{value:"zone_b",label:"Zone B"},{value:"zone_c",label:"Zone C"}]},{name:"activity",label:"Activity Type",type:"select",options:[{value:"all",label:"All Activities"},{value:"loading",label:"Loading"},{value:"unloading",label:"Unloading"},{value:"storage",label:"Storage"}]}],mockDataGenerator:e=>{const t=[];for(let a=0;a<10;a++)t.push([e.zone==="all"?`Zone ${String.fromCharCode(65+a%3)}`:e.zone,["loading","unloading","storage"][a%3],`Cargo_${a+1}`,`${Math.floor(Math.random()*500)} tons`,`Vessel_${a+1}`,`Operator_${a+1}`,e.date?e.date.toString():"2024-03-15"]);return t},columns:[{data:0,title:"Zone"},{data:1,title:"Activity Type"},{data:2,title:"Cargo ID"},{data:3,title:"Volume"},{data:4,title:"Vessel"},{data:5,title:"Operator"},{data:6,title:"Date"}]}],mm=()=>{const[e,t]=d.useState(""),[a,r]=d.useState(null),[n,u]=d.useState({}),[i,l]=d.useState([]),o=d.useRef(null);d.useEffect(()=>{if(e){const c=Du.find(b=>b.id===e);r(c||null);const m={};c?.parameters.forEach(b=>{b.type==="date"?m[b.name]=null:m[b.name]=""}),u(m),l([])}else r(null),u({}),l([])},[e]);const s=(c,m)=>{u(b=>({...b,[c]:m}))},f=()=>{if(a){const c=a.mockDataGenerator(n);l(c),o.current?.hotInstance&&o.current.hotInstance.loadData(c)}};return T.jsx(md,{children:T.jsxs("div",{className:"flex flex-col space-y-2 p-2",children:[T.jsxs(nn,{children:[T.jsx(un,{children:T.jsx(ln,{children:"Report Selection and Parameters"})}),T.jsxs(on,{className:"p-3",children:[T.jsxs("div",{className:"mb-4",children:[T.jsx(sn,{htmlFor:"report-select",className:"text-sm mb-1",children:"Select Report"}),T.jsxs(dn,{onValueChange:t,value:e,children:[T.jsx(cn,{className:"w-[240px]",children:T.jsx(fn,{placeholder:"Select a report"})}),T.jsx(mn,{children:Du.map(c=>T.jsx($n,{value:c.id,children:c.name},c.id))})]})]}),a&&T.jsxs(T.Fragment,{children:[T.jsxs("h4",{className:"text-lg font-semibold mb-3",children:[a.name," Parameters"]}),T.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-2",children:a.parameters.map(c=>T.jsxs("div",{children:[T.jsx(sn,{htmlFor:c.name,className:"text-sm mb-1",children:c.label}),c.type==="text"&&T.jsx(bd,{id:c.name,value:n[c.name]||"",onChange:m=>s(c.name,m.target.value),className:"w-[240px]"}),c.type==="date"&&T.jsx(um,{value:n[c.name]||null,onChange:m=>s(c.name,m)}),c.type==="select"&&T.jsxs(dn,{value:n[c.name]||"",onValueChange:m=>s(c.name,m),children:[T.jsx(cn,{className:"w-[240px]",children:T.jsx(fn,{placeholder:`Select ${c.label}`})}),T.jsx(mn,{children:c.options?.map(m=>T.jsx($n,{value:m.value,children:m.label},m.value))})]})]},c.name))}),T.jsx($d,{onClick:f,className:"mt-4",children:"Run Report"})]})]})]}),i.length>0&&a&&T.jsxs(nn,{children:[T.jsx(un,{children:T.jsxs(ln,{children:[a.name," Results"]})}),T.jsx(on,{className:"p-3",children:T.jsx(Dd,{ref:o,themeName:"ht-theme-main",data:i,columns:a.columns,colHeaders:a.columns.map(c=>c.title),rowHeaders:!0,height:"auto",width:"auto",stretchH:"all",licenseKey:"non-commercial-and-evaluation"})})]})]})})};export{mm as default};
//# sourceMappingURL=report-B_Vm8Lca.js.map
