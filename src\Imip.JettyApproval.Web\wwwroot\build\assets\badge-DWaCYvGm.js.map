{"version": 3, "file": "badge-DWaCYvGm.js", "sources": ["../../../../../frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { Slot as SlotPrimitive } from 'radix-ui';\n\nexport interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {\n  asChild?: boolean;\n  dotClassName?: string;\n  disabled?: boolean;\n}\n\nexport interface BadgeButtonProps\n  extends React.ButtonHTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeButtonVariants> {\n  asChild?: boolean;\n}\n\nexport type BadgeDotProps = React.HTMLAttributes<HTMLSpanElement>;\n\nconst badgeVariants = cva(\n  'inline-flex items-center justify-center border border-transparent font-medium focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 [&_svg]:-ms-px [&_svg]:shrink-0',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-primary text-primary-foreground',\n        secondary: 'bg-secondary text-secondary-foreground',\n        success:\n          'bg-[var(--color-success-accent,var(--color-green-500))] text-[var(--color-success-foreground,var(--color-white))]',\n        warning:\n          'bg-[var(--color-warning-accent,var(--color-yellow-500))] text-[var(--color-warning-foreground,var(--color-white))]',\n        info: 'bg-[var(--color-info-accent,var(--color-violet-500))] text-[var(--color-info-foreground,var(--color-white))]',\n        outline: 'bg-transparent border border-border text-secondary-foreground',\n        destructive: 'bg-destructive text-destructive-foreground',\n      },\n      appearance: {\n        default: '',\n        light: '',\n        outline: '',\n        ghost: 'border-transparent bg-transparent',\n      },\n      disabled: {\n        true: 'opacity-50 pointer-events-none',\n      },\n      size: {\n        lg: 'rounded-md px-[0.5rem] h-7 min-w-7 gap-1.5 text-xs [&_svg]:size-3.5',\n        md: 'rounded-md px-[0.45rem] h-6 min-w-6 gap-1.5 text-xs [&_svg]:size-3.5 ',\n        sm: 'rounded-sm px-[0.325rem] h-5 min-w-5 gap-1 text-[0.6875rem] leading-[0.75rem] [&_svg]:size-3',\n        xs: 'rounded-sm px-[0.25rem] h-4 min-w-4 gap-1 text-[0.625rem] leading-[0.5rem] [&_svg]:size-3',\n      },\n      shape: {\n        default: '',\n        circle: 'rounded-full',\n      },\n    },\n    compoundVariants: [\n      /* Light */\n      {\n        variant: 'primary',\n        appearance: 'light',\n        className:\n          'text-[var(--color-primary-accent,var(--color-blue-700))] bg-[var(--color-primary-soft,var(--color-blue-50))] dark:bg-[var(--color-primary-soft,var(--color-blue-950))] dark:text-[var(--color-primary-soft,var(--color-blue-600))]',\n      },\n      {\n        variant: 'secondary',\n        appearance: 'light',\n        className: 'bg-secondary dark:bg-secondary/50 text-secondary-foreground',\n      },\n      {\n        variant: 'success',\n        appearance: 'light',\n        className:\n          'text-[var(--color-success-accent,var(--color-green-800))] bg-[var(--color-success-soft,var(--color-green-100))] dark:bg-[var(--color-success-soft,var(--color-green-950))] dark:text-[var(--color-success-soft,var(--color-green-600))]',\n      },\n      {\n        variant: 'warning',\n        appearance: 'light',\n        className:\n          'text-[var(--color-warning-accent,var(--color-yellow-700))] bg-[var(--color-warning-soft,var(--color-yellow-100))] dark:bg-[var(--color-warning-soft,var(--color-yellow-950))] dark:text-[var(--color-warning-soft,var(--color-yellow-600))]',\n      },\n      {\n        variant: 'info',\n        appearance: 'light',\n        className:\n          'text-[var(--color-info-accent,var(--color-violet-700))] bg-[var(--color-info-soft,var(--color-violet-100))] dark:bg-[var(--color-info-soft,var(--color-violet-950))] dark:text-[var(--color-info-soft,var(--color-violet-400))]',\n      },\n      {\n        variant: 'destructive',\n        appearance: 'light',\n        className:\n          'text-[var(--color-destructive-accent,var(--color-red-700))] bg-[var(--color-destructive-soft,var(--color-red-50))] dark:bg-[var(--color-destructive-soft,var(--color-red-950))] dark:text-[var(--color-destructive-soft,var(--color-red-600))]',\n      },\n      /* Outline */\n      {\n        variant: 'primary',\n        appearance: 'outline',\n        className:\n          'text-[var(--color-primary-accent,var(--color-blue-700))] border-[var(--color-primary-soft,var(--color-blue-100))] bg-[var(--color-primary-soft,var(--color-blue-50))] dark:bg-[var(--color-primary-soft,var(--color-blue-950))] dark:border-[var(--color-primary-soft,var(--color-blue-900))] dark:text-[var(--color-primary-soft,var(--color-blue-600))]',\n      },\n      {\n        variant: 'success',\n        appearance: 'outline',\n        className:\n          'text-[var(--color-success-accent,var(--color-green-700))] border-[var(--color-success-soft,var(--color-green-200))] bg-[var(--color-success-soft,var(--color-green-50))] dark:bg-[var(--color-success-soft,var(--color-green-950))] dark:border-[var(--color-success-soft,var(--color-green-900))] dark:text-[var(--color-success-soft,var(--color-green-600))]',\n      },\n      {\n        variant: 'warning',\n        appearance: 'outline',\n        className:\n          'text-[var(--color-warning-accent,var(--color-yellow-700))] border-[var(--color-warning-soft,var(--color-yellow-200))] bg-[var(--color-warning-soft,var(--color-yellow-50))] dark:bg-[var(--color-warning-soft,var(--color-yellow-950))] dark:border-[var(--color-warning-soft,var(--color-yellow-900))] dark:text-[var(--color-warning-soft,var(--color-yellow-600))]',\n      },\n      {\n        variant: 'info',\n        appearance: 'outline',\n        className:\n          'text-[var(--color-info-accent,var(--color-violet-700))] border-[var(--color-info-soft,var(--color-violet-100))] bg-[var(--color-info-soft,var(--color-violet-50))] dark:bg-[var(--color-info-soft,var(--color-violet-950))] dark:border-[var(--color-info-soft,var(--color-violet-900))] dark:text-[var(--color-info-soft,var(--color-violet-400))]',\n      },\n      {\n        variant: 'destructive',\n        appearance: 'outline',\n        className:\n          'text-[var(--color-destructive-accent,var(--color-red-700))] border-[var(--color-destructive-soft,var(--color-red-100))] bg-[var(--color-destructive-soft,var(--color-red-50))] dark:bg-[var(--color-destructive-soft,var(--color-red-950))] dark:border-[var(--color-destructive-soft,var(--color-red-900))] dark:text-[var(--color-destructive-soft,var(--color-red-600))]',\n      },\n      /* Ghost */\n      {\n        variant: 'primary',\n        appearance: 'ghost',\n        className: 'text-primary',\n      },\n      {\n        variant: 'secondary',\n        appearance: 'ghost',\n        className: 'text-secondary-foreground',\n      },\n      {\n        variant: 'success',\n        appearance: 'ghost',\n        className: 'text-[var(--color-success-accent,var(--color-green-500))]',\n      },\n      {\n        variant: 'warning',\n        appearance: 'ghost',\n        className: 'text-[var(--color-warning-accent,var(--color-yellow-500))]',\n      },\n      {\n        variant: 'info',\n        appearance: 'ghost',\n        className: 'text-[var(--color-info-accent,var(--color-violet-500))]',\n      },\n      {\n        variant: 'destructive',\n        appearance: 'ghost',\n        className: 'text-destructive',\n      },\n\n      { size: 'lg', appearance: 'ghost', className: 'px-0' },\n      { size: 'md', appearance: 'ghost', className: 'px-0' },\n      { size: 'sm', appearance: 'ghost', className: 'px-0' },\n      { size: 'xs', appearance: 'ghost', className: 'px-0' },\n    ],\n    defaultVariants: {\n      variant: 'primary',\n      appearance: 'default',\n      size: 'md',\n    },\n  },\n);\n\nconst badgeButtonVariants = cva(\n  'cursor-pointer transition-all inline-flex items-center justify-center leading-none size-3.5 [&>svg]:opacity-100! [&>svg]:size-3.5 p-0 rounded-md -me-0.5 opacity-60 hover:opacity-100',\n  {\n    variants: {\n      variant: {\n        default: '',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  },\n);\n\nfunction Badge({\n  className,\n  variant,\n  size,\n  appearance,\n  shape,\n  asChild = false,\n  disabled,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? SlotPrimitive.Slot : 'span';\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant, size, appearance, shape, disabled }), className)}\n      {...props}\n    />\n  );\n}\n\nfunction BadgeButton({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & VariantProps<typeof badgeButtonVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? SlotPrimitive.Slot : 'span';\n  return (\n    <Comp\n      data-slot=\"badge-button\"\n      className={cn(badgeButtonVariants({ variant, className }))}\n      role=\"button\"\n      {...props}\n    />\n  );\n}\n\nfunction BadgeDot({ className, ...props }: React.ComponentProps<'span'>) {\n  return (\n    <span\n      data-slot=\"badge-dot\"\n      className={cn('size-1.5 rounded-full bg-[currentColor] opacity-75', className)}\n      {...props}\n    />\n  );\n}\n\nexport { Badge, BadgeButton, BadgeDot, badgeVariants };\n"], "names": ["badgeVariants", "cva", "Badge", "className", "variant", "size", "appearance", "shape", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "props", "Comp", "SlotPrimitive.Slot", "jsx", "cn"], "mappings": "qIAmBA,MAAMA,EAAgBC,EACpB,sLACA,CACE,SAAU,CACR,QAAS,CACP,QAAS,qCACT,UAAW,yCACX,QACE,oHACF,QACE,qHACF,KAAM,+GACN,QAAS,gEACT,YAAa,4CACf,EACA,WAAY,CACV,QAAS,GACT,MAAO,GACP,QAAS,GACT,MAAO,mCACT,EACA,SAAU,CACR,KAAM,gCACR,EACA,KAAM,CACJ,GAAI,sEACJ,GAAI,wEACJ,GAAI,+FACJ,GAAI,2FACN,EACA,MAAO,CACL,QAAS,GACT,OAAQ,cAAA,CAEZ,EACA,iBAAkB,CAEhB,CACE,QAAS,UACT,WAAY,QACZ,UACE,oOACJ,EACA,CACE,QAAS,YACT,WAAY,QACZ,UAAW,6DACb,EACA,CACE,QAAS,UACT,WAAY,QACZ,UACE,yOACJ,EACA,CACE,QAAS,UACT,WAAY,QACZ,UACE,6OACJ,EACA,CACE,QAAS,OACT,WAAY,QACZ,UACE,iOACJ,EACA,CACE,QAAS,cACT,WAAY,QACZ,UACE,gPACJ,EAEA,CACE,QAAS,UACT,WAAY,UACZ,UACE,2VACJ,EACA,CACE,QAAS,UACT,WAAY,UACZ,UACE,iWACJ,EACA,CACE,QAAS,UACT,WAAY,UACZ,UACE,uWACJ,EACA,CACE,QAAS,OACT,WAAY,UACZ,UACE,qVACJ,EACA,CACE,QAAS,cACT,WAAY,UACZ,UACE,6WACJ,EAEA,CACE,QAAS,UACT,WAAY,QACZ,UAAW,cACb,EACA,CACE,QAAS,YACT,WAAY,QACZ,UAAW,2BACb,EACA,CACE,QAAS,UACT,WAAY,QACZ,UAAW,2DACb,EACA,CACE,QAAS,UACT,WAAY,QACZ,UAAW,4DACb,EACA,CACE,QAAS,OACT,WAAY,QACZ,UAAW,yDACb,EACA,CACE,QAAS,cACT,WAAY,QACZ,UAAW,kBACb,EAEA,CAAE,KAAM,KAAM,WAAY,QAAS,UAAW,MAAO,EACrD,CAAE,KAAM,KAAM,WAAY,QAAS,UAAW,MAAO,EACrD,CAAE,KAAM,KAAM,WAAY,QAAS,UAAW,MAAO,EACrD,CAAE,KAAM,KAAM,WAAY,QAAS,UAAW,MAAO,CACvD,EACA,gBAAiB,CACf,QAAS,UACT,WAAY,UACZ,KAAM,IAAA,CACR,CAEJ,EAgBA,SAASC,EAAM,CACb,UAAAC,EACA,QAAAC,EACA,KAAAC,EACA,WAAAC,EACA,MAAAC,EACA,QAAAC,EAAU,GACV,SAAAC,EACA,GAAGC,CACL,EAA8F,CACtF,MAAAC,EAAOH,EAAUI,EAAqB,OAG1C,OAAAC,EAAA,IAACF,EAAA,CACC,YAAU,QACV,UAAWG,EAAGd,EAAc,CAAE,QAAAI,EAAS,KAAAC,EAAM,WAAAC,EAAY,MAAAC,EAAO,SAAAE,EAAU,EAAGN,CAAS,EACrF,GAAGO,CAAA,CACN,CAEJ"}