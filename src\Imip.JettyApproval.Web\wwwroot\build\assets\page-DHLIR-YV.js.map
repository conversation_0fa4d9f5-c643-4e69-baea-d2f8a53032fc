{"version": 3, "file": "page-DHLIR-YV.js", "sources": ["../../../../../frontend/src/pages/export/edit/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { ExportVesselFormWithData } from '@/components/jetty/vessel/export/export-vessel-form';\r\nimport type { ExportVesselHeaderForm } from '@/components/jetty/vessel/export/export-vessel-header-schema';\r\nimport type { ExportVesselItemForm } from '@/components/jetty/vessel/export/export-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { toDatetimeLocalString } from '@/lib/utils/date-convert';\r\nimport { Head, usePage } from '@inertiajs/react';\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ExportVesselEditPage = () => {\r\n  const { t } = useTranslation();\r\n  const { props } = usePage();\r\n  const { toast } = useToast();\r\n  const id = typeof props.id === 'string' ? props.id : undefined;\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    data: vesselData,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: ['export-vessel', id],\r\n    queryFn: async () => {\r\n      if (!id) return null;\r\n      const response = await ekbProxyService.getExportVesselWithItems(id);\r\n      return response.data;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n\r\n  const initialHeader: Partial<ExportVesselHeaderForm> = vesselData\r\n    ? {\r\n      docNum: vesselData.docNum ?? '',\r\n      voyage: vesselData.voyage ?? '',\r\n      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? ''),\r\n      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? ''),\r\n      vesselId: vesselData.vesselId ?? '',\r\n      jettyId: vesselData.jettyId ?? '',\r\n      portOriginId: vesselData.portOriginId ?? '',\r\n      destinationPortId: vesselData.destinationPortId ?? '',\r\n      postingDate: vesselData.postingDate ?? '',\r\n      asideDate: toDatetimeLocalString(vesselData.asideDate ?? ''),\r\n      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? ''),\r\n    }\r\n    : {};\r\n\r\n  const initialItems: ExportVesselItemForm[] = vesselData?.items\r\n    ? vesselData.items.map(item => {\r\n      const mapped = {\r\n        itemName: item.itemName ?? '',\r\n        itemQty: item.itemQty ?? 0,\r\n        unitQty: item.unitQty ?? '',\r\n        remarks: item.remarks ?? '',\r\n        ajuNo: item.ajuNo ?? '',\r\n        regDate: item.regDate ? item.regDate : undefined,\r\n        regNo: item.regNo ?? '',\r\n        grossWeight: item.grossWeight ?? '',\r\n        unitWeight: item.unitWeight ?? '',\r\n        shippingInstructionNo: item.shippingInstructionNo ?? '',\r\n        shippingInstructionDate: item.shippingInstructionDate ? item.shippingInstructionDate : undefined,\r\n        letterNo: item.letterNo ?? '',\r\n        letterDate: item.letterDate ? item.letterDate : undefined,\r\n        status: item.status ?? '',\r\n        regType: item.regType ?? '',\r\n        attachments: item.attachments ?? [],\r\n        tenant: item.tenantName ?? '',\r\n        tenantId: item.tenantId ?? '',\r\n        businessPartner: item.businessPartner?.name ?? '',\r\n        businessPartnerId: item.businessPartnerId ?? '',\r\n        concurrencyStamp: item.concurrencyStamp ?? undefined,\r\n        id: item.id ?? undefined,\r\n      } as ExportVesselItemForm & { concurrencyStamp?: string; id?: string };\r\n      return mapped;\r\n    })\r\n    : [];\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: ExportVesselHeaderForm; items: ExportVesselItemForm[] }) => {\r\n      if (!id) throw new Error('No ID provided');\r\n      const response = await ekbProxyService.updateExportVessel(id, {\r\n        ...header,\r\n        docStatus: header.docStatus ?? 'Open',\r\n        items: items.map(item => ({\r\n          ...item,\r\n          createdBy: '',\r\n          docType: '',\r\n          isScan: '',\r\n          isOriginal: '',\r\n          isActive: true,\r\n          isDeleted: false,\r\n          isSend: '',\r\n          isFeOri: '',\r\n          isFeSend: '',\r\n          isChange: '',\r\n          isFeChange: '',\r\n          isFeActive: '',\r\n          deleted: '',\r\n          isUrgent: '',\r\n          tenantId: item.tenantId || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n        })),\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: () => {\r\n      toast({ title: 'Success', description: 'Export vessel updated.', variant: 'success' });\r\n      queryClient.invalidateQueries({ queryKey: ['export-vessel', id] });\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  if (isLoading) return <div>Loading...</div>;\r\n  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;\r\n\r\n  return (\r\n    <ExportVesselFormWithData\r\n      mode=\"edit\"\r\n      title={t('pages.vessel.edit.export')}\r\n      initialHeader={initialHeader}\r\n      initialItems={initialItems}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function ExportVEsselEdit() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.edit.export')} />\r\n      <ExportVesselEditPage />\r\n    </AppLayout>\r\n  );\r\n}"], "names": ["ExportVesselEditPage", "t", "useTranslation", "props", "usePage", "toast", "useToast", "id", "queryClient", "useQueryClient", "vesselData", "isLoading", "isError", "error", "useQuery", "ekbProxyService", "initialHeader", "toDatetimeLocalString", "initialItems", "item", "mutation", "useMutation", "header", "items", "response", "err", "handleSubmit", "jsx", "jsxs", "ExportVesselFormWithData", "ExportVEsselEdit", "AppLayout", "Head"], "mappings": "+yBAYA,MAAMA,EAAuB,IAAM,CAC3B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAQ,EACpB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrBC,EAAK,OAAOJ,EAAM,IAAO,SAAWA,EAAM,GAAK,OAC/CK,EAAcC,EAAe,EAE7B,CACJ,KAAMC,EACN,UAAAC,EACA,QAAAC,EACA,MAAAC,GACEC,EAAS,CACX,SAAU,CAAC,gBAAiBP,CAAE,EAC9B,QAAS,SACFA,GACY,MAAMQ,EAAgB,yBAAyBR,CAAE,GAClD,KAFA,KAIlB,QAAS,CAAC,CAACA,CAAA,CACZ,EAEKS,EAAiDN,EACnD,CACA,OAAQA,EAAW,QAAU,GAC7B,OAAQA,EAAW,QAAU,GAC7B,cAAeO,EAAsBP,EAAW,eAAiB,EAAE,EACnE,gBAAiBO,EAAsBP,EAAW,iBAAmB,EAAE,EACvE,SAAUA,EAAW,UAAY,GACjC,QAASA,EAAW,SAAW,GAC/B,aAAcA,EAAW,cAAgB,GACzC,kBAAmBA,EAAW,mBAAqB,GACnD,YAAaA,EAAW,aAAe,GACvC,UAAWO,EAAsBP,EAAW,WAAa,EAAE,EAC3D,WAAYO,EAAsBP,EAAW,YAAc,EAAE,CAAA,EAE7D,CAAC,EAECQ,EAAuCR,GAAY,MACrDA,EAAW,MAAM,IAAYS,IACd,CACb,SAAUA,EAAK,UAAY,GAC3B,QAASA,EAAK,SAAW,EACzB,QAASA,EAAK,SAAW,GACzB,QAASA,EAAK,SAAW,GACzB,MAAOA,EAAK,OAAS,GACrB,QAASA,EAAK,QAAUA,EAAK,QAAU,OACvC,MAAOA,EAAK,OAAS,GACrB,YAAaA,EAAK,aAAe,GACjC,WAAYA,EAAK,YAAc,GAC/B,sBAAuBA,EAAK,uBAAyB,GACrD,wBAAyBA,EAAK,wBAA0BA,EAAK,wBAA0B,OACvF,SAAUA,EAAK,UAAY,GAC3B,WAAYA,EAAK,WAAaA,EAAK,WAAa,OAChD,OAAQA,EAAK,QAAU,GACvB,QAASA,EAAK,SAAW,GACzB,YAAaA,EAAK,aAAe,CAAC,EAClC,OAAQA,EAAK,YAAc,GAC3B,SAAUA,EAAK,UAAY,GAC3B,gBAAiBA,EAAK,iBAAiB,MAAQ,GAC/C,kBAAmBA,EAAK,mBAAqB,GAC7C,iBAAkBA,EAAK,kBAAoB,OAC3C,GAAIA,EAAK,IAAM,MACjB,EAED,EACC,CAAC,EAECC,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA+E,CAC1G,GAAI,CAAChB,EAAU,MAAA,IAAI,MAAM,gBAAgB,EACzC,MAAMiB,EAAW,MAAMT,EAAgB,mBAAmBR,EAAI,CAC5D,GAAGe,EACH,UAAWA,EAAO,WAAa,OAC/B,MAAOC,EAAM,IAAaJ,IAAA,CACxB,GAAGA,EACH,UAAW,GACX,QAAS,GACT,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,UAAW,GACX,OAAQ,GACR,QAAS,GACT,SAAU,GACV,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,QAAS,GACT,SAAU,GACV,SAAUA,EAAK,UAAY,GAC3B,kBAAmBA,EAAK,mBAAqB,EAAA,EAC7C,CAAA,CACH,EACD,GAAIK,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAW,IAAM,CACfnB,EAAM,CAAE,MAAO,UAAW,YAAa,yBAA0B,QAAS,UAAW,EACrFG,EAAY,kBAAkB,CAAE,SAAU,CAAC,gBAAiBD,CAAE,EAAG,CACnE,EACA,QAAUkB,GAAoC,CACtCpB,EAAA,CACJ,MAAOoB,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOJ,EAAgCC,IAAkC,CAC5F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAEA,OAAIZ,EAAmBgB,EAAA,IAAA,MAAA,CAAI,SAAU,aAAA,EACjCf,EAAgBgB,EAAAA,KAAC,MAAI,CAAA,SAAA,CAAA,uBAAqBf,aAAiB,MAAQA,EAAM,QAAU,eAAA,EAAgB,EAGrGc,EAAA,IAACE,EAAA,CACC,KAAK,OACL,MAAO5B,EAAE,0BAA0B,EACnC,cAAAe,EACA,aAAAE,EACA,SAAUQ,EACV,aAAcN,EAAS,SAAA,CACzB,CAEJ,EAEA,SAAwBU,GAAmB,CACnC,KAAA,CAAE,EAAA7B,CAAE,EAAIC,EAAe,EAC7B,cACG6B,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACK,EAAK,CAAA,MAAO/B,EAAE,0BAA0B,CAAG,CAAA,QAC3CD,EAAqB,CAAA,CAAA,CAAA,EACxB,CAEJ"}