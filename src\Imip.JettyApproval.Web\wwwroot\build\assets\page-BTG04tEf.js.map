{"version": 3, "file": "page-BTG04tEf.js", "sources": ["../../../../../frontend/src/pages/local/edit/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { CreateUpdateLocalVesselDto, CreateUpdateVesselItemDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { LocalVesselFormWithData } from '@/components/jetty/vessel/local/local-vessel-form';\r\nimport type { LocalVesselHeaderForm } from '@/components/jetty/vessel/local/local-vessel-header-schema';\r\nimport type { LocalVesselItemForm } from '@/components/jetty/vessel/local/local-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { toDatetimeLocalString } from '@/lib/utils/date-convert';\r\nimport { Head, usePage } from '@inertiajs/react';\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst LocalVesselEditPage = () => {\r\n  const { t } = useTranslation();\r\n  const { props } = usePage();\r\n  const { toast } = useToast();\r\n  const id = typeof props.id === 'string' ? props.id : undefined;\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    data: vesselData,\r\n    isLoading,\r\n    isError,\r\n    error,\r\n  } = useQuery({\r\n    queryKey: ['local-vessel', id],\r\n    queryFn: async () => {\r\n      if (!id) return null;\r\n      const response = await ekbProxyService.getLocalVesselWithItems(id);\r\n      return response.data;\r\n    },\r\n    enabled: !!id,\r\n  });\r\n\r\n  const initialHeader: Partial<Record<keyof CreateUpdateLocalVesselDto, string>> = vesselData\r\n    ? {\r\n      docNum: vesselData.docNum ?? '',\r\n      voyage: vesselData.voyage ?? '',\r\n      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? '') ?? '',\r\n      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? '') ?? '',\r\n      asideDate: toDatetimeLocalString(vesselData.asideDate ?? '') ?? '',\r\n      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? '') ?? '',\r\n      vesselId: vesselData.vesselId ?? '',\r\n      bargeId: vesselData.bargeId ?? '',\r\n      jettyId: vesselData.jettyId ?? '',\r\n      portOriginId: vesselData.portOriginId ?? '',\r\n      destinationPortId: vesselData.destinationPortId ?? '',\r\n      postingDate: vesselData.postingDate ?? '',\r\n      ...(vesselData.concurrencyStamp ? { concurrencyStamp: vesselData.concurrencyStamp } : {}),\r\n      deleted: vesselData.deleted ?? '',\r\n      docType: vesselData.docType ?? '',\r\n      docStatus: vesselData.docStatus ?? '',\r\n      statusBms: vesselData.statusBms ?? '',\r\n      transType: vesselData.transType ?? '',\r\n      status: vesselData.status ?? '',\r\n      vesselType: vesselData.vesselType ?? '',\r\n      shipment: vesselData.shipment ?? '',\r\n      portOrigin: vesselData.portOrigin ?? '',\r\n      destinationPort: vesselData.destinationPort ?? '',\r\n    }\r\n    : {\r\n      deleted: '',\r\n      docType: '',\r\n      docStatus: '',\r\n      statusBms: '',\r\n      transType: '',\r\n      status: '',\r\n      vesselType: '',\r\n      shipment: '',\r\n      portOrigin: '',\r\n      destinationPort: '',\r\n      concurrencyStamp: '',\r\n    };\r\n\r\n  const initialItems: CreateUpdateVesselItemDto[] = vesselData?.items\r\n    ? vesselData.items.map(item => {\r\n      let unitWeight: string | null | undefined = null;\r\n      if (item.unitWeight != null) {\r\n        if (typeof item.unitWeight === 'string') {\r\n          unitWeight = item.unitWeight !== '' ? item.unitWeight : null;\r\n        } else {\r\n          unitWeight = String(item.unitWeight);\r\n        }\r\n      }\r\n      let grossWeight: number | null = null;\r\n      if (item.grossWeight != null) {\r\n        if (typeof item.grossWeight === 'string') {\r\n          grossWeight = item.grossWeight !== '' ? Number(item.grossWeight) : null;\r\n        } else {\r\n          grossWeight = item.grossWeight;\r\n        }\r\n      }\r\n      return {\r\n        itemName: item.itemName ?? null,\r\n        itemQty: item.itemQty ?? 0,\r\n        unitQty: item.unitQty ?? null,\r\n        remarks: item.remarks ?? null,\r\n        ajuNo: item.ajuNo ?? null,\r\n        regDate: item.regDate ? item.regDate : null,\r\n        regNo: item.regNo ?? null,\r\n        grossWeight,\r\n        unitWeight,\r\n        shippingInstructionNo: item.shippingInstructionNo ?? null,\r\n        shippingInstructionDate: item.shippingInstructionDate ?? null,\r\n        letterNo: item.letterNo ?? null,\r\n        letterDate: item.letterDate ?? null,\r\n        status: item.status ?? null,\r\n        regType: item.regType ?? null,\r\n        attachments: item.attachments ?? [],\r\n        tenant: item.tenantName ?? null,\r\n        tenantId: item.tenantId ?? '',\r\n        businessPartner: item.businessPartner?.name ?? null,\r\n        businessPartnerId: item.businessPartnerId ?? null,\r\n        concurrencyStamp: item.concurrencyStamp ?? undefined,\r\n        // Only include concurrencyStamp if present and non-empty\r\n        ...(item.concurrencyStamp ? { concurrencyStamp: item.concurrencyStamp } : {}),\r\n        id: item.id ?? '',\r\n      };\r\n    })\r\n    : [];\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: LocalVesselHeaderForm; items: LocalVesselItemForm[] }) => {\r\n      if (!id) throw new Error('No ID provided');\r\n      const response = await ekbProxyService.updateLocalVessel(id, {\r\n        ...header,\r\n        docNum: header.docNum ?? '',\r\n        docType: 'Local',\r\n        deleted: header.deleted ?? '',\r\n        docStatus: header.docStatus ?? 'Open',\r\n        statusBms: header.statusBms ?? '',\r\n        transType: header.transType ?? '',\r\n        status: header.status ?? '',\r\n        vesselType: header.vesselType ?? '',\r\n        shipment: header.shipment ?? '',\r\n        portOrigin: header.portOrigin ?? '',\r\n        destinationPort: header.destinationPort ?? '',\r\n        concurrencyStamp: header.concurrencyStamp ?? '',\r\n        items: items.map(item => ({\r\n          ...item,\r\n          createdBy: '',\r\n          docType: '',\r\n          isScan: '',\r\n          isOriginal: '',\r\n          isActive: true,\r\n          isDeleted: false,\r\n          isSend: '',\r\n          isFeOri: '',\r\n          isFeSend: '',\r\n          isChange: '',\r\n          isFeChange: '',\r\n          isFeActive: '',\r\n          deleted: '',\r\n          isUrgent: '',\r\n          tenantId: item.tenantId || '',\r\n          concurrencyStamp: item.concurrencyStamp || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n        })),\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: () => {\r\n      toast({ title: 'Success', description: 'Local vessel updated.', variant: 'success' });\r\n      queryClient.invalidateQueries({ queryKey: ['local-vessel', id] });\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  if (isLoading) return <div>Loading...</div>;\r\n  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;\r\n\r\n  return (\r\n    <LocalVesselFormWithData\r\n      mode=\"edit\"\r\n      title={t('pages.vessel.edit.local')}\r\n      initialHeader={initialHeader}\r\n      initialItems={initialItems}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n      queryClient={queryClient}\r\n      vesselData={vesselData || undefined}\r\n      jettyList={vesselData?.masterJetty ? [{ id: vesselData.masterJetty.id ?? '', isCustomArea: !!vesselData.masterJetty.isCustomArea }] : []}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function LocalVesselEdit() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.edit.local')} />\r\n      <LocalVesselEditPage />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["LocalVesselEditPage", "t", "useTranslation", "props", "usePage", "toast", "useToast", "id", "queryClient", "useQueryClient", "vesselData", "isLoading", "isError", "error", "useQuery", "ekbProxyService", "initialHeader", "toDatetimeLocalString", "initialItems", "item", "unitWeight", "grossWeight", "mutation", "useMutation", "header", "items", "response", "err", "handleSubmit", "jsx", "jsxs", "LocalVesselFormWithData", "LocalVesselEdit", "AppLayout", "Head"], "mappings": "2vBAYA,MAAMA,EAAsB,IAAM,CAC1B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAQ,EACpB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrBC,EAAK,OAAOJ,EAAM,IAAO,SAAWA,EAAM,GAAK,OAC/CK,EAAcC,EAAe,EAE7B,CACJ,KAAMC,EACN,UAAAC,EACA,QAAAC,EACA,MAAAC,GACEC,EAAS,CACX,SAAU,CAAC,eAAgBP,CAAE,EAC7B,QAAS,SACFA,GACY,MAAMQ,EAAgB,wBAAwBR,CAAE,GACjD,KAFA,KAIlB,QAAS,CAAC,CAACA,CAAA,CACZ,EAEKS,EAA2EN,EAC7E,CACA,OAAQA,EAAW,QAAU,GAC7B,OAAQA,EAAW,QAAU,GAC7B,cAAeO,EAAsBP,EAAW,eAAiB,EAAE,GAAK,GACxE,gBAAiBO,EAAsBP,EAAW,iBAAmB,EAAE,GAAK,GAC5E,UAAWO,EAAsBP,EAAW,WAAa,EAAE,GAAK,GAChE,WAAYO,EAAsBP,EAAW,YAAc,EAAE,GAAK,GAClE,SAAUA,EAAW,UAAY,GACjC,QAASA,EAAW,SAAW,GAC/B,QAASA,EAAW,SAAW,GAC/B,aAAcA,EAAW,cAAgB,GACzC,kBAAmBA,EAAW,mBAAqB,GACnD,YAAaA,EAAW,aAAe,GACvC,GAAIA,EAAW,iBAAmB,CAAE,iBAAkBA,EAAW,gBAAA,EAAqB,CAAC,EACvF,QAASA,EAAW,SAAW,GAC/B,QAASA,EAAW,SAAW,GAC/B,UAAWA,EAAW,WAAa,GACnC,UAAWA,EAAW,WAAa,GACnC,UAAWA,EAAW,WAAa,GACnC,OAAQA,EAAW,QAAU,GAC7B,WAAYA,EAAW,YAAc,GACrC,SAAUA,EAAW,UAAY,GACjC,WAAYA,EAAW,YAAc,GACrC,gBAAiBA,EAAW,iBAAmB,EAAA,EAE/C,CACA,QAAS,GACT,QAAS,GACT,UAAW,GACX,UAAW,GACX,UAAW,GACX,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,WAAY,GACZ,gBAAiB,GACjB,iBAAkB,EACpB,EAEIQ,EAA4CR,GAAY,MAC1DA,EAAW,MAAM,IAAYS,GAAA,CAC7B,IAAIC,EAAwC,KACxCD,EAAK,YAAc,OACjB,OAAOA,EAAK,YAAe,SAC7BC,EAAaD,EAAK,aAAe,GAAKA,EAAK,WAAa,KAE3CC,EAAA,OAAOD,EAAK,UAAU,GAGvC,IAAIE,EAA6B,KAC7B,OAAAF,EAAK,aAAe,OAClB,OAAOA,EAAK,aAAgB,SAC9BE,EAAcF,EAAK,cAAgB,GAAK,OAAOA,EAAK,WAAW,EAAI,KAEnEE,EAAcF,EAAK,aAGhB,CACL,SAAUA,EAAK,UAAY,KAC3B,QAASA,EAAK,SAAW,EACzB,QAASA,EAAK,SAAW,KACzB,QAASA,EAAK,SAAW,KACzB,MAAOA,EAAK,OAAS,KACrB,QAASA,EAAK,QAAUA,EAAK,QAAU,KACvC,MAAOA,EAAK,OAAS,KACrB,YAAAE,EACA,WAAAD,EACA,sBAAuBD,EAAK,uBAAyB,KACrD,wBAAyBA,EAAK,yBAA2B,KACzD,SAAUA,EAAK,UAAY,KAC3B,WAAYA,EAAK,YAAc,KAC/B,OAAQA,EAAK,QAAU,KACvB,QAASA,EAAK,SAAW,KACzB,YAAaA,EAAK,aAAe,CAAC,EAClC,OAAQA,EAAK,YAAc,KAC3B,SAAUA,EAAK,UAAY,GAC3B,gBAAiBA,EAAK,iBAAiB,MAAQ,KAC/C,kBAAmBA,EAAK,mBAAqB,KAC7C,iBAAkBA,EAAK,kBAAoB,OAE3C,GAAIA,EAAK,iBAAmB,CAAE,iBAAkBA,EAAK,gBAAA,EAAqB,CAAC,EAC3E,GAAIA,EAAK,IAAM,EACjB,CACD,CAAA,EACC,CAAC,EAECG,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA6E,CACxG,GAAI,CAAClB,EAAU,MAAA,IAAI,MAAM,gBAAgB,EACzC,MAAMmB,EAAW,MAAMX,EAAgB,kBAAkBR,EAAI,CAC3D,GAAGiB,EACH,OAAQA,EAAO,QAAU,GACzB,QAAS,QACT,QAASA,EAAO,SAAW,GAC3B,UAAWA,EAAO,WAAa,OAC/B,UAAWA,EAAO,WAAa,GAC/B,UAAWA,EAAO,WAAa,GAC/B,OAAQA,EAAO,QAAU,GACzB,WAAYA,EAAO,YAAc,GACjC,SAAUA,EAAO,UAAY,GAC7B,WAAYA,EAAO,YAAc,GACjC,gBAAiBA,EAAO,iBAAmB,GAC3C,iBAAkBA,EAAO,kBAAoB,GAC7C,MAAOC,EAAM,IAAaN,IAAA,CACxB,GAAGA,EACH,UAAW,GACX,QAAS,GACT,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,UAAW,GACX,OAAQ,GACR,QAAS,GACT,SAAU,GACV,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,QAAS,GACT,SAAU,GACV,SAAUA,EAAK,UAAY,GAC3B,iBAAkBA,EAAK,kBAAoB,GAC3C,kBAAmBA,EAAK,mBAAqB,EAAA,EAC7C,CAAA,CACH,EACD,GAAIO,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAW,IAAM,CACfrB,EAAM,CAAE,MAAO,UAAW,YAAa,wBAAyB,QAAS,UAAW,EACpFG,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAgBD,CAAE,EAAG,CAClE,EACA,QAAUoB,GAAoC,CACtCtB,EAAA,CACJ,MAAOsB,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOJ,EAA+BC,IAAiC,CAC1F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAEA,OAAId,EAAmBkB,EAAA,IAAA,MAAA,CAAI,SAAU,aAAA,EACjCjB,EAAgBkB,EAAAA,KAAC,MAAI,CAAA,SAAA,CAAA,uBAAqBjB,aAAiB,MAAQA,EAAM,QAAU,eAAA,EAAgB,EAGrGgB,EAAA,IAACE,EAAA,CACC,KAAK,OACL,MAAO9B,EAAE,yBAAyB,EAClC,cAAAe,EACA,aAAAE,EACA,SAAUU,EACV,aAAcN,EAAS,UACvB,YAAAd,EACA,WAAYE,GAAc,OAC1B,UAAWA,GAAY,YAAc,CAAC,CAAE,GAAIA,EAAW,YAAY,IAAM,GAAI,aAAc,CAAC,CAACA,EAAW,YAAY,YAAa,CAAC,EAAI,CAAA,CAAC,CACzI,CAEJ,EAEA,SAAwBsB,GAAkB,CAClC,KAAA,CAAE,EAAA/B,CAAE,EAAIC,EAAe,EAC7B,cACG+B,EACC,CAAA,SAAA,CAAAJ,EAAA,IAACK,EAAK,CAAA,MAAOjC,EAAE,yBAAyB,CAAG,CAAA,QAC1CD,EAAoB,CAAA,CAAA,CAAA,EACvB,CAEJ"}