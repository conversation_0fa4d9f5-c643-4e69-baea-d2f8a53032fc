/**
 * React Hook for EKB Proxy Operations
 * 
 * This hook provides a convenient way to use the EKB proxy service
 * with React Query for caching, loading states, and error handling.
 */

import { useMutation, useQuery, useQueryClient, type UseQueryOptions } from '@tanstack/react-query';
import { ekbProxyService, type EkbProxyResponse } from '@/services/ekbProxyService';
import { toast } from '@/lib/useToast';
import type {
  // Vessel types
  ExportVesselDto,
  ImportVesselDto,
  LocalVesselDto,
  CreateUpdateExportVesselDto,
  CreateUpdateImportVesselDto,
  VesselListRequestDto,
  // Paged result types
  PagedResultDtoOfExportVesselDto,
  PagedResultDtoOfImportVesselDto,
  PagedResultDtoOfLocalVesselDto,
  PagedResultDtoOfTradingVesselDto,
  PagedResultDtoOfVesselHeaderDto,
  PagedResultDtoOfAgentDto,
  PagedResultDtoOfBusinessPartnerDto,
  PagedResultDtoOfJettyDto,
  PagedResultDtoOfMasterTenantDto,
  // Application configuration
  ApplicationConfigurationDto,
  // Query types
  QueryParametersDto,
} from '@/clientEkb/types.gen';

// Query keys for React Query
export const EKB_QUERY_KEYS = {
  // Vessel query keys
  exportVessels: ['ekb', 'export-vessels'],
  exportVessel: (id: string) => ['ekb', 'export-vessels', id],
  importVessels: ['ekb', 'import-vessels'],
  importVessel: (id: string) => ['ekb', 'import-vessels', id],
  localVessels: ['ekb', 'local-vessels'],
  localVessel: (id: string) => ['ekb', 'local-vessels', id],
  tradingVessels: ['ekb', 'trading-vessels'],
  tradingVessel: (id: string) => ['ekb', 'trading-vessels', id],
  vesselHeaders: ['ekb', 'vessel-headers'],
  vesselItems: ['ekb', 'vessel-items'],
  // Master data query keys
  agents: ['ekb', 'agents'],
  businessPartners: ['ekb', 'business-partners'],
  jetties: ['ekb', 'jetties'],
  tenants: ['ekb', 'tenants'],
  // Application configuration
  applicationConfig: ['ekb', 'application-configuration'],
  // Search and filter keys
  search: (params: QueryParametersDto) => ['ekb', 'search', params],
} as const;

/**
 * Hook for GET requests through EKB proxy
 */
export function useEkbQuery<T = unknown>(
  path: string,
  queryKey: readonly unknown[],
  options?: Omit<UseQueryOptions<EkbProxyResponse<T>>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey,
    queryFn: () => ekbProxyService.get<T>(path),
    ...options,
  });
}

/**
 * Hook for mutations (POST, PUT, PATCH, DELETE) through EKB proxy
 */
export function useEkbMutation<TData = unknown, TVariables = unknown>(
  mutationFn: (variables: TVariables) => Promise<EkbProxyResponse<TData>>,
  options?: {
    onSuccess?: (data: EkbProxyResponse<TData>, variables: TVariables) => void;
    onError?: (error: Error, variables: TVariables) => void;
    invalidateQueries?: unknown[][];
    showSuccessToast?: boolean;
    showErrorToast?: boolean;
    successMessage?: string;
  }
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn,
    onSuccess: (data, variables) => {
      // Show success toast if enabled
      if (options?.showSuccessToast && data.status >= 200 && data.status < 300) {
        toast({
          title: "Success",
          description: options.successMessage || "Operation completed successfully",
        });
      }

      // Invalidate specified queries
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }

      // Call custom success handler
      options?.onSuccess?.(data, variables);
    },
    onError: (error, variables) => {
      // Show error toast if enabled
      if (options?.showErrorToast) {
        toast({
          title: "Error",
          description: error?.message || "An error occurred",
          variant: "destructive",
        });
      }

      // Call custom error handler
      options?.onError?.(error, variables);
    },
  });
}

/**
 * Hook for file upload mutations
 */
export function useEkbFileUpload<TData = unknown>(
  path: string,
  options?: {
    onSuccess?: (data: EkbProxyResponse<TData>) => void;
    onError?: (error: Error) => void;
    invalidateQueries?: unknown[][];
    showSuccessToast?: boolean;
    showErrorToast?: boolean;
  }
) {
  return useEkbMutation(
    async (variables: { files: File[]; additionalData?: Record<string, string> }) => {
      return ekbProxyService.uploadFile<TData>(path, variables.files, variables.additionalData);
    },
    {
      ...options,
      successMessage: "File uploaded successfully",
    }
  );
}

/**
 * Specific hooks for common EKB operations
 */

// Export Vessel hooks
export function useExportVessels(options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfExportVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfExportVesselDto>('/api/ekb/export-vessel', EKB_QUERY_KEYS.exportVessels, options);
}

export function useExportVessel(id: string, options?: Omit<UseQueryOptions<EkbProxyResponse<ExportVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<ExportVesselDto>(`/api/ekb/export-vessel/${id}`, EKB_QUERY_KEYS.exportVessel(id), {
    enabled: !!id,
    ...options,
  });
}

export function useCreateExportVessel() {
  return useEkbMutation<ExportVesselDto, CreateUpdateExportVesselDto>(
    (vesselData) => ekbProxyService.post<ExportVesselDto>('/api/ekb/export-vessel', vesselData),
    {
      invalidateQueries: [[...EKB_QUERY_KEYS.exportVessels]],
      showSuccessToast: true,
      showErrorToast: true,
      successMessage: "Export vessel created successfully",
    }
  );
}

export function useUpdateExportVessel(id: string) {
  return useEkbMutation<ExportVesselDto, CreateUpdateExportVesselDto>(
    (vesselData) => ekbProxyService.put<ExportVesselDto>(`/api/ekb/export-vessel/${id}`, vesselData),
    {
      invalidateQueries: [[...EKB_QUERY_KEYS.exportVessels], [...EKB_QUERY_KEYS.exportVessel(id)]],
      showSuccessToast: true,
      showErrorToast: true,
      successMessage: "Export vessel updated successfully",
    }
  );
}

// Import Vessel hooks
export function useImportVessels(options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfImportVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfImportVesselDto>('/api/ekb/import-vessel', EKB_QUERY_KEYS.importVessels, options);
}

export function useImportVessel(id: string, options?: Omit<UseQueryOptions<EkbProxyResponse<ImportVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<ImportVesselDto>(`/api/ekb/import-vessel/${id}`, EKB_QUERY_KEYS.importVessel(id), {
    enabled: !!id,
    ...options,
  });
}

export function useCreateImportVessel() {
  return useEkbMutation<ImportVesselDto, CreateUpdateImportVesselDto>(
    (vesselData) => ekbProxyService.post<ImportVesselDto>('/api/ekb/import-vessel', vesselData),
    {
      invalidateQueries: [[...EKB_QUERY_KEYS.importVessels]],
      showSuccessToast: true,
      showErrorToast: true,
      successMessage: "Import vessel created successfully",
    }
  );
}

// Local Vessel hooks
export function useLocalVessels(options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfLocalVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfLocalVesselDto>('/api/ekb/local-vessel', EKB_QUERY_KEYS.localVessels, options);
}

export function useLocalVessel(id: string, options?: Omit<UseQueryOptions<EkbProxyResponse<LocalVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<LocalVesselDto>(`/api/ekb/local-vessel/${id}`, EKB_QUERY_KEYS.localVessel(id), {
    enabled: !!id,
    ...options,
  });
}

// Trading Vessel hooks
export function useTradingVessels(options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfTradingVesselDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfTradingVesselDto>('/api/ekb/trading-vessel', EKB_QUERY_KEYS.tradingVessels, options);
}

// Vessel Headers and Items
export function useVesselHeaders(request: VesselListRequestDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfVesselHeaderDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfVesselHeaderDto>('/api/ekb/vessel/vessel-headers', EKB_QUERY_KEYS.vesselHeaders, {
    enabled: !!request,
    ...options,
  });
}

// Master data hooks
export function useAgents(request: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfAgentDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfAgentDto>('/api/master/agent/filter-list', [...EKB_QUERY_KEYS.agents, request], {
    enabled: !!request,
    ...options,
  });
}

export function useBusinessPartners(request: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfBusinessPartnerDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfBusinessPartnerDto>('/api/ekb/business-partner/filter-list', [...EKB_QUERY_KEYS.businessPartners, request], {
    enabled: !!request,
    ...options,
  });
}

export function useJetties(request: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfJettyDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfJettyDto>('/api/ekb/jetty/filter-list', [...EKB_QUERY_KEYS.jetties, request], {
    enabled: !!request,
    ...options,
  });
}

export function useTenants(request: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfMasterTenantDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfMasterTenantDto>('/api/ekb/tenant/filter-list', [...EKB_QUERY_KEYS.tenants, request], {
    enabled: !!request,
    ...options,
  });
}

// Get application configuration
export function useApplicationConfiguration(options?: Omit<UseQueryOptions<EkbProxyResponse<ApplicationConfigurationDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<ApplicationConfigurationDto>('/api/abp/application-configuration', EKB_QUERY_KEYS.applicationConfig, {
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
}

// Note: Filter operations are POST requests, so they're used directly in DataGrid queryFn
// rather than as React Query hooks. The service methods above handle the actual API calls.

// Filter hooks for master data (these are the ones currently used in the frontend)
export function useFilterBusinessPartners(filterData: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfBusinessPartnerDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfBusinessPartnerDto>(
    '/api/ekb-proxy/master-filter/business-partner',
    [...EKB_QUERY_KEYS.businessPartners, 'filter', filterData],
    {
      enabled: !!filterData,
      ...options,
    }
  );
}

export function useFilterTenants(filterData: QueryParametersDto, options?: Omit<UseQueryOptions<EkbProxyResponse<PagedResultDtoOfMasterTenantDto>>, 'queryKey' | 'queryFn'>) {
  return useEkbQuery<PagedResultDtoOfMasterTenantDto>(
    '/api/ekb-proxy/master-filter/tenant',
    [...EKB_QUERY_KEYS.tenants, 'filter', filterData],
    {
      enabled: !!filterData,
      ...options,
    }
  );
}

/**
 * Example usage in a React component:
 * 
 * ```tsx
 * import { useVessels, useCreateVessel, useUploadVesselDocuments } from '@/hooks/useEkbProxy';
 * 
 * function VesselManagement() {
 *   const { data: vessels, isLoading, error } = useVessels();
 *   const createVessel = useCreateVessel();
 *   const uploadDocuments = useUploadVesselDocuments('vessel-id');
 * 
 *   const handleCreateVessel = (vesselData: any) => {
 *     createVessel.mutate(vesselData);
 *   };
 * 
 *   const handleFileUpload = (files: File[]) => {
 *     uploadDocuments.mutate({ files });
 *   };
 * 
 *   if (isLoading) return <div>Loading...</div>;
 *   if (error) return <div>Error: {error.message}</div>;
 * 
 *   return (
 *     <div>
 *       {vessels?.data?.map(vessel => (
 *         <div key={vessel.id}>{vessel.name}</div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
