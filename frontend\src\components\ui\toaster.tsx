import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { useToast } from "@/lib/useToast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider swipeDirection="right">
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="flex w-full justify-between gap-2">
              <div className="flex flex-col gap-3">
                <div className="space-y-1">
                  {title && <ToastTitle>{title}</ToastTitle>}
                  {description && (
                    <ToastDescription>{description}</ToastDescription>
                  )}
                </div>
                <div>{action}</div>
              </div>
              <div>
                <ToastClose />
              </div>
            </div>
          </Toast>
        )
      })}
      <ToastViewport asChild>
        <div
          style={{
            position: "fixed",
            top: "1rem",
            right: "1rem",
            left: "auto",
            bottom: "auto",
            zIndex: 9999,
            display: "flex",
            flexDirection: "column",
            alignItems: "flex-end",
          }}
        />
      </ToastViewport>
    </ToastProvider>
  )
}
