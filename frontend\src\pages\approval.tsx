import React, { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuCheckboxItem, DropdownMenuItem, DropdownMenuLabel } from '@/components/ui/dropdown-menu';
import { IconChevronDown, IconDotsVertical } from '@tabler/icons-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';

interface PendingApproval {
  id: string;
  vesselName: string;
  arrivalDate: string;
  departureDate: string;
  itemName: string;
  requestBy: string;
}

const mockPendingApprovalData: PendingApproval[] = Array(10).fill(null).map((_, i) => ({
  id: `pending_${i + 1}`,
  vesselName: `MV. PENDING REQUEST V. 00${i + 1}`,
  arrivalDate: '2025-05-05',
  departureDate: '2025-05-06',
  itemName: 'CRUDE OIL 200MT',
  requestBy: 'USER3',
}));

export default function ApprovalList() {
  const [filter, setFilter] = useState('');
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [visibleColumns, setVisibleColumns] = useState<Set<keyof PendingApproval>>(
    new Set(Object.keys(mockPendingApprovalData[0]) as (keyof PendingApproval)[])
  );
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectNotes, setRejectNotes] = useState('');
  const [currentRejectId, setCurrentRejectId] = useState<string | null>(null);

  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [currentApproveId, setCurrentApproveId] = useState<string | null>(null);

  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');

  const filteredData = mockPendingApprovalData.filter(approval =>
    Object.values(approval).some(value =>
      value.toString().toLowerCase().includes(filter.toLowerCase())
    )
  );

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(new Set(filteredData.map(row => row.id)));
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleRowSelect = (id: string, checked: boolean) => {
    const newSelection = new Set(selectedRows);
    if (checked) {
      newSelection.add(id);
    } else {
      newSelection.delete(id);
    }
    setSelectedRows(newSelection);
  };

  const handleToggleColumn = (columnKey: keyof PendingApproval, checked: boolean) => {
    const newVisibleColumns = new Set(visibleColumns);
    if (checked) {
      newVisibleColumns.add(columnKey);
    } else {
      newVisibleColumns.delete(columnKey);
    }
    setVisibleColumns(newVisibleColumns);
  };

  const handleApproveClick = (id: string) => {
    setCurrentApproveId(id);
    setIsApproveDialogOpen(true);
  };

  const handleApproveConfirm = () => {
    if (currentApproveId) {
      console.log(`Approving application with ID: ${currentApproveId}`);
      // Implement actual approve logic here
      setIsApproveDialogOpen(false);
      setCurrentApproveId(null);
    }
  };

  const handleRejectClick = (id: string) => {
    setCurrentRejectId(id);
    setIsRejectDialogOpen(true);
  };

  const handleRejectConfirm = () => {
    if (currentRejectId) {
      console.log(`Rejecting application with ID: ${currentRejectId} with notes: ${rejectNotes}`);
      // Implement actual reject logic here
      setIsRejectDialogOpen(false);
      setRejectNotes('');
      setCurrentRejectId(null);
    }
  };

  const handlePreview = (id: string) => {
    console.log(`Preview document for ID: ${id}`);
    setPreviewDocumentSrc('/pdf/surat1.pdf'); // Example PDF path
    setIsPreviewDialogOpen(true);
  };

  return (
    <AppLayout>
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Waiting Approvals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <Input
                placeholder="Filter lines..."
                value={filter}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFilter(e.target.value)}
                className="max-w-sm"
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-auto">
                    Columns <IconChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {Object.keys(mockPendingApprovalData[0]).map((key) => (
                    <DropdownMenuCheckboxItem
                      key={key}
                      className="capitalize"
                      checked={visibleColumns.has(key as keyof PendingApproval)}
                      onCheckedChange={(checked: boolean | 'indeterminate') => handleToggleColumn(key as keyof PendingApproval, checked === true)}
                    >
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[30px]">
                      <Checkbox
                        checked={selectedRows.size === filteredData.length && filteredData.length > 0}
                        onCheckedChange={(checked: boolean | 'indeterminate') => handleSelectAll(checked === true)}
                      />
                    </TableHead>
                    {Object.keys(mockPendingApprovalData[0]).map((key) => (visibleColumns.has(key as keyof PendingApproval) && key !== 'id' &&
                      <TableHead key={key} className="capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </TableHead>
                    ))}
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((approval) => (
                    <TableRow key={approval.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedRows.has(approval.id)}
                          onCheckedChange={(checked: boolean | 'indeterminate') => handleRowSelect(approval.id, checked === true)}
                        />
                      </TableCell>
                      {Object.entries(approval).map(([key, value]) => (visibleColumns.has(key as keyof PendingApproval) && key !== 'id' &&
                        <TableCell key={key}>{value}</TableCell>
                      ))}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <IconDotsVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handlePreview(approval.id)}>
                              Preview
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleApproveClick(approval.id)}>
                              Approve
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRejectClick(approval.id)}>
                              Reject
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500">
                {selectedRows.size} of {filteredData.length} row(s) selected.
              </div>
              <div className="space-x-2">
                <Button variant="outline" size="sm">Previous</Button>
                <Button variant="outline" size="sm">Next</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reject Request Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Reject Request</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={rejectNotes}
                onChange={(e) => setRejectNotes(e.target.value)}
                placeholder="Value"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleRejectConfirm} className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">Reject</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Approve Confirmation Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirmation</DialogTitle>
            <DialogDescription>Are you sure you want to approve this document?</DialogDescription>
          </DialogHeader>
          <DialogFooter className="justify-end">
            <Button onClick={handleApproveConfirm} className="px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">Yes Approve</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </AppLayout>
  );
}
