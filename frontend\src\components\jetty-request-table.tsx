import type { FilterCondition, FilterGroup, FilterOperator, JettyRequestDto, LogicalOperator } from "@/client/types.gen";
import { DataTable } from "@/components/data-table/DataTable";
import FilterSortBar, { type SortDirection } from "@/components/filter-sort-bar";
import { Button } from "@/components/ui/button";
import ErrorBoundary from "@/components/ui/error-boundary";
import TableSkeleton from "@/components/ui/table-skeleton";
import { useJettyRequests } from "@/lib/hooks/useJettyRequests";
import { router } from "@inertiajs/react";
import { type ColumnDef, type PaginationState } from "@tanstack/react-table";
import { AlertTriangle, Pencil, Plus, RefreshCw } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { ContentCard } from "./layout/content-card";

const FILTER_FIELDS = [
  { value: "docNum", label: "Document Number" },
  { value: "vesselName", label: "Vessel Name" },
  { value: "jetty", label: "Jetty" },
  { value: "arrivalDate", label: "Arrival Date" },
  { value: "departureDate", label: "Departure Date" },
  // Add more fields as needed
];
const FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [
  { value: "Equals", label: "Equals" },
  { value: "Contains", label: "Contains" },
  { value: "NotEquals", label: "Not Equals" },
  { value: "GreaterThan", label: ">" },
  { value: "LessThan", label: "<" },
];

const columns: ColumnDef<JettyRequestDto>[] = [
  {
    accessorKey: "docNum",
    header: "Document Number",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "vesselName",
    header: "Vessel Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jetty",
    header: "Jetty",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "arrivalDate",
    header: "Arrival Date",
    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
  },
  {
    accessorKey: "departureDate",
    header: "Departure Date",
    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
  },
  // Edit button column
  {
    id: "edit",
    header: "",
    cell: ({ row }) => {
      const id = row.original.id;
      const handleEdit = () => router.visit(`/application/${id}/edit`);
      const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === "Enter" || e.key === " ") {
          handleEdit();
        }
      };
      return (
        <Button
          onClick={handleEdit}
          onKeyDown={handleKeyDown}
          aria-label="Edit Application"
          tabIndex={0}
          variant="outline"
          size="icon"
          className="ml-2 h-8 w-8"
        >
          <Pencil className="w-4 h-4" aria-hidden="true" />
        </Button>
      );
    },
    enableSorting: false,
    enableColumnFilter: false,
  },
  // Add more columns as needed
];

const JettyRequestTableContent: React.FC = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Reset to first page when filters or sorts change
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [filters, sorts]);

  // Build filter group for backend
  const filterGroup: FilterGroup | undefined = useMemo(() => {
    if (!filters.length) return undefined;
    return {
      operator: "And" as LogicalOperator,
      conditions: filters,
    };
  }, [filters]);

  // Build sorting string for backend
  const sortingStr = useMemo(() => {
    if (!sorts.length) return undefined;
    return sorts.map(s => `${s.field} ${s.direction}`).join(", ");
  }, [sorts]);

  const { data, isLoading, error, refetch } = useJettyRequests(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    sortingStr
  );

  // New Application button
  const handleNewApplication = () => router.visit("/application/create");

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Error UI
  if (error) {
    console.error("JettyRequests error:", error);
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center">
        <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
        <h3 className="font-semibold text-destructive mb-2">Error loading data</h3>
        <p className="text-sm text-muted-foreground mb-4">{String(error.message)}</p>
        <Button onClick={() => window.location.reload()} variant="destructive">Retry</Button>
      </div>
    );
  }

  return (
    <ContentCard>
      <div className="text-xl font-bold px-2 pt-2 pb-1">Jetty Application List</div>
      <FilterSortBar
        filterFields={FILTER_FIELDS}
        operators={FILTER_OPERATORS}
        filters={filters}
        sorts={sorts}
        onFiltersChange={setFilters}
        onSortsChange={setSorts}
      >
        <div className="ml-auto flex items-center gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="icon"
            className="h-10 w-10"
            disabled={isLoading || isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            onClick={handleNewApplication}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base"
            size="lg"
          >
            <Plus className="h-5 w-5" /> New Application
          </Button>
        </div>
      </FilterSortBar>
      {isLoading ? (
        <TableSkeleton columns={columns} />
      ) : (
        <DataTable
          title=""
          columns={columns}
          data={data?.items ?? []}
          totalCount={data?.totalCount ?? 0}
          isLoading={isLoading}
          manualPagination={true}
          pageSize={pagination.pageSize}
          onPaginationChange={setPagination}
          hideDefaultFilterbar={true}
          enableRowSelection={false}
          manualSorting={true}
        // Sorting and filtering are handled above
        />
      )}
    </ContentCard>
  );
};

const JettyRequestTable: React.FC = () => {
  return (
    <ErrorBoundary>
      <JettyRequestTableContent />
    </ErrorBoundary>
  );
};

export default JettyRequestTable; 