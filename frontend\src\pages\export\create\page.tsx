import { ekbProxyService } from '@/services/ekbProxyService';
import type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { ExportVesselFormWithData } from '@/components/jetty/vessel/export/export-vessel-form';
import type { ExportVesselHeaderForm } from '@/components/jetty/vessel/export/export-vessel-header-schema';
import type { ExportVesselItemForm } from '@/components/jetty/vessel/export/export-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { Head, router } from '@inertiajs/react';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const ExportVesselCreatePage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: ExportVesselHeaderForm; items: ExportVesselItemForm[] }) => {
      const response = await ekbProxyService.createExportVessel({
        ...header,
        vesselId: header.vesselId ? String(header.vesselId) : '',
        jettyId: header.jettyId ? String(header.jettyId) : '',
        concurrencyStamp: header.concurrencyStamp ? String(header.concurrencyStamp) : '',
        items: items.map(item => ({
          ...item,
          createdBy: '',
          docType: '',
          isScan: '',
          isOriginal: '',
          isActive: true,
          isDeleted: false,
          isSend: '',
          isFeOri: '',
          isFeSend: '',
          isChange: '',
          isFeChange: '',
          isFeActive: '',
          deleted: '',
          isUrgent: '',
          tenantId: item.tenantId || '',
          businessPartnerId: item.businessPartnerId || '',
          concurrencyStamp: item.concurrencyStamp || '',
        })),
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: (data) => {
      toast({ title: 'Success', description: 'Export vessel created.', variant: 'success' });
      if (data && data.id) {
        router.visit(`/export/edit/${data.id}`);
      }
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  return (
    <ExportVesselFormWithData
      mode="create"
      title={t('pages.vessel.create.export')}
      initialHeader={{} as Partial<ExportVesselHeaderForm>}
      initialItems={[]}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
    />
  );
};

export default function ExportVesselCreate() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.create.export')} />
      <ExportVesselCreatePage />
    </AppLayout>
  );
}