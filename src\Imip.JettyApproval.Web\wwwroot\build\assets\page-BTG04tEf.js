import{j as o,u as D,h as T,f as N}from"./vendor-6tJeyfYI.js";import{F as m,A as P,u as b,M as y}from"./app-layout-rNt37hVL.js";import{L as W}from"./local-vessel-form-B3V1uWrj.js";import{t as i}from"./date-convert-rY_-W0p4.js";import{$ as j,q as L}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./DocumentPreviewDialog-C1lfiEeE.js";import"./dialog-BmEXyFlW.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */const E=()=>{const{t:u}=m(),{props:l}=L(),{toast:c}=b(),s=typeof l.id=="string"?l.id:void 0,p=D(),{data:e,isLoading:v,isError:S,error:d}=T({queryKey:["local-vessel",s],queryFn:async()=>s?(await y.getLocalVesselWithItems(s)).data:null,enabled:!!s}),f=e?{docNum:e.docNum??"",voyage:e.voyage??"",vesselArrival:i(e.vesselArrival??"")??"",vesselDeparture:i(e.vesselDeparture??"")??"",asideDate:i(e.asideDate??"")??"",castOfDate:i(e.castOfDate??"")??"",vesselId:e.vesselId??"",bargeId:e.bargeId??"",jettyId:e.jettyId??"",portOriginId:e.portOriginId??"",destinationPortId:e.destinationPortId??"",postingDate:e.postingDate??"",...e.concurrencyStamp?{concurrencyStamp:e.concurrencyStamp}:{},deleted:e.deleted??"",docType:e.docType??"",docStatus:e.docStatus??"",statusBms:e.statusBms??"",transType:e.transType??"",status:e.status??"",vesselType:e.vesselType??"",shipment:e.shipment??"",portOrigin:e.portOrigin??"",destinationPort:e.destinationPort??""}:{deleted:"",docType:"",docStatus:"",statusBms:"",transType:"",status:"",vesselType:"",shipment:"",portOrigin:"",destinationPort:"",concurrencyStamp:""},I=e?.items?e.items.map(t=>{let n=null;t.unitWeight!=null&&(typeof t.unitWeight=="string"?n=t.unitWeight!==""?t.unitWeight:null:n=String(t.unitWeight));let r=null;return t.grossWeight!=null&&(typeof t.grossWeight=="string"?r=t.grossWeight!==""?Number(t.grossWeight):null:r=t.grossWeight),{itemName:t.itemName??null,itemQty:t.itemQty??0,unitQty:t.unitQty??null,remarks:t.remarks??null,ajuNo:t.ajuNo??null,regDate:t.regDate?t.regDate:null,regNo:t.regNo??null,grossWeight:r,unitWeight:n,shippingInstructionNo:t.shippingInstructionNo??null,shippingInstructionDate:t.shippingInstructionDate??null,letterNo:t.letterNo??null,letterDate:t.letterDate??null,status:t.status??null,regType:t.regType??null,attachments:t.attachments??[],tenant:t.tenantName??null,tenantId:t.tenantId??"",businessPartner:t.businessPartner?.name??null,businessPartnerId:t.businessPartnerId??null,concurrencyStamp:t.concurrencyStamp??void 0,...t.concurrencyStamp?{concurrencyStamp:t.concurrencyStamp}:{},id:t.id??""}}):[],g=N({mutationFn:async({header:t,items:n})=>{if(!s)throw new Error("No ID provided");const r=await y.updateLocalVessel(s,{...t,docNum:t.docNum??"",docType:"Local",deleted:t.deleted??"",docStatus:t.docStatus??"Open",statusBms:t.statusBms??"",transType:t.transType??"",status:t.status??"",vesselType:t.vesselType??"",shipment:t.shipment??"",portOrigin:t.portOrigin??"",destinationPort:t.destinationPort??"",concurrencyStamp:t.concurrencyStamp??"",items:n.map(a=>({...a,createdBy:"",docType:"",isScan:"",isOriginal:"",isActive:!0,isDeleted:!1,isSend:"",isFeOri:"",isFeSend:"",isChange:"",isFeChange:"",isFeActive:"",deleted:"",isUrgent:"",tenantId:a.tenantId||"",concurrencyStamp:a.concurrencyStamp||"",businessPartnerId:a.businessPartnerId||""}))});if(r.error)throw new Error(r.error);return r.data},onSuccess:()=>{c({title:"Success",description:"Local vessel updated.",variant:"success"}),p.invalidateQueries({queryKey:["local-vessel",s]})},onError:t=>{c({title:t instanceof Error?t.message:t?.error?.message||"Error",description:t instanceof Error?void 0:t?.error?.details,variant:"destructive"})}}),h=async(t,n)=>{await g.mutateAsync({header:t,items:n})};return v?o.jsx("div",{children:"Loading..."}):S?o.jsxs("div",{children:["Error loading data: ",d instanceof Error?d.message:"Unknown error"]}):o.jsx(W,{mode:"edit",title:u("pages.vessel.edit.local"),initialHeader:f,initialItems:I,onSubmit:h,isSubmitting:g.isPending,queryClient:p,vesselData:e||void 0,jettyList:e?.masterJetty?[{id:e.masterJetty.id??"",isCustomArea:!!e.masterJetty.isCustomArea}]:[]})};function X(){const{t:u}=m();return o.jsxs(P,{children:[o.jsx(j,{title:u("pages.vessel.edit.local")}),o.jsx(E,{})]})}export{X as default};
//# sourceMappingURL=page-BTG04tEf.js.map
