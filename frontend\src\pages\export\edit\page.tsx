import { ekbProxyService } from '@/services/ekbProxyService';
import type { RemoteServiceErrorResponse } from '@/clientEkb/types.gen';
import { ExportVesselFormWithData } from '@/components/jetty/vessel/export/export-vessel-form';
import type { ExportVesselHeaderForm } from '@/components/jetty/vessel/export/export-vessel-header-schema';
import type { ExportVesselItemForm } from '@/components/jetty/vessel/export/export-vessel-item-schema';
import AppLayout from '@/layouts/app-layout';
import { useToast } from '@/lib/useToast';
import { toDatetimeLocalString } from '@/lib/utils/date-convert';
import { Head, usePage } from '@inertiajs/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const ExportVesselEditPage = () => {
  const { t } = useTranslation();
  const { props } = usePage();
  const { toast } = useToast();
  const id = typeof props.id === 'string' ? props.id : undefined;
  const queryClient = useQueryClient();

  const {
    data: vesselData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['export-vessel', id],
    queryFn: async () => {
      if (!id) return null;
      const response = await ekbProxyService.getExportVesselWithItems(id);
      return response.data;
    },
    enabled: !!id,
  });

  const initialHeader: Partial<ExportVesselHeaderForm> = vesselData
    ? {
      docNum: vesselData.docNum ?? '',
      voyage: vesselData.voyage ?? '',
      vesselArrival: toDatetimeLocalString(vesselData.vesselArrival ?? ''),
      vesselDeparture: toDatetimeLocalString(vesselData.vesselDeparture ?? ''),
      vesselId: vesselData.vesselId ?? '',
      jettyId: vesselData.jettyId ?? '',
      portOriginId: vesselData.portOriginId ?? '',
      destinationPortId: vesselData.destinationPortId ?? '',
      postingDate: vesselData.postingDate ?? '',
      asideDate: toDatetimeLocalString(vesselData.asideDate ?? ''),
      castOfDate: toDatetimeLocalString(vesselData.castOfDate ?? ''),
    }
    : {};

  const initialItems: ExportVesselItemForm[] = vesselData?.items
    ? vesselData.items.map(item => {
      const mapped = {
        itemName: item.itemName ?? '',
        itemQty: item.itemQty ?? 0,
        unitQty: item.unitQty ?? '',
        remarks: item.remarks ?? '',
        ajuNo: item.ajuNo ?? '',
        regDate: item.regDate ? item.regDate : undefined,
        regNo: item.regNo ?? '',
        grossWeight: item.grossWeight ?? '',
        unitWeight: item.unitWeight ?? '',
        shippingInstructionNo: item.shippingInstructionNo ?? '',
        shippingInstructionDate: item.shippingInstructionDate ? item.shippingInstructionDate : undefined,
        letterNo: item.letterNo ?? '',
        letterDate: item.letterDate ? item.letterDate : undefined,
        status: item.status ?? '',
        regType: item.regType ?? '',
        attachments: item.attachments ?? [],
        tenant: item.tenantName ?? '',
        tenantId: item.tenantId ?? '',
        businessPartner: item.businessPartner?.name ?? '',
        businessPartnerId: item.businessPartnerId ?? '',
        concurrencyStamp: item.concurrencyStamp ?? undefined,
        id: item.id ?? undefined,
      } as ExportVesselItemForm & { concurrencyStamp?: string; id?: string };
      return mapped;
    })
    : [];

  const mutation = useMutation({
    mutationFn: async ({ header, items }: { header: ExportVesselHeaderForm; items: ExportVesselItemForm[] }) => {
      if (!id) throw new Error('No ID provided');
      const response = await ekbProxyService.updateExportVessel(id, {
        ...header,
        docStatus: header.docStatus ?? 'Open',
        items: items.map(item => ({
          ...item,
          createdBy: '',
          docType: '',
          isScan: '',
          isOriginal: '',
          isActive: true,
          isDeleted: false,
          isSend: '',
          isFeOri: '',
          isFeSend: '',
          isChange: '',
          isFeChange: '',
          isFeActive: '',
          deleted: '',
          isUrgent: '',
          tenantId: item.tenantId || '',
          businessPartnerId: item.businessPartnerId || '',
        })),
      });
      if (response.error) throw new Error(response.error);
      return response.data;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Export vessel updated.', variant: 'success' });
      queryClient.invalidateQueries({ queryKey: ['export-vessel', id] });
    },
    onError: (err: RemoteServiceErrorResponse) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSubmit = async (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => {
    await mutation.mutateAsync({ header, items });
  };

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading data: {error instanceof Error ? error.message : 'Unknown error'}</div>;

  return (
    <ExportVesselFormWithData
      mode="edit"
      title={t('pages.vessel.edit.export')}
      initialHeader={initialHeader}
      initialItems={initialItems}
      onSubmit={handleSubmit}
      isSubmitting={mutation.isPending}
    />
  );
};

export default function ExportVEsselEdit() {
  const { t } = useTranslation();
  return (
    <AppLayout>
      <Head title={t('pages.vessel.edit.export')} />
      <ExportVesselEditPage />
    </AppLayout>
  );
}