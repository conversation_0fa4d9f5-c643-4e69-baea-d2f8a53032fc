'use client'

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import '@/styles/custom-scrollbar.css'
import { Check, ChevronsUpDown, X } from "lucide-react"
import * as React from 'react'
import { type FieldValues, type Path, type UseFormRegister } from "react-hook-form"

export type MultiSelectOption = {
  value: string
  label: string
  description?: string
  data?: unknown // For storing additional data like jetty info
}

type MultiSelectProps<T extends FieldValues = FieldValues> = {
  options: MultiSelectOption[]
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  maxHeight?: number
  mode?: 'multiple' | 'single'
  name?: Path<T>
  register?: UseFormRegister<T>
  valueAsNumber?: boolean
  searchValue?: string
  onSearchValueChange?: (val: string) => void
  // Enhanced props for dynamic data loading
  isLoading?: boolean
  onLoadMore?: () => void
  hasMore?: boolean
  loadingText?: string
  emptyText?: string
  // For jetty-specific functionality
  showDescription?: boolean
  onSelectionChange?: (selectedOptions: MultiSelectOption[]) => void
}

export const MultiSelect = <T extends FieldValues = FieldValues>({
  options = [],
  value = [],
  onChange,
  placeholder = 'Select options',
  className,
  disabled = false,
  maxHeight = 300,
  mode = 'multiple',
  name,
  register,
  valueAsNumber = false,
  searchValue: controlledSearchValue,
  onSearchValueChange,
  isLoading = false,
  onLoadMore,
  hasMore = false,
  loadingText = 'Loading...',
  emptyText = 'No options found',
  showDescription = false,
  onSelectionChange,
}: MultiSelectProps<T>) => {
  const [open, setOpen] = React.useState(false)
  const [uncontrolledSearchValue, setUncontrolledSearchValue] = React.useState('')
  const searchValue = controlledSearchValue !== undefined ? controlledSearchValue : uncontrolledSearchValue
  const setSearchValue = onSearchValueChange || setUncontrolledSearchValue
  const scrollAreaRef = React.useRef<HTMLDivElement>(null)

  // Handle wheel events for scrolling
  const handleWheel = React.useCallback((e: WheelEvent) => {
    if (scrollAreaRef.current) {
      const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollableElement) {
        e.preventDefault();
        scrollableElement.scrollTop += e.deltaY;
      }
    }
  }, []);

  // Add wheel event listener when dropdown is open
  React.useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (open && scrollArea) {
      scrollArea.addEventListener('wheel', handleWheel, { passive: false });
      return () => {
        scrollArea.removeEventListener('wheel', handleWheel);
      };
    }
  }, [open, handleWheel]);

  // Single selection mode helper
  const isSingleMode = mode === 'single'

  // Get labels for selected values
  const selectedLabels = React.useMemo(() =>
    options
      .filter(option => value.includes(option.value))
      .map(option => ({ value: option.value, label: option.label, description: option.description, data: option.data })),
    [options, value]
  )

  // Get selected options for callback
  const selectedOptions = React.useMemo(() =>
    options.filter(option => value.includes(option.value)),
    [options, value]
  )

  // Call selection change callback when selection changes
  React.useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(selectedOptions)
    }
  }, [selectedOptions, onSelectionChange])

  // Register with React Hook Form if needed
  React.useEffect(() => {
    if (register && name) {
      // Register the field with proper typing
      const fieldValue = value.length > 0 ? (isSingleMode ? (valueAsNumber ? Number(value[0]) : value[0]) : value) : undefined;

      // We're not actually setting the value using register directly, just registering the field
      register(name);

      // Instead, we'll update the form value manually if there's an initial value
      if (fieldValue !== undefined) {
        const event = {
          target: {
            name,
            value: fieldValue
          }
        };

        void register(name).onChange(event);
      }
    }
  }, [register, name, isSingleMode, valueAsNumber]); // Deliberately omitting 'value' to avoid re-registering on every value change

  // Toggle selection of an option
  const toggleOption = React.useCallback((optionValue: string) => {
    if (isSingleMode) {
      // For single mode, just replace the current selection
      onChange([optionValue])

      // Update the form value if register is provided
      if (register && name) {
        const event = {
          target: {
            name,
            value: valueAsNumber ? Number(optionValue) : optionValue
          }
        };

        void register(name).onChange(event);
      }

      setOpen(false) // Close the popover on selection in single mode
    } else {
      // For multiple mode, toggle the selection
      const newValue = value.includes(optionValue)
        ? value.filter(v => v !== optionValue)
        : [...value, optionValue]
      onChange(newValue)

      // Update the form value if register is provided
      if (register && name) {
        const event = {
          target: {
            name,
            value: newValue
          }
        };

        void register(name).onChange(event);
      }
    }
  }, [value, onChange, isSingleMode, setOpen, register, name, valueAsNumber])

  // Remove a selected option
  const removeOption = React.useCallback((optionValue: string, e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    const newValue = value.filter(v => v !== optionValue);
    onChange(newValue)

    // Update the form value if register is provided
    if (register && name) {
      const event = {
        target: {
          name,
          value: newValue.length > 0 ? newValue : undefined
        }
      };

      void register(name).onChange(event);
    }
  }, [value, onChange, register, name])

  // Clear all selected options
  const clearAll = React.useCallback((e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    onChange([])

    // Update the form value if register is provided
    if (register && name) {
      const event = {
        target: {
          name,
          value: undefined
        }
      };

      void register(name).onChange(event);
    }
  }, [onChange, register, name])

  // Custom display for trigger button based on selection mode
  const renderTriggerContent = () => {
    if (selectedLabels.length === 0) {
      return <span>{placeholder}</span>
    }

    // For single select mode with a selection
    if (isSingleMode && selectedLabels.length > 0) {
      // We've already checked that selectedLabels has at least one item
      const firstLabel = selectedLabels[0]?.label ?? ''
      return <span className="text-foreground">{firstLabel}</span>
    }

    // For multi-select mode with selections
    return (
      <div className="flex flex-wrap gap-1 w-full">
        {selectedLabels.map((option) => (
          <Badge
            key={option.value}
            variant="secondary"
            className="mr-1 mb-1 max-w-full overflow-hidden text-ellipsis whitespace-nowrap"
          >
            <span className="truncate">{option.label}</span>
            <span
              className="ml-1 rounded-full outline-none hover:bg-muted cursor-pointer inline-flex items-center flex-shrink-0"
              onKeyDown={(e) => {
                if (e.key === 'Enter') removeOption(option.value)
              }}
              onMouseDown={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}
              onClick={(e) => removeOption(option.value, e)}
              role="button"
              tabIndex={0}
              aria-label={`Remove ${option.label}`}
            >
              <X className="h-3 w-3" />
            </span>
          </Badge>
        ))}
      </div>
    )
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between h-8 px-3 text-[0.8125rem] rounded", // matches Input md
            !value.length && "text-muted-foreground",
            className
          )}
          onClick={() => setOpen(!open)}
          disabled={disabled}
        >
          <div className="flex flex-wrap gap-1 items-center w-full mr-2">
            {renderTriggerContent()}
          </div>
          <div className="flex items-center flex-shrink-0">
            {selectedLabels.length > 0 && (
              <span
                className="mr-1 rounded-full outline-none hover:bg-muted p-0.5 cursor-pointer inline-flex items-center"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') clearAll()
                }}
                onMouseDown={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                }}
                onClick={(e) => clearAll(e)}
                role="button"
                tabIndex={0}
                aria-label="Clear all selections"
              >
                <X className="h-4 w-4" />
              </span>
            )}
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden"
        align="start"
        sideOffset={5}
        onWheel={(e) => {
          if (scrollAreaRef.current) {
            const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
            if (scrollableElement) {
              // e.preventDefault();
              scrollableElement.scrollTop += e.deltaY;
            }
          }
        }}
      >
        <Command
          shouldFilter={false}
          className="max-h-full"
          onWheel={(e) => {
            if (scrollAreaRef.current) {
              const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
              if (scrollableElement) {
                // e.preventDefault();
                scrollableElement.scrollTop += e.deltaY;
              }
            }
          }}
        >
          <CommandInput
            placeholder="Search..."
            value={searchValue}
            onValueChange={setSearchValue}
            className="h-9"
          />
          <CommandEmpty>
            {isLoading ? loadingText : emptyText}
          </CommandEmpty>
          <ScrollArea
            className="overflow-hidden h-full custom-scrollbar"
            style={{ height: `${maxHeight - 40}px`, maxHeight: `${maxHeight - 40}px` }}
            ref={scrollAreaRef}
          >
            <CommandGroup
              onWheel={(e) => {
                if (scrollAreaRef.current) {
                  const scrollableElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
                  if (scrollableElement) {
                    scrollableElement.scrollTop += e.deltaY;
                  }
                }
              }}
            >
              {isLoading ? (
                // Show loading skeleton
                Array.from({ length: 3 }).map((_, index) => (
                  <CommandItem key={`loading-${index}`} className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-sm border opacity-50" />
                    <div className="h-4 bg-muted rounded flex-1 animate-pulse" />
                  </CommandItem>
                ))
              ) : (
                options
                  .filter(option =>
                    option.label.toLowerCase().includes(searchValue.toLowerCase())
                  )
                  .map((option) => {
                    const isSelected = value.includes(option.value)
                    return (
                      <CommandItem
                        key={option.value}
                        value={option.value}
                        onSelect={() => toggleOption(option.value)}
                        className={cn(
                          "flex items-center gap-2",
                          isSelected ? "bg-muted" : ""
                        )}
                      >
                        <div className={cn(
                          "flex h-4 w-4 items-center justify-center rounded-sm border",
                          isSelected
                            ? "bg-primary border-primary text-primary-foreground"
                            : "opacity-50"
                        )}>
                          {isSelected && <Check className="h-3 w-3" />}
                        </div>
                        <div className="flex flex-col flex-1">
                          <span>{option.label}</span>
                          {showDescription && option.description && (
                            <span className="text-xs text-muted-foreground">
                              {option.description}
                            </span>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })
              )}
              {hasMore && onLoadMore && (
                <CommandItem
                  onSelect={onLoadMore}
                  className="flex items-center justify-center gap-2 text-muted-foreground"
                >
                  <span>Load more...</span>
                </CommandItem>
              )}
            </CommandGroup>
          </ScrollArea>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

