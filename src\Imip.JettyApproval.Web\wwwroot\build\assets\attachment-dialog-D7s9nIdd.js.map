{"version": 3, "file": "attachment-dialog-D7s9nIdd.js", "sources": ["../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-alert.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-up.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file.js", "../../../../../frontend/node_modules/.pnpm/lucide-react@0.513.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/image.js", "../../../../../frontend/src/hooks/use-file-upload.ts", "../../../../../frontend/src/components/jetty/vessel/attachment-dialog.tsx"], "sourcesContent": ["/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"line\", { x1: \"12\", x2: \"12\", y1: \"8\", y2: \"12\", key: \"1pkeuh\" }],\n  [\"line\", { x1: \"12\", x2: \"12.01\", y1: \"16\", y2: \"16\", key: \"4dfq90\" }]\n];\nconst CircleAlert = createLucideIcon(\"circle-alert\", __iconNode);\n\nexport { __iconNode, CircleAlert as default };\n//# sourceMappingURL=circle-alert.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M12 15V3\", key: \"m9g1x1\" }],\n  [\"path\", { d: \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\", key: \"ih7n3h\" }],\n  [\"path\", { d: \"m7 10 5 5 5-5\", key: \"brsn70\" }]\n];\nconst Download = createLucideIcon(\"download\", __iconNode);\n\nexport { __iconNode, Download as default };\n//# sourceMappingURL=download.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\", key: \"1rqfz7\" }],\n  [\"path\", { d: \"M14 2v4a2 2 0 0 0 2 2h4\", key: \"tnqrlb\" }],\n  [\"path\", { d: \"M10 9H8\", key: \"b1mrlr\" }],\n  [\"path\", { d: \"M16 13H8\", key: \"t4e002\" }],\n  [\"path\", { d: \"M16 17H8\", key: \"z1uh3a\" }]\n];\nconst FileText = createLucideIcon(\"file-text\", __iconNode);\n\nexport { __iconNode, FileText as default };\n//# sourceMappingURL=file-text.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\", key: \"1rqfz7\" }],\n  [\"path\", { d: \"M14 2v4a2 2 0 0 0 2 2h4\", key: \"tnqrlb\" }],\n  [\"path\", { d: \"M12 12v6\", key: \"3ahymv\" }],\n  [\"path\", { d: \"m15 15-3-3-3 3\", key: \"15xj92\" }]\n];\nconst FileUp = createLucideIcon(\"file-up\", __iconNode);\n\nexport { __iconNode, FileUp as default };\n//# sourceMappingURL=file-up.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"path\", { d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\", key: \"1rqfz7\" }],\n  [\"path\", { d: \"M14 2v4a2 2 0 0 0 2 2h4\", key: \"tnqrlb\" }]\n];\nconst File = createLucideIcon(\"file\", __iconNode);\n\nexport { __iconNode, File as default };\n//# sourceMappingURL=file.js.map\n", "/**\n * @license lucide-react v0.513.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\n\nconst __iconNode = [\n  [\"rect\", { width: \"18\", height: \"18\", x: \"3\", y: \"3\", rx: \"2\", ry: \"2\", key: \"1m3agn\" }],\n  [\"circle\", { cx: \"9\", cy: \"9\", r: \"2\", key: \"af1f0g\" }],\n  [\"path\", { d: \"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21\", key: \"1xmnt7\" }]\n];\nconst Image = createLucideIcon(\"image\", __iconNode);\n\nexport { __iconNode, Image as default };\n//# sourceMappingURL=image.js.map\n", "'use client';\n\nimport type React from 'react';\nimport { useCallback, useRef, useState, type ChangeEvent, type DragEvent, type InputHTMLAttributes } from 'react';\n\nexport type FileMetadata = {\n  name: string;\n  size: number;\n  type: string;\n  url: string;\n  id: string;\n};\n\nexport type FileWithPreview = {\n  file: File | FileMetadata;\n  id: string;\n  preview?: string;\n};\n\nexport type FileUploadOptions = {\n  maxFiles?: number; // Only used when multiple is true, defaults to Infinity\n  maxSize?: number; // in bytes\n  accept?: string;\n  multiple?: boolean; // Defaults to false\n  initialFiles?: FileMetadata[];\n  onFilesChange?: (files: FileWithPreview[]) => void; // Callback when files change\n  onFilesAdded?: (addedFiles: FileWithPreview[]) => void; // Callback when new files are added\n};\n\nexport type FileUploadState = {\n  files: FileWithPreview[];\n  isDragging: boolean;\n  errors: string[];\n};\n\nexport type FileUploadActions = {\n  addFiles: (files: FileList | File[]) => void;\n  removeFile: (id: string) => void;\n  clearFiles: () => void;\n  clearErrors: () => void;\n  handleDragEnter: (e: DragEvent<HTMLElement>) => void;\n  handleDragLeave: (e: DragEvent<HTMLElement>) => void;\n  handleDragOver: (e: DragEvent<HTMLElement>) => void;\n  handleDrop: (e: DragEvent<HTMLElement>) => void;\n  handleFileChange: (e: ChangeEvent<HTMLInputElement>) => void;\n  openFileDialog: () => void;\n  getInputProps: (props?: InputHTMLAttributes<HTMLInputElement>) => InputHTMLAttributes<HTMLInputElement> & {\n    ref: React.Ref<HTMLInputElement>;\n  };\n};\n\nexport const useFileUpload = (options: FileUploadOptions = {}): [FileUploadState, FileUploadActions] => {\n  const {\n    maxFiles = Infinity,\n    maxSize = Infinity,\n    accept = '*',\n    multiple = false,\n    initialFiles = [],\n    onFilesChange,\n    onFilesAdded,\n  } = options;\n\n  const [state, setState] = useState<FileUploadState>({\n    files: initialFiles.map((file) => ({\n      file,\n      id: file.id,\n      preview: file.url,\n    })),\n    isDragging: false,\n    errors: [],\n  });\n\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const validateFile = useCallback(\n    (file: File | FileMetadata): string | null => {\n      if (file instanceof File) {\n        if (file.size > maxSize) {\n          return `File \"${file.name}\" exceeds the maximum size of ${formatBytes(maxSize)}.`;\n        }\n      } else {\n        if (file.size > maxSize) {\n          return `File \"${file.name}\" exceeds the maximum size of ${formatBytes(maxSize)}.`;\n        }\n      }\n\n      if (accept !== '*') {\n        const acceptedTypes = accept.split(',').map((type) => type.trim());\n        const fileType = file instanceof File ? file.type || '' : file.type;\n        const fileExtension = `.${file instanceof File ? file.name.split('.').pop() : file.name.split('.').pop()}`;\n\n        const isAccepted = acceptedTypes.some((type) => {\n          if (type.startsWith('.')) {\n            return fileExtension.toLowerCase() === type.toLowerCase();\n          }\n          if (type.endsWith('/*')) {\n            const baseType = type.split('/')[0];\n            return fileType.startsWith(`${baseType}/`);\n          }\n          return fileType === type;\n        });\n\n        if (!isAccepted) {\n          return `File \"${file instanceof File ? file.name : file.name}\" is not an accepted file type.`;\n        }\n      }\n\n      return null;\n    },\n    [accept, maxSize],\n  );\n\n  const createPreview = useCallback((file: File | FileMetadata): string | undefined => {\n    if (file instanceof File) {\n      return URL.createObjectURL(file);\n    }\n    return file.url;\n  }, []);\n\n  const generateUniqueId = useCallback((file: File | FileMetadata): string => {\n    if (file instanceof File) {\n      return `${file.name}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;\n    }\n    return file.id;\n  }, []);\n\n  const clearFiles = useCallback(() => {\n    setState((prev) => {\n      // Clean up object URLs\n      prev.files.forEach((file) => {\n        if (file.preview && file.file instanceof File && file.file.type.startsWith('image/')) {\n          URL.revokeObjectURL(file.preview);\n        }\n      });\n\n      if (inputRef.current) {\n        inputRef.current.value = '';\n      }\n\n      const newState = {\n        ...prev,\n        files: [],\n        errors: [],\n      };\n\n      onFilesChange?.(newState.files);\n      return newState;\n    });\n  }, [onFilesChange]);\n\n  const addFiles = useCallback(\n    (newFiles: FileList | File[]) => {\n      if (!newFiles || newFiles.length === 0) return;\n\n      const newFilesArray = Array.from(newFiles);\n      const errors: string[] = [];\n\n      // Clear existing errors when new files are uploaded\n      setState((prev) => ({ ...prev, errors: [] }));\n\n      // In single file mode, clear existing files first\n      if (!multiple) {\n        clearFiles();\n      }\n\n      // Check if adding these files would exceed maxFiles (only in multiple mode)\n      if (multiple && maxFiles !== Infinity && state.files.length + newFilesArray.length > maxFiles) {\n        errors.push(`You can only upload a maximum of ${maxFiles} files.`);\n        setState((prev) => ({ ...prev, errors }));\n        return;\n      }\n\n      const validFiles: FileWithPreview[] = [];\n\n      newFilesArray.forEach((file) => {\n        // Only check for duplicates if multiple files are allowed\n        if (multiple) {\n          const isDuplicate = state.files.some(\n            (existingFile) => existingFile.file.name === file.name && existingFile.file.size === file.size,\n          );\n\n          // Skip duplicate files silently\n          if (isDuplicate) {\n            return;\n          }\n        }\n\n        // Check file size\n        if (file.size > maxSize) {\n          errors.push(\n            multiple\n              ? `Some files exceed the maximum size of ${formatBytes(maxSize)}.`\n              : `File exceeds the maximum size of ${formatBytes(maxSize)}.`,\n          );\n          return;\n        }\n\n        const error = validateFile(file);\n        if (error) {\n          errors.push(error);\n        } else {\n          validFiles.push({\n            file,\n            id: generateUniqueId(file),\n            preview: createPreview(file),\n          });\n        }\n      });\n\n      // Only update state if we have valid files to add\n      if (validFiles.length > 0) {\n        // Call the onFilesAdded callback with the newly added valid files\n        onFilesAdded?.(validFiles);\n\n        setState((prev) => {\n          const newFiles = !multiple ? validFiles : [...prev.files, ...validFiles];\n          onFilesChange?.(newFiles);\n          return {\n            ...prev,\n            files: newFiles,\n            errors,\n          };\n        });\n      } else if (errors.length > 0) {\n        setState((prev) => ({\n          ...prev,\n          errors,\n        }));\n      }\n\n      // Reset input value after handling files\n      if (inputRef.current) {\n        inputRef.current.value = '';\n      }\n    },\n    [\n      state.files,\n      maxFiles,\n      multiple,\n      maxSize,\n      validateFile,\n      createPreview,\n      generateUniqueId,\n      clearFiles,\n      onFilesChange,\n      onFilesAdded,\n    ],\n  );\n\n  const removeFile = useCallback(\n    (id: string) => {\n      setState((prev) => {\n        const fileToRemove = prev.files.find((file) => file.id === id);\n        if (\n          fileToRemove &&\n          fileToRemove.preview &&\n          fileToRemove.file instanceof File &&\n          fileToRemove.file.type.startsWith('image/')\n        ) {\n          URL.revokeObjectURL(fileToRemove.preview);\n        }\n\n        const newFiles = prev.files.filter((file) => file.id !== id);\n        onFilesChange?.(newFiles);\n\n        return {\n          ...prev,\n          files: newFiles,\n          errors: [],\n        };\n      });\n    },\n    [onFilesChange],\n  );\n\n  const clearErrors = useCallback(() => {\n    setState((prev) => ({\n      ...prev,\n      errors: [],\n    }));\n  }, []);\n\n  const handleDragEnter = useCallback((e: DragEvent<HTMLElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setState((prev) => ({ ...prev, isDragging: true }));\n  }, []);\n\n  const handleDragLeave = useCallback((e: DragEvent<HTMLElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (e.currentTarget.contains(e.relatedTarget as Node)) {\n      return;\n    }\n\n    setState((prev) => ({ ...prev, isDragging: false }));\n  }, []);\n\n  const handleDragOver = useCallback((e: DragEvent<HTMLElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n  }, []);\n\n  const handleDrop = useCallback(\n    (e: DragEvent<HTMLElement>) => {\n      e.preventDefault();\n      e.stopPropagation();\n      setState((prev) => ({ ...prev, isDragging: false }));\n\n      // Don't process files if the input is disabled\n      if (inputRef.current?.disabled) {\n        return;\n      }\n\n      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\n        // In single file mode, only use the first file\n        if (!multiple) {\n          const file = e.dataTransfer.files[0];\n          addFiles([file]);\n        } else {\n          addFiles(e.dataTransfer.files);\n        }\n      }\n    },\n    [addFiles, multiple],\n  );\n\n  const handleFileChange = useCallback(\n    (e: ChangeEvent<HTMLInputElement>) => {\n      if (e.target.files && e.target.files.length > 0) {\n        addFiles(e.target.files);\n      }\n    },\n    [addFiles],\n  );\n\n  const openFileDialog = useCallback(() => {\n    if (inputRef.current) {\n      inputRef.current.click();\n    }\n  }, []);\n\n  const getInputProps = useCallback(\n    (props: InputHTMLAttributes<HTMLInputElement> = {}) => {\n      return {\n        ...props,\n        type: 'file' as const,\n        onChange: handleFileChange,\n        accept: props.accept || accept,\n        multiple: props.multiple !== undefined ? props.multiple : multiple,\n        ref: inputRef,\n      };\n    },\n    [accept, multiple, handleFileChange],\n  );\n\n  return [\n    state,\n    {\n      addFiles,\n      removeFile,\n      clearFiles,\n      clearErrors,\n      handleDragEnter,\n      handleDragLeave,\n      handleDragOver,\n      handleDrop,\n      handleFileChange,\n      openFileDialog,\n      getInputProps,\n    },\n  ];\n};\n\n// Helper function to format bytes to human-readable format\nexport const formatBytes = (bytes: number, decimals = 2): string => {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + sizes[i];\n};\n", "import type { DocAttachmentDto, DocAttachmentSortDto } from '@/clientEkb';\r\nimport { ekbProxyService } from '@/services/ekbProxyService';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { formatBytes, useFileUpload } from '@/hooks/use-file-upload';\r\nimport { getCookie } from '@/lib/utils/cookie';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport { AlertCircleIcon, Download, File, FileText, FileUpIcon, Image, Trash2, XIcon } from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\ninterface AttachmentDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  attachments: DocAttachmentSortDto[];\r\n  title: string;\r\n  queryClient: QueryClient;\r\n  referenceId: string | number;\r\n  documentReferenceId: number;\r\n  defaultTabName?: string;\r\n  docType?: string;\r\n  transType?: string;\r\n  tabName?: string;\r\n  onUploadSuccess?: () => void; // Add callback for successful upload\r\n}\r\n\r\n// Separate preview dialog component\r\ninterface AttachmentPreviewDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  attachment: DocAttachmentSortDto | null;\r\n}\r\n\r\nconst AttachmentPreviewDialog: React.FC<AttachmentPreviewDialogProps> = ({\r\n  isOpen,\r\n  onOpenChange,\r\n  attachment\r\n}) => {\r\n  const [documentSrc, setDocumentSrc] = useState<string>('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string>('');\r\n\r\n  React.useEffect(() => {\r\n    if (attachment && isOpen) {\r\n      setIsLoading(true);\r\n      setError('');\r\n\r\n      ekbProxyService.getFileStream(attachment.id!)\r\n        .then((response) => {\r\n          if (response && response.data instanceof Blob) {\r\n            const url = URL.createObjectURL(response.data);\r\n            setDocumentSrc(url);\r\n            return;\r\n          }\r\n          throw new Error('Invalid response type for file stream');\r\n        })\r\n        .catch(err => {\r\n          console.error('Error loading file:', err);\r\n          setError('Failed to load file preview');\r\n        })\r\n        .finally(() => {\r\n          setIsLoading(false);\r\n        });\r\n    }\r\n\r\n    // Cleanup function - only cleanup when attachment or isOpen changes\r\n    return () => {\r\n      // This cleanup will run when the effect re-runs or component unmounts\r\n      // We'll handle cleanup in a separate effect\r\n    };\r\n  }, [attachment, isOpen]);\r\n\r\n  // Separate cleanup effect for blob URLs\r\n  React.useEffect(() => {\r\n    return () => {\r\n      if (documentSrc && documentSrc.startsWith('blob:')) {\r\n        URL.revokeObjectURL(documentSrc);\r\n      }\r\n    };\r\n  }, [documentSrc]);\r\n\r\n  const canPreviewInBrowser = (fileName: string) => {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    return ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'htm'].includes(extension || '');\r\n  };\r\n\r\n  if (!attachment) return null;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"min-w-[1000px] w-auto h-[90vh] flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle>{attachment.fileName}</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex-grow overflow-auto\">\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\r\n              <span className=\"ml-2\">Loading preview...</span>\r\n            </div>\r\n          ) : error ? (\r\n            <div className=\"flex items-center justify-center h-full text-red-500\">\r\n              <span>{error}</span>\r\n            </div>\r\n          ) : canPreviewInBrowser(attachment.fileName ?? '') ? (\r\n            <iframe\r\n              src={documentSrc}\r\n              title=\"Document Preview\"\r\n              className=\"w-full h-full border-none\"\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-full text-muted-foreground\">\r\n              <div className=\"text-center\">\r\n                <File className=\"w-12 h-12 mx-auto mb-4\" />\r\n                <p>Preview not available for this file type</p>\r\n                <p className=\"text-sm\">Please download the file to view it</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport const AttachmentDialog: React.FC<AttachmentDialogProps> = ({\r\n  open,\r\n  onOpenChange,\r\n  attachments,\r\n  title,\r\n  queryClient,\r\n  referenceId,\r\n  documentReferenceId,\r\n  defaultTabName,\r\n  docType,\r\n  transType,\r\n  tabName,\r\n  onUploadSuccess,\r\n}) => {\r\n  const [selectedAttachment, setSelectedAttachment] = useState<DocAttachmentDto | null>(null);\r\n  const [previewOpen, setPreviewOpen] = useState(false);\r\n\r\n  // Group attachments by tab name\r\n  const groupedAttachments = attachments.reduce((groups, attachment) => {\r\n    const tabName = attachment.tabName || 'Other';\r\n    if (!groups[tabName]) {\r\n      groups[tabName] = [];\r\n    }\r\n    groups[tabName].push(attachment);\r\n    return groups;\r\n  }, {} as Record<string, DocAttachmentDto[]>);\r\n\r\n  const tabNames = Object.keys(groupedAttachments);\r\n  const hasShippingTab = tabNames.includes('SHIPPING');\r\n  const allTabNames = hasShippingTab ? tabNames : [...tabNames, 'SHIPPING'];\r\n  const [selectedTab, setSelectedTab] = useState(\r\n    defaultTabName && allTabNames.includes(defaultTabName)\r\n      ? defaultTabName\r\n      : allTabNames[0] ?? ''\r\n  );\r\n\r\n  // Ensure selectedTab is always valid when tab names change (including SHIPPING)\r\n  React.useEffect(() => {\r\n    if (!allTabNames.includes(selectedTab)) {\r\n      setSelectedTab(allTabNames[0] ?? '');\r\n    }\r\n  }, [allTabNames, selectedTab]);\r\n\r\n  const handlePreview = (attachment: DocAttachmentDto) => {\r\n    setSelectedAttachment(attachment);\r\n    setPreviewOpen(true);\r\n  };\r\n\r\n  const handleDownload = async (attachment: DocAttachmentDto) => {\r\n    try {\r\n      // Manually construct the URL to avoid double encoding\r\n      const rawBaseUrl = getCookie(\"ekbUrl\");\r\n      const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : \"https://ekb-dev.imip.co.id\";\r\n      const url = `${ekbBaseUrl}/api/ekb/files/stream/${attachment.id}`;\r\n\r\n      const response = await fetch(url, {\r\n        method: 'GET',\r\n        credentials: 'include', // Include cookies for authentication\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const blob = await response.blob();\r\n\r\n      // Create a blob from the response and download it\r\n      const downloadUrl = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = downloadUrl;\r\n      link.download = attachment.fileName ?? '';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(downloadUrl);\r\n    } catch (error) {\r\n      console.error('Error downloading file:', error);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (attachment: DocAttachmentDto) => {\r\n    if (!attachment.id) {\r\n      console.error('No attachment ID found');\r\n      return;\r\n    }\r\n\r\n    if (!confirm(`Are you sure you want to delete \"${attachment.fileName}\"?`)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await ekbProxyService.deleteDocAttachment(attachment.id);\r\n\r\n      // Invalidate queries to refresh the data\r\n      queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n      queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n      queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n\r\n      // Show a success message (do not close the dialog)\r\n      setUploadSuccess('Attachment deleted successfully!');\r\n      setTimeout(() => setUploadSuccess(null), 3000);\r\n\r\n      // Refresh the data, do not close the dialog\r\n      onUploadSuccess?.();\r\n    } catch (error) {\r\n      console.error('Error deleting attachment:', error);\r\n      alert('Failed to delete attachment');\r\n    }\r\n  };\r\n\r\n  const getFileIcon = (fileName: string) => {\r\n    const extension = fileName.split('.').pop()?.toLowerCase();\r\n    switch (extension) {\r\n      case 'pdf':\r\n        return <FileText className=\"w-4 h-4\" />;\r\n      case 'jpg':\r\n      case 'jpeg':\r\n      case 'png':\r\n      case 'gif':\r\n        return <Image className=\"w-4 h-4\" />;\r\n      default:\r\n        return <File className=\"w-4 h-4\" />;\r\n    }\r\n  };\r\n\r\n  const maxSize = 100 * 1024 * 1024;\r\n  const maxFiles = 10;\r\n  const [uploading, setUploading] = React.useState(false);\r\n  const [state, actions] = useFileUpload({\r\n    multiple: true,\r\n    maxFiles,\r\n    maxSize,\r\n    onFilesAdded: async (files) => {\r\n      for (const fileObj of files) {\r\n        // Use a type check instead of instanceof File\r\n        if (fileObj.file && typeof fileObj.file === 'object' && 'name' in fileObj.file && 'size' in fileObj.file) {\r\n          // This part is now handled by the separate selectedFile state and handleAgentUpload\r\n        }\r\n      }\r\n      actions.clearFiles();\r\n    },\r\n  });\r\n  const [uploadError, setUploadError] = React.useState<string | null>(null);\r\n  const [uploadSuccess, setUploadSuccess] = React.useState<string | null>(null);\r\n\r\n  return (\r\n    <>\r\n      <Dialog open={open} onOpenChange={onOpenChange}>\r\n        <DialogContent className=\"min-w-[800px] w-auto h-[90vh] flex flex-col\">\r\n          <DialogHeader>\r\n            <DialogTitle>{title}</DialogTitle>\r\n          </DialogHeader>\r\n          <div className=\"flex-grow overflow-hidden\">\r\n            <Tabs value={selectedTab} onValueChange={val => setSelectedTab(val)} className=\"h-full flex flex-col\">\r\n              <TabsList className={`grid w-full grid-cols-${allTabNames.length}`}>\r\n                {tabNames.map(tabName => (\r\n                  <TabsTrigger key={tabName} value={tabName}>\r\n                    {tabName}\r\n                  </TabsTrigger>\r\n                ))}\r\n                {/* <TabsTrigger key=\"SHIPPING\" value=\"SHIPPING\">SHIPPING</TabsTrigger> */}\r\n              </TabsList>\r\n              {tabNames.length > 0 ? tabNames.map(tabName => (\r\n                <TabsContent key={tabName} value={tabName}>\r\n                  <div className=\"p-4\">\r\n                    {/* Dropzone always at the top */}\r\n                    <div\r\n                      role=\"button\"\r\n                      onClick={uploading ? undefined : actions.openFileDialog}\r\n                      onDragEnter={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragEnter(e); })}\r\n                      onDragLeave={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragLeave(e); })}\r\n                      onDragOver={uploading ? undefined : (e => e.preventDefault())}\r\n                      onDrop={uploading ? undefined : async e => {\r\n                        e.preventDefault();\r\n                        actions.handleDrop(e);\r\n                        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n                          const file = e.dataTransfer.files[0];\r\n                          setUploading(true);\r\n                          try {\r\n                            const rawBaseUrl = getCookie('ekbUrl');\r\n                            const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';\r\n                            let url = '/api/ekb/doc-attachments/upload';\r\n                            if (url.startsWith('/')) {\r\n                              url = ekbBaseUrl.replace(/\\/$/, '') + url;\r\n                            }\r\n                            const token = getCookie('EkbApiToken') ?? '';\r\n                            const formData = new FormData();\r\n                            formData.append('File', file);\r\n                            formData.append('ReferenceId', String(referenceId));\r\n                            formData.append('DocType', docType ?? 'Import');\r\n                            formData.append('ReferenceType', docType ?? 'Import');\r\n                            formData.append('TransType', transType ?? 'ImportDetails');\r\n                            formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));\r\n                            formData.append('DocumentReferenceId', String(documentReferenceId));\r\n                            await fetch(url, {\r\n                              method: 'POST',\r\n                              body: formData,\r\n                              credentials: 'include',\r\n                              headers: token ? { Authorization: `Bearer ${token}` } : undefined,\r\n                            });\r\n                            queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n                            queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n                            queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n                            setUploadError(null);\r\n                            setUploadSuccess('File uploaded successfully!');\r\n                            setUploading(false);\r\n                            onUploadSuccess?.(); // Call the new prop\r\n                            // Clear success message after 3 seconds\r\n                            setTimeout(() => setUploadSuccess(null), 3000);\r\n                          } catch (err) {\r\n                            setUploading(false);\r\n\r\n                            console.error('Upload failed', err);\r\n                            let message = 'Upload failed';\r\n                            let details = '';\r\n                            if (err && typeof err === 'object') {\r\n                              if ('message' in err && typeof err.message === 'string') message = err.message;\r\n                              if ('details' in err && typeof err.details === 'string') details = err.details;\r\n                            }\r\n                            setUploadError(details ? `${message}\\n${details}` : message);\r\n                          }\r\n                        }\r\n                      }}\r\n                      data-dragging={state.isDragging || undefined}\r\n                      className=\"border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]\"\r\n                      aria-disabled={uploading}\r\n                      tabIndex={uploading ? -1 : 0}\r\n                    >\r\n                      <Input\r\n                        {...actions.getInputProps()}\r\n                        disabled={uploading}\r\n                        onChange={async e => {\r\n                          if (e.target.files && e.target.files.length > 0) {\r\n                            const file = e.target.files[0];\r\n                            setUploading(true);\r\n                            try {\r\n                              const rawBaseUrl = getCookie('ekbUrl');\r\n                              const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';\r\n                              let url = '/api/ekb/doc-attachments/upload';\r\n                              if (url.startsWith('/')) {\r\n                                url = ekbBaseUrl.replace(/\\/$/, '') + url;\r\n                              }\r\n                              const token = getCookie('EkbApiToken') ?? '';\r\n                              const formData = new FormData();\r\n                              formData.append('File', file);\r\n                              formData.append('ReferenceId', String(referenceId));\r\n                              formData.append('DocType', docType ?? 'Import');\r\n                              formData.append('ReferenceType', docType ?? 'Import');\r\n                              formData.append('TransType', transType ?? 'ImportDetails');\r\n                              formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));\r\n                              formData.append('DocumentReferenceId', String(documentReferenceId));\r\n                              await fetch(url, {\r\n                                method: 'POST',\r\n                                body: formData,\r\n                                credentials: 'include',\r\n                                headers: token ? { Authorization: `Bearer ${token}` } : undefined,\r\n                              });\r\n                              queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n                              queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n                              queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n                              setUploadError(null);\r\n                              setUploadSuccess('File uploaded successfully!');\r\n                              setUploading(false);\r\n                              onUploadSuccess?.(); // Call the new prop\r\n                              // Clear success message after 3 seconds\r\n                              setTimeout(() => setUploadSuccess(null), 3000);\r\n                            } catch (err) {\r\n                              setUploading(false);\r\n\r\n                              console.error('Upload failed', err);\r\n                              let message = 'Upload failed';\r\n                              let details = '';\r\n                              if (err && typeof err === 'object') {\r\n                                if ('message' in err && typeof err.message === 'string') message = err.message;\r\n                                if ('details' in err && typeof err.details === 'string') details = err.details;\r\n                              }\r\n                              setUploadError(details ? `${message}\\n${details}` : message);\r\n                            }\r\n                          }\r\n                        }}\r\n                        className=\"sr-only\"\r\n                        aria-label=\"Upload files\"\r\n                      />\r\n                      <div className=\"flex flex-col items-center justify-center text-center\">\r\n                        <div\r\n                          className=\"bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border\"\r\n                          aria-hidden=\"true\"\r\n                        >\r\n                          <FileUpIcon className=\"size-4 opacity-60\" />\r\n                        </div>\r\n                        <p className=\"mb-1.5 text-sm font-medium\">Upload files</p>\r\n                        <p className=\"text-muted-foreground mb-2 text-xs\">\r\n                          Drag & drop or click to browse\r\n                        </p>\r\n                        <div className=\"text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs\">\r\n                          <span>All files</span>\r\n                          <span>∙</span>\r\n                          <span>Max {maxFiles} files</span>\r\n                          <span>∙</span>\r\n                          <span>Up to {formatBytes(maxSize)}</span>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    {uploading && (\r\n                      <div className=\"flex items-center gap-2 text-xs text-muted-foreground mt-2\">\r\n                        <span className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"></span>\r\n                        Uploading...\r\n                      </div>\r\n                    )}\r\n                    {state.errors.length > 0 && (\r\n                      <div className=\"text-destructive flex items-center gap-1 text-xs\" role=\"alert\">\r\n                        <AlertCircleIcon className=\"size-3 shrink-0\" />\r\n                        <span>{state.errors[0]}</span>\r\n                      </div>\r\n                    )}\r\n                    {uploadError && (\r\n                      <div className=\"text-destructive flex items-center gap-1 text-xs\" role=\"alert\">\r\n                        <AlertCircleIcon className=\"size-3 shrink-0\" />\r\n                        <span>{uploadError}</span>\r\n                      </div>\r\n                    )}\r\n                    {uploadSuccess && (\r\n                      <div className=\"text-green-600 flex items-center gap-1 text-xs\" role=\"alert\">\r\n                        <span>✓</span>\r\n                        <span>{uploadSuccess}</span>\r\n                      </div>\r\n                    )}\r\n                    {/* File list (pending uploads) */}\r\n                    {state.files.length > 0 && (\r\n                      <div className=\"space-y-2\">\r\n                        {state.files.map((file) => (\r\n                          <div\r\n                            key={file.id}\r\n                            className=\"bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3\"\r\n                          >\r\n                            <div className=\"flex items-center gap-3 overflow-hidden\">\r\n                              <div className=\"flex aspect-square size-10 shrink-0 items-center justify-center rounded border\">\r\n                                {getFileIcon((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name)}\r\n                              </div>\r\n                              <div className=\"flex min-w-0 flex-col gap-0.5\">\r\n                                <p className=\"truncate text-[13px] font-medium\">\r\n                                  {(typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name}\r\n                                </p>\r\n                                <p className=\"text-muted-foreground text-xs\">\r\n                                  {formatBytes((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.size : file.file.size)}\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                            <Button\r\n                              size=\"icon\"\r\n                              variant=\"ghost\"\r\n                              className=\"text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent\"\r\n                              onClick={() => actions.removeFile(file.id)}\r\n                              aria-label=\"Remove file\"\r\n                            >\r\n                              <XIcon className=\"size-4\" aria-hidden=\"true\" />\r\n                            </Button>\r\n                          </div>\r\n                        ))}\r\n                        {state.files.length > 1 && (\r\n                          <div>\r\n                            <Button size=\"sm\" variant=\"outline\" onClick={actions.clearFiles}>\r\n                              Remove all files\r\n                            </Button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                    {/* Attachments preview table below dropzone */}\r\n                    <Table>\r\n                      <TableHeader>\r\n                        <TableRow>\r\n                          <TableHead>File Name</TableHead>\r\n                          <TableHead className=\"w-40\">Actions</TableHead>\r\n                        </TableRow>\r\n                      </TableHeader>\r\n                      <TableBody>\r\n                        {attachments.length === 0 ? (\r\n                          <TableRow>\r\n                            <TableCell colSpan={2} className=\"text-center text-muted-foreground\">No files uploaded yet.</TableCell>\r\n                          </TableRow>\r\n                        ) : (\r\n                          attachments.map((attachment, idx) => (\r\n                            <TableRow key={attachment.id || idx}>\r\n                              <TableCell>\r\n                                <div className=\"flex items-center gap-2\">\r\n                                  {getFileIcon(attachment.fileName ?? '')}\r\n                                  <span title={attachment.fileName ?? ''}>{attachment.fileName}</span>\r\n                                </div>\r\n                              </TableCell>\r\n                              <TableCell>\r\n                                <div className=\"flex gap-2\">\r\n                                  <Button variant=\"outline\" size=\"sm\" onClick={() => handlePreview(attachment)}>\r\n                                    Preview\r\n                                  </Button>\r\n                                  <Button variant=\"outline\" size=\"sm\" onClick={() => handleDownload(attachment)}>\r\n                                    <Download className=\"w-4 h-4\" />\r\n                                  </Button>\r\n                                  <Button\r\n                                    variant=\"outline\"\r\n                                    size=\"sm\"\r\n                                    onClick={() => handleDelete(attachment)}\r\n                                    className=\"text-destructive hover:text-destructive\"\r\n                                  >\r\n                                    <Trash2 className=\"w-4 h-4\" />\r\n                                  </Button>\r\n                                </div>\r\n                              </TableCell>\r\n                            </TableRow>\r\n                          ))\r\n                        )}\r\n                      </TableBody>\r\n                    </Table>\r\n                  </div>\r\n                </TabsContent>\r\n              )) : <TabsContent key=\"SHIPPING\" value=\"SHIPPING\">\r\n                <AgentTabContent\r\n                  attachments={attachments}\r\n                  getFileIcon={getFileIcon}\r\n                  queryClient={queryClient}\r\n                  referenceId={referenceId}\r\n                  documentReferenceId={documentReferenceId}\r\n                  docType={docType}\r\n                  transType={transType}\r\n                  tabName={tabName}\r\n                  defaultTabName={defaultTabName}\r\n                  onUploadSuccess={onUploadSuccess} // Pass the new prop down\r\n                />\r\n              </TabsContent>}\r\n            </Tabs>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <AttachmentPreviewDialog\r\n        isOpen={previewOpen}\r\n        onOpenChange={setPreviewOpen}\r\n        attachment={selectedAttachment}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\n// AGENT Tab Content Component\r\ntype AgentTabContentProps = {\r\n  attachments: DocAttachmentSortDto[];\r\n  getFileIcon: (fileName: string) => React.ReactNode;\r\n  queryClient: QueryClient;\r\n  referenceId: string | number;\r\n  documentReferenceId: number;\r\n  docType?: string;\r\n  transType?: string;\r\n  tabName?: string;\r\n  defaultTabName?: string;\r\n  onUploadSuccess?: () => void; // Add callback for successful upload\r\n};\r\nfunction AgentTabContent({\r\n  attachments, getFileIcon, queryClient, referenceId, documentReferenceId,\r\n  docType, transType, tabName, defaultTabName, onUploadSuccess\r\n}: AgentTabContentProps) {\r\n  const maxSize = 100 * 1024 * 1024;\r\n  const maxFiles = 10;\r\n  const [uploading, setUploading] = React.useState(false);\r\n  const [state, actions] = useFileUpload({\r\n    multiple: true,\r\n    maxFiles,\r\n    maxSize,\r\n    onFilesAdded: async (files) => {\r\n      for (const fileObj of files) {\r\n        // Use a type check instead of instanceof File\r\n        if (fileObj.file && typeof fileObj.file === 'object' && 'name' in fileObj.file && 'size' in fileObj.file) {\r\n          // This part is now handled by the separate selectedFile state and handleAgentUpload\r\n        }\r\n      }\r\n      actions.clearFiles();\r\n    },\r\n  });\r\n  const [uploadError, setUploadError] = React.useState<string | null>(null);\r\n  const [uploadSuccess, setUploadSuccess] = React.useState<string | null>(null);\r\n\r\n  const handleDelete = async (attachment: DocAttachmentDto) => {\r\n    if (!attachment.id) {\r\n      console.error('No attachment ID found');\r\n      return;\r\n    }\r\n\r\n    if (!confirm(`Are you sure you want to delete \"${attachment.fileName}\"?`)) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await ekbProxyService.deleteDocAttachment(attachment.id);\r\n\r\n      // Invalidate queries to refresh the data\r\n      queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n      queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n      queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n\r\n      // Call the upload success callback to refresh the table\r\n      onUploadSuccess?.();\r\n    } catch (error) {\r\n      console.error('Error deleting attachment:', error);\r\n      alert('Failed to delete attachment');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={e => e.preventDefault()} className=\"space-y-4\">\r\n      <div className=\"flex flex-col gap-4\">\r\n        {/* Drop area */}\r\n        <div\r\n          role=\"button\"\r\n          onClick={uploading ? undefined : actions.openFileDialog}\r\n          onDragEnter={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragEnter(e); })}\r\n          onDragLeave={uploading ? undefined : (e => { e.preventDefault(); actions.handleDragLeave(e); })}\r\n          onDragOver={uploading ? undefined : (e => e.preventDefault())}\r\n          onDrop={uploading ? undefined : async e => {\r\n            e.preventDefault();\r\n            actions.handleDrop(e);\r\n            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n              const file = e.dataTransfer.files[0];\r\n              setUploading(true);\r\n              try {\r\n                const rawBaseUrl = getCookie('ekbUrl');\r\n                const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';\r\n                let url = '/api/ekb/doc-attachments/upload';\r\n                if (url.startsWith('/')) {\r\n                  url = ekbBaseUrl.replace(/\\/$/, '') + url;\r\n                }\r\n                const token = getCookie('EkbApiToken') ?? '';\r\n                const formData = new FormData();\r\n                formData.append('File', file);\r\n                formData.append('ReferenceId', String(referenceId));\r\n                formData.append('DocType', docType ?? 'Import');\r\n                formData.append('ReferenceType', docType ?? 'Import');\r\n                formData.append('TransType', transType ?? 'ImportDetails');\r\n                formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));\r\n                formData.append('DocumentReferenceId', String(documentReferenceId));\r\n                await fetch(url, {\r\n                  method: 'POST',\r\n                  body: formData,\r\n                  credentials: 'include',\r\n                  headers: token ? { Authorization: `Bearer ${token}` } : undefined,\r\n                });\r\n                queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n                queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n                queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n                setUploadError(null);\r\n                setUploadSuccess('File uploaded successfully!');\r\n                setUploading(false);\r\n                onUploadSuccess?.(); // Call the new prop\r\n                // Clear success message after 3 seconds\r\n                setTimeout(() => setUploadSuccess(null), 3000);\r\n              } catch (err) {\r\n                setUploading(false);\r\n\r\n                console.error('Upload failed', err);\r\n                let message = 'Upload failed';\r\n                let details = '';\r\n                if (err && typeof err === 'object') {\r\n                  if ('message' in err && typeof err.message === 'string') message = err.message;\r\n                  if ('details' in err && typeof err.details === 'string') details = err.details;\r\n                }\r\n                setUploadError(details ? `${message}\\n${details}` : message);\r\n              }\r\n            }\r\n          }}\r\n          data-dragging={state.isDragging || undefined}\r\n          className=\"border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]\"\r\n          aria-disabled={uploading}\r\n          tabIndex={uploading ? -1 : 0}\r\n        >\r\n          <Input\r\n            {...actions.getInputProps()}\r\n            disabled={uploading}\r\n            onChange={async e => {\r\n              if (e.target.files && e.target.files.length > 0) {\r\n                const file = e.target.files[0];\r\n                setUploading(true);\r\n                try {\r\n                  const rawBaseUrl = getCookie('ekbUrl');\r\n                  const ekbBaseUrl = rawBaseUrl ? decodeURIComponent(rawBaseUrl) : 'https://ekb-dev.imip.co.id';\r\n                  let url = '/api/ekb/doc-attachments/upload';\r\n                  if (url.startsWith('/')) {\r\n                    url = ekbBaseUrl.replace(/\\/$/, '') + url;\r\n                  }\r\n                  const token = getCookie('EkbApiToken') ?? '';\r\n                  const formData = new FormData();\r\n                  formData.append('File', file);\r\n                  formData.append('ReferenceId', String(referenceId));\r\n                  formData.append('DocType', docType ?? 'Import');\r\n                  formData.append('ReferenceType', docType ?? 'Import');\r\n                  formData.append('TransType', transType ?? 'ImportDetails');\r\n                  formData.append('TabName', tabName ?? (defaultTabName ?? 'SHIPPING'));\r\n                  formData.append('DocumentReferenceId', String(documentReferenceId));\r\n                  await fetch(url, {\r\n                    method: 'POST',\r\n                    body: formData,\r\n                    credentials: 'include',\r\n                    headers: token ? { Authorization: `Bearer ${token}` } : undefined,\r\n                  });\r\n                  queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n                  queryClient.invalidateQueries({ queryKey: ['local-vessel'] });\r\n                  queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n                  setUploadError(null);\r\n                  setUploadSuccess('File uploaded successfully!');\r\n                  setUploading(false);\r\n                  onUploadSuccess?.(); // Call the new prop\r\n                  // Clear success message after 3 seconds\r\n                  setTimeout(() => setUploadSuccess(null), 3000);\r\n                } catch (err) {\r\n                  setUploading(false);\r\n\r\n                  console.error('Upload failed', err);\r\n                  let message = 'Upload failed';\r\n                  let details = '';\r\n                  if (err && typeof err === 'object') {\r\n                    if ('message' in err && typeof err.message === 'string') message = err.message;\r\n                    if ('details' in err && typeof err.details === 'string') details = err.details;\r\n                  }\r\n                  setUploadError(details ? `${message}\\n${details}` : message);\r\n                }\r\n              }\r\n            }}\r\n            className=\"sr-only\"\r\n            aria-label=\"Upload files\"\r\n          />\r\n          <div className=\"flex flex-col items-center justify-center text-center\">\r\n            <div\r\n              className=\"bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border\"\r\n              aria-hidden=\"true\"\r\n            >\r\n              <FileUpIcon className=\"size-4 opacity-60\" />\r\n            </div>\r\n            <p className=\"mb-1.5 text-sm font-medium\">Upload files</p>\r\n            <p className=\"text-muted-foreground mb-2 text-xs\">\r\n              Drag & drop or click to browse\r\n            </p>\r\n            <div className=\"text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs\">\r\n              <span>All files</span>\r\n              <span>∙</span>\r\n              <span>Max {maxFiles} files</span>\r\n              <span>∙</span>\r\n              <span>Up to {formatBytes(maxSize)}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {uploading && (\r\n          <div className=\"flex items-center gap-2 text-xs text-muted-foreground mt-2\">\r\n            <span className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"></span>\r\n            Uploading...\r\n          </div>\r\n        )}\r\n        {state.errors.length > 0 && (\r\n          <div className=\"text-destructive flex items-center gap-1 text-xs\" role=\"alert\">\r\n            <AlertCircleIcon className=\"size-3 shrink-0\" />\r\n            <span>{state.errors[0]}</span>\r\n          </div>\r\n        )}\r\n        {uploadError && (\r\n          <div className=\"text-destructive flex items-center gap-1 text-xs\" role=\"alert\">\r\n            <AlertCircleIcon className=\"size-3 shrink-0\" />\r\n            <span>{uploadError}</span>\r\n          </div>\r\n        )}\r\n        {uploadSuccess && (\r\n          <div className=\"text-green-600 flex items-center gap-1 text-xs\" role=\"alert\">\r\n            <span>✓</span>\r\n            <span>{uploadSuccess}</span>\r\n          </div>\r\n        )}\r\n        {/* File list (pending uploads) */}\r\n        {state.files.length > 0 && (\r\n          <div className=\"space-y-2\">\r\n            {state.files.map((file) => (\r\n              <div\r\n                key={file.id}\r\n                className=\"bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3\"\r\n              >\r\n                <div className=\"flex items-center gap-3 overflow-hidden\">\r\n                  <div className=\"flex aspect-square size-10 shrink-0 items-center justify-center rounded border\">\r\n                    {getFileIcon((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name)}\r\n                  </div>\r\n                  <div className=\"flex min-w-0 flex-col gap-0.5\">\r\n                    <p className=\"truncate text-[13px] font-medium\">\r\n                      {(typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.name : file.file.name}\r\n                    </p>\r\n                    <p className=\"text-muted-foreground text-xs\">\r\n                      {formatBytes((typeof file.file === 'object' && 'lastModified' in file.file) ? file.file.size : file.file.size)}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <Button\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  className=\"text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent\"\r\n                  onClick={() => actions.removeFile(file.id)}\r\n                  aria-label=\"Remove file\"\r\n                >\r\n                  <XIcon className=\"size-4\" aria-hidden=\"true\" />\r\n                </Button>\r\n              </div>\r\n            ))}\r\n            {state.files.length > 1 && (\r\n              <div>\r\n                <Button size=\"sm\" variant=\"outline\" onClick={actions.clearFiles}>\r\n                  Remove all files\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n        {/* Uploaded files table */}\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>File Name</TableHead>\r\n              <TableHead className=\"w-40\">Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {attachments.length === 0 ? (\r\n              <TableRow>\r\n                <TableCell colSpan={2} className=\"text-center text-muted-foreground\">No files uploaded yet.</TableCell>\r\n              </TableRow>\r\n            ) : (\r\n              attachments.map((attachment, idx) => (\r\n                <TableRow key={attachment.id || idx}>\r\n                  <TableCell>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      {getFileIcon(attachment.fileName ?? '')}\r\n                      <span title={attachment.fileName ?? ''}>{attachment.fileName}</span>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <div className=\"flex gap-2\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => handleDelete(attachment)}\r\n                        className=\"text-destructive hover:text-destructive\"\r\n                      >\r\n                        <Trash2 className=\"w-4 h-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n    </form>\r\n  );\r\n}"], "names": ["__iconNode", "Circle<PERSON>lert", "createLucideIcon", "Download", "FileText", "FileUp", "File", "Image", "useFileUpload", "options", "maxFiles", "maxSize", "accept", "multiple", "initialFiles", "onFilesChange", "onFilesAdded", "state", "setState", "useState", "file", "inputRef", "useRef", "validateFile", "useCallback", "formatBytes", "acceptedTypes", "type", "fileType", "fileExtension", "baseType", "createPreview", "generateUniqueId", "clearFiles", "prev", "newState", "addFiles", "newFiles", "newFilesArray", "errors", "validFiles", "existingFile", "error", "removeFile", "id", "fileToRemove", "clearErrors", "handleDragEnter", "e", "handleDragLeave", "handleDragOver", "handleDrop", "handleFileChange", "openFileDialog", "getInputProps", "props", "bytes", "decimals", "k", "dm", "sizes", "i", "AttachmentPreviewDialog", "isOpen", "onOpenChange", "attachment", "documentSrc", "setDocumentSrc", "isLoading", "setIsLoading", "setError", "React", "ekbProxyService", "response", "url", "err", "canPreviewInBrowser", "fileName", "extension", "jsx", "Dialog", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "AttachmentDialog", "open", "attachments", "title", "queryClient", "referenceId", "documentReferenceId", "defaultTabName", "docType", "transType", "tabName", "onUploadSuccess", "selected<PERSON>ttach<PERSON>", "setSelectedAttachment", "previewOpen", "setPreviewOpen", "groupedAttachments", "groups", "tabNames", "allTabNames", "selectedTab", "setSelectedTab", "handlePreview", "handleDownload", "rawBaseUrl", "<PERSON><PERSON><PERSON><PERSON>", "blob", "downloadUrl", "link", "handleDelete", "setUploadSuccess", "getFileIcon", "uploading", "setUploading", "actions", "files", "fileObj", "uploadError", "setUploadError", "uploadSuccess", "Fragment", "Tabs", "val", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ekbBaseUrl", "token", "formData", "message", "details", "Input", "FileUpIcon", "AlertCircleIcon", "<PERSON><PERSON>", "XIcon", "Table", "TableHeader", "TableRow", "TableHead", "TableBody", "TableCell", "idx", "Trash2", "Agent<PERSON>ab<PERSON><PERSON><PERSON>"], "mappings": "kaAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,SAAU,CAAE,GAAI,KAAM,GAAI,KAAM,EAAG,KAAM,IAAK,SAAU,EACzD,CAAC,OAAQ,CAAE,GAAI,KAAM,GAAI,KAAM,GAAI,IAAK,GAAI,KAAM,IAAK,QAAQ,CAAE,EACjE,CAAC,OAAQ,CAAE,GAAI,KAAM,GAAI,QAAS,GAAI,KAAM,GAAI,KAAM,IAAK,QAAU,CAAA,CACvE,EACMC,EAAcC,EAAiB,eAAgBF,EAAU,ECd/D;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,4CAA6C,IAAK,QAAQ,CAAE,EAC1E,CAAC,OAAQ,CAAE,EAAG,gBAAiB,IAAK,QAAU,CAAA,CAChD,EACMG,GAAWD,EAAiB,WAAYF,EAAU,ECdxD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,6DAA8D,IAAK,QAAQ,CAAE,EAC3F,CAAC,OAAQ,CAAE,EAAG,0BAA2B,IAAK,QAAQ,CAAE,EACxD,CAAC,OAAQ,CAAE,EAAG,UAAW,IAAK,QAAQ,CAAE,EACxC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAU,CAAA,CAC3C,EACMI,GAAWF,EAAiB,YAAaF,EAAU,EChBzD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,6DAA8D,IAAK,QAAQ,CAAE,EAC3F,CAAC,OAAQ,CAAE,EAAG,0BAA2B,IAAK,QAAQ,CAAE,EACxD,CAAC,OAAQ,CAAE,EAAG,WAAY,IAAK,QAAQ,CAAE,EACzC,CAAC,OAAQ,CAAE,EAAG,iBAAkB,IAAK,QAAU,CAAA,CACjD,EACMK,GAASH,EAAiB,UAAWF,EAAU,ECfrD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,EAAG,6DAA8D,IAAK,QAAQ,CAAE,EAC3F,CAAC,OAAQ,CAAE,EAAG,0BAA2B,IAAK,QAAU,CAAA,CAC1D,EACMM,GAAOJ,EAAiB,OAAQF,EAAU,ECbhD;AAAA;AAAA;AAAA;AAAA;AAAA,GASA,MAAMA,GAAa,CACjB,CAAC,OAAQ,CAAE,MAAO,KAAM,OAAQ,KAAM,EAAG,IAAK,EAAG,IAAK,GAAI,IAAK,GAAI,IAAK,IAAK,SAAU,EACvF,CAAC,SAAU,CAAE,GAAI,IAAK,GAAI,IAAK,EAAG,IAAK,IAAK,SAAU,EACtD,CAAC,OAAQ,CAAE,EAAG,4CAA6C,IAAK,QAAU,CAAA,CAC5E,EACMO,GAAQL,EAAiB,QAASF,EAAU,ECqCrCQ,GAAgB,CAACC,EAA6B,KAA6C,CAChG,KAAA,CACJ,SAAAC,EAAW,IACX,QAAAC,EAAU,IACV,OAAAC,EAAS,IACT,SAAAC,EAAW,GACX,aAAAC,EAAe,CAAC,EAChB,cAAAC,EACA,aAAAC,CAAA,EACEP,EAEE,CAACQ,EAAOC,CAAQ,EAAIC,WAA0B,CAClD,MAAOL,EAAa,IAAKM,IAAU,CACjC,KAAAA,EACA,GAAIA,EAAK,GACT,QAASA,EAAK,GAAA,EACd,EACF,WAAY,GACZ,OAAQ,CAAA,CAAC,CACV,EAEKC,EAAWC,SAAyB,IAAI,EAExCC,EAAeC,EAAA,YAClBJ,GAA6C,CAC5C,GAAIA,aAAgB,MACd,GAAAA,EAAK,KAAOT,EACd,MAAO,SAASS,EAAK,IAAI,iCAAiCK,EAAYd,CAAO,CAAC,YAG5ES,EAAK,KAAOT,EACd,MAAO,SAASS,EAAK,IAAI,iCAAiCK,EAAYd,CAAO,CAAC,IAIlF,GAAIC,IAAW,IAAK,CACZ,MAAAc,EAAgBd,EAAO,MAAM,GAAG,EAAE,IAAKe,GAASA,EAAK,MAAM,EAC3DC,EAAWR,aAAgB,KAAOA,EAAK,MAAQ,GAAKA,EAAK,KACzDS,EAAgB,IAAIT,aAAgB,KAAOA,EAAK,KAAK,MAAM,GAAG,EAAE,IAAA,CAAkC,GAaxG,GAAI,CAXeM,EAAc,KAAMC,GAAS,CAC1C,GAAAA,EAAK,WAAW,GAAG,EACrB,OAAOE,EAAc,gBAAkBF,EAAK,YAAY,EAEtD,GAAAA,EAAK,SAAS,IAAI,EAAG,CACvB,MAAMG,EAAWH,EAAK,MAAM,GAAG,EAAE,CAAC,EAClC,OAAOC,EAAS,WAAW,GAAGE,CAAQ,GAAG,CAAA,CAE3C,OAAOF,IAAaD,CAAA,CACrB,EAGC,MAAO,SAASP,aAAgB,KAAOA,EAAK,IAAgB,iCAC9D,CAGK,OAAA,IACT,EACA,CAACR,EAAQD,CAAO,CAClB,EAEMoB,EAAgBP,cAAaJ,GAC7BA,aAAgB,KACX,IAAI,gBAAgBA,CAAI,EAE1BA,EAAK,IACX,EAAE,EAECY,EAAmBR,cAAaJ,GAChCA,aAAgB,KACX,GAAGA,EAAK,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,OAAA,EAAS,SAAS,EAAE,EAAE,UAAU,EAAG,CAAC,CAAC,GAE1EA,EAAK,GACX,EAAE,EAECa,EAAaT,EAAAA,YAAY,IAAM,CACnCN,EAAUgB,GAAS,CAEZA,EAAA,MAAM,QAASd,GAAS,CACvBA,EAAK,SAAWA,EAAK,gBAAgB,MAAQA,EAAK,KAAK,KAAK,WAAW,QAAQ,GAC7E,IAAA,gBAAgBA,EAAK,OAAO,CAClC,CACD,EAEGC,EAAS,UACXA,EAAS,QAAQ,MAAQ,IAG3B,MAAMc,EAAW,CACf,GAAGD,EACH,MAAO,CAAC,EACR,OAAQ,CAAA,CACV,EAEA,OAAAnB,IAAgBoB,EAAS,KAAK,EACvBA,CAAA,CACR,CAAA,EACA,CAACpB,CAAa,CAAC,EAEZqB,EAAWZ,EAAA,YACda,GAAgC,CAC/B,GAAI,CAACA,GAAYA,EAAS,SAAW,EAAG,OAElC,MAAAC,EAAgB,MAAM,KAAKD,CAAQ,EACnCE,EAAmB,CAAC,EAWtB,GARKrB,EAACgB,IAAU,CAAE,GAAGA,EAAM,OAAQ,IAAK,EAGvCrB,GACQoB,EAAA,EAITpB,GAAYH,IAAa,KAAYO,EAAM,MAAM,OAASqB,EAAc,OAAS5B,EAAU,CACtF6B,EAAA,KAAK,oCAAoC7B,CAAQ,SAAS,EACjEQ,EAAUgB,IAAU,CAAE,GAAGA,EAAM,OAAAK,CAAS,EAAA,EACxC,MAAA,CAGF,MAAMC,EAAgC,CAAC,EAEzBF,EAAA,QAASlB,GAAS,CAE9B,GAAIP,GACkBI,EAAM,MAAM,KAC7BwB,GAAiBA,EAAa,KAAK,OAASrB,EAAK,MAAQqB,EAAa,KAAK,OAASrB,EAAK,IAC5F,EAIE,OAKA,GAAAA,EAAK,KAAOT,EAAS,CAChB4B,EAAA,KACL1B,EACI,yCAAyCY,EAAYd,CAAO,CAAC,IAC7D,oCAAoCc,EAAYd,CAAO,CAAC,GAC9D,EACA,MAAA,CAGI,MAAA+B,EAAQnB,EAAaH,CAAI,EAC3BsB,EACFH,EAAO,KAAKG,CAAK,EAEjBF,EAAW,KAAK,CACd,KAAApB,EACA,GAAIY,EAAiBZ,CAAI,EACzB,QAASW,EAAcX,CAAI,CAAA,CAC5B,CACH,CACD,EAGGoB,EAAW,OAAS,GAEtBxB,IAAewB,CAAU,EAEzBtB,EAAUgB,GAAS,CACXG,MAAAA,EAAYxB,EAAwB,CAAC,GAAGqB,EAAK,MAAO,GAAGM,CAAU,EAA1CA,EAC7B,OAAAzB,IAAgBsB,CAAQ,EACjB,CACL,GAAGH,EACH,MAAOG,EACP,OAAAE,CACF,CAAA,CACD,GACQA,EAAO,OAAS,GACzBrB,EAAUgB,IAAU,CAClB,GAAGA,EACH,OAAAK,CAAA,EACA,EAIAlB,EAAS,UACXA,EAAS,QAAQ,MAAQ,GAE7B,EACA,CACEJ,EAAM,MACNP,EACAG,EACAF,EACAY,EACAQ,EACAC,EACAC,EACAlB,EACAC,CAAA,CAEJ,EAEM2B,EAAanB,EAAA,YAChBoB,GAAe,CACd1B,EAAUgB,GAAS,CACX,MAAAW,EAAeX,EAAK,MAAM,KAAMd,GAASA,EAAK,KAAOwB,CAAE,EAE3DC,GACAA,EAAa,SACbA,EAAa,gBAAgB,MAC7BA,EAAa,KAAK,KAAK,WAAW,QAAQ,GAEtC,IAAA,gBAAgBA,EAAa,OAAO,EAGpC,MAAAR,EAAWH,EAAK,MAAM,OAAQd,GAASA,EAAK,KAAOwB,CAAE,EAC3D,OAAA7B,IAAgBsB,CAAQ,EAEjB,CACL,GAAGH,EACH,MAAOG,EACP,OAAQ,CAAA,CACV,CAAA,CACD,CACH,EACA,CAACtB,CAAa,CAChB,EAEM+B,EAActB,EAAAA,YAAY,IAAM,CACpCN,EAAUgB,IAAU,CAClB,GAAGA,EACH,OAAQ,CAAA,CAAC,EACT,CACJ,EAAG,EAAE,EAECa,EAAkBvB,cAAawB,GAA8B,CACjEA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAClB9B,EAAUgB,IAAU,CAAE,GAAGA,EAAM,WAAY,IAAO,CACpD,EAAG,EAAE,EAECe,EAAkBzB,cAAawB,GAA8B,CACjEA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAEd,CAAAA,EAAE,cAAc,SAASA,EAAE,aAAqB,GAIpD9B,EAAUgB,IAAU,CAAE,GAAGA,EAAM,WAAY,IAAQ,CACrD,EAAG,EAAE,EAECgB,EAAiB1B,cAAawB,GAA8B,CAChEA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,CACpB,EAAG,EAAE,EAECG,EAAa3B,EAAA,YAChBwB,GAA8B,CAMzB,GALJA,EAAE,eAAe,EACjBA,EAAE,gBAAgB,EAClB9B,EAAUgB,IAAU,CAAE,GAAGA,EAAM,WAAY,IAAQ,EAG/C,CAAAb,EAAS,SAAS,UAIlB2B,EAAE,aAAa,OAASA,EAAE,aAAa,MAAM,OAAS,EAExD,GAAKnC,EAIMuB,EAAAY,EAAE,aAAa,KAAK,MAJhB,CACb,MAAM5B,EAAO4B,EAAE,aAAa,MAAM,CAAC,EAC1BZ,EAAA,CAAChB,CAAI,CAAC,CAAA,CAKrB,EACA,CAACgB,EAAUvB,CAAQ,CACrB,EAEMuC,EAAmB5B,EAAA,YACtBwB,GAAqC,CAChCA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,GACnCZ,EAAAY,EAAE,OAAO,KAAK,CAE3B,EACA,CAACZ,CAAQ,CACX,EAEMiB,EAAiB7B,EAAAA,YAAY,IAAM,CACnCH,EAAS,SACXA,EAAS,QAAQ,MAAM,CAE3B,EAAG,EAAE,EAECiC,EAAgB9B,EAAA,YACpB,CAAC+B,EAA+C,CAAA,KACvC,CACL,GAAGA,EACH,KAAM,OACN,SAAUH,EACV,OAAQG,EAAM,QAAU3C,EACxB,SAAU2C,EAAM,WAAa,OAAYA,EAAM,SAAW1C,EAC1D,IAAKQ,CACP,GAEF,CAACT,EAAQC,EAAUuC,CAAgB,CACrC,EAEO,MAAA,CACLnC,EACA,CACE,SAAAmB,EACA,WAAAO,EACA,WAAAV,EACA,YAAAa,EACA,gBAAAC,EACA,gBAAAE,EACA,eAAAC,EACA,WAAAC,EACA,iBAAAC,EACA,eAAAC,EACA,cAAAC,CAAA,CAEJ,CACF,EAGa7B,EAAc,CAAC+B,EAAeC,EAAW,IAAc,CAC9D,GAAAD,IAAU,EAAU,MAAA,UAExB,MAAME,EAAI,KACJC,EAAKF,EAAW,EAAI,EAAIA,EACxBG,EAAQ,CAAC,QAAS,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAEhEC,EAAI,KAAK,MAAM,KAAK,IAAIL,CAAK,EAAI,KAAK,IAAIE,CAAC,CAAC,EAElD,OAAO,OAAO,YAAYF,EAAQ,KAAK,IAAIE,EAAGG,CAAC,GAAG,QAAQF,CAAE,CAAC,EAAIC,EAAMC,CAAC,CAC1E,EC/VMC,GAAkE,CAAC,CACvE,OAAAC,EACA,aAAAC,EACA,WAAAC,CACF,IAAM,CACJ,KAAM,CAACC,EAAaC,CAAc,EAAIhD,EAAAA,SAAiB,EAAE,EACnD,CAACiD,EAAWC,CAAY,EAAIlD,EAAAA,SAAS,EAAK,EAC1C,CAACuB,EAAO4B,CAAQ,EAAInD,EAAAA,SAAiB,EAAE,EAE7CoD,EAAM,UAAU,KACVN,GAAcF,IAChBM,EAAa,EAAI,EACjBC,EAAS,EAAE,EAEXE,GAAgB,cAAcP,EAAW,EAAG,EACzC,KAAMQ,GAAa,CACd,GAAAA,GAAYA,EAAS,gBAAgB,KAAM,CAC7C,MAAMC,EAAM,IAAI,gBAAgBD,EAAS,IAAI,EAC7CN,EAAeO,CAAG,EAClB,MAAA,CAEI,MAAA,IAAI,MAAM,uCAAuC,CAAA,CACxD,EACA,MAAaC,GAAA,CAEZL,EAAS,6BAA6B,CAAA,CACvC,EACA,QAAQ,IAAM,CACbD,EAAa,EAAK,CAAA,CACnB,GAIE,IAAM,CAGb,GACC,CAACJ,EAAYF,CAAM,CAAC,EAGvBQ,EAAM,UAAU,IACP,IAAM,CACPL,GAAeA,EAAY,WAAW,OAAO,GAC/C,IAAI,gBAAgBA,CAAW,CAEnC,EACC,CAACA,CAAW,CAAC,EAEV,MAAAU,EAAuBC,GAAqB,CAChD,MAAMC,EAAYD,EAAS,MAAM,GAAG,EAAE,OAAO,YAAY,EACzD,MAAO,CAAC,MAAO,MAAO,OAAQ,MAAO,MAAO,MAAO,OAAQ,KAAK,EAAE,SAASC,GAAa,EAAE,CAC5F,EAEI,OAACb,EAGHc,EAAA,IAACC,IAAO,KAAMjB,EAAQ,aAAAC,EACpB,SAACiB,EAAAA,KAAAC,GAAA,CAAc,UAAU,+CACvB,SAAA,CAAAH,EAAAA,IAACI,GACC,CAAA,SAAAJ,MAACK,GAAa,CAAA,SAAAnB,EAAW,QAAS,CAAA,EACpC,EACAc,EAAAA,IAAC,OAAI,UAAU,0BACZ,WACEE,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACb,SAAA,CAACF,EAAAA,IAAA,MAAA,CAAI,UAAU,6DAA8D,CAAA,EAC5EA,EAAA,IAAA,OAAA,CAAK,UAAU,OAAO,SAAkB,oBAAA,CAAA,CAAA,EAC3C,EACErC,EACDqC,MAAA,MAAA,CAAI,UAAU,uDACb,SAAAA,MAAC,OAAM,CAAA,SAAArC,CAAM,CAAA,EACf,EACEkC,EAAoBX,EAAW,UAAY,EAAE,EAC/Cc,EAAA,IAAC,SAAA,CACC,IAAKb,EACL,MAAM,mBACN,UAAU,2BAAA,CACZ,QAEC,MAAI,CAAA,UAAU,gEACb,SAACe,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACF,EAAAA,IAAAzE,GAAA,CAAK,UAAU,wBAAyB,CAAA,EACzCyE,EAAAA,IAAC,KAAE,SAAwC,0CAAA,CAAA,EAC1CA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAU,SAAmC,qCAAA,CAAA,CAAA,CAC5D,CAAA,CACF,CAAA,CAEJ,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAnCsB,IAqC1B,EAEaM,GAAoD,CAAC,CAChE,KAAAC,EACA,aAAAtB,EACA,YAAAuB,EACA,MAAAC,EACA,YAAAC,EACA,YAAAC,EACA,oBAAAC,EACA,eAAAC,EACA,QAAAC,EACA,UAAAC,EACA,QAAAC,EACA,gBAAAC,CACF,IAAM,CACJ,KAAM,CAACC,EAAoBC,CAAqB,EAAI/E,EAAAA,SAAkC,IAAI,EACpF,CAACgF,EAAaC,CAAc,EAAIjF,EAAAA,SAAS,EAAK,EAG9CkF,EAAqBd,EAAY,OAAO,CAACe,EAAQrC,IAAe,CAC9D8B,MAAAA,EAAU9B,EAAW,SAAW,QAClC,OAACqC,EAAOP,CAAO,IACVA,EAAAA,CAAO,EAAI,CAAC,GAEdA,EAAAA,CAAO,EAAE,KAAK9B,CAAU,EACxBqC,CACT,EAAG,EAAwC,EAErCC,EAAW,OAAO,KAAKF,CAAkB,EAEzCG,EADiBD,EAAS,SAAS,UAAU,EACdA,EAAW,CAAC,GAAGA,EAAU,UAAU,EAClE,CAACE,EAAaC,CAAc,EAAIvF,EAAA,SACpCyE,GAAkBY,EAAY,SAASZ,CAAc,EACjDA,EACAY,EAAY,CAAC,GAAK,EACxB,EAGAjC,EAAM,UAAU,IAAM,CACfiC,EAAY,SAASC,CAAW,GACpBC,EAAAF,EAAY,CAAC,GAAK,EAAE,CACrC,EACC,CAACA,EAAaC,CAAW,CAAC,EAEvB,MAAAE,EAAiB1C,GAAiC,CACtDiC,EAAsBjC,CAAU,EAChCmC,EAAe,EAAI,CACrB,EAEMQ,EAAiB,MAAO3C,GAAiC,CACzD,GAAA,CAEI,MAAA4C,EAAaC,EAAU,QAAQ,EAE/BpC,EAAM,GADOmC,EAAa,mBAAmBA,CAAU,EAAI,4BACxC,yBAAyB5C,EAAW,EAAE,GAEzDQ,EAAW,MAAM,MAAMC,EAAK,CAChC,OAAQ,MACR,YAAa,SAAA,CACd,EAEG,GAAA,CAACD,EAAS,GACZ,MAAM,IAAI,MAAM,uBAAuBA,EAAS,MAAM,EAAE,EAGpD,MAAAsC,EAAO,MAAMtC,EAAS,KAAK,EAG3BuC,EAAc,OAAO,IAAI,gBAAgBD,CAAI,EAC7CE,EAAO,SAAS,cAAc,GAAG,EACvCA,EAAK,KAAOD,EACPC,EAAA,SAAWhD,EAAW,UAAY,GAC9B,SAAA,KAAK,YAAYgD,CAAI,EAC9BA,EAAK,MAAM,EACF,SAAA,KAAK,YAAYA,CAAI,EACvB,OAAA,IAAI,gBAAgBD,CAAW,OACxB,CAAA,CAGlB,EAEME,EAAe,MAAOjD,GAAiC,CACvD,GAACA,EAAW,IAKX,QAAQ,oCAAoCA,EAAW,QAAQ,IAAI,EAIpE,GAAA,CACI,MAAAO,GAAgB,oBAAoBP,EAAW,EAAE,EAGvDwB,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAG7D0B,EAAiB,kCAAkC,EACnD,WAAW,IAAMA,EAAiB,IAAI,EAAG,GAAI,EAG3BnB,IAAA,OACJ,CAEd,MAAM,6BAA6B,CAAA,CAEvC,EAEMoB,EAAevC,GAAqB,CAExC,OADkBA,EAAS,MAAM,GAAG,EAAE,OAAO,YAAY,EACtC,CACjB,IAAK,MACI,OAAAE,EAAA,IAAC3E,GAAS,CAAA,UAAU,SAAU,CAAA,EACvC,IAAK,MACL,IAAK,OACL,IAAK,MACL,IAAK,MACI,OAAA2E,EAAA,IAACxE,GAAM,CAAA,UAAU,SAAU,CAAA,EACpC,QACS,OAAAwE,EAAA,IAACzE,GAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAEvC,EAEMK,EAAU,IAAM,KAAO,KACvBD,EAAW,GACX,CAAC2G,EAAWC,CAAY,EAAI/C,EAAM,SAAS,EAAK,EAChD,CAACtD,EAAOsG,CAAO,EAAI/G,GAAc,CACrC,SAAU,GACV,SAAAE,EACA,QAAAC,EACA,aAAc,MAAO6G,GAAU,CAC7B,UAAWC,KAAWD,EAEhBC,EAAQ,MAAQ,OAAOA,EAAQ,MAAS,UAAY,SAAUA,EAAQ,MAAQ,SAAUA,EAAQ,KAItGF,EAAQ,WAAW,CAAA,CACrB,CACD,EACK,CAACG,EAAaC,CAAc,EAAIpD,EAAM,SAAwB,IAAI,EAClE,CAACqD,GAAeT,CAAgB,EAAI5C,EAAM,SAAwB,IAAI,EAE5E,OAEIU,EAAA,KAAA4C,WAAA,CAAA,SAAA,CAAA9C,EAAAA,IAACC,IAAO,KAAAM,EAAY,aAAAtB,EAClB,SAACiB,EAAA,KAAAC,GAAA,CAAc,UAAU,8CACvB,SAAA,CAAAH,MAACI,GACC,CAAA,SAAAJ,EAAA,IAACK,GAAa,CAAA,SAAAI,CAAM,CAAA,EACtB,EACCT,EAAA,IAAA,MAAA,CAAI,UAAU,4BACb,gBAAC+C,GAAK,CAAA,MAAOrB,EAAa,cAAsBsB,GAAArB,EAAeqB,CAAG,EAAG,UAAU,uBAC7E,SAAA,CAAAhD,MAACiD,IAAS,UAAW,yBAAyBxB,EAAY,MAAM,GAC7D,WAAS,IAAIT,GACZhB,EAAA,IAACkD,IAA0B,MAAOlC,EAC/B,SAAAA,CADeA,EAAAA,CAElB,CACD,EAEH,EACCQ,EAAS,OAAS,EAAIA,EAAS,IAAIR,GACjChB,EAAAA,IAAAmD,GAAA,CAA0B,MAAOnC,EAChC,SAACd,EAAA,KAAA,MAAA,CAAI,UAAU,MAEb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,KAAK,SACL,QAASoC,EAAY,OAAYE,EAAQ,eACzC,YAAaF,EAAY,OAAkBrE,GAAA,CAAEA,EAAE,eAAe,EAAGuE,EAAQ,gBAAgBvE,CAAC,CAAG,EAC7F,YAAaqE,EAAY,OAAkBrE,GAAA,CAAEA,EAAE,eAAe,EAAGuE,EAAQ,gBAAgBvE,CAAC,CAAG,EAC7F,WAAYqE,EAAY,OAAarE,GAAKA,EAAE,eAAe,EAC3D,OAAQqE,EAAY,OAAY,MAAMrE,GAAK,CAGzC,GAFAA,EAAE,eAAe,EACjBuE,EAAQ,WAAWvE,CAAC,EAChBA,EAAE,aAAa,OAASA,EAAE,aAAa,MAAM,OAAS,EAAG,CAC3D,MAAM5B,EAAO4B,EAAE,aAAa,MAAM,CAAC,EACnCsE,EAAa,EAAI,EACb,GAAA,CACI,MAAAT,EAAaC,EAAU,QAAQ,EAC/BqB,EAAatB,EAAa,mBAAmBA,CAAU,EAAI,6BACjE,IAAInC,EAAM,kCACNA,EAAI,WAAW,GAAG,IACpBA,EAAMyD,EAAW,QAAQ,MAAO,EAAE,EAAIzD,GAElC,MAAA0D,EAAQtB,EAAU,aAAa,GAAK,GACpCuB,EAAW,IAAI,SACZA,EAAA,OAAO,OAAQjH,CAAI,EAC5BiH,EAAS,OAAO,cAAe,OAAO3C,CAAW,CAAC,EACzC2C,EAAA,OAAO,UAAWxC,GAAW,QAAQ,EACrCwC,EAAA,OAAO,gBAAiBxC,GAAW,QAAQ,EAC3CwC,EAAA,OAAO,YAAavC,GAAa,eAAe,EACzDuC,EAAS,OAAO,UAAWtC,GAAYH,GAAkB,UAAW,EACpEyC,EAAS,OAAO,sBAAuB,OAAO1C,CAAmB,CAAC,EAClE,MAAM,MAAMjB,EAAK,CACf,OAAQ,OACR,KAAM2D,EACN,YAAa,UACb,QAASD,EAAQ,CAAE,cAAe,UAAUA,CAAK,IAAO,MAAA,CACzD,EACD3C,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DkC,EAAe,IAAI,EACnBR,EAAiB,6BAA6B,EAC9CG,EAAa,EAAK,EACAtB,IAAA,EAElB,WAAW,IAAMmB,EAAiB,IAAI,EAAG,GAAI,QACtCxC,EAAK,CACZ2C,EAAa,EAAK,EAGlB,IAAIgB,EAAU,gBACVC,EAAU,GACV5D,GAAO,OAAOA,GAAQ,WACpB,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,SACnE,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,UAE1DgD,EAAAY,EAAU,GAAGD,CAAO;AAAA,EAAKC,CAAO,GAAKD,CAAO,CAAA,CAC7D,CAEJ,EACA,gBAAerH,EAAM,YAAc,OACnC,UAAU,gUACV,gBAAeoG,EACf,SAAUA,EAAY,GAAK,EAE3B,SAAA,CAAAtC,EAAA,IAACyD,GAAA,CACE,GAAGjB,EAAQ,cAAc,EAC1B,SAAUF,EACV,SAAU,MAAMrE,GAAK,CACnB,GAAIA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,EAAG,CAC/C,MAAM5B,EAAO4B,EAAE,OAAO,MAAM,CAAC,EAC7BsE,EAAa,EAAI,EACb,GAAA,CACI,MAAAT,EAAaC,EAAU,QAAQ,EAC/BqB,EAAatB,EAAa,mBAAmBA,CAAU,EAAI,6BACjE,IAAInC,EAAM,kCACNA,EAAI,WAAW,GAAG,IACpBA,EAAMyD,EAAW,QAAQ,MAAO,EAAE,EAAIzD,GAElC,MAAA0D,EAAQtB,EAAU,aAAa,GAAK,GACpCuB,EAAW,IAAI,SACZA,EAAA,OAAO,OAAQjH,CAAI,EAC5BiH,EAAS,OAAO,cAAe,OAAO3C,CAAW,CAAC,EACzC2C,EAAA,OAAO,UAAWxC,GAAW,QAAQ,EACrCwC,EAAA,OAAO,gBAAiBxC,GAAW,QAAQ,EAC3CwC,EAAA,OAAO,YAAavC,GAAa,eAAe,EACzDuC,EAAS,OAAO,UAAWtC,GAAYH,GAAkB,UAAW,EACpEyC,EAAS,OAAO,sBAAuB,OAAO1C,CAAmB,CAAC,EAClE,MAAM,MAAMjB,EAAK,CACf,OAAQ,OACR,KAAM2D,EACN,YAAa,UACb,QAASD,EAAQ,CAAE,cAAe,UAAUA,CAAK,IAAO,MAAA,CACzD,EACD3C,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DkC,EAAe,IAAI,EACnBR,EAAiB,6BAA6B,EAC9CG,EAAa,EAAK,EACAtB,IAAA,EAElB,WAAW,IAAMmB,EAAiB,IAAI,EAAG,GAAI,QACtCxC,EAAK,CACZ2C,EAAa,EAAK,EAGlB,IAAIgB,EAAU,gBACVC,EAAU,GACV5D,GAAO,OAAOA,GAAQ,WACpB,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,SACnE,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,UAE1DgD,EAAAY,EAAU,GAAGD,CAAO;AAAA,EAAKC,CAAO,GAAKD,CAAO,CAAA,CAC7D,CAEJ,EACA,UAAU,UACV,aAAW,cAAA,CACb,EACArD,EAAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAAAF,EAAA,IAAC,MAAA,CACC,UAAU,2FACV,cAAY,OAEZ,SAAAA,EAAAA,IAAC0D,GAAW,CAAA,UAAU,mBAAoB,CAAA,CAAA,CAC5C,EACC1D,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAY,eAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,qCAAqC,SAElD,iCAAA,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAAAF,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,EACfA,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,SACN,OAAK,CAAA,SAAA,CAAA,OAAKrE,EAAS,QAAA,EAAM,EAC1BqE,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,SACN,OAAK,CAAA,SAAA,CAAA,SAAOtD,EAAYd,CAAO,CAAA,CAAE,CAAA,CAAA,CACpC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACC0G,GACCpC,EAAA,KAAC,MAAI,CAAA,UAAU,6DACb,SAAA,CAACF,EAAAA,IAAA,OAAA,CAAK,UAAU,6DAA8D,CAAA,EAAO,cAAA,EAEvF,EAED9D,EAAM,OAAO,OAAS,UACpB,MAAI,CAAA,UAAU,mDAAmD,KAAK,QACrE,SAAA,CAAC8D,EAAAA,IAAA2D,EAAA,CAAgB,UAAU,iBAAkB,CAAA,EAC5C3D,EAAA,IAAA,OAAA,CAAM,SAAM9D,EAAA,OAAO,CAAC,CAAE,CAAA,CAAA,EACzB,EAEDyG,GACEzC,EAAAA,KAAA,MAAA,CAAI,UAAU,mDAAmD,KAAK,QACrE,SAAA,CAACF,EAAAA,IAAA2D,EAAA,CAAgB,UAAU,iBAAkB,CAAA,EAC7C3D,EAAAA,IAAC,QAAM,SAAY2C,CAAA,CAAA,CAAA,EACrB,EAEDE,IACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,iDAAiD,KAAK,QACnE,SAAA,CAAAF,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,EACPA,EAAAA,IAAC,QAAM,SAAc6C,EAAA,CAAA,CAAA,EACvB,EAGD3G,EAAM,MAAM,OAAS,GACnBgE,EAAAA,KAAA,MAAA,CAAI,UAAU,YACZ,SAAA,CAAMhE,EAAA,MAAM,IAAKG,GAChB6D,EAAA,KAAC,MAAA,CAEC,UAAU,mFAEV,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACb,SAAA,CAAAF,MAAC,OAAI,UAAU,iFACZ,SAAaqC,GAAA,OAAOhG,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,KAAqB,EAC/G,EACA6D,EAAAA,KAAC,MAAI,CAAA,UAAU,gCACb,SAAA,CAAAF,MAAC,IAAE,CAAA,UAAU,mCACT,UAAA,OAAO3D,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,MAC9E,QACC,IAAE,CAAA,UAAU,gCACV,SAAaK,GAAA,OAAOL,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,KAAqB,CAC/G,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACA2D,EAAA,IAAC4D,EAAA,CACC,KAAK,OACL,QAAQ,QACR,UAAU,mFACV,QAAS,IAAMpB,EAAQ,WAAWnG,EAAK,EAAE,EACzC,aAAW,cAEX,SAAC2D,EAAA,IAAA6D,GAAA,CAAM,UAAU,SAAS,cAAY,MAAO,CAAA,CAAA,CAAA,CAC/C,CAAA,EAxBKxH,EAAK,EAAA,CA0Bb,EACAH,EAAM,MAAM,OAAS,GACpB8D,EAAA,IAAC,OACC,SAACA,EAAAA,IAAA4D,EAAA,CAAO,KAAK,KAAK,QAAQ,UAAU,QAASpB,EAAQ,WAAY,4BAEjE,CACF,CAAA,CAAA,EAEJ,SAGDsB,GACC,CAAA,SAAA,CAAC9D,EAAA,IAAA+D,GAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAAAhE,EAAAA,IAACiE,GAAU,SAAS,WAAA,CAAA,EACnBjE,EAAA,IAAAiE,EAAA,CAAU,UAAU,OAAO,SAAO,SAAA,CAAA,CAAA,CAAA,CACrC,CACF,CAAA,EACAjE,EAAAA,IAACkE,IACE,SAAY1D,EAAA,SAAW,EACrBR,MAAAgE,EAAA,CACC,SAAChE,EAAA,IAAAmE,EAAA,CAAU,QAAS,EAAG,UAAU,oCAAoC,SAAA,wBAAA,CAAsB,EAC7F,EAEA3D,EAAY,IAAI,CAACtB,EAAYkF,IAC3BlE,EAAA,KAAC8D,EACC,CAAA,SAAA,CAAAhE,MAACmE,EACC,CAAA,SAAAjE,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACZ,SAAA,CAAYmC,EAAAnD,EAAW,UAAY,EAAE,QACrC,OAAK,CAAA,MAAOA,EAAW,UAAY,GAAK,WAAW,QAAS,CAAA,CAAA,CAAA,CAC/D,CACF,CAAA,EACCc,MAAAmE,EAAA,CACC,SAACjE,EAAAA,KAAA,MAAA,CAAI,UAAU,aACb,SAAA,CAACF,EAAAA,IAAA4D,EAAA,CAAO,QAAQ,UAAU,KAAK,KAAK,QAAS,IAAMhC,EAAc1C,CAAU,EAAG,SAE9E,SAAA,CAAA,EACCc,EAAA,IAAA4D,EAAA,CAAO,QAAQ,UAAU,KAAK,KAAK,QAAS,IAAM/B,EAAe3C,CAAU,EAC1E,SAAAc,MAAC5E,GAAS,CAAA,UAAU,SAAU,CAAA,EAChC,EACA4E,EAAA,IAAC4D,EAAA,CACC,QAAQ,UACR,KAAK,KACL,QAAS,IAAMzB,EAAajD,CAAU,EACtC,UAAU,0CAEV,SAAAc,EAAAA,IAACqE,GAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,CAC9B,CAAA,CACF,CACF,CAAA,CAAA,CAAA,EAxBanF,EAAW,IAAMkF,CAyBhC,CACD,CAEL,CAAA,CAAA,CACF,CAAA,CAAA,EACF,GA1PgBpD,CA2PlB,CACD,EAAKhB,EAAAA,IAAAmD,GAAA,CAA2B,MAAM,WACrC,SAAAnD,EAAA,IAACsE,GAAA,CACC,YAAA9D,EACA,YAAA6B,EACA,YAAA3B,EACA,YAAAC,EACA,oBAAAC,EACA,QAAAE,EACA,UAAAC,EACA,QAAAC,EACA,eAAAH,EACA,gBAAAI,CAAA,IAXkB,UAatB,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,EAEAjB,EAAA,IAACjB,GAAA,CACC,OAAQqC,EACR,aAAcC,EACd,WAAYH,CAAA,CAAA,CACd,EACF,CAEJ,EAeA,SAASoD,GAAgB,CACvB,YAAA9D,EAAa,YAAA6B,EAAa,YAAA3B,EAAa,YAAAC,EAAa,oBAAAC,EACpD,QAAAE,EAAS,UAAAC,EAAW,QAAAC,EAAS,eAAAH,EAAgB,gBAAAI,CAC/C,EAAyB,CAGvB,KAAM,CAACqB,EAAWC,CAAY,EAAI/C,EAAM,SAAS,EAAK,EAChD,CAACtD,EAAOsG,CAAO,EAAI/G,GAAc,CACrC,SAAU,GACV,YACA,kBACA,aAAc,MAAOgH,GAAU,CAC7B,UAAWC,KAAWD,EAEhBC,EAAQ,MAAQ,OAAOA,EAAQ,MAAS,UAAY,SAAUA,EAAQ,MAAQ,SAAUA,EAAQ,KAItGF,EAAQ,WAAW,CAAA,CACrB,CACD,EACK,CAACG,EAAaC,CAAc,EAAIpD,EAAM,SAAwB,IAAI,EAClE,CAACqD,EAAeT,CAAgB,EAAI5C,EAAM,SAAwB,IAAI,EAEtE2C,EAAe,MAAOjD,GAAiC,CACvD,GAACA,EAAW,IAKX,QAAQ,oCAAoCA,EAAW,QAAQ,IAAI,EAIpE,GAAA,CACI,MAAAO,GAAgB,oBAAoBP,EAAW,EAAE,EAGvDwB,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAG3CO,IAAA,OACJ,CAEd,MAAM,6BAA6B,CAAA,CAEvC,EAEA,OACGjB,EAAAA,IAAA,OAAA,CAAK,SAAU/B,GAAKA,EAAE,eAAkB,EAAA,UAAU,YACjD,SAAAiC,OAAC,MAAI,CAAA,UAAU,sBAEb,SAAA,CAAAA,EAAA,KAAC,MAAA,CACC,KAAK,SACL,QAASoC,EAAY,OAAYE,EAAQ,eACzC,YAAaF,EAAY,OAAkBrE,GAAA,CAAEA,EAAE,eAAe,EAAGuE,EAAQ,gBAAgBvE,CAAC,CAAG,EAC7F,YAAaqE,EAAY,OAAkBrE,GAAA,CAAEA,EAAE,eAAe,EAAGuE,EAAQ,gBAAgBvE,CAAC,CAAG,EAC7F,WAAYqE,EAAY,OAAarE,GAAKA,EAAE,eAAe,EAC3D,OAAQqE,EAAY,OAAY,MAAMrE,GAAK,CAGzC,GAFAA,EAAE,eAAe,EACjBuE,EAAQ,WAAWvE,CAAC,EAChBA,EAAE,aAAa,OAASA,EAAE,aAAa,MAAM,OAAS,EAAG,CAC3D,MAAM5B,EAAO4B,EAAE,aAAa,MAAM,CAAC,EACnCsE,EAAa,EAAI,EACb,GAAA,CACI,MAAAT,EAAaC,EAAU,QAAQ,EAC/BqB,EAAatB,EAAa,mBAAmBA,CAAU,EAAI,6BACjE,IAAInC,EAAM,kCACNA,EAAI,WAAW,GAAG,IACpBA,EAAMyD,EAAW,QAAQ,MAAO,EAAE,EAAIzD,GAElC,MAAA0D,EAAQtB,EAAU,aAAa,GAAK,GACpCuB,EAAW,IAAI,SACZA,EAAA,OAAO,OAAQjH,CAAI,EAC5BiH,EAAS,OAAO,cAAe,OAAO3C,CAAW,CAAC,EACzC2C,EAAA,OAAO,UAAWxC,GAAW,QAAQ,EACrCwC,EAAA,OAAO,gBAAiBxC,GAAW,QAAQ,EAC3CwC,EAAA,OAAO,YAAavC,GAAa,eAAe,EACzDuC,EAAS,OAAO,UAAWtC,GAAYH,GAAkB,UAAW,EACpEyC,EAAS,OAAO,sBAAuB,OAAO1C,CAAmB,CAAC,EAClE,MAAM,MAAMjB,EAAK,CACf,OAAQ,OACR,KAAM2D,EACN,YAAa,UACb,QAASD,EAAQ,CAAE,cAAe,UAAUA,CAAK,IAAO,MAAA,CACzD,EACD3C,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DkC,EAAe,IAAI,EACnBR,EAAiB,6BAA6B,EAC9CG,EAAa,EAAK,EACAtB,IAAA,EAElB,WAAW,IAAMmB,EAAiB,IAAI,EAAG,GAAI,QACtCxC,EAAK,CACZ2C,EAAa,EAAK,EAGlB,IAAIgB,EAAU,gBACVC,EAAU,GACV5D,GAAO,OAAOA,GAAQ,WACpB,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,SACnE,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,UAE1DgD,EAAAY,EAAU,GAAGD,CAAO;AAAA,EAAKC,CAAO,GAAKD,CAAO,CAAA,CAC7D,CAEJ,EACA,gBAAerH,EAAM,YAAc,OACnC,UAAU,gUACV,gBAAeoG,EACf,SAAUA,EAAY,GAAK,EAE3B,SAAA,CAAAtC,EAAA,IAACyD,GAAA,CACE,GAAGjB,EAAQ,cAAc,EAC1B,SAAUF,EACV,SAAU,MAAMrE,GAAK,CACnB,GAAIA,EAAE,OAAO,OAASA,EAAE,OAAO,MAAM,OAAS,EAAG,CAC/C,MAAM5B,EAAO4B,EAAE,OAAO,MAAM,CAAC,EAC7BsE,EAAa,EAAI,EACb,GAAA,CACI,MAAAT,EAAaC,EAAU,QAAQ,EAC/BqB,EAAatB,EAAa,mBAAmBA,CAAU,EAAI,6BACjE,IAAInC,EAAM,kCACNA,EAAI,WAAW,GAAG,IACpBA,EAAMyD,EAAW,QAAQ,MAAO,EAAE,EAAIzD,GAElC,MAAA0D,EAAQtB,EAAU,aAAa,GAAK,GACpCuB,EAAW,IAAI,SACZA,EAAA,OAAO,OAAQjH,CAAI,EAC5BiH,EAAS,OAAO,cAAe,OAAO3C,CAAW,CAAC,EACzC2C,EAAA,OAAO,UAAWxC,GAAW,QAAQ,EACrCwC,EAAA,OAAO,gBAAiBxC,GAAW,QAAQ,EAC3CwC,EAAA,OAAO,YAAavC,GAAa,eAAe,EACzDuC,EAAS,OAAO,UAAWtC,GAAYH,GAAkB,UAAW,EACpEyC,EAAS,OAAO,sBAAuB,OAAO1C,CAAmB,CAAC,EAClE,MAAM,MAAMjB,EAAK,CACf,OAAQ,OACR,KAAM2D,EACN,YAAa,UACb,QAASD,EAAQ,CAAE,cAAe,UAAUA,CAAK,IAAO,MAAA,CACzD,EACD3C,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,cAAc,EAAG,EAC5DA,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAC7DkC,EAAe,IAAI,EACnBR,EAAiB,6BAA6B,EAC9CG,EAAa,EAAK,EACAtB,IAAA,EAElB,WAAW,IAAMmB,EAAiB,IAAI,EAAG,GAAI,QACtCxC,EAAK,CACZ2C,EAAa,EAAK,EAGlB,IAAIgB,EAAU,gBACVC,EAAU,GACV5D,GAAO,OAAOA,GAAQ,WACpB,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,SACnE,YAAaA,GAAO,OAAOA,EAAI,SAAY,aAAoBA,EAAI,UAE1DgD,EAAAY,EAAU,GAAGD,CAAO;AAAA,EAAKC,CAAO,GAAKD,CAAO,CAAA,CAC7D,CAEJ,EACA,UAAU,UACV,aAAW,cAAA,CACb,EACArD,EAAAA,KAAC,MAAI,CAAA,UAAU,wDACb,SAAA,CAAAF,EAAA,IAAC,MAAA,CACC,UAAU,2FACV,cAAY,OAEZ,SAAAA,EAAAA,IAAC0D,GAAW,CAAA,UAAU,mBAAoB,CAAA,CAAA,CAC5C,EACC1D,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAY,eAAA,EACrDA,EAAA,IAAA,IAAA,CAAE,UAAU,qCAAqC,SAElD,iCAAA,EACAE,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAAAF,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,EACfA,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,SACN,OAAK,CAAA,SAAA,CAAA,OAAK,GAAS,QAAA,EAAM,EAC1BA,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,SACN,OAAK,CAAA,SAAA,CAAA,SAAOtD,EAAY,SAAO,CAAA,CAAE,CAAA,CAAA,CACpC,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,EACC4F,GACCpC,EAAA,KAAC,MAAI,CAAA,UAAU,6DACb,SAAA,CAACF,EAAAA,IAAA,OAAA,CAAK,UAAU,6DAA8D,CAAA,EAAO,cAAA,EAEvF,EAED9D,EAAM,OAAO,OAAS,UACpB,MAAI,CAAA,UAAU,mDAAmD,KAAK,QACrE,SAAA,CAAC8D,EAAAA,IAAA2D,EAAA,CAAgB,UAAU,iBAAkB,CAAA,EAC5C3D,EAAA,IAAA,OAAA,CAAM,SAAM9D,EAAA,OAAO,CAAC,CAAE,CAAA,CAAA,EACzB,EAEDyG,GACEzC,EAAAA,KAAA,MAAA,CAAI,UAAU,mDAAmD,KAAK,QACrE,SAAA,CAACF,EAAAA,IAAA2D,EAAA,CAAgB,UAAU,iBAAkB,CAAA,EAC7C3D,EAAAA,IAAC,QAAM,SAAY2C,CAAA,CAAA,CAAA,EACrB,EAEDE,GACE3C,EAAAA,KAAA,MAAA,CAAI,UAAU,iDAAiD,KAAK,QACnE,SAAA,CAAAF,EAAAA,IAAC,QAAK,SAAC,GAAA,CAAA,EACPA,EAAAA,IAAC,QAAM,SAAc6C,CAAA,CAAA,CAAA,EACvB,EAGD3G,EAAM,MAAM,OAAS,GACnBgE,EAAAA,KAAA,MAAA,CAAI,UAAU,YACZ,SAAA,CAAMhE,EAAA,MAAM,IAAKG,GAChB6D,EAAA,KAAC,MAAA,CAEC,UAAU,mFAEV,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,0CACb,SAAA,CAAAF,MAAC,OAAI,UAAU,iFACZ,SAAaqC,GAAA,OAAOhG,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,KAAqB,EAC/G,EACA6D,EAAAA,KAAC,MAAI,CAAA,UAAU,gCACb,SAAA,CAAAF,MAAC,IAAE,CAAA,UAAU,mCACT,UAAA,OAAO3D,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,MAC9E,QACC,IAAE,CAAA,UAAU,gCACV,SAAaK,GAAA,OAAOL,EAAK,MAAS,UAAY,iBAAkBA,EAAK,KAAQA,EAAK,KAAK,KAAqB,CAC/G,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACA2D,EAAA,IAAC4D,EAAA,CACC,KAAK,OACL,QAAQ,QACR,UAAU,mFACV,QAAS,IAAMpB,EAAQ,WAAWnG,EAAK,EAAE,EACzC,aAAW,cAEX,SAAC2D,EAAA,IAAA6D,GAAA,CAAM,UAAU,SAAS,cAAY,MAAO,CAAA,CAAA,CAAA,CAC/C,CAAA,EAxBKxH,EAAK,EAAA,CA0Bb,EACAH,EAAM,MAAM,OAAS,GACpB8D,EAAA,IAAC,OACC,SAACA,EAAAA,IAAA4D,EAAA,CAAO,KAAK,KAAK,QAAQ,UAAU,QAASpB,EAAQ,WAAY,4BAEjE,CACF,CAAA,CAAA,EAEJ,SAGDsB,GACC,CAAA,SAAA,CAAC9D,EAAA,IAAA+D,GAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAAAhE,EAAAA,IAACiE,GAAU,SAAS,WAAA,CAAA,EACnBjE,EAAA,IAAAiE,EAAA,CAAU,UAAU,OAAO,SAAO,SAAA,CAAA,CAAA,CAAA,CACrC,CACF,CAAA,EACAjE,EAAAA,IAACkE,IACE,SAAY1D,EAAA,SAAW,EACrBR,MAAAgE,EAAA,CACC,SAAChE,EAAA,IAAAmE,EAAA,CAAU,QAAS,EAAG,UAAU,oCAAoC,SAAA,wBAAA,CAAsB,EAC7F,EAEA3D,EAAY,IAAI,CAACtB,EAAYkF,IAC3BlE,EAAA,KAAC8D,EACC,CAAA,SAAA,CAAAhE,MAACmE,EACC,CAAA,SAAAjE,EAAAA,KAAC,MAAI,CAAA,UAAU,0BACZ,SAAA,CAAYmC,EAAAnD,EAAW,UAAY,EAAE,QACrC,OAAK,CAAA,MAAOA,EAAW,UAAY,GAAK,WAAW,QAAS,CAAA,CAAA,CAAA,CAC/D,CACF,CAAA,EACCc,MAAAmE,EAAA,CACC,SAACnE,EAAA,IAAA,MAAA,CAAI,UAAU,aACb,SAAAA,EAAA,IAAC4D,EAAA,CACC,QAAQ,UACR,KAAK,KACL,QAAS,IAAMzB,EAAajD,CAAU,EACtC,UAAU,0CAEV,SAAAc,EAAAA,IAACqE,GAAO,CAAA,UAAU,SAAU,CAAA,CAAA,GAEhC,CACF,CAAA,CAAA,CAAA,EAlBanF,EAAW,IAAMkF,CAmBhC,CACD,CAEL,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}