# Cookie Lifetime Configuration

## Overview

This document explains the cookie lifetime configuration for the Jetty Approval application and the recent changes made to extend session duration.

## Current Configuration (Updated)

### Authentication Cookies
- **Authentication Cookie**: 8 hours (`TimeSpan.FromHours(8)`)
- **Session Timeout**: 8 hours (`TimeSpan.FromHours(8)`)
- **Antiforgery Cookie**: 2 hours (`TimeSpan.FromHours(2)`)
- **Token Refresh Interval**: 5 minutes (background refresh)

### Key Features
- **Sliding Expiration**: Authentication cookies are refreshed on each request
- **Background Token Refresh**: Access tokens are refreshed every 5 minutes automatically
- **Activity-Based Refresh**: Tokens are refreshed on user activity (mouse, keyboard, scroll)
- **Visibility-Based Refresh**: Tokens are refreshed when the page becomes visible

## Configuration Files Modified

### 1. Authentication Configuration
**File**: `src/Imip.JettyApproval.Web/Services/AuthenticationConfigurationService.cs`

```csharp
// Before
options.ExpireTimeSpan = TimeSpan.FromMinutes(15);

// After
options.ExpireTimeSpan = TimeSpan.FromHours(8);
```

### 2. Session Configuration
**File**: `src/Imip.JettyApproval.Web/JettyApprovalWebModule.cs`

```csharp
// Before
options.IdleTimeout = TimeSpan.FromMinutes(15);

// After
options.IdleTimeout = TimeSpan.FromHours(8);
```

## Security Considerations

### Benefits of 8-Hour Sessions
1. **Better User Experience**: Users don't need to re-authenticate frequently
2. **Reduced Login Fatigue**: Especially important for business applications
3. **Maintained Security**: Background token refresh keeps access tokens current

### Security Measures in Place
1. **Sliding Expiration**: Cookies are refreshed on each request
2. **Background Token Refresh**: Access tokens are refreshed every 5 minutes
3. **Activity Monitoring**: Tokens are refreshed on user activity
4. **Secure Cookie Settings**: HttpOnly, SameSite, and proper security policies

## Recommendations

### For Production Environments
1. **Monitor Session Usage**: Track how long users typically stay logged in
2. **Consider Business Hours**: 8 hours covers a typical workday
3. **Review Security Policies**: Ensure the timeout aligns with your security requirements

### For Development Environments
1. **Current Settings Are Appropriate**: 8 hours provides good development experience
2. **Consider Shorter Timeouts**: For testing session expiration scenarios

### Alternative Configurations

#### 4-Hour Sessions (More Conservative)
```csharp
options.ExpireTimeSpan = TimeSpan.FromHours(4);
options.IdleTimeout = TimeSpan.FromHours(4);
```

#### 12-Hour Sessions (More Permissive)
```csharp
options.ExpireTimeSpan = TimeSpan.FromHours(12);
options.IdleTimeout = TimeSpan.FromHours(12);
```

#### 24-Hour Sessions (Very Permissive - Not Recommended for Production)
```csharp
options.ExpireTimeSpan = TimeSpan.FromHours(24);
options.IdleTimeout = TimeSpan.FromHours(24);
```

## Token Refresh Strategy

The application uses a multi-layered approach to maintain session freshness:

1. **Background Refresh**: Every 5 minutes
2. **Activity-Based Refresh**: On user interaction
3. **Visibility-Based Refresh**: When page becomes visible
4. **Error-Based Refresh**: On 401 responses

This ensures that even with 8-hour cookies, the actual access tokens remain current and secure.

## Troubleshooting

### Common Issues
1. **Session Expires Too Quickly**: Check if sliding expiration is enabled
2. **Token Refresh Fails**: Verify OIDC configuration and network connectivity
3. **Cookies Not Set**: Check SameSite and Secure policies for your environment

### Debugging
- Check browser developer tools for cookie expiration times
- Monitor network requests for token refresh calls
- Review application logs for authentication events

## Related Configuration

### OIDC Settings
- **Refresh Interval**: 5 minutes
- **Token Lifetime**: Controlled by identity server
- **Scope**: Includes `offline_access` for refresh tokens

### Frontend Token Management
- **Automatic Refresh**: Every 5 minutes
- **Activity Detection**: Mouse, keyboard, scroll events
- **Error Handling**: Automatic redirect on authentication failure 