import ApprovalTable from "@/components/approval/approval-table";
import AppLayout from "@/layouts/app-layout";
import { Head } from '@inertiajs/react';
import React from "react";

const JettyPage: React.FC = () => {
  return (
    <AppLayout>
      <Head title="Approval Management" />
      <div className="flex flex-col space-y-4 p-4">
        <ApprovalTable />
      </div>
    </AppLayout>
  );
};

export default JettyPage;