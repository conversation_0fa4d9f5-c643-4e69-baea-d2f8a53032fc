import{a as n,j as e}from"./vendor-6tJeyfYI.js";import{A as H,o as U,q as Y,r as E,s as _,D as v,v as f,B as c,w as S,x as q,I as P,y as G,z as y,L as p,S as Q,c as W,d as X,e as K,f as D}from"./app-layout-rNt37hVL.js";import{I as N}from"./input-DlXlkYlT.js";import{T as ee,a as se,b as J,c as C,d as ae,e as w}from"./table-BKSoE52x.js";import{C as T}from"./checkbox-D1loOtZt.js";import{D as te,b as ne,c as le,d as ce,e as ie}from"./dialog-BmEXyFlW.js";import{I as oe}from"./IconChevronDown-DtNUJLVx.js";import"./radix-e4nK4mWk.js";import"./App-DnhJzTNn.js";const h=Array(10).fill(null).map((x,l)=>({id:`jetty_${l+1}`,location:`Fatufia ${l%2===0?"A":"B"}`,jettyName:`F${l+1}`,boundedZone:l%3===0?"Yes":"No"}));function Ne(){const[x,l]=n.useState(""),[o,m]=n.useState(new Set),[r,k]=n.useState(new Set(Object.keys(h[0]))),[I,d]=n.useState(!1),[M,b]=n.useState(null),[A,j]=n.useState(""),[L,u]=n.useState(""),[O,g]=n.useState("No"),i=h.filter(s=>Object.values(s).some(a=>a.toString().toLowerCase().includes(x.toLowerCase()))),Z=s=>{m(s?new Set(i.map(a=>a.id)):new Set)},F=(s,a)=>{const t=new Set(o);a?t.add(s):t.delete(s),m(t)},z=(s,a)=>{const t=new Set(r);a?t.add(s):t.delete(s),k(t)},B=()=>{b(null),j(""),u(""),g("No"),d(!0)},R=s=>{b(s),j(s.location),u(s.jettyName),g(s.boundedZone),d(!0)},V=s=>{},$=()=>{d(!1)};return e.jsxs(H,{children:[e.jsx("div",{className:"container mx-auto p-4",children:e.jsxs(U,{children:[e.jsx(Y,{children:e.jsx(E,{className:"text-2xl font-bold",children:"Manage Jetty"})}),e.jsxs(_,{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx(N,{placeholder:"Filter lines...",value:x,onChange:s=>l(s.target.value),className:"max-w-sm"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs(v,{children:[e.jsx(f,{asChild:!0,children:e.jsxs(c,{variant:"outline",children:["Columns ",e.jsx(oe,{className:"ml-2 h-4 w-4"})]})}),e.jsx(S,{align:"end",children:Object.keys(h[0]).map(s=>e.jsx(q,{className:"capitalize",checked:r.has(s),onCheckedChange:a=>z(s,a===!0),children:s.replace(/([A-Z])/g," $1").trim()},s))})]}),e.jsx(c,{onClick:B,children:"Create New Jetty"})]})]}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(ee,{children:[e.jsx(se,{children:e.jsxs(J,{children:[e.jsx(C,{className:"w-[30px]",children:e.jsx(T,{checked:o.size===i.length&&i.length>0,onCheckedChange:s=>Z(s===!0)})}),Object.keys(h[0]).map(s=>r.has(s)&&s!=="id"&&e.jsx(C,{className:"capitalize",children:s.replace(/([A-Z])/g," $1").trim()},s)),e.jsx(C,{className:"text-right",children:"Actions"})]})}),e.jsx(ae,{children:i.map(s=>e.jsxs(J,{children:[e.jsx(w,{children:e.jsx(T,{checked:o.has(s.id),onCheckedChange:a=>F(s.id,a===!0)})}),Object.entries(s).map(([a,t])=>r.has(a)&&a!=="id"&&e.jsx(w,{children:t},a)),e.jsx(w,{className:"text-right",children:e.jsxs(v,{children:[e.jsx(f,{asChild:!0,children:e.jsxs(c,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(P,{className:"h-4 w-4"})]})}),e.jsxs(S,{align:"end",children:[e.jsx(G,{children:"Actions"}),e.jsx(y,{onClick:()=>R(s),children:"Update"}),e.jsx(y,{onClick:()=>V(s.id),children:"Delete"})]})]})})]},s.id))})]})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsxs("div",{className:"text-sm text-gray-500",children:[o.size," of ",i.length," row(s) selected."]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx(c,{variant:"outline",size:"sm",children:"Previous"}),e.jsx(c,{variant:"outline",size:"sm",children:"Next"})]})]})]})]})}),e.jsx(te,{open:I,onOpenChange:d,children:e.jsxs(ne,{className:"sm:max-w-[425px]",children:[e.jsx(le,{children:e.jsx(ce,{children:M?"Update Jetty":"Create New Jetty"})}),e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(p,{htmlFor:"location",className:"text-right",children:"Location"}),e.jsx(N,{id:"location",value:A,onChange:s=>j(s.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(p,{htmlFor:"jettyName",className:"text-right",children:"Jetty"}),e.jsx(N,{id:"jettyName",value:L,onChange:s=>u(s.target.value),className:"col-span-3"})]}),e.jsxs("div",{className:"grid grid-cols-4 items-center gap-4",children:[e.jsx(p,{htmlFor:"boundedZone",className:"text-right",children:"Bounded Z"}),e.jsxs(Q,{onValueChange:s=>g(s),value:O,children:[e.jsx(W,{className:"col-span-3",children:e.jsx(X,{placeholder:"Select Bounded Zone"})}),e.jsxs(K,{children:[e.jsx(D,{value:"Yes",children:"Yes"}),e.jsx(D,{value:"No",children:"No"})]})]})]})]}),e.jsx(ie,{children:e.jsx(c,{onClick:$,className:"px-6 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50",children:"Save"})})]})})]})}export{Ne as default};
//# sourceMappingURL=jetty-D4caMuDE.js.map
