import type { CreateUpdateJettyRequestDto, JettyRequestDto, RemoteServiceErrorResponse, VesselHeaderDto } from '@/client';
import { getApiIdjasJettyRequestById, putApiIdjasJettyRequestById } from '@/client/sdk.gen';
import ApplicationForm from '@/components/applications/application-form';
import { Button } from '@/components/ui/button';
import { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';
import { formatDateForInput } from '@/lib/date-helper';
import { useToast } from '@/lib/useToast';
import { HotTable } from '@handsontable/react-wrapper';
import { usePage } from '@inertiajs/react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-horizon.css';
import 'handsontable/styles/ht-theme-main.min.css';
import { useEffect, useRef, useState } from 'react';
import { columns, type TableRowData } from './handsontable-column';
import { renderDeleteButton, renderPreviewButton, renderSubmitButton } from './handsontable-renderer';

registerAllModules();

const EditApplication = () => {
  const { props } = usePage();
  const id = typeof props.id === 'string' ? props.id : undefined;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const hotTableComponent = useRef(null);

  const [docNum, setDocNum] = useState('');
  const [vesselType, setVesselType] = useState('');
  const [voyage, setVoyage] = useState('');
  const [jetty, setJetty] = useState('');
  const [arrivalDate, setArrivalDate] = useState('');
  const [departureDate, setDepartureDate] = useState('');
  const [asideDate, setAsideDate] = useState('');
  const [castOfDate, setCastOfDate] = useState('');
  const [postDate, setPostDate] = useState('');
  const [portOrigin, setPortOrigin] = useState('');
  const [destinationPort, setDestinationPort] = useState('');
  const [barge, setBarge] = useState('');
  const [tableData, setTableData] = useState<TableRowData[]>([]);
  const [vessel, setVessel] = useState<VesselHeaderDto | null>(null);

  // Dialog states for preview
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');
  const [loadingStates, setLoadingStates] = useState<Map<number, boolean>>(new Map());

  // Helper functions for preview dialog
  const handlePreview = (documentSrc: string) => {
    setPreviewDocumentSrc(documentSrc);
    setIsPreviewDialogOpen(true);
  };

  const setLoadingState = (row: number, loading: boolean) => {
    setLoadingStates(prev => {
      const newMap = new Map(prev);
      if (loading) {
        newMap.set(row, true);
      } else {
        newMap.delete(row);
      }
      return newMap;
    });
  };

  // Fetch data for edit
  const { data, isLoading: loading, error } = useQuery<JettyRequestDto, Error>({
    queryKey: ['jetty-request', id],
    enabled: !!id,
    queryFn: async () => {
      if (!id) throw new Error('No ID provided');
      const { data, error } = await getApiIdjasJettyRequestById({ path: { id } });
      if (error) throw new Error(error.error?.message || 'Failed to load application');
      return data!;
    },
    retry: 1,
    retryDelay: 1000,
  });

  // Populate state from loaded data
  useEffect(() => {
    if (data) {
      setDocNum(String(data.docNum ?? ''));
      setVesselType(data.vesselType ?? '');
      setVoyage(data.voyage ?? '');
      setJetty(data.jetty ?? '');
      setArrivalDate(formatDateForInput(data.arrivalDate));
      setDepartureDate(formatDateForInput(data.departureDate));
      setAsideDate(formatDateForInput(data.asideDate));
      setCastOfDate(formatDateForInput(data.castOfDate));
      setPostDate(data.postDate ? data.postDate.split('T')[0] : '');
      setPortOrigin(data.portOrigin ?? '');
      setDestinationPort(data.destinationPort ?? '');
      setBarge(data.barge ?? '');
      setVessel({
        id: undefined,
        docEntry: undefined,
        vesselName: data.vesselName ?? '',
        voyage: data.voyage ?? '',
        vesselArrival: data.arrivalDate ?? '',
        vesselDeparture: data.departureDate ?? '',
        vesselType: data.vesselType ?? '',
        items: undefined,
        cargo: undefined,
        barge: undefined,
        jetty: data.jetty ? { id: data.jetty, name: data.jetty } : undefined,
        portOrigin: undefined,
        destinationPort: undefined,
        berthingDate: undefined,
        anchorageDate: undefined,
        unloadingDate: undefined,
        finishUnloadingDate: undefined,
        grtWeight: undefined,
        agentName: undefined,
      } as VesselHeaderDto);
      setTableData(
        (data.items || []).map(item => ({
          tenantName: item.tenantName ?? '',
          itemName: item.itemName ?? '',
          quantity: String(item.qty ?? ''),
          uom: item.uoM ?? '',
          remark: item.notes ?? '',
          status: item.status ?? 'Draft',
          letterNo: item.letterNo ?? '',
          letterDate: item.letterDate ?? '',
          id: item.id,
          preview: '',
          submit: '',
          delete: '',
        }))
      );
    }
  }, [data]);

  const columnConfig = columns.map(col => {
    if (col.data === 'id') {
      return { ...col, renderer: renderPreviewButton(tableData, handlePreview, loadingStates, setLoadingState) };
    }
    if (col.data === 'submit') {
      return { ...col, renderer: renderSubmitButton(tableData, vesselType, loadingStates, setLoadingState, queryClient, id ?? '') };
    }
    if (col.data === 'delete') {
      return { ...col, renderer: renderDeleteButton(tableData, setTableData, loadingStates, setLoadingState, queryClient, id ?? '') };
    }
    return col;
  });

  const mutation = useMutation({
    mutationFn: async (formData: CreateUpdateJettyRequestDto) => {
      if (!id) throw new Error('No ID provided');
      const { error } = await putApiIdjasJettyRequestById({ path: { id }, body: formData });
      if (error) throw error;
    },
    onSuccess: () => {
      toast({ title: 'Success', description: 'Application updated.', variant: 'success' });
      queryClient.invalidateQueries({ queryKey: ['jetty-request', id] });
    },
    onError: (err: RemoteServiceErrorResponse | Error) => {
      toast({
        title: err instanceof Error ? err.message : err?.error?.message || 'Error',
        description: err instanceof Error ? undefined : err?.error?.details,
        variant: 'destructive',
      });
    },
  });

  const handleSave = () => {
    if (mutation.isPending) return;
    const payload: CreateUpdateJettyRequestDto = {
      docNum: Number(docNum),
      vesselType,
      vesselName: vessel?.vesselName ?? '',
      voyage,
      jetty,
      arrivalDate,
      departureDate,
      asideDate,
      castOfDate,
      postDate,
      portOrigin,
      destinationPort,
      barge,
      referenceId: data?.referenceId,
      items: tableData.map(item => ({
        tenantName: item.tenantName,
        itemName: item.itemName,
        qty: Number(item.quantity) || 0,
        uoM: item.uom,
        id: item.id,
        notes: item.remark,
        letterNo: item.letterNo,
        letterDate: item.letterDate,
        status: item.status === 'Draft' ? 0 :
               item.status === 'Open' ? 1 :
               item.status === 'Submit' ? 2 :
               item.status === 'Approve' ? 3 :
               item.status === 'Reject' ? 4 : 0,
      })),
    };
    mutation.mutate(payload);
  };

  if (loading) return <div className="p-8">Loading...</div>;
  if (error) return <div className="p-8 text-red-500">{error.message}</div>;

  return (
    <div className="container mx-auto">
      <div className='bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4'>
        <ApplicationForm
          docNum={docNum}
          vesselType={vesselType}
          vessel={vessel}
          voyage={voyage}
          jetty={jetty}
          arrivalDate={arrivalDate}
          departureDate={departureDate}
          asideDate={asideDate}
          castOfDate={castOfDate}
          postDate={postDate}
          portOrigin={portOrigin}
          destinationPort={destinationPort}
          barge={barge}
          onDocNumChange={setDocNum}
          onVesselTypeChange={setVesselType}
          onVesselChange={setVessel}
          onVoyageChange={setVoyage}
          onJettyChange={setJetty}
          onArrivalDateChange={setArrivalDate}
          onDepartureDateChange={setDepartureDate}
          onAsideDateChange={setAsideDate}
          onCastOfDateChange={setCastOfDate}
          onPostDateChange={setPostDate}
          onPortOriginChange={setPortOrigin}
          onDestinationPortChange={setDestinationPort}
          onBargeChange={setBarge}
          title="Edit Application"
        />
        <div  style={{ maxWidth: '100%', overflowX: 'auto' }} className="mb-8">
          <HotTable
            ref={hotTableComponent}
            themeName="ht-theme-main"
            data={tableData}
            columns={columnConfig}
            colHeaders={columns.map(col => col.title)}
            rowHeaders={true}
            height="50vh"
            rowHeights={27}
            currentRowClassName="currentRow"
            currentColClassName="currentCol"
            // autoWrapRow={true}
            licenseKey="non-commercial-and-evaluation"
            stretchH="all"
            contextMenu={true}
            manualColumnResize={true}
            manualRowResize={true}
            autoColumnSize={false}
            autoRowSize={false}
            startRows={1}
            viewportRowRenderingOffset={1000}
            viewportColumnRenderingOffset={100}
            dropdownMenu={true}
            filters={true}
            colWidths={80}
            width="100%"
            persistentState={true}
          />
        </div>
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={mutation.isPending}
            className="px-6 py-2 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {mutation.isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </div>
  );
};

export default EditApplication; 