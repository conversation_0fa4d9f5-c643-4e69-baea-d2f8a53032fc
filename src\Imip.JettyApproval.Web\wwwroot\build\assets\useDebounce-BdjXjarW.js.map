{"version": 3, "file": "useDebounce-BdjXjarW.js", "sources": ["../../../../../frontend/src/components/ui/useDebounce.ts"], "sourcesContent": ["import { useEffect, useState } from 'react'\r\n\r\nexport const useDebounce = <V>(value: V, delay = 800): V => {\r\n  // State and setters for debounced value\r\n  const [debouncedValue, setDebouncedValue] = useState<V>(value)\r\n  useEffect(\r\n    () => {\r\n      // Update debounced value after delay\r\n      const handler = setTimeout(() => {\r\n        setDebouncedValue(value)\r\n      }, delay)\r\n      // Cancel the timeout if value changes (also on delay change or unmount)\r\n      // This is how we prevent debounced value from updating if value is changed ...\r\n      // .. within the delay period. Timeout gets cleared and restarted.\r\n      return () => {\r\n        clearTimeout(handler)\r\n      }\r\n    },\r\n    [value, delay] // Only re-call effect if value or delay changes\r\n  )\r\n  return debouncedValue\r\n}\r\n"], "names": ["useDebounce", "value", "delay", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "useState", "useEffect", "handler"], "mappings": "yCAEO,MAAMA,EAAc,CAAIC,EAAUC,EAAQ,MAAW,CAE1D,KAAM,CAACC,EAAgBC,CAAiB,EAAIC,EAAAA,SAAYJ,CAAK,EAC7DK,OAAAA,EAAA,UACE,IAAM,CAEE,MAAAC,EAAU,WAAW,IAAM,CAC/BH,EAAkBH,CAAK,GACtBC,CAAK,EAIR,MAAO,IAAM,CACX,aAAaK,CAAO,CACtB,CACF,EACA,CAACN,EAAOC,CAAK,CACf,EACOC,CACT"}