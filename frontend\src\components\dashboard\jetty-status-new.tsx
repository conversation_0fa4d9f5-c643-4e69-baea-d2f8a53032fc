"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, LabelList, XAxis, YA<PERSON>s } from "recharts"

import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { useJettyStatus } from '@/lib/hooks/useJettyStatus'
import { Skeleton } from '@/components/ui/skeleton'

export const description = "A bar chart with jetty status data"

const chartConfig = {
  occupied: {
    label: "Occupied",
    color: "hsl(var(--chart-1))",
  },
  available: {
    label: "Available", 
    color: "hsl(var(--chart-2))",
  },
  label: {
    color: "hsl(var(--background))",
  },
} satisfies ChartConfig

export default function JettyStatus() {
  const { data: jettyData, isLoading, error } = useJettyStatus()

  // Transform API data to chart format
  const chartData = jettyData?.map(jetty => ({
    jetty: jetty.jettyName,
    occupied: jetty.occupied,
    available: jetty.available,
  })) || []

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Jetty Status</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[250px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Jetty Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[250px] text-muted-foreground">
            Error loading jetty status
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Jetty Status</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart
            accessibilityLayer
            data={chartData}
            layout="vertical"
            margin={{
              right: 16,
            }}
          >
            <CartesianGrid horizontal={false} />
            <YAxis
              dataKey="jetty"
              type="category"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
              hide
            />
            <XAxis dataKey="occupied" type="number" hide />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Bar
              dataKey="occupied"
              layout="horizontal"
              fill="var(--color-occupied)"
              radius={4}
            >
              <LabelList
                dataKey="jetty"
                position="insideLeft"
                offset={8}
                className="fill-[--color-label]"
                fontSize={12}
              />
              <LabelList
                dataKey="occupied"
                position="right"
                offset={8}
                className="fill-foreground"
                fontSize={12}
              />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Jetty utilization status <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing occupied vs available berths
        </div>
      </CardFooter>
    </Card>
  )
}
