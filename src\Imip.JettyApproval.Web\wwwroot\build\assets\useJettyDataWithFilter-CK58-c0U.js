import{f as o}from"./vendor-6tJeyfYI.js";import{m as s,t as a}from"./app-layout-rNt37hVL.js";const p=()=>o({mutationFn:async r=>{try{return(await s({body:r})).data?.items||[]}catch(t){let e="Unknown error occurred while loading Jetty data";throw typeof t=="object"&&t&&"message"in t&&typeof t.message=="string"&&(e=t.message??e),a({title:"Error loading Jetty data",description:e,variant:"destructive"}),t}},onError:r=>{}});export{p as u};
//# sourceMappingURL=useJettyDataWithFilter-CK58-c0U.js.map
