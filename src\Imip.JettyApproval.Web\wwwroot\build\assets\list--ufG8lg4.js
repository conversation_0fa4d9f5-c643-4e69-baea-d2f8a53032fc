import{j as e,u as J,$ as C,f as S}from"./vendor-6tJeyfYI.js";import{postApiEkbJetty as F,putApiEkbJettyById as M,postApiEkbJettyFilterList as V}from"./sdk.gen-BHIMY_5K.js";import{D as k}from"./data-grid-DZ2U-5jU.js";import{B as A,S as w,c as q,d as I,e as K,f as B,E as P}from"./app-layout-rNt37hVL.js";import{B as v}from"./badge-DWaCYvGm.js";import{P as T}from"./pencil-BTZ0_LzS.js";import{C as W}from"./checkbox-D1loOtZt.js";import{D as $,b as O,c as Q,d as R,e as z,g as L}from"./dialog-BmEXyFlW.js";import{a as b,D as Y}from"./FormField-AGj4WUYd.js";import{I as j}from"./input-DlXlkYlT.js";import{u as G}from"./index.esm-BubGICDC.js";const U=d=>[{accessorKey:"name",header:"Jetty Name",cell:s=>s.getValue()??"-",enableSorting:!0},{accessorKey:"alias",header:"Alias",cell:s=>s.getValue()??"-",enableSorting:!0},{accessorKey:"port",header:"Port",cell:s=>s.getValue()??"-"},{accessorKey:"max",header:"Max",cell:s=>s.getValue()??"-"},{accessorKey:"isCustomArea",header:"Is Custom Area",cell:s=>{const t=s.getValue();return e.jsx(v,{variant:t?"primary":"warning",children:t?"True":"False"})}},{id:"edit",header:"",cell:({row:s})=>{const t=()=>d(s.original),l=i=>{(i.key==="Enter"||i.key===" ")&&t()};return e.jsx(A,{onClick:t,onKeyDown:l,"aria-label":"Edit Jetty",tabIndex:0,variant:"outline",size:"icon",className:"ml-2 h-8 w-8",children:e.jsx(T,{className:"w-4 h-4","aria-hidden":"true"})})},enableSorting:!1,enableColumnFilter:!1}],_=({open:d,onClose:s,initialData:t,queryKey:l})=>{const i=J(),u=!!(t&&t.id),{register:x,handleSubmit:o,formState:{errors:r,isSubmitting:p},reset:y,setValue:h,watch:m}=G({defaultValues:{name:t?.name??"",alias:t?.alias??"",max:t?.max??0,port:t?.port??"",isCustomArea:t?.isCustomArea??!1,docEntry:t?.docEntry??0,deleted:t?.deleted??"",createdBy:t?.createdBy??0,updatedBy:t?.updatedBy??0}});C.useEffect(()=>{y({name:t?.name??"",alias:t?.alias??"",max:t?.max??0,port:t?.port??"",isCustomArea:t?.isCustomArea??!1,docEntry:t?.docEntry??0,deleted:t?.deleted??"",createdBy:t?.createdBy??0,updatedBy:t?.updatedBy??0})},[t,d,y]);const c=S({mutationFn:async a=>{const n={...a,name:a.name??"",alias:a.alias??"",max:a.max??0,port:a.port??"",isCustomArea:a.isCustomArea??!1,deleted:a.deleted??"",createdBy:a.createdBy??0,updatedBy:a.updatedBy??0,docEntry:a.docEntry??0};return F({body:n})},onSuccess:()=>{i.invalidateQueries({queryKey:l}),s()}}),g=S({mutationFn:async a=>{if(!t?.id)throw new Error("No id");const n={...a,name:a.name??"",alias:a.alias??"",max:a.max??0,port:a.port??"",isCustomArea:a.isCustomArea??!1,deleted:a.deleted??"",createdBy:a.createdBy??0,updatedBy:a.updatedBy??0,docEntry:a.docEntry??0};return M({path:{id:t.id},body:n})},onSuccess:()=>{i.invalidateQueries({queryKey:l}),s()}}),f=a=>{u?g.mutate(a):c.mutate(a)},E=m("isCustomArea");return e.jsx($,{open:d,onOpenChange:a=>{a||s()},children:e.jsxs(O,{size:"md",children:[e.jsx(Q,{children:e.jsx(R,{children:u?"Edit Jetty":"Create Jetty"})}),e.jsxs("form",{onSubmit:o(f),className:"space-y-4",children:[e.jsxs(b,{label:"Name",labelWidth:"120px",children:[e.jsx(j,{...x("name",{required:"Name is required"}),"aria-invalid":!!r.name,"aria-label":"Jetty Name",autoFocus:!0}),r.name&&e.jsx("span",{className:"text-red-500 text-xs",children:r.name.message})]}),e.jsxs(b,{label:"Alias",labelWidth:"120px",children:[e.jsx(j,{...x("alias",{required:"Alias is required"}),"aria-invalid":!!r.alias,"aria-label":"Alias"}),r.alias&&e.jsx("span",{className:"text-red-500 text-xs",children:r.alias.message})]}),e.jsxs(b,{label:"Max",labelWidth:"120px",children:[e.jsx(j,{type:"number",...x("max",{required:"Max is required",valueAsNumber:!0,validate:a=>!isNaN(a)||"Max must be a number"}),"aria-invalid":!!r.max,"aria-label":"Max"}),r.max&&e.jsx("span",{className:"text-red-500 text-xs",children:r.max.message})]}),e.jsxs(b,{label:"Port",labelWidth:"120px",children:[e.jsxs(w,{value:m("port")||"",onValueChange:a=>h("port",a,{shouldValidate:!0}),children:[e.jsx(q,{"aria-label":"Port","aria-invalid":!!r.port,children:e.jsx(I,{placeholder:"Select Port"})}),e.jsxs(K,{children:[e.jsx(B,{value:"Labota",children:"Labota"}),e.jsx(B,{value:"Fatufia",children:"Fatufia"})]})]}),r.port&&e.jsx("span",{className:"text-red-500 text-xs",children:r.port.message})]}),e.jsxs(b,{label:"Custom Area",labelWidth:"120px",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(W,{checked:!!E,onCheckedChange:a=>h("isCustomArea",!!a,{shouldValidate:!0}),"aria-label":"Is Custom Area"}),e.jsx("span",{children:"Is Custom Area"})]}),r.isCustomArea&&e.jsx("span",{className:"text-red-500 text-xs",children:r.isCustomArea.message})]}),e.jsx(Y,{className:"my-2"}),e.jsxs(z,{children:[e.jsx(L,{asChild:!0,children:e.jsx(A,{type:"button",variant:"outline",onClick:s,children:"Cancel"})}),e.jsx(A,{type:"submit",disabled:p||c.isPending||g.isPending,children:u?"Save Changes":"Create"})]})]})]})})},N=["jetty-list"],H=()=>{const[d,s]=C.useState(!1),[t,l]=C.useState(void 0),i=async({pageIndex:o,pageSize:r,sorting:p,filters:y,globalFilter:h})=>{let m;Array.isArray(p)&&p.length>0&&(m=p.map(n=>`${n.id} ${n.desc?"desc":"asc"}`).join(", "));const c=[];if(h&&c.push({fieldName:"name",operator:"Contains",value:h}),Array.isArray(y))for(const n of y)n.value&&c.push({fieldName:n.id,operator:"Contains",value:n.value});const g=c.length>0?{operator:"And",conditions:c}:void 0,f={page:o+1,maxResultCount:r,...m?{sorting:m}:{},...g?{filterGroup:g}:{}},a=(await V({body:f}))?.data;return{items:a?.items??[],totalCount:a?.totalCount??0}},u=()=>{l(void 0),s(!0)},x=o=>{l(o),s(!0)};return e.jsx("div",{className:"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2",children:e.jsx(k,{columns:U(x),title:"Manage Jetty",queryFn:i,queryKey:N,rowIdAccessor:o=>String(o.id||o.docEntry||o.name||"row-"+JSON.stringify(o)),enableRowSelection:!0,defaultPageSize:10,manualSorting:!0,manualFiltering:!0,onCreate:u,createModalContent:d?e.jsx(_,{open:d,onClose:()=>s(!1),initialData:t,queryKey:N}):null})})},le=()=>e.jsx(P,{children:e.jsx(H,{})});export{le as M};
//# sourceMappingURL=list--ufG8lg4.js.map
