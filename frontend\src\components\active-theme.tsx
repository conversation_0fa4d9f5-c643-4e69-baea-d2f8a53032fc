'use client';

import Cookies from 'js-cookie';
import {
  type ReactNode,
  createContext,
  useContext,
  useEffect,
  useState
} from 'react';

const COOKIE_NAME = 'active_theme';
const DEFAULT_THEME = 'green-scaled';

function setThemeCookie(theme: string) {
  if (typeof window === 'undefined') return;

  try {
    // Remove any existing theme cookie first
    Cookies.remove(COOKIE_NAME, { path: '/' });

    // Set the new theme cookie with all required options
    Cookies.set(COOKIE_NAME, theme, {
      path: '/',
      expires: 365,
      sameSite: 'lax',
    });

    // Verify the cookie was set
    const verifyTheme = Cookies.get(COOKIE_NAME);
    if (!verifyTheme) {
      console.warn('Failed to set theme cookie');
    }
  } catch (error) {
    console.error('Error setting theme cookie:', error);
  }
}

type ThemeContextType = {
  activeTheme: string;
  setActiveTheme: (theme: string) => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ActiveThemeProvider({
  children,
  initialTheme
}: {
  children: ReactNode;
  initialTheme?: string;
}) {
  const [activeTheme, setActiveTheme] = useState<string>(
    () => {
      if (typeof window !== 'undefined') {
        // First try to get the theme from cookie
        const cookieTheme = Cookies.get(COOKIE_NAME);
        if (cookieTheme) {
          return cookieTheme;
        }
      }
      // Fall back to initialTheme or DEFAULT_THEME
      return initialTheme || DEFAULT_THEME;
    }
  );
  useEffect(() => {
    // Always set cookie when theme changes
    setThemeCookie(activeTheme);

    // Update body classes
    const body = document.body;
    const themeClasses = Array.from(body.classList).filter((className) =>
      className.startsWith('theme-')
    );
    themeClasses.forEach((className) => body.classList.remove(className));

    body.classList.add(`theme-${activeTheme}`);
    if (activeTheme.endsWith('-scaled')) {
      body.classList.add('theme-scaled');
    }
  }, [activeTheme]);

  return (
    <ThemeContext.Provider value={{ activeTheme, setActiveTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeConfig() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error(
      'useThemeConfig must be used within an ActiveThemeProvider'
    );
  }
  return context;
}
