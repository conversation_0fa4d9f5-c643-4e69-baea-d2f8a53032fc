import { ekbProxyService } from '@/services/ekbProxyService';
import { toast } from '@/lib/useToast';
import type { FilterGroup, PagedResultDtoOfJettyDto, QueryParametersDto } from '@/clientEkb/types.gen';
import { useQuery } from '@tanstack/react-query';

export const useJettyData = (
  pageIndex: number,
  pageSize: number,
  filterGroup?: FilterGroup,
  sorting?: string
) => {
  return useQuery<PagedResultDtoOfJettyDto, Error>({
    queryKey: ['manage-jetty', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],
    queryFn: async (): Promise<PagedResultDtoOfJettyDto> => {
      const payload: QueryParametersDto = {
        page: pageIndex + 1,
        maxResultCount: pageSize,
        sorting,
        filterGroup,
      };

      try {
        const response = await ekbProxyService.filterJetties(payload);
        return response?.data ?? { items: [], totalCount: 0 };
      } catch (error: unknown) {
        let message = 'Unknown error occurred while loading Jetty Requests';
        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {
          message = (error as { message?: string }).message ?? message;
        }

        console.error('JettyRequests API Error:', error);
        toast({
          title: 'Error loading Jetty Requests',
          description: message,
          variant: 'destructive',
        });

        // Return empty result instead of throwing
        return { items: [], totalCount: 0 };
      }
    },
    retry: 1, // Only retry once
    retryDelay: 1000, // Wait 1 second before retry
  });
}; 