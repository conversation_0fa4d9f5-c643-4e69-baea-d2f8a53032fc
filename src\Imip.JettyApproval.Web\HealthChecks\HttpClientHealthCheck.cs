using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.HealthChecks;

/// <summary>
/// Health check for monitoring HttpClient resilience and connectivity
/// </summary>
public class HttpClientHealthCheck : IHealthCheck
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<HttpClientHealthCheck> _logger;
    private readonly Dictionary<string, string> _endpointsToCheck;

    public HttpClientHealthCheck(
        IHttpClientFactory httpClientFactory,
        ILogger<HttpClientHealthCheck> logger)
    {
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _endpointsToCheck = new Dictionary<string, string>
        {
            { "IdentityServer", "/health" },
            { "ExternalAuth", "/health" }
        };
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<string, object>();
        var overallHealthy = true;
        var issues = new List<string>();

        foreach (var endpoint in _endpointsToCheck)
        {
            try
            {
                var client = _httpClientFactory.CreateClient(endpoint.Key);
                var healthCheckUrl = endpoint.Value;

                // Quick health check with short timeout
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(TimeSpan.FromSeconds(5));

                var response = await client.GetAsync(healthCheckUrl, cts.Token);

                var isHealthy = response.IsSuccessStatusCode;
                results[endpoint.Key] = new
                {
                    Status = isHealthy ? "Healthy" : "Unhealthy",
                    StatusCode = (int)response.StatusCode,
                    ResponseTime = DateTime.UtcNow.ToString("O")
                };

                if (!isHealthy)
                {
                    overallHealthy = false;
                    issues.Add($"{endpoint.Key}: {response.StatusCode}");
                }

                _logger.LogDebug("Health check for {ClientName}: {Status} ({StatusCode})",
                    endpoint.Key, isHealthy ? "Healthy" : "Unhealthy", response.StatusCode);
            }
            catch (OperationCanceledException)
            {
                overallHealthy = false;
                issues.Add($"{endpoint.Key}: Timeout");
                results[endpoint.Key] = new
                {
                    Status = "Timeout",
                    Error = "Request timed out",
                    ResponseTime = DateTime.UtcNow.ToString("O")
                };
                _logger.LogWarning("Health check timeout for {ClientName}", endpoint.Key);
            }
            catch (Exception ex)
            {
                overallHealthy = false;
                issues.Add($"{endpoint.Key}: {ex.GetType().Name}");
                results[endpoint.Key] = new
                {
                    Status = "Error",
                    Error = ex.Message,
                    ResponseTime = DateTime.UtcNow.ToString("O")
                };
                _logger.LogError(ex, "Health check error for {ClientName}", endpoint.Key);
            }
        }

        var status = overallHealthy ? HealthStatus.Healthy : HealthStatus.Degraded;
        var description = overallHealthy
            ? "All HttpClient endpoints are healthy"
            : $"Issues detected: {string.Join(", ", issues)}";

        return new HealthCheckResult(status, description, data: results);
    }
}

/// <summary>
/// Health check specifically for resilience patterns monitoring
/// </summary>
public class ResilienceHealthCheck : IHealthCheck
{
    private readonly ILogger<ResilienceHealthCheck> _logger;
    private static readonly Dictionary<string, ResilienceMetrics> _metrics = new();

    public ResilienceHealthCheck(ILogger<ResilienceHealthCheck> logger)
    {
        _logger = logger;
    }

    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var results = new Dictionary<string, object>();
        var overallHealthy = true;
        var warnings = new List<string>();

        foreach (var metric in _metrics)
        {
            var clientName = metric.Key;
            var stats = metric.Value;

            // Calculate health based on recent failure rates
            var recentFailureRate = stats.CalculateRecentFailureRate();
            var circuitBreakerOpen = stats.IsCircuitBreakerOpen;

            var isHealthy = recentFailureRate < 0.5 && !circuitBreakerOpen;

            results[clientName] = new
            {
                Status = isHealthy ? "Healthy" : "Degraded",
                FailureRate = Math.Round(recentFailureRate * 100, 2),
                CircuitBreakerOpen = circuitBreakerOpen,
                TotalRequests = stats.TotalRequests,
                FailedRequests = stats.FailedRequests,
                RetryAttempts = stats.RetryAttempts,
                LastUpdated = stats.LastUpdated.ToString("O")
            };

            if (!isHealthy)
            {
                overallHealthy = false;
                if (circuitBreakerOpen)
                {
                    warnings.Add($"{clientName}: Circuit breaker open");
                }
                if (recentFailureRate >= 0.5)
                {
                    warnings.Add($"{clientName}: High failure rate ({recentFailureRate:P})");
                }
            }
        }

        var status = overallHealthy ? HealthStatus.Healthy : HealthStatus.Degraded;
        var description = overallHealthy
            ? "All resilience patterns are operating normally"
            : $"Resilience issues: {string.Join(", ", warnings)}";

        return Task.FromResult(new HealthCheckResult(status, description, data: results));
    }

    public static void RecordRequest(string clientName, bool success, int retryCount = 0)
    {
        if (!_metrics.ContainsKey(clientName))
        {
            _metrics[clientName] = new ResilienceMetrics();
        }

        _metrics[clientName].RecordRequest(success, retryCount);
    }

    public static void RecordCircuitBreakerState(string clientName, bool isOpen)
    {
        if (!_metrics.ContainsKey(clientName))
        {
            _metrics[clientName] = new ResilienceMetrics();
        }

        _metrics[clientName].IsCircuitBreakerOpen = isOpen;
    }
}

public class ResilienceMetrics
{
    private readonly Queue<RequestRecord> _recentRequests = new();
    private readonly object _lock = new();

    public int TotalRequests { get; private set; }
    public int FailedRequests { get; private set; }
    public int RetryAttempts { get; private set; }
    public bool IsCircuitBreakerOpen { get; set; }
    public DateTime LastUpdated { get; private set; } = DateTime.UtcNow;

    public void RecordRequest(bool success, int retryCount = 0)
    {
        lock (_lock)
        {
            TotalRequests++;
            if (!success) FailedRequests++;
            RetryAttempts += retryCount;
            LastUpdated = DateTime.UtcNow;

            // Keep only last 100 requests for recent failure rate calculation
            _recentRequests.Enqueue(new RequestRecord(success, DateTime.UtcNow));
            while (_recentRequests.Count > 100)
            {
                _recentRequests.Dequeue();
            }
        }
    }

    public double CalculateRecentFailureRate()
    {
        lock (_lock)
        {
            if (_recentRequests.Count == 0) return 0;

            var cutoff = DateTime.UtcNow.AddMinutes(-5); // Last 5 minutes
            var recentRequests = _recentRequests.Where(r => r.Timestamp > cutoff).ToList();

            if (recentRequests.Count == 0) return 0;

            return (double)recentRequests.Count(r => !r.Success) / recentRequests.Count;
        }
    }

    private record RequestRecord(bool Success, DateTime Timestamp);
}
