# EKB Proxy Implementation Guide

## Overview

This implementation provides a comprehensive proxy/forwarding mechanism for internal applications that share the same bearer token authentication. The solution includes backend proxy services with resilience patterns and frontend integration.

## Architecture

### Backend Components

1. **Enhanced AppToAppService** (`src/Imip.JettyApproval.Web/Services/AppToAppService.cs`)
   - Domain mapping to internal IP (**********)
   - Support for all HTTP methods (GET, POST, PUT, PATCH, DELETE)
   - File upload handling with multipart/form-data
   - Bearer token authentication forwarding
   - Integration with resilience patterns

2. **EkbProxyController** (`src/Imip.JettyApproval.Web/Controllers/EkbProxyController.cs`)
   - RESTful proxy endpoints for all HTTP methods
   - Specialized file upload endpoint
   - Automatic header forwarding
   - Error handling and logging

### Frontend Components

1. **EkbProxyService** (`frontend/src/services/ekbProxyService.ts`)
   - Clean TypeScript interface for proxy requests
   - File upload support
   - Error handling and response parsing

2. **React Hooks** (`frontend/src/hooks/useEkbProxy.ts`)
   - React Query integration
   - Caching and loading states
   - Toast notifications
   - Query invalidation

## Key Features

### ✅ Domain Mapping
- Automatically resolves external domains to internal IP **********
- Configurable domain mappings in AppToAppService
- Supports multiple domain patterns

### ✅ HTTP Method Support
- **GET**: Data retrieval with query parameters
- **POST**: Resource creation with JSON/form data
- **PUT**: Full resource updates
- **PATCH**: Partial resource updates
- **DELETE**: Resource deletion
- **File Upload**: Multipart/form-data handling

### ✅ Authentication
- Bearer token forwarding from current user session
- Automatic token refresh using silent refresh service
- Retry on authentication failures

### ✅ Resilience Integration
- Leverages existing resilience patterns (retry, circuit breaker, timeouts)
- Configured through HttpClient with Microsoft.Extensions.Http.Resilience
- Service-specific timeout and retry configurations

### ✅ File Upload Support
- Handles multipart/form-data content
- Preserves file metadata and content
- Supports multiple file uploads
- Additional form field support

## Configuration

### Backend Configuration (`appsettings.json`)

```json
{
  "ExternalApps": {
    "EKB": {
      "BaseUrl": "https://ekb-dev.imip.co.id",
      "InternalIp": "**********",
      "TimeoutSeconds": 30
    }
  }
}
```

### Domain Mapping (AppToAppService)

```csharp
_domainMapping = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
{
    { "ekb-dev.imip.co.id", "**********" },
    { "ekb.imip.co.id", "**********" },
    { "api-identity-dev.imip.co.id", "**********" },
    { "identity.imip.co.id", "**********" }
};
```

## API Endpoints

### Proxy Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/ekb-proxy/{**path}` | Proxy GET requests |
| POST | `/api/ekb-proxy/{**path}` | Proxy POST requests |
| PUT | `/api/ekb-proxy/{**path}` | Proxy PUT requests |
| PATCH | `/api/ekb-proxy/{**path}` | Proxy PATCH requests |
| DELETE | `/api/ekb-proxy/{**path}` | Proxy DELETE requests |
| POST | `/api/ekb-proxy/upload/{**path}` | File upload proxy |

### Example Usage

```typescript
// Using the service directly
import { ekbProxyService } from '@/services/ekbProxyService';

// GET request
const vessels = await ekbProxyService.get('/api/vessels');

// POST request
const newVessel = await ekbProxyService.post('/api/vessels', vesselData);

// File upload
const files = [file1, file2];
const result = await ekbProxyService.uploadFile('/api/vessels/123/documents', files);

// Using React hooks
import { useVessels, useCreateVessel } from '@/hooks/useEkbProxy';

function VesselComponent() {
  const { data: vessels, isLoading } = useVessels();
  const createVessel = useCreateVessel();
  
  const handleCreate = (data) => {
    createVessel.mutate(data);
  };
}
```

## Benefits of Proxy Approach vs Direct Frontend Calls

### ✅ Proxy Approach (Recommended)
- **Security**: Centralized authentication and token management
- **Resilience**: Built-in retry, circuit breaker, and timeout patterns
- **Monitoring**: Centralized logging and error handling
- **Domain Mapping**: Automatic resolution to internal IPs
- **Consistency**: Unified error handling and response format
- **Caching**: Server-side caching opportunities
- **Rate Limiting**: Centralized rate limiting and throttling

### ❌ Direct Frontend Approach
- **Security Risk**: Tokens exposed in browser
- **No Resilience**: Manual implementation of retry logic
- **CORS Issues**: Cross-origin request complications
- **Inconsistent**: Different error handling per endpoint
- **Performance**: No server-side optimizations

## Migration Strategy

### For Existing Direct Frontend Calls

1. **Identify Current EKB Calls**: Review existing `clientEkb` usage in frontend
2. **Replace with Proxy Service**: Update components to use `ekbProxyService`
3. **Update React Queries**: Replace direct API calls with proxy hooks
4. **Test Authentication**: Verify token forwarding works correctly
5. **Monitor Performance**: Check proxy overhead vs direct calls

### Example Migration

```typescript
// Before (Direct)
import { clientEkb } from '@/client';
const response = await clientEkb.GET('/api/vessels');

// After (Proxy)
import { ekbProxyService } from '@/services/ekbProxyService';
const response = await ekbProxyService.get('/api/vessels');

// Or with React Query
import { useVessels } from '@/hooks/useEkbProxy';
const { data: vessels } = useVessels();
```

## Testing

### Backend Testing
```bash
# Test proxy endpoints
curl -X GET "http://localhost:5000/api/ekb-proxy/api/vessels" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test file upload
curl -X POST "http://localhost:5000/api/ekb-proxy/upload/api/vessels/123/documents" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf"
```

### Frontend Testing
```typescript
// Test in browser console
import { ekbProxyService } from '@/services/ekbProxyService';
const result = await ekbProxyService.get('/api/vessels');
console.log(result);
```

## Monitoring and Logging

- All proxy requests are logged with request/response details
- Resilience events (retries, circuit breaker state changes) are logged
- Authentication failures are tracked
- Performance metrics available through existing logging infrastructure

## Next Steps

1. **Implement the proxy endpoints** in your backend
2. **Test authentication flow** with existing tokens
3. **Migrate critical frontend calls** to use proxy service
4. **Monitor performance** and adjust resilience settings
5. **Gradually migrate remaining calls** from direct to proxy approach
