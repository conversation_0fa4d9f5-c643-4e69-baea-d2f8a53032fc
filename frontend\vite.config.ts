import { mkdirSync } from "fs"
import path from "path"
import { defineConfig } from "vite"
import laravel from "laravel-vite-plugin"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react"
import { compression } from 'vite-plugin-compression2'

const outDir = '../src/Imip.JettyApproval.Web/wwwroot/build'
mkdirSync(outDir, { recursive: true })

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    // React plugin with minimal configuration to avoid conflicts
    react(),
    laravel({
      input: ['src/App.tsx'],
      publicDirectory: outDir,
      refresh: true,
    }),
    tailwindcss(),
    // Add compression plugins for production
    ...(process.env.NODE_ENV === 'production' ? [
      // Gzip compression
      compression({
        algorithms: ['gzip'],
        exclude: [/\.(br)$/, /\.(gz)$/],
        threshold: 1024, // Only compress files larger than 1KB
        deleteOriginalAssets: false, // Keep original files
      }),
      // Brotli compression (better than gzip)
      compression({
        algorithms: ['brotliCompress'],
        exclude: [/\.(br)$/, /\.(gz)$/],
        threshold: 1024,
        deleteOriginalAssets: false,
      }),
    ] : []),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir,
    emptyOutDir: true,
    // Target modern browsers for better optimization
    target: 'es2020',
    // Increase chunk size warning limit (default is 500kb)
    chunkSizeWarningLimit: 1000,
    // Add rollup options to optimize chunks
    rollupOptions: {
      output: {
        manualChunks: {
          'vendor': [
            'react',
            'react-dom',
            '@tanstack/react-query'
          ],
          'radix': [
            '@radix-ui/react-accordion',
            '@radix-ui/react-alert-dialog',
            '@radix-ui/react-avatar',
            '@radix-ui/react-checkbox',
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-label',
            '@radix-ui/react-popover',
            '@radix-ui/react-select',
            '@radix-ui/react-tabs'
          ],
          'dnd': [
            '@atlaskit/pragmatic-drag-and-drop',
            '@atlaskit/pragmatic-drag-and-drop-flourish',
            '@atlaskit/pragmatic-drag-and-drop-hitbox',
            '@dnd-kit/core',
            '@dnd-kit/modifiers',
            '@dnd-kit/sortable'
          ]
        }
      }
    },
    // Optimize chunk size
    minify: 'esbuild',
    sourcemap: true,
    // Enable CSS code splitting
    cssCodeSplit: true,
    // Optimize asset inlining threshold
    assetsInlineLimit: 4096,
    // Additional compression settings
    reportCompressedSize: true, // Report compressed sizes
  },
  // Optimize dependencies for React 19
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@tanstack/react-query',
      // Radix UI components
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-label',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-tabs',
      // DnD related
      '@atlaskit/pragmatic-drag-and-drop',
      '@atlaskit/pragmatic-drag-and-drop-flourish',
      '@atlaskit/pragmatic-drag-and-drop-hitbox',
      '@dnd-kit/core',
      '@dnd-kit/modifiers',
      '@dnd-kit/sortable'
    ],
    exclude: ['@vitejs/plugin-react']
  },
  // Production-specific optimizations
  esbuild: {
    // Remove console logs in production
    drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
  },
})