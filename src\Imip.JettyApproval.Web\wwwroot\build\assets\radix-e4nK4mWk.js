import{r as Pa,g as Aa,a as i,j as m,$ as Te,R as Ao,b as Ma}from"./vendor-6tJeyfYI.js";var nt=Pa();const _a=Aa(nt);function eo(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Mt(...e){return t=>{let n=!1;const o=e.map(r=>{const s=eo(r,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let r=0;r<o.length;r++){const s=o[r];typeof s=="function"?s():eo(e[r],null)}}}}function F(...e){return i.useCallback(Mt(...e),e)}function Ee(e){const t=Ia(e),n=i.forwardRef((o,r)=>{const{children:s,...a}=o,c=i.Children.toArray(s),l=c.find(Ta);if(l){const u=l.props.children,f=c.map(d=>d===l?i.Children.count(u)>1?i.Children.only(null):i.isValidElement(u)?u.props.children:null:d);return m.jsx(t,{...a,ref:r,children:i.isValidElement(u)?i.cloneElement(u,void 0,f):null})}return m.jsx(t,{...a,ref:r,children:s})});return n.displayName=`${e}.Slot`,n}var Ad=Ee("Slot");function Ia(e){const t=i.forwardRef((n,o)=>{const{children:r,...s}=n;if(i.isValidElement(r)){const a=Na(r),c=Oa(s,r.props);return r.type!==i.Fragment&&(c.ref=o?Mt(o,a):a),i.cloneElement(r,c)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Mo=Symbol("radix.slottable");function Md(e){const t=({children:n})=>m.jsx(m.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Mo,t}function Ta(e){return i.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Mo}function Oa(e,t){const n={...t};for(const o in t){const r=e[o],s=t[o];/^on[A-Z]/.test(o)?r&&s?n[o]=(...c)=>{const l=s(...c);return r(...c),l}:r&&(n[o]=r):o==="style"?n[o]={...r,...s}:o==="className"&&(n[o]=[r,s].filter(Boolean).join(" "))}return{...e,...n}}function Na(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Da=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],T=Da.reduce((e,t)=>{const n=Ee(`Primitive.${t}`),o=i.forwardRef((r,s)=>{const{asChild:a,...c}=r,l=a?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(l,{...c,ref:s})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function _o(e,t){e&&nt.flushSync(()=>e.dispatchEvent(t))}var Io=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ka="VisuallyHidden",To=i.forwardRef((e,t)=>m.jsx(T.span,{...e,ref:t,style:{...Io,...e.style}}));To.displayName=ka;var _d=To;function La(e,t){const n=i.createContext(t),o=s=>{const{children:a,...c}=s,l=i.useMemo(()=>c,Object.values(c));return m.jsx(n.Provider,{value:l,children:a})};o.displayName=e+"Provider";function r(s){const a=i.useContext(n);if(a)return a;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[o,r]}function se(e,t=[]){let n=[];function o(s,a){const c=i.createContext(a),l=n.length;n=[...n,a];const u=d=>{const{scope:v,children:h,...w}=d,p=v?.[e]?.[l]||c,g=i.useMemo(()=>w,Object.values(w));return m.jsx(p.Provider,{value:g,children:h})};u.displayName=s+"Provider";function f(d,v){const h=v?.[e]?.[l]||c,w=i.useContext(h);if(w)return w;if(a!==void 0)return a;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[u,f]}const r=()=>{const s=n.map(a=>i.createContext(a));return function(c){const l=c?.[e]||s;return i.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return r.scopeName=e,[o,ja(r,...t)]}function ja(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const o=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(s){const a=o.reduce((c,{useScope:l,scopeName:u})=>{const d=l(s)[`__scope${u}`];return{...c,...d}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}function Sn(e){const t=e+"CollectionProvider",[n,o]=se(t),[r,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=p=>{const{scope:g,children:y}=p,x=Te.useRef(null),C=Te.useRef(new Map).current;return m.jsx(r,{scope:g,itemMap:C,collectionRef:x,children:y})};a.displayName=t;const c=e+"CollectionSlot",l=Ee(c),u=Te.forwardRef((p,g)=>{const{scope:y,children:x}=p,C=s(c,y),b=F(g,C.collectionRef);return m.jsx(l,{ref:b,children:x})});u.displayName=c;const f=e+"CollectionItemSlot",d="data-radix-collection-item",v=Ee(f),h=Te.forwardRef((p,g)=>{const{scope:y,children:x,...C}=p,b=Te.useRef(null),S=F(g,b),_=s(f,y);return Te.useEffect(()=>(_.itemMap.set(b,{ref:b,...C}),()=>void _.itemMap.delete(b))),m.jsx(v,{[d]:"",ref:S,children:x})});h.displayName=f;function w(p){const g=s(e+"CollectionConsumer",p);return Te.useCallback(()=>{const x=g.collectionRef.current;if(!x)return[];const C=Array.from(x.querySelectorAll(`[${d}]`));return Array.from(g.itemMap.values()).sort((_,E)=>C.indexOf(_.ref.current)-C.indexOf(E.ref.current))},[g.collectionRef,g.itemMap])}return[{Provider:a,Slot:u,ItemSlot:h},w,o]}function R(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),n===!1||!r.defaultPrevented)return t?.(r)}}var z=globalThis?.document?i.useLayoutEffect:()=>{},Fa=Ao[" useInsertionEffect ".trim().toString()]||z;function xe({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){const[r,s,a]=$a({defaultProp:t,onChange:n}),c=e!==void 0,l=c?e:r;{const f=i.useRef(e!==void 0);i.useEffect(()=>{const d=f.current;if(d!==c){const v=d?"controlled":"uncontrolled",h=c?"controlled":"uncontrolled"}f.current=c},[c,o])}const u=i.useCallback(f=>{if(c){const d=Ba(f)?f(e):f;d!==e&&a.current?.(d)}else s(f)},[c,e,s,a]);return[l,u]}function $a({defaultProp:e,onChange:t}){const[n,o]=i.useState(e),r=i.useRef(n),s=i.useRef(t);return Fa(()=>{s.current=t},[t]),i.useEffect(()=>{r.current!==n&&(s.current?.(n),r.current=n)},[n,r]),[n,o,s]}function Ba(e){return typeof e=="function"}function Wa(e,t){return i.useReducer((n,o)=>t[n][o]??n,e)}var ee=e=>{const{present:t,children:n}=e,o=Va(t),r=typeof n=="function"?n({present:o.isPresent}):i.Children.only(n),s=F(o.ref,Ha(r));return typeof n=="function"||o.isPresent?i.cloneElement(r,{ref:s}):null};ee.displayName="Presence";function Va(e){const[t,n]=i.useState(),o=i.useRef(null),r=i.useRef(e),s=i.useRef("none"),a=e?"mounted":"unmounted",[c,l]=Wa(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return i.useEffect(()=>{const u=dt(o.current);s.current=c==="mounted"?u:"none"},[c]),z(()=>{const u=o.current,f=r.current;if(f!==e){const v=s.current,h=dt(u);e?l("MOUNT"):h==="none"||u?.display==="none"?l("UNMOUNT"):l(f&&v!==h?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,l]),z(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=h=>{const p=dt(o.current).includes(h.animationName);if(h.target===t&&p&&(l("ANIMATION_END"),!r.current)){const g=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=g)})}},v=h=>{h.target===t&&(s.current=dt(o.current))};return t.addEventListener("animationstart",v),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",v),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(u=>{o.current=u?getComputedStyle(u):null,n(u)},[])}}function dt(e){return e?.animationName||"none"}function Ha(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ua=Ao[" useId ".trim().toString()]||(()=>{}),Ka=0;function oe(e){const[t,n]=i.useState(Ua());return z(()=>{n(o=>o??String(Ka++))},[e]),e||(t?`radix-${t}`:"")}var _t="Collapsible",[Ga,Id]=se(_t),[za,En]=Ga(_t),Oo=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,open:o,defaultOpen:r,disabled:s,onOpenChange:a,...c}=e,[l,u]=xe({prop:o,defaultProp:r??!1,onChange:a,caller:_t});return m.jsx(za,{scope:n,disabled:s,contentId:oe(),open:l,onOpenToggle:i.useCallback(()=>u(f=>!f),[u]),children:m.jsx(T.div,{"data-state":Pn(l),"data-disabled":s?"":void 0,...c,ref:t})})});Oo.displayName=_t;var No="CollapsibleTrigger",Ya=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,...o}=e,r=En(No,n);return m.jsx(T.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":Pn(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...o,ref:t,onClick:R(e.onClick,r.onOpenToggle)})});Ya.displayName=No;var Rn="CollapsibleContent",Xa=i.forwardRef((e,t)=>{const{forceMount:n,...o}=e,r=En(Rn,e.__scopeCollapsible);return m.jsx(ee,{present:n||r.open,children:({present:s})=>m.jsx(qa,{...o,ref:t,present:s})})});Xa.displayName=Rn;var qa=i.forwardRef((e,t)=>{const{__scopeCollapsible:n,present:o,children:r,...s}=e,a=En(Rn,n),[c,l]=i.useState(o),u=i.useRef(null),f=F(t,u),d=i.useRef(0),v=d.current,h=i.useRef(0),w=h.current,p=a.open||c,g=i.useRef(p),y=i.useRef(void 0);return i.useEffect(()=>{const x=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(x)},[]),z(()=>{const x=u.current;if(x){y.current=y.current||{transitionDuration:x.style.transitionDuration,animationName:x.style.animationName},x.style.transitionDuration="0s",x.style.animationName="none";const C=x.getBoundingClientRect();d.current=C.height,h.current=C.width,g.current||(x.style.transitionDuration=y.current.transitionDuration,x.style.animationName=y.current.animationName),l(o)}},[a.open,o]),m.jsx(T.div,{"data-state":Pn(a.open),"data-disabled":a.disabled?"":void 0,id:a.contentId,hidden:!p,...s,ref:f,style:{"--radix-collapsible-content-height":v?`${v}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:p&&r})});function Pn(e){return e?"open":"closed"}var Td=Oo,Za=i.createContext(void 0);function It(e){const t=i.useContext(Za);return e||t||"ltr"}function ce(e){const t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...n)=>t.current?.(...n),[])}function Qa(e,t=globalThis?.document){const n=ce(e);i.useEffect(()=>{const o=r=>{r.key==="Escape"&&n(r)};return t.addEventListener("keydown",o,{capture:!0}),()=>t.removeEventListener("keydown",o,{capture:!0})},[n,t])}var Ja="DismissableLayer",dn="dismissableLayer.update",ei="dismissableLayer.pointerDownOutside",ti="dismissableLayer.focusOutside",to,Do=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ke=i.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:r,onFocusOutside:s,onInteractOutside:a,onDismiss:c,...l}=e,u=i.useContext(Do),[f,d]=i.useState(null),v=f?.ownerDocument??globalThis?.document,[,h]=i.useState({}),w=F(t,E=>d(E)),p=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),y=p.indexOf(g),x=f?p.indexOf(f):-1,C=u.layersWithOutsidePointerEventsDisabled.size>0,b=x>=y,S=oi(E=>{const M=E.target,D=[...u.branches].some(N=>N.contains(M));!b||D||(r?.(E),a?.(E),E.defaultPrevented||c?.())},v),_=ri(E=>{const M=E.target;[...u.branches].some(N=>N.contains(M))||(s?.(E),a?.(E),E.defaultPrevented||c?.())},v);return Qa(E=>{x===u.layers.size-1&&(o?.(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},v),i.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(to=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),no(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(v.body.style.pointerEvents=to)}},[f,v,n,u]),i.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),no())},[f,u]),i.useEffect(()=>{const E=()=>h({});return document.addEventListener(dn,E),()=>document.removeEventListener(dn,E)},[]),m.jsx(T.div,{...l,ref:w,style:{pointerEvents:C?b?"auto":"none":void 0,...e.style},onFocusCapture:R(e.onFocusCapture,_.onFocusCapture),onBlurCapture:R(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:R(e.onPointerDownCapture,S.onPointerDownCapture)})});Ke.displayName=Ja;var ni="DismissableLayerBranch",ko=i.forwardRef((e,t)=>{const n=i.useContext(Do),o=i.useRef(null),r=F(t,o);return i.useEffect(()=>{const s=o.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),m.jsx(T.div,{...e,ref:r})});ko.displayName=ni;function oi(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1),r=i.useRef(()=>{});return i.useEffect(()=>{const s=c=>{if(c.target&&!o.current){let l=function(){Lo(ei,n,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",r.current),r.current=l,t.addEventListener("click",r.current,{once:!0})):l()}else t.removeEventListener("click",r.current);o.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",r.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}function ri(e,t=globalThis?.document){const n=ce(e),o=i.useRef(!1);return i.useEffect(()=>{const r=s=>{s.target&&!o.current&&Lo(ti,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",r),()=>t.removeEventListener("focusin",r)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}function no(){const e=new CustomEvent(dn);document.dispatchEvent(e)}function Lo(e,t,n,{discrete:o}){const r=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&r.addEventListener(e,t,{once:!0}),o?_o(r,s):r.dispatchEvent(s)}var Od=Ke,Nd=ko,Qt="focusScope.autoFocusOnMount",Jt="focusScope.autoFocusOnUnmount",oo={bubbles:!1,cancelable:!0},si="FocusScope",ot=i.forwardRef((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:r,onUnmountAutoFocus:s,...a}=e,[c,l]=i.useState(null),u=ce(r),f=ce(s),d=i.useRef(null),v=F(t,p=>l(p)),h=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(o){let p=function(C){if(h.paused||!c)return;const b=C.target;c.contains(b)?d.current=b:be(d.current,{select:!0})},g=function(C){if(h.paused||!c)return;const b=C.relatedTarget;b!==null&&(c.contains(b)||be(d.current,{select:!0}))},y=function(C){if(document.activeElement===document.body)for(const S of C)S.removedNodes.length>0&&be(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",g);const x=new MutationObserver(y);return c&&x.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",g),x.disconnect()}}},[o,c,h.paused]),i.useEffect(()=>{if(c){so.add(h);const p=document.activeElement;if(!c.contains(p)){const y=new CustomEvent(Qt,oo);c.addEventListener(Qt,u),c.dispatchEvent(y),y.defaultPrevented||(ai(di(jo(c)),{select:!0}),document.activeElement===p&&be(c))}return()=>{c.removeEventListener(Qt,u),setTimeout(()=>{const y=new CustomEvent(Jt,oo);c.addEventListener(Jt,f),c.dispatchEvent(y),y.defaultPrevented||be(p??document.body,{select:!0}),c.removeEventListener(Jt,f),so.remove(h)},0)}}},[c,u,f,h]);const w=i.useCallback(p=>{if(!n&&!o||h.paused)return;const g=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,y=document.activeElement;if(g&&y){const x=p.currentTarget,[C,b]=ii(x);C&&b?!p.shiftKey&&y===b?(p.preventDefault(),n&&be(C,{select:!0})):p.shiftKey&&y===C&&(p.preventDefault(),n&&be(b,{select:!0})):y===x&&p.preventDefault()}},[n,o,h.paused]);return m.jsx(T.div,{tabIndex:-1,...a,ref:v,onKeyDown:w})});ot.displayName=si;function ai(e,{select:t=!1}={}){const n=document.activeElement;for(const o of e)if(be(o,{select:t}),document.activeElement!==n)return}function ii(e){const t=jo(e),n=ro(t,e),o=ro(t.reverse(),e);return[n,o]}function jo(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>{const r=o.tagName==="INPUT"&&o.type==="hidden";return o.disabled||o.hidden||r?NodeFilter.FILTER_SKIP:o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ro(e,t){for(const n of e)if(!ci(n,{upTo:t}))return n}function ci(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function li(e){return e instanceof HTMLInputElement&&"select"in e}function be(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&li(e)&&t&&e.select()}}var so=ui();function ui(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=ao(e,t),e.unshift(t)},remove(t){e=ao(e,t),e[0]?.resume()}}}function ao(e,t){const n=[...e],o=n.indexOf(t);return o!==-1&&n.splice(o,1),n}function di(e){return e.filter(t=>t.tagName!=="A")}var fi="Portal",rt=i.forwardRef((e,t)=>{const{container:n,...o}=e,[r,s]=i.useState(!1);z(()=>s(!0),[]);const a=n||r&&globalThis?.document?.body;return a?_a.createPortal(m.jsx(T.div,{...o,ref:t}),a):null});rt.displayName=fi;var en=0;function Tt(){i.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??io()),document.body.insertAdjacentElement("beforeend",e[1]??io()),en++,()=>{en===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),en--}},[])}function io(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var fe=function(){return fe=Object.assign||function(t){for(var n,o=1,r=arguments.length;o<r;o++){n=arguments[o];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},fe.apply(this,arguments)};function Fo(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}function pi(e,t,n){if(n||arguments.length===2)for(var o=0,r=t.length,s;o<r;o++)(s||!(o in t))&&(s||(s=Array.prototype.slice.call(t,0,o)),s[o]=t[o]);return e.concat(s||Array.prototype.slice.call(t))}var gt="right-scroll-bar-position",wt="width-before-scroll-bar",mi="with-scroll-bars-hidden",vi="--removed-body-scroll-bar-size";function tn(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function hi(e,t){var n=i.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(o){var r=n.value;r!==o&&(n.value=o,n.callback(o,r))}}}})[0];return n.callback=t,n.facade}var gi=typeof window<"u"?i.useLayoutEffect:i.useEffect,co=new WeakMap;function wi(e,t){var n=hi(null,function(o){return e.forEach(function(r){return tn(r,o)})});return gi(function(){var o=co.get(n);if(o){var r=new Set(o),s=new Set(e),a=n.current;r.forEach(function(c){s.has(c)||tn(c,null)}),s.forEach(function(c){r.has(c)||tn(c,a)})}co.set(n,e)},[e]),n}function xi(e){return e}function yi(e,t){t===void 0&&(t=xi);var n=[],o=!1,r={read:function(){if(o)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,o);return n.push(a),function(){n=n.filter(function(c){return c!==a})}},assignSyncMedium:function(s){for(o=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(c){return s(c)},filter:function(){return n}}},assignMedium:function(s){o=!0;var a=[];if(n.length){var c=n;n=[],c.forEach(s),a=n}var l=function(){var f=a;a=[],f.forEach(s)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){a.push(f),u()},filter:function(f){return a=a.filter(f),n}}}};return r}function Ci(e){e===void 0&&(e={});var t=yi(null);return t.options=fe({async:!0,ssr:!1},e),t}var $o=function(e){var t=e.sideCar,n=Fo(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return i.createElement(o,fe({},n))};$o.isSideCarExport=!0;function bi(e,t){return e.useMedium(t),$o}var Bo=Ci(),nn=function(){},Ot=i.forwardRef(function(e,t){var n=i.useRef(null),o=i.useState({onScrollCapture:nn,onWheelCapture:nn,onTouchMoveCapture:nn}),r=o[0],s=o[1],a=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,v=e.sideCar,h=e.noRelative,w=e.noIsolation,p=e.inert,g=e.allowPinchZoom,y=e.as,x=y===void 0?"div":y,C=e.gapMode,b=Fo(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=v,_=wi([n,t]),E=fe(fe({},b),r);return i.createElement(i.Fragment,null,f&&i.createElement(S,{sideCar:Bo,removeScrollBar:u,shards:d,noRelative:h,noIsolation:w,inert:p,setCallbacks:s,allowPinchZoom:!!g,lockRef:n,gapMode:C}),a?i.cloneElement(i.Children.only(c),fe(fe({},E),{ref:_})):i.createElement(x,fe({},E,{className:l,ref:_}),c))});Ot.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ot.classNames={fullWidth:wt,zeroRight:gt};var Si=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ei(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Si();return t&&e.setAttribute("nonce",t),e}function Ri(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Pi(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Ai=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Ei())&&(Ri(t,n),Pi(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Mi=function(){var e=Ai();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Wo=function(){var e=Mi(),t=function(n){var o=n.styles,r=n.dynamic;return e(o,r),null};return t},_i={left:0,top:0,right:0,gap:0},on=function(e){return parseInt(e||"",10)||0},Ii=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],o=t[e==="padding"?"paddingTop":"marginTop"],r=t[e==="padding"?"paddingRight":"marginRight"];return[on(n),on(o),on(r)]},Ti=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return _i;var t=Ii(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},Oi=Wo(),We="data-scroll-locked",Ni=function(e,t,n,o){var r=e.left,s=e.top,a=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(mi,` {
   overflow: hidden `).concat(o,`;
   padding-right: `).concat(c,"px ").concat(o,`;
  }
  body[`).concat(We,`] {
    overflow: hidden `).concat(o,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(o,";"),n==="margin"&&`
    padding-left: `.concat(r,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(o,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(o,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(gt,` {
    right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(wt,` {
    margin-right: `).concat(c,"px ").concat(o,`;
  }
  
  .`).concat(gt," .").concat(gt,` {
    right: 0 `).concat(o,`;
  }
  
  .`).concat(wt," .").concat(wt,` {
    margin-right: 0 `).concat(o,`;
  }
  
  body[`).concat(We,`] {
    `).concat(vi,": ").concat(c,`px;
  }
`)},lo=function(){var e=parseInt(document.body.getAttribute(We)||"0",10);return isFinite(e)?e:0},Di=function(){i.useEffect(function(){return document.body.setAttribute(We,(lo()+1).toString()),function(){var e=lo()-1;e<=0?document.body.removeAttribute(We):document.body.setAttribute(We,e.toString())}},[])},ki=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,r=o===void 0?"margin":o;Di();var s=i.useMemo(function(){return Ti(r)},[r]);return i.createElement(Oi,{styles:Ni(s,!t,r,n?"":"!important")})},fn=!1;if(typeof window<"u")try{var ft=Object.defineProperty({},"passive",{get:function(){return fn=!0,!0}});window.addEventListener("test",ft,ft),window.removeEventListener("test",ft,ft)}catch{fn=!1}var Fe=fn?{passive:!1}:!1,Li=function(e){return e.tagName==="TEXTAREA"},Vo=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Li(e)&&n[t]==="visible")},ji=function(e){return Vo(e,"overflowY")},Fi=function(e){return Vo(e,"overflowX")},uo=function(e,t){var n=t.ownerDocument,o=t;do{typeof ShadowRoot<"u"&&o instanceof ShadowRoot&&(o=o.host);var r=Ho(e,o);if(r){var s=Uo(e,o),a=s[1],c=s[2];if(a>c)return!0}o=o.parentNode}while(o&&o!==n.body);return!1},$i=function(e){var t=e.scrollTop,n=e.scrollHeight,o=e.clientHeight;return[t,n,o]},Bi=function(e){var t=e.scrollLeft,n=e.scrollWidth,o=e.clientWidth;return[t,n,o]},Ho=function(e,t){return e==="v"?ji(t):Fi(t)},Uo=function(e,t){return e==="v"?$i(t):Bi(t)},Wi=function(e,t){return e==="h"&&t==="rtl"?-1:1},Vi=function(e,t,n,o,r){var s=Wi(e,window.getComputedStyle(t).direction),a=s*o,c=n.target,l=t.contains(c),u=!1,f=a>0,d=0,v=0;do{if(!c)break;var h=Uo(e,c),w=h[0],p=h[1],g=h[2],y=p-g-s*w;(w||y)&&Ho(e,c)&&(d+=y,v+=w);var x=c.parentNode;c=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(f&&Math.abs(d)<1||!f&&Math.abs(v)<1)&&(u=!0),u},pt=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},fo=function(e){return[e.deltaX,e.deltaY]},po=function(e){return e&&"current"in e?e.current:e},Hi=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Ui=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ki=0,$e=[];function Gi(e){var t=i.useRef([]),n=i.useRef([0,0]),o=i.useRef(),r=i.useState(Ki++)[0],s=i.useState(Wo)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var p=pi([e.lockRef.current],(e.shards||[]).map(po),!0).filter(Boolean);return p.forEach(function(g){return g.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),p.forEach(function(g){return g.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var c=i.useCallback(function(p,g){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!a.current.allowPinchZoom;var y=pt(p),x=n.current,C="deltaX"in p?p.deltaX:x[0]-y[0],b="deltaY"in p?p.deltaY:x[1]-y[1],S,_=p.target,E=Math.abs(C)>Math.abs(b)?"h":"v";if("touches"in p&&E==="h"&&_.type==="range")return!1;var M=uo(E,_);if(!M)return!0;if(M?S=E:(S=E==="v"?"h":"v",M=uo(E,_)),!M)return!1;if(!o.current&&"changedTouches"in p&&(C||b)&&(o.current=S),!S)return!0;var D=o.current||S;return Vi(D,g,p,D==="h"?C:b)},[]),l=i.useCallback(function(p){var g=p;if(!(!$e.length||$e[$e.length-1]!==s)){var y="deltaY"in g?fo(g):pt(g),x=t.current.filter(function(S){return S.name===g.type&&(S.target===g.target||g.target===S.shadowParent)&&Hi(S.delta,y)})[0];if(x&&x.should){g.cancelable&&g.preventDefault();return}if(!x){var C=(a.current.shards||[]).map(po).filter(Boolean).filter(function(S){return S.contains(g.target)}),b=C.length>0?c(g,C[0]):!a.current.noIsolation;b&&g.cancelable&&g.preventDefault()}}},[]),u=i.useCallback(function(p,g,y,x){var C={name:p,delta:g,target:y,should:x,shadowParent:zi(y)};t.current.push(C),setTimeout(function(){t.current=t.current.filter(function(b){return b!==C})},1)},[]),f=i.useCallback(function(p){n.current=pt(p),o.current=void 0},[]),d=i.useCallback(function(p){u(p.type,fo(p),p.target,c(p,e.lockRef.current))},[]),v=i.useCallback(function(p){u(p.type,pt(p),p.target,c(p,e.lockRef.current))},[]);i.useEffect(function(){return $e.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:v}),document.addEventListener("wheel",l,Fe),document.addEventListener("touchmove",l,Fe),document.addEventListener("touchstart",f,Fe),function(){$e=$e.filter(function(p){return p!==s}),document.removeEventListener("wheel",l,Fe),document.removeEventListener("touchmove",l,Fe),document.removeEventListener("touchstart",f,Fe)}},[]);var h=e.removeScrollBar,w=e.inert;return i.createElement(i.Fragment,null,w?i.createElement(s,{styles:Ui(r)}):null,h?i.createElement(ki,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function zi(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Yi=bi(Bo,Gi);var st=i.forwardRef(function(e,t){return i.createElement(Ot,fe({},e,{ref:t,sideCar:Yi}))});st.classNames=Ot.classNames;var Xi=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Be=new WeakMap,mt=new WeakMap,vt={},rn=0,Ko=function(e){return e&&(e.host||Ko(e.parentNode))},qi=function(e,t){return t.map(function(n){if(e.contains(n))return n;var o=Ko(n);return o&&e.contains(o)?o:null}).filter(function(n){return!!n})},Zi=function(e,t,n,o){var r=qi(t,Array.isArray(e)?e:[e]);vt[n]||(vt[n]=new WeakMap);var s=vt[n],a=[],c=new Set,l=new Set(r),u=function(d){!d||c.has(d)||(c.add(d),u(d.parentNode))};r.forEach(u);var f=function(d){!d||l.has(d)||Array.prototype.forEach.call(d.children,function(v){if(c.has(v))f(v);else try{var h=v.getAttribute(o),w=h!==null&&h!=="false",p=(Be.get(v)||0)+1,g=(s.get(v)||0)+1;Be.set(v,p),s.set(v,g),a.push(v),p===1&&w&&mt.set(v,!0),g===1&&v.setAttribute(n,"true"),w||v.setAttribute(o,"true")}catch{}})};return f(t),c.clear(),rn++,function(){a.forEach(function(d){var v=Be.get(d)-1,h=s.get(d)-1;Be.set(d,v),s.set(d,h),v||(mt.has(d)||d.removeAttribute(o),mt.delete(d)),h||d.removeAttribute(n)}),rn--,rn||(Be=new WeakMap,Be=new WeakMap,mt=new WeakMap,vt={})}},Nt=function(e,t,n){n===void 0&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),r=Xi(e);return r?(o.push.apply(o,Array.from(r.querySelectorAll("[aria-live], script"))),Zi(o,r,n,"aria-hidden")):function(){return null}},Dt="Dialog",[Go,Dd]=se(Dt),[Qi,de]=Go(Dt),zo=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!0}=e,c=i.useRef(null),l=i.useRef(null),[u,f]=xe({prop:o,defaultProp:r??!1,onChange:s,caller:Dt});return m.jsx(Qi,{scope:t,triggerRef:c,contentRef:l,contentId:oe(),titleId:oe(),descriptionId:oe(),open:u,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(d=>!d),[f]),modal:a,children:n})};zo.displayName=Dt;var Yo="DialogTrigger",Xo=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Yo,n),s=F(t,r.triggerRef);return m.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":_n(r.open),...o,ref:s,onClick:R(e.onClick,r.onOpenToggle)})});Xo.displayName=Yo;var An="DialogPortal",[Ji,qo]=Go(An,{forceMount:void 0}),Zo=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,s=de(An,t);return m.jsx(Ji,{scope:t,forceMount:n,children:i.Children.map(o,a=>m.jsx(ee,{present:n||s.open,children:m.jsx(rt,{asChild:!0,container:r,children:a})}))})};Zo.displayName=An;var yt="DialogOverlay",Qo=i.forwardRef((e,t)=>{const n=qo(yt,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(yt,e.__scopeDialog);return s.modal?m.jsx(ee,{present:o||s.open,children:m.jsx(tc,{...r,ref:t})}):null});Qo.displayName=yt;var ec=Ee("DialogOverlay.RemoveScroll"),tc=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(yt,n);return m.jsx(st,{as:ec,allowPinchZoom:!0,shards:[r.contentRef],children:m.jsx(T.div,{"data-state":_n(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),Oe="DialogContent",Jo=i.forwardRef((e,t)=>{const n=qo(Oe,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,s=de(Oe,e.__scopeDialog);return m.jsx(ee,{present:o||s.open,children:s.modal?m.jsx(nc,{...r,ref:t}):m.jsx(oc,{...r,ref:t})})});Jo.displayName=Oe;var nc=i.forwardRef((e,t)=>{const n=de(Oe,e.__scopeDialog),o=i.useRef(null),r=F(t,n.contentRef,o);return i.useEffect(()=>{const s=o.current;if(s)return Nt(s)},[]),m.jsx(er,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,s=>{s.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,c=a.button===0&&a.ctrlKey===!0;(a.button===2||c)&&s.preventDefault()}),onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault())})}),oc=i.forwardRef((e,t)=>{const n=de(Oe,e.__scopeDialog),o=i.useRef(!1),r=i.useRef(!1);return m.jsx(er,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),er=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,...a}=e,c=de(Oe,n),l=i.useRef(null),u=F(t,l);return Tt(),m.jsxs(m.Fragment,{children:[m.jsx(ot,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:m.jsx(Ke,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":_n(c.open),...a,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(rc,{titleId:c.titleId}),m.jsx(ac,{contentRef:l,descriptionId:c.descriptionId})]})]})}),Mn="DialogTitle",tr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(Mn,n);return m.jsx(T.h2,{id:r.titleId,...o,ref:t})});tr.displayName=Mn;var nr="DialogDescription",or=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(nr,n);return m.jsx(T.p,{id:r.descriptionId,...o,ref:t})});or.displayName=nr;var rr="DialogClose",sr=i.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=de(rr,n);return m.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});sr.displayName=rr;function _n(e){return e?"open":"closed"}var ar="DialogTitleWarning",[kd,ir]=La(ar,{contentName:Oe,titleName:Mn,docsSlug:"dialog"}),rc=({titleId:e})=>{const t=ir(ar),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{if(e){const o=document.getElementById(e)}},[n,e]),null},sc="DialogDescriptionWarning",ac=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${ir(sc).contentName}}.`;return i.useEffect(()=>{const r=e.current?.getAttribute("aria-describedby");if(t&&r){const s=document.getElementById(t)}},[o,e,t]),null},Ld=zo,jd=Xo,Fd=Zo,$d=Qo,Bd=Jo,Wd=tr,Vd=or,Hd=sr,sn={exports:{}},an={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mo;function ic(){if(mo)return an;mo=1;var e=Ma();function t(d,v){return d===v&&(d!==0||1/d===1/v)||d!==d&&v!==v}var n=typeof Object.is=="function"?Object.is:t,o=e.useState,r=e.useEffect,s=e.useLayoutEffect,a=e.useDebugValue;function c(d,v){var h=v(),w=o({inst:{value:h,getSnapshot:v}}),p=w[0].inst,g=w[1];return s(function(){p.value=h,p.getSnapshot=v,l(p)&&g({inst:p})},[d,h,v]),r(function(){return l(p)&&g({inst:p}),d(function(){l(p)&&g({inst:p})})},[d]),a(h),h}function l(d){var v=d.getSnapshot;d=d.value;try{var h=v();return!n(d,h)}catch{return!0}}function u(d,v){return v()}var f=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:c;return an.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:f,an}var vo;function cc(){return vo||(vo=1,sn.exports=ic()),sn.exports}var lc=cc();function uc(){return lc.useSyncExternalStore(dc,()=>!0,()=>!1)}function dc(){return()=>{}}var In="Avatar",[fc,Ud]=se(In),[pc,cr]=fc(In),lr=i.forwardRef((e,t)=>{const{__scopeAvatar:n,...o}=e,[r,s]=i.useState("idle");return m.jsx(pc,{scope:n,imageLoadingStatus:r,onImageLoadingStatusChange:s,children:m.jsx(T.span,{...o,ref:t})})});lr.displayName=In;var ur="AvatarImage",mc=i.forwardRef((e,t)=>{const{__scopeAvatar:n,src:o,onLoadingStatusChange:r=()=>{},...s}=e,a=cr(ur,n),c=vc(o,s),l=ce(u=>{r(u),a.onImageLoadingStatusChange(u)});return z(()=>{c!=="idle"&&l(c)},[c,l]),c==="loaded"?m.jsx(T.img,{...s,ref:t,src:o}):null});mc.displayName=ur;var dr="AvatarFallback",fr=i.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:o,...r}=e,s=cr(dr,n),[a,c]=i.useState(o===void 0);return i.useEffect(()=>{if(o!==void 0){const l=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(l)}},[o]),a&&s.imageLoadingStatus!=="loaded"?m.jsx(T.span,{...r,ref:t}):null});fr.displayName=dr;function ho(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function vc(e,{referrerPolicy:t,crossOrigin:n}){const o=uc(),r=i.useRef(null),s=o?(r.current||(r.current=new window.Image),r.current):null,[a,c]=i.useState(()=>ho(s,e));return z(()=>{c(ho(s,e))},[s,e]),z(()=>{const l=d=>()=>{c(d)};if(!s)return;const u=l("loaded"),f=l("error");return s.addEventListener("load",u),s.addEventListener("error",f),t&&(s.referrerPolicy=t),typeof n=="string"&&(s.crossOrigin=n),()=>{s.removeEventListener("load",u),s.removeEventListener("error",f)}},[s,n,t]),a}var Kd=lr,Gd=fr;function pr(e){const t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function mr(e){const[t,n]=i.useState(void 0);return z(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const o=new ResizeObserver(r=>{if(!Array.isArray(r)||!r.length)return;const s=r[0];let a,c;if("borderBoxSize"in s){const l=s.borderBoxSize,u=Array.isArray(l)?l[0]:l;a=u.inlineSize,c=u.blockSize}else a=e.offsetWidth,c=e.offsetHeight;n({width:a,height:c})});return o.observe(e,{box:"border-box"}),()=>o.unobserve(e)}else n(void 0)},[e]),t}var kt="Checkbox",[hc,zd]=se(kt),[gc,Tn]=hc(kt);function wc(e){const{__scopeCheckbox:t,checked:n,children:o,defaultChecked:r,disabled:s,form:a,name:c,onCheckedChange:l,required:u,value:f="on",internal_do_not_use_render:d}=e,[v,h]=xe({prop:n,defaultProp:r??!1,onChange:l,caller:kt}),[w,p]=i.useState(null),[g,y]=i.useState(null),x=i.useRef(!1),C=w?!!a||!!w.closest("form"):!0,b={checked:v,disabled:s,setChecked:h,control:w,setControl:p,name:c,form:a,value:f,hasConsumerStoppedPropagationRef:x,required:u,defaultChecked:Se(r)?!1:r,isFormControl:C,bubbleInput:g,setBubbleInput:y};return m.jsx(gc,{scope:t,...b,children:Cc(d)?d(b):o})}var vr="CheckboxTrigger",hr=i.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...o},r)=>{const{control:s,value:a,disabled:c,checked:l,required:u,setControl:f,setChecked:d,hasConsumerStoppedPropagationRef:v,isFormControl:h,bubbleInput:w}=Tn(vr,e),p=F(r,f),g=i.useRef(l);return i.useEffect(()=>{const y=s?.form;if(y){const x=()=>d(g.current);return y.addEventListener("reset",x),()=>y.removeEventListener("reset",x)}},[s,d]),m.jsx(T.button,{type:"button",role:"checkbox","aria-checked":Se(l)?"mixed":l,"aria-required":u,"data-state":yr(l),"data-disabled":c?"":void 0,disabled:c,value:a,...o,ref:p,onKeyDown:R(t,y=>{y.key==="Enter"&&y.preventDefault()}),onClick:R(n,y=>{d(x=>Se(x)?!0:!x),w&&h&&(v.current=y.isPropagationStopped(),v.current||y.stopPropagation())})})});hr.displayName=vr;var xc=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:o,checked:r,defaultChecked:s,required:a,disabled:c,value:l,onCheckedChange:u,form:f,...d}=e;return m.jsx(wc,{__scopeCheckbox:n,checked:r,defaultChecked:s,disabled:c,required:a,onCheckedChange:u,name:o,form:f,value:l,internal_do_not_use_render:({isFormControl:v})=>m.jsxs(m.Fragment,{children:[m.jsx(hr,{...d,ref:t,__scopeCheckbox:n}),v&&m.jsx(xr,{__scopeCheckbox:n})]})})});xc.displayName=kt;var gr="CheckboxIndicator",yc=i.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:o,...r}=e,s=Tn(gr,n);return m.jsx(ee,{present:o||Se(s.checked)||s.checked===!0,children:m.jsx(T.span,{"data-state":yr(s.checked),"data-disabled":s.disabled?"":void 0,...r,ref:t,style:{pointerEvents:"none",...e.style}})})});yc.displayName=gr;var wr="CheckboxBubbleInput",xr=i.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:o,hasConsumerStoppedPropagationRef:r,checked:s,defaultChecked:a,required:c,disabled:l,name:u,value:f,form:d,bubbleInput:v,setBubbleInput:h}=Tn(wr,e),w=F(n,h),p=pr(s),g=mr(o);i.useEffect(()=>{const x=v;if(!x)return;const C=window.HTMLInputElement.prototype,S=Object.getOwnPropertyDescriptor(C,"checked").set,_=!r.current;if(p!==s&&S){const E=new Event("click",{bubbles:_});x.indeterminate=Se(s),S.call(x,Se(s)?!1:s),x.dispatchEvent(E)}},[v,p,s,r]);const y=i.useRef(Se(s)?!1:s);return m.jsx(T.input,{type:"checkbox","aria-hidden":!0,defaultChecked:a??y.current,required:c,disabled:l,name:u,value:f,form:d,...t,tabIndex:-1,ref:w,style:{...t.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});xr.displayName=wr;function Cc(e){return typeof e=="function"}function Se(e){return e==="indeterminate"}function yr(e){return Se(e)?"indeterminate":e?"checked":"unchecked"}const bc=["top","right","bottom","left"],Re=Math.min,Q=Math.max,Ct=Math.round,ht=Math.floor,me=e=>({x:e,y:e}),Sc={left:"right",right:"left",bottom:"top",top:"bottom"},Ec={start:"end",end:"start"};function pn(e,t,n){return Q(e,Re(t,n))}function ye(e,t){return typeof e=="function"?e(t):e}function Ce(e){return e.split("-")[0]}function Ge(e){return e.split("-")[1]}function On(e){return e==="x"?"y":"x"}function Nn(e){return e==="y"?"height":"width"}function pe(e){return["top","bottom"].includes(Ce(e))?"y":"x"}function Dn(e){return On(pe(e))}function Rc(e,t,n){n===void 0&&(n=!1);const o=Ge(e),r=Dn(e),s=Nn(r);let a=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=bt(a)),[a,bt(a)]}function Pc(e){const t=bt(e);return[mn(e),t,mn(t)]}function mn(e){return e.replace(/start|end/g,t=>Ec[t])}function Ac(e,t,n){const o=["left","right"],r=["right","left"],s=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?s:a;default:return[]}}function Mc(e,t,n,o){const r=Ge(e);let s=Ac(Ce(e),n==="start",o);return r&&(s=s.map(a=>a+"-"+r),t&&(s=s.concat(s.map(mn)))),s}function bt(e){return e.replace(/left|right|bottom|top/g,t=>Sc[t])}function _c(e){return{top:0,right:0,bottom:0,left:0,...e}}function Cr(e){return typeof e!="number"?_c(e):{top:e,right:e,bottom:e,left:e}}function St(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function go(e,t,n){let{reference:o,floating:r}=e;const s=pe(t),a=Dn(t),c=Nn(a),l=Ce(t),u=s==="y",f=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,v=o[c]/2-r[c]/2;let h;switch(l){case"top":h={x:f,y:o.y-r.height};break;case"bottom":h={x:f,y:o.y+o.height};break;case"right":h={x:o.x+o.width,y:d};break;case"left":h={x:o.x-r.width,y:d};break;default:h={x:o.x,y:o.y}}switch(Ge(t)){case"start":h[a]-=v*(n&&u?-1:1);break;case"end":h[a]+=v*(n&&u?-1:1);break}return h}const Ic=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:s=[],platform:a}=n,c=s.filter(Boolean),l=await(a.isRTL==null?void 0:a.isRTL(t));let u=await a.getElementRects({reference:e,floating:t,strategy:r}),{x:f,y:d}=go(u,o,l),v=o,h={},w=0;for(let p=0;p<c.length;p++){const{name:g,fn:y}=c[p],{x,y:C,data:b,reset:S}=await y({x:f,y:d,initialPlacement:o,placement:v,strategy:r,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});f=x??f,d=C??d,h={...h,[g]:{...h[g],...b}},S&&w<=50&&(w++,typeof S=="object"&&(S.placement&&(v=S.placement),S.rects&&(u=S.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:r}):S.rects),{x:f,y:d}=go(u,v,l)),p=-1)}return{x:f,y:d,placement:v,strategy:r,middlewareData:h}};async function Qe(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:s,rects:a,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:v=!1,padding:h=0}=ye(t,e),w=Cr(h),g=c[v?d==="floating"?"reference":"floating":d],y=St(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(c.floating)),boundary:u,rootBoundary:f,strategy:l})),x=d==="floating"?{x:o,y:r,width:a.floating.width,height:a.floating.height}:a.reference,C=await(s.getOffsetParent==null?void 0:s.getOffsetParent(c.floating)),b=await(s.isElement==null?void 0:s.isElement(C))?await(s.getScale==null?void 0:s.getScale(C))||{x:1,y:1}:{x:1,y:1},S=St(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:C,strategy:l}):x);return{top:(y.top-S.top+w.top)/b.y,bottom:(S.bottom-y.bottom+w.bottom)/b.y,left:(y.left-S.left+w.left)/b.x,right:(S.right-y.right+w.right)/b.x}}const Tc=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:s,platform:a,elements:c,middlewareData:l}=t,{element:u,padding:f=0}=ye(e,t)||{};if(u==null)return{};const d=Cr(f),v={x:n,y:o},h=Dn(r),w=Nn(h),p=await a.getDimensions(u),g=h==="y",y=g?"top":"left",x=g?"bottom":"right",C=g?"clientHeight":"clientWidth",b=s.reference[w]+s.reference[h]-v[h]-s.floating[w],S=v[h]-s.reference[h],_=await(a.getOffsetParent==null?void 0:a.getOffsetParent(u));let E=_?_[C]:0;(!E||!await(a.isElement==null?void 0:a.isElement(_)))&&(E=c.floating[C]||s.floating[w]);const M=b/2-S/2,D=E/2-p[w]/2-1,N=Re(d[y],D),$=Re(d[x],D),B=N,L=E-p[w]-$,k=E/2-p[w]/2+M,W=pn(B,k,L),O=!l.arrow&&Ge(r)!=null&&k!==W&&s.reference[w]/2-(k<B?N:$)-p[w]/2<0,j=O?k<B?k-B:k-L:0;return{[h]:v[h]+j,data:{[h]:W,centerOffset:k-W-j,...O&&{alignmentOffset:j}},reset:O}}}),Oc=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:s,rects:a,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:v,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:p=!0,...g}=ye(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const y=Ce(r),x=pe(c),C=Ce(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(u.floating)),S=v||(C||!p?[bt(c)]:Pc(c)),_=w!=="none";!v&&_&&S.push(...Mc(c,p,w,b));const E=[c,...S],M=await Qe(t,g),D=[];let N=((o=s.flip)==null?void 0:o.overflows)||[];if(f&&D.push(M[y]),d){const k=Rc(r,a,b);D.push(M[k[0]],M[k[1]])}if(N=[...N,{placement:r,overflows:D}],!D.every(k=>k<=0)){var $,B;const k=((($=s.flip)==null?void 0:$.index)||0)+1,W=E[k];if(W&&(!(d==="alignment"?x!==pe(W):!1)||N.every(I=>I.overflows[0]>0&&pe(I.placement)===x)))return{data:{index:k,overflows:N},reset:{placement:W}};let O=(B=N.filter(j=>j.overflows[0]<=0).sort((j,I)=>j.overflows[1]-I.overflows[1])[0])==null?void 0:B.placement;if(!O)switch(h){case"bestFit":{var L;const j=(L=N.filter(I=>{if(_){const P=pe(I.placement);return P===x||P==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(P=>P>0).reduce((P,K)=>P+K,0)]).sort((I,P)=>I[1]-P[1])[0])==null?void 0:L[0];j&&(O=j);break}case"initialPlacement":O=c;break}if(r!==O)return{reset:{placement:O}}}return{}}}};function wo(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function xo(e){return bc.some(t=>e[t]>=0)}const Nc=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:o="referenceHidden",...r}=ye(e,t);switch(o){case"referenceHidden":{const s=await Qe(t,{...r,elementContext:"reference"}),a=wo(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:xo(a)}}}case"escaped":{const s=await Qe(t,{...r,altBoundary:!0}),a=wo(s,n.floating);return{data:{escapedOffsets:a,escaped:xo(a)}}}default:return{}}}}};async function Dc(e,t){const{placement:n,platform:o,elements:r}=e,s=await(o.isRTL==null?void 0:o.isRTL(r.floating)),a=Ce(n),c=Ge(n),l=pe(n)==="y",u=["left","top"].includes(a)?-1:1,f=s&&l?-1:1,d=ye(t,e);let{mainAxis:v,crossAxis:h,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return c&&typeof w=="number"&&(h=c==="end"?w*-1:w),l?{x:h*f,y:v*u}:{x:v*u,y:h*f}}const kc=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:s,placement:a,middlewareData:c}=t,l=await Dc(t,e);return a===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:r+l.x,y:s+l.y,data:{...l,placement:a}}}}},Lc=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:c={fn:g=>{let{x:y,y:x}=g;return{x:y,y:x}}},...l}=ye(e,t),u={x:n,y:o},f=await Qe(t,l),d=pe(Ce(r)),v=On(d);let h=u[v],w=u[d];if(s){const g=v==="y"?"top":"left",y=v==="y"?"bottom":"right",x=h+f[g],C=h-f[y];h=pn(x,h,C)}if(a){const g=d==="y"?"top":"left",y=d==="y"?"bottom":"right",x=w+f[g],C=w-f[y];w=pn(x,w,C)}const p=c.fn({...t,[v]:h,[d]:w});return{...p,data:{x:p.x-n,y:p.y-o,enabled:{[v]:s,[d]:a}}}}}},jc=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:o,placement:r,rects:s,middlewareData:a}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=ye(e,t),f={x:n,y:o},d=pe(r),v=On(d);let h=f[v],w=f[d];const p=ye(c,t),g=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const C=v==="y"?"height":"width",b=s.reference[v]-s.floating[C]+g.mainAxis,S=s.reference[v]+s.reference[C]-g.mainAxis;h<b?h=b:h>S&&(h=S)}if(u){var y,x;const C=v==="y"?"width":"height",b=["top","left"].includes(Ce(r)),S=s.reference[d]-s.floating[C]+(b&&((y=a.offset)==null?void 0:y[d])||0)+(b?0:g.crossAxis),_=s.reference[d]+s.reference[C]+(b?0:((x=a.offset)==null?void 0:x[d])||0)-(b?g.crossAxis:0);w<S?w=S:w>_&&(w=_)}return{[v]:h,[d]:w}}}},Fc=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,o;const{placement:r,rects:s,platform:a,elements:c}=t,{apply:l=()=>{},...u}=ye(e,t),f=await Qe(t,u),d=Ce(r),v=Ge(r),h=pe(r)==="y",{width:w,height:p}=s.floating;let g,y;d==="top"||d==="bottom"?(g=d,y=v===(await(a.isRTL==null?void 0:a.isRTL(c.floating))?"start":"end")?"left":"right"):(y=d,g=v==="end"?"top":"bottom");const x=p-f.top-f.bottom,C=w-f.left-f.right,b=Re(p-f[g],x),S=Re(w-f[y],C),_=!t.middlewareData.shift;let E=b,M=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(M=C),(o=t.middlewareData.shift)!=null&&o.enabled.y&&(E=x),_&&!v){const N=Q(f.left,0),$=Q(f.right,0),B=Q(f.top,0),L=Q(f.bottom,0);h?M=w-2*(N!==0||$!==0?N+$:Q(f.left,f.right)):E=p-2*(B!==0||L!==0?B+L:Q(f.top,f.bottom))}await l({...t,availableWidth:M,availableHeight:E});const D=await a.getDimensions(c.floating);return w!==D.width||p!==D.height?{reset:{rects:!0}}:{}}}};function Lt(){return typeof window<"u"}function ze(e){return br(e)?(e.nodeName||"").toLowerCase():"#document"}function J(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function he(e){var t;return(t=(br(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function br(e){return Lt()?e instanceof Node||e instanceof J(e).Node:!1}function le(e){return Lt()?e instanceof Element||e instanceof J(e).Element:!1}function ve(e){return Lt()?e instanceof HTMLElement||e instanceof J(e).HTMLElement:!1}function yo(e){return!Lt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof J(e).ShadowRoot}function at(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=ue(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function $c(e){return["table","td","th"].includes(ze(e))}function jt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function kn(e){const t=Ln(),n=le(e)?ue(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function Bc(e){let t=Pe(e);for(;ve(t)&&!He(t);){if(kn(t))return t;if(jt(t))return null;t=Pe(t)}return null}function Ln(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function He(e){return["html","body","#document"].includes(ze(e))}function ue(e){return J(e).getComputedStyle(e)}function Ft(e){return le(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Pe(e){if(ze(e)==="html")return e;const t=e.assignedSlot||e.parentNode||yo(e)&&e.host||he(e);return yo(t)?t.host:t}function Sr(e){const t=Pe(e);return He(t)?e.ownerDocument?e.ownerDocument.body:e.body:ve(t)&&at(t)?t:Sr(t)}function Je(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=Sr(e),s=r===((o=e.ownerDocument)==null?void 0:o.body),a=J(r);if(s){const c=vn(a);return t.concat(a,a.visualViewport||[],at(r)?r:[],c&&n?Je(c):[])}return t.concat(r,Je(r,[],n))}function vn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Er(e){const t=ue(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=ve(e),s=r?e.offsetWidth:n,a=r?e.offsetHeight:o,c=Ct(n)!==s||Ct(o)!==a;return c&&(n=s,o=a),{width:n,height:o,$:c}}function jn(e){return le(e)?e:e.contextElement}function Ve(e){const t=jn(e);if(!ve(t))return me(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:s}=Er(t);let a=(s?Ct(n.width):n.width)/o,c=(s?Ct(n.height):n.height)/r;return(!a||!Number.isFinite(a))&&(a=1),(!c||!Number.isFinite(c))&&(c=1),{x:a,y:c}}const Wc=me(0);function Rr(e){const t=J(e);return!Ln()||!t.visualViewport?Wc:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Vc(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==J(e)?!1:t}function Ne(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=jn(e);let a=me(1);t&&(o?le(o)&&(a=Ve(o)):a=Ve(e));const c=Vc(s,n,o)?Rr(s):me(0);let l=(r.left+c.x)/a.x,u=(r.top+c.y)/a.y,f=r.width/a.x,d=r.height/a.y;if(s){const v=J(s),h=o&&le(o)?J(o):o;let w=v,p=vn(w);for(;p&&o&&h!==w;){const g=Ve(p),y=p.getBoundingClientRect(),x=ue(p),C=y.left+(p.clientLeft+parseFloat(x.paddingLeft))*g.x,b=y.top+(p.clientTop+parseFloat(x.paddingTop))*g.y;l*=g.x,u*=g.y,f*=g.x,d*=g.y,l+=C,u+=b,w=J(p),p=vn(w)}}return St({width:f,height:d,x:l,y:u})}function Fn(e,t){const n=Ft(e).scrollLeft;return t?t.left+n:Ne(he(e)).left+n}function Pr(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:Fn(e,o)),s=o.top+t.scrollTop;return{x:r,y:s}}function Hc(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const s=r==="fixed",a=he(o),c=t?jt(t.floating):!1;if(o===a||c&&s)return n;let l={scrollLeft:0,scrollTop:0},u=me(1);const f=me(0),d=ve(o);if((d||!d&&!s)&&((ze(o)!=="body"||at(a))&&(l=Ft(o)),ve(o))){const h=Ne(o);u=Ve(o),f.x=h.x+o.clientLeft,f.y=h.y+o.clientTop}const v=a&&!d&&!s?Pr(a,l,!0):me(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+f.x+v.x,y:n.y*u.y-l.scrollTop*u.y+f.y+v.y}}function Uc(e){return Array.from(e.getClientRects())}function Kc(e){const t=he(e),n=Ft(e),o=e.ownerDocument.body,r=Q(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),s=Q(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let a=-n.scrollLeft+Fn(e);const c=-n.scrollTop;return ue(o).direction==="rtl"&&(a+=Q(t.clientWidth,o.clientWidth)-r),{width:r,height:s,x:a,y:c}}function Gc(e,t){const n=J(e),o=he(e),r=n.visualViewport;let s=o.clientWidth,a=o.clientHeight,c=0,l=0;if(r){s=r.width,a=r.height;const u=Ln();(!u||u&&t==="fixed")&&(c=r.offsetLeft,l=r.offsetTop)}return{width:s,height:a,x:c,y:l}}function zc(e,t){const n=Ne(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,s=ve(e)?Ve(e):me(1),a=e.clientWidth*s.x,c=e.clientHeight*s.y,l=r*s.x,u=o*s.y;return{width:a,height:c,x:l,y:u}}function Co(e,t,n){let o;if(t==="viewport")o=Gc(e,n);else if(t==="document")o=Kc(he(e));else if(le(t))o=zc(t,n);else{const r=Rr(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return St(o)}function Ar(e,t){const n=Pe(e);return n===t||!le(n)||He(n)?!1:ue(n).position==="fixed"||Ar(n,t)}function Yc(e,t){const n=t.get(e);if(n)return n;let o=Je(e,[],!1).filter(c=>le(c)&&ze(c)!=="body"),r=null;const s=ue(e).position==="fixed";let a=s?Pe(e):e;for(;le(a)&&!He(a);){const c=ue(a),l=kn(a);!l&&c.position==="fixed"&&(r=null),(s?!l&&!r:!l&&c.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||at(a)&&!l&&Ar(e,a))?o=o.filter(f=>f!==a):r=c,a=Pe(a)}return t.set(e,o),o}function Xc(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const a=[...n==="clippingAncestors"?jt(t)?[]:Yc(t,this._c):[].concat(n),o],c=a[0],l=a.reduce((u,f)=>{const d=Co(t,f,r);return u.top=Q(d.top,u.top),u.right=Re(d.right,u.right),u.bottom=Re(d.bottom,u.bottom),u.left=Q(d.left,u.left),u},Co(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function qc(e){const{width:t,height:n}=Er(e);return{width:t,height:n}}function Zc(e,t,n){const o=ve(t),r=he(t),s=n==="fixed",a=Ne(e,!0,s,t);let c={scrollLeft:0,scrollTop:0};const l=me(0);function u(){l.x=Fn(r)}if(o||!o&&!s)if((ze(t)!=="body"||at(r))&&(c=Ft(t)),o){const h=Ne(t,!0,s,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else r&&u();s&&!o&&r&&u();const f=r&&!o&&!s?Pr(r,c):me(0),d=a.left+c.scrollLeft-l.x-f.x,v=a.top+c.scrollTop-l.y-f.y;return{x:d,y:v,width:a.width,height:a.height}}function cn(e){return ue(e).position==="static"}function bo(e,t){if(!ve(e)||ue(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return he(e)===n&&(n=n.ownerDocument.body),n}function Mr(e,t){const n=J(e);if(jt(e))return n;if(!ve(e)){let r=Pe(e);for(;r&&!He(r);){if(le(r)&&!cn(r))return r;r=Pe(r)}return n}let o=bo(e,t);for(;o&&$c(o)&&cn(o);)o=bo(o,t);return o&&He(o)&&cn(o)&&!kn(o)?n:o||Bc(e)||n}const Qc=async function(e){const t=this.getOffsetParent||Mr,n=this.getDimensions,o=await n(e.floating);return{reference:Zc(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Jc(e){return ue(e).direction==="rtl"}const el={convertOffsetParentRelativeRectToViewportRelativeRect:Hc,getDocumentElement:he,getClippingRect:Xc,getOffsetParent:Mr,getElementRects:Qc,getClientRects:Uc,getDimensions:qc,getScale:Ve,isElement:le,isRTL:Jc};function _r(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function tl(e,t){let n=null,o;const r=he(e);function s(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function a(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),s();const u=e.getBoundingClientRect(),{left:f,top:d,width:v,height:h}=u;if(c||t(),!v||!h)return;const w=ht(d),p=ht(r.clientWidth-(f+v)),g=ht(r.clientHeight-(d+h)),y=ht(f),C={rootMargin:-w+"px "+-p+"px "+-g+"px "+-y+"px",threshold:Q(0,Re(1,l))||1};let b=!0;function S(_){const E=_[0].intersectionRatio;if(E!==l){if(!b)return a();E?a(!1,E):o=setTimeout(()=>{a(!1,1e-7)},1e3)}E===1&&!_r(u,e.getBoundingClientRect())&&a(),b=!1}try{n=new IntersectionObserver(S,{...C,root:r.ownerDocument})}catch{n=new IntersectionObserver(S,C)}n.observe(e)}return a(!0),s}function nl(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,u=jn(e),f=r||s?[...u?Je(u):[],...Je(t)]:[];f.forEach(y=>{r&&y.addEventListener("scroll",n,{passive:!0}),s&&y.addEventListener("resize",n)});const d=u&&c?tl(u,n):null;let v=-1,h=null;a&&(h=new ResizeObserver(y=>{let[x]=y;x&&x.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var C;(C=h)==null||C.observe(t)})),n()}),u&&!l&&h.observe(u),h.observe(t));let w,p=l?Ne(e):null;l&&g();function g(){const y=Ne(e);p&&!_r(p,y)&&n(),p=y,w=requestAnimationFrame(g)}return n(),()=>{var y;f.forEach(x=>{r&&x.removeEventListener("scroll",n),s&&x.removeEventListener("resize",n)}),d?.(),(y=h)==null||y.disconnect(),h=null,l&&cancelAnimationFrame(w)}}const ol=kc,rl=Lc,sl=Oc,al=Fc,il=Nc,So=Tc,cl=jc,ll=(e,t,n)=>{const o=new Map,r={platform:el,...n},s={...r.platform,_c:o};return Ic(e,t,{...r,platform:s})};var ul=typeof document<"u",dl=function(){},xt=ul?i.useLayoutEffect:dl;function Et(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,o,r;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(o=n;o--!==0;)if(!Et(e[o],t[o]))return!1;return!0}if(r=Object.keys(e),n=r.length,n!==Object.keys(t).length)return!1;for(o=n;o--!==0;)if(!{}.hasOwnProperty.call(t,r[o]))return!1;for(o=n;o--!==0;){const s=r[o];if(!(s==="_owner"&&e.$$typeof)&&!Et(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ir(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Eo(e,t){const n=Ir(e);return Math.round(t*n)/n}function ln(e){const t=i.useRef(e);return xt(()=>{t.current=e}),t}function fl(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:r,elements:{reference:s,floating:a}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[f,d]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=i.useState(o);Et(v,o)||h(o);const[w,p]=i.useState(null),[g,y]=i.useState(null),x=i.useCallback(I=>{I!==_.current&&(_.current=I,p(I))},[]),C=i.useCallback(I=>{I!==E.current&&(E.current=I,y(I))},[]),b=s||w,S=a||g,_=i.useRef(null),E=i.useRef(null),M=i.useRef(f),D=l!=null,N=ln(l),$=ln(r),B=ln(u),L=i.useCallback(()=>{if(!_.current||!E.current)return;const I={placement:t,strategy:n,middleware:v};$.current&&(I.platform=$.current),ll(_.current,E.current,I).then(P=>{const K={...P,isPositioned:B.current!==!1};k.current&&!Et(M.current,K)&&(M.current=K,nt.flushSync(()=>{d(K)}))})},[v,t,n,$,B]);xt(()=>{u===!1&&M.current.isPositioned&&(M.current.isPositioned=!1,d(I=>({...I,isPositioned:!1})))},[u]);const k=i.useRef(!1);xt(()=>(k.current=!0,()=>{k.current=!1}),[]),xt(()=>{if(b&&(_.current=b),S&&(E.current=S),b&&S){if(N.current)return N.current(b,S,L);L()}},[b,S,L,N,D]);const W=i.useMemo(()=>({reference:_,floating:E,setReference:x,setFloating:C}),[x,C]),O=i.useMemo(()=>({reference:b,floating:S}),[b,S]),j=i.useMemo(()=>{const I={position:n,left:0,top:0};if(!O.floating)return I;const P=Eo(O.floating,f.x),K=Eo(O.floating,f.y);return c?{...I,transform:"translate("+P+"px, "+K+"px)",...Ir(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:P,top:K}},[n,c,O.floating,f.x,f.y]);return i.useMemo(()=>({...f,update:L,refs:W,elements:O,floatingStyles:j}),[f,L,W,O,j])}const pl=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:o,padding:r}=typeof e=="function"?e(n):e;return o&&t(o)?o.current!=null?So({element:o.current,padding:r}).fn(n):{}:o?So({element:o,padding:r}).fn(n):{}}}},ml=(e,t)=>({...ol(e),options:[e,t]}),vl=(e,t)=>({...rl(e),options:[e,t]}),hl=(e,t)=>({...cl(e),options:[e,t]}),gl=(e,t)=>({...sl(e),options:[e,t]}),wl=(e,t)=>({...al(e),options:[e,t]}),xl=(e,t)=>({...il(e),options:[e,t]}),yl=(e,t)=>({...pl(e),options:[e,t]});var Cl="Arrow",Tr=i.forwardRef((e,t)=>{const{children:n,width:o=10,height:r=5,...s}=e;return m.jsx(T.svg,{...s,ref:t,width:o,height:r,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});Tr.displayName=Cl;var bl=Tr,$n="Popper",[Or,Ye]=se($n),[Sl,Nr]=Or($n),Dr=e=>{const{__scopePopper:t,children:n}=e,[o,r]=i.useState(null);return m.jsx(Sl,{scope:t,anchor:o,onAnchorChange:r,children:n})};Dr.displayName=$n;var kr="PopperAnchor",Lr=i.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...r}=e,s=Nr(kr,n),a=i.useRef(null),c=F(t,a);return i.useEffect(()=>{s.onAnchorChange(o?.current||a.current)}),o?null:m.jsx(T.div,{...r,ref:c})});Lr.displayName=kr;var Bn="PopperContent",[El,Rl]=Or(Bn),jr=i.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:r=0,align:s="center",alignOffset:a=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:v=!1,updatePositionStrategy:h="optimized",onPlaced:w,...p}=e,g=Nr(Bn,n),[y,x]=i.useState(null),C=F(t,A=>x(A)),[b,S]=i.useState(null),_=mr(b),E=_?.width??0,M=_?.height??0,D=o+(s!=="center"?"-"+s:""),N=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},$=Array.isArray(u)?u:[u],B=$.length>0,L={padding:N,boundary:$.filter(Al),altBoundary:B},{refs:k,floatingStyles:W,placement:O,isPositioned:j,middlewareData:I}=fl({strategy:"fixed",placement:D,whileElementsMounted:(...A)=>nl(...A,{animationFrame:h==="always"}),elements:{reference:g.anchor},middleware:[ml({mainAxis:r+M,alignmentAxis:a}),l&&vl({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?hl():void 0,...L}),l&&gl({...L}),wl({...L,apply:({elements:A,rects:V,availableWidth:X,availableHeight:H})=>{const{width:U,height:G}=V.reference,ne=A.floating.style;ne.setProperty("--radix-popper-available-width",`${X}px`),ne.setProperty("--radix-popper-available-height",`${H}px`),ne.setProperty("--radix-popper-anchor-width",`${U}px`),ne.setProperty("--radix-popper-anchor-height",`${G}px`)}}),b&&yl({element:b,padding:c}),Ml({arrowWidth:E,arrowHeight:M}),v&&xl({strategy:"referenceHidden",...L})]}),[P,K]=Br(O),Y=ce(w);z(()=>{j&&Y?.()},[j,Y]);const ae=I.arrow?.x,ge=I.arrow?.y,te=I.arrow?.centerOffset!==0,[we,Z]=i.useState();return z(()=>{y&&Z(window.getComputedStyle(y).zIndex)},[y]),m.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...W,transform:j?W.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:we,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:m.jsx(El,{scope:n,placedSide:P,onArrowChange:S,arrowX:ae,arrowY:ge,shouldHideArrow:te,children:m.jsx(T.div,{"data-side":P,"data-align":K,...p,ref:C,style:{...p.style,animation:j?void 0:"none"}})})})});jr.displayName=Bn;var Fr="PopperArrow",Pl={top:"bottom",right:"left",bottom:"top",left:"right"},$r=i.forwardRef(function(t,n){const{__scopePopper:o,...r}=t,s=Rl(Fr,o),a=Pl[s.placedSide];return m.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:m.jsx(bl,{...r,ref:n,style:{...r.style,display:"block"}})})});$r.displayName=Fr;function Al(e){return e!==null}var Ml=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:o,middlewareData:r}=t,a=r.arrow?.centerOffset!==0,c=a?0:e.arrowWidth,l=a?0:e.arrowHeight,[u,f]=Br(n),d={start:"0%",center:"50%",end:"100%"}[f],v=(r.arrow?.x??0)+c/2,h=(r.arrow?.y??0)+l/2;let w="",p="";return u==="bottom"?(w=a?d:`${v}px`,p=`${-l}px`):u==="top"?(w=a?d:`${v}px`,p=`${o.floating.height+l}px`):u==="right"?(w=`${-l}px`,p=a?d:`${h}px`):u==="left"&&(w=`${o.floating.width+l}px`,p=a?d:`${h}px`),{data:{x:w,y:p}}}});function Br(e){const[t,n="center"]=e.split("-");return[t,n]}var Wn=Dr,$t=Lr,Vn=jr,Hn=$r,un="rovingFocusGroup.onEntryFocus",_l={bubbles:!1,cancelable:!0},it="RovingFocusGroup",[hn,Wr,Il]=Sn(it),[Tl,Bt]=se(it,[Il]),[Ol,Nl]=Tl(it),Vr=i.forwardRef((e,t)=>m.jsx(hn.Provider,{scope:e.__scopeRovingFocusGroup,children:m.jsx(hn.Slot,{scope:e.__scopeRovingFocusGroup,children:m.jsx(Dl,{...e,ref:t})})}));Vr.displayName=it;var Dl=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:s,currentTabStopId:a,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:f=!1,...d}=e,v=i.useRef(null),h=F(t,v),w=It(s),[p,g]=xe({prop:a,defaultProp:c??null,onChange:l,caller:it}),[y,x]=i.useState(!1),C=ce(u),b=Wr(n),S=i.useRef(!1),[_,E]=i.useState(0);return i.useEffect(()=>{const M=v.current;if(M)return M.addEventListener(un,C),()=>M.removeEventListener(un,C)},[C]),m.jsx(Ol,{scope:n,orientation:o,dir:w,loop:r,currentTabStopId:p,onItemFocus:i.useCallback(M=>g(M),[g]),onItemShiftTab:i.useCallback(()=>x(!0),[]),onFocusableItemAdd:i.useCallback(()=>E(M=>M+1),[]),onFocusableItemRemove:i.useCallback(()=>E(M=>M-1),[]),children:m.jsx(T.div,{tabIndex:y||_===0?-1:0,"data-orientation":o,...d,ref:h,style:{outline:"none",...e.style},onMouseDown:R(e.onMouseDown,()=>{S.current=!0}),onFocus:R(e.onFocus,M=>{const D=!S.current;if(M.target===M.currentTarget&&D&&!y){const N=new CustomEvent(un,_l);if(M.currentTarget.dispatchEvent(N),!N.defaultPrevented){const $=b().filter(O=>O.focusable),B=$.find(O=>O.active),L=$.find(O=>O.id===p),W=[B,L,...$].filter(Boolean).map(O=>O.ref.current);Kr(W,f)}}S.current=!1}),onBlur:R(e.onBlur,()=>x(!1))})})}),Hr="RovingFocusGroupItem",Ur=i.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:s,children:a,...c}=e,l=oe(),u=s||l,f=Nl(Hr,n),d=f.currentTabStopId===u,v=Wr(n),{onFocusableItemAdd:h,onFocusableItemRemove:w,currentTabStopId:p}=f;return i.useEffect(()=>{if(o)return h(),()=>w()},[o,h,w]),m.jsx(hn.ItemSlot,{scope:n,id:u,focusable:o,active:r,children:m.jsx(T.span,{tabIndex:d?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:R(e.onMouseDown,g=>{o?f.onItemFocus(u):g.preventDefault()}),onFocus:R(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:R(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){f.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const y=jl(g,f.orientation,f.dir);if(y!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let C=v().filter(b=>b.focusable).map(b=>b.ref.current);if(y==="last")C.reverse();else if(y==="prev"||y==="next"){y==="prev"&&C.reverse();const b=C.indexOf(g.currentTarget);C=f.loop?Fl(C,b+1):C.slice(b+1)}setTimeout(()=>Kr(C))}}),children:typeof a=="function"?a({isCurrentTabStop:d,hasTabStop:p!=null}):a})})});Ur.displayName=Hr;var kl={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ll(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function jl(e,t,n){const o=Ll(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return kl[o]}function Kr(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function Fl(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var Gr=Vr,zr=Ur,gn=["Enter"," "],$l=["ArrowDown","PageUp","Home"],Yr=["ArrowUp","PageDown","End"],Bl=[...$l,...Yr],Wl={ltr:[...gn,"ArrowRight"],rtl:[...gn,"ArrowLeft"]},Vl={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ct="Menu",[et,Hl,Ul]=Sn(ct),[Le,Xr]=se(ct,[Ul,Ye,Bt]),Wt=Ye(),qr=Bt(),[Kl,je]=Le(ct),[Gl,lt]=Le(ct),Zr=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:s,modal:a=!0}=e,c=Wt(t),[l,u]=i.useState(null),f=i.useRef(!1),d=ce(s),v=It(r);return i.useEffect(()=>{const h=()=>{f.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>f.current=!1;return document.addEventListener("keydown",h,{capture:!0}),()=>{document.removeEventListener("keydown",h,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),m.jsx(Wn,{...c,children:m.jsx(Kl,{scope:t,open:n,onOpenChange:d,content:l,onContentChange:u,children:m.jsx(Gl,{scope:t,onClose:i.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:v,modal:a,children:o})})})};Zr.displayName=ct;var zl="MenuAnchor",Un=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Wt(n);return m.jsx($t,{...r,...o,ref:t})});Un.displayName=zl;var Kn="MenuPortal",[Yl,Qr]=Le(Kn,{forceMount:void 0}),Jr=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,s=je(Kn,t);return m.jsx(Yl,{scope:t,forceMount:n,children:m.jsx(ee,{present:n||s.open,children:m.jsx(rt,{asChild:!0,container:r,children:o})})})};Jr.displayName=Kn;var re="MenuContent",[Xl,Gn]=Le(re),es=i.forwardRef((e,t)=>{const n=Qr(re,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=je(re,e.__scopeMenu),a=lt(re,e.__scopeMenu);return m.jsx(et.Provider,{scope:e.__scopeMenu,children:m.jsx(ee,{present:o||s.open,children:m.jsx(et.Slot,{scope:e.__scopeMenu,children:a.modal?m.jsx(ql,{...r,ref:t}):m.jsx(Zl,{...r,ref:t})})})})}),ql=i.forwardRef((e,t)=>{const n=je(re,e.__scopeMenu),o=i.useRef(null),r=F(t,o);return i.useEffect(()=>{const s=o.current;if(s)return Nt(s)},[]),m.jsx(zn,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:R(e.onFocusOutside,s=>s.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Zl=i.forwardRef((e,t)=>{const n=je(re,e.__scopeMenu);return m.jsx(zn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Ql=Ee("MenuContent.ScrollLock"),zn=i.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,disableOutsideScroll:w,...p}=e,g=je(re,n),y=lt(re,n),x=Wt(n),C=qr(n),b=Hl(n),[S,_]=i.useState(null),E=i.useRef(null),M=F(t,E,g.onContentChange),D=i.useRef(0),N=i.useRef(""),$=i.useRef(0),B=i.useRef(null),L=i.useRef("right"),k=i.useRef(0),W=w?st:i.Fragment,O=w?{as:Ql,allowPinchZoom:!0}:void 0,j=P=>{const K=N.current+P,Y=b().filter(A=>!A.disabled),ae=document.activeElement,ge=Y.find(A=>A.ref.current===ae)?.textValue,te=Y.map(A=>A.textValue),we=uu(te,K,ge),Z=Y.find(A=>A.textValue===we)?.ref.current;(function A(V){N.current=V,window.clearTimeout(D.current),V!==""&&(D.current=window.setTimeout(()=>A(""),1e3))})(K),Z&&setTimeout(()=>Z.focus())};i.useEffect(()=>()=>window.clearTimeout(D.current),[]),Tt();const I=i.useCallback(P=>L.current===B.current?.side&&fu(P,B.current?.area),[]);return m.jsx(Xl,{scope:n,searchRef:N,onItemEnter:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),onItemLeave:i.useCallback(P=>{I(P)||(E.current?.focus(),_(null))},[I]),onTriggerLeave:i.useCallback(P=>{I(P)&&P.preventDefault()},[I]),pointerGraceTimerRef:$,onPointerGraceIntentChange:i.useCallback(P=>{B.current=P},[]),children:m.jsx(W,{...O,children:m.jsx(ot,{asChild:!0,trapped:r,onMountAutoFocus:R(s,P=>{P.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:a,children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:d,onInteractOutside:v,onDismiss:h,children:m.jsx(Gr,{asChild:!0,...C,dir:y.dir,orientation:"vertical",loop:o,currentTabStopId:S,onCurrentTabStopIdChange:_,onEntryFocus:R(l,P=>{y.isUsingKeyboardRef.current||P.preventDefault()}),preventScrollOnEntryFocus:!0,children:m.jsx(Vn,{role:"menu","aria-orientation":"vertical","data-state":hs(g.open),"data-radix-menu-content":"",dir:y.dir,...x,...p,ref:M,style:{outline:"none",...p.style},onKeyDown:R(p.onKeyDown,P=>{const Y=P.target.closest("[data-radix-menu-content]")===P.currentTarget,ae=P.ctrlKey||P.altKey||P.metaKey,ge=P.key.length===1;Y&&(P.key==="Tab"&&P.preventDefault(),!ae&&ge&&j(P.key));const te=E.current;if(P.target!==te||!Bl.includes(P.key))return;P.preventDefault();const Z=b().filter(A=>!A.disabled).map(A=>A.ref.current);Yr.includes(P.key)&&Z.reverse(),cu(Z)}),onBlur:R(e.onBlur,P=>{P.currentTarget.contains(P.target)||(window.clearTimeout(D.current),N.current="")}),onPointerMove:R(e.onPointerMove,tt(P=>{const K=P.target,Y=k.current!==P.clientX;if(P.currentTarget.contains(K)&&Y){const ae=P.clientX>k.current?"right":"left";L.current=ae,k.current=P.clientX}}))})})})})})})});es.displayName=re;var Jl="MenuGroup",Yn=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{role:"group",...o,ref:t})});Yn.displayName=Jl;var eu="MenuLabel",ts=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{...o,ref:t})});ts.displayName=eu;var Rt="MenuItem",Ro="menu.itemSelect",Vt=i.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,s=i.useRef(null),a=lt(Rt,e.__scopeMenu),c=Gn(Rt,e.__scopeMenu),l=F(t,s),u=i.useRef(!1),f=()=>{const d=s.current;if(!n&&d){const v=new CustomEvent(Ro,{bubbles:!0,cancelable:!0});d.addEventListener(Ro,h=>o?.(h),{once:!0}),_o(d,v),v.defaultPrevented?u.current=!1:a.onClose()}};return m.jsx(ns,{...r,ref:l,disabled:n,onClick:R(e.onClick,f),onPointerDown:d=>{e.onPointerDown?.(d),u.current=!0},onPointerUp:R(e.onPointerUp,d=>{u.current||d.currentTarget?.click()}),onKeyDown:R(e.onKeyDown,d=>{const v=c.searchRef.current!=="";n||v&&d.key===" "||gn.includes(d.key)&&(d.currentTarget.click(),d.preventDefault())})})});Vt.displayName=Rt;var ns=i.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...s}=e,a=Gn(Rt,n),c=qr(n),l=i.useRef(null),u=F(t,l),[f,d]=i.useState(!1),[v,h]=i.useState("");return i.useEffect(()=>{const w=l.current;w&&h((w.textContent??"").trim())},[s.children]),m.jsx(et.ItemSlot,{scope:n,disabled:o,textValue:r??v,children:m.jsx(zr,{asChild:!0,...c,focusable:!o,children:m.jsx(T.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:u,onPointerMove:R(e.onPointerMove,tt(w=>{o?a.onItemLeave(w):(a.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:R(e.onPointerLeave,tt(w=>a.onItemLeave(w))),onFocus:R(e.onFocus,()=>d(!0)),onBlur:R(e.onBlur,()=>d(!1))})})})}),tu="MenuCheckboxItem",os=i.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return m.jsx(cs,{scope:e.__scopeMenu,checked:n,children:m.jsx(Vt,{role:"menuitemcheckbox","aria-checked":Pt(n)?"mixed":n,...r,ref:t,"data-state":qn(n),onSelect:R(r.onSelect,()=>o?.(Pt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});os.displayName=tu;var rs="MenuRadioGroup",[nu,ou]=Le(rs,{value:void 0,onValueChange:()=>{}}),ss=i.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,s=ce(o);return m.jsx(nu,{scope:e.__scopeMenu,value:n,onValueChange:s,children:m.jsx(Yn,{...r,ref:t})})});ss.displayName=rs;var as="MenuRadioItem",is=i.forwardRef((e,t)=>{const{value:n,...o}=e,r=ou(as,e.__scopeMenu),s=n===r.value;return m.jsx(cs,{scope:e.__scopeMenu,checked:s,children:m.jsx(Vt,{role:"menuitemradio","aria-checked":s,...o,ref:t,"data-state":qn(s),onSelect:R(o.onSelect,()=>r.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});is.displayName=as;var Xn="MenuItemIndicator",[cs,ru]=Le(Xn,{checked:!1}),ls=i.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,s=ru(Xn,n);return m.jsx(ee,{present:o||Pt(s.checked)||s.checked===!0,children:m.jsx(T.span,{...r,ref:t,"data-state":qn(s.checked)})})});ls.displayName=Xn;var su="MenuSeparator",us=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return m.jsx(T.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});us.displayName=su;var au="MenuArrow",ds=i.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=Wt(n);return m.jsx(Hn,{...r,...o,ref:t})});ds.displayName=au;var iu="MenuSub",[Yd,fs]=Le(iu),qe="MenuSubTrigger",ps=i.forwardRef((e,t)=>{const n=je(qe,e.__scopeMenu),o=lt(qe,e.__scopeMenu),r=fs(qe,e.__scopeMenu),s=Gn(qe,e.__scopeMenu),a=i.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=s,u={__scopeMenu:e.__scopeMenu},f=i.useCallback(()=>{a.current&&window.clearTimeout(a.current),a.current=null},[]);return i.useEffect(()=>f,[f]),i.useEffect(()=>{const d=c.current;return()=>{window.clearTimeout(d),l(null)}},[c,l]),m.jsx(Un,{asChild:!0,...u,children:m.jsx(ns,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":hs(n.open),...e,ref:Mt(t,r.onTriggerChange),onClick:d=>{e.onClick?.(d),!(e.disabled||d.defaultPrevented)&&(d.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:R(e.onPointerMove,tt(d=>{s.onItemEnter(d),!d.defaultPrevented&&!e.disabled&&!n.open&&!a.current&&(s.onPointerGraceIntentChange(null),a.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:R(e.onPointerLeave,tt(d=>{f();const v=n.content?.getBoundingClientRect();if(v){const h=n.content?.dataset.side,w=h==="right",p=w?-5:5,g=v[w?"left":"right"],y=v[w?"right":"left"];s.onPointerGraceIntentChange({area:[{x:d.clientX+p,y:d.clientY},{x:g,y:v.top},{x:y,y:v.top},{x:y,y:v.bottom},{x:g,y:v.bottom}],side:h}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(d),d.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:R(e.onKeyDown,d=>{const v=s.searchRef.current!=="";e.disabled||v&&d.key===" "||Wl[o.dir].includes(d.key)&&(n.onOpenChange(!0),n.content?.focus(),d.preventDefault())})})})});ps.displayName=qe;var ms="MenuSubContent",vs=i.forwardRef((e,t)=>{const n=Qr(re,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,s=je(re,e.__scopeMenu),a=lt(re,e.__scopeMenu),c=fs(ms,e.__scopeMenu),l=i.useRef(null),u=F(t,l);return m.jsx(et.Provider,{scope:e.__scopeMenu,children:m.jsx(ee,{present:o||s.open,children:m.jsx(et.Slot,{scope:e.__scopeMenu,children:m.jsx(zn,{id:c.contentId,"aria-labelledby":c.triggerId,...r,ref:u,align:"start",side:a.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{a.isUsingKeyboardRef.current&&l.current?.focus(),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:R(e.onFocusOutside,f=>{f.target!==c.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:R(e.onEscapeKeyDown,f=>{a.onClose(),f.preventDefault()}),onKeyDown:R(e.onKeyDown,f=>{const d=f.currentTarget.contains(f.target),v=Vl[a.dir].includes(f.key);d&&v&&(s.onOpenChange(!1),c.trigger?.focus(),f.preventDefault())})})})})})});vs.displayName=ms;function hs(e){return e?"open":"closed"}function Pt(e){return e==="indeterminate"}function qn(e){return Pt(e)?"indeterminate":e?"checked":"unchecked"}function cu(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function lu(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function uu(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=lu(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function du(e,t){const{x:n,y:o}=e;let r=!1;for(let s=0,a=t.length-1;s<t.length;a=s++){const c=t[s],l=t[a],u=c.x,f=c.y,d=l.x,v=l.y;f>o!=v>o&&n<(d-u)*(o-f)/(v-f)+u&&(r=!r)}return r}function fu(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return du(n,t)}function tt(e){return t=>t.pointerType==="mouse"?e(t):void 0}var pu=Zr,mu=Un,vu=Jr,hu=es,gu=Yn,wu=ts,xu=Vt,yu=os,Cu=ss,bu=is,Su=ls,Eu=us,Ru=ds,Pu=ps,Au=vs,Ht="DropdownMenu",[Mu,Xd]=se(Ht,[Xr]),q=Xr(),[_u,gs]=Mu(Ht),ws=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:s,onOpenChange:a,modal:c=!0}=e,l=q(t),u=i.useRef(null),[f,d]=xe({prop:r,defaultProp:s??!1,onChange:a,caller:Ht});return m.jsx(_u,{scope:t,triggerId:oe(),triggerRef:u,contentId:oe(),open:f,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(v=>!v),[d]),modal:c,children:m.jsx(pu,{...l,open:f,onOpenChange:d,dir:o,modal:c,children:n})})};ws.displayName=Ht;var xs="DropdownMenuTrigger",ys=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,s=gs(xs,n),a=q(n);return m.jsx(mu,{asChild:!0,...a,children:m.jsx(T.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:Mt(t,s.triggerRef),onPointerDown:R(e.onPointerDown,c=>{!o&&c.button===0&&c.ctrlKey===!1&&(s.onOpenToggle(),s.open||c.preventDefault())}),onKeyDown:R(e.onKeyDown,c=>{o||(["Enter"," "].includes(c.key)&&s.onOpenToggle(),c.key==="ArrowDown"&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});ys.displayName=xs;var Iu="DropdownMenuPortal",Cs=e=>{const{__scopeDropdownMenu:t,...n}=e,o=q(t);return m.jsx(vu,{...o,...n})};Cs.displayName=Iu;var bs="DropdownMenuContent",Ss=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=gs(bs,n),s=q(n),a=i.useRef(!1);return m.jsx(hu,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:t,onCloseAutoFocus:R(e.onCloseAutoFocus,c=>{a.current||r.triggerRef.current?.focus(),a.current=!1,c.preventDefault()}),onInteractOutside:R(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,f=l.button===2||u;(!r.modal||f)&&(a.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Ss.displayName=bs;var Tu="DropdownMenuGroup",Es=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(gu,{...r,...o,ref:t})});Es.displayName=Tu;var Ou="DropdownMenuLabel",Rs=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(wu,{...r,...o,ref:t})});Rs.displayName=Ou;var Nu="DropdownMenuItem",Ps=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(xu,{...r,...o,ref:t})});Ps.displayName=Nu;var Du="DropdownMenuCheckboxItem",As=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(yu,{...r,...o,ref:t})});As.displayName=Du;var ku="DropdownMenuRadioGroup",Lu=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Cu,{...r,...o,ref:t})});Lu.displayName=ku;var ju="DropdownMenuRadioItem",Fu=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(bu,{...r,...o,ref:t})});Fu.displayName=ju;var $u="DropdownMenuItemIndicator",Ms=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Su,{...r,...o,ref:t})});Ms.displayName=$u;var Bu="DropdownMenuSeparator",_s=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Eu,{...r,...o,ref:t})});_s.displayName=Bu;var Wu="DropdownMenuArrow",Vu=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Ru,{...r,...o,ref:t})});Vu.displayName=Wu;var Hu="DropdownMenuSubTrigger",Uu=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Pu,{...r,...o,ref:t})});Uu.displayName=Hu;var Ku="DropdownMenuSubContent",Gu=i.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=q(n);return m.jsx(Au,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Gu.displayName=Ku;var qd=ws,Zd=ys,Qd=Cs,Jd=Ss,ef=Es,tf=Rs,nf=Ps,of=As,rf=Ms,sf=_s,zu="Label",Is=i.forwardRef((e,t)=>m.jsx(T.label,{...e,ref:t,onMouseDown:n=>{n.target.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));Is.displayName=zu;var af=Is;function Po(e,[t,n]){return Math.min(n,Math.max(t,e))}var Ut="Popover",[Ts,cf]=se(Ut,[Ye]),ut=Ye(),[Yu,Ae]=Ts(Ut),Os=e=>{const{__scopePopover:t,children:n,open:o,defaultOpen:r,onOpenChange:s,modal:a=!1}=e,c=ut(t),l=i.useRef(null),[u,f]=i.useState(!1),[d,v]=xe({prop:o,defaultProp:r??!1,onChange:s,caller:Ut});return m.jsx(Wn,{...c,children:m.jsx(Yu,{scope:t,contentId:oe(),triggerRef:l,open:d,onOpenChange:v,onOpenToggle:i.useCallback(()=>v(h=>!h),[v]),hasCustomAnchor:u,onCustomAnchorAdd:i.useCallback(()=>f(!0),[]),onCustomAnchorRemove:i.useCallback(()=>f(!1),[]),modal:a,children:n})})};Os.displayName=Ut;var Ns="PopoverAnchor",Xu=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=Ae(Ns,n),s=ut(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:c}=r;return i.useEffect(()=>(a(),()=>c()),[a,c]),m.jsx($t,{...s,...o,ref:t})});Xu.displayName=Ns;var Ds="PopoverTrigger",ks=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=Ae(Ds,n),s=ut(n),a=F(t,r.triggerRef),c=m.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":Bs(r.open),...o,ref:a,onClick:R(e.onClick,r.onOpenToggle)});return r.hasCustomAnchor?c:m.jsx($t,{asChild:!0,...s,children:c})});ks.displayName=Ds;var Zn="PopoverPortal",[qu,Zu]=Ts(Zn,{forceMount:void 0}),Ls=e=>{const{__scopePopover:t,forceMount:n,children:o,container:r}=e,s=Ae(Zn,t);return m.jsx(qu,{scope:t,forceMount:n,children:m.jsx(ee,{present:n||s.open,children:m.jsx(rt,{asChild:!0,container:r,children:o})})})};Ls.displayName=Zn;var Ue="PopoverContent",js=i.forwardRef((e,t)=>{const n=Zu(Ue,e.__scopePopover),{forceMount:o=n.forceMount,...r}=e,s=Ae(Ue,e.__scopePopover);return m.jsx(ee,{present:o||s.open,children:s.modal?m.jsx(Ju,{...r,ref:t}):m.jsx(ed,{...r,ref:t})})});js.displayName=Ue;var Qu=Ee("PopoverContent.RemoveScroll"),Ju=i.forwardRef((e,t)=>{const n=Ae(Ue,e.__scopePopover),o=i.useRef(null),r=F(t,o),s=i.useRef(!1);return i.useEffect(()=>{const a=o.current;if(a)return Nt(a)},[]),m.jsx(st,{as:Qu,allowPinchZoom:!0,children:m.jsx(Fs,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:R(e.onCloseAutoFocus,a=>{a.preventDefault(),s.current||n.triggerRef.current?.focus()}),onPointerDownOutside:R(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0,u=c.button===2||l;s.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:R(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1})})})}),ed=i.forwardRef((e,t)=>{const n=Ae(Ue,e.__scopePopover),o=i.useRef(!1),r=i.useRef(!1);return m.jsx(Fs,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(o.current||n.triggerRef.current?.focus(),s.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(o.current=!0,s.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const a=s.target;n.triggerRef.current?.contains(a)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&r.current&&s.preventDefault()}})}),Fs=i.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:s,disableOutsidePointerEvents:a,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:f,...d}=e,v=Ae(Ue,n),h=ut(n);return Tt(),m.jsx(ot,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:s,children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>v.onOpenChange(!1),children:m.jsx(Vn,{"data-state":Bs(v.open),role:"dialog",id:v.contentId,...h,...d,ref:t,style:{...d.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),$s="PopoverClose",td=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=Ae($s,n);return m.jsx(T.button,{type:"button",...o,ref:t,onClick:R(e.onClick,()=>r.onOpenChange(!1))})});td.displayName=$s;var nd="PopoverArrow",od=i.forwardRef((e,t)=>{const{__scopePopover:n,...o}=e,r=ut(n);return m.jsx(Hn,{...r,...o,ref:t})});od.displayName=nd;function Bs(e){return e?"open":"closed"}var lf=Os,uf=ks,df=Ls,ff=js,rd=[" ","Enter","ArrowUp","ArrowDown"],sd=[" ","Enter"],De="Select",[Kt,Gt,ad]=Sn(De),[Xe,pf]=se(De,[ad,Ye]),zt=Ye(),[id,Me]=Xe(De),[cd,ld]=Xe(De),Ws=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:r,onOpenChange:s,value:a,defaultValue:c,onValueChange:l,dir:u,name:f,autoComplete:d,disabled:v,required:h,form:w}=e,p=zt(t),[g,y]=i.useState(null),[x,C]=i.useState(null),[b,S]=i.useState(!1),_=It(u),[E,M]=xe({prop:o,defaultProp:r??!1,onChange:s,caller:De}),[D,N]=xe({prop:a,defaultProp:c,onChange:l,caller:De}),$=i.useRef(null),B=g?w||!!g.closest("form"):!0,[L,k]=i.useState(new Set),W=Array.from(L).map(O=>O.props.value).join(";");return m.jsx(Wn,{...p,children:m.jsxs(id,{required:h,scope:t,trigger:g,onTriggerChange:y,valueNode:x,onValueNodeChange:C,valueNodeHasChildren:b,onValueNodeHasChildrenChange:S,contentId:oe(),value:D,onValueChange:N,open:E,onOpenChange:M,dir:_,triggerPointerDownPosRef:$,disabled:v,children:[m.jsx(Kt.Provider,{scope:t,children:m.jsx(cd,{scope:e.__scopeSelect,onNativeOptionAdd:i.useCallback(O=>{k(j=>new Set(j).add(O))},[]),onNativeOptionRemove:i.useCallback(O=>{k(j=>{const I=new Set(j);return I.delete(O),I})},[]),children:n})}),B?m.jsxs(fa,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:d,value:D,onChange:O=>N(O.target.value),disabled:v,form:w,children:[D===void 0?m.jsx("option",{value:""}):null,Array.from(L)]},W):null]})})};Ws.displayName=De;var Vs="SelectTrigger",Hs=i.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...r}=e,s=zt(n),a=Me(Vs,n),c=a.disabled||o,l=F(t,a.onTriggerChange),u=Gt(n),f=i.useRef("touch"),[d,v,h]=ma(p=>{const g=u().filter(C=>!C.disabled),y=g.find(C=>C.value===a.value),x=va(g,p,y);x!==void 0&&a.onValueChange(x.value)}),w=p=>{c||(a.onOpenChange(!0),h()),p&&(a.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return m.jsx($t,{asChild:!0,...s,children:m.jsx(T.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":pa(a.value)?"":void 0,...r,ref:l,onClick:R(r.onClick,p=>{p.currentTarget.focus(),f.current!=="mouse"&&w(p)}),onPointerDown:R(r.onPointerDown,p=>{f.current=p.pointerType;const g=p.target;g.hasPointerCapture(p.pointerId)&&g.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(w(p),p.preventDefault())}),onKeyDown:R(r.onKeyDown,p=>{const g=d.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&v(p.key),!(g&&p.key===" ")&&rd.includes(p.key)&&(w(),p.preventDefault())})})})});Hs.displayName=Vs;var Us="SelectValue",Ks=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,children:s,placeholder:a="",...c}=e,l=Me(Us,n),{onValueNodeHasChildrenChange:u}=l,f=s!==void 0,d=F(t,l.onValueNodeChange);return z(()=>{u(f)},[u,f]),m.jsx(T.span,{...c,ref:d,style:{pointerEvents:"none"},children:pa(l.value)?m.jsx(m.Fragment,{children:a}):s})});Ks.displayName=Us;var ud="SelectIcon",Gs=i.forwardRef((e,t)=>{const{__scopeSelect:n,children:o,...r}=e;return m.jsx(T.span,{"aria-hidden":!0,...r,ref:t,children:o||"▼"})});Gs.displayName=ud;var dd="SelectPortal",zs=e=>m.jsx(rt,{asChild:!0,...e});zs.displayName=dd;var ke="SelectContent",Ys=i.forwardRef((e,t)=>{const n=Me(ke,e.__scopeSelect),[o,r]=i.useState();if(z(()=>{r(new DocumentFragment)},[]),!n.open){const s=o;return s?nt.createPortal(m.jsx(Xs,{scope:e.__scopeSelect,children:m.jsx(Kt.Slot,{scope:e.__scopeSelect,children:m.jsx("div",{children:e.children})})}),s):null}return m.jsx(qs,{...e,ref:t})});Ys.displayName=ke;var ie=10,[Xs,_e]=Xe(ke),fd="SelectContentImpl",pd=Ee("SelectContent.RemoveScroll"),qs=i.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:r,onEscapeKeyDown:s,onPointerDownOutside:a,side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g,...y}=e,x=Me(ke,n),[C,b]=i.useState(null),[S,_]=i.useState(null),E=F(t,A=>b(A)),[M,D]=i.useState(null),[N,$]=i.useState(null),B=Gt(n),[L,k]=i.useState(!1),W=i.useRef(!1);i.useEffect(()=>{if(C)return Nt(C)},[C]),Tt();const O=i.useCallback(A=>{const[V,...X]=B().map(G=>G.ref.current),[H]=X.slice(-1),U=document.activeElement;for(const G of A)if(G===U||(G?.scrollIntoView({block:"nearest"}),G===V&&S&&(S.scrollTop=0),G===H&&S&&(S.scrollTop=S.scrollHeight),G?.focus(),document.activeElement!==U))return},[B,S]),j=i.useCallback(()=>O([M,C]),[O,M,C]);i.useEffect(()=>{L&&j()},[L,j]);const{onOpenChange:I,triggerPointerDownPosRef:P}=x;i.useEffect(()=>{if(C){let A={x:0,y:0};const V=H=>{A={x:Math.abs(Math.round(H.pageX)-(P.current?.x??0)),y:Math.abs(Math.round(H.pageY)-(P.current?.y??0))}},X=H=>{A.x<=10&&A.y<=10?H.preventDefault():C.contains(H.target)||I(!1),document.removeEventListener("pointermove",V),P.current=null};return P.current!==null&&(document.addEventListener("pointermove",V),document.addEventListener("pointerup",X,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",V),document.removeEventListener("pointerup",X,{capture:!0})}}},[C,I,P]),i.useEffect(()=>{const A=()=>I(!1);return window.addEventListener("blur",A),window.addEventListener("resize",A),()=>{window.removeEventListener("blur",A),window.removeEventListener("resize",A)}},[I]);const[K,Y]=ma(A=>{const V=B().filter(U=>!U.disabled),X=V.find(U=>U.ref.current===document.activeElement),H=va(V,A,X);H&&setTimeout(()=>H.ref.current.focus())}),ae=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(x.value!==void 0&&x.value===V||H)&&(D(A),H&&(W.current=!0))},[x.value]),ge=i.useCallback(()=>C?.focus(),[C]),te=i.useCallback((A,V,X)=>{const H=!W.current&&!X;(x.value!==void 0&&x.value===V||H)&&$(A)},[x.value]),we=o==="popper"?wn:Zs,Z=we===wn?{side:c,sideOffset:l,align:u,alignOffset:f,arrowPadding:d,collisionBoundary:v,collisionPadding:h,sticky:w,hideWhenDetached:p,avoidCollisions:g}:{};return m.jsx(Xs,{scope:n,content:C,viewport:S,onViewportChange:_,itemRefCallback:ae,selectedItem:M,onItemLeave:ge,itemTextRefCallback:te,focusSelectedItem:j,selectedItemText:N,position:o,isPositioned:L,searchRef:K,children:m.jsx(st,{as:pd,allowPinchZoom:!0,children:m.jsx(ot,{asChild:!0,trapped:x.open,onMountAutoFocus:A=>{A.preventDefault()},onUnmountAutoFocus:R(r,A=>{x.trigger?.focus({preventScroll:!0}),A.preventDefault()}),children:m.jsx(Ke,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:a,onFocusOutside:A=>A.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:m.jsx(we,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:A=>A.preventDefault(),...y,...Z,onPlaced:()=>k(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:R(y.onKeyDown,A=>{const V=A.ctrlKey||A.altKey||A.metaKey;if(A.key==="Tab"&&A.preventDefault(),!V&&A.key.length===1&&Y(A.key),["ArrowUp","ArrowDown","Home","End"].includes(A.key)){let H=B().filter(U=>!U.disabled).map(U=>U.ref.current);if(["ArrowUp","End"].includes(A.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(A.key)){const U=A.target,G=H.indexOf(U);H=H.slice(G+1)}setTimeout(()=>O(H)),A.preventDefault()}})})})})})})});qs.displayName=fd;var md="SelectItemAlignedPosition",Zs=i.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...r}=e,s=Me(ke,n),a=_e(ke,n),[c,l]=i.useState(null),[u,f]=i.useState(null),d=F(t,E=>f(E)),v=Gt(n),h=i.useRef(!1),w=i.useRef(!0),{viewport:p,selectedItem:g,selectedItemText:y,focusSelectedItem:x}=a,C=i.useCallback(()=>{if(s.trigger&&s.valueNode&&c&&u&&p&&g&&y){const E=s.trigger.getBoundingClientRect(),M=u.getBoundingClientRect(),D=s.valueNode.getBoundingClientRect(),N=y.getBoundingClientRect();if(s.dir!=="rtl"){const U=N.left-M.left,G=D.left-U,ne=E.left-G,Ie=E.width+ne,Xt=Math.max(Ie,M.width),qt=window.innerWidth-ie,Zt=Po(G,[ie,Math.max(ie,qt-Xt)]);c.style.minWidth=Ie+"px",c.style.left=Zt+"px"}else{const U=M.right-N.right,G=window.innerWidth-D.right-U,ne=window.innerWidth-E.right-G,Ie=E.width+ne,Xt=Math.max(Ie,M.width),qt=window.innerWidth-ie,Zt=Po(G,[ie,Math.max(ie,qt-Xt)]);c.style.minWidth=Ie+"px",c.style.right=Zt+"px"}const $=v(),B=window.innerHeight-ie*2,L=p.scrollHeight,k=window.getComputedStyle(u),W=parseInt(k.borderTopWidth,10),O=parseInt(k.paddingTop,10),j=parseInt(k.borderBottomWidth,10),I=parseInt(k.paddingBottom,10),P=W+O+L+I+j,K=Math.min(g.offsetHeight*5,P),Y=window.getComputedStyle(p),ae=parseInt(Y.paddingTop,10),ge=parseInt(Y.paddingBottom,10),te=E.top+E.height/2-ie,we=B-te,Z=g.offsetHeight/2,A=g.offsetTop+Z,V=W+O+A,X=P-V;if(V<=te){const U=$.length>0&&g===$[$.length-1].ref.current;c.style.bottom="0px";const G=u.clientHeight-p.offsetTop-p.offsetHeight,ne=Math.max(we,Z+(U?ge:0)+G+j),Ie=V+ne;c.style.height=Ie+"px"}else{const U=$.length>0&&g===$[0].ref.current;c.style.top="0px";const ne=Math.max(te,W+p.offsetTop+(U?ae:0)+Z)+X;c.style.height=ne+"px",p.scrollTop=V-te+p.offsetTop}c.style.margin=`${ie}px 0`,c.style.minHeight=K+"px",c.style.maxHeight=B+"px",o?.(),requestAnimationFrame(()=>h.current=!0)}},[v,s.trigger,s.valueNode,c,u,p,g,y,s.dir,o]);z(()=>C(),[C]);const[b,S]=i.useState();z(()=>{u&&S(window.getComputedStyle(u).zIndex)},[u]);const _=i.useCallback(E=>{E&&w.current===!0&&(C(),x?.(),w.current=!1)},[C,x]);return m.jsx(hd,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:_,children:m.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:b},children:m.jsx(T.div,{...r,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...r.style}})})})});Zs.displayName=md;var vd="SelectPopperPosition",wn=i.forwardRef((e,t)=>{const{__scopeSelect:n,align:o="start",collisionPadding:r=ie,...s}=e,a=zt(n);return m.jsx(Vn,{...a,...s,ref:t,align:o,collisionPadding:r,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});wn.displayName=vd;var[hd,Qn]=Xe(ke,{}),xn="SelectViewport",Qs=i.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...r}=e,s=_e(xn,n),a=Qn(xn,n),c=F(t,s.onViewportChange),l=i.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),m.jsx(Kt.Slot,{scope:n,children:m.jsx(T.div,{"data-radix-select-viewport":"",role:"presentation",...r,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...r.style},onScroll:R(r.onScroll,u=>{const f=u.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:v}=a;if(v?.current&&d){const h=Math.abs(l.current-f.scrollTop);if(h>0){const w=window.innerHeight-ie*2,p=parseFloat(d.style.minHeight),g=parseFloat(d.style.height),y=Math.max(p,g);if(y<w){const x=y+h,C=Math.min(w,x),b=x-C;d.style.height=C+"px",d.style.bottom==="0px"&&(f.scrollTop=b>0?b:0,d.style.justifyContent="flex-end")}}}l.current=f.scrollTop})})})]})});Qs.displayName=xn;var Js="SelectGroup",[gd,wd]=Xe(Js),ea=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=oe();return m.jsx(gd,{scope:n,id:r,children:m.jsx(T.div,{role:"group","aria-labelledby":r,...o,ref:t})})});ea.displayName=Js;var ta="SelectLabel",na=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=wd(ta,n);return m.jsx(T.div,{id:r.id,...o,ref:t})});na.displayName=ta;var At="SelectItem",[xd,oa]=Xe(At),ra=i.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:r=!1,textValue:s,...a}=e,c=Me(At,n),l=_e(At,n),u=c.value===o,[f,d]=i.useState(s??""),[v,h]=i.useState(!1),w=F(t,x=>l.itemRefCallback?.(x,o,r)),p=oe(),g=i.useRef("touch"),y=()=>{r||(c.onValueChange(o),c.onOpenChange(!1))};if(o==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(xd,{scope:n,value:o,disabled:r,textId:p,isSelected:u,onItemTextChange:i.useCallback(x=>{d(C=>C||(x?.textContent??"").trim())},[]),children:m.jsx(Kt.ItemSlot,{scope:n,value:o,disabled:r,textValue:f,children:m.jsx(T.div,{role:"option","aria-labelledby":p,"data-highlighted":v?"":void 0,"aria-selected":u&&v,"data-state":u?"checked":"unchecked","aria-disabled":r||void 0,"data-disabled":r?"":void 0,tabIndex:r?void 0:-1,...a,ref:w,onFocus:R(a.onFocus,()=>h(!0)),onBlur:R(a.onBlur,()=>h(!1)),onClick:R(a.onClick,()=>{g.current!=="mouse"&&y()}),onPointerUp:R(a.onPointerUp,()=>{g.current==="mouse"&&y()}),onPointerDown:R(a.onPointerDown,x=>{g.current=x.pointerType}),onPointerMove:R(a.onPointerMove,x=>{g.current=x.pointerType,r?l.onItemLeave?.():g.current==="mouse"&&x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:R(a.onPointerLeave,x=>{x.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:R(a.onKeyDown,x=>{l.searchRef?.current!==""&&x.key===" "||(sd.includes(x.key)&&y(),x.key===" "&&x.preventDefault())})})})})});ra.displayName=At;var Ze="SelectItemText",sa=i.forwardRef((e,t)=>{const{__scopeSelect:n,className:o,style:r,...s}=e,a=Me(Ze,n),c=_e(Ze,n),l=oa(Ze,n),u=ld(Ze,n),[f,d]=i.useState(null),v=F(t,y=>d(y),l.onItemTextChange,y=>c.itemTextRefCallback?.(y,l.value,l.disabled)),h=f?.textContent,w=i.useMemo(()=>m.jsx("option",{value:l.value,disabled:l.disabled,children:h},l.value),[l.disabled,l.value,h]),{onNativeOptionAdd:p,onNativeOptionRemove:g}=u;return z(()=>(p(w),()=>g(w)),[p,g,w]),m.jsxs(m.Fragment,{children:[m.jsx(T.span,{id:l.textId,...s,ref:v}),l.isSelected&&a.valueNode&&!a.valueNodeHasChildren?nt.createPortal(s.children,a.valueNode):null]})});sa.displayName=Ze;var aa="SelectItemIndicator",ia=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return oa(aa,n).isSelected?m.jsx(T.span,{"aria-hidden":!0,...o,ref:t}):null});ia.displayName=aa;var yn="SelectScrollUpButton",ca=i.forwardRef((e,t)=>{const n=_e(yn,e.__scopeSelect),o=Qn(yn,e.__scopeSelect),[r,s]=i.useState(!1),a=F(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollTop>0;s(u)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?m.jsx(ua,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});ca.displayName=yn;var Cn="SelectScrollDownButton",la=i.forwardRef((e,t)=>{const n=_e(Cn,e.__scopeSelect),o=Qn(Cn,e.__scopeSelect),[r,s]=i.useState(!1),a=F(t,o.onScrollButtonChange);return z(()=>{if(n.viewport&&n.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,f=Math.ceil(l.scrollTop)<u;s(f)};const l=n.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[n.viewport,n.isPositioned]),r?m.jsx(ua,{...e,ref:a,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=n;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});la.displayName=Cn;var ua=i.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...r}=e,s=_e("SelectScrollButton",n),a=i.useRef(null),c=Gt(n),l=i.useCallback(()=>{a.current!==null&&(window.clearInterval(a.current),a.current=null)},[]);return i.useEffect(()=>()=>l(),[l]),z(()=>{c().find(f=>f.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[c]),m.jsx(T.div,{"aria-hidden":!0,...r,ref:t,style:{flexShrink:0,...r.style},onPointerDown:R(r.onPointerDown,()=>{a.current===null&&(a.current=window.setInterval(o,50))}),onPointerMove:R(r.onPointerMove,()=>{s.onItemLeave?.(),a.current===null&&(a.current=window.setInterval(o,50))}),onPointerLeave:R(r.onPointerLeave,()=>{l()})})}),yd="SelectSeparator",da=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e;return m.jsx(T.div,{"aria-hidden":!0,...o,ref:t})});da.displayName=yd;var bn="SelectArrow",Cd=i.forwardRef((e,t)=>{const{__scopeSelect:n,...o}=e,r=zt(n),s=Me(bn,n),a=_e(bn,n);return s.open&&a.position==="popper"?m.jsx(Hn,{...r,...o,ref:t}):null});Cd.displayName=bn;var bd="SelectBubbleInput",fa=i.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{const r=i.useRef(null),s=F(o,r),a=pr(t);return i.useEffect(()=>{const c=r.current;if(!c)return;const l=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(l,"value").set;if(a!==t&&f){const d=new Event("change",{bubbles:!0});f.call(c,t),c.dispatchEvent(d)}},[a,t]),m.jsx(T.select,{...n,style:{...Io,...n.style},ref:s,defaultValue:t})});fa.displayName=bd;function pa(e){return e===""||e===void 0}function ma(e){const t=ce(e),n=i.useRef(""),o=i.useRef(0),r=i.useCallback(a=>{const c=n.current+a;t(c),function l(u){n.current=u,window.clearTimeout(o.current),u!==""&&(o.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),s=i.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return i.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,r,s]}function va(e,t,n){const r=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let a=Sd(e,Math.max(s,0));r.length===1&&(a=a.filter(u=>u!==n));const l=a.find(u=>u.textValue.toLowerCase().startsWith(r.toLowerCase()));return l!==n?l:void 0}function Sd(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var mf=Ws,vf=Hs,hf=Ks,gf=Gs,wf=zs,xf=Ys,yf=Qs,Cf=ea,bf=na,Sf=ra,Ef=sa,Rf=ia,Pf=ca,Af=la,Mf=da,Yt="Tabs",[Ed,_f]=se(Yt,[Bt]),ha=Bt(),[Rd,Jn]=Ed(Yt),ga=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,onValueChange:r,defaultValue:s,orientation:a="horizontal",dir:c,activationMode:l="automatic",...u}=e,f=It(c),[d,v]=xe({prop:o,onChange:r,defaultProp:s??"",caller:Yt});return m.jsx(Rd,{scope:n,baseId:oe(),value:d,onValueChange:v,orientation:a,dir:f,activationMode:l,children:m.jsx(T.div,{dir:f,"data-orientation":a,...u,ref:t})})});ga.displayName=Yt;var wa="TabsList",xa=i.forwardRef((e,t)=>{const{__scopeTabs:n,loop:o=!0,...r}=e,s=Jn(wa,n),a=ha(n);return m.jsx(Gr,{asChild:!0,...a,orientation:s.orientation,dir:s.dir,loop:o,children:m.jsx(T.div,{role:"tablist","aria-orientation":s.orientation,...r,ref:t})})});xa.displayName=wa;var ya="TabsTrigger",Ca=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,disabled:r=!1,...s}=e,a=Jn(ya,n),c=ha(n),l=Ea(a.baseId,o),u=Ra(a.baseId,o),f=o===a.value;return m.jsx(zr,{asChild:!0,...c,focusable:!r,active:f,children:m.jsx(T.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:l,...s,ref:t,onMouseDown:R(e.onMouseDown,d=>{!r&&d.button===0&&d.ctrlKey===!1?a.onValueChange(o):d.preventDefault()}),onKeyDown:R(e.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&a.onValueChange(o)}),onFocus:R(e.onFocus,()=>{const d=a.activationMode!=="manual";!f&&!r&&d&&a.onValueChange(o)})})})});Ca.displayName=ya;var ba="TabsContent",Sa=i.forwardRef((e,t)=>{const{__scopeTabs:n,value:o,forceMount:r,children:s,...a}=e,c=Jn(ba,n),l=Ea(c.baseId,o),u=Ra(c.baseId,o),f=o===c.value,d=i.useRef(f);return i.useEffect(()=>{const v=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(v)},[]),m.jsx(ee,{present:r||f,children:({present:v})=>m.jsx(T.div,{"data-state":f?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!v,id:u,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:d.current?"0s":void 0},children:v&&s})})});Sa.displayName=ba;function Ea(e,t){return`${e}-trigger-${t}`}function Ra(e,t){return`${e}-content-${t}`}var If=ga,Tf=xa,Of=Ca,Nf=Sa;export{_a as $,$t as A,Nd as B,Nf as C,Ke as D,wf as E,xf as F,yf as G,Sf as H,gf as I,Rf as J,Ef as K,Tf as L,Pf as M,Af as N,$d as O,T as P,Cf as Q,If as R,Ad as S,Of as T,bf as U,To as V,Mf as W,Td as X,Ya as Y,Xa as Z,Hd as _,Ld as a,Wd as a0,Vd as a1,Kd as a2,Gd as a3,qd as a4,Zd as a5,Qd as a6,Jd as a7,of as a8,rf as a9,tf as aa,nf as ab,sf as ac,ef as ad,af as ae,jd as af,lf as ag,uf as ah,df as ai,ff as aj,It as ak,Po as al,xc as am,yc as an,Bt as ao,Gr as ap,zr as aq,pr as ar,mr as as,Fd as b,Bd as c,Mt as d,Sn as e,se as f,xe as g,ee as h,R as i,ce as j,F as k,Od as l,rt as m,_o as n,z as o,Ye as p,Wn as q,nt as r,Hn as s,Vn as t,oe as u,Md as v,_d as w,mf as x,vf as y,hf as z};
//# sourceMappingURL=radix-e4nK4mWk.js.map
