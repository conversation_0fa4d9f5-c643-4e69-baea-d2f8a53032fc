import type { ApprovalStageDto, FilterCondition, FilterGroup, FilterOperator, LogicalOperator } from "@/client/types.gen";
import FilterSortBar, { type SortDirection } from "@/components/filter-sort-bar";
import { Button } from "@/components/ui/button";
import { DocumentPreviewDialog } from "@/components/ui/DocumentPreviewDialog";
import ErrorBoundary from "@/components/ui/error-boundary";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { useApprovalStages } from "@/lib/hooks/useApprovalStages";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
  type ColumnDef,
  type PaginationState
} from "@tanstack/react-table";
import { AlertTriangle, Eye, FileText, RefreshCw } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { ContentCard } from "../layout/content-card";

const FILTER_FIELDS = [
  { value: "jettyRequestItem.tenantName", label: "Tenant Name" },
  { value: "jettyRequestItem.itemName", label: "Item Name" },
  { value: "jettyRequestItem.jettyRequest.docNum", label: "Document Number" },
  { value: "jettyRequestItem.jettyRequest.vesselName", label: "Vessel Name" },
  { value: "jettyRequestItem.jettyRequest.vesselType", label: "Vessel Type" },
  { value: "requestDate", label: "Request Date" },
  { value: "status", label: "Status" },
  { value: "requesterUserName", label: "Requester" },
  { value: "approverUserName", label: "Approver" },
];

const FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [
  { value: "Equals", label: "Equals" },
  { value: "Contains", label: "Contains" },
  { value: "NotEquals", label: "Not Equals" },
  { value: "GreaterThan", label: ">" },
  { value: "LessThan", label: "<" },
];

const columns: ColumnDef<ApprovalStageDto>[] = [
  {
    accessorKey: "jettyRequestItem.tenantName",
    header: "Tenant",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.itemName",
    header: "Item Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.docNum",
    header: "Document Number",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.vesselName",
    header: "Vessel Name",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "jettyRequestItem.jettyRequest.vesselType",
    header: "Vessel Type",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "requestDate",
    header: "Request Date",
    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: info => {
      const status = info.getValue() as number;
      // Status mapping based on backend ApprovalStatus enum:
      // 0 = Pending, 1 = Approved, 2 = Rejected, 3 = Cancelled
      const statusText = status === 0 ? "Pending" :
        status === 1 ? "Approved" :
          status === 2 ? "Rejected" :
            status === 3 ? "Cancelled" : "Unknown";
      const statusColor = status === 0 ? "text-yellow-600" : // Pending
        status === 1 ? "text-green-600" : // Approved
          status === 2 ? "text-red-600" : // Rejected
            status === 3 ? "text-gray-600" : "text-gray-400"; // Cancelled or Unknown
      return <span className={statusColor}>{statusText}</span>;
    },
  },
  {
    accessorKey: "requesterUserName",
    header: "Requester",
    cell: info => info.getValue() ?? "-",
  },
  {
    accessorKey: "approverUserName",
    header: "Approver",
    cell: info => info.getValue() ?? "-",
  },
  // View details button column
  {
    id: "view",
    header: "",
    cell: ({ row, table }) => {
      const approval = row.original;
      const tableMeta = table.options.meta as {
        handlePreview: (documentSrc: string) => void;
        getDocumentStreamUrl: (documentId: string) => string;
      };

      const handleView = () => {
        // TODO: Implement view details action
        console.log("View details:", approval.id);
      };

      const handlePreview = () => {
        // Use the new attachmentStreamUrls from the API response
        const streamUrls = approval.attachmentStreamUrls || [];
        if (streamUrls.length > 0) {
          // Use the first attachment stream URL
          const streamUrl = streamUrls[0];
          tableMeta.handlePreview(streamUrl);
        } else {
          // Fallback to the old method if no attachments
          const streamUrl = tableMeta.getDocumentStreamUrl(approval.documentId || "");
          if (streamUrl) {
            tableMeta.handlePreview(streamUrl);
          }
        }
      };

      const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === "Enter" || e.key === " ") {
          handleView();
        }
      };

      const handlePreviewKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === "Enter" || e.key === " ") {
          handlePreview();
        }
      };

      return (
        <div className="flex space-x-2">
          <Button
            onClick={handlePreview}
            onKeyDown={handlePreviewKeyDown}
            aria-label="Preview Document"
            tabIndex={0}
            variant="outline"
            size="icon"
            className="h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
            disabled={!approval.attachmentStreamUrls?.length && !approval.documentId}
          >
            <FileText className="w-4 h-4" aria-hidden="true" />
          </Button>
          <Button
            onClick={handleView}
            onKeyDown={handleKeyDown}
            aria-label="View Approval Details"
            tabIndex={0}
            variant="outline"
            size="icon"
            className="h-8 w-8"
          >
            <Eye className="w-4 h-4" aria-hidden="true" />
          </Button>
        </div>
      );
    },
    enableSorting: false,
    enableColumnFilter: false,
  },
];

const ApprovalTableContent: React.FC = () => {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });
  const [filters, setFilters] = useState<FilterCondition[]>([]);
  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // State for document preview dialog
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState("");

  // Helper function to handle document preview
  const handlePreview = (documentSrc: string) => {
    setPreviewDocumentSrc(documentSrc);
    setIsPreviewDialogOpen(true);
  };

  // Helper function to get document stream URL
  const getDocumentStreamUrl = (documentId: string) => {
    // Updated to match the backend URL pattern
    return `/api/attachment/stream/${documentId}`;
  };

  // Reset to first page when filters or sorts change
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [filters, sorts]);

  // Build filter group for backend
  const filterGroup: FilterGroup | undefined = useMemo(() => {
    if (!filters.length) return undefined;
    return {
      operator: "And" as LogicalOperator,
      conditions: filters,
    };
  }, [filters]);

  // Build sorting string for backend
  const sortingStr = useMemo(() => {
    if (!sorts.length) return undefined;
    return sorts.map(s => `${s.field} ${s.direction}`).join(", ");
  }, [sorts]);

  const { data, isLoading, error, refetch } = useApprovalStages(
    pagination.pageIndex,
    pagination.pageSize,
    filterGroup,
    sortingStr
  );

  const table = useReactTable({
    data: data?.items ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onPaginationChange: setPagination,
    state: {
      pagination,
    },
    meta: {
      handlePreview,
      getDocumentStreamUrl,
    },
  });

  // Refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } finally {
      setIsRefreshing(false);
    }
  };

  // Error UI
  if (error) {
    console.error("ApprovalStages error:", error);
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center">
        <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
        <h3 className="font-semibold text-destructive mb-2">Error loading data</h3>
        <p className="text-sm text-muted-foreground mb-4">{String(error.message)}</p>
        <Button onClick={() => window.location.reload()} variant="destructive">Retry</Button>
      </div>
    );
  }

  return (
    <ContentCard>
      {/* <div className="text-xl font-bold px-2 pt-2 pb-1">Approval Stages</div> */}
      <div className="mb-6">
        <h2 className="text-lg font-bold text-gray-800 dark:text-white">Approval Stages</h2>
        <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
      </div>
      <FilterSortBar
        filterFields={FILTER_FIELDS}
        operators={FILTER_OPERATORS}
        filters={filters}
        sorts={sorts}
        onFiltersChange={setFilters}
        onSortsChange={setSorts}
      >
        <div className="ml-auto flex items-center gap-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="icon"
            className="h-10 w-10"
            disabled={isLoading || isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </FilterSortBar>
      {isLoading ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {columns.map((column, index) => (
                <TableHead key={column.id || `column-${index}`}>
                  {typeof column.header === 'string' ? column.header : 'Loading...'}
                </TableHead>
              ))}
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Loading approval stages...
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No approval stages found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} to{" "}
          {Math.min(
            (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
            data?.totalCount || 0
          )}{" "}
          of {data?.totalCount || 0} results
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>

      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </ContentCard>
  );
};

const ApprovalTableNew: React.FC = () => {
  return (
    <ErrorBoundary>
      <ApprovalTableContent />
    </ErrorBoundary>
  );
};

export default ApprovalTableNew; 