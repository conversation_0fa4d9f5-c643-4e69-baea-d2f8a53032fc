import{j as r,u as x,h as E,f as N}from"./vendor-6tJeyfYI.js";import{F as m,A as S,u as j,M as g}from"./app-layout-rNt37hVL.js";import{E as b}from"./export-vessel-form-CVscznfP.js";import{t as n}from"./date-convert-rY_-W0p4.js";import{$ as P,q as w}from"./App-DnhJzTNn.js";import"./radix-e4nK4mWk.js";import"./FormField-AGj4WUYd.js";import"./input-DlXlkYlT.js";import"./ht-theme-main.min-DuylQxQp.js";import"./types-B5GxFm4f.js";import"./index.esm-BubGICDC.js";import"./multi-select-Dsa7V91B.js";import"./badge-DWaCYvGm.js";import"./command-BPGQPJw5.js";import"./popover-ChFN9yvN.js";import"./scroll-area-DuGBN-Ug.js";import"./useDebounce-B2N8e_3P.js";/* empty css                         */import"./attachment-dialog-D7s9nIdd.js";import"./dialog-BmEXyFlW.js";import"./table-BKSoE52x.js";import"./tabs-Dk-TLCdA.js";const F=()=>{const{t:o}=m(),{props:p}=w(),{toast:u}=j(),s=typeof p.id=="string"?p.id:void 0,v=x(),{data:e,isLoading:y,isError:I,error:c}=E({queryKey:["export-vessel",s],queryFn:async()=>s?(await g.getExportVesselWithItems(s)).data:null,enabled:!!s}),D=e?{docNum:e.docNum??"",voyage:e.voyage??"",vesselArrival:n(e.vesselArrival??""),vesselDeparture:n(e.vesselDeparture??""),vesselId:e.vesselId??"",jettyId:e.jettyId??"",portOriginId:e.portOriginId??"",destinationPortId:e.destinationPortId??"",postingDate:e.postingDate??"",asideDate:n(e.asideDate??""),castOfDate:n(e.castOfDate??"")}:{},h=e?.items?e.items.map(t=>({itemName:t.itemName??"",itemQty:t.itemQty??0,unitQty:t.unitQty??"",remarks:t.remarks??"",ajuNo:t.ajuNo??"",regDate:t.regDate?t.regDate:void 0,regNo:t.regNo??"",grossWeight:t.grossWeight??"",unitWeight:t.unitWeight??"",shippingInstructionNo:t.shippingInstructionNo??"",shippingInstructionDate:t.shippingInstructionDate?t.shippingInstructionDate:void 0,letterNo:t.letterNo??"",letterDate:t.letterDate?t.letterDate:void 0,status:t.status??"",regType:t.regType??"",attachments:t.attachments??[],tenant:t.tenantName??"",tenantId:t.tenantId??"",businessPartner:t.businessPartner?.name??"",businessPartnerId:t.businessPartnerId??"",concurrencyStamp:t.concurrencyStamp??void 0,id:t.id??void 0})):[],l=N({mutationFn:async({header:t,items:i})=>{if(!s)throw new Error("No ID provided");const a=await g.updateExportVessel(s,{...t,docStatus:t.docStatus??"Open",items:i.map(d=>({...d,createdBy:"",docType:"",isScan:"",isOriginal:"",isActive:!0,isDeleted:!1,isSend:"",isFeOri:"",isFeSend:"",isChange:"",isFeChange:"",isFeActive:"",deleted:"",isUrgent:"",tenantId:d.tenantId||"",businessPartnerId:d.businessPartnerId||""}))});if(a.error)throw new Error(a.error);return a.data},onSuccess:()=>{u({title:"Success",description:"Export vessel updated.",variant:"success"}),v.invalidateQueries({queryKey:["export-vessel",s]})},onError:t=>{u({title:t instanceof Error?t.message:t?.error?.message||"Error",description:t instanceof Error?void 0:t?.error?.details,variant:"destructive"})}}),f=async(t,i)=>{await l.mutateAsync({header:t,items:i})};return y?r.jsx("div",{children:"Loading..."}):I?r.jsxs("div",{children:["Error loading data: ",c instanceof Error?c.message:"Unknown error"]}):r.jsx(b,{mode:"edit",title:o("pages.vessel.edit.export"),initialHeader:D,initialItems:h,onSubmit:f,isSubmitting:l.isPending})};function Z(){const{t:o}=m();return r.jsxs(S,{children:[r.jsx(P,{title:o("pages.vessel.edit.export")}),r.jsx(F,{})]})}export{Z as default};
//# sourceMappingURL=page-DHLIR-YV.js.map
