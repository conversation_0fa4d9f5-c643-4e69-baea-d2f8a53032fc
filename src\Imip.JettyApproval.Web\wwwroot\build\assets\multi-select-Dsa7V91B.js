import{a as n,j as s}from"./vendor-6tJeyfYI.js";import{B as U}from"./badge-DWaCYvGm.js";import{B as F,l as j,X as P,C as G}from"./app-layout-rNt37hVL.js";import{C as X,a as _,b as z,c as H,d as J,e as w}from"./command-BPGQPJw5.js";import{P as Q,a as Z,b as ee}from"./popover-ChFN9yvN.js";import{S as le}from"./scroll-area-DuGBN-Ug.js";const oe=({options:d=[],value:a=[],onChange:o,placeholder:L="Select options",className:M,disabled:O=!1,maxHeight:g=300,mode:T="multiple",name:r,register:t,valueAsNumber:x=!1,searchValue:N,onSearchValueChange:q,isLoading:y=!1,onLoadMore:C,hasMore:B=!1,loadingText:I="Loading...",emptyText:R="No options found",showDescription:W=!1,onSelectionChange:p})=>{const[f,h]=n.useState(!1),[Y,$]=n.useState(""),S=N!==void 0?N:Y,V=q||$,c=n.useRef(null),v=n.useCallback(e=>{if(c.current){const l=c.current.querySelector("[data-radix-scroll-area-viewport]");l&&(e.preventDefault(),l.scrollTop+=e.deltaY)}},[]);n.useEffect(()=>{const e=c.current;if(f&&e)return e.addEventListener("wheel",v,{passive:!1}),()=>{e.removeEventListener("wheel",v)}},[f,v]);const u=T==="single",m=n.useMemo(()=>d.filter(e=>a.includes(e.value)).map(e=>({value:e.value,label:e.label,description:e.description,data:e.data})),[d,a]),E=n.useMemo(()=>d.filter(e=>a.includes(e.value)),[d,a]);n.useEffect(()=>{p&&p(E)},[E,p]),n.useEffect(()=>{if(t&&r){const e=a.length>0?u?x?Number(a[0]):a[0]:a:void 0;if(t(r),e!==void 0){const l={target:{name:r,value:e}};t(r).onChange(l)}}},[t,r,u,x]);const A=n.useCallback(e=>{if(u){if(o([e]),t&&r){const l={target:{name:r,value:x?Number(e):e}};t(r).onChange(l)}h(!1)}else{const l=a.includes(e)?a.filter(i=>i!==e):[...a,e];if(o(l),t&&r){const i={target:{name:r,value:l}};t(r).onChange(i)}}},[a,o,u,h,t,r,x]),k=n.useCallback((e,l)=>{l?.preventDefault(),l?.stopPropagation();const i=a.filter(b=>b!==e);if(o(i),t&&r){const b={target:{name:r,value:i.length>0?i:void 0}};t(r).onChange(b)}},[a,o,t,r]),D=n.useCallback(e=>{if(e?.preventDefault(),e?.stopPropagation(),o([]),t&&r){const l={target:{name:r,value:void 0}};t(r).onChange(l)}},[o,t,r]),K=()=>{if(m.length===0)return s.jsx("span",{children:L});if(u&&m.length>0){const e=m[0]?.label??"";return s.jsx("span",{className:"text-foreground",children:e})}return s.jsx("div",{className:"flex flex-wrap gap-1 w-full",children:m.map(e=>s.jsxs(U,{variant:"secondary",className:"mr-1 mb-1 max-w-full overflow-hidden text-ellipsis whitespace-nowrap",children:[s.jsx("span",{className:"truncate",children:e.label}),s.jsx("span",{className:"ml-1 rounded-full outline-none hover:bg-muted cursor-pointer inline-flex items-center flex-shrink-0",onKeyDown:l=>{l.key==="Enter"&&k(e.value)},onMouseDown:l=>{l.preventDefault(),l.stopPropagation()},onClick:l=>k(e.value,l),role:"button",tabIndex:0,"aria-label":`Remove ${e.label}`,children:s.jsx(P,{className:"h-3 w-3"})})]},e.value))})};return s.jsxs(Q,{open:f,onOpenChange:h,children:[s.jsx(Z,{asChild:!0,children:s.jsxs(F,{variant:"outline",role:"combobox","aria-expanded":f,className:j("w-full justify-between h-8 px-3 text-[0.8125rem] rounded",!a.length&&"text-muted-foreground",M),onClick:()=>h(!f),disabled:O,children:[s.jsx("div",{className:"flex flex-wrap gap-1 items-center w-full mr-2",children:K()}),s.jsxs("div",{className:"flex items-center flex-shrink-0",children:[m.length>0&&s.jsx("span",{className:"mr-1 rounded-full outline-none hover:bg-muted p-0.5 cursor-pointer inline-flex items-center",onKeyDown:e=>{e.key==="Enter"&&D()},onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},onClick:e=>D(e),role:"button",tabIndex:0,"aria-label":"Clear all selections",children:s.jsx(P,{className:"h-4 w-4"})}),s.jsx(X,{className:"h-4 w-4 shrink-0 opacity-50"})]})]})}),s.jsx(ee,{className:"p-0 w-[var(--radix-popover-trigger-width)] overflow-hidden",align:"start",sideOffset:5,onWheel:e=>{if(c.current){const l=c.current.querySelector("[data-radix-scroll-area-viewport]");l&&(l.scrollTop+=e.deltaY)}},children:s.jsxs(_,{shouldFilter:!1,className:"max-h-full",onWheel:e=>{if(c.current){const l=c.current.querySelector("[data-radix-scroll-area-viewport]");l&&(l.scrollTop+=e.deltaY)}},children:[s.jsx(z,{placeholder:"Search...",value:S,onValueChange:V,className:"h-9"}),s.jsx(H,{children:y?I:R}),s.jsx(le,{className:"overflow-hidden h-full custom-scrollbar",style:{height:`${g-40}px`,maxHeight:`${g-40}px`},ref:c,children:s.jsxs(J,{onWheel:e=>{if(c.current){const l=c.current.querySelector("[data-radix-scroll-area-viewport]");l&&(l.scrollTop+=e.deltaY)}},children:[y?Array.from({length:3}).map((e,l)=>s.jsxs(w,{className:"flex items-center gap-2",children:[s.jsx("div",{className:"h-4 w-4 rounded-sm border opacity-50"}),s.jsx("div",{className:"h-4 bg-muted rounded flex-1 animate-pulse"})]},`loading-${l}`)):d.filter(e=>e.label.toLowerCase().includes(S.toLowerCase())).map(e=>{const l=a.includes(e.value);return s.jsxs(w,{value:e.value,onSelect:()=>A(e.value),className:j("flex items-center gap-2",l?"bg-muted":""),children:[s.jsx("div",{className:j("flex h-4 w-4 items-center justify-center rounded-sm border",l?"bg-primary border-primary text-primary-foreground":"opacity-50"),children:l&&s.jsx(G,{className:"h-3 w-3"})}),s.jsxs("div",{className:"flex flex-col flex-1",children:[s.jsx("span",{children:e.label}),W&&e.description&&s.jsx("span",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.value)}),B&&C&&s.jsx(w,{onSelect:C,className:"flex items-center justify-center gap-2 text-muted-foreground",children:s.jsx("span",{children:"Load more..."})})]})})]})})]})};export{oe as M};
//# sourceMappingURL=multi-select-Dsa7V91B.js.map
