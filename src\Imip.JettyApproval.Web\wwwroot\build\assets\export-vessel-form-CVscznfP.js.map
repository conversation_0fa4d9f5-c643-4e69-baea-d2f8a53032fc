{"version": 3, "file": "export-vessel-form-CVscznfP.js", "sources": ["../../../../../frontend/src/components/jetty/vessel/export/export-vessel-columns.ts", "../../../../../frontend/src/components/jetty/vessel/export/export-vessel-header-schema.ts", "../../../../../frontend/src/components/jetty/vessel/export/export-vessel-item-schema.ts", "../../../../../frontend/src/components/jetty/vessel/export/export-vessel-form.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button';\r\nimport type { BusinessPartnerDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport type { Root } from 'react-dom/client';\r\nimport { AttachmentDialog } from '../attachment-dialog';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport Handsontable from 'handsontable';\r\n\r\nfunction getRootContainer(td: unknown): Root | undefined {\r\n  return typeof td === 'object' && td !== null && '_reactRootContainer' in td\r\n    ? (td as { _reactRootContainer?: Root })._reactRootContainer\r\n    : undefined;\r\n}\r\nfunction setRootContainer(td: unknown, root: Root) {\r\n  if (typeof td === 'object' && td !== null) {\r\n    (td as { _reactRootContainer?: Root })._reactRootContainer = root;\r\n  }\r\n}\r\n\r\n// Store dialog states outside of the component to persist across re-renders\r\nconst dialogStates = new Map<string, boolean>();\r\n\r\n// Renderer factory for attachment button\r\nexport const renderAttachmentButton = (queryClient: QueryClient) => {\r\n  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');\r\n  return (\r\n    _instance: Handsontable.Core | undefined,\r\n    td: HTMLTableCellElement,\r\n    _row: number,\r\n    _col: number,\r\n    _prop: string | number,\r\n    _value: unknown,\r\n    _cellProperties: Handsontable.CellProperties\r\n  ) => {\r\n    void _col;\r\n    void _prop;\r\n    void _value;\r\n    void _cellProperties;\r\n\r\n    // Check if there's already a React root for this td element\r\n    let root = getRootContainer(td);\r\n\r\n    if (!root) {\r\n      // Only create a new root if one doesn't exist\r\n      root = createRoot(td);\r\n      setRootContainer(td, root);\r\n    }\r\n\r\n    // Create a wrapper component to handle the dialog state\r\n    const AttachmentButton = () => {\r\n      // Get row data to create a unique key for this dialog and fetch fresh data\r\n      const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);\r\n      const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};\r\n      const dialogKey = `${rowData.id || _row}`;\r\n      const vesselId = rowData.vesselId || rowData.id;\r\n\r\n      // Use external state that persists across re-renders\r\n      const [open, setOpenInternal] = React.useState(dialogStates.get(dialogKey) || false);\r\n\r\n      const setOpen = (newOpen: boolean) => {\r\n        dialogStates.set(dialogKey, newOpen);\r\n        setOpenInternal(newOpen);\r\n      };\r\n\r\n      // Get fresh data from the query cache first, then fallback to Handsontable data\r\n      const freshData = queryClient.getQueryData(['export-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;\r\n      const freshAttachments = freshData?.items?.find((item) => item.id === rowData.id)?.attachments;\r\n      const attachments = freshAttachments || _instance?.getDataAtRowProp(_row, 'attachments') || [];\r\n\r\n      // console.log(\"attachments\", attachments)\r\n      // console.log(\"freshAttachments\", freshAttachments)\r\n      const itemName = _instance?.getDataAtRowProp(_row, 'itemName') || '';\r\n      const referenceId = rowData.id ?? '';\r\n      const documentReferenceId = rowData.docEntry ?? 0;\r\n\r\n      // Callback to refresh the table data after successful upload\r\n      const handleUploadSuccess = () => {\r\n        // Use the already declared rowData and vesselId from the parent scope\r\n\r\n        // Invalidate the specific query with the vessel ID to refetch the vessel data with updated attachments\r\n        if (vesselId) {\r\n          queryClient.invalidateQueries({ queryKey: ['export-vessel', vesselId] });\r\n        } else {\r\n          // Fallback to invalidate all export-vessel queries\r\n          queryClient.invalidateQueries({ queryKey: ['export-vessel'] });\r\n        }\r\n\r\n        // Simple debounced re-render to avoid multiple rapid updates\r\n        setTimeout(() => {\r\n          if (_instance) {\r\n            try {\r\n              // Get fresh data from the query cache and update Handsontable\r\n              const freshData = queryClient.getQueryData(['export-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;\r\n              if (freshData && freshData.items) {\r\n                const currentItemId = rowData.id;\r\n                const freshItem = freshData.items.find((item) => item.id === currentItemId);\r\n                if (freshItem) {\r\n                  // Update the specific row with fresh data\r\n                  _instance.setDataAtRowProp(_row, 'attachments', freshItem.attachments || []);\r\n                }\r\n              }\r\n\r\n              // Single re-render to update the attachment count\r\n              // The dialog state will persist because it's stored externally\r\n              root.render(React.createElement(AttachmentButton));\r\n            } catch (error) {\r\n              console.warn('Error re-rendering cell:', error);\r\n            }\r\n          }\r\n        }, 200); // Single timeout with reasonable delay\r\n      };\r\n\r\n      return React.createElement(React.Fragment, null, [\r\n        React.createElement(Button, {\r\n          key: 'button',\r\n          size: 'xs',\r\n          variant: 'success',\r\n          type: 'button',\r\n          onClick: () => setOpen(true),\r\n          'aria-label': 'View Attachments',\r\n          children: `Attachment (${attachments?.length || 0})`\r\n        }),\r\n        React.createElement(AttachmentDialog, {\r\n          key: 'dialog',\r\n          open: open,\r\n          onOpenChange: setOpen,\r\n          attachments: attachments || [],\r\n          title: `Attachments - ${itemName || 'Item'}`,\r\n          queryClient,\r\n          referenceId,\r\n          documentReferenceId,\r\n          defaultTabName: 'SHIPPING',\r\n          docType: 'Export',\r\n          transType: 'ExportDetails',\r\n          tabName: 'SHIPPING',\r\n          onUploadSuccess: handleUploadSuccess,\r\n        })\r\n      ]);\r\n    };\r\n\r\n    root.render(React.createElement(AttachmentButton));\r\n    return td;\r\n  };\r\n};\r\n\r\nexport const getExportVesselColumns = (tenants: MasterTenantDto[], businessPartners: BusinessPartnerDto[], queryClient: QueryClient) => {\r\n  // Extract tenant names for autocomplete source\r\n  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');\r\n\r\n  // Extract business partner names for autocomplete source\r\n  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');\r\n\r\n  return [\r\n    { data: 'id', title: 'Id', type: 'text', width: 200 },\r\n    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },\r\n    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },\r\n    {\r\n      data: 'tenant',\r\n      title: 'Tenant',\r\n      type: 'autocomplete',\r\n      width: 140,\r\n      source: tenantNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    {\r\n      data: 'businessPartner',\r\n      title: 'Business Partner',\r\n      type: 'autocomplete',\r\n      width: 300,\r\n      source: businessPartnerNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    { data: 'itemName', title: 'Item Name', type: 'text', width: 250 },\r\n    { data: 'grossWeight', title: 'Gross Weight', type: 'numeric', width: 100 },\r\n    { data: 'unitWeight', title: 'Unit Weight', type: 'text', width: 100 },\r\n    { data: 'letterNo', title: 'Letter No', type: 'text', width: 120 },\r\n    { data: 'letterDate', title: 'Letter Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'shippingInstructionNo', title: 'Shipping Instruction No', type: 'text', width: 180 },\r\n    { data: 'shippingInstructionDate', title: 'Shipping Instruction Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'status', title: 'Status', type: 'text', width: 120 },\r\n    { data: 'regType', title: 'RegType', type: 'text', width: 120 },\r\n    { data: 'noBl', title: 'No BL', type: 'text', width: 120 },\r\n    { data: 'dateBl', title: 'Date BL', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'ajuNo', title: 'AJU No', type: 'text', width: 120 },\r\n    { data: 'regNo', title: 'Reg No', type: 'text', width: 120 },\r\n    { data: 'regDate', title: 'Reg Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppbNo', title: 'SPPB No', type: 'text', width: 120 },\r\n    { data: 'sppbDate', title: 'SPPB Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppdNo', title: 'SPPD No', type: 'text', width: 120 },\r\n    { data: 'sppdDate', title: 'SPPD Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'remarks', title: 'Remark', type: 'text', width: 120 },\r\n    { data: 'attachments', title: 'Attachment', width: 100, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },\r\n  ];\r\n}; ", "import * as z from 'zod';\r\n\r\nexport const exportVesselHeaderSchema = z.object({\r\n  docNum: z.string().optional(),\r\n  vesselId: z.string().min(1, 'Vessel Name is required'),\r\n  postingDate: z.string().min(1, 'Posting Date is required'),\r\n  voyage: z.string().optional(),\r\n  vesselArrival: z.string().optional(),\r\n  vesselDeparture: z.string().optional(),\r\n  portOriginId: z.string().optional(),\r\n  destinationPortId: z.string().optional(),\r\n  concurrencyStamp: z.string().optional(),\r\n  docStatus: z.string().optional(),\r\n  jettyId: z.string().min(1, 'Jetty is required'),\r\n  // Add more fields as needed from CreateUpdateExportVesselDto\r\n  asideDate: z.string().optional(),\r\n  castOfDate: z.string().optional(),\r\n});\r\n\r\nexport type ExportVesselHeaderForm = z.infer<typeof exportVesselHeaderSchema>; ", "import * as z from 'zod';\r\n\r\nexport const exportVesselItemSchema = z.object({\r\n  itemName: z.string().optional(),\r\n  itemQty: z.number().optional(),\r\n  unitQty: z.string().optional(),\r\n  remarks: z.string().optional(),\r\n  tenant: z.string().optional(),\r\n  tenantId: z.string().optional(),\r\n  businessPartner: z.string().optional(),\r\n  businessPartnerId: z.string().optional(),\r\n  concurrencyStamp: z.string().optional(),\r\n  shippingInstructionDate: z.string().optional(),\r\n  letterDate: z.string().optional(),\r\n  regDate: z.string().optional(),\r\n  // Add more fields as needed from CreateUpdateVesselItemDto\r\n});\r\n\r\nexport type ExportVesselItemForm = z.infer<typeof exportVesselItemSchema>; ", "import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { BusinessPartnerDto, CreateUpdateExportVesselDto, CreateUpdateVesselItemDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport { Button } from '@/components/ui/button';\r\nimport { FormField, FormSection } from '@/components/ui/FormField';\r\nimport { Input } from '@/components/ui/input';\r\nimport { HotTable, type HotTableRef } from '@handsontable/react-wrapper';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { router } from '@inertiajs/react';\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { registerAllModules } from 'handsontable/registry';\r\nimport 'handsontable/styles/handsontable.min.css';\r\nimport 'handsontable/styles/ht-theme-horizon.css';\r\nimport 'handsontable/styles/ht-theme-main.min.css';\r\nimport type { ColumnSettings } from 'node_modules/handsontable/settings';\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { Controller, FormProvider, useForm } from 'react-hook-form';\r\nimport { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from './async-selects';\r\nimport { getExportVesselColumns } from './export-vessel-columns';\r\nimport { type ExportVesselHeaderForm, exportVesselHeaderSchema } from './export-vessel-header-schema';\r\nimport { type ExportVesselItemForm, exportVesselItemSchema } from './export-vessel-item-schema';\r\nregisterAllModules();\r\n\r\nexport type ExportVesselFormProps = {\r\n  mode: 'create' | 'edit';\r\n  initialHeader: Partial<Record<keyof ExportVesselHeaderForm, string>>;\r\n  initialItems: ExportVesselItemForm[];\r\n  onSubmit: (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => Promise<void>;\r\n  columns?: ColumnSettings[];\r\n  headerSchema?: typeof exportVesselHeaderSchema;\r\n  itemSchema?: typeof exportVesselItemSchema;\r\n  isSubmitting?: boolean;\r\n  tenants: MasterTenantDto[];\r\n  businessPartners: BusinessPartnerDto[];\r\n  loadingTenants: boolean;\r\n  loadingBusinessPartners: boolean;\r\n  title?: string;\r\n  showAddLineButton?: boolean;\r\n};\r\n\r\nfunction toExportVesselHeaderForm(dto: Partial<Record<keyof ExportVesselHeaderForm, string>>): ExportVesselHeaderForm {\r\n  return {\r\n    docNum: dto.docNum ?? '',\r\n    vesselId: dto.vesselId ?? '',\r\n    voyage: dto.voyage ?? '',\r\n    postingDate: dto.postingDate ?? '',\r\n    vesselArrival: dto.vesselArrival ?? '',\r\n    vesselDeparture: dto.vesselDeparture ?? '',\r\n    portOriginId: dto.portOriginId ?? '',\r\n    destinationPortId: dto.destinationPortId ?? '',\r\n    jettyId: dto.jettyId ?? '',\r\n    asideDate: dto.asideDate ?? '',\r\n    castOfDate: dto.castOfDate ?? '',\r\n  };\r\n}\r\n\r\nfunction handleApproval() {\r\n  // TODO: Implement approval logic\r\n}\r\n\r\n// Extend ExportVesselItemForm to always include concurrencyStamp and id for form logic\r\ntype ExportVesselItemFormWithConcurrency = ExportVesselItemForm & { concurrencyStamp?: string; id?: string };\r\n\r\n// Wrapper component that handles data fetching\r\nexport const ExportVesselFormWithData: React.FC<Omit<ExportVesselFormProps, 'tenants' | 'businessPartners' | 'loadingTenants' | 'loadingBusinessPartners'>> = (props) => {\r\n  const { data: tenants = [], isLoading: loadingTenants } = useQuery({\r\n    queryKey: ['tenants'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({\r\n    queryKey: ['businessPartners'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;\r\n\r\n  return (\r\n    <ExportVesselForm\r\n      {...props}\r\n      tenants={tenants}\r\n      businessPartners={businessPartners}\r\n      loadingTenants={loadingTenants}\r\n      loadingBusinessPartners={loadingBusinessPartners}\r\n    />\r\n  );\r\n};\r\n\r\n// Main form component that receives data as props\r\nconst ExportVesselForm: React.FC<ExportVesselFormProps> = ({\r\n  mode,\r\n  initialHeader,\r\n  initialItems,\r\n  onSubmit,\r\n  headerSchema = exportVesselHeaderSchema,\r\n  itemSchema = exportVesselItemSchema,\r\n  isSubmitting = false,\r\n  tenants,\r\n  businessPartners,\r\n  title = 'Create Export Vessel',\r\n  showAddLineButton = true,\r\n}) => {\r\n  const [items, setItems] = useState<ExportVesselItemForm[]>(initialItems);\r\n  const [itemErrors, setItemErrors] = useState<string[]>([]);\r\n  const hotTableRef = useRef<HotTableRef | null>(null);\r\n  const [submitError, setSubmitError] = useState<string | null>(null);\r\n  const [docNum, setDocNum] = useState<string>('');\r\n  const prevHeaderString = useRef<string | undefined>(undefined);\r\n  const prevItemsString = useRef<string | undefined>(undefined);\r\n\r\n  const methods = useForm<ExportVesselHeaderForm>({\r\n    resolver: zodResolver(headerSchema),\r\n    defaultValues: {\r\n      ...toExportVesselHeaderForm(initialHeader),\r\n      docNum: mode === 'create' ? docNum : initialHeader.docNum ?? '',\r\n    },\r\n    mode: 'onBlur',\r\n  });\r\n  const { register, handleSubmit, formState: { errors }, reset, setValue } = methods;\r\n\r\n  // Get postDate from form or use current date\r\n  const watchedPostDate = methods.watch('vesselArrival');\r\n  const getCurrentDate = () => {\r\n    const d = new Date();\r\n    return d.toISOString().slice(0, 10);\r\n  };\r\n  const postDate = watchedPostDate || getCurrentDate();\r\n\r\n  // Fetch docNum only in create mode, and when postDate changes\r\n  const { data: generatedDocNum } = useQuery<string, Error>({\r\n    queryKey: ['generateDocNum', postDate, mode],\r\n    queryFn: () => ekbProxyService.generateNextExportVesselDocNum(postDate).then(res => String(res.data ?? '')),\r\n    enabled: mode === 'create' && !!postDate,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (mode === 'create' && generatedDocNum) {\r\n      setDocNum(generatedDocNum);\r\n    }\r\n  }, [generatedDocNum, mode]);\r\n\r\n  // Keep docNum in sync with fetched value in create mode\r\n  useEffect(() => {\r\n    if (mode === 'create' && docNum) {\r\n      setValue('docNum', docNum);\r\n    }\r\n  }, [docNum, mode, setValue]);\r\n\r\n  useEffect(() => {\r\n    const headerString = JSON.stringify(initialHeader);\r\n    const itemsString = JSON.stringify(initialItems);\r\n    if (\r\n      mode === 'edit' &&\r\n      ((headerString && prevHeaderString.current !== headerString) ||\r\n        (itemsString && prevItemsString.current !== itemsString))\r\n    ) {\r\n      reset(toExportVesselHeaderForm(initialHeader));\r\n      setItems(\r\n        initialItems.map(i => ({\r\n          ...i,\r\n          concurrencyStamp: (i as ExportVesselItemFormWithConcurrency).concurrencyStamp ?? undefined,\r\n          id: (i as ExportVesselItemFormWithConcurrency).id ?? undefined,\r\n        })) as ExportVesselItemFormWithConcurrency[]\r\n      );\r\n      prevHeaderString.current = headerString;\r\n      prevItemsString.current = itemsString;\r\n    }\r\n  }, [initialHeader, initialItems, mode, reset]);\r\n\r\n  const validateItems = (data: ExportVesselItemForm[]): boolean => {\r\n    const errors: string[] = [];\r\n    data.forEach((item, idx) => {\r\n      const result = itemSchema.safeParse(item);\r\n      if (!result.success) {\r\n        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');\r\n      } else {\r\n        errors[idx] = '';\r\n      }\r\n    });\r\n    setItemErrors(errors);\r\n    return errors.every(e => !e);\r\n  };\r\n\r\n  // Utility to map nulls/undefined to empty string for required string fields\r\n  const mapRequiredFields = <T extends Record<string, unknown>>(obj: T, requiredKeys: string[]): T => {\r\n    const result: Record<string, unknown> = { ...obj };\r\n    for (const key of requiredKeys) {\r\n      if (result[key] === undefined || result[key] === null) {\r\n        result[key] = '';\r\n      }\r\n    }\r\n    // For all string | null fields, set to '' if null\r\n    for (const key in result) {\r\n      if (typeof result[key] === 'string' || result[key] === null) {\r\n        result[key] = result[key] ?? '';\r\n      }\r\n    }\r\n    return result as T;\r\n  };\r\n\r\n  // Type guard for result with id\r\n  function hasId(obj: unknown): obj is { id: string | number } {\r\n    return typeof obj === 'object' && obj !== null && 'id' in obj && (typeof (obj as { id?: unknown }).id === 'string' || typeof (obj as { id?: unknown }).id === 'number');\r\n  }\r\n\r\n  const onFormSubmit = async (header: ExportVesselHeaderForm) => {\r\n    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as ExportVesselItemForm[];\r\n\r\n    // Backend-required fields for items and header\r\n    const requiredKeys = [\r\n      'deleted', 'docType', 'shipment', 'docStatus', 'statusBms', 'portOrigin', 'concurrencyStamp',\r\n      'tenantId', 'businessPartnerId', 'itemName', 'unitQty', 'remarks'\r\n    ];\r\n    const requiredFields: Record<string, string> = {\r\n      deleted: 'N',\r\n      docType: '',\r\n      shipment: '',\r\n      docStatus: '',\r\n      statusBms: '',\r\n      portOrigin: '',\r\n      concurrencyStamp: '',\r\n      tenantId: '',\r\n      businessPartnerId: '',\r\n      itemName: '',\r\n      unitQty: '',\r\n      remarks: '',\r\n    };\r\n\r\n    // Transform tenant names to tenant IDs and business partner names to business partner IDs\r\n    const transformedItems: CreateUpdateVesselItemDto[] = currentItems.map(item => {\r\n      const transformedItem = { ...item } as ExportVesselItemFormWithConcurrency;\r\n\r\n      if (item.tenant) {\r\n        const tenant = tenants.find(t => t.name === item.tenant);\r\n        transformedItem.tenantId = tenant?.id || '';\r\n      } else {\r\n        transformedItem.tenantId = '';\r\n      }\r\n\r\n      if (item.businessPartner) {\r\n        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);\r\n        transformedItem.businessPartnerId = businessPartner?.id || '';\r\n      } else {\r\n        transformedItem.businessPartnerId = '';\r\n      }\r\n      // Set date fields to undefined if falsy or empty string (for backend compatibility)\r\n      if (!transformedItem.shippingInstructionDate) transformedItem.shippingInstructionDate = undefined;\r\n      if (!transformedItem.letterDate) transformedItem.letterDate = undefined;\r\n      if (!transformedItem.regDate) transformedItem.regDate = undefined;\r\n      const itemWithRequired = {\r\n        ...requiredFields,\r\n        ...transformedItem,\r\n      };\r\n      const latest = (initialItems as ExportVesselItemFormWithConcurrency[]).find(i => i.id === transformedItem.id);\r\n      if (latest && latest.concurrencyStamp) {\r\n        transformedItem.concurrencyStamp = latest.concurrencyStamp;\r\n      }\r\n      return mapRequiredFields(itemWithRequired, requiredKeys) as CreateUpdateVesselItemDto;\r\n    });\r\n\r\n    // Ensure docNum is string\r\n    const headerWithRequiredFields: CreateUpdateExportVesselDto = mapRequiredFields({\r\n      ...requiredFields,\r\n      ...header,\r\n      docNum: String(header.docNum ?? ''),\r\n    }, requiredKeys);\r\n\r\n    if (!validateItems(transformedItems as ExportVesselItemForm[])) return;\r\n    setSubmitError(null);\r\n    try {\r\n      // Await the result and expect the created ID in the result\r\n      type SubmitResult = { id?: string | number } | void;\r\n      const result: SubmitResult = await onSubmit(headerWithRequiredFields as ExportVesselHeaderForm, transformedItems as ExportVesselItemForm[]);\r\n      // Only reset after success\r\n      methods.reset();\r\n      // Redirect to edit page if result has id\r\n      if (hasId(result)) {\r\n        router.visit(`/export-vessel/edit/${result.id}`);\r\n      }\r\n    } catch (error: unknown) {\r\n      let message = 'An error occurred';\r\n      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {\r\n        message = (error as { message: string }).message;\r\n      }\r\n      setSubmitError(message);\r\n      // Do not reset form\r\n    }\r\n  };\r\n\r\n  const handleAddLine = () => {\r\n    setItems(prev => [...prev, {} as ExportVesselItemForm]);\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const columns = getExportVesselColumns(tenants, businessPartners, queryClient);\r\n\r\n  // Define a minimal Jetty type and get the jetty list (replace with your actual jetty list source)\r\n  type Jetty = { id: string; isCustomArea?: boolean };\r\n  const jettyList: Jetty[] = []; // TODO: replace with actual jetty list\r\n  const selectedJetty = jettyList.find(j => j.id === methods.watch('jettyId')) || null;\r\n\r\n  return (\r\n    <div className=\"w-full mx-auto\">\r\n      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>\r\n        <div className=\"mb-6\">\r\n          <h2 className=\"text-lg font-bold text-gray-800 dark:text-white\">{title}</h2>\r\n          <div className=\"h-1 w-16 bg-primary rounded mt-2 mb-4\" />\r\n        </div>\r\n        <FormProvider {...methods}>\r\n          <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\r\n            {submitError && (\r\n              <div className=\"text-red-500 text-sm mb-2\">{submitError}</div>\r\n            )}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"DocNum\" labelWidth='100px'>\r\n                  <Input\r\n                    {...register('docNum')}\r\n                    value={methods.watch('docNum')}\r\n                    readOnly={mode === 'create'}\r\n                    disabled={mode === 'edit'}\r\n                  />\r\n                  {errors.docNum && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.docNum.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Vessel Name\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"vesselId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <VesselSelect\r\n                        value={field.value}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select vessel...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.vesselId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Voyage\" labelWidth='100px'>\r\n                  <Input {...register('voyage')} />\r\n                  {errors.voyage && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.voyage.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Jetty\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"jettyId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <JettySelect\r\n                        value={field.value}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select jetty...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.jettyId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.jettyId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"A/Side Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('asideDate')} />\r\n                  {errors.asideDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.asideDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Cast Of Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('castOfDate')} />\r\n                  {errors.castOfDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.castOfDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Arrival Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselArrival')} />\r\n                  {errors.vesselArrival && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselArrival.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Departure Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselDeparture')} />\r\n                  {errors.vesselDeparture && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselDeparture.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"Posting Date\" labelWidth='100px'>\r\n                  <Input type=\"date\" {...register('postingDate')} />\r\n                  {errors.postingDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.postingDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Port Origin\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"portOriginId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <PortOfLoadingSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select port origin...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.portOriginId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.portOriginId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Destination Port\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"destinationPortId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <DestinationPortSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select destination port...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.destinationPortId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.destinationPortId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n            </div>\r\n            <div className=\"mt-6\">\r\n              <label className=\"block font-medium mb-2\">Items</label>\r\n              <HotTable\r\n                ref={hotTableRef}\r\n                themeName=\"ht-theme-main\"\r\n                data={items}\r\n                columns={columns}\r\n                colHeaders={columns.map((col: { title?: string }) => col.title).filter((t: string | undefined): t is string => typeof t === 'string')}\r\n                rowHeaders={true}\r\n                height=\"50vh\"\r\n                licenseKey=\"non-commercial-and-evaluation\"\r\n                stretchH=\"all\"\r\n                contextMenu={true}\r\n                manualColumnResize={true}\r\n                manualRowResize={true}\r\n                autoColumnSize={false}\r\n                autoRowSize={false}\r\n                startRows={1}\r\n                dropdownMenu={true}\r\n                filters={true}\r\n                colWidths={80}\r\n                hiddenColumns={{\r\n                  copyPasteEnabled: true,\r\n                  indicators: true,\r\n                  columns: [0, 1, 2]\r\n                }}\r\n                width=\"100%\"\r\n                persistentState={true}\r\n              />\r\n              {itemErrors.some(e => e) && (\r\n                <div className=\"mt-2 text-red-500 text-xs\">\r\n                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"flex justify-end gap-2\">\r\n              {showAddLineButton && (\r\n                <Button type=\"button\" variant=\"outline\" onClick={handleAddLine} disabled={isSubmitting}>\r\n                  + Add Line\r\n                </Button>\r\n              )}\r\n              {/* Approval button logic */}\r\n              {typeof selectedJetty?.isCustomArea === 'boolean' && !selectedJetty.isCustomArea && (\r\n                <Button type=\"button\" variant=\"primary\" onClick={handleApproval} disabled={isSubmitting}>\r\n                  Submit Approval\r\n                </Button>\r\n              )}\r\n              <Button type=\"submit\" disabled={isSubmitting}>\r\n                {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </FormProvider>\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportVesselForm; "], "names": ["getRootContainer", "td", "setRootContainer", "root", "dialogStates", "renderAttachmentButton", "queryClient", "_instance", "_row", "_col", "_prop", "_value", "_cellProperties", "createRoot", "AttachmentButton", "rowDataRaw", "rowData", "dialogKey", "vesselId", "open", "setOpenInternal", "React", "<PERSON><PERSON><PERSON>", "newOpen", "attachments", "item", "itemName", "referenceId", "documentReferenceId", "handleUploadSuccess", "freshData", "currentItemId", "freshItem", "<PERSON><PERSON>", "AttachmentDialog", "getExportVesselColumns", "tenants", "businessPartners", "tenantNames", "t", "name", "businessPartnerNames", "bp", "exportVesselHeaderSchema", "z.object", "z.string", "exportVesselItemSchema", "z.number", "registerAllModules", "toExportVesselHeaderForm", "dto", "handleApproval", "ExportVesselFormWithData", "props", "loadingTenants", "useQuery", "ekbProxyService", "res", "loadingBusinessPartners", "jsx", "ExportVesselForm", "mode", "initialHeader", "initialItems", "onSubmit", "headerSchema", "itemSchema", "isSubmitting", "title", "showAddLineButton", "items", "setItems", "useState", "itemErrors", "setItemErrors", "hotTableRef", "useRef", "submitError", "setSubmitError", "doc<PERSON>um", "setDocNum", "prevHeaderString", "prevItemsString", "methods", "useForm", "zodResolver", "register", "handleSubmit", "errors", "reset", "setValue", "postDate", "generatedDocNum", "useEffect", "headerString", "itemsString", "i", "validateItems", "data", "idx", "result", "e", "mapRequiredFields", "obj", "requiredKeys", "key", "hasId", "onFormSubmit", "header", "currentItems", "requiredFields", "transformedItems", "transformedItem", "tenant", "business<PERSON><PERSON>ner", "itemWithRequired", "latest", "headerWithRequiredFields", "router", "error", "message", "handleAddLine", "prev", "useQueryClient", "columns", "<PERSON><PERSON><PERSON><PERSON>", "j", "jsxs", "FormProvider", "FormSection", "FormField", "Input", "Controller", "field", "VesselSelect", "JettySelect", "PortOfLoadingSelect", "DestinationPortSelect", "HotTable", "col", "err"], "mappings": "yjBASA,SAASA,GAAiBC,EAA+B,CAChD,OAAA,OAAOA,GAAO,UAAYA,IAAO,MAAQ,wBAAyBA,EACpEA,EAAsC,oBACvC,MACN,CACA,SAASC,GAAiBD,EAAaE,EAAY,CAC7C,OAAOF,GAAO,UAAYA,IAAO,OAClCA,EAAsC,oBAAsBE,EAEjE,CAGA,MAAMC,OAAmB,IAGZC,GAA0BC,GAA6B,CAClE,GAAI,CAACA,EAAmB,MAAA,IAAI,MAAM,oDAAoD,EACtF,MAAO,CACLC,EACAN,EACAO,EACAC,EACAC,EACAC,EACAC,IACG,CAOC,IAAAT,EAAOH,GAAiBC,CAAE,EAEzBE,IAEHA,EAAOU,cAAWZ,CAAE,EACpBC,GAAiBD,EAAIE,CAAI,GAI3B,MAAMW,EAAmB,IAAM,CAEvB,MAAAC,EAAaR,GAAW,qBAAqBC,CAAI,EACjDQ,EAAWD,GAAc,CAAC,MAAM,QAAQA,CAAU,EAAKA,EAAa,CAAC,EACrEE,EAAY,GAAGD,EAAQ,IAAMR,CAAI,GACjCU,EAAWF,EAAQ,UAAYA,EAAQ,GAGvC,CAACG,EAAMC,CAAe,EAAIC,EAAM,SAASjB,GAAa,IAAIa,CAAS,GAAK,EAAK,EAE7EK,EAAWC,GAAqB,CACvBnB,GAAA,IAAIa,EAAWM,CAAO,EACnCH,EAAgBG,CAAO,CACzB,EAKMC,EAFYlB,EAAY,aAAa,CAAC,gBAAiBY,CAAQ,CAAC,GAClC,OAAO,KAAMO,GAASA,EAAK,KAAOT,EAAQ,EAAE,GAAG,aAC3CT,GAAW,iBAAiBC,EAAM,aAAa,GAAK,CAAC,EAIvFkB,EAAWnB,GAAW,iBAAiBC,EAAM,UAAU,GAAK,GAC5DmB,EAAcX,EAAQ,IAAM,GAC5BY,EAAsBZ,EAAQ,UAAY,EAG1Ca,EAAsB,IAAM,CAI5BX,EACFZ,EAAY,kBAAkB,CAAE,SAAU,CAAC,gBAAiBY,CAAQ,EAAG,EAGvEZ,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAI/D,WAAW,IAAM,CACf,GAAIC,EACE,GAAA,CAEF,MAAMuB,EAAYxB,EAAY,aAAa,CAAC,gBAAiBY,CAAQ,CAAC,EAClEY,GAAAA,GAAaA,EAAU,MAAO,CAChC,MAAMC,EAAgBf,EAAQ,GACxBgB,EAAYF,EAAU,MAAM,KAAML,GAASA,EAAK,KAAOM,CAAa,EACtEC,GAEFzB,EAAU,iBAAiBC,EAAM,cAAewB,EAAU,aAAe,EAAE,CAC7E,CAKF7B,EAAK,OAAOkB,EAAM,cAAcP,CAAgB,CAAC,OACnC,CAAA,GAIjB,GAAG,CACR,EAEA,OAAOO,EAAM,cAAcA,EAAM,SAAU,KAAM,CAC/CA,EAAM,cAAcY,EAAQ,CAC1B,IAAK,SACL,KAAM,KACN,QAAS,UACT,KAAM,SACN,QAAS,IAAMX,EAAQ,EAAI,EAC3B,aAAc,mBACd,SAAU,eAAeE,GAAa,QAAU,CAAC,GAAA,CAClD,EACDH,EAAM,cAAca,GAAkB,CACpC,IAAK,SACL,KAAAf,EACA,aAAcG,EACd,YAAaE,GAAe,CAAC,EAC7B,MAAO,iBAAiBE,GAAY,MAAM,GAC1C,YAAApB,EACA,YAAAqB,EACA,oBAAAC,EACA,eAAgB,WAChB,QAAS,SACT,UAAW,gBACX,QAAS,WACT,gBAAiBC,CAClB,CAAA,CAAA,CACF,CACH,EAEA,OAAA1B,EAAK,OAAOkB,EAAM,cAAcP,CAAgB,CAAC,EAC1Cb,CACT,CACF,EAEakC,GAAyB,CAACC,EAA4BC,EAAwC/B,IAA6B,CAEhI,MAAAgC,EAAcF,EAAQ,IAASG,GAAAA,EAAE,MAAQ,EAAE,EAAE,OAAeC,GAAAA,IAAS,EAAE,EAGvEC,EAAuBJ,EAAiB,IAAUK,GAAAA,EAAG,MAAQ,EAAE,EAAE,OAAeF,GAAAA,IAAS,EAAE,EAE1F,MAAA,CACL,CAAE,KAAM,KAAM,MAAO,KAAM,KAAM,OAAQ,MAAO,GAAI,EACpD,CAAE,KAAM,WAAY,MAAO,WAAY,KAAM,OAAQ,MAAO,GAAI,EAChE,CAAE,KAAM,mBAAoB,MAAO,mBAAoB,KAAM,OAAQ,MAAO,GAAI,EAChF,CACE,KAAM,SACN,MAAO,SACP,KAAM,eACN,MAAO,IACP,OAAQF,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CACE,KAAM,kBACN,MAAO,mBACP,KAAM,eACN,MAAO,IACP,OAAQG,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,MAAO,GAAI,EACjE,CAAE,KAAM,cAAe,MAAO,eAAgB,KAAM,UAAW,MAAO,GAAI,EAC1E,CAAE,KAAM,aAAc,MAAO,cAAe,KAAM,OAAQ,MAAO,GAAI,EACrE,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,MAAO,GAAI,EACjE,CAAE,KAAM,aAAc,MAAO,cAAe,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EACpH,CAAE,KAAM,wBAAyB,MAAO,0BAA2B,KAAM,OAAQ,MAAO,GAAI,EAC5F,CAAE,KAAM,0BAA2B,MAAO,4BAA6B,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC/I,CAAE,KAAM,SAAU,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC5D,CAAE,KAAM,UAAW,MAAO,UAAW,KAAM,OAAQ,MAAO,GAAI,EAC9D,CAAE,KAAM,OAAQ,MAAO,QAAS,KAAM,OAAQ,MAAO,GAAI,EACzD,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC5G,CAAE,KAAM,QAAS,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC3D,CAAE,KAAM,QAAS,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC3D,CAAE,KAAM,UAAW,MAAO,WAAY,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC9G,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAChH,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAChH,CAAE,KAAM,UAAW,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,cAAe,MAAO,aAAc,MAAO,IAAK,SAAUpC,GAAuBC,CAAW,EAAG,SAAU,GAAM,WAAY,EAAM,CAC3I,CACF,ECtMaqC,GAA2BC,GAAS,CAC/C,OAAQC,EAAS,EAAE,SAAS,EAC5B,SAAUA,EAAW,EAAA,IAAI,EAAG,yBAAyB,EACrD,YAAaA,EAAW,EAAA,IAAI,EAAG,0BAA0B,EACzD,OAAQA,EAAS,EAAE,SAAS,EAC5B,cAAeA,EAAS,EAAE,SAAS,EACnC,gBAAiBA,EAAS,EAAE,SAAS,EACrC,aAAcA,EAAS,EAAE,SAAS,EAClC,kBAAmBA,EAAS,EAAE,SAAS,EACvC,iBAAkBA,EAAS,EAAE,SAAS,EACtC,UAAWA,EAAS,EAAE,SAAS,EAC/B,QAASA,EAAW,EAAA,IAAI,EAAG,mBAAmB,EAE9C,UAAWA,EAAS,EAAE,SAAS,EAC/B,WAAYA,EAAS,EAAE,SAAS,CAClC,CAAC,ECfYC,GAAyBF,GAAS,CAC7C,SAAUC,EAAS,EAAE,SAAS,EAC9B,QAASE,GAAS,EAAE,SAAS,EAC7B,QAASF,EAAS,EAAE,SAAS,EAC7B,QAASA,EAAS,EAAE,SAAS,EAC7B,OAAQA,EAAS,EAAE,SAAS,EAC5B,SAAUA,EAAS,EAAE,SAAS,EAC9B,gBAAiBA,EAAS,EAAE,SAAS,EACrC,kBAAmBA,EAAS,EAAE,SAAS,EACvC,iBAAkBA,EAAS,EAAE,SAAS,EACtC,wBAAyBA,EAAS,EAAE,SAAS,EAC7C,WAAYA,EAAS,EAAE,SAAS,EAChC,QAASA,EAAS,EAAE,SAAS,CAE/B,CAAC,ECIDG,GAAmB,EAmBnB,SAASC,GAAyBC,EAAoF,CAC7G,MAAA,CACL,OAAQA,EAAI,QAAU,GACtB,SAAUA,EAAI,UAAY,GAC1B,OAAQA,EAAI,QAAU,GACtB,YAAaA,EAAI,aAAe,GAChC,cAAeA,EAAI,eAAiB,GACpC,gBAAiBA,EAAI,iBAAmB,GACxC,aAAcA,EAAI,cAAgB,GAClC,kBAAmBA,EAAI,mBAAqB,GAC5C,QAASA,EAAI,SAAW,GACxB,UAAWA,EAAI,WAAa,GAC5B,WAAYA,EAAI,YAAc,EAChC,CACF,CAEA,SAASC,IAAiB,CAE1B,CAMa,MAAAC,GAAkJC,GAAU,CACjK,KAAA,CAAE,KAAMjB,EAAU,CAAA,EAAI,UAAWkB,GAAmBC,EAAS,CACjE,SAAU,CAAC,SAAS,EACpB,QAAS,IACPC,EAAgB,cAAc,CAAE,KAAM,EAAG,eAAgB,GAAK,CAAC,EAC5D,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,CAAA,CAAA,CACvC,EAEK,CAAE,KAAMpB,EAAmB,CAAA,EAAI,UAAWqB,GAA4BH,EAAS,CACnF,SAAU,CAAC,kBAAkB,EAC7B,QAAS,IACPC,EAAgB,uBAAuB,CAAE,KAAM,EAAG,eAAgB,GAAM,CAAC,EACtE,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,CAAA,CAAA,CACvC,EAED,OAAIH,GAAkBI,EAAgCC,EAAAA,IAAC,OAAI,SAAe,kBAAA,EAGxEA,EAAA,IAACC,GAAA,CACE,GAAGP,EACJ,QAAAjB,EACA,iBAAAC,EACA,eAAAiB,EACA,wBAAAI,CAAA,CACF,CAEJ,EAGME,GAAoD,CAAC,CACzD,KAAAC,EACA,cAAAC,EACA,aAAAC,EACA,SAAAC,EACA,aAAAC,EAAetB,GACf,WAAAuB,EAAapB,GACb,aAAAqB,EAAe,GACf,QAAA/B,EACA,iBAAAC,EACA,MAAA+B,EAAQ,uBACR,kBAAAC,EAAoB,EACtB,IAAM,CACJ,KAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAAA,SAAiCT,CAAY,EACjE,CAACU,EAAYC,CAAa,EAAIF,EAAAA,SAAmB,CAAA,CAAE,EACnDG,EAAcC,SAA2B,IAAI,EAC7C,CAACC,EAAaC,CAAc,EAAIN,EAAAA,SAAwB,IAAI,EAC5D,CAACO,EAAQC,CAAS,EAAIR,EAAAA,SAAiB,EAAE,EACzCS,EAAmBL,SAA2B,MAAS,EACvDM,EAAkBN,SAA2B,MAAS,EAEtDO,EAAUC,GAAgC,CAC9C,SAAUC,GAAYpB,CAAY,EAClC,cAAe,CACb,GAAGhB,GAAyBa,CAAa,EACzC,OAAQD,IAAS,SAAWkB,EAASjB,EAAc,QAAU,EAC/D,EACA,KAAM,QAAA,CACP,EACK,CAAE,SAAAwB,EAAU,aAAAC,EAAc,UAAW,CAAE,OAAAC,GAAU,MAAAC,EAAO,SAAAC,CAAA,EAAaP,EAQrEQ,EALkBR,EAAQ,MAAM,eAAe,OAErC,KAAK,EACV,YAAA,EAAc,MAAM,EAAG,EAAE,EAK9B,CAAE,KAAMS,CAAgB,EAAIrC,EAAwB,CACxD,SAAU,CAAC,iBAAkBoC,EAAU9B,CAAI,EAC3C,QAAS,IAAML,EAAgB,+BAA+BmC,CAAQ,EAAE,KAAKlC,GAAO,OAAOA,EAAI,MAAQ,EAAE,CAAC,EAC1G,QAASI,IAAS,UAAY,CAAC,CAAC8B,CAAA,CACjC,EAEDE,EAAAA,UAAU,IAAM,CACVhC,IAAS,UAAY+B,GACvBZ,EAAUY,CAAe,CAC3B,EACC,CAACA,EAAiB/B,CAAI,CAAC,EAG1BgC,EAAAA,UAAU,IAAM,CACVhC,IAAS,UAAYkB,GACvBW,EAAS,SAAUX,CAAM,CAE1B,EAAA,CAACA,EAAQlB,EAAM6B,CAAQ,CAAC,EAE3BG,EAAAA,UAAU,IAAM,CACR,MAAAC,EAAe,KAAK,UAAUhC,CAAa,EAC3CiC,EAAc,KAAK,UAAUhC,CAAY,EAE7CF,IAAS,SACPiC,GAAgBb,EAAiB,UAAYa,GAC5CC,GAAeb,EAAgB,UAAYa,KAExCN,EAAAxC,GAAyBa,CAAa,CAAC,EAC7CS,EACER,EAAa,IAAUiC,IAAA,CACrB,GAAGA,EACH,iBAAmBA,EAA0C,kBAAoB,OACjF,GAAKA,EAA0C,IAAM,MAAA,EACrD,CACJ,EACAf,EAAiB,QAAUa,EAC3BZ,EAAgB,QAAUa,IAE3B,CAACjC,EAAeC,EAAcF,EAAM4B,CAAK,CAAC,EAEvC,MAAAQ,GAAiBC,GAA0C,CAC/D,MAAMV,EAAmB,CAAC,EACrB,OAAAU,EAAA,QAAQ,CAACzE,EAAM0E,IAAQ,CACpB,MAAAC,EAASlC,EAAW,UAAUzC,CAAI,EACnC2E,EAAO,QAGVZ,EAAOW,CAAG,EAAI,GAFdX,EAAOW,CAAG,EAAI,OAAO,OAAOC,EAAO,MAAM,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,IAAI,CAGlF,CACD,EACD1B,EAAcc,CAAM,EACbA,EAAO,MAAWa,GAAA,CAACA,CAAC,CAC7B,EAGMC,EAAoB,CAAoCC,EAAQC,IAA8B,CAC5F,MAAAJ,EAAkC,CAAE,GAAGG,CAAI,EACjD,UAAWE,KAAOD,GACZJ,EAAOK,CAAG,IAAM,QAAaL,EAAOK,CAAG,IAAM,QAC/CL,EAAOK,CAAG,EAAI,IAIlB,UAAWA,KAAOL,GACZ,OAAOA,EAAOK,CAAG,GAAM,UAAYL,EAAOK,CAAG,IAAM,QACrDL,EAAOK,CAAG,EAAIL,EAAOK,CAAG,GAAK,IAG1B,OAAAL,CACT,EAGA,SAASM,GAAMH,EAA8C,CAC3D,OAAO,OAAOA,GAAQ,UAAYA,IAAQ,MAAQ,OAAQA,IAAQ,OAAQA,EAAyB,IAAO,UAAY,OAAQA,EAAyB,IAAO,SAAA,CAG1J,MAAAI,GAAe,MAAOC,GAAmC,CAC7D,MAAMC,EAAgBlC,EAAY,SAAS,aAAa,mBAAqB,CAAC,EAGxE6B,EAAe,CACnB,UAAW,UAAW,WAAY,YAAa,YAAa,aAAc,mBAC1E,WAAY,oBAAqB,WAAY,UAAW,SAC1D,EACMM,EAAyC,CAC7C,QAAS,IACT,QAAS,GACT,SAAU,GACV,UAAW,GACX,UAAW,GACX,WAAY,GACZ,iBAAkB,GAClB,SAAU,GACV,kBAAmB,GACnB,SAAU,GACV,QAAS,GACT,QAAS,EACX,EAGMC,EAAgDF,EAAa,IAAYpF,GAAA,CACvE,MAAAuF,EAAkB,CAAE,GAAGvF,CAAK,EAElC,GAAIA,EAAK,OAAQ,CACf,MAAMwF,EAAS7E,EAAQ,QAAUG,EAAE,OAASd,EAAK,MAAM,EACvCuF,EAAA,SAAWC,GAAQ,IAAM,EAAA,MAEzCD,EAAgB,SAAW,GAG7B,GAAIvF,EAAK,gBAAiB,CACxB,MAAMyF,EAAkB7E,EAAiB,QAAWK,EAAG,OAASjB,EAAK,eAAe,EACpEuF,EAAA,kBAAoBE,GAAiB,IAAM,EAAA,MAE3DF,EAAgB,kBAAoB,GAGjCA,EAAgB,0BAAyBA,EAAgB,wBAA0B,QACnFA,EAAgB,aAAYA,EAAgB,WAAa,QACzDA,EAAgB,UAASA,EAAgB,QAAU,QACxD,MAAMG,GAAmB,CACvB,GAAGL,EACH,GAAGE,CACL,EACMI,EAAUrD,EAAuD,QAAUiC,EAAE,KAAOgB,EAAgB,EAAE,EACxG,OAAAI,GAAUA,EAAO,mBACnBJ,EAAgB,iBAAmBI,EAAO,kBAErCd,EAAkBa,GAAkBX,CAAY,CAAA,CACxD,EAGKa,GAAwDf,EAAkB,CAC9E,GAAGQ,EACH,GAAGF,EACH,OAAQ,OAAOA,EAAO,QAAU,EAAE,GACjCJ,CAAY,EAEX,GAACP,GAAcc,CAA0C,EAC7D,CAAAjC,EAAe,IAAI,EACf,GAAA,CAGF,MAAMsB,EAAuB,MAAMpC,EAASqD,GAAoDN,CAA0C,EAE1I5B,EAAQ,MAAM,EAEVuB,GAAMN,CAAM,GACdkB,GAAO,MAAM,uBAAuBlB,EAAO,EAAE,EAAE,QAE1CmB,EAAgB,CACvB,IAAIC,EAAU,oBACV,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAAgC,SAAY,WAClHC,EAAWD,EAA8B,SAE3CzC,EAAe0C,CAAO,CAAA,EAG1B,EAEMC,GAAgB,IAAM,CAC1BlD,KAAiB,CAAC,GAAGmD,EAAM,CAA0B,CAAA,CAAC,CACxD,EAEMpH,GAAcqH,GAAe,EAC7BC,EAAUzF,GAAuBC,EAASC,EAAkB/B,EAAW,EAKvEuH,EADqB,CAAC,EACI,KAAUC,GAAAA,EAAE,KAAO3C,EAAQ,MAAM,SAAS,CAAC,GAAK,KAEhF,aACG,MAAI,CAAA,UAAU,iBACb,SAAC4C,EAAA,KAAA,MAAA,CAAI,UAAU,qEACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACpE,EAAA,IAAA,KAAA,CAAG,UAAU,kDAAmD,SAAMS,EAAA,EACvET,EAAAA,IAAC,MAAI,CAAA,UAAU,uCAAwC,CAAA,CAAA,EACzD,EACAA,EAAAA,IAACqE,GAAc,CAAA,GAAG7C,EAChB,SAAA4C,EAAAA,KAAC,OAAK,CAAA,SAAUxC,EAAaoB,EAAY,EAAG,UAAU,YACnD,SAAA,CAAA9B,GACElB,EAAA,IAAA,MAAA,CAAI,UAAU,4BAA6B,SAAYkB,EAAA,EAE1DkD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAAE,EAAA,CAAY,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,SAAS,WAAW,QACnC,SAAA,CAAAvE,EAAA,IAACwE,EAAA,CACE,GAAG7C,EAAS,QAAQ,EACrB,MAAOH,EAAQ,MAAM,QAAQ,EAC7B,SAAUtB,IAAS,SACnB,SAAUA,IAAS,MAAA,CACrB,EACC2B,EAAO,QACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,OAAO,OAAkB,CAAA,CAAA,EAE5E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,SAAA,CAAAvE,EAAA,IAACyE,EAAA,CACC,KAAK,WACL,QAASjD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAkD,CAAA,IACT1E,EAAA,IAAC2E,GAAA,CACC,MAAOD,EAAM,MACb,cAAeA,EAAM,SACrB,YAAY,mBACZ,SAAUlE,CAAA,CAAA,CACZ,CAEJ,EACCqB,EAAO,UACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,SAAS,OAAkB,CAAA,CAAA,EAE9E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,SAAS,WAAW,QACnC,SAAA,CAAAvE,EAAAA,IAACwE,EAAO,CAAA,GAAG7C,EAAS,QAAQ,CAAG,CAAA,EAC9BE,EAAO,QACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,OAAO,OAAkB,CAAA,CAAA,EAE5E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,QAAQ,WAAW,QAClC,SAAA,CAAAvE,EAAA,IAACyE,EAAA,CACC,KAAK,UACL,QAASjD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAkD,CAAA,IACT1E,EAAA,IAAC4E,GAAA,CACC,MAAOF,EAAM,MACb,cAAeA,EAAM,SACrB,YAAY,kBACZ,SAAUlE,CAAA,CAAA,CACZ,CAEJ,EACCqB,EAAO,SACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,QAAQ,OAAkB,CAAA,CAAA,CAE7E,CAAA,CAAA,EACF,EAEAuC,EAAAA,KAACE,EAAY,CAAA,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,cAAc,WAAW,QACxC,SAAA,CAAAvE,EAAA,IAACwE,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,WAAW,EAAG,EACvDE,EAAO,WACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,UAAU,OAAkB,CAAA,CAAA,EAE/E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,SAAA,CAAAvE,EAAA,IAACwE,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,YAAY,EAAG,EACxDE,EAAO,YACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,WAAW,OAAkB,CAAA,CAAA,EAEhF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,SAAA,CAAAvE,EAAA,IAACwE,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,eAAe,EAAG,EAC3DE,EAAO,eACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,cAAc,OAAkB,CAAA,CAAA,EAEnF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,iBAAiB,WAAW,QAC3C,SAAA,CAAAvE,EAAA,IAACwE,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,iBAAiB,EAAG,EAC7DE,EAAO,iBACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,gBAAgB,OAAkB,CAAA,CAAA,CAErF,CAAA,CAAA,EACF,EAEAuC,EAAAA,KAACE,EAAY,CAAA,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,eAAe,WAAW,QACzC,SAAA,CAAAvE,EAAA,IAACwE,GAAM,KAAK,OAAQ,GAAG7C,EAAS,aAAa,EAAG,EAC/CE,EAAO,aACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,YAAY,OAAkB,CAAA,CAAA,EAEjF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,SAAA,CAAAvE,EAAA,IAACyE,EAAA,CACC,KAAK,eACL,QAASjD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAkD,CAAA,IACT1E,EAAA,IAAC6E,GAAA,CACC,MAAOH,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,wBACZ,SAAUlE,CAAA,CAAA,CACZ,CAEJ,EACCqB,EAAO,cACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,aAAa,OAAkB,CAAA,CAAA,EAElF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,mBAAmB,WAAW,QAC7C,SAAA,CAAAvE,EAAA,IAACyE,EAAA,CACC,KAAK,oBACL,QAASjD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAkD,CAAA,IACT1E,EAAA,IAAC8E,GAAA,CACC,MAAOJ,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,6BACZ,SAAUlE,CAAA,CAAA,CACZ,CAEJ,EACCqB,EAAO,mBACL7B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA6B,EAAO,kBAAkB,OAAkB,CAAA,CAAA,CAEvF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACAuC,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACpE,EAAA,IAAA,QAAA,CAAM,UAAU,yBAAyB,SAAK,QAAA,EAC/CA,EAAA,IAAC+E,GAAA,CACC,IAAK/D,EACL,UAAU,gBACV,KAAML,EACN,QAAAsD,EACA,WAAYA,EAAQ,IAAKe,GAA4BA,EAAI,KAAK,EAAE,OAAQpG,GAAuC,OAAOA,GAAM,QAAQ,EACpI,WAAY,GACZ,OAAO,OACP,WAAW,gCACX,SAAS,MACT,YAAa,GACb,mBAAoB,GACpB,gBAAiB,GACjB,eAAgB,GAChB,YAAa,GACb,UAAW,EACX,aAAc,GACd,QAAS,GACT,UAAW,GACX,cAAe,CACb,iBAAkB,GAClB,WAAY,GACZ,QAAS,CAAC,EAAG,EAAG,CAAC,CACnB,EACA,MAAM,OACN,gBAAiB,EAAA,CACnB,EACCkC,EAAW,KAAK4B,GAAKA,CAAC,SACpB,MAAI,CAAA,UAAU,4BACZ,SAAA5B,EAAW,IAAI,CAACmE,EAAKzC,IAAQyC,UAAQ,MAAc,CAAA,SAAA,CAAA,OAAKzC,EAAM,EAAE,KAAGyC,CAAA,CAArB,EAAAzC,CAAyB,CAAM,CAChF,CAAA,CAAA,EAEJ,EACA4B,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACZ,SAAA,CACC1D,GAAAV,EAAAA,IAAC1B,EAAO,CAAA,KAAK,SAAS,QAAQ,UAAU,QAASwF,GAAe,SAAUtD,EAAc,SAExF,YAAA,CAAA,EAGD,OAAO0D,GAAe,cAAiB,WAAa,CAACA,EAAc,cAClElE,EAAAA,IAAC1B,EAAO,CAAA,KAAK,SAAS,QAAQ,UAAU,QAASkB,GAAgB,SAAUgB,EAAc,SAEzF,kBAAA,EAEDR,EAAA,IAAA1B,EAAA,CAAO,KAAK,SAAS,SAAUkC,EAC7B,SAAAA,EAAgBN,IAAS,OAAS,YAAc,cAAkBA,IAAS,OAAS,eAAiB,QACxG,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CAEF,CACF,CAAA,CAEJ"}