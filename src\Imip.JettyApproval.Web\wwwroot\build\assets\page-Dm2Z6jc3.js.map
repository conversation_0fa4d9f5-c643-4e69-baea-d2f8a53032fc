{"version": 3, "file": "page-Dm2Z6jc3.js", "sources": ["../../../../../frontend/src/pages/import/create/page.tsx"], "sourcesContent": ["import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { CreateUpdateImportVesselDto, RemoteServiceErrorResponse } from '@/clientEkb/types.gen';\r\nimport { ImportVesselFormWithData } from '@/components/jetty/vessel/import/import-vessel-form';\r\nimport type { ImportVesselHeaderForm } from '@/components/jetty/vessel/import/import-vessel-header-schema';\r\nimport type { ImportVesselItemForm } from '@/components/jetty/vessel/import/import-vessel-item-schema';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { Head, router } from '@inertiajs/react';\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ImportVesselCreatePage = () => {\r\n  const { t } = useTranslation();\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  const { data: docNumData, isLoading } = useQuery({\r\n    queryKey: ['import-vessel-next-docnum'],\r\n    queryFn: async () => {\r\n      const response = await ekbProxyService.generateNextImportVesselDocNum();\r\n      return response.data;\r\n    },\r\n  });\r\n\r\n  const mutation = useMutation({\r\n    mutationFn: async ({ header, items }: { header: ImportVesselHeaderForm; items: ImportVesselItemForm[] }) => {\r\n      const response = await ekbProxyService.createImportVessel({\r\n        ...header,\r\n        docNum: Number(header.docNum),\r\n        items: items.map(item => ({\r\n          ...item,\r\n          createdBy: '',\r\n          docType: '',\r\n          isScan: '',\r\n          isOriginal: '',\r\n          isActive: true,\r\n          isDeleted: false,\r\n          isSend: '',\r\n          isFeOri: '',\r\n          isFeSend: '',\r\n          isChange: '',\r\n          isFeChange: '',\r\n          isFeActive: '',\r\n          deleted: '',\r\n          isUrgent: '',\r\n          tenantId: item.tenantId || '',\r\n          businessPartnerId: item.businessPartnerId || '',\r\n        })),\r\n      });\r\n      if (response.error) throw new Error(response.error);\r\n      return response.data;\r\n    },\r\n    onSuccess: (data) => {\r\n      toast({ title: 'Success', description: 'Import vessel created.', variant: 'success' });\r\n      if (data && data.id) {\r\n        router.visit(`/jetty/vessel/import/edit/${data.id}`);\r\n      }\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err instanceof Error ? err.message : err?.error?.message || 'Error',\r\n        description: err instanceof Error ? undefined : err?.error?.details,\r\n        variant: 'destructive',\r\n      });\r\n    },\r\n  });\r\n\r\n  const handleSubmit = async (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => {\r\n    await mutation.mutateAsync({ header, items });\r\n  };\r\n\r\n  if (isLoading) return <div>Loading...</div>;\r\n\r\n  return (\r\n    <ImportVesselFormWithData\r\n      mode=\"create\"\r\n      title={t('pages.vessel.create.import')}\r\n      initialHeader={{ docNum: Number(docNumData) } as Partial<CreateUpdateImportVesselDto>}\r\n      initialItems={[]}\r\n      onSubmit={handleSubmit}\r\n      isSubmitting={mutation.isPending}\r\n      queryClient={queryClient}\r\n      showAddLineButton={false}\r\n    />\r\n  );\r\n};\r\n\r\nexport default function ImportVesselCreate() {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <AppLayout>\r\n      <Head title={t('pages.vessel.create.import')} />\r\n      <ImportVesselCreatePage />\r\n    </AppLayout>\r\n  );\r\n} "], "names": ["ImportVesselCreatePage", "t", "useTranslation", "toast", "useToast", "queryClient", "useQueryClient", "docNumData", "isLoading", "useQuery", "ekbProxyService", "mutation", "useMutation", "header", "items", "response", "item", "data", "router", "err", "handleSubmit", "jsx", "ImportVesselFormWithData", "ImportVesselCreate", "AppLayout", "Head"], "mappings": "gwBAWA,MAAMA,EAAyB,IAAM,CAC7B,KAAA,CAAE,EAAAC,CAAE,EAAIC,EAAe,EACvB,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrBC,EAAcC,EAAe,EAE7B,CAAE,KAAMC,EAAY,UAAAC,CAAA,EAAcC,EAAS,CAC/C,SAAU,CAAC,2BAA2B,EACtC,QAAS,UACU,MAAMC,EAAgB,+BAA+B,GACtD,IAClB,CACD,EAEKC,EAAWC,EAAY,CAC3B,WAAY,MAAO,CAAE,OAAAC,EAAQ,MAAAC,KAA+E,CACpG,MAAAC,EAAW,MAAML,EAAgB,mBAAmB,CACxD,GAAGG,EACH,OAAQ,OAAOA,EAAO,MAAM,EAC5B,MAAOC,EAAM,IAAaE,IAAA,CACxB,GAAGA,EACH,UAAW,GACX,QAAS,GACT,OAAQ,GACR,WAAY,GACZ,SAAU,GACV,UAAW,GACX,OAAQ,GACR,QAAS,GACT,SAAU,GACV,SAAU,GACV,WAAY,GACZ,WAAY,GACZ,QAAS,GACT,SAAU,GACV,SAAUA,EAAK,UAAY,GAC3B,kBAAmBA,EAAK,mBAAqB,EAAA,EAC7C,CAAA,CACH,EACD,GAAID,EAAS,MAAO,MAAM,IAAI,MAAMA,EAAS,KAAK,EAClD,OAAOA,EAAS,IAClB,EACA,UAAYE,GAAS,CACnBd,EAAM,CAAE,MAAO,UAAW,YAAa,yBAA0B,QAAS,UAAW,EACjFc,GAAQA,EAAK,IACfC,EAAO,MAAM,6BAA6BD,EAAK,EAAE,EAAE,CAEvD,EACA,QAAUE,GAAoC,CACtChB,EAAA,CACJ,MAAOgB,aAAe,MAAQA,EAAI,QAAUA,GAAK,OAAO,SAAW,QACnE,YAAaA,aAAe,MAAQ,OAAYA,GAAK,OAAO,QAC5D,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAEKC,EAAe,MAAOP,EAAgCC,IAAkC,CAC5F,MAAMH,EAAS,YAAY,CAAE,OAAAE,EAAQ,MAAAC,EAAO,CAC9C,EAEA,OAAIN,EAAmBa,EAAA,IAAA,MAAA,CAAI,SAAU,aAAA,EAGnCA,EAAA,IAACC,EAAA,CACC,KAAK,SACL,MAAOrB,EAAE,4BAA4B,EACrC,cAAe,CAAE,OAAQ,OAAOM,CAAU,CAAE,EAC5C,aAAc,CAAC,EACf,SAAUa,EACV,aAAcT,EAAS,UACvB,YAAAN,EACA,kBAAmB,EAAA,CACrB,CAEJ,EAEA,SAAwBkB,GAAqB,CACrC,KAAA,CAAE,EAAAtB,CAAE,EAAIC,EAAe,EAC7B,cACGsB,EACC,CAAA,SAAA,CAAAH,EAAA,IAACI,EAAK,CAAA,MAAOxB,EAAE,4BAA4B,CAAG,CAAA,QAC7CD,EAAuB,CAAA,CAAA,CAAA,EAC1B,CAEJ"}