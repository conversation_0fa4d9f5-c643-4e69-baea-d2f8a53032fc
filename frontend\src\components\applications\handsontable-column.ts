export const columns = [
  { data: 'tenantName', type: 'text', title: 'Tenant', width: 100 },
  { data: 'itemName', type: 'text', title: 'Item Name', width: 200, wordWrap: false },
  { data: 'quantity', type: 'numeric', title: 'Quantity', width: 80 },
  { data: 'uom', type: 'text', title: 'UoM', width: 120 },
  { data: 'remark', type: 'text', title: 'Remark', width: 120 },
  { data: 'status', type: 'text', title: 'Status', width: 80, readOnly: true },
  { data: 'letterNo', type: 'text', title: 'Letter No', width: 120 },
  {
    data: 'letterDate',
    type: 'date',
    title: 'Letter Date',
    dateFormat: 'YYYY-MM-DD',
    width: 120,
    correctFormat: true,
    datePickerConfig: {
      firstDay: 0,
      showWeekNumber: true,
      numberOfMonths: 1,
    },
  },
  { data: 'id', title: 'Preview', width: 80, readOnly: true, filterable: false },
  { data: 'submit', title: 'Submit', width: 80, readOnly: true, filterable: false },
  { data: 'delete', title: 'Delete', width: 80, readOnly: true, filterable: false },
];

export type TableRowData = {
  id?: string; // JettyRequestItem ID for document generation
  tenantName: string;
  itemName: string;
  quantity: string;
  uom: string;
  remark: string;
  status: string;
  letterNo: string;
  letterDate: string;
  preview?: string;
  submit: string;
  delete: string;
};