{"version": 3, "mappings": ";wlBAmBA,eAAeA,GACbC,EACAC,EACAC,EACAC,EACAC,EACA,CACI,IACFF,EAAW,EAAI,EAwBf,MAAMG,EAAQ,CACZ,YAAa,GACb,aAvBmB,CACnB,WAAYL,EAAQ,QAAU,GAC9B,SAAUA,EAAQ,UAAY,GAC9B,IAAKA,EAAQ,SAAW,EACxB,IAAKA,EAAQ,SAAW,GACxB,MAAOA,EAAQ,SAAW,GAC1B,OAAQA,EAAQ,QAAU,GAC1B,SAAUA,EAAQ,UAAY,GAC9B,WAAYA,EAAQ,WAAaA,EAAQ,WAAa,KACtD,OAAQ,OAAOG,GAAQ,MAAM,GAAK,EAClC,WAAYC,GAAY,QAAQ,MAAQD,GAAQ,UAAY,GAC5D,OAAQA,GAAQ,QAAU,GAC1B,MAAOC,GAAY,aAAa,MAAQD,GAAQ,SAAW,GAC3D,YAAaA,GAAQ,eAAiB,KACtC,cAAeA,GAAQ,iBAAmB,KAC1C,WAAYC,GAAY,kBAAkB,MAAQD,GAAQ,cAAgB,GAC1E,gBAAiBC,GAAY,uBAAuB,MAAQD,GAAQ,mBAAqB,GACzF,cAAe,IAAI,KAAK,EAAE,YAAY,EACtC,YAAa,EACf,CAKA,EAGM,CAAE,kFAAAG,CAAA,EAAsF,MAAMC,EAAA,kGAAAD,GAAA,aAAO,0BAAkB,OAAAE,KAAA,6FAAAF,CAAA,iCACvHG,EAAW,MAAMH,EAAkF,CACvG,KAAMD,CAAA,CACP,EAED,GAAII,EAAS,KAAM,CAEjB,MAAMC,EAASD,EAAS,KACpBC,EAAO,UAETT,EAAUS,EAAO,SAAS,EAG1B,MAAM,gDAAgD,CACxD,MAGA,MAAM,gDAAgD,OAE1C,CAEd,MAAM,8CAA8C,SACpD,CACAR,EAAW,EAAK,EAEpB,CAKA,eAAeS,GACbC,EACAC,EACAX,EACAY,EACAC,EACAf,EACA,CACI,IACFE,EAAW,EAAI,EAEf,KAAM,CAAE,2BAAAc,CAAA,EAA+B,MAAMT,EAAA,2CAAAS,GAAA,aAAO,0BAAkB,OAAAR,KAAA,sCAAAQ,CAAA,iCAChE,CAAE,MAAAC,CAAA,EAAU,MAAAV,EAAA,sBAAAU,GAAA,KAAM,QAAO,0BAAgB,OAAAT,KAAA,iBAAAS,CAAA,iCAY/C,IAJiB,MAAMD,EAA2B,CAChD,KAPY,CACZ,WAAYJ,EACZ,aAAAC,EACA,MAAO,wBACT,CAGQ,CACP,GAEY,KAAM,CACXI,EAAA,CACJ,MAAO,UACP,YAAa,sCACb,QAAS,UACV,EAEG,IACF,KAAM,CAAE,yBAAAC,CAAA,EAA6B,MAAAX,EAAA,yCAAAW,GAAA,KAAM,QAAO,uBAAqB,kCAAAA,CAAA,+BACvE,MAAMA,EAAyB,CAC7B,KAAM,CAAE,GAAIN,CAAmB,EAC/B,KAAM,CACJ,GAAGZ,EACH,SAAUA,EAAQ,UAAY,GAC9B,iBAAkBA,EAAQ,iBAC1B,SAAUA,EAAQ,UAAYA,EAAQ,UAAY,GAClD,OAAQA,EAAQ,QAAUA,EAAQ,QAAU,EAC5C,QAASA,EAAQ,SAAWA,EAAQ,SAAW,GAC/C,OAAQ,UACV,CACD,EACD,MAAMc,EAAY,eAAe,CAAE,SAAU,CAAC,cAAc,EAAG,EACzDG,EAAA,CACJ,MAAO,iBACP,YAAa,4BACb,QAAS,UACV,OACqB,CAEhBA,EAAA,CACJ,MAAO,uBACP,YAAa,qCACb,QAAS,cACV,EAEG,MAAAH,EAAY,eAAe,CAAE,SAAU,CAAC,eAAgBC,CAAc,EAAG,OAEzEE,EAAA,CACJ,MAAO,QACP,YAAa,gCACb,QAAS,cACV,QAEIE,EAAgB,CAGvB,KAAM,CAAE,MAAAF,CAAA,EAAU,MAAAV,EAAA,sBAAAU,CAAA,OAAM,QAAO,0BAAgB,OAAAT,KAAA,iBAAAS,CAAA,iCAC/C,IAAIG,EAAU,mDACV,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHC,EAAWD,EAA+B,SAAWC,GAGjDH,EAAA,CACJ,MAAO,gCACP,YAAaG,EACb,QAAS,cACV,SACD,CACAlB,EAAW,EAAK,EAEpB,CAIO,MAAMmB,GAAsB,CACjCC,EACArB,EACAsB,EACAC,EACArB,EACAC,IACG,CACHqB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAOD,MAAMC,EAFUV,EAAUK,CAAI,GAEP,GACjBM,EAAYV,EAAc,IAAII,CAAI,GAAK,GAavCO,EAAgB,kBAXFF,GAAS,CAACC,EAC1B,oJACA,6EAS+C,eAAeN,CAAI,kCAPlDK,EACfC,EAAY,yBAA2B,mBACxC,8CAK+G,KAH9FD,GAAS,CAACC,EAAY,GAAK,UAGoF,IAFjHA,EAAY,aAAe,SAEoG,YAClJP,EAAG,UAAYQ,EAET,MAAAC,EAAaT,EAAG,cAAc,yBAAyB,EACzDS,GAAcH,GAAS,CAACC,GACfE,EAAA,iBAAiB,QAAS,SAAY,CACzCnC,QAAUsB,EAAUK,CAAI,EAC1B3B,GAAS,IACL,MAAAD,GACJC,EACAC,EACCmC,GAAYZ,EAAgBG,EAAMS,CAAO,EAC1CjC,EACAC,CACF,CACF,CACD,CAEL,EAEWiC,GAAqB,CAChCf,EACAT,EACAU,EACAC,EACAV,EACAC,IACG,CACHU,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CAOD,MAAMC,EAFUV,EAAUK,CAAI,GAEP,GACjBM,EAAYV,EAAc,IAAII,CAAI,GAAK,GAcvCW,EAAe,kBAXDN,GAAS,CAACC,EAC1B,uJACA,6EAS8C,eAAeN,CAAI,iCAPjDK,EACfC,EAAY,6BAA+B,sBAC5C,6CAK6G,KAH5FD,GAAS,CAACC,EAAY,GAAK,UAGkF,IAF/GA,EAAY,gBAAkB,QAE+F,YAChJP,EAAG,UAAYY,EAET,MAAAC,EAAYb,EAAG,cAAc,wBAAwB,EACvDa,GAAaP,GAAS,CAACC,GACfM,EAAA,iBAAiB,QAAS,SAAY,CACxCvC,QAAUsB,EAAUK,CAAI,EAC1B3B,GAAS,IACL,MAAAW,GACJX,EAAQ,GACRa,EACCuB,GAAYZ,EAAgBG,EAAMS,CAAO,EAC1CtB,EACAC,EACAf,CACF,CACF,CACD,CAEL,EAGWwC,GAA0B1B,GAA6B,CAClE,GAAI,CAACA,EAAmB,UAAI,MAAM,oDAAoD,EACtF,MAAO,CACLW,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,IACG,CASH,GAFAL,EAAG,UAAY,GAEX,CAACD,EAAW,OAEhB,MAAMgB,EAAchB,EAAU,iBAAiBE,EAAM,aAAa,GAAK,CAAC,EAClEe,EAAWjB,EAAU,iBAAiBE,EAAM,UAAU,GAAK,GAI3DgB,EAAcF,EAAY,OAAS,EACrC,6JACA,8EAEEG,EAAcH,EAAY,OAAS,EACrC,qBAAqBA,EAAY,MAAM,IACvC,iBAEEI,EAAeJ,EAAY,OAAS,EAAI,GAAK,WAE7CK,EAAmB,kBAAkBH,CAAW,eAAehB,CAAI,qCAAqCiB,CAAW,KAAKC,CAAY,gBAAgBJ,EAAY,MAAM,aAC5Kf,EAAG,UAAYoB,EAET,MAAAC,EAAMrB,EAAG,cAAc,4BAA4B,EACrDqB,GAAON,EAAY,OAAS,GAC1BM,EAAA,iBAAiB,QAAS,IAAM,CAE3B,aAAM,wBAAwBL,CAAQ,gCAAgC,EAC9E,CAEL,CACF,EAEaM,GAAwB,CACnCC,EACAC,EACApC,EACAQ,EAA4B,GAC5BrB,EACAsB,EACAC,EACAT,EACAZ,EACAC,IACG,CAEG,MAAA+C,EAAcF,EAAQ,IAASG,KAAE,MAAQ,EAAE,EAAE,OAAeC,OAAS,EAAE,EAGvEC,EAAuBJ,EAAiB,IAAUK,KAAG,MAAQ,EAAE,EAAE,OAAeF,OAAS,EAAE,EAE1F,OACL,CAAE,KAAM,KAAM,MAAO,KAAM,KAAM,OAAQ,MAAO,GAAI,EACpD,CAAE,KAAM,WAAY,MAAO,WAAY,KAAM,OAAQ,MAAO,GAAI,EAChE,CAAE,KAAM,mBAAoB,MAAO,mBAAoB,KAAM,OAAQ,MAAO,GAAI,EAChF,CACE,KAAM,SACN,MAAO,SACP,KAAM,eACN,MAAO,IACP,OAAQF,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CACE,KAAM,kBACN,MAAO,mBACP,KAAM,eACN,MAAO,IACP,OAAQG,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,MAAO,GAAI,EACjE,CAAE,KAAM,UAAW,MAAO,WAAY,KAAM,UAAW,MAAO,EAAG,EACjE,CAAE,KAAM,UAAW,MAAO,MAAO,KAAM,OAAQ,MAAO,GAAI,EAC1D,CAAE,KAAM,UAAW,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,MAAO,GAAI,EACjE,CAAE,KAAM,aAAc,MAAO,cAAe,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EACpH,CAAE,KAAM,OAAQ,MAAO,QAAS,KAAM,OAAQ,MAAO,GAAI,EACzD,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC5G,CAAE,KAAM,QAAS,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC3D,CAAE,KAAM,QAAS,MAAO,SAAU,KAAM,OAAQ,MAAO,GAAI,EAC3D,CAAE,KAAM,UAAW,MAAO,WAAY,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC9G,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAChH,CAAE,KAAM,SAAU,MAAO,UAAW,KAAM,OAAQ,MAAO,GAAI,EAC7D,CAAE,KAAM,WAAY,MAAO,YAAa,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAChH,CAAE,KAAM,cAAe,MAAO,eAAgB,KAAM,UAAW,MAAO,GAAI,EAC1E,CAAE,KAAM,aAAc,MAAO,cAAe,KAAM,OAAQ,MAAO,GAAI,EACrE,CAAE,KAAM,SAAU,MAAO,SAAU,KAAM,OAAQ,MAAO,IAAK,SAAU,EAAK,EAC5E,CAAE,KAAM,cAAe,MAAO,aAAc,MAAO,IAAK,SAAUd,GAAuB1B,CAAW,EAAG,SAAU,GAAM,WAAY,EAAM,EACzI,CACE,KAAM,UACN,MAAO,UACP,MAAO,IACP,SAAUb,GAAasB,GAAiBC,EACpCH,GAAoBC,EAAWrB,EAAWsB,EAAeC,EAAiBrB,EAAQC,CAAU,EAC5F,OACJ,SAAU,GACV,WAAY,EACd,EACA,CACE,KAAM,SACN,MAAO,SACP,MAAO,IACP,SAAUiC,GAAmBf,EAAW,QAASC,EAAgBC,EAAkBV,EAAaC,GAAkB,EAAE,EACpH,SAAU,GACV,WAAY,GAEhB,CACF,EAGkCiC,GAAsB,GAAI,GAAI,CAAiB,GCtapE,MAAAQ,GAA0BC,GAAS,CAC9C,OAAQC,EAAW,MAAI,EAAG,oBAAoB,EAC9C,SAAUA,EAAW,MAAI,EAAG,yBAAyB,EACrD,OAAQA,EAAW,MAAI,EAAG,oBAAoB,EAC9C,YAAaA,EAAW,MAAI,EAAG,0BAA0B,EACzD,cAAeA,EAAW,MAAI,EAAG,0BAA0B,EAC3D,gBAAiBA,EAAW,MAAI,EAAG,4BAA4B,EAC/D,aAAcA,EAAS,EAAE,SAAS,EAClC,kBAAmBA,EAAS,EAAE,SAAS,EACvC,QAASA,EAAS,EAAE,SAAS,EAC7B,QAASA,EAAS,EAAE,SAAS,EAC7B,SAAUA,EAAS,EAAE,SAAS,EAC9B,SAAUA,EAAS,EAAE,SAAS,EAC9B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAW,MAAI,EAAG,uBAAuB,EACpD,iBAAkBA,EAAS,EAAE,SAAS,EACtC,QAASA,EAAW,MAAI,EAAG,mBAAmB,EAC9C,OAAQA,EAAS,EAAE,SAAS,EAC5B,WAAYA,EAAS,EAAE,SAAS,EAChC,SAAUA,EAAS,EAAE,SAAS,EAC9B,WAAYA,EAAS,EAAE,SAAS,EAChC,gBAAiBA,EAAS,EAAE,SAAS,EAErC,QAASA,EAAS,EAAE,SAAS,EAC7B,UAAWA,EAAS,EAAE,SAAS,EAC/B,WAAYA,EAAS,EAAE,SAAS,CAClC,CAAC,EC5BYC,GAAwBF,GAAS,CAC5C,GAAIC,EAAS,EAAE,SAAS,EACxB,SAAUA,EAAE,EAAS,WAAW,SAAS,EACzC,QAASE,GAAE,EAAS,WAAW,SAAS,EACxC,QAASF,EAAE,EAAS,WAAW,SAAS,EACxC,QAASA,EAAE,EAAS,WAAW,SAAS,EACxC,OAAQA,EAAE,EAAS,WAAW,SAAS,EACvC,SAAUA,EAAE,EAAS,WAAW,SAAS,EACzC,gBAAiBA,EAAE,EAAS,WAAW,SAAS,EAChD,kBAAmBA,EAAE,EAAS,WAAW,SAAS,EAClD,SAAUA,EAAE,EAAS,WAAW,SAAS,EACzC,WAAYA,EAAE,EAAS,WAAW,SAAS,EAC3C,OAAQA,EAAE,EAAS,WAAW,SAAS,EACvC,iBAAkBA,EAAE,EAAS,WAAW,SAAS,CAEnD,CAAC,ECQDG,GAAmB,EAmCnB,SAASC,GAAwBC,EAAkF,CAE3G,MAAAC,EAAWC,GAAiBA,EAAMA,EAAI,MAAM,EAAG,EAAE,EAAI,GACrDC,EAAQ,IAAU,WAAO,cAAc,MAAM,EAAG,EAAE,EACjD,OACL,OAAQH,EAAI,QAAU,GACtB,SAAUA,EAAI,UAAY,GAC1B,OAAQA,EAAI,QAAU,GACtB,YAAaA,EAAI,cAAgBC,EAAQD,EAAI,aAAa,GAAKG,KAC/D,cAAeH,EAAI,eAAiB,GACpC,gBAAiBA,EAAI,iBAAmB,GACxC,UAAWA,EAAI,WAAa,GAC5B,aAAcA,EAAI,cAAgB,GAClC,kBAAmBA,EAAI,mBAAqB,GAC5C,QAASA,EAAI,SAAW,GACxB,QAASA,EAAI,SAAW,GACxB,UAAWA,EAAI,WAAa,GAC5B,WAAYA,EAAI,YAAc,GAC9B,GAAIA,EAAI,iBAAmB,CAAE,iBAAkBA,EAAI,kBAAqB,EAC1E,CACF,CAGa,MAAAI,GAAkLC,GAAU,CACjM,MAAE,KAAMnB,EAAU,GAAI,UAAWoB,GAAmBC,EAAS,CACjE,SAAU,CAAC,SAAS,EACpB,QAAS,IACPC,EAAgB,cAAc,CAAE,KAAM,EAAG,eAAgB,GAAK,CAAC,EAC5D,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,GACvC,EAEK,CAAE,KAAMtB,EAAmB,GAAI,UAAWuB,GAA4BH,EAAS,CACnF,SAAU,CAAC,kBAAkB,EAC7B,QAAS,IACPC,EAAgB,uBAAuB,CAAE,KAAM,EAAG,eAAgB,GAAM,CAAC,EACtE,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,GACvC,EAGD,OAAIH,GAAkBI,EAAgCC,MAAC,OAAI,SAAe,oBAGxEA,EAAA,IAACC,GAAA,CACE,GAAGP,EACJ,QAASpB,GAAsBC,EAASC,EAAkBkB,EAAM,WAAW,EAC3E,QAAAnB,EACA,iBAAAC,EACA,YAAakB,EAAM,YACnB,WAAYA,EAAM,WAClB,UAAWA,EAAM,UACnB,CAEJ,EAGMO,GAAkD,CAAC,CACvD,KAAAC,EACA,cAAAC,EACA,aAAAC,EACA,SAAAC,EACA,aAAAC,EAAexB,GACf,WAAAyB,EAAatB,GACb,aAAAuB,EAAe,GACf,MAAAC,EAAQ,sBACR,QAAAlC,EACA,iBAAAC,EACA,YAAApC,EACA,WAAAV,EACA,kBAAAgF,EAAoB,GACpB,UAAAC,CACF,IAAM,CACJ,KAAM,CAACC,EAAOC,CAAQ,EAAIC,WAAgCV,CAAY,EAChE,CAACW,EAAYC,CAAa,EAAIF,WAAmB,EAAE,EACnD,CAACjE,EAAeoE,CAAgB,EAAIH,WAA+B,IAAI,GAAK,EAC5EI,EAAcC,SAA2B,IAAI,EAC7C,CAACC,EAAqBC,CAAsB,EAAIP,WAAS,EAAK,EAC9D,CAACQ,EAAoBC,EAAqB,EAAIT,WAAS,EAAE,EACzD,CAACU,EAAQC,EAAS,EAAIX,WAAiB,EAAE,EACzC,CAACY,GAAaC,EAAc,EAAIb,WAAwB,IAAI,EAC5D,CAACc,GAAsBC,EAAuB,EAAIf,WAAS,EAAK,EAChEgB,GAAUX,SAA2B,IAAI,EAEzCY,EAAUC,GAA+B,CAC7C,SAAUC,GAAY3B,CAAY,EAClC,cAAe,CACb,GAAGlB,GAAwBe,CAAa,EACxC,OAAQD,IAAS,SAAWsB,EAASrB,EAAc,QAAU,EAC/D,EACA,KAAM,SACP,EACK,CAAE,SAAA+B,EAAU,aAAAC,GAAc,UAAW,CAAE,OAAAC,GAAU,MAAAC,GAAO,SAAAC,CAAA,EAAaP,EAQrEQ,EALkBR,EAAQ,MAAM,eAAe,OAErC,KAAK,EACV,cAAc,MAAM,EAAG,EAAE,EAK9B,CAAE,KAAMS,CAAgB,EAAI5C,EAAwB,CACxD,SAAU,CAAC,iBAAkB2C,EAAUrC,CAAI,EAC3C,QAAS,IAAML,EAAgB,8BAA8B0C,CAAQ,EAAE,KAAKzC,GAAO,OAAOA,EAAI,MAAQ,EAAE,CAAC,EACzG,QAASI,IAAS,UAAY,CAAC,CAACqC,CAAA,CACjC,EAEDE,YAAU,IAAM,CACVvC,IAAS,UAAYsC,GACvBf,GAAUe,CAAe,CAC3B,EACC,CAACA,EAAiBtC,CAAI,CAAC,EAE1BuC,YAAU,IAAM,CACVvC,IAAS,UAAYsB,GACvBc,EAAS,SAAUd,CAAM,CAE1B,GAACA,EAAQtB,EAAMoC,CAAQ,CAAC,EAuBrB,MAAAI,GAAiBC,GAAwB,CAC7CpB,GAAsBoB,CAAW,EACjCtB,EAAuB,EAAI,CAC7B,EAGMuB,GAA2B,CAACC,EAAanF,IAAqB,CAClEuD,EAAyB6B,GAAA,CACjB,MAAAC,EAAS,IAAI,IAAID,CAAI,EAC3B,OAAIpF,EACKqF,EAAA,IAAIF,EAAK,EAAI,EAEpBE,EAAO,OAAOF,CAAG,EAEZE,CAAA,CACR,CACH,EAGMC,GAAgBC,UAAQ,IAAMtC,EAAU,QAAUuC,EAAE,KAAOnB,EAAQ,MAAM,SAAS,CAAC,EAAG,CAACpB,EAAWoB,CAAO,CAAC,EAE1GoB,GAAiB,SAAY,CACjC,MAAMC,EAAajD,EAAc,IAAMzE,GAAY,IAAM,GACzD,GAAI,CAAC0H,EAAY,CACf7G,EAAM,CAAE,MAAO,QAAS,YAAa,0BAA2B,QAAS,cAAe,EACxF,OAEFsF,GAAwB,EAAI,EACxB,IACF,MAAMvF,GAA2B,CAC/B,KAAM,CACJ,WAAA8G,EACA,aAAc,QACd,MAAO,GACT,CACD,EACD7G,EAAM,CAAE,MAAO,UAAW,YAAa,mCAAoC,QAAS,UAAW,EAG/F,MAAM8G,EAAgBnC,EAAY,SAAS,aAAa,mBAAqB,CAAC,EAIxEoC,EAA0B,CAC9B,GAJoBvB,EAAQ,UAAU,EAKtC,UAAW,SACb,EAGM,MAAA1B,EAASiD,EAAyBD,CAAY,QAE7C5G,EAAgB,CACvB,IAAIC,EAAU,iCACV,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAAgC,SAAY,WAClHC,EAAWD,EAA8B,SAErCF,EAAA,CACJ,MAAO,QACP,YAAaG,EACb,QAAS,cACV,SACD,CACAmF,GAAwB,EAAK,EAEjC,EAGM0B,GAAmBjF,GACvBC,EACAC,EACApC,EACAwE,EACA8B,GACA7F,EACA+F,GACA,OAAOzC,EAAc,IAAM,EAAE,EAC7B4B,EAAQ,UAAU,EAClBrG,CACF,EAIA+G,YAAU,IAAM,CACR,MAAAe,EAAMrD,EAAc,IAAMA,EAAc,OAE1CD,IAAS,QAAUsD,GAAO1B,GAAQ,UAAY0B,IAC1CnB,GAAAjD,GAAwBe,CAAa,CAAC,EAEnCU,EAAAT,EAAa,IAAIqD,IAAM,CAAE,GAAGA,EAAG,iBAAmBA,EAAyC,gBAAiB,EAAE,CAAC,EACxH3B,GAAQ,QAAU0B,IAEnB,CAACrD,EAAeC,EAAcF,EAAMmC,EAAK,CAAC,EAG7CI,YAAU,IAAM,CACVtC,EAAc,kBACPmC,EAAA,mBAAoBnC,EAAc,gBAAgB,CAE5D,GAACA,EAAc,iBAAkBmC,CAAQ,CAAC,EAEvC,MAAAoB,GAAiBC,GAAyC,CAC9D,MAAMvB,EAAmB,CAAC,EACrB,OAAAuB,EAAA,QAAQ,CAACC,EAAMC,IAAQ,CACpB,MAAA7H,EAASuE,EAAW,UAAUqD,CAAI,EACnC5H,EAAO,QAGVoG,EAAOyB,CAAG,EAAI,GAFdzB,EAAOyB,CAAG,EAAI,OAAO,OAAO7H,EAAO,MAAM,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,IAAI,CAGlF,CACD,EACDgF,EAAcoB,CAAM,EACbA,EAAO,MAAW0B,GAAA,CAACA,CAAC,CAC7B,EAEMC,GAAe,MAAOtI,GAAkC,CAC5D,MAAM4H,EAAgBnC,EAAY,SAAS,aAAa,mBAAqB,CAAC,EAGxE8C,EAAiB,CACrB,QAAS,GACT,SAAU,GACV,UAAW,GACX,UAAW,GACX,WAAY,GACZ,WAAY,GACZ,gBAAiB,EACnB,EAEMC,EAAqB,CAAE,GAAGD,EAAgB,GAAGvI,EAAQ,iBAAkBA,EAAO,gBAAiB,EAE/FyI,EAAoBb,EAAa,IAAYO,GAAA,CAEjD,KAAM,CAAE,iBAAAO,EAAkB,GAAGC,EAAA,EAASR,EAChCS,EAAsD,CAAE,GAAGL,EAAgB,GAAGI,GAAM,iBAAkBD,GAAoB,MAAU,EAC1I,GAAIP,EAAK,OAAQ,CACf,MAAMU,EAAS/F,EAAQ,QAAUG,EAAE,OAASkF,EAAK,MAAM,EACvCS,EAAA,SAAWC,GAAQ,IAAM,QAEzCD,EAAgB,SAAW,GAE7B,GAAIT,EAAK,gBAAiB,CACxB,MAAMW,EAAkB/F,EAAiB,QAAWK,EAAG,OAAS+E,EAAK,eAAe,EACpES,EAAA,kBAAoBE,GAAiB,IAAM,QAE3DF,EAAgB,kBAAoB,GAGtC,MAAMG,EAAUpE,EAAsD,QAAUqD,EAAE,KAAOG,EAAK,EAAE,EAC5F,OAAAY,GAAUA,EAAO,mBACnBH,EAAgB,iBAAmBG,EAAO,kBAErCH,CAAA,CACR,EAEG,GAACX,GAAcQ,CAAiB,EACpC,CAAAvC,GAAe,IAAI,EACf,IACI,MAAAtB,EAAS4D,EAAoBC,CAAiB,QAE7CzH,EAAgB,CACvB,IAAIC,EAAU,oBACV,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAAgC,SAAY,WAClHC,EAAWD,EAA8B,SAE3CkF,GAAejF,CAAO,GAG1B,EAEM+H,GAAgB,IAAM,CAC1B5D,KAAiB,CAAC,GAAGiC,EAAM,CAAyB,EAAC,CACvD,EAEM,CAAE,MAAAvG,CAAM,EAAImI,GAAS,EAGzB,OAAAC,EAAA,KAAC,MAAI,WAAU,iBACb,UAACA,OAAA,OAAI,UAAU,qEACb,UAAA3E,MAAC,OAAI,UAAU,OACb,SAAC2E,EAAA,YAAI,UAAU,oCACb,UAAAA,OAAC,MACC,WAAC3E,EAAA,UAAG,UAAU,kDAAmD,SAAMS,EAAA,EACvET,MAAC,MAAI,WAAU,uCAAwC,IACzD,EAECG,EAAc,WACZwE,OAAA,OAAI,UAAU,8BACb,UAAC3E,EAAA,YAAK,UAAU,uDAAuD,SAAO,YAC7EA,EAAA,YAAK,UAAU,wIACb,WAAc,SACjB,GACF,IAEJ,CACF,GACAA,MAAC4E,GAAc,IAAG7C,EAChB,SAAA4C,OAAC,OAAK,UAAUxC,GAAa4B,EAAY,EAAG,UAAU,YACnD,UAAArC,IACE1B,EAAA,WAAI,UAAU,4BAA6B,SAAY0B,GAAA,EAE1DiD,OAAC,MAAI,WAAU,wCACb,UAACA,OAAAE,EAAA,CAAY,YAAa,GACxB,UAAAF,EAAA,KAACG,EAAU,OAAM,SAAS,WAAW,QACnC,UAAA9E,EAAA,IAAC+E,EAAA,CACE,GAAG7C,EAAS,QAAQ,EACrB,MAAOH,EAAQ,MAAM,QAAQ,EAC7B,SAAU7B,IAAS,SACnB,SAAUA,IAAS,OACrB,EACCkC,EAAO,QACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,OAAO,OAAkB,IAE5E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,YAAY,WAAW,QACtC,UAAAH,EAAA,KAACK,GAAO,OAAOjD,EAAQ,MAAM,WAAW,GAAK,GAAI,cAAoBkD,GAAA3C,EAAS,YAAa2C,CAAC,EAC1F,UAACjF,MAAAkF,GAAA,CAAc,KAAM,KAAM,UAAU,SACnC,SAAClF,MAAAmF,GAAA,CAAY,YAAY,mBAAmB,CAC9C,UACCC,GACC,WAACpF,EAAA,IAAAqF,GAAA,CAAW,MAAM,KAAK,SAAE,OACxBrF,EAAA,IAAAqF,GAAA,CAAW,MAAM,MAAM,SAAG,QAC7B,IACF,EACCjD,EAAO,WACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,UAAU,OAAkB,IAE/E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,UAAA9E,EAAA,IAACsF,EAAA,CACC,KAAK,WACL,QAASvD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAwD,CAAA,IACTvF,EAAA,IAACwF,GAAA,CACC,MAAOD,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,mBACZ,SAAU/E,CAAA,EACZ,CAEJ,EACC4B,EAAO,UACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,SAAS,OAAkB,IAE9E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,aAAa,WAAW,QACvC,UAAA9E,EAAA,IAACsF,EAAA,CACC,KAAK,UACL,QAASvD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAwD,CAAA,IACTvF,EAAA,IAACwF,GAAA,CACC,MAAOD,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,kBACZ,SAAU/E,CAAA,EACZ,CAEJ,EACC4B,EAAO,SACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,QAAQ,OAAkB,IAE7E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,SAAS,WAAW,QACnC,UAAA9E,MAAC+E,EAAO,IAAG7C,EAAS,QAAQ,CAAG,GAC9BE,EAAO,QACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,OAAO,OAAkB,GAE5E,IACF,EAEAuC,OAACE,EAAY,aAAa,GACxB,UAAAF,EAAA,KAACG,EAAU,OAAM,QAAQ,WAAW,QAClC,UAAA9E,EAAA,IAACsF,EAAA,CACC,KAAK,UACL,QAASvD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAwD,CAAA,IACTvF,EAAA,IAACyF,GAAA,CACC,MAAOF,EAAM,MACb,cAAeA,EAAM,SACrB,YAAY,kBACZ,SAAU/E,CAAA,EACZ,CAEJ,EACC4B,EAAO,SACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,QAAQ,OAAkB,IAE7E,EACCuC,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,UAAA9E,EAAA,IAAC+E,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,WAAW,EAAG,EACvDE,EAAO,WACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,UAAU,OAAkB,IAE/E,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,UAAA9E,EAAA,IAAC+E,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,YAAY,EAAG,EACxDE,EAAO,YACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,WAAW,OAAkB,IAEhF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,UAAA9E,EAAA,IAAC+E,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,eAAe,EAAG,EAC3DE,EAAO,eACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,cAAc,OAAkB,IAEnF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,iBAAiB,WAAW,QAC3C,UAAA9E,EAAA,IAAC+E,GAAM,KAAK,iBAAkB,GAAG7C,EAAS,iBAAiB,EAAG,EAC7DE,EAAO,iBACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,gBAAgB,OAAkB,GAErF,IACF,EAEAuC,OAACE,EAAY,aAAa,GACxB,UAAAF,EAAA,KAACG,EAAU,OAAM,eAAe,WAAW,QACzC,UAAA9E,EAAA,IAAC+E,GAAM,KAAK,OAAQ,GAAG7C,EAAS,aAAa,EAAG,EAC/CE,EAAO,aACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,YAAY,OAAkB,IAEjF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,UAAA9E,EAAA,IAACsF,EAAA,CACC,KAAK,eACL,QAASvD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAwD,CAAA,IACTvF,EAAA,IAAC0F,GAAA,CACC,MAAOH,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,wBACZ,SAAU/E,CAAA,EACZ,CAEJ,EACC4B,EAAO,cACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,aAAa,OAAkB,IAElF,EAECuC,EAAA,KAAAG,EAAA,CAAU,MAAM,mBAAmB,WAAW,QAC7C,UAAA9E,EAAA,IAACsF,EAAA,CACC,KAAK,oBACL,QAASvD,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAwD,CAAA,IACTvF,EAAA,IAAC2F,GAAA,CACC,MAAOJ,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,6BACZ,SAAU/E,CAAA,EACZ,CAEJ,EACC4B,EAAO,mBACLpC,MAAA,QAAK,UAAU,uBAAwB,SAAAoC,EAAO,kBAAkB,OAAkB,GAEvF,GACF,IACF,EACAuC,OAAC,MAAI,WAAU,OACb,UAAC3E,EAAA,aAAM,UAAU,yBAAyB,SAAK,UAC/CA,EAAA,IAAC4F,GAAA,CACC,IAAK1E,EACL,UAAU,gBACV,KAAMN,EACN,QAAS2C,GACT,WAAYA,GAAiB,IAAKsC,GAAkB,OAAOA,GAAQ,UAAYA,GAAO,UAAWA,EAAOA,EAA2B,MAAQ,MAAU,EAAE,OAAQnH,GAA4B,OAAOA,GAAM,QAAQ,EAChN,WAAY,GACZ,OAAO,OACP,WAAW,gCACX,SAAS,MACT,YAAa,GACb,mBAAoB,GACpB,gBAAiB,GACjB,eAAgB,GAChB,YAAa,GACb,UAAW,EACX,aAAc,GACd,QAAS,GACT,UAAW,GACX,cAAe,CACb,iBAAkB,GAClB,WAAY,GACZ,QAAS,CAAC,EAAG,EAAG,CAAC,CACnB,EACA,MAAM,OACN,gBAAiB,GACnB,EACCqC,EAAW,KAAK+C,GAAKA,CAAC,SACpB,MAAI,WAAU,4BACZ,SAAA/C,EAAW,IAAI,CAAC+E,EAAKjC,IAAQiC,UAAQ,MAAc,kBAAKjC,EAAM,EAAE,KAAGiC,CAAA,CAArB,EAAAjC,CAAyB,CAAM,CAChF,IAEJ,EACAc,OAAC,MAAI,WAAU,yBACZ,UACCjE,GAAAV,MAAC+F,EAAO,MAAK,SAAS,QAAQ,UAAU,QAAStB,GAAe,SAAUjE,EAAc,SAExF,eAGD,OAAOwC,IAAe,cAAiB,WAAaA,GAAc,cACjEhD,EAAA,IAAC+F,EAAA,CACC,KAAK,SACL,QAAQ,UACR,QAAS5C,GACT,SACE3C,GACAoB,IACAzB,EAAc,YAAc,WAC5BA,EAAc,YAAc,WAG7B,YAAuB,gBAAkB,kBAC5C,EAEDH,EAAA,IAAA+F,EAAA,CAAO,KAAK,SAAS,SAAUvF,EAC7B,SAAAA,EAAgBN,IAAS,OAAS,YAAc,cAAkBA,IAAS,OAAS,eAAiB,QACxG,GACF,IACF,CACF,IACF,EACAF,EAAA,IAACgG,GAAA,CACC,OAAQ5E,EACR,aAAcC,EACd,YAAaC,CAAA,EACf,EACF,CAEJ", "names": ["generateAndOpenDocument", "rowData", "onPreview", "setLoading", "header", "vesselData", "input", "postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment", "__vitePreload", "n", "response", "result", "submitForApproval", "jettyRequestItemId", "documentType", "queryClient", "jettyRequestId", "postApiIdjasApprovalSubmit", "toast", "putApiEkbBoundedZoneById", "error", "message", "renderPreviewButton", "tableData", "loadingStates", "setLoadingState", "_instance", "td", "_row", "_col", "_prop", "_value", "_cellProperties", "hasId", "isLoading", "previewButton", "previewBtn", "loading", "renderSubmitButton", "submitButton", "submitBtn", "renderAttachmentButton", "attachments", "itemName", "buttonClass", "buttonTitle", "disabledAttr", "attachmentButton", "btn", "getLocalVesselColumns", "tenants", "businessPartners", "tenantNames", "t", "name", "businessPartnerNames", "bp", "localVesselHeaderSchema", "z.object", "z.string", "localVesselItemSchema", "z.number", "registerAllModules", "toLocalVesselHeaderForm", "dto", "getDate", "val", "today", "LocalVesselFormWithData", "props", "loadingTenants", "useQuery", "ekbProxyService", "res", "loadingBusinessPartners", "jsx", "LocalVesselForm", "mode", "initialHeader", "initialItems", "onSubmit", "headerSchema", "itemSchema", "isSubmitting", "title", "showAddLineButton", "jettyList", "items", "setItems", "useState", "itemErrors", "setItemErrors", "setLoadingStates", "hotTableRef", "useRef", "isPreviewDialogOpen", "setIsPreviewDialogOpen", "previewDocumentSrc", "setPreviewDocumentSrc", "doc<PERSON>um", "setDocNum", "submitError", "setSubmitError", "isSubmittingApproval", "setIsSubmittingApproval", "prev<PERSON><PERSON>", "methods", "useForm", "zodResolver", "register", "handleSubmit", "errors", "reset", "setValue", "postDate", "generatedDocNum", "useEffect", "handlePreview", "documentSrc", "handleLoadingStateChange", "row", "prev", "newMap", "<PERSON><PERSON><PERSON><PERSON>", "useMemo", "j", "handleApproval", "documentId", "currentItems", "headerWithWaitingStatus", "generatedColumns", "key", "i", "validateItems", "data", "item", "idx", "e", "onFormSubmit", "requiredFields", "headerWithRequired", "itemsWithRequired", "concurrencyStamp", "rest", "transformedItem", "tenant", "business<PERSON><PERSON>ner", "latest", "handleAddLine", "useToast", "jsxs", "FormProvider", "FormSection", "FormField", "Input", "Select", "v", "SelectTrigger", "SelectValue", "SelectContent", "SelectItem", "Controller", "field", "VesselSelect", "JettySelect", "PortOfLoadingSelect", "DestinationPortSelect", "HotTable", "col", "err", "<PERSON><PERSON>", "DocumentPreviewDialog"], "ignoreList": [], "sources": ["../../../../../frontend/src/components/jetty/vessel/local/local-vessel-columns.tsx", "../../../../../frontend/src/components/jetty/vessel/local/local-vessel-header-schema.ts", "../../../../../frontend/src/components/jetty/vessel/local/local-vessel-item-schema.ts", "../../../../../frontend/src/components/jetty/vessel/local/local-vessel-form.tsx"], "sourcesContent": ["import type { BusinessPartnerDto, LocalVesselWithItemsDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport Handsontable from 'handsontable';\r\nimport type { LocalVesselHeaderForm } from './local-vessel-header-schema';\r\nimport type { LocalVesselItemForm } from './local-vessel-item-schema';\r\n\r\n// Type for row data that includes id and status\r\ntype TableRowData = LocalVesselItemForm & {\r\n  id?: string;\r\n  status?: string;\r\n  concurrencyStamp?: string;\r\n  headerId?: string;\r\n  docNum?: number | undefined;\r\n  agentId?: string;\r\n};\r\n\r\n/**\r\n * Generates and opens an application document for preview\r\n */\r\nasync function generateAndOpenDocument(\r\n  rowData: TableRowData,\r\n  onPreview: (documentSrc: string) => void,\r\n  setLoading: (loading: boolean) => void,\r\n  header?: LocalVesselHeaderForm,\r\n  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects\r\n) {\r\n  try {\r\n    setLoading(true);\r\n\r\n    // Create the document data payload from row data and header\r\n    const documentData = {\r\n      tenantName: rowData.tenant || '',\r\n      itemName: rowData.itemName || '',\r\n      qty: rowData.itemQty || 0,\r\n      uoM: rowData.unitQty || '',\r\n      notes: rowData.remarks || '',\r\n      status: rowData.status || '',\r\n      letterNo: rowData.letterNo || '',\r\n      letterDate: rowData.letterDate ? rowData.letterDate : null,\r\n      docNum: Number(header?.docNum) || 0,\r\n      vesselName: vesselData?.vessel?.name || header?.vesselId || '',\r\n      voyage: header?.voyage || '',\r\n      jetty: vesselData?.masterJetty?.name || header?.jettyId || '',\r\n      arrivalDate: header?.vesselArrival || null,\r\n      departureDate: header?.vesselDeparture || null,\r\n      portOrigin: vesselData?.masterPortOrigin?.name || header?.portOriginId || '',\r\n      destinationPort: vesselData?.masterDestinationPort?.name || header?.destinationPortId || '',\r\n      generatedDate: new Date().toISOString(),\r\n      generatedBy: '', // This could be set from current user\r\n    };\r\n\r\n    const input = {\r\n      generatePdf: true,\r\n      documentData: documentData,\r\n    };\r\n\r\n    // Use the generated SDK function\r\n    const { postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment } = await import('@/client/sdk.gen');\r\n    const response = await postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment({\r\n      body: input\r\n    });\r\n\r\n    if (response.data) {\r\n      // The response.data is the custom object from the controller\r\n      const result = response.data;\r\n      if (result.streamUrl) {\r\n        // Open the document in the preview dialog\r\n        onPreview(result.streamUrl);\r\n      } else {\r\n        console.error('Failed to generate document:', result);\r\n        alert('Failed to generate document. Please try again.');\r\n      }\r\n    } else {\r\n      console.error('No response data received');\r\n      alert('Failed to generate document. Please try again.');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error generating document:', error);\r\n    alert('Error generating document. Please try again.');\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\n/**\r\n * Submits a document for approval\r\n */\r\nasync function submitForApproval(\r\n  jettyRequestItemId: string,\r\n  documentType: string,\r\n  setLoading: (loading: boolean) => void,\r\n  queryClient: QueryClient,\r\n  jettyRequestId: string,\r\n  rowData: TableRowData // Added rowData parameter\r\n) {\r\n  try {\r\n    setLoading(true);\r\n\r\n    const { postApiIdjasApprovalSubmit } = await import('@/client/sdk.gen');\r\n    const { toast } = await import('@/lib/useToast');\r\n\r\n    const input = {\r\n      documentId: jettyRequestItemId,\r\n      documentType: documentType,\r\n      notes: 'Submitted for approval'\r\n    };\r\n\r\n    const response = await postApiIdjasApprovalSubmit({\r\n      body: input\r\n    });\r\n\r\n    if (response.data) {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Successfully submitted for approval',\r\n        variant: 'default',\r\n      });\r\n      // Update status to Pending after approval submission\r\n      try {\r\n        const { putApiEkbBoundedZoneById } = await import('@/clientEkb/sdk.gen');\r\n        await putApiEkbBoundedZoneById({\r\n          path: { id: jettyRequestItemId },\r\n          body: {\r\n            ...rowData,\r\n            tenantId: rowData.tenantId ?? '',\r\n            concurrencyStamp: rowData.concurrencyStamp,\r\n            headerId: rowData.headerId ?? rowData.headerId ?? '',\r\n            docNum: rowData.docNum ?? rowData.docNum ?? 0,\r\n            agentId: rowData.agentId ?? rowData.agentId ?? '',\r\n            status: 'Pending',\r\n          }\r\n        });\r\n        await queryClient.refetchQueries({ queryKey: ['local-vessel'] });\r\n        toast({\r\n          title: 'Status Updated',\r\n          description: 'Status updated to Pending',\r\n          variant: 'default',\r\n        });\r\n      } catch (_err: unknown) {\r\n        console.log(\"error\", _err)\r\n        toast({\r\n          title: 'Status Update Failed',\r\n          description: 'Failed to update status to Pending',\r\n          variant: 'destructive',\r\n        });\r\n      }\r\n      await queryClient.refetchQueries({ queryKey: ['local-vessel', jettyRequestId] });\r\n    } else {\r\n      toast({\r\n        title: 'Error',\r\n        description: 'Failed to submit for approval',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  } catch (error: unknown) {\r\n    console.error('Error submitting for approval:', error);\r\n\r\n    const { toast } = await import('@/lib/useToast');\r\n    let message = 'Error submitting for approval. Please try again.';\r\n    if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n      message = (error as { message?: string }).message ?? message;\r\n    }\r\n\r\n    toast({\r\n      title: 'Error submitting for approval',\r\n      description: message,\r\n      variant: 'destructive',\r\n    });\r\n  } finally {\r\n    setLoading(false);\r\n  }\r\n}\r\n\r\n// Place PreviewButtonProps and PreviewButton at the top level\r\n\r\nexport const renderPreviewButton = (\r\n  tableData: TableRowData[],\r\n  onPreview: (documentSrc: string) => void,\r\n  loadingStates: Map<number, boolean>,\r\n  setLoadingState: (row: number, loading: boolean) => void,\r\n  header?: LocalVesselHeaderForm,\r\n  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects\r\n) => (\r\n  _instance: Handsontable.Core | undefined,\r\n  td: HTMLTableCellElement,\r\n  _row: number,\r\n  _col: number,\r\n  _prop: string | number,\r\n  _value: unknown,\r\n  _cellProperties: Handsontable.CellProperties\r\n) => {\r\n    void _col;\r\n    void _prop;\r\n    void _value;\r\n    void _cellProperties;\r\n    const rowData = tableData[_row];\r\n\r\n    const hasId = rowData?.id;\r\n    const isLoading = loadingStates.get(_row) || false;\r\n\r\n    const buttonClass = hasId && !isLoading\r\n      ? 'px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50'\r\n      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n    const buttonTitle = hasId\r\n      ? (isLoading ? 'Generating document...' : 'Preview document')\r\n      : 'Save the application first to enable preview';\r\n\r\n    const disabledAttr = hasId && !isLoading ? '' : 'disabled';\r\n    const buttonText = isLoading ? 'Loading...' : 'Preview';\r\n\r\n    const previewButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"preview\" title=\"${buttonTitle}\" ${disabledAttr}>${buttonText}</button>`;\r\n    td.innerHTML = previewButton;\r\n\r\n    const previewBtn = td.querySelector('[data-action=\"preview\"]');\r\n    if (previewBtn && hasId && !isLoading) {\r\n      previewBtn.addEventListener('click', async () => {\r\n        const rowData = tableData[_row];\r\n        if (rowData?.id) {\r\n          await generateAndOpenDocument(\r\n            rowData,\r\n            onPreview,\r\n            (loading) => setLoadingState(_row, loading),\r\n            header,\r\n            vesselData\r\n          );\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\nexport const renderSubmitButton = (\r\n  tableData: TableRowData[],\r\n  documentType: string,\r\n  loadingStates: Map<number, boolean>,\r\n  setLoadingState: (row: number, loading: boolean) => void,\r\n  queryClient: QueryClient,\r\n  jettyRequestId: string\r\n) => (\r\n  _instance: Handsontable.Core | undefined,\r\n  td: HTMLTableCellElement,\r\n  _row: number,\r\n  _col: number,\r\n  _prop: string | number,\r\n  _value: unknown,\r\n  _cellProperties: Handsontable.CellProperties\r\n) => {\r\n    void _col;\r\n    void _prop;\r\n    void _value;\r\n    void _cellProperties;\r\n    const rowData = tableData[_row];\r\n\r\n    const hasId = rowData?.id;\r\n    const isLoading = loadingStates.get(_row) || false;\r\n\r\n    // Always render the button, just like preview\r\n    const buttonClass = hasId && !isLoading\r\n      ? 'px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50'\r\n      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n    const buttonTitle = hasId\r\n      ? (isLoading ? 'Submitting for approval...' : 'Submit for approval')\r\n      : 'Save the application first to enable submit';\r\n\r\n    const disabledAttr = hasId && !isLoading ? '' : 'disabled';\r\n    const buttonText = isLoading ? 'Submitting...' : 'Submit';\r\n\r\n    const submitButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"submit\" title=\"${buttonTitle}\" ${disabledAttr}>${buttonText}</button>`;\r\n    td.innerHTML = submitButton;\r\n\r\n    const submitBtn = td.querySelector('[data-action=\"submit\"]');\r\n    if (submitBtn && hasId && !isLoading) {\r\n      submitBtn.addEventListener('click', async () => {\r\n        const rowData = tableData[_row];\r\n        if (rowData?.id) {\r\n          await submitForApproval(\r\n            rowData.id,\r\n            documentType,\r\n            (loading) => setLoadingState(_row, loading),\r\n            queryClient,\r\n            jettyRequestId,\r\n            rowData // Pass rowData to submitForApproval\r\n          );\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n// Renderer factory for attachment button\r\nexport const renderAttachmentButton = (queryClient: QueryClient) => {\r\n  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');\r\n  return (\r\n    _instance: Handsontable.Core | undefined,\r\n    td: HTMLTableCellElement,\r\n    _row: number,\r\n    _col: number,\r\n    _prop: string | number,\r\n    _value: unknown,\r\n    _cellProperties: Handsontable.CellProperties\r\n  ) => {\r\n    void _col;\r\n    void _prop;\r\n    void _value;\r\n    void _cellProperties;\r\n\r\n    // Defensive: clear previous content\r\n    td.innerHTML = '';\r\n\r\n    if (!_instance) return;\r\n\r\n    const attachments = _instance.getDataAtRowProp(_row, 'attachments') || [];\r\n    const itemName = _instance.getDataAtRowProp(_row, 'itemName') || '';\r\n    // const rowDataRaw = _instance.getSourceDataAtRow?.(_row);\r\n    // const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};\r\n\r\n    const buttonClass = attachments.length > 0\r\n      ? 'px-2 py-0.5 bg-emerald-500 text-white rounded-md text-xs hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-opacity-50'\r\n      : 'px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed';\r\n\r\n    const buttonTitle = attachments.length > 0\r\n      ? `View Attachments (${attachments.length})`\r\n      : 'No attachments';\r\n\r\n    const disabledAttr = attachments.length > 0 ? '' : 'disabled';\r\n\r\n    const attachmentButton = `<button class=\"${buttonClass}\" data-row=\"${_row}\" data-action=\"attachment\" title=\"${buttonTitle}\" ${disabledAttr}>Attachment (${attachments.length})</button>`;\r\n    td.innerHTML = attachmentButton;\r\n\r\n    const btn = td.querySelector('[data-action=\"attachment\"]');\r\n    if (btn && attachments.length > 0) {\r\n      btn.addEventListener('click', () => {\r\n        // TODO: Replace this with your actual dialog logic\r\n        window.alert(`Show attachments for ${itemName} (implement dialog logic here)`);\r\n      });\r\n    }\r\n  };\r\n};\r\n\r\nexport const getLocalVesselColumns = (\r\n  tenants: MasterTenantDto[],\r\n  businessPartners: BusinessPartnerDto[],\r\n  queryClient: QueryClient,\r\n  tableData: TableRowData[] = [],\r\n  onPreview?: (documentSrc: string) => void,\r\n  loadingStates?: Map<number, boolean>,\r\n  setLoadingState?: (row: number, loading: boolean) => void,\r\n  jettyRequestId?: string,\r\n  header?: LocalVesselHeaderForm,\r\n  vesselData?: LocalVesselWithItemsDto // Full vessel data from API with master objects\r\n) => {\r\n  // Extract tenant names for autocomplete source\r\n  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');\r\n\r\n  // Extract business partner names for autocomplete source\r\n  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');\r\n\r\n  return [\r\n    { data: 'id', title: 'Id', type: 'text', width: 200 },\r\n    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },\r\n    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },\r\n    {\r\n      data: 'tenant',\r\n      title: 'Tenant',\r\n      type: 'autocomplete',\r\n      width: 140,\r\n      source: tenantNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    {\r\n      data: 'businessPartner',\r\n      title: 'Business Partner',\r\n      type: 'autocomplete',\r\n      width: 250,\r\n      source: businessPartnerNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    { data: 'itemName', title: 'Item Name', type: 'text', width: 200 },\r\n    { data: 'itemQty', title: 'Quantity', type: 'numeric', width: 80 },\r\n    { data: 'unitQty', title: 'UOM', type: 'text', width: 100 },\r\n    { data: 'remarks', title: 'Remark', type: 'text', width: 120 },\r\n    { data: 'letterNo', title: 'Letter No', type: 'text', width: 120 },\r\n    { data: 'letterDate', title: 'Letter Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'noBl', title: 'No BL', type: 'text', width: 120 },\r\n    { data: 'dateBl', title: 'Date BL', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'ajuNo', title: 'AJU No', type: 'text', width: 120 },\r\n    { data: 'regNo', title: 'Reg No', type: 'text', width: 120 },\r\n    { data: 'regDate', title: 'Reg Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppbNo', title: 'SPPB No', type: 'text', width: 120 },\r\n    { data: 'sppbDate', title: 'SPPB Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppdNo', title: 'SPPD No', type: 'text', width: 120 },\r\n    { data: 'sppdDate', title: 'SPPD Date', type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'grossWeight', title: 'Gross Weight', type: 'numeric', width: 100 },\r\n    { data: 'unitWeight', title: 'Unit Weight', type: 'text', width: 100 },\r\n    { data: 'status', title: 'Status', type: 'text', width: 100, readOnly: true },\r\n    { data: 'attachments', title: 'Attachment', width: 130, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },\r\n    {\r\n      data: 'preview',\r\n      title: 'Preview',\r\n      width: 100,\r\n      renderer: onPreview && loadingStates && setLoadingState\r\n        ? renderPreviewButton(tableData, onPreview, loadingStates, setLoadingState, header, vesselData)\r\n        : undefined,\r\n      readOnly: true,\r\n      filterable: false\r\n    },\r\n    {\r\n      data: 'submit',\r\n      title: 'Submit',\r\n      width: 100,\r\n      renderer: renderSubmitButton(tableData, 'Local', loadingStates!, setLoadingState!, queryClient, jettyRequestId || ''),\r\n      readOnly: true,\r\n      filterable: false\r\n    },\r\n  ];\r\n};\r\n\r\n// Legacy export for backward compatibility\r\nexport const localVesselColumns = getLocalVesselColumns([], [], {} as QueryClient); ", "import * as z from 'zod';\r\n\r\nexport const localVesselHeaderSchema = z.object({\r\n  docNum: z.string().min(1, 'DocNum is required'),\r\n  vesselId: z.string().min(1, 'Vessel Name is required'),\r\n  voyage: z.string().min(1, 'Voyage is required'),\r\n  postingDate: z.string().min(1, 'Posting Date is required'),\r\n  vesselArrival: z.string().min(1, 'Arrival Date is required'),\r\n  vesselDeparture: z.string().min(1, 'Departure Date is required'),\r\n  portOriginId: z.string().optional(),\r\n  destinationPortId: z.string().optional(),\r\n  deleted: z.string().optional(),\r\n  docType: z.string().optional(),\r\n  isChange: z.string().optional(),\r\n  isLocked: z.string().optional(),\r\n  createdBy: z.string().optional(),\r\n  docStatus: z.string().optional(),\r\n  statusBms: z.string().optional(),\r\n  transType: z.string().min(1, 'TransType is required'),\r\n  concurrencyStamp: z.string().optional(),\r\n  jettyId: z.string().min(1, 'Jetty is required'),\r\n  status: z.string().optional(),\r\n  vesselType: z.string().optional(),\r\n  shipment: z.string().optional(),\r\n  portOrigin: z.string().optional(),\r\n  destinationPort: z.string().optional(),\r\n  // Add more fields as needed from CreateUpdateLocalVesselDto\r\n  bargeId: z.string().optional(),\r\n  asideDate: z.string().optional(),\r\n  castOfDate: z.string().optional(),\r\n});\r\n\r\nexport type LocalVesselHeaderForm = z.infer<typeof localVesselHeaderSchema>; ", "import * as z from 'zod';\r\n\r\nexport const localVesselItemSchema = z.object({\r\n  id: z.string().optional(),\r\n  itemName: z.string().nullable().optional(),\r\n  itemQty: z.number().nullable().optional(),\r\n  unitQty: z.string().nullable().optional(),\r\n  remarks: z.string().nullable().optional(),\r\n  tenant: z.string().nullable().optional(),\r\n  tenantId: z.string().nullable().optional(),\r\n  businessPartner: z.string().nullable().optional(),\r\n  businessPartnerId: z.string().nullable().optional(),\r\n  letterNo: z.string().nullable().optional(),\r\n  letterDate: z.string().nullable().optional(),\r\n  status: z.string().nullable().optional(),\r\n  concurrencyStamp: z.string().nullable().optional(),\r\n  // Add more fields as needed from CreateUpdateVesselItemDto\r\n});\r\n\r\nexport type LocalVesselItemForm = z.infer<typeof localVesselItemSchema>; ", "import { postApiIdjasApprovalSubmit } from '@/client/sdk.gen';\r\nimport { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { BusinessPartnerDto, CreateUpdateLocalVesselDto, LocalVesselWithItemsDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport { Button } from '@/components/ui/button';\r\nimport { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';\r\nimport { FormField, FormSection } from '@/components/ui/FormField';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { HotTable, type HotTableRef } from '@handsontable/react-wrapper';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { registerAllModules } from 'handsontable/registry';\r\nimport 'handsontable/styles/handsontable.min.css';\r\nimport 'handsontable/styles/ht-theme-horizon.css';\r\nimport 'handsontable/styles/ht-theme-main.min.css';\r\nimport type { ColumnSettings } from 'node_modules/handsontable/settings';\r\nimport { useEffect, useMemo, useRef, useState } from 'react';\r\nimport { Controller, FormProvider, useForm } from 'react-hook-form';\r\nimport { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from '../export/async-selects';\r\nimport { getLocalVesselColumns } from './local-vessel-columns';\r\nimport { type LocalVesselHeaderForm, localVesselHeaderSchema } from './local-vessel-header-schema';\r\nimport { type LocalVesselItemForm, localVesselItemSchema } from './local-vessel-item-schema';\r\n\r\nregisterAllModules();\r\n\r\n// Type for row data that includes id and status\r\ntype TableRowData = LocalVesselItemForm & {\r\n  id?: string;\r\n  status?: string;\r\n  headerId?: string;\r\n  docNum?: number;\r\n  agentId?: string;\r\n  concurrencyStamp?: string;\r\n};\r\n\r\n// Extend LocalVesselItemForm to always include concurrencyStamp for form logic\r\ntype LocalVesselItemFormWithConcurrency = LocalVesselItemForm & { concurrencyStamp?: string };\r\n\r\ntype Jetty = { id: string; isCustomArea?: boolean };\r\n\r\nexport type LocalVesselFormProps = {\r\n  mode: 'create' | 'edit';\r\n  initialHeader: Partial<Record<keyof CreateUpdateLocalVesselDto, string>>;\r\n  initialItems: LocalVesselItemForm[];\r\n  onSubmit: (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => Promise<void>;\r\n  columns?: ColumnSettings[];\r\n  headerSchema?: typeof localVesselHeaderSchema;\r\n  itemSchema?: typeof localVesselItemSchema;\r\n  isSubmitting?: boolean;\r\n  title?: string;\r\n  tenants: MasterTenantDto[];\r\n  businessPartners: BusinessPartnerDto[];\r\n  queryClient: QueryClient;\r\n  vesselData?: LocalVesselWithItemsDto; // Full vessel data from API with master objects\r\n  showAddLineButton?: boolean;\r\n  jettyList: Jetty[];\r\n};\r\n\r\nfunction toLocalVesselHeaderForm(dto: Partial<Record<keyof LocalVesselHeaderForm, string>>): LocalVesselHeaderForm {\r\n  // Helper to extract date part (YYYY-MM-DD)\r\n  const getDate = (val?: string) => val ? val.slice(0, 10) : '';\r\n  const today = () => new Date().toISOString().slice(0, 10);\r\n  return {\r\n    docNum: dto.docNum ?? '',\r\n    vesselId: dto.vesselId ?? '',\r\n    voyage: dto.voyage ?? '',\r\n    postingDate: dto.postingDate ?? (getDate(dto.vesselArrival) || today()),\r\n    vesselArrival: dto.vesselArrival ?? '',\r\n    vesselDeparture: dto.vesselDeparture ?? '',\r\n    transType: dto.transType ?? '',\r\n    portOriginId: dto.portOriginId ?? '',\r\n    destinationPortId: dto.destinationPortId ?? '',\r\n    jettyId: dto.jettyId ?? '',\r\n    bargeId: dto.bargeId ?? '',\r\n    asideDate: dto.asideDate ?? '',\r\n    castOfDate: dto.castOfDate ?? '',\r\n    ...(dto.concurrencyStamp ? { concurrencyStamp: dto.concurrencyStamp } : {}),\r\n  };\r\n}\r\n\r\n// Wrapper component that handles data fetching\r\nexport const LocalVesselFormWithData: React.FC<Omit<LocalVesselFormProps, 'columns' | 'tenants' | 'businessPartners'> & { queryClient: QueryClient; vesselData?: LocalVesselWithItemsDto }> = (props) => {\r\n  const { data: tenants = [], isLoading: loadingTenants } = useQuery({\r\n    queryKey: ['tenants'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({\r\n    queryKey: ['businessPartners'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  // You must provide jettyList prop from parent or data loader\r\n  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;\r\n\r\n  return (\r\n    <LocalVesselForm\r\n      {...props}\r\n      columns={getLocalVesselColumns(tenants, businessPartners, props.queryClient)}\r\n      tenants={tenants}\r\n      businessPartners={businessPartners}\r\n      queryClient={props.queryClient}\r\n      vesselData={props.vesselData}\r\n      jettyList={props.jettyList} // <-- pass jettyList prop\r\n    />\r\n  );\r\n};\r\n\r\n// Main form component that receives data as props\r\nconst LocalVesselForm: React.FC<LocalVesselFormProps> = ({\r\n  mode,\r\n  initialHeader,\r\n  initialItems,\r\n  onSubmit,\r\n  headerSchema = localVesselHeaderSchema,\r\n  itemSchema = localVesselItemSchema,\r\n  isSubmitting = false,\r\n  title = 'Create Local Vessel',\r\n  tenants,\r\n  businessPartners,\r\n  queryClient,\r\n  vesselData,\r\n  showAddLineButton = true,\r\n  jettyList,\r\n}) => {\r\n  const [items, setItems] = useState<LocalVesselItemForm[]>(initialItems);\r\n  const [itemErrors, setItemErrors] = useState<string[]>([]);\r\n  const [loadingStates, setLoadingStates] = useState<Map<number, boolean>>(new Map());\r\n  const hotTableRef = useRef<HotTableRef | null>(null);\r\n  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);\r\n  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');\r\n  const [docNum, setDocNum] = useState<string>('');\r\n  const [submitError, setSubmitError] = useState<string | null>(null);\r\n  const [isSubmittingApproval, setIsSubmittingApproval] = useState(false);\r\n  const prevKey = useRef<string | undefined>(null);\r\n\r\n  const methods = useForm<LocalVesselHeaderForm>({\r\n    resolver: zodResolver(headerSchema),\r\n    defaultValues: {\r\n      ...toLocalVesselHeaderForm(initialHeader),\r\n      docNum: mode === 'create' ? docNum : initialHeader.docNum ?? '',\r\n    },\r\n    mode: 'onBlur',\r\n  });\r\n  const { register, handleSubmit, formState: { errors }, reset, setValue } = methods;\r\n\r\n  // Get postDate from form or use current date\r\n  const watchedPostDate = methods.watch('vesselArrival');\r\n  const getCurrentDate = () => {\r\n    const d = new Date();\r\n    return d.toISOString().slice(0, 10);\r\n  };\r\n  const postDate = watchedPostDate || getCurrentDate();\r\n\r\n  // Fetch docNum only in create mode, and when postDate changes\r\n  const { data: generatedDocNum } = useQuery<string, Error>({\r\n    queryKey: ['generateDocNum', postDate, mode],\r\n    queryFn: () => ekbProxyService.generateNextLocalVesselDocNum(postDate).then(res => String(res.data ?? '')),\r\n    enabled: mode === 'create' && !!postDate,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (mode === 'create' && generatedDocNum) {\r\n      setDocNum(generatedDocNum);\r\n    }\r\n  }, [generatedDocNum, mode]);\r\n\r\n  useEffect(() => {\r\n    if (mode === 'create' && docNum) {\r\n      setValue('docNum', docNum);\r\n    }\r\n  }, [docNum, mode, setValue]);\r\n\r\n  // Get document IDs from items for approval data fetching\r\n  // const documentIds = items\r\n  //   .filter(item => item.id)\r\n  //   .map(item => item.id!)\r\n  //   .filter(id => id !== '');\r\n\r\n  // Fetch approval data for the items\r\n  // const { data: approvalStages = [] } = useApprovalStagesByDocumentIds(documentIds);\r\n\r\n  // Create a map of document ID to approval status\r\n  // approvalStages.forEach(stage => {\r\n  //   if (stage.documentId) {\r\n  //     const status = stage.status === 0 ? 'Pending' :\r\n  //       stage.status === 1 ? 'Approved' :\r\n  //         stage.status === 2 ? 'Rejected' :\r\n  //           stage.status === 3 ? 'Cancelled' : 'Draft';\r\n  //     approvalStatusMap.set(stage.documentId, status);\r\n  //   }\r\n  // });\r\n\r\n  // Handle preview functionality\r\n  const handlePreview = (documentSrc: string) => {\r\n    setPreviewDocumentSrc(documentSrc);\r\n    setIsPreviewDialogOpen(true);\r\n  };\r\n\r\n  // Handle loading state changes\r\n  const handleLoadingStateChange = (row: number, loading: boolean) => {\r\n    setLoadingStates(prev => {\r\n      const newMap = new Map(prev);\r\n      if (loading) {\r\n        newMap.set(row, true);\r\n      } else {\r\n        newMap.delete(row);\r\n      }\r\n      return newMap;\r\n    });\r\n  };\r\n\r\n  // Use the prop for selectedJetty lookup\r\n  const selectedJetty = useMemo(() => jettyList.find(j => j.id === methods.watch('jettyId')), [jettyList, methods]);\r\n\r\n  const handleApproval = async () => {\r\n    const documentId = initialHeader.id || vesselData?.id || '';\r\n    if (!documentId) {\r\n      toast({ title: 'Error', description: 'Document ID is missing.', variant: 'destructive' });\r\n      return;\r\n    }\r\n    setIsSubmittingApproval(true);\r\n    try {\r\n      await postApiIdjasApprovalSubmit({\r\n        body: {\r\n          documentId,\r\n          documentType: 'Local',\r\n          notes: '',\r\n        }\r\n      });\r\n      toast({ title: 'Success', description: 'Document submitted for approval.', variant: 'success' });\r\n\r\n      // After successful approval submission, update the document with DocStatus = \"Waiting\"\r\n      const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as LocalVesselItemForm[];\r\n      const currentHeader = methods.getValues();\r\n\r\n      // Create header with DocStatus = \"Waiting\"\r\n      const headerWithWaitingStatus = {\r\n        ...currentHeader,\r\n        docStatus: 'Waiting'\r\n      };\r\n\r\n      // Call onSubmit to update the document\r\n      await onSubmit(headerWithWaitingStatus, currentItems);\r\n\r\n    } catch (error: unknown) {\r\n      let message = 'Failed to submit for approval.';\r\n      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {\r\n        message = (error as { message: string }).message;\r\n      }\r\n      toast({\r\n        title: 'Error',\r\n        description: message,\r\n        variant: 'destructive',\r\n      });\r\n    } finally {\r\n      setIsSubmittingApproval(false);\r\n    }\r\n  };\r\n\r\n  // Find the call to getLocalVesselColumns and ensure jettyRequestId is always a string\r\n  const generatedColumns = getLocalVesselColumns(\r\n    tenants,\r\n    businessPartners,\r\n    queryClient,\r\n    items as TableRowData[],\r\n    handlePreview,\r\n    loadingStates,\r\n    handleLoadingStateChange,\r\n    String(initialHeader.id || ''), // Ensure this is always a string\r\n    methods.getValues(),\r\n    vesselData\r\n  );\r\n\r\n  // Remove any mapping that strips fields from initialItems\r\n  // Instead, just setItems(initialItems) directly on mount if needed\r\n  useEffect(() => {\r\n    const key = initialHeader.id ?? initialHeader.docNum;\r\n    console.log(\"check key\", key, prevKey.current);\r\n    if (mode === 'edit' && key && prevKey.current !== key) {\r\n      reset(toLocalVesselHeaderForm(initialHeader));\r\n      // Always ensure concurrencyStamp is present on each item in form state\r\n      setItems(initialItems.map(i => ({ ...i, concurrencyStamp: (i as LocalVesselItemFormWithConcurrency).concurrencyStamp })));\r\n      prevKey.current = key;\r\n    }\r\n  }, [initialHeader, initialItems, mode, reset]);\r\n\r\n  // Always update concurrencyStamp in form state when initialHeader changes\r\n  useEffect(() => {\r\n    if (initialHeader.concurrencyStamp) {\r\n      setValue('concurrencyStamp', initialHeader.concurrencyStamp);\r\n    }\r\n  }, [initialHeader.concurrencyStamp, setValue]);\r\n\r\n  const validateItems = (data: LocalVesselItemForm[]): boolean => {\r\n    const errors: string[] = [];\r\n    data.forEach((item, idx) => {\r\n      const result = itemSchema.safeParse(item);\r\n      if (!result.success) {\r\n        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');\r\n      } else {\r\n        errors[idx] = '';\r\n      }\r\n    });\r\n    setItemErrors(errors);\r\n    return errors.every(e => !e);\r\n  };\r\n\r\n  const onFormSubmit = async (header: LocalVesselHeaderForm) => {\r\n    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as LocalVesselItemForm[];\r\n\r\n    // Required backend fields\r\n    const requiredFields = {\r\n      deleted: '',\r\n      shipment: '',\r\n      statusBms: '',\r\n      transType: '',\r\n      portOrigin: '',\r\n      vesselType: '',\r\n      destinationPort: '',\r\n    };\r\n    // Ensure required fields for header, but always use concurrencyStamp from header\r\n    const headerWithRequired = { ...requiredFields, ...header, concurrencyStamp: header.concurrencyStamp };\r\n    // Ensure required fields for each item and map tenant/businessPartner names to IDs\r\n    const itemsWithRequired = currentItems.map(item => {\r\n      // Remove concurrencyStamp from the spread to avoid null\r\n      const { concurrencyStamp, ...rest } = item;\r\n      const transformedItem: LocalVesselItemFormWithConcurrency = { ...requiredFields, ...rest, concurrencyStamp: concurrencyStamp ?? undefined };\r\n      if (item.tenant) {\r\n        const tenant = tenants.find(t => t.name === item.tenant);\r\n        transformedItem.tenantId = tenant?.id || '';\r\n      } else {\r\n        transformedItem.tenantId = '';\r\n      }\r\n      if (item.businessPartner) {\r\n        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);\r\n        transformedItem.businessPartnerId = businessPartner?.id || '';\r\n      } else {\r\n        transformedItem.businessPartnerId = '';\r\n      }\r\n      // Always use the latest concurrencyStamp from initialItems (API data)\r\n      const latest = (initialItems as LocalVesselItemFormWithConcurrency[]).find(i => i.id === item.id);\r\n      if (latest && latest.concurrencyStamp) {\r\n        transformedItem.concurrencyStamp = latest.concurrencyStamp;\r\n      }\r\n      return transformedItem;\r\n    });\r\n\r\n    if (!validateItems(itemsWithRequired)) return;\r\n    setSubmitError(null);\r\n    try {\r\n      await onSubmit(headerWithRequired, itemsWithRequired);\r\n      // Do not reset here! Let the parent refetch and effect handle it.\r\n    } catch (error: unknown) {\r\n      let message = 'An error occurred';\r\n      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {\r\n        message = (error as { message: string }).message;\r\n      }\r\n      setSubmitError(message);\r\n      // Do not reset form or items on error\r\n    }\r\n  };\r\n\r\n  const handleAddLine = () => {\r\n    setItems(prev => [...prev, {} as LocalVesselItemForm]);\r\n  };\r\n\r\n  const { toast } = useToast();\r\n\r\n  return (\r\n    <div className=\"w-full mx-auto\">\r\n      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>\r\n        <div className=\"mb-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <h2 className=\"text-lg font-bold text-gray-800 dark:text-white\">{title}</h2>\r\n              <div className=\"h-1 w-16 bg-primary rounded mt-2 mb-4\" />\r\n            </div>\r\n\r\n            {initialHeader.docStatus && (\r\n              <div className=\"flex items-center space-x-2\">\r\n                <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Status:</span>\r\n                <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\r\n                  {initialHeader.docStatus}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n        <FormProvider {...methods}>\r\n          <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\r\n            {submitError && (\r\n              <div className=\"text-red-500 text-sm mb-2\">{submitError}</div>\r\n            )}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"DocNum\" labelWidth='100px'>\r\n                  <Input\r\n                    {...register('docNum')}\r\n                    value={methods.watch('docNum')}\r\n                    readOnly={mode === 'create'}\r\n                    disabled={mode === 'edit'}\r\n                  />\r\n                  {errors.docNum && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.docNum.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"TransType\" labelWidth='100px'>\r\n                  <Select value={methods.watch('transType') ?? ''} onValueChange={v => setValue('transType', v)}>\r\n                    <SelectTrigger size={'sm'} className=\"w-full\">\r\n                      <SelectValue placeholder=\"Select TransType\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"IN\">IN</SelectItem>\r\n                      <SelectItem value=\"OUT\">OUT</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                  {errors.transType && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.transType.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Vessel Name\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"vesselId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <VesselSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select vessel...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.vesselId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Barge Name\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"bargeId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <VesselSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select barge...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.bargeId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.bargeId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Voyage\" labelWidth='100px'>\r\n                  <Input {...register('voyage')} />\r\n                  {errors.voyage && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.voyage.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"Jetty\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"jettyId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <JettySelect\r\n                        value={field.value}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select jetty...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.jettyId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.jettyId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n                <FormField label=\"A/Side Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('asideDate')} />\r\n                  {errors.asideDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.asideDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Cast Of Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('castOfDate')} />\r\n                  {errors.castOfDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.castOfDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Arrival Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselArrival')} />\r\n                  {errors.vesselArrival && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselArrival.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Departure Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselDeparture')} />\r\n                  {errors.vesselDeparture && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselDeparture.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"Posting Date\" labelWidth='100px'>\r\n                  <Input type=\"date\" {...register('postingDate')} />\r\n                  {errors.postingDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.postingDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Port Origin\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"portOriginId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <PortOfLoadingSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select port origin...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.portOriginId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.portOriginId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Destination Port\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"destinationPortId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <DestinationPortSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select destination port...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.destinationPortId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.destinationPortId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n            </div>\r\n            <div className=\"mt-6\">\r\n              <label className=\"block font-medium mb-2\">Items</label>\r\n              <HotTable\r\n                ref={hotTableRef}\r\n                themeName=\"ht-theme-main\"\r\n                data={items}\r\n                columns={generatedColumns as ColumnSettings[]}\r\n                colHeaders={generatedColumns.map((col: unknown) => (typeof col === 'object' && col && 'title' in col ? (col as { title?: string }).title : undefined)).filter((t: unknown): t is string => typeof t === 'string')}\r\n                rowHeaders={true}\r\n                height=\"50vh\"\r\n                licenseKey=\"non-commercial-and-evaluation\"\r\n                stretchH=\"all\"\r\n                contextMenu={true}\r\n                manualColumnResize={true}\r\n                manualRowResize={true}\r\n                autoColumnSize={false}\r\n                autoRowSize={false}\r\n                startRows={1}\r\n                dropdownMenu={true}\r\n                filters={true}\r\n                colWidths={80}\r\n                hiddenColumns={{\r\n                  copyPasteEnabled: true,\r\n                  indicators: true,\r\n                  columns: [0, 1, 2]\r\n                }}\r\n                width=\"100%\"\r\n                persistentState={true}\r\n              />\r\n              {itemErrors.some(e => e) && (\r\n                <div className=\"mt-2 text-red-500 text-xs\">\r\n                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"flex justify-end gap-2\">\r\n              {showAddLineButton && (\r\n                <Button type=\"button\" variant=\"outline\" onClick={handleAddLine} disabled={isSubmitting}>\r\n                  + Add Line\r\n                </Button>\r\n              )}\r\n              {/* Approval button logic */}\r\n              {typeof selectedJetty?.isCustomArea === 'boolean' && selectedJetty.isCustomArea && (\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"warning\"\r\n                  onClick={handleApproval}\r\n                  disabled={\r\n                    isSubmitting ||\r\n                    isSubmittingApproval ||\r\n                    initialHeader.docStatus === 'Waiting' ||\r\n                    initialHeader.docStatus === 'Approved'\r\n                  }\r\n                >\r\n                  {isSubmittingApproval ? 'Submitting...' : 'Submit Approval'}\r\n                </Button>\r\n              )}\r\n              <Button type=\"submit\" disabled={isSubmitting}>\r\n                {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </FormProvider>\r\n      </div>\r\n      <DocumentPreviewDialog\r\n        isOpen={isPreviewDialogOpen}\r\n        onOpenChange={setIsPreviewDialogOpen}\r\n        documentSrc={previewDocumentSrc}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LocalVesselForm; "], "file": "assets/local-vessel-form-B3V1uWrj.js"}