{"version": 3, "file": "page-D_wh8JcV.js", "sources": ["../../../../../frontend/src/lib/hooks/useJettyRequests.ts", "../../../../../frontend/src/components/jetty-request-table.tsx", "../../../../../frontend/src/pages/application/list/page.tsx"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\r\nimport { postApiIdjasJettyRequestFilterList } from '@/client/sdk.gen';\r\nimport { toast } from '@/lib/useToast';\r\nimport type { FilterGroup, QueryParametersDto, PagedResultDtoOfJettyRequestDto } from '@/client/types.gen';\r\n\r\nexport const useJettyRequests = (\r\n  pageIndex: number,\r\n  pageSize: number,\r\n  filterGroup?: FilterGroup,\r\n  sorting?: string\r\n) => {\r\n  return useQuery<PagedResultDtoOfJettyRequestDto, Error>({\r\n    queryKey: ['jetty-requests', pageIndex, pageSize, JSON.stringify(filterGroup), sorting],\r\n    queryFn: async (): Promise<PagedResultDtoOfJettyRequestDto> => {\r\n      const payload: QueryParametersDto = {\r\n        skipCount: pageIndex * pageSize,\r\n        maxResultCount: pageSize,\r\n        sorting,\r\n        filterGroup,\r\n      };\r\n      \r\n      try {\r\n        const response = await postApiIdjasJettyRequestFilterList({ body: payload });\r\n        return response.data || { items: [], totalCount: 0 };\r\n      } catch (error: unknown) {\r\n        let message = 'Unknown error occurred while loading Jetty Requests';\r\n        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n          message = (error as { message?: string }).message ?? message;\r\n        }\r\n        \r\n        console.error('JettyRequests API Error:', error);\r\n        toast({\r\n          title: 'Error loading Jetty Requests',\r\n          description: message,\r\n          variant: 'destructive',\r\n        });\r\n        \r\n        // Return empty result instead of throwing\r\n        return { items: [], totalCount: 0 };\r\n      }\r\n    },\r\n    retry: 1, // Only retry once\r\n    retryDelay: 1000, // Wait 1 second before retry\r\n  });\r\n}; ", "import type { FilterCondition, FilterGroup, FilterOperator, JettyRequestDto, LogicalOperator } from \"@/client/types.gen\";\r\nimport { DataTable } from \"@/components/data-table/DataTable\";\r\nimport FilterSortBar, { type SortDirection } from \"@/components/filter-sort-bar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport ErrorBoundary from \"@/components/ui/error-boundary\";\r\nimport TableSkeleton from \"@/components/ui/table-skeleton\";\r\nimport { useJettyRequests } from \"@/lib/hooks/useJettyRequests\";\r\nimport { router } from \"@inertiajs/react\";\r\nimport { type ColumnDef, type PaginationState } from \"@tanstack/react-table\";\r\nimport { AlertTriangle, Pencil, Plus, RefreshCw } from \"lucide-react\";\r\nimport React, { useEffect, useMemo, useState } from \"react\";\r\n\r\nconst FILTER_FIELDS = [\r\n  { value: \"docNum\", label: \"Document Number\" },\r\n  { value: \"vesselName\", label: \"Vessel Name\" },\r\n  { value: \"jetty\", label: \"Jetty\" },\r\n  { value: \"arrivalDate\", label: \"Arrival Date\" },\r\n  { value: \"departureDate\", label: \"Departure Date\" },\r\n  // Add more fields as needed\r\n];\r\nconst FILTER_OPERATORS: { value: FilterOperator; label: string }[] = [\r\n  { value: \"Equals\", label: \"Equals\" },\r\n  { value: \"Contains\", label: \"Contains\" },\r\n  { value: \"NotEquals\", label: \"Not Equals\" },\r\n  { value: \"GreaterThan\", label: \">\" },\r\n  { value: \"LessThan\", label: \"<\" },\r\n];\r\n\r\nconst columns: ColumnDef<JettyRequestDto>[] = [\r\n  {\r\n    accessorKey: \"docNum\",\r\n    header: \"Document Number\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"vesselName\",\r\n    header: \"Vessel Name\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"jetty\",\r\n    header: \"Jetty\",\r\n    cell: info => info.getValue() ?? \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"arrivalDate\",\r\n    header: \"Arrival Date\",\r\n    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : \"-\",\r\n  },\r\n  {\r\n    accessorKey: \"departureDate\",\r\n    header: \"Departure Date\",\r\n    cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : \"-\",\r\n  },\r\n  // Edit button column\r\n  {\r\n    id: \"edit\",\r\n    header: \"\",\r\n    cell: ({ row }) => {\r\n      const id = row.original.id;\r\n      const handleEdit = () => router.visit(`/application/${id}/edit`);\r\n      const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {\r\n        if (e.key === \"Enter\" || e.key === \" \") {\r\n          handleEdit();\r\n        }\r\n      };\r\n      return (\r\n        <Button\r\n          onClick={handleEdit}\r\n          onKeyDown={handleKeyDown}\r\n          aria-label=\"Edit Application\"\r\n          tabIndex={0}\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className=\"ml-2 h-8 w-8\"\r\n        >\r\n          <Pencil className=\"w-4 h-4\" aria-hidden=\"true\" />\r\n        </Button>\r\n      );\r\n    },\r\n    enableSorting: false,\r\n    enableColumnFilter: false,\r\n  },\r\n  // Add more columns as needed\r\n];\r\n\r\nconst JettyRequestTableContent: React.FC = () => {\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: 0,\r\n    pageSize: 10,\r\n  });\r\n  const [filters, setFilters] = useState<FilterCondition[]>([]);\r\n  const [sorts, setSorts] = useState<{ field: string; direction: SortDirection }[]>([]);\r\n  const [isRefreshing, setIsRefreshing] = useState(false);\r\n\r\n  // Reset to first page when filters or sorts change\r\n  useEffect(() => {\r\n    setPagination((prev) => ({ ...prev, pageIndex: 0 }));\r\n  }, [filters, sorts]);\r\n\r\n  // Build filter group for backend\r\n  const filterGroup: FilterGroup | undefined = useMemo(() => {\r\n    if (!filters.length) return undefined;\r\n    return {\r\n      operator: \"And\" as LogicalOperator,\r\n      conditions: filters,\r\n    };\r\n  }, [filters]);\r\n\r\n  // Build sorting string for backend\r\n  const sortingStr = useMemo(() => {\r\n    if (!sorts.length) return undefined;\r\n    return sorts.map(s => `${s.field} ${s.direction}`).join(\", \");\r\n  }, [sorts]);\r\n\r\n  const { data, isLoading, error, refetch } = useJettyRequests(\r\n    pagination.pageIndex,\r\n    pagination.pageSize,\r\n    filterGroup,\r\n    sortingStr\r\n  );\r\n\r\n  // New Application button\r\n  const handleNewApplication = () => router.visit(\"/application/create\");\r\n\r\n  // Refresh data\r\n  const handleRefresh = async () => {\r\n    setIsRefreshing(true);\r\n    try {\r\n      await refetch();\r\n    } finally {\r\n      setIsRefreshing(false);\r\n    }\r\n  };\r\n\r\n  // Error UI\r\n  if (error) {\r\n    console.error(\"JettyRequests error:\", error);\r\n    return (\r\n      <div className=\"bg-destructive/10 border border-destructive/20 rounded-xl p-6 text-center\">\r\n        <AlertTriangle className=\"mx-auto h-12 w-12 text-destructive mb-4\" />\r\n        <h3 className=\"font-semibold text-destructive mb-2\">Error loading data</h3>\r\n        <p className=\"text-sm text-muted-foreground mb-4\">{String(error.message)}</p>\r\n        <Button onClick={() => window.location.reload()} variant=\"destructive\">Retry</Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-card text-card-foreground rounded-xl border shadow-sm px-2 py-2\">\r\n      <div className=\"text-xl font-bold px-2 pt-2 pb-1\">Jetty Application List</div>\r\n      <FilterSortBar\r\n        filterFields={FILTER_FIELDS}\r\n        operators={FILTER_OPERATORS}\r\n        filters={filters}\r\n        sorts={sorts}\r\n        onFiltersChange={setFilters}\r\n        onSortsChange={setSorts}\r\n      >\r\n        <div className=\"ml-auto flex items-center gap-2\">\r\n          <Button\r\n            onClick={handleRefresh}\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"h-10 w-10\"\r\n            disabled={isLoading || isRefreshing}\r\n          >\r\n            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />\r\n          </Button>\r\n          <Button\r\n            onClick={handleNewApplication}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-full shadow font-semibold text-base\"\r\n            size=\"lg\"\r\n          >\r\n            <Plus className=\"h-5 w-5\" /> New Application\r\n          </Button>\r\n        </div>\r\n      </FilterSortBar>\r\n      {isLoading ? (\r\n        <TableSkeleton columns={columns} />\r\n      ) : (\r\n        <DataTable\r\n          title=\"\"\r\n          columns={columns}\r\n          data={data?.items ?? []}\r\n          totalCount={data?.totalCount ?? 0}\r\n          isLoading={isLoading}\r\n          manualPagination={true}\r\n          pageSize={pagination.pageSize}\r\n          onPaginationChange={setPagination}\r\n          hideDefaultFilterbar={true}\r\n          enableRowSelection={false}\r\n          manualSorting={true}\r\n          // Sorting and filtering are handled above\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst JettyRequestTable: React.FC = () => {\r\n  return (\r\n    <ErrorBoundary>\r\n      <JettyRequestTableContent />\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\nexport default JettyRequestTable; ", "import JettyRequestTable from \"@/components/jetty-request-table\";\r\nimport AppLayout from \"@/layouts/app-layout\";\r\nimport { Head } from \"@inertiajs/react\";\r\nimport React from \"react\";\r\n\r\nconst ApplicationListPage: React.FC = () => {\r\n  return (\r\n    <AppLayout>\r\n      <Head title={`Application List`} />\r\n      <div className=\"flex flex-col space-y-4 p-4\">\r\n        <JettyRequestTable />\r\n      </div>\r\n    </AppLayout>\r\n  );\r\n};\r\n\r\nexport default ApplicationListPage;"], "names": ["useJettyRequests", "pageIndex", "pageSize", "filterGroup", "sorting", "useQuery", "payload", "postApiIdjasJettyRequestFilterList", "error", "message", "toast", "FILTER_FIELDS", "FILTER_OPERATORS", "columns", "info", "row", "id", "handleEdit", "router", "handleKeyDown", "e", "jsx", "<PERSON><PERSON>", "Pencil", "JettyRequestTableContent", "pagination", "setPagination", "useState", "filters", "setFilters", "sorts", "setSorts", "isRefreshing", "setIsRefreshing", "useEffect", "prev", "useMemo", "sortingStr", "s", "data", "isLoading", "refetch", "handleNewApplication", "handleRefresh", "jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FilterSortBar", "RefreshCw", "Plus", "TableSkeleton", "DataTable", "JettyRequestTable", "Error<PERSON>ou<PERSON><PERSON>", "ApplicationListPage", "AppLayout", "Head"], "mappings": "+vBAKO,MAAMA,EAAmB,CAC9BC,EACAC,EACAC,EACAC,IAEOC,EAAiD,CACtD,SAAU,CAAC,iBAAkBJ,EAAWC,EAAU,KAAK,UAAUC,CAAW,EAAGC,CAAO,EACtF,QAAS,SAAsD,CAC7D,MAAME,EAA8B,CAClC,UAAWL,EAAYC,EACvB,eAAgBA,EAChB,QAAAE,EACA,YAAAD,CACF,EAEI,GAAA,CAEF,OADiB,MAAMI,EAAmC,CAAE,KAAMD,EAAS,GAC3D,MAAQ,CAAE,MAAO,CAAC,EAAG,WAAY,CAAE,QAC5CE,EAAgB,CACvB,IAAIC,EAAU,sDACV,OAAA,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHC,EAAWD,EAA+B,SAAWC,GAIjDC,EAAA,CACJ,MAAO,+BACP,YAAaD,EACb,QAAS,aAAA,CACV,EAGM,CAAE,MAAO,GAAI,WAAY,CAAE,CAAA,CAEtC,EACA,MAAO,EACP,WAAY,GAAA,CACb,EC/BGE,EAAgB,CACpB,CAAE,MAAO,SAAU,MAAO,iBAAkB,EAC5C,CAAE,MAAO,aAAc,MAAO,aAAc,EAC5C,CAAE,MAAO,QAAS,MAAO,OAAQ,EACjC,CAAE,MAAO,cAAe,MAAO,cAAe,EAC9C,CAAE,MAAO,gBAAiB,MAAO,gBAAiB,CAEpD,EACMC,EAA+D,CACnE,CAAE,MAAO,SAAU,MAAO,QAAS,EACnC,CAAE,MAAO,WAAY,MAAO,UAAW,EACvC,CAAE,MAAO,YAAa,MAAO,YAAa,EAC1C,CAAE,MAAO,cAAe,MAAO,GAAI,EACnC,CAAE,MAAO,WAAY,MAAO,GAAI,CAClC,EAEMC,EAAwC,CAC5C,CACE,YAAa,SACb,OAAQ,kBACR,KAAMC,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,aACb,OAAQ,cACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,QACb,OAAQ,QACR,KAAMA,GAAQA,EAAK,SAAA,GAAc,GACnC,EACA,CACE,YAAa,cACb,OAAQ,eACR,KAAMA,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,mBAAA,EAAuB,GAC7F,EACA,CACE,YAAa,gBACb,OAAQ,iBACR,KAAMA,GAAQA,EAAK,SAAa,EAAA,IAAI,KAAKA,EAAK,UAAoB,EAAE,mBAAA,EAAuB,GAC7F,EAEA,CACE,GAAI,OACJ,OAAQ,GACR,KAAM,CAAC,CAAE,IAAAC,KAAU,CACX,MAAAC,EAAKD,EAAI,SAAS,GAClBE,EAAa,IAAMC,EAAO,MAAM,gBAAgBF,CAAE,OAAO,EACzDG,EAAiBC,GAA8C,EAC/DA,EAAE,MAAQ,SAAWA,EAAE,MAAQ,MACtBH,EAAA,CAEf,EAEE,OAAAI,EAAA,IAACC,EAAA,CACC,QAASL,EACT,UAAWE,EACX,aAAW,mBACX,SAAU,EACV,QAAQ,UACR,KAAK,OACL,UAAU,eAEV,SAACE,EAAA,IAAAE,EAAA,CAAO,UAAU,UAAU,cAAY,MAAO,CAAA,CAAA,CACjD,CAEJ,EACA,cAAe,GACf,mBAAoB,EAAA,CAGxB,EAEMC,EAAqC,IAAM,CAC/C,KAAM,CAACC,EAAYC,CAAa,EAAIC,WAA0B,CAC5D,UAAW,EACX,SAAU,EAAA,CACX,EACK,CAACC,EAASC,CAAU,EAAIF,EAAAA,SAA4B,CAAA,CAAE,EACtD,CAACG,EAAOC,CAAQ,EAAIJ,EAAAA,SAAwD,CAAA,CAAE,EAC9E,CAACK,EAAcC,CAAe,EAAIN,EAAAA,SAAS,EAAK,EAGtDO,EAAAA,UAAU,IAAM,CACdR,EAAeS,IAAU,CAAE,GAAGA,EAAM,UAAW,GAAI,CAAA,EAClD,CAACP,EAASE,CAAK,CAAC,EAGb,MAAA3B,EAAuCiC,EAAAA,QAAQ,IAAM,CACrD,GAACR,EAAQ,OACN,MAAA,CACL,SAAU,MACV,WAAYA,CACd,CAAA,EACC,CAACA,CAAO,CAAC,EAGNS,EAAaD,EAAAA,QAAQ,IAAM,CAC3B,GAACN,EAAM,OACX,OAAOA,EAAM,IAASQ,GAAA,GAAGA,EAAE,KAAK,IAAIA,EAAE,SAAS,EAAE,EAAE,KAAK,IAAI,CAAA,EAC3D,CAACR,CAAK,CAAC,EAEJ,CAAE,KAAAS,EAAM,UAAAC,EAAW,MAAAhC,EAAO,QAAAiC,CAAY,EAAAzC,EAC1CyB,EAAW,UACXA,EAAW,SACXtB,EACAkC,CACF,EAGMK,EAAuB,IAAMxB,EAAO,MAAM,qBAAqB,EAG/DyB,EAAgB,SAAY,CAChCV,EAAgB,EAAI,EAChB,GAAA,CACF,MAAMQ,EAAQ,CAAA,QACd,CACAR,EAAgB,EAAK,CAAA,CAEzB,EAGA,OAAIzB,EAGAoC,EAAA,KAAC,MAAI,CAAA,UAAU,4EACb,SAAA,CAACvB,EAAAA,IAAAwB,EAAA,CAAc,UAAU,yCAA0C,CAAA,EAClExB,EAAA,IAAA,KAAA,CAAG,UAAU,sCAAsC,SAAkB,qBAAA,QACrE,IAAE,CAAA,UAAU,qCAAsC,SAAO,OAAAb,EAAM,OAAO,EAAE,EACzEa,EAAAA,IAACC,EAAO,CAAA,QAAS,IAAM,OAAO,SAAS,OAAO,EAAG,QAAQ,cAAc,SAAK,OAAA,CAAA,CAAA,EAC9E,EAKFsB,EAAA,KAAC,MAAI,CAAA,UAAU,qEACb,SAAA,CAACvB,EAAA,IAAA,MAAA,CAAI,UAAU,mCAAmC,SAAsB,yBAAA,EACxEA,EAAA,IAACyB,EAAA,CACC,aAAcnC,EACd,UAAWC,EACX,QAAAgB,EACA,MAAAE,EACA,gBAAiBD,EACjB,cAAeE,EAEf,SAAAa,EAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAvB,EAAA,IAACC,EAAA,CACC,QAASqB,EACT,QAAQ,UACR,KAAK,OACL,UAAU,YACV,SAAUH,GAAaR,EAEvB,eAACe,EAAU,CAAA,UAAW,WAAWf,EAAe,eAAiB,EAAE,EAAI,CAAA,CAAA,CACzE,EACAY,EAAA,KAACtB,EAAA,CACC,QAASoB,EACT,UAAU,mHACV,KAAK,KAEL,SAAA,CAACrB,EAAAA,IAAA2B,EAAA,CAAK,UAAU,SAAU,CAAA,EAAE,kBAAA,CAAA,CAAA,CAC9B,CACF,CAAA,CAAA,CACF,EACCR,EACCnB,EAAAA,IAAC4B,EAAc,CAAA,QAAApC,CAAA,CAAkB,EAEjCQ,EAAA,IAAC6B,EAAA,CACC,MAAM,GACN,QAAArC,EACA,KAAM0B,GAAM,OAAS,CAAC,EACtB,WAAYA,GAAM,YAAc,EAChC,UAAAC,EACA,iBAAkB,GAClB,SAAUf,EAAW,SACrB,mBAAoBC,EACpB,qBAAsB,GACtB,mBAAoB,GACpB,cAAe,EAAA,CAAA,CAEjB,EAEJ,CAEJ,EAEMyB,EAA8B,IAE/B9B,EAAAA,IAAA+B,EAAA,CACC,SAAC/B,EAAAA,IAAAG,EAAA,CAAyB,CAAA,EAC5B,ECvME6B,GAAgC,WAEjCC,EACC,CAAA,SAAA,CAACjC,EAAAA,IAAAkC,EAAA,CAAK,MAAO,kBAAoB,CAAA,QAChC,MAAI,CAAA,UAAU,8BACb,SAAAlC,EAAA,IAAC8B,IAAkB,CACrB,CAAA,CAAA,EACF"}