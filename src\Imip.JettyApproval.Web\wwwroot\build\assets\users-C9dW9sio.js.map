{"version": 3, "file": "users-C9dW9sio.js", "sources": ["../../../../../frontend/src/components/layout/page-container.tsx", "../../../../../frontend/src/pages/users.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\n\r\nexport default function PageContainer({\r\n  children,\r\n  scrollable = true\r\n}: {\r\n  children: React.ReactNode;\r\n  scrollable?: boolean;\r\n}) {\r\n  return (\r\n    <>\r\n      {scrollable ? (\r\n        <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea>\r\n      ) : (\r\n        <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n", "import PageContainer from '@/components/layout/page-container';\r\nimport AppLayout from '../layouts/app-layout';\r\nimport OverViewPage from '../components/dashboard/overview';\r\nimport { Head } from '@inertiajs/react';\r\nexport default function OverViewLayout() {\r\n  return (\r\n    <AppLayout policy='AbpIdentity.Users'>\r\n      <Head title=\"Users\" />\r\n\r\n      <PageContainer>\r\n        <OverViewPage />\r\n      </PageContainer>\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "children", "scrollable", "jsx", "ScrollArea", "OverViewLayout", "jsxs", "AppLayout", "Head", "OverViewPage"], "mappings": "6WAGA,SAAwBA,EAAc,CACpC,SAAAC,EACA,WAAAC,EAAa,EACf,EAGG,CACD,yBAEK,SACCA,EAAAC,EAAAA,IAACC,GAAW,UAAU,wBACpB,eAAC,MAAI,CAAA,UAAU,0BAA2B,SAAAH,CAAS,CAAA,CACrD,CAAA,EAEAE,EAAAA,IAAC,OAAI,UAAU,0BAA2B,SAAAF,EAAS,CAEvD,CAAA,CAEJ,CCjBA,SAAwBI,GAAiB,CAErC,OAAAC,EAAA,KAACC,EAAU,CAAA,OAAO,oBAChB,SAAA,CAACJ,EAAAA,IAAAK,EAAA,CAAK,MAAM,OAAQ,CAAA,EAEnBL,EAAA,IAAAH,EAAA,CACC,SAACG,EAAAA,IAAAM,EAAA,CAAA,CAAa,CAChB,CAAA,CAAA,EACF,CAEJ"}