import type { CargoDto } from '@/clientEkb/types.gen';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type ColumnDef } from '@tanstack/react-table';
import { Pencil } from 'lucide-react';

export const cargoColumns = (onEdit: (row: CargoDto) => void): ColumnDef<CargoDto>[] => [
  {
    accessorKey: 'name',
    header: 'Vessel Name',
    size: 250,
    cell: info => info.getValue() ?? '-',
    enableSorting: true,
  },
  {
    accessorKey: 'type',
    header: 'Type',
    cell: info => info.getValue() ?? '-',
    enableSorting: true,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: info => {
      const value = (info.getValue() as string)?.trim();
      if (value === 'Y') {
        return <Badge className="bg-green-500 text-white">Active</Badge>;
      }
      return <Badge className="bg-gray-400 text-white">Inactive</Badge>;
    },
    enableSorting: true,
  },
  {
    accessorKey: 'grossWeight',
    header: 'Gross Weight',
    cell: info => info.getValue() ?? '-',
  },
  {
    accessorKey: 'loaQty',
    header: 'Loa Qty',
    cell: info => info.getValue() ?? '-',
  },
  // {
  //   accessorKey: 'createdAt',
  //   header: 'Created At',
  //   cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : '-',
  // },
  // {
  //   accessorKey: 'updatedAt',
  //   header: 'Updated At',
  //   cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : '-',
  // },
  {
    id: 'edit',
    header: '',
    cell: ({ row }) => {
      const handleEdit = () => onEdit(row.original);
      const handleKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleEdit();
        }
      };
      return (
        <Button
          onClick={handleEdit}
          onKeyDown={handleKeyDown}
          aria-label="Edit Jetty"
          tabIndex={0}
          variant="outline"
          size="icon"
          className="ml-2 h-7 w-7"
        >
          <Pencil className="w-2 h-2" aria-hidden="true" />
        </Button>
      );
    },
    enableSorting: false,
    enableColumnFilter: false,
  },
]; 