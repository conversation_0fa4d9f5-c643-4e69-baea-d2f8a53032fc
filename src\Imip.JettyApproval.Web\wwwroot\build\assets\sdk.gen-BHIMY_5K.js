import{e as t}from"./App-DnhJzTNn.js";import"./vendor-6tJeyfYI.js";const p=e=>(e.client??t).put({url:"/api/ekb/bounded-zone/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),a=e=>(e?.client??t).post({url:"/api/ekb/jetty",...e,headers:{"Content-Type":"application/json",...e?.headers}}),i=e=>(e.client??t).put({url:"/api/ekb/jetty/{id}",...e,headers:{"Content-Type":"application/json",...e.headers}}),d=e=>(e?.client??t).post({url:"/api/ekb/jetty/filter-list",...e,headers:{"Content-Type":"application/json",...e?.headers}});export{a as postApiEkbJetty,d as postApiEkbJettyFilterList,p as putApiEkbBoundedZoneById,i as putApiEkbJettyById};
//# sourceMappingURL=sdk.gen-BHIMY_5K.js.map
