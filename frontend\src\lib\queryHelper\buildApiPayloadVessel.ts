import type { FilterCondition, FilterGroup, QueryParametersDto } from "@/clientEkb";
import type { ColumnFiltersState, SortingState } from "@tanstack/react-table";

type BuildApiPayloadProps = {
  pageIndex: number;
  pageSize: number;
  sorting?: SortingState;
  filters?: ColumnFiltersState;
  globalFilter?: string;
  vesselType?: string;
  isCustomArea?: boolean;
}

export function buildApiPayloadVessel({ pageIndex, pageSize, sorting, filters, globalFilter, vesselType, isCustomArea = true }: BuildApiPayloadProps) {
  // Convert sorting array to string if present
  let sortingStr: string | undefined = undefined;
  if (Array.isArray(sorting) && sorting.length > 0) {
    sortingStr = sorting
      .map((s) => `${s.id} ${s.desc ? "desc" : "asc"}`)
      .join(", ");
  } else {
    sortingStr = "docNum desc"
  }

  // Build filterGroup for global and column filters
  const conditions: FilterCondition[] = [];
  if (globalFilter) {
    // You can add more fields for global search if needed
    conditions.push({
      fieldName: "vessel.name",
      operator: "Contains",
      value: globalFilter,
    });
  }
  if (Array.isArray(filters)) {
    for (const filter of filters) {
      if (filter.value) {
        conditions.push({
          fieldName: filter.id,
          operator: "Contains",
          value: filter.value,
        });
      }
    }
  }

  // Only add DocType = Export for export vessel
  if (vesselType && vesselType.toLowerCase() === 'export') {
    conditions.unshift({
      fieldName: 'docType',
      operator: 'Equals',
      value: 'Export',
    });
  }

  if (isCustomArea) {
    conditions.unshift({
      fieldName: 'masterJetty.isCustomArea',
      operator: 'Equals',
      value: 'true',
    });
  } else {
    conditions.unshift({
      fieldName: 'masterJetty.isCustomArea',
      operator: 'Equals',
      value: 'false',
    });
  }

  const filterGroup: FilterGroup | undefined = conditions.length > 0 ? { operator: "And", conditions } : undefined;

  // Build payload
  const payload: QueryParametersDto & { sorting?: string } = {
    page: pageIndex + 1,
    maxResultCount: pageSize,
    ...(sortingStr ? { sorting: sortingStr } : {}),
    ...(filterGroup ? { filterGroup } : {}),
  };
  return payload;
}