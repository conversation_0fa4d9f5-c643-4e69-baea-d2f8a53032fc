import type { FilterCondition, FilterGroup, JettyDto, QueryParametersDto } from "@/clientEkb/types.gen";
import { DataGrid } from "@/components/ui/data-grid";
import ErrorBoundary from "@/components/ui/error-boundary";
import { type ColumnFiltersState, type SortingState } from "@tanstack/react-table";
import React from "react";
import { jettyColumns } from "./jetty-columns";
import JettyDialog from "./JettyDialog";
import ekbProxyService from "@/services/ekbProxyService";
import { ContentCard } from "@/components/layout/content-card";

const JETTY_QUERY_KEY = ["jetty-list"];

const ManageJettyTableContent: React.FC = () => {
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editData, setEditData] = React.useState<JettyDto | undefined>(undefined);

  // DataGrid queryFn: adapts DataGrid's params to API
  const queryFn = async ({ pageIndex, pageSize, sorting, filters, globalFilter }: { pageIndex: number; pageSize: number; sorting?: SortingState; filters?: ColumnFiltersState; globalFilter?: string }) => {
    // Convert sorting array to string if present
    let sortingStr: string | undefined = undefined;
    if (Array.isArray(sorting) && sorting.length > 0) {
      sortingStr = sorting
        .map((s) => `${s.id} ${s.desc ? "desc" : "asc"}`)
        .join(", ");
    }

    // Build filterGroup for global and column filters
    const conditions: FilterCondition[] = [];
    if (globalFilter) {
      conditions.push({
        fieldName: "name",
        operator: "Contains",
        value: globalFilter,
      });
      // Add more fields for global search if needed
    }
    if (Array.isArray(filters)) {
      for (const filter of filters) {
        if (filter.value) {
          conditions.push({
            fieldName: filter.id,
            operator: "Contains",
            value: filter.value,
          });
        }
      }
    }
    const filterGroup: FilterGroup | undefined = conditions.length > 0 ? { operator: "And", conditions } : undefined;

    // Call the API directly
    const payload: QueryParametersDto & { sorting?: string } = {
      page: pageIndex + 1,
      maxResultCount: pageSize,
      ...(sortingStr ? { sorting: sortingStr } : {}),
      ...(filterGroup ? { filterGroup } : {}),
    };
    const response = await ekbProxyService.filterJetties(payload);

    const data = response?.data;
    return {
      items: data?.items ?? [],
      totalCount: data?.totalCount ?? 0,
    };
  };

  // Handler for create
  const handleCreate = () => {
    setEditData(undefined);
    setDialogOpen(true);
  };

  // Handler for edit (to be passed to columns)
  const handleEdit = (row: JettyDto) => {
    setEditData(row);
    setDialogOpen(true);
  };

  return (
    <ContentCard>
      <DataGrid
        columns={jettyColumns(handleEdit)}
        title="Manage Jetty"
        queryFn={queryFn}
        queryKey={JETTY_QUERY_KEY}
        rowIdAccessor={row => String(row.id || row.docEntry || row.name || 'row-' + JSON.stringify(row))}
        enableRowSelection={true}
        defaultPageSize={10}
        manualSorting={true}
        manualFiltering={true}
        onCreate={handleCreate}
        createModalContent={dialogOpen ? (
          <JettyDialog
            open={dialogOpen}
            onClose={() => setDialogOpen(false)}
            initialData={editData}
            queryKey={JETTY_QUERY_KEY}
          />
        ) : null}
      />
    </ContentCard>
  );
};

const ManageJettyTable: React.FC = () => {
  return (
    <ErrorBoundary>
      <ManageJettyTableContent />
    </ErrorBoundary>
  );
};

export default ManageJettyTable; 