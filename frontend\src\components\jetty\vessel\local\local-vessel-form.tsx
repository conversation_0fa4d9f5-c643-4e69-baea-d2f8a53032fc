import { postApiIdjasApprovalSubmit } from '@/client/sdk.gen';
import { ekbProxyService } from '@/services/ekbProxyService';
import type { BusinessPartnerDto, CreateUpdateLocalVesselDto, GenerateDocNumDto, LocalVesselWithItemsDto, MasterTenantDto } from '@/clientEkb/types.gen';
import { Button } from '@/components/ui/button';
import { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';
import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/lib/useToast';
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';
import { zodResolver } from '@hookform/resolvers/zod';
import type { QueryClient } from '@tanstack/react-query';
import { useQuery } from '@tanstack/react-query';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-horizon.css';
import 'handsontable/styles/ht-theme-main.min.css';
import type { ColumnSettings } from 'node_modules/handsontable/settings';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from '../export/async-selects';
import { getLocalVesselColumns } from './local-vessel-columns';
import { type LocalVesselHeaderForm, localVesselHeaderSchema } from './local-vessel-header-schema';
import { type LocalVesselItemForm, localVesselItemSchema } from './local-vessel-item-schema';
import { useCurrentUser } from '@/lib/hooks/useCurrentUser';

registerAllModules();

// Type for row data that includes id and status
type TableRowData = LocalVesselItemForm & {
  id?: string;
  status?: string;
  headerId?: string;
  docNum?: number;
  agentId?: string;
  concurrencyStamp?: string;
};

// Extend LocalVesselItemForm to always include concurrencyStamp for form logic
type LocalVesselItemFormWithConcurrency = LocalVesselItemForm & { concurrencyStamp?: string };

type Jetty = { id: string; isCustomArea?: boolean };

export type LocalVesselFormProps = {
  mode: 'create' | 'edit';
  initialHeader: Partial<Record<keyof CreateUpdateLocalVesselDto, string>>;
  initialItems: LocalVesselItemForm[];
  onSubmit: (header: LocalVesselHeaderForm, items: LocalVesselItemForm[]) => Promise<void>;
  columns?: ColumnSettings[];
  headerSchema?: typeof localVesselHeaderSchema;
  itemSchema?: typeof localVesselItemSchema;
  isSubmitting?: boolean;
  title?: string;
  tenants: MasterTenantDto[];
  businessPartners: BusinessPartnerDto[];
  queryClient: QueryClient;
  vesselData?: LocalVesselWithItemsDto; // Full vessel data from API with master objects
  showAddLineButton?: boolean;
  jettyList: Jetty[];
};

function toLocalVesselHeaderForm(dto: Partial<Record<keyof LocalVesselHeaderForm, string>>): LocalVesselHeaderForm {
  // Helper to extract date part (YYYY-MM-DD)
  const getDate = (val?: string) => val ? val.slice(0, 10) : '';
  const today = () => new Date().toISOString().slice(0, 10);
  return {
    docNum: dto.docNum ?? '',
    vesselId: dto.vesselId ?? '',
    voyage: dto.voyage ?? '',
    postingDate: dto.postingDate ?? (getDate(dto.vesselArrival) || today()),
    vesselArrival: dto.vesselArrival ?? '',
    vesselDeparture: dto.vesselDeparture ?? '',
    transType: dto.transType ?? '',
    portOriginId: dto.portOriginId ?? '',
    destinationPortId: dto.destinationPortId ?? '',
    jettyId: dto.jettyId ?? '',
    bargeId: dto.bargeId ?? '',
    asideDate: dto.asideDate ?? '',
    castOfDate: dto.castOfDate ?? '',
    ...(dto.concurrencyStamp ? { concurrencyStamp: dto.concurrencyStamp } : {}),
  };
}

// Wrapper component that handles data fetching
export const LocalVesselFormWithData: React.FC<Omit<LocalVesselFormProps, 'columns' | 'tenants' | 'businessPartners'> & { queryClient: QueryClient; vesselData?: LocalVesselWithItemsDto }> = (props) => {
  const { data: tenants = [], isLoading: loadingTenants } = useQuery({
    queryKey: ['tenants'],
    queryFn: () =>
      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })
        .then(res => res.data?.items ?? []),
  });

  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({
    queryKey: ['businessPartners'],
    queryFn: () =>
      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })
        .then(res => res.data?.items ?? []),
  });

  // You must provide jettyList prop from parent or data loader
  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;

  return (
    <LocalVesselForm
      {...props}
      columns={getLocalVesselColumns(tenants, businessPartners, props.queryClient)}
      tenants={tenants}
      businessPartners={businessPartners}
      queryClient={props.queryClient}
      vesselData={props.vesselData}
      jettyList={props.jettyList} // <-- pass jettyList prop
    />
  );
};

// Main form component that receives data as props
const LocalVesselForm: React.FC<LocalVesselFormProps> = ({
  mode,
  initialHeader,
  initialItems,
  onSubmit,
  headerSchema = localVesselHeaderSchema,
  itemSchema = localVesselItemSchema,
  isSubmitting = false,
  title = 'Create Local Vessel',
  tenants,
  businessPartners,
  queryClient,
  vesselData,
  showAddLineButton = true,
  jettyList,
}) => {
  const [items, setItems] = useState<LocalVesselItemForm[]>(initialItems);
  const [itemErrors, setItemErrors] = useState<string[]>([]);
  const [loadingStates, setLoadingStates] = useState<Map<number, boolean>>(new Map());
  const hotTableRef = useRef<HotTableRef | null>(null);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');
  const [docNum, setDocNum] = useState<string>('');
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSubmittingApproval, setIsSubmittingApproval] = useState(false);
  const prevKey = useRef<string | undefined>(null);
  const currentUser = useCurrentUser();

  const methods = useForm<LocalVesselHeaderForm>({
    resolver: zodResolver(headerSchema),
    defaultValues: {
      ...toLocalVesselHeaderForm(initialHeader),
      docNum: mode === 'create' ? docNum : initialHeader.docNum ?? '',
    },
    mode: 'onBlur',
  });
  const { register, handleSubmit, formState: { errors }, reset, setValue } = methods;
  const { t } = useTranslation();

  // Get postDate from form or use current date
  const watchedPostDate = methods.watch('vesselArrival');
  const getCurrentDate = () => {
    const d = new Date();
    return d.toISOString().slice(0, 10);
  };
  const postDate = watchedPostDate || getCurrentDate();

  // Fetch docNum only in create mode, and when postDate changes
  const { data: generatedDocNum } = useQuery<GenerateDocNumDto, Error>({
    queryKey: ['generateDocNum', postDate, mode],
    queryFn: () => ekbProxyService.generateNextLocalVesselDocNum(postDate).then(res => res.data ?? {}),
    enabled: mode === 'create' && !!postDate,
  });

  useEffect(() => {
    if (mode === 'create' && generatedDocNum) {
      setDocNum(generatedDocNum.docNum ?? '');
    }
  }, [generatedDocNum, mode]);

  useEffect(() => {
    if (mode === 'create' && docNum) {
      setValue('docNum', docNum);
    }
  }, [docNum, mode, setValue]);


  // Auto-set posting date to today when in create mode
  useEffect(() => {
    if (mode === 'create' && !methods.watch('postingDate')) {
      const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
      setValue('postingDate', today);
    }
  }, [mode, setValue, methods]);

  // Get document IDs from items for approval data fetching
  // const documentIds = items
  //   .filter(item => item.id)
  //   .map(item => item.id!)
  //   .filter(id => id !== '');

  // Fetch approval data for the items
  // const { data: approvalStages = [] } = useApprovalStagesByDocumentIds(documentIds);

  // Create a map of document ID to approval status
  // approvalStages.forEach(stage => {
  //   if (stage.documentId) {
  //     const status = stage.status === 0 ? 'Pending' :
  //       stage.status === 1 ? 'Approved' :
  //         stage.status === 2 ? 'Rejected' :
  //           stage.status === 3 ? 'Cancelled' : 'Draft';
  //     approvalStatusMap.set(stage.documentId, status);
  //   }
  // });

  // Handle preview functionality
  const handlePreview = (documentSrc: string) => {
    setPreviewDocumentSrc(documentSrc);
    setIsPreviewDialogOpen(true);
  };

  // Handle loading state changes
  const handleLoadingStateChange = (row: number, loading: boolean) => {
    setLoadingStates(prev => {
      const newMap = new Map(prev);
      if (loading) {
        newMap.set(row, true);
      } else {
        newMap.delete(row);
      }
      return newMap;
    });
  };

  // Use the prop for selectedJetty lookup
  const selectedJetty = useMemo(() => jettyList.find(j => j.id === methods.watch('jettyId')), [jettyList, methods]);

  const handleApproval = async () => {
    const documentId = initialHeader.id || vesselData?.id || '';
    if (!documentId) {
      toast({ title: 'Error', description: 'Document ID is missing.', variant: 'destructive' });
      return;
    }
    setIsSubmittingApproval(true);
    try {
      await postApiIdjasApprovalSubmit({
        body: {
          documentId,
          documentType: 'Local',
          notes: '',
        }
      });
      toast({ title: 'Success', description: 'Document submitted for approval.', variant: 'success' });

      // After successful approval submission, update the document with DocStatus = "Waiting"
      const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as LocalVesselItemForm[];
      const currentHeader = methods.getValues();

      // Create header with DocStatus = "Waiting"
      const headerWithWaitingStatus = {
        ...currentHeader,
        docStatus: 'Waiting'
      };

      // Call onSubmit to update the document
      await onSubmit(headerWithWaitingStatus, currentItems);

    } catch (error: unknown) {
      let message = 'Failed to submit for approval.';
      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {
        message = (error as { message: string }).message;
      }
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsSubmittingApproval(false);
    }
  };

  // Find the call to getLocalVesselColumns and ensure jettyRequestId is always a string
  const generatedColumns = getLocalVesselColumns(
    tenants,
    businessPartners,
    queryClient,
    items as TableRowData[],
    handlePreview,
    loadingStates,
    handleLoadingStateChange,
    String(initialHeader.id || ''), // Ensure this is always a string
    methods.getValues(),
    vesselData
  );

  // Remove any mapping that strips fields from initialItems
  // Instead, just setItems(initialItems) directly on mount if needed
  useEffect(() => {
    const key = initialHeader.id ?? initialHeader.docNum;
    console.log("check key", key, prevKey.current);
    if (mode === 'edit' && key && prevKey.current !== key) {
      reset(toLocalVesselHeaderForm(initialHeader));
      // Always ensure concurrencyStamp is present on each item in form state
      setItems(initialItems.map(i => ({ ...i, concurrencyStamp: (i as LocalVesselItemFormWithConcurrency).concurrencyStamp })));
      prevKey.current = key;
    }
  }, [initialHeader, initialItems, mode, reset]);

  // Always update concurrencyStamp in form state when initialHeader changes
  useEffect(() => {
    if (initialHeader.concurrencyStamp) {
      setValue('concurrencyStamp', initialHeader.concurrencyStamp);
    }
  }, [initialHeader.concurrencyStamp, setValue]);

  const validateItems = (data: LocalVesselItemForm[]): boolean => {
    const errors: string[] = [];
    data.forEach((item, idx) => {
      const result = itemSchema.safeParse(item);
      if (!result.success) {
        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');
      } else {
        errors[idx] = '';
      }
    });
    setItemErrors(errors);
    return errors.every(e => !e);
  };

  const onFormSubmit = async (header: LocalVesselHeaderForm) => {
    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as LocalVesselItemForm[];

    // Required backend fields
    const requiredFields = {
      deleted: '',
      shipment: '',
      statusBms: '',
      transType: '',
      portOrigin: '',
      vesselType: '',
      destinationPort: '',
    };
    // Ensure required fields for header, but always use concurrencyStamp from header
    const headerWithRequired = { ...requiredFields, ...header, concurrencyStamp: header.concurrencyStamp };
    // Ensure required fields for each item and map tenant/businessPartner names to IDs
    const itemsWithRequired = currentItems.map(item => {
      // Remove concurrencyStamp from the spread to avoid null
      const { concurrencyStamp, ...rest } = item;
      const transformedItem: LocalVesselItemFormWithConcurrency = { ...requiredFields, ...rest, concurrencyStamp: concurrencyStamp ?? undefined };
      if (item.tenant) {
        const tenant = tenants.find(t => t.name === item.tenant);
        transformedItem.tenantId = tenant?.id || '';
      } else {
        transformedItem.tenantId = '';
      }
      if (item.businessPartner) {
        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);
        transformedItem.businessPartnerId = businessPartner?.id || '';
      } else {
        transformedItem.businessPartnerId = '';
      }
      // Always use the latest concurrencyStamp from initialItems (API data)
      const latest = (initialItems as LocalVesselItemFormWithConcurrency[]).find(i => i.id === item.id);
      if (latest && latest.concurrencyStamp) {
        transformedItem.concurrencyStamp = latest.concurrencyStamp;
      }
      return transformedItem;
    });

    if (!validateItems(itemsWithRequired)) return;
    setSubmitError(null);
    try {
      await onSubmit(headerWithRequired, itemsWithRequired);
      // Do not reset here! Let the parent refetch and effect handle it.
    } catch (error: unknown) {
      let message = 'An error occurred';
      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {
        message = (error as { message: string }).message;
      }
      setSubmitError(message);
      // Do not reset form or items on error
    }
  };

  const handleAddLine = () => {
    setItems(prev => [...prev, {} as LocalVesselItemForm]);
  };

  const { toast } = useToast();

  return (
    <div className="w-full mx-auto">
      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-bold text-gray-800 dark:text-white">{title}</h2>
              <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
            </div>

            {initialHeader.docStatus && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Status:</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {initialHeader.docStatus}
                </span>
              </div>
            )}
          </div>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
            {submitError && (
              <div className="text-red-500 text-sm mb-2">{submitError}</div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormSection showDivider={false}>
                <FormField label={t('form.labels.docNum')} labelWidth='100px'>
                  <Input
                    {...register('docNum')}
                    value={methods.watch('docNum')}
                    readOnly={mode === 'create'}
                    disabled={mode === 'edit'}
                  />
                  {errors.docNum && (
                    <span className="text-red-500 text-xs">{errors.docNum.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.transType')} labelWidth='100px'>
                  <Select value={methods.watch('transType') ?? ''} onValueChange={v => setValue('transType', v)}>
                    <SelectTrigger size={'sm'} className="w-full">
                      <SelectValue placeholder="Select TransType" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="IN">IN</SelectItem>
                      <SelectItem value="OUT">OUT</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.transType && (
                    <span className="text-red-500 text-xs">{errors.transType.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.vesselName')} labelWidth='100px'>
                  <Controller
                    name="vesselId"
                    control={methods.control}
                    render={({ field }) => (
                      <VesselSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select vessel..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.vesselId && (
                    <span className="text-red-500 text-xs">{errors.vesselId.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.bargeName')} labelWidth='100px'>
                  <Controller
                    name="bargeId"
                    control={methods.control}
                    render={({ field }) => (
                      <VesselSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select barge..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.bargeId && (
                    <span className="text-red-500 text-xs">{errors.bargeId.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.voyage')} labelWidth='100px'>
                  <Input {...register('voyage')} />
                  {errors.voyage && (
                    <span className="text-red-500 text-xs">{errors.voyage.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label={t('form.labels.jetty')} labelWidth='100px'>
                  <Controller
                    name="jettyId"
                    control={methods.control}
                    render={({ field }) => (
                      <JettySelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select jetty..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.jettyId && (
                    <span className="text-red-500 text-xs">{errors.jettyId.message as string}</span>
                  )}
                </FormField>
                <FormField label={t('form.labels.asideDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('asideDate')} />
                  {errors.asideDate && (
                    <span className="text-red-500 text-xs">{errors.asideDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.castOfDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('castOfDate')} />
                  {errors.castOfDate && (
                    <span className="text-red-500 text-xs">{errors.castOfDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.arrivalDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselArrival')} />
                  {errors.vesselArrival && (
                    <span className="text-red-500 text-xs">{errors.vesselArrival.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.departureDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselDeparture')} />
                  {errors.vesselDeparture && (
                    <span className="text-red-500 text-xs">{errors.vesselDeparture.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label={t('form.labels.postingDate')} labelWidth='100px'>
                  <Input type="date" {...register('postingDate')} />
                  {errors.postingDate && (
                    <span className="text-red-500 text-xs">{errors.postingDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.portOrigin')} labelWidth='100px'>
                  <Controller
                    name="portOriginId"
                    control={methods.control}
                    render={({ field }) => (
                      <PortOfLoadingSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select port origin..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.portOriginId && (
                    <span className="text-red-500 text-xs">{errors.portOriginId.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.destinationPort')} labelWidth='100px'>
                  <Controller
                    name="destinationPortId"
                    control={methods.control}
                    render={({ field }) => (
                      <DestinationPortSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select destination port..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.destinationPortId && (
                    <span className="text-red-500 text-xs">{errors.destinationPortId.message as string}</span>
                  )}
                </FormField>
              </FormSection>
            </div>
            <div className="mt-6">
              <label className="block font-medium mb-2">Items</label>
              <HotTable
                ref={hotTableRef}
                themeName="ht-theme-main"
                data={items}
                columns={generatedColumns as ColumnSettings[]}
                colHeaders={generatedColumns.map((col: unknown) => (typeof col === 'object' && col && 'title' in col ? (col as { title?: string }).title : undefined)).filter((t: unknown): t is string => typeof t === 'string')}
                rowHeaders={true}
                height="50vh"
                licenseKey="non-commercial-and-evaluation"
                stretchH="all"
                contextMenu={true}
                manualColumnResize={true}
                manualRowResize={true}
                autoColumnSize={false}
                autoRowSize={false}
                startRows={1}
                dropdownMenu={true}
                filters={true}
                colWidths={80}
                hiddenColumns={{
                  copyPasteEnabled: true,
                  indicators: true,
                  columns: [0, 1, 2]
                }}
                width="100%"
                persistentState={true}
              />
              {itemErrors.some(e => e) && (
                <div className="mt-2 text-red-500 text-xs">
                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}
                </div>
              )}
            </div>
            <div className="flex justify-end gap-2">
              {currentUser && ['Jetty Approval Admin', 'Jetty Approval Shipping', 'admin'].some(role => currentUser?.roles?.includes(role)) && (
                <>
                  {showAddLineButton && (
                    <Button type="button" variant="outline" onClick={handleAddLine} disabled={isSubmitting}>
                      + Add Line
                    </Button>
                  )}
                  {typeof selectedJetty?.isCustomArea === 'boolean' && selectedJetty.isCustomArea && (
                    <Button
                      type="button"
                      variant="warning"
                      onClick={handleApproval}
                      disabled={
                        isSubmitting ||
                        isSubmittingApproval ||
                        initialHeader.docStatus === 'Waiting' ||
                        initialHeader.docStatus === 'Approved'
                      }
                    >
                      {isSubmittingApproval ? 'Submitting...' : 'Submit Approval'}
                    </Button>
                  )}
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}
                  </Button>
                </>
              )}
            </div>
          </form>
        </FormProvider>
      </div>
      <DocumentPreviewDialog
        isOpen={isPreviewDialogOpen}
        onOpenChange={setIsPreviewDialogOpen}
        documentSrc={previewDocumentSrc}
      />
    </div>
  );
};

export default LocalVesselForm; 