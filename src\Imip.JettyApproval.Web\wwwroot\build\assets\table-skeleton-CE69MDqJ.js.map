{"version": 3, "file": "table-skeleton-CE69MDqJ.js", "sources": ["../../../../../frontend/src/components/ui/table-skeleton.tsx"], "sourcesContent": ["import { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { type ColumnDef } from \"@tanstack/react-table\";\r\n\r\ninterface TableSkeletonProps<TData> {\r\n  columns: ColumnDef<TData>[];\r\n  rowCount?: number;\r\n}\r\n\r\nconst TableSkeleton = <TData,>({ \r\n  columns, \r\n  rowCount = 10 \r\n}: TableSkeletonProps<TData>) => {\r\n  const skeletonRows = Array.from({ length: rowCount }, (_, i) => i);\r\n  \r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Table Header */}\r\n      <div className=\"border-b bg-muted/50\">\r\n        <div className=\"flex items-center h-12 px-4\">\r\n          {columns.map((_column, index) => (\r\n            <div key={index} className=\"flex-1 px-2\">\r\n              <Skeleton className=\"h-4 w-20\" />\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Table Body */}\r\n      <div className=\"divide-y\">\r\n        {skeletonRows.map((rowIndex) => (\r\n          <div key={rowIndex} className=\"flex items-center h-14 px-4 hover:bg-muted/50\">\r\n            {columns.map((_column, colIndex) => (\r\n              <div key={colIndex} className=\"flex-1 px-2\">\r\n                <Skeleton className=\"h-4 w-full max-w-32\" />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      {/* Pagination Skeleton */}\r\n      <div className=\"flex items-center justify-between px-4 py-3 border-t bg-muted/30\">\r\n        <Skeleton className=\"h-4 w-32\" />\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Skeleton className=\"h-8 w-8\" />\r\n          <Skeleton className=\"h-8 w-8\" />\r\n          <Skeleton className=\"h-8 w-8\" />\r\n          <Skeleton className=\"h-8 w-8\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TableSkeleton; "], "names": ["TableSkeleton", "columns", "rowCount", "skeletonRows", "_", "i", "jsxs", "jsx", "_column", "index", "Skeleton", "rowIndex", "colIndex"], "mappings": "oFAQA,MAAMA,EAAgB,CAAS,CAC7B,QAAAC,EACA,SAAAC,EAAW,EACb,IAAiC,CACzB,MAAAC,EAAe,MAAM,KAAK,CAAE,OAAQD,GAAY,CAACE,EAAGC,IAAMA,CAAC,EAG/D,OAAAC,EAAA,KAAC,MAAI,CAAA,UAAU,SAEb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,uBACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACZ,SAAAN,EAAQ,IAAI,CAACO,EAASC,UACpB,MAAgB,CAAA,UAAU,cACzB,SAAAF,EAAA,IAACG,EAAS,CAAA,UAAU,UAAW,CAAA,CADvB,EAAAD,CAEV,CACD,CAAA,CACH,CACF,CAAA,EAGCF,EAAA,IAAA,MAAA,CAAI,UAAU,WACZ,SAAaJ,EAAA,IAAKQ,GAChBJ,EAAA,IAAA,MAAA,CAAmB,UAAU,gDAC3B,WAAQ,IAAI,CAACC,EAASI,IACpBL,EAAAA,IAAA,MAAA,CAAmB,UAAU,cAC5B,eAACG,EAAS,CAAA,UAAU,qBAAsB,CAAA,GADlCE,CAEV,CACD,GALOD,CAMV,CACD,EACH,EAGAL,EAAAA,KAAC,MAAI,CAAA,UAAU,mEACb,SAAA,CAACC,EAAAA,IAAAG,EAAA,CAAS,UAAU,UAAW,CAAA,EAC/BJ,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAAG,EAAA,CAAS,UAAU,SAAU,CAAA,EAC9BH,EAAAA,IAACG,EAAS,CAAA,UAAU,SAAU,CAAA,EAC9BH,EAAAA,IAACG,EAAS,CAAA,UAAU,SAAU,CAAA,EAC9BH,EAAAA,IAACG,EAAS,CAAA,UAAU,SAAU,CAAA,CAAA,CAChC,CAAA,CAAA,CACF,CAAA,CAAA,EACF,CAEJ"}