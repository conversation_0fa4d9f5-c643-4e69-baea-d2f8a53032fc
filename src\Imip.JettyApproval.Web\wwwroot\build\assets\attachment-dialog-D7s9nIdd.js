import{a as u,$ as E,j as e}from"./vendor-6tJeyfYI.js";import{k as W,B as O,X as ae,M as ee}from"./app-layout-rNt37hVL.js";import{D as ie,b as re,c as le,d as ne}from"./dialog-BmEXyFlW.js";import{I as oe}from"./input-DlXlkYlT.js";import{T as ce,a as de,b as q,c as X,d as pe,e as G}from"./table-BKSoE52x.js";import{T as he,a as ge,b as ve,c as te,d as fe}from"./tabs-Dk-TLCdA.js";import{g as L}from"./App-DnhJzTNn.js";/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],J=W("circle-alert",je);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],ye=W("download",be);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],ke=W("file-text",Ne);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]],ue=W("file-up",we);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]],me=W("file",De);/**
 * @license lucide-react v0.513.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fe=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Te=W("image",Fe),xe=(U={})=>{const{maxFiles:S=1/0,maxSize:r=1/0,accept:v="*",multiple:l=!1,initialFiles:w=[],onFilesChange:N,onFilesAdded:D}=U,[k,m]=u.useState({files:w.map(s=>({file:s,id:s.id,preview:s.url})),isDragging:!1,errors:[]}),j=u.useRef(null),P=u.useCallback(s=>{if(s instanceof File){if(s.size>r)return`File "${s.name}" exceeds the maximum size of ${H(r)}.`}else if(s.size>r)return`File "${s.name}" exceeds the maximum size of ${H(r)}.`;if(v!=="*"){const n=v.split(",").map(h=>h.trim()),i=s instanceof File?s.type||"":s.type,x=`.${s instanceof File,s.name.split(".").pop()}`;if(!n.some(h=>{if(h.startsWith("."))return x.toLowerCase()===h.toLowerCase();if(h.endsWith("/*")){const B=h.split("/")[0];return i.startsWith(`${B}/`)}return i===h}))return`File "${s instanceof File,s.name}" is not an accepted file type.`}return null},[v,r]),z=u.useCallback(s=>s instanceof File?URL.createObjectURL(s):s.url,[]),A=u.useCallback(s=>s instanceof File?`${s.name}-${Date.now()}-${Math.random().toString(36).substring(2,9)}`:s.id,[]),R=u.useCallback(()=>{m(s=>{s.files.forEach(i=>{i.preview&&i.file instanceof File&&i.file.type.startsWith("image/")&&URL.revokeObjectURL(i.preview)}),j.current&&(j.current.value="");const n={...s,files:[],errors:[]};return N?.(n.files),n})},[N]),b=u.useCallback(s=>{if(!s||s.length===0)return;const n=Array.from(s),i=[];if(m(o=>({...o,errors:[]})),l||R(),l&&S!==1/0&&k.files.length+n.length>S){i.push(`You can only upload a maximum of ${S} files.`),m(o=>({...o,errors:i}));return}const x=[];n.forEach(o=>{if(l&&k.files.some(Z=>Z.file.name===o.name&&Z.file.size===o.size))return;if(o.size>r){i.push(l?`Some files exceed the maximum size of ${H(r)}.`:`File exceeds the maximum size of ${H(r)}.`);return}const h=P(o);h?i.push(h):x.push({file:o,id:A(o),preview:z(o)})}),x.length>0?(D?.(x),m(o=>{const h=l?[...o.files,...x]:x;return N?.(h),{...o,files:h,errors:i}})):i.length>0&&m(o=>({...o,errors:i})),j.current&&(j.current.value="")},[k.files,S,l,r,P,z,A,R,N,D]),Q=u.useCallback(s=>{m(n=>{const i=n.files.find(o=>o.id===s);i&&i.preview&&i.file instanceof File&&i.file.type.startsWith("image/")&&URL.revokeObjectURL(i.preview);const x=n.files.filter(o=>o.id!==s);return N?.(x),{...n,files:x,errors:[]}})},[N]),I=u.useCallback(()=>{m(s=>({...s,errors:[]}))},[]),V=u.useCallback(s=>{s.preventDefault(),s.stopPropagation(),m(n=>({...n,isDragging:!0}))},[]),$=u.useCallback(s=>{s.preventDefault(),s.stopPropagation(),!s.currentTarget.contains(s.relatedTarget)&&m(n=>({...n,isDragging:!1}))},[]),K=u.useCallback(s=>{s.preventDefault(),s.stopPropagation()},[]),a=u.useCallback(s=>{if(s.preventDefault(),s.stopPropagation(),m(n=>({...n,isDragging:!1})),!j.current?.disabled&&s.dataTransfer.files&&s.dataTransfer.files.length>0)if(l)b(s.dataTransfer.files);else{const n=s.dataTransfer.files[0];b([n])}},[b,l]),y=u.useCallback(s=>{s.target.files&&s.target.files.length>0&&b(s.target.files)},[b]),d=u.useCallback(()=>{j.current&&j.current.click()},[]),F=u.useCallback((s={})=>({...s,type:"file",onChange:y,accept:s.accept||v,multiple:s.multiple!==void 0?s.multiple:l,ref:j}),[v,l,y]);return[k,{addFiles:b,removeFile:Q,clearFiles:R,clearErrors:I,handleDragEnter:V,handleDragLeave:$,handleDragOver:K,handleDrop:a,handleFileChange:y,openFileDialog:d,getInputProps:F}]},H=(U,S=2)=>{if(U===0)return"0 Bytes";const r=1024,v=S<0?0:S,l=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],w=Math.floor(Math.log(U)/Math.log(r));return Number.parseFloat((U/Math.pow(r,w)).toFixed(v))+l[w]},Ue=({isOpen:U,onOpenChange:S,attachment:r})=>{const[v,l]=u.useState(""),[w,N]=u.useState(!1),[D,k]=u.useState("");E.useEffect(()=>(r&&U&&(N(!0),k(""),ee.getFileStream(r.id).then(j=>{if(j&&j.data instanceof Blob){const P=URL.createObjectURL(j.data);l(P);return}throw new Error("Invalid response type for file stream")}).catch(j=>{k("Failed to load file preview")}).finally(()=>{N(!1)})),()=>{}),[r,U]),E.useEffect(()=>()=>{v&&v.startsWith("blob:")&&URL.revokeObjectURL(v)},[v]);const m=j=>{const P=j.split(".").pop()?.toLowerCase();return["pdf","jpg","jpeg","png","gif","txt","html","htm"].includes(P||"")};return r?e.jsx(ie,{open:U,onOpenChange:S,children:e.jsxs(re,{className:"min-w-[1000px] w-auto h-[90vh] flex flex-col",children:[e.jsx(le,{children:e.jsx(ne,{children:r.fileName})}),e.jsx("div",{className:"flex-grow overflow-auto",children:w?e.jsxs("div",{className:"flex items-center justify-center h-full",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),e.jsx("span",{className:"ml-2",children:"Loading preview..."})]}):D?e.jsx("div",{className:"flex items-center justify-center h-full text-red-500",children:e.jsx("span",{children:D})}):m(r.fileName??"")?e.jsx("iframe",{src:v,title:"Document Preview",className:"w-full h-full border-none"}):e.jsx("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:e.jsxs("div",{className:"text-center",children:[e.jsx(me,{className:"w-12 h-12 mx-auto mb-4"}),e.jsx("p",{children:"Preview not available for this file type"}),e.jsx("p",{className:"text-sm",children:"Please download the file to view it"})]})})})]})}):null},Ce=({open:U,onOpenChange:S,attachments:r,title:v,queryClient:l,referenceId:w,documentReferenceId:N,defaultTabName:D,docType:k,transType:m,tabName:j,onUploadSuccess:P})=>{const[z,A]=u.useState(null),[R,b]=u.useState(!1),Q=r.reduce((c,t)=>{const C=t.tabName||"Other";return c[C]||(c[C]=[]),c[C].push(t),c},{}),I=Object.keys(Q),$=I.includes("SHIPPING")?I:[...I,"SHIPPING"],[K,a]=u.useState(D&&$.includes(D)?D:$[0]??"");E.useEffect(()=>{$.includes(K)||a($[0]??"")},[$,K]);const y=c=>{A(c),b(!0)},d=async c=>{try{const t=L("ekbUrl"),p=`${t?decodeURIComponent(t):"https://ekb-dev.imip.co.id"}/api/ekb/files/stream/${c.id}`,T=await fetch(p,{method:"GET",credentials:"include"});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const g=await T.blob(),M=window.URL.createObjectURL(g),f=document.createElement("a");f.href=M,f.download=c.fileName??"",document.body.appendChild(f),f.click(),document.body.removeChild(f),window.URL.revokeObjectURL(M)}catch{}},F=async c=>{if(c.id&&confirm(`Are you sure you want to delete "${c.fileName}"?`))try{await ee.deleteDocAttachment(c.id),l.invalidateQueries({queryKey:["export-vessel"]}),l.invalidateQueries({queryKey:["local-vessel"]}),l.invalidateQueries({queryKey:["import-vessel"]}),_("Attachment deleted successfully!"),setTimeout(()=>_(null),3e3),P?.()}catch{alert("Failed to delete attachment")}},s=c=>{switch(c.split(".").pop()?.toLowerCase()){case"pdf":return e.jsx(ke,{className:"w-4 h-4"});case"jpg":case"jpeg":case"png":case"gif":return e.jsx(Te,{className:"w-4 h-4"});default:return e.jsx(me,{className:"w-4 h-4"})}},n=100*1024*1024,i=10,[x,o]=E.useState(!1),[h,B]=xe({multiple:!0,maxFiles:i,maxSize:n,onFilesAdded:async c=>{for(const t of c)t.file&&typeof t.file=="object"&&"name"in t.file&&"size"in t.file;B.clearFiles()}}),[Z,Y]=E.useState(null),[se,_]=E.useState(null);return e.jsxs(e.Fragment,{children:[e.jsx(ie,{open:U,onOpenChange:S,children:e.jsxs(re,{className:"min-w-[800px] w-auto h-[90vh] flex flex-col",children:[e.jsx(le,{children:e.jsx(ne,{children:v})}),e.jsx("div",{className:"flex-grow overflow-hidden",children:e.jsxs(he,{value:K,onValueChange:c=>a(c),className:"h-full flex flex-col",children:[e.jsx(ge,{className:`grid w-full grid-cols-${$.length}`,children:I.map(c=>e.jsx(ve,{value:c,children:c},c))}),I.length>0?I.map(c=>e.jsx(te,{value:c,children:e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{role:"button",onClick:x?void 0:B.openFileDialog,onDragEnter:x?void 0:t=>{t.preventDefault(),B.handleDragEnter(t)},onDragLeave:x?void 0:t=>{t.preventDefault(),B.handleDragLeave(t)},onDragOver:x?void 0:t=>t.preventDefault(),onDrop:x?void 0:async t=>{if(t.preventDefault(),B.handleDrop(t),t.dataTransfer.files&&t.dataTransfer.files.length>0){const C=t.dataTransfer.files[0];o(!0);try{const p=L("ekbUrl"),T=p?decodeURIComponent(p):"https://ekb-dev.imip.co.id";let g="/api/ekb/doc-attachments/upload";g.startsWith("/")&&(g=T.replace(/\/$/,"")+g);const M=L("EkbApiToken")??"",f=new FormData;f.append("File",C),f.append("ReferenceId",String(w)),f.append("DocType",k??"Import"),f.append("ReferenceType",k??"Import"),f.append("TransType",m??"ImportDetails"),f.append("TabName",c??D??"SHIPPING"),f.append("DocumentReferenceId",String(N)),await fetch(g,{method:"POST",body:f,credentials:"include",headers:M?{Authorization:`Bearer ${M}`}:void 0}),l.invalidateQueries({queryKey:["export-vessel"]}),l.invalidateQueries({queryKey:["local-vessel"]}),l.invalidateQueries({queryKey:["import-vessel"]}),Y(null),_("File uploaded successfully!"),o(!1),P?.(),setTimeout(()=>_(null),3e3)}catch(p){o(!1);let T="Upload failed",g="";p&&typeof p=="object"&&("message"in p&&typeof p.message=="string"&&(T=p.message),"details"in p&&typeof p.details=="string"&&(g=p.details)),Y(g?`${T}
${g}`:T)}}},"data-dragging":h.isDragging||void 0,className:"border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]","aria-disabled":x,tabIndex:x?-1:0,children:[e.jsx(oe,{...B.getInputProps(),disabled:x,onChange:async t=>{if(t.target.files&&t.target.files.length>0){const C=t.target.files[0];o(!0);try{const p=L("ekbUrl"),T=p?decodeURIComponent(p):"https://ekb-dev.imip.co.id";let g="/api/ekb/doc-attachments/upload";g.startsWith("/")&&(g=T.replace(/\/$/,"")+g);const M=L("EkbApiToken")??"",f=new FormData;f.append("File",C),f.append("ReferenceId",String(w)),f.append("DocType",k??"Import"),f.append("ReferenceType",k??"Import"),f.append("TransType",m??"ImportDetails"),f.append("TabName",c??D??"SHIPPING"),f.append("DocumentReferenceId",String(N)),await fetch(g,{method:"POST",body:f,credentials:"include",headers:M?{Authorization:`Bearer ${M}`}:void 0}),l.invalidateQueries({queryKey:["export-vessel"]}),l.invalidateQueries({queryKey:["local-vessel"]}),l.invalidateQueries({queryKey:["import-vessel"]}),Y(null),_("File uploaded successfully!"),o(!1),P?.(),setTimeout(()=>_(null),3e3)}catch(p){o(!1);let T="Upload failed",g="";p&&typeof p=="object"&&("message"in p&&typeof p.message=="string"&&(T=p.message),"details"in p&&typeof p.details=="string"&&(g=p.details)),Y(g?`${T}
${g}`:T)}}},className:"sr-only","aria-label":"Upload files"}),e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsx("div",{className:"bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(ue,{className:"size-4 opacity-60"})}),e.jsx("p",{className:"mb-1.5 text-sm font-medium",children:"Upload files"}),e.jsx("p",{className:"text-muted-foreground mb-2 text-xs",children:"Drag & drop or click to browse"}),e.jsxs("div",{className:"text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs",children:[e.jsx("span",{children:"All files"}),e.jsx("span",{children:"∙"}),e.jsxs("span",{children:["Max ",i," files"]}),e.jsx("span",{children:"∙"}),e.jsxs("span",{children:["Up to ",H(n)]})]})]})]}),x&&e.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground mt-2",children:[e.jsx("span",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),"Uploading..."]}),h.errors.length>0&&e.jsxs("div",{className:"text-destructive flex items-center gap-1 text-xs",role:"alert",children:[e.jsx(J,{className:"size-3 shrink-0"}),e.jsx("span",{children:h.errors[0]})]}),Z&&e.jsxs("div",{className:"text-destructive flex items-center gap-1 text-xs",role:"alert",children:[e.jsx(J,{className:"size-3 shrink-0"}),e.jsx("span",{children:Z})]}),se&&e.jsxs("div",{className:"text-green-600 flex items-center gap-1 text-xs",role:"alert",children:[e.jsx("span",{children:"✓"}),e.jsx("span",{children:se})]}),h.files.length>0&&e.jsxs("div",{className:"space-y-2",children:[h.files.map(t=>e.jsxs("div",{className:"bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3",children:[e.jsxs("div",{className:"flex items-center gap-3 overflow-hidden",children:[e.jsx("div",{className:"flex aspect-square size-10 shrink-0 items-center justify-center rounded border",children:s((typeof t.file=="object"&&"lastModified"in t.file,t.file.name))}),e.jsxs("div",{className:"flex min-w-0 flex-col gap-0.5",children:[e.jsx("p",{className:"truncate text-[13px] font-medium",children:(typeof t.file=="object"&&"lastModified"in t.file,t.file.name)}),e.jsx("p",{className:"text-muted-foreground text-xs",children:H((typeof t.file=="object"&&"lastModified"in t.file,t.file.size))})]})]}),e.jsx(O,{size:"icon",variant:"ghost",className:"text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent",onClick:()=>B.removeFile(t.id),"aria-label":"Remove file",children:e.jsx(ae,{className:"size-4","aria-hidden":"true"})})]},t.id)),h.files.length>1&&e.jsx("div",{children:e.jsx(O,{size:"sm",variant:"outline",onClick:B.clearFiles,children:"Remove all files"})})]}),e.jsxs(ce,{children:[e.jsx(de,{children:e.jsxs(q,{children:[e.jsx(X,{children:"File Name"}),e.jsx(X,{className:"w-40",children:"Actions"})]})}),e.jsx(pe,{children:r.length===0?e.jsx(q,{children:e.jsx(G,{colSpan:2,className:"text-center text-muted-foreground",children:"No files uploaded yet."})}):r.map((t,C)=>e.jsxs(q,{children:[e.jsx(G,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[s(t.fileName??""),e.jsx("span",{title:t.fileName??"",children:t.fileName})]})}),e.jsx(G,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(O,{variant:"outline",size:"sm",onClick:()=>y(t),children:"Preview"}),e.jsx(O,{variant:"outline",size:"sm",onClick:()=>d(t),children:e.jsx(ye,{className:"w-4 h-4"})}),e.jsx(O,{variant:"outline",size:"sm",onClick:()=>F(t),className:"text-destructive hover:text-destructive",children:e.jsx(fe,{className:"w-4 h-4"})})]})})]},t.id||C))})]})]})},c)):e.jsx(te,{value:"SHIPPING",children:e.jsx(Se,{attachments:r,getFileIcon:s,queryClient:l,referenceId:w,documentReferenceId:N,docType:k,transType:m,tabName:j,defaultTabName:D,onUploadSuccess:P})},"SHIPPING")]})})]})}),e.jsx(Ue,{isOpen:R,onOpenChange:b,attachment:z})]})};function Se({attachments:U,getFileIcon:S,queryClient:r,referenceId:v,documentReferenceId:l,docType:w,transType:N,tabName:D,defaultTabName:k,onUploadSuccess:m}){const[z,A]=E.useState(!1),[R,b]=xe({multiple:!0,maxFiles:10,maxSize:104857600,onFilesAdded:async a=>{for(const y of a)y.file&&typeof y.file=="object"&&"name"in y.file&&"size"in y.file;b.clearFiles()}}),[Q,I]=E.useState(null),[V,$]=E.useState(null),K=async a=>{if(a.id&&confirm(`Are you sure you want to delete "${a.fileName}"?`))try{await ee.deleteDocAttachment(a.id),r.invalidateQueries({queryKey:["export-vessel"]}),r.invalidateQueries({queryKey:["local-vessel"]}),r.invalidateQueries({queryKey:["import-vessel"]}),m?.()}catch{alert("Failed to delete attachment")}};return e.jsx("form",{onSubmit:a=>a.preventDefault(),className:"space-y-4",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{role:"button",onClick:z?void 0:b.openFileDialog,onDragEnter:z?void 0:a=>{a.preventDefault(),b.handleDragEnter(a)},onDragLeave:z?void 0:a=>{a.preventDefault(),b.handleDragLeave(a)},onDragOver:z?void 0:a=>a.preventDefault(),onDrop:z?void 0:async a=>{if(a.preventDefault(),b.handleDrop(a),a.dataTransfer.files&&a.dataTransfer.files.length>0){const y=a.dataTransfer.files[0];A(!0);try{const d=L("ekbUrl"),F=d?decodeURIComponent(d):"https://ekb-dev.imip.co.id";let s="/api/ekb/doc-attachments/upload";s.startsWith("/")&&(s=F.replace(/\/$/,"")+s);const n=L("EkbApiToken")??"",i=new FormData;i.append("File",y),i.append("ReferenceId",String(v)),i.append("DocType",w??"Import"),i.append("ReferenceType",w??"Import"),i.append("TransType",N??"ImportDetails"),i.append("TabName",D??k??"SHIPPING"),i.append("DocumentReferenceId",String(l)),await fetch(s,{method:"POST",body:i,credentials:"include",headers:n?{Authorization:`Bearer ${n}`}:void 0}),r.invalidateQueries({queryKey:["export-vessel"]}),r.invalidateQueries({queryKey:["local-vessel"]}),r.invalidateQueries({queryKey:["import-vessel"]}),I(null),$("File uploaded successfully!"),A(!1),m?.(),setTimeout(()=>$(null),3e3)}catch(d){A(!1);let F="Upload failed",s="";d&&typeof d=="object"&&("message"in d&&typeof d.message=="string"&&(F=d.message),"details"in d&&typeof d.details=="string"&&(s=d.details)),I(s?`${F}
${s}`:F)}}},"data-dragging":R.isDragging||void 0,className:"border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]","aria-disabled":z,tabIndex:z?-1:0,children:[e.jsx(oe,{...b.getInputProps(),disabled:z,onChange:async a=>{if(a.target.files&&a.target.files.length>0){const y=a.target.files[0];A(!0);try{const d=L("ekbUrl"),F=d?decodeURIComponent(d):"https://ekb-dev.imip.co.id";let s="/api/ekb/doc-attachments/upload";s.startsWith("/")&&(s=F.replace(/\/$/,"")+s);const n=L("EkbApiToken")??"",i=new FormData;i.append("File",y),i.append("ReferenceId",String(v)),i.append("DocType",w??"Import"),i.append("ReferenceType",w??"Import"),i.append("TransType",N??"ImportDetails"),i.append("TabName",D??k??"SHIPPING"),i.append("DocumentReferenceId",String(l)),await fetch(s,{method:"POST",body:i,credentials:"include",headers:n?{Authorization:`Bearer ${n}`}:void 0}),r.invalidateQueries({queryKey:["export-vessel"]}),r.invalidateQueries({queryKey:["local-vessel"]}),r.invalidateQueries({queryKey:["import-vessel"]}),I(null),$("File uploaded successfully!"),A(!1),m?.(),setTimeout(()=>$(null),3e3)}catch(d){A(!1);let F="Upload failed",s="";d&&typeof d=="object"&&("message"in d&&typeof d.message=="string"&&(F=d.message),"details"in d&&typeof d.details=="string"&&(s=d.details)),I(s?`${F}
${s}`:F)}}},className:"sr-only","aria-label":"Upload files"}),e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsx("div",{className:"bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border","aria-hidden":"true",children:e.jsx(ue,{className:"size-4 opacity-60"})}),e.jsx("p",{className:"mb-1.5 text-sm font-medium",children:"Upload files"}),e.jsx("p",{className:"text-muted-foreground mb-2 text-xs",children:"Drag & drop or click to browse"}),e.jsxs("div",{className:"text-muted-foreground/70 flex flex-wrap justify-center gap-1 text-xs",children:[e.jsx("span",{children:"All files"}),e.jsx("span",{children:"∙"}),e.jsxs("span",{children:["Max ",10," files"]}),e.jsx("span",{children:"∙"}),e.jsxs("span",{children:["Up to ",H(104857600)]})]})]})]}),z&&e.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground mt-2",children:[e.jsx("span",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),"Uploading..."]}),R.errors.length>0&&e.jsxs("div",{className:"text-destructive flex items-center gap-1 text-xs",role:"alert",children:[e.jsx(J,{className:"size-3 shrink-0"}),e.jsx("span",{children:R.errors[0]})]}),Q&&e.jsxs("div",{className:"text-destructive flex items-center gap-1 text-xs",role:"alert",children:[e.jsx(J,{className:"size-3 shrink-0"}),e.jsx("span",{children:Q})]}),V&&e.jsxs("div",{className:"text-green-600 flex items-center gap-1 text-xs",role:"alert",children:[e.jsx("span",{children:"✓"}),e.jsx("span",{children:V})]}),R.files.length>0&&e.jsxs("div",{className:"space-y-2",children:[R.files.map(a=>e.jsxs("div",{className:"bg-background flex items-center justify-between gap-2 rounded-lg border p-2 pe-3",children:[e.jsxs("div",{className:"flex items-center gap-3 overflow-hidden",children:[e.jsx("div",{className:"flex aspect-square size-10 shrink-0 items-center justify-center rounded border",children:S((typeof a.file=="object"&&"lastModified"in a.file,a.file.name))}),e.jsxs("div",{className:"flex min-w-0 flex-col gap-0.5",children:[e.jsx("p",{className:"truncate text-[13px] font-medium",children:(typeof a.file=="object"&&"lastModified"in a.file,a.file.name)}),e.jsx("p",{className:"text-muted-foreground text-xs",children:H((typeof a.file=="object"&&"lastModified"in a.file,a.file.size))})]})]}),e.jsx(O,{size:"icon",variant:"ghost",className:"text-muted-foreground/80 hover:text-foreground -me-2 size-8 hover:bg-transparent",onClick:()=>b.removeFile(a.id),"aria-label":"Remove file",children:e.jsx(ae,{className:"size-4","aria-hidden":"true"})})]},a.id)),R.files.length>1&&e.jsx("div",{children:e.jsx(O,{size:"sm",variant:"outline",onClick:b.clearFiles,children:"Remove all files"})})]}),e.jsxs(ce,{children:[e.jsx(de,{children:e.jsxs(q,{children:[e.jsx(X,{children:"File Name"}),e.jsx(X,{className:"w-40",children:"Actions"})]})}),e.jsx(pe,{children:U.length===0?e.jsx(q,{children:e.jsx(G,{colSpan:2,className:"text-center text-muted-foreground",children:"No files uploaded yet."})}):U.map((a,y)=>e.jsxs(q,{children:[e.jsx(G,{children:e.jsxs("div",{className:"flex items-center gap-2",children:[S(a.fileName??""),e.jsx("span",{title:a.fileName??"",children:a.fileName})]})}),e.jsx(G,{children:e.jsx("div",{className:"flex gap-2",children:e.jsx(O,{variant:"outline",size:"sm",onClick:()=>K(a),className:"text-destructive hover:text-destructive",children:e.jsx(fe,{className:"w-4 h-4"})})})})]},a.id||y))})]})]})})}export{Ce as A};
//# sourceMappingURL=attachment-dialog-D7s9nIdd.js.map
