import type { ExportVesselProjectionDto, ImportVesselProjectionDto, LocalVesselProjectionDto } from '@/clientEkb/types.gen';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { router } from '@inertiajs/react';
import type { CellContext, ColumnDef } from '@tanstack/react-table';
import { ArrowUpRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';

// Helper function to render docStatus badge
const renderDocStatusBadge = (status: string | null | undefined) => {
  if (!status) return <Badge variant="outline" size="sm">-</Badge>;

  const statusLower = status.toLowerCase();

  switch (statusLower) {
    case 'draft':
      return <Badge variant="secondary" size="sm">Draft</Badge>;
    case 'waiting':
    case 'pending':
      return <Badge variant="warning" size="sm">{status}</Badge>;
    case 'approved':
      return <Badge variant="success" size="sm">Approved</Badge>;
    case 'rejected':
      return <Badge variant="destructive" size="sm">Rejected</Badge>;
    case 'cancelled':
      return <Badge variant="outline" size="sm">Cancelled</Badge>;
    case 'open':
      return <Badge variant="info" size="sm">Open</Badge>;
    case 'submit':
      return <Badge variant="primary" size="sm">Submit</Badge>;
    default:
      return <Badge variant="outline" size="sm">{status}</Badge>;
  }
};

function renderViewButton(id: string, type: 'import' | 'export' | 'local') {
  const handleClick = () => {
    router.visit(`/${type}/edit/${id}`);
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button size="sm" variant="ghost" aria-label="View Vessel" onClick={handleClick}>
          <ArrowUpRight className="w-4 h-4 text-blue-600" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>View Vessel</TooltipContent>
    </Tooltip>
  );
}

export const useImportColumns = () => {
  const { t } = useTranslation();
  const columns: ColumnDef<ImportVesselProjectionDto, unknown>[] = [
    {
      accessorKey: 'docNum',
      header: t('table.docNum'),
      cell: (info) => info.getValue() ?? '-',
      enableSorting: true,
      size: 120,
    },
    {
      id: 'vessel.name',
      accessorKey: 'vessel.name',
      header: t('table.vesselName'),
      cell: (info) => info.row.original?.vessel?.name ?? '-',
      size: 200,
      minSize: 150,
      maxSize: 300,
    },
    {
      accessorKey: 'voyage',
      header: t('table.voyage'),
      cell: (info) => info.getValue() ?? '-',
      size: 100,
    },
    {
      accessorKey: 'vesselArrival',
      header: t('table.arrivalDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      accessorKey: 'vesselDeparture',
      header: t('table.departureDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      id: 'masterJetty.name',
      accessorKey: 'masterJetty.name',
      header: t('table.jetty'),
      cell: (info) => info.row.original?.masterJetty?.name ?? '-',
      size: 120,
    },
    {
      accessorKey: 'portOrigin',
      header: t('table.portOrigin'),
      cell: (info) => info.getValue() ?? '-',
      size: 180,
      minSize: 120,
      maxSize: 250,
    },
    {
      accessorKey: 'destinationPort',
      header: t('table.destinationPort'),
      cell: (info) => info.getValue() ?? '-',
      size: 200,
      minSize: 150,
      maxSize: 300,
    },
    {
      id: 'actions',
      header: t('table.actions'),
      cell: (info: CellContext<ImportVesselProjectionDto, unknown>) => {
        const id = info.row.original.id;
        if (!id) return null;
        return renderViewButton(id, 'import');
      },
      enableSorting: false,
      enableColumnFilter: false,
      size: 100,
    },
  ];
  return columns;
};

export const useExportColumns = () => {
  const { t } = useTranslation();
  const columns: ColumnDef<ExportVesselProjectionDto, unknown>[] = [
    {
      accessorKey: 'docNum',
      header: t('table.docNum'),
      cell: (info) => info.getValue() ?? '-',
      enableSorting: true,
      size: 120,
    },
    {
      id: 'vessel.name',
      accessorKey: 'vessel.name',
      header: t('table.vesselName'),
      cell: (info) => info.row.original?.vessel?.name ?? '-',
      size: 200,
      minSize: 150,
      maxSize: 300,
    },
    {
      accessorKey: 'voyage',
      header: t('table.voyage'),
      cell: (info) => info.getValue() ?? '-',
      size: 100,
    },
    {
      accessorKey: 'vesselArrival',
      header: t('table.arrivalDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      accessorKey: 'vesselDeparture',
      header: t('table.departureDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      id: 'masterJetty.name',
      accessorKey: 'masterJetty.name',
      header: t('table.jetty'),
      cell: (info) => info.row.original?.masterJetty?.name ?? '-',
      size: 120,
    },
    {
      accessorKey: 'portOrigin',
      header: t('table.portOrigin'),
      cell: (info) => info.getValue() ?? '-',
      size: 180,
      minSize: 120,
      maxSize: 250,
    },
    {
      accessorKey: 'destinationPort',
      header: t('table.destinationPort'),
      cell: (info) => info.getValue() ?? '-',
      size: 200,
      minSize: 150,
      maxSize: 300,
    },
    {
      id: 'actions',
      header: t('table.actions'),
      cell: (info: CellContext<ExportVesselProjectionDto, unknown>) => {
        const id = info.row.original.id;
        if (!id) return null;
        return renderViewButton(id, 'export');
      },
      enableSorting: false,
      enableColumnFilter: false,
      size: 100,
    },
  ];
  return columns;
};

export const useLocalColumns = () => {
  const { t } = useTranslation();
  const columns: ColumnDef<LocalVesselProjectionDto, unknown>[] = [
    {
      accessorKey: 'docNum',
      header: t('table.docNum'),
      cell: (info) => info.getValue() ?? '-',
      enableSorting: true,
      size: 120,
    },
    {
      accessorKey: 'docType',
      header: t('table.docType'),
      cell: (info) => info.getValue() ?? '-',
      enableSorting: true,
      size: 100,
    },
    {
      id: 'vessel.name',
      accessorKey: 'vessel.name',
      header: t('table.vesselName'),
      cell: (info) => info.row.original?.vessel?.name ?? '-',
      size: 200,
      minSize: 150,
      maxSize: 300,
    },
    {
      accessorKey: 'voyage',
      header: t('table.voyage'),
      cell: (info) => info.getValue() ?? '-',
      size: 100,
    },
    {
      id: 'barge.name',
      accessorKey: 'barge.name',
      header: t('table.bargeName'),
      cell: (info) => info.row.original?.barge?.name ?? '-',
      size: 180,
      minSize: 120,
      maxSize: 250,
    },
    {
      accessorKey: 'vesselArrival',
      header: t('table.arrivalDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      accessorKey: 'vesselDeparture',
      header: t('table.departureDate'),
      cell: (info) => {
        const value = info.getValue() as string | null | undefined;
        if (!value) return '-';
        const date = new Date(value);
        if (isNaN(date.getTime())) return '-';
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
      },
      size: 150,
    },
    {
      id: 'masterJetty.name',
      accessorKey: 'masterJetty.name',
      header: t('table.jetty'),
      cell: (info) => info.row.original?.masterJetty?.name ?? '-',
      size: 120,
    },
    {
      id: 'docStatus',
      accessorKey: 'docStatus',
      header: t('table.docStatus'),
      cell: (info) => renderDocStatusBadge(info.row.original?.docStatus),
      size: 120,
    },
    {
      id: 'actions',
      header: t('table.actions'),
      cell: (info: CellContext<LocalVesselProjectionDto, unknown>) => {
        const id = info.row.original.id;
        if (!id) return null;
        return renderViewButton(id, 'local');
      },
      enableSorting: false,
      enableColumnFilter: false,
      size: 100,
    },
  ];
  return columns;
}; 