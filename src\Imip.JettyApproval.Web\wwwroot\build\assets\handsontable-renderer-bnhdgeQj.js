const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/app-layout-rNt37hVL.js","assets/vendor-6tJeyfYI.js","assets/radix-e4nK4mWk.js","assets/App-DnhJzTNn.js","assets/App-DE5GgHVK.css"])))=>i.map(i=>d[i]);
import{f as H,a as _,j as e,$ as L}from"./vendor-6tJeyfYI.js";import{F as R,a as w}from"./FormField-AGj4WUYd.js";import{I as b}from"./input-DlXlkYlT.js";import{M as J}from"./multi-select-Dsa7V91B.js";import{t as q,b as U,B as M,S as G,c as Y,d as Q,e as K,f as F,h as z,i as X}from"./app-layout-rNt37hVL.js";import{u as Z}from"./useDebounce-B2N8e_3P.js";import{u as ee}from"./useJettyDataWithFilter-CK58-c0U.js";import{F as te}from"./filter-sort-bar-MpsapXP_.js";import{D as ae,a as se,b as ne,c as ie,d as re}from"./dialog-BmEXyFlW.js";import{T as le,a as oe,b as W,c as I,d as ce,e as O}from"./table-BKSoE52x.js";import{T as de}from"./TableSkeleton-CIQBoxBh.js";import{_ as $}from"./App-DnhJzTNn.js";const ue=()=>H({mutationFn:async({vesselType:t,filterGroup:s,skipCount:n=0,maxResultCount:x=50})=>{const a=await U({body:{vesselType:t,filterGroup:s,skipCount:n,maxResultCount:x}});return a?.data?.items?a.data:(q({title:"No vessels found",variant:"destructive"}),{items:[],totalCount:0})},onError:t=>{q({title:"Error loading vessel data",description:t instanceof Error?t.message:"Unknown error occurred",variant:"destructive"})}}),pe=[{value:"docNum",label:"Document Number"},{value:"vesselName",label:"Vessel Name"},{value:"voyage",label:"Voyage"},{value:"arrival",label:"Arrival Date"},{value:"departure",label:"Departure Date"}],me=[{value:"Equals",label:"Equals"},{value:"Contains",label:"Contains"},{value:"NotEquals",label:"Not Equals"},{value:"GreaterThan",label:">"},{value:"LessThan",label:"<"}],xe=({vesselType:t,selectedVessel:s,onVesselSelect:n,trigger:x,open:i,onOpenChange:a})=>{const l=typeof i=="boolean"&&typeof a=="function",[o,u]=_.useState(!1),h=l?i:o,y=l?a:u,[v,D]=_.useState([]),[p,r]=_.useState([]),[c,f]=_.useState(null),S=_.useMemo(()=>{if(v.length)return{operator:"And",conditions:v}},[v]),{mutate:C,data:E,isPending:T,error:N}=ue();_.useEffect(()=>{t&&C({vesselType:t,filterGroup:S})},[t,S,C]);const j=E?.items??[],A=E?.totalCount??0,k=()=>{c!==null&&j[c]?n(j[c]):n(null),y(!1)},V=d=>{f(d)},g=e.jsx(M,{variant:"outline",className:"w-full justify-start font-normal",children:s?s.vesselName:"Select Vessel"});return e.jsxs(ae,{open:h,onOpenChange:y,children:[x!==null&&e.jsx(se,{asChild:!0,children:x||g}),e.jsxs(ne,{className:"min-w-[1000px] w-auto max-h-[80vh] flex flex-col",children:[e.jsx(ie,{children:e.jsxs(re,{children:["Select Vessel ",t&&`(${t})`]})}),T?e.jsx(de,{rowCount:10,columnCount:4,hasTitle:!0,hasSearch:!0,hasFilters:!0,hasPagination:!0,hasActions:!0}):N?e.jsx("div",{className:"flex items-center justify-center py-8 text-destructive",children:"Error loading vessels"}):j.length===0?e.jsx("div",{className:"flex items-center justify-center py-8 text-muted-foreground",children:t?`No vessels found for ${t}`:"Please select a vessel type first"}):e.jsxs(e.Fragment,{children:[e.jsx(te,{filterFields:pe,operators:me,filters:v,sorts:p,onFiltersChange:D,onSortsChange:r}),e.jsx("div",{className:"flex-grow overflow-hidden",children:e.jsx("div",{className:"relative overflow-hidden overflow-x-auto",children:e.jsxs(le,{children:[e.jsx(oe,{children:e.jsxs(W,{className:"border-y border-gray-200 dark:border-gray-800",children:[e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs w-12",children:"Select"}),e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs",children:"Document Number"}),e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs",children:"Vessel Name"}),e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs",children:"Voyage"}),e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs",children:"Arrival Date"}),e.jsx(I,{className:"whitespace-nowrap py-1 text-sm sm:text-xs",children:"Departure Date"})]})}),e.jsx(ce,{children:j.map((d,P)=>e.jsxs(W,{className:"group select-none hover:bg-gray-50 dark:hover:bg-gray-900 cursor-pointer",onClick:()=>V(P),children:[e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400 w-12",children:e.jsx("input",{type:"radio",name:"vessel-selection",checked:c===P,onChange:()=>V(P),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"})}),e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400",children:d.docEntry??"-"}),e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400",children:d.vesselName??"-"}),e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400",children:d.voyage??"-"}),e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400",children:d.vesselArrival?new Date(d.vesselArrival).toLocaleDateString():"-"}),e.jsx(O,{className:"relative whitespace-nowrap py-1 text-gray-600 dark:text-gray-400",children:d.vesselDeparture?new Date(d.vesselDeparture).toLocaleDateString():"-"})]},d.id||P))})]})})}),e.jsxs("div",{className:"flex items-center justify-between mt-4",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:A>0&&`Showing ${j.length} of ${A} vessels`}),e.jsx(M,{onClick:k,disabled:c===null,children:"Select Vessel"})]})]})]})]})},ge=({value:t,onValueChange:s,placeholder:n="Select jetty...",className:x,disabled:i=!1})=>{const[a,l]=L.useState(""),[o,u]=L.useState([]),h=Z(a,300),{mutate:y,isPending:v}=ee();L.useEffect(()=>{y({maxResultCount:50,skipCount:0,filterGroup:h?{operator:"Or",conditions:[{fieldName:"name",operator:"Contains",value:h},{fieldName:"alias",operator:"Contains",value:h},{fieldName:"port",operator:"Contains",value:h}]}:void 0},{onSuccess:r=>{const c=r.map(f=>({value:f.id||"",label:f.name||f.alias||"Unknown Jetty",description:f.port?`Port: ${f.port}`:void 0,data:f}));u(c)}})},[h,y]),L.useEffect(()=>{t&&!o.find(p=>p.value===t)&&y({maxResultCount:1,skipCount:0,filterGroup:{operator:"And",conditions:[{fieldName:"id",operator:"Equals",value:t}]}},{onSuccess:r=>{if(r.length>0){const c={value:r[0].id||"",label:r[0].name||r[0].alias||"Unknown Jetty",description:r[0].port?`Port: ${r[0].port}`:void 0,data:r[0]};u(f=>f.find(C=>C.value===c.value)?f:[c,...f])}}})},[t,o,y]);const D=p=>{const r=p[0]||"";s(r)};return L.useEffect(()=>{},[t]),e.jsx(J,{options:o,value:t?[t]:[],onChange:D,placeholder:n,className:x,disabled:i,mode:"single",searchValue:a,onSearchValueChange:l,isLoading:v,loadingText:"Loading jetties...",emptyText:"No jetties found",showDescription:!0})},_e=({docNum:t,vesselType:s,vessel:n,voyage:x,jetty:i,arrivalDate:a,departureDate:l,asideDate:o,castOfDate:u,postDate:h,portOrigin:y,destinationPort:v,barge:D,onDocNumChange:p,onVesselTypeChange:r,onVesselChange:c,onVoyageChange:f,onJettyChange:S,onArrivalDateChange:C,onDepartureDateChange:E,onAsideDateChange:T,onCastOfDateChange:N,onPostDateChange:j,onPortOriginChange:A,onDestinationPortChange:k,onBargeChange:V,errors:g={},register:d,title:P="Create Application"})=>{const B=m=>{c(m),m?.jetty?.id?S(m.jetty.id):S("")};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-lg font-bold text-gray-800 dark:text-white",children:P}),e.jsx("div",{className:"h-1 w-16 bg-primary rounded mt-2 mb-4"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 md:grid-cols-2 gap-6 mb-2",children:[e.jsxs(R,{children:[e.jsxs(w,{label:"DocNum",labelWidth:"100px",children:[d?e.jsx(b,{id:"docNum",...d("docNum")}):e.jsx(b,{id:"docNum",value:t,onChange:m=>p(m.target.value)}),g.docNum&&e.jsx("span",{className:"text-red-500 text-xs",children:g.docNum.message})]}),e.jsxs(w,{label:"Vessel Type",labelWidth:"100px",children:[e.jsxs(G,{value:s,onValueChange:r,children:[e.jsx(Y,{className:"w-full",children:e.jsx(Q,{placeholder:"Select Vessel Type"})}),e.jsxs(K,{children:[e.jsx(F,{value:"Import",children:"Import"}),e.jsx(F,{value:"Export",children:"Export"}),e.jsx(F,{value:"LocalIn",children:"Local In"}),e.jsx(F,{value:"LocalOut",children:"Local Out"})]})]}),g.vesselType&&e.jsx("span",{className:"text-red-500 text-xs",children:g.vesselType.message})]}),e.jsxs(w,{label:"Vessel",labelWidth:"100px",children:[e.jsx(xe,{vesselType:s,selectedVessel:n,onVesselSelect:B}),g.vesselName&&e.jsx("span",{className:"text-red-500 text-xs",children:g.vesselName.message})]}),e.jsxs(w,{label:"Voyage",labelWidth:"100px",children:[d?e.jsx(b,{id:"voyage",...d("voyage")}):e.jsx(b,{id:"voyage",value:x,onChange:m=>f(m.target.value)}),g.voyage&&e.jsx("span",{className:"text-red-500 text-xs",children:g.voyage.message})]}),e.jsxs(w,{label:"Jetty",labelWidth:"100px",children:[e.jsx(ge,{value:i,onValueChange:S,placeholder:"Select Jetty"}),g.jetty&&e.jsx("span",{className:"text-red-500 text-xs",children:g.jetty.message})]})]}),e.jsxs(R,{children:[e.jsxs(w,{label:"Arrival Date",labelWidth:"100px",children:[d?e.jsx(b,{id:"arrivalDate",type:"datetime-local",...d("arrivalDate")}):e.jsx(b,{id:"arrivalDate",type:"datetime-local",value:a,onChange:m=>C(m.target.value)}),g.arrivalDate&&e.jsx("span",{className:"text-red-500 text-xs",children:g.arrivalDate.message})]}),e.jsxs(w,{label:"Departure Date",labelWidth:"100px",children:[d?e.jsx(b,{id:"departureDate",type:"datetime-local",...d("departureDate")}):e.jsx(b,{id:"departureDate",type:"datetime-local",value:l,onChange:m=>E(m.target.value)}),g.departureDate&&e.jsx("span",{className:"text-red-500 text-xs",children:g.departureDate.message})]}),e.jsx(w,{label:"A/Side Date",labelWidth:"100px",children:e.jsx(b,{id:"asideDate",type:"datetime-local",value:o,onChange:m=>T(m.target.value)})}),e.jsx(w,{label:"Cast Of Date",labelWidth:"100px",children:e.jsx(b,{id:"castOfDate",type:"datetime-local",value:u,onChange:m=>N(m.target.value)})}),e.jsx(w,{label:"Posting Date",labelWidth:"100px",children:e.jsx(b,{id:"postingDate",type:"date",value:h,onChange:m=>j(m.target.value)})})]}),e.jsxs(R,{children:[e.jsxs(w,{label:"Port Origin",labelWidth:"100px",children:[d?e.jsx(b,{id:"portOrigin",...d("portOrigin")}):e.jsx(b,{id:"portOrigin",value:y,onChange:m=>A(m.target.value)}),g.portOrigin&&e.jsx("span",{className:"text-red-500 text-xs",children:g.portOrigin.message})]}),e.jsxs(w,{label:"Destination Port",labelWidth:"100px",children:[d?e.jsx(b,{id:"destinationPort",...d("destinationPort")}):e.jsx(b,{id:"destinationPort",value:v,onChange:m=>k(m.target.value)}),g.destinationPort&&e.jsx("span",{className:"text-red-500 text-xs",children:g.destinationPort.message})]}),e.jsxs(w,{label:"Barge",labelWidth:"100px",children:[d?e.jsx(b,{id:"barge",...d("barge")}):e.jsx(b,{id:"barge",value:D,onChange:m=>V(m.target.value)}),g.barge&&e.jsx("span",{className:"text-red-500 text-xs",children:g.barge.message})]})]})]})]})};function Ie(t){if(!t)return"";const s=new Date(t);if(isNaN(s.getTime()))return"";const n=x=>x.toString().padStart(2,"0");return`${s.getFullYear()}-${n(s.getMonth()+1)}-${n(s.getDate())}T${n(s.getHours())}:${n(s.getMinutes())}`}const Oe=[{data:"tenantName",type:"text",title:"Tenant",width:100},{data:"itemName",type:"text",title:"Item Name",width:200,wordWrap:!1},{data:"quantity",type:"numeric",title:"Quantity",width:80},{data:"uom",type:"text",title:"UoM",width:120},{data:"remark",type:"text",title:"Remark",width:120},{data:"status",type:"text",title:"Status",width:80,readOnly:!0},{data:"letterNo",type:"text",title:"Letter No",width:120},{data:"letterDate",type:"date",title:"Letter Date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0,datePickerConfig:{firstDay:0,showWeekNumber:!0,numberOfMonths:1}},{data:"id",title:"Preview",width:80,readOnly:!0,filterable:!1},{data:"submit",title:"Submit",width:80,readOnly:!0,filterable:!1},{data:"delete",title:"Delete",width:80,readOnly:!0,filterable:!1}];async function he(t,s,n){try{n(!0);const i=await z({body:{jettyRequestItemId:t,generatePdf:!0}});if(i.data){const a=i.data;a.streamUrl?s(a.streamUrl):alert("Failed to generate document. Please try again.")}else alert("Failed to generate document. Please try again.")}catch{alert("Error generating document. Please try again.")}finally{n(!1)}}const ke=(t,s,n,x)=>(i,a,l,o,u,h,y)=>{const v=t[l];if(v&&v.status==="Draft"){a.innerHTML="";return}const D=v?.id,p=n.get(l)||!1,C=`<button class="${D&&!p?"px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed"}" data-row="${l}" data-action="preview" title="${D?p?"Generating document...":"Preview document":"Save the application first to enable preview"}" ${D&&!p?"":"disabled"}>${p?"Loading...":"Preview"}</button>`;a.innerHTML=C;const E=a.querySelector('[data-action="preview"]');E&&D&&!p&&E.addEventListener("click",async()=>{const T=t[l];T?.id&&await he(T.id,s,N=>x(l,N))})};async function fe(t,s,n,x,i){try{n(!0);const{postApiIdjasApprovalSubmit:a}=await $(async()=>{const{postApiIdjasApprovalSubmit:h}=await import("./app-layout-rNt37hVL.js").then(y=>y.aa);return{postApiIdjasApprovalSubmit:h}},__vite__mapDeps([0,1,2,3,4])),{toast:l}=await $(async()=>{const{toast:h}=await import("./app-layout-rNt37hVL.js").then(y=>y.ab);return{toast:h}},__vite__mapDeps([0,1,2,3,4]));(await a({body:{documentId:t,documentType:s,notes:"Submitted for approval"}})).data?(l({title:"Success",description:"Successfully submitted for approval",variant:"default"}),await x.refetchQueries({queryKey:["jetty-request",i]})):l({title:"Error",description:"Failed to submit for approval",variant:"destructive"})}catch(a){const{toast:l}=await $(async()=>{const{toast:u}=await import("./app-layout-rNt37hVL.js").then(h=>h.ab);return{toast:u}},__vite__mapDeps([0,1,2,3,4]));let o="Error submitting for approval. Please try again.";typeof a=="object"&&a&&"message"in a&&typeof a.message=="string"&&(o=a.message??o),l({title:"Error submitting for approval",description:o,variant:"destructive"})}finally{n(!1)}}const Le=(t,s,n,x,i,a)=>(l,o,u,h,y,v,D)=>{const p=t[u];if(p&&p.status==="Draft"){o.innerHTML="";return}const r=p?.id,c=n.get(u)||!1,T=`<button class="${r&&!c?"px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed"}" data-row="${u}" data-action="submit" title="${r?c?"Submitting for approval...":"Submit for approval":"Save the application first to enable submit"}" ${r&&!c?"":"disabled"}>${c?"Submitting...":"Submit"}</button>`;o.innerHTML=T;const N=o.querySelector('[data-action="submit"]');N&&r&&!c&&N.addEventListener("click",async()=>{const j=t[u];j?.id&&await fe(j.id,s,A=>x(u,A),i,a)})};async function be(t,s,n,x){try{s(!0);const i=await X({path:{id:t}});if(i.error)throw i.error;const{toast:a}=await $(async()=>{const{toast:l}=await import("./app-layout-rNt37hVL.js").then(o=>o.ab);return{toast:l}},__vite__mapDeps([0,1,2,3,4]));a({title:"Success",description:"Item deleted successfully",variant:"default"}),await n.refetchQueries({queryKey:["jetty-request",x]})}catch(i){const{toast:a}=await $(async()=>{const{toast:o}=await import("./app-layout-rNt37hVL.js").then(u=>u.ab);return{toast:o}},__vite__mapDeps([0,1,2,3,4]));let l="Error deleting item. Please try again.";typeof i=="object"&&i&&"message"in i&&typeof i.message=="string"&&(l=i.message??l),a({title:"Error deleting item",description:l,variant:"destructive"})}finally{s(!1)}}const $e=(t,s,n,x,i,a)=>(l,o,u,h,y,v,D)=>{const p=t[u];if(p&&p.status==="Draft"){o.innerHTML="";return}const r=p?.id,c=n?.get(u)||!1,T=`<button class="${r&&!c?"px-2 py-0.5 bg-red-500 text-white rounded-md text-xs hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed"}" data-row="${u}" data-action="delete" title="${r?c?"Deleting item...":"Delete item from database":"Save the application first to enable delete"}" ${r&&!c?"":"disabled"}>${c?"Deleting...":"Delete"}</button>`;o.innerHTML=T;const N=o.querySelector('[data-action="delete"]');N&&r&&!c&&N.addEventListener("click",async()=>{const j=t[u];j?.id&&window.confirm("Are you sure you want to delete this item? This action cannot be undone.")&&await be(j.id,k=>x?.(u,k),i,a)})};export{_e as A,Le as a,$e as b,Oe as c,Ie as f,ke as r};
//# sourceMappingURL=handsontable-renderer-bnhdgeQj.js.map
