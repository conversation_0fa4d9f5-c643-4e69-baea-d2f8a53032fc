# SkiaSharp Compatibility Fix for Kubernetes Deployment

## Problem
The application was failing with the following error in Kubernetes:
```
System.TypeInitializationException: The type initializer for 'SkiaSharp.SKObject' threw an exception.
System.InvalidOperationException: The version of the native libSkiaSharp library (119.0) is incompatible with this version of SkiaSharp. Supported versions of the native libSkiaSharp library are in the range [116.0, 117.0).
```

## Root Cause
Version mismatch between SkiaSharp managed library and native library:
- **Native library version**: 119.0
- **Supported range**: 116.0-117.0
- **Current managed library**: Only supports 116.0-117.0

## Solution Applied

### 1. Updated Package References
**File**: `src/Imip.JettyApproval.Application/Imip.JettyApproval.Application.csproj`

**Changes**:
```xml
<!-- Before -->
<PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.119.0" />

<!-- After -->
<PackageReference Include="HarfBuzzSharp" Version="*******" />
<PackageReference Include="SkiaSharp" Version="3.116.0" />
<PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="3.116.0" />
```

### 2. Updated Kubernetes Configuration
**Files**: 
- `k8s/dev/web-deployment.yaml`
- `k8s/prod/web-deployment.yaml`

**Changes**: Added `skiasharp-config` ConfigMap to `envFrom` section:
```yaml
envFrom:
  - configMapRef:
      name: imip-idjas-config
  - configMapRef:
      name: skiasharp-config  # Added this line
  - secretRef:
      name: imip-idjas-secrets
```

### 3. SkiaSharp Environment Configuration
The existing `skiasharp-config.yaml` files already contain proper environment variables:
- `LD_LIBRARY_PATH`
- `SKIASHARP_DEBUG`
- `SKIASHARP_PRELOAD_NATIVE`
- `SKIASHARP_NATIVE_SEARCH_PATHS`

## Deployment Steps

### 1. Build the Application
```bash
# Clean and restore packages
dotnet clean
dotnet restore

# Build with Release configuration
dotnet build --configuration Release

# Publish the application
dotnet publish --configuration Release --no-build --output ./publish
```

### 2. Build and Push Docker Image
```bash
# Build Docker image
docker build -t your-registry/imip-idjas-web:latest .

# Push to registry
docker push your-registry/imip-idjas-web:latest
```

### 3. Deploy to Kubernetes

#### Development Environment
```bash
# Apply namespaces
kubectl apply -f k8s/dev/namespaces.yaml

# Apply SkiaSharp configuration
kubectl apply -f k8s/dev/skiasharp-config.yaml

# Apply all other configurations
kubectl apply -f k8s/dev/
```

#### Production Environment
```bash
# Apply namespaces
kubectl apply -f k8s/prod/namespaces.yaml

# Apply SkiaSharp configuration
kubectl apply -f k8s/prod/skiasharp-config.yaml

# Apply all other configurations
kubectl apply -f k8s/prod/
```

### 4. Verify Deployment
```bash
# Check pod status
kubectl get pods -n imip-idjas-dev

# Check logs for SkiaSharp-related errors
kubectl logs -n imip-idjas-dev deployment/imip-idjas-web

# Test PDF generation functionality
```

## Key Changes Summary

1. **SkiaSharp Version**: Downgraded from 3.119.0 to 3.116.0 for compatibility
2. **Added HarfBuzzSharp**: Ensures complete font rendering support
3. **Kubernetes Config**: Added SkiaSharp ConfigMap to deployment
4. **Environment Variables**: Properly configured for Linux container environment

## Verification

After deployment, test the PDF generation functionality:
1. Create a new application
2. Submit for approval
3. Generate PDF document
4. Verify no SkiaSharp errors in logs

## Rollback Plan

If issues occur, you can rollback by:
1. Reverting the package versions in `Imip.JettyApproval.Application.csproj`
2. Removing the `skiasharp-config` ConfigMap reference from deployments
3. Redeploying with the previous image

## Notes

- The fix ensures compatibility between managed and native SkiaSharp libraries
- All existing functionality should work as expected
- The change only affects the PDF generation component
- No database changes required 