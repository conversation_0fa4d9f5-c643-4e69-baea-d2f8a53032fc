const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/app-layout-rNt37hVL.js","assets/vendor-6tJeyfYI.js","assets/radix-e4nK4mWk.js","assets/App-DnhJzTNn.js","assets/App-DE5GgHVK.css","assets/sdk.gen-BHIMY_5K.js"])))=>i.map(i=>d[i]);
import{h as X,j as e,a as x}from"./vendor-6tJeyfYI.js";import{u as De,S as we,c as Ne,d as Ie,e as Pe,f as ce,B as G,O as Ae,M as H}from"./app-layout-rNt37hVL.js";import{D as Te}from"./DocumentPreviewDialog-C1lfiEeE.js";import{F as Z,a as S}from"./FormField-AGj4WUYd.js";import{I as O}from"./input-DlXlkYlT.js";import{H as Oe,r as Ce}from"./ht-theme-main.min-DuylQxQp.js";import{o as pe,s,n as Ee,V as de,J as Le,P as Fe,D as Ve,a as _e}from"./types-B5GxFm4f.js";/* empty css                         */import{u as Be,F as We,C as B}from"./index.esm-BubGICDC.js";import{_ as W}from"./App-DnhJzTNn.js";async function qe(t,n,d,r,m){try{d(!0);const l={generatePdf:!0,documentData:{tenantName:t.tenant||"",itemName:t.itemName||"",qty:t.itemQty||0,uoM:t.unitQty||"",notes:t.remarks||"",status:t.status||"",letterNo:t.letterNo||"",letterDate:t.letterDate?t.letterDate:null,docNum:Number(r?.docNum)||0,vesselName:m?.vessel?.name||r?.vesselId||"",voyage:r?.voyage||"",jetty:m?.masterJetty?.name||r?.jettyId||"",arrivalDate:r?.vesselArrival||null,departureDate:r?.vesselDeparture||null,portOrigin:m?.masterPortOrigin?.name||r?.portOriginId||"",destinationPort:m?.masterDestinationPort?.name||r?.destinationPortId||"",generatedDate:new Date().toISOString(),generatedBy:""}},{postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment:p}=await W(async()=>{const{postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment:g}=await import("./app-layout-rNt37hVL.js").then(h=>h.aa);return{postApiIdjasApplicationDocumentGenerateApplicationDocumentWithPayloadAsAttachment:g}},__vite__mapDeps([0,1,2,3,4])),o=await p({body:l});if(o.data){const g=o.data;g.streamUrl?n(g.streamUrl):alert("Failed to generate document. Please try again.")}else alert("Failed to generate document. Please try again.")}catch{alert("Error generating document. Please try again.")}finally{d(!1)}}async function Re(t,n,d,r,m,i){try{d(!0);const{postApiIdjasApprovalSubmit:l}=await W(async()=>{const{postApiIdjasApprovalSubmit:h}=await import("./app-layout-rNt37hVL.js").then(b=>b.aa);return{postApiIdjasApprovalSubmit:h}},__vite__mapDeps([0,1,2,3,4])),{toast:p}=await W(async()=>{const{toast:h}=await import("./app-layout-rNt37hVL.js").then(b=>b.ab);return{toast:h}},__vite__mapDeps([0,1,2,3,4]));if((await l({body:{documentId:t,documentType:n,notes:"Submitted for approval"}})).data){p({title:"Success",description:"Successfully submitted for approval",variant:"default"});try{const{putApiEkbBoundedZoneById:h}=await W(async()=>{const{putApiEkbBoundedZoneById:b}=await import("./sdk.gen-BHIMY_5K.js");return{putApiEkbBoundedZoneById:b}},__vite__mapDeps([5,3,1,4]));await h({path:{id:t},body:{...i,tenantId:i.tenantId??"",concurrencyStamp:i.concurrencyStamp,headerId:i.headerId??i.headerId??"",docNum:i.docNum??i.docNum??0,agentId:i.agentId??i.agentId??"",status:"Pending"}}),await r.refetchQueries({queryKey:["local-vessel"]}),p({title:"Status Updated",description:"Status updated to Pending",variant:"default"})}catch{p({title:"Status Update Failed",description:"Failed to update status to Pending",variant:"destructive"})}await r.refetchQueries({queryKey:["local-vessel",m]})}else p({title:"Error",description:"Failed to submit for approval",variant:"destructive"})}catch(l){const{toast:p}=await W(async()=>{const{toast:g}=await import("./app-layout-rNt37hVL.js").then(h=>h.ab);return{toast:g}},__vite__mapDeps([0,1,2,3,4]));let o="Error submitting for approval. Please try again.";typeof l=="object"&&l&&"message"in l&&typeof l.message=="string"&&(o=l.message??o),p({title:"Error submitting for approval",description:o,variant:"destructive"})}finally{d(!1)}}const Me=(t,n,d,r,m,i)=>(l,p,o,g,h,b,j)=>{const y=t[o]?.id,v=d.get(o)||!1,P=`<button class="${y&&!v?"px-2 py-0.5 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed"}" data-row="${o}" data-action="preview" title="${y?v?"Generating document...":"Preview document":"Save the application first to enable preview"}" ${y&&!v?"":"disabled"}>${v?"Loading...":"Preview"}</button>`;p.innerHTML=P;const A=p.querySelector('[data-action="preview"]');A&&y&&!v&&A.addEventListener("click",async()=>{const I=t[o];I?.id&&await qe(I,n,L=>r(o,L),m,i)})},Ye=(t,n,d,r,m,i)=>(l,p,o,g,h,b,j)=>{const y=t[o]?.id,v=d.get(o)||!1,P=`<button class="${y&&!v?"px-2 py-0.5 bg-green-500 text-white rounded-md text-xs hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed"}" data-row="${o}" data-action="submit" title="${y?v?"Submitting for approval...":"Submit for approval":"Save the application first to enable submit"}" ${y&&!v?"":"disabled"}>${v?"Submitting...":"Submit"}</button>`;p.innerHTML=P;const A=p.querySelector('[data-action="submit"]');A&&y&&!v&&A.addEventListener("click",async()=>{const I=t[o];I?.id&&await Re(I.id,n,L=>r(o,L),m,i,I)})},ke=t=>{if(!t)throw new Error("queryClient is required for renderAttachmentButton");return(n,d,r,m,i,l,p)=>{if(d.innerHTML="",!n)return;const o=n.getDataAtRowProp(r,"attachments")||[],g=n.getDataAtRowProp(r,"itemName")||"",h=o.length>0?"px-2 py-0.5 bg-emerald-500 text-white rounded-md text-xs hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-opacity-50":"px-2 py-0.5 bg-gray-300 text-gray-500 rounded-md text-xs cursor-not-allowed",b=o.length>0?`View Attachments (${o.length})`:"No attachments",j=o.length>0?"":"disabled",C=`<button class="${h}" data-row="${r}" data-action="attachment" title="${b}" ${j}>Attachment (${o.length})</button>`;d.innerHTML=C;const y=d.querySelector('[data-action="attachment"]');y&&o.length>0&&y.addEventListener("click",()=>{window.alert(`Show attachments for ${g} (implement dialog logic here)`)})}},ee=(t,n,d,r=[],m,i,l,p,o,g)=>{const h=t.map(j=>j.name??"").filter(j=>j!==""),b=n.map(j=>j.name??"").filter(j=>j!=="");return[{data:"id",title:"Id",type:"text",width:200},{data:"docEntry",title:"DocEntry",type:"text",width:200},{data:"concurrencyStamp",title:"concurrencyStamp",type:"text",width:200},{data:"tenant",title:"Tenant",type:"autocomplete",width:140,source:h,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"businessPartner",title:"Business Partner",type:"autocomplete",width:250,source:b,strict:!1,allowInvalid:!1,trimDropdown:!1,visibleRows:6},{data:"itemName",title:"Item Name",type:"text",width:200},{data:"itemQty",title:"Quantity",type:"numeric",width:80},{data:"unitQty",title:"UOM",type:"text",width:100},{data:"remarks",title:"Remark",type:"text",width:120},{data:"letterNo",title:"Letter No",type:"text",width:120},{data:"letterDate",title:"Letter Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"noBl",title:"No BL",type:"text",width:120},{data:"dateBl",title:"Date BL",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"ajuNo",title:"AJU No",type:"text",width:120},{data:"regNo",title:"Reg No",type:"text",width:120},{data:"regDate",title:"Reg Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppbNo",title:"SPPB No",type:"text",width:120},{data:"sppbDate",title:"SPPB Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"sppdNo",title:"SPPD No",type:"text",width:120},{data:"sppdDate",title:"SPPD Date",type:"date",dateFormat:"YYYY-MM-DD",width:120,correctFormat:!0},{data:"grossWeight",title:"Gross Weight",type:"numeric",width:100},{data:"unitWeight",title:"Unit Weight",type:"text",width:100},{data:"status",title:"Status",type:"text",width:100,readOnly:!0},{data:"attachments",title:"Attachment",width:130,renderer:ke(d),readOnly:!0,filterable:!1},{data:"preview",title:"Preview",width:100,renderer:m&&i&&l?Me(r,m,i,l,o,g):void 0,readOnly:!0,filterable:!1},{data:"submit",title:"Submit",width:100,renderer:Ye(r,"Local",i,l,d,p||""),readOnly:!0,filterable:!1}]};ee([],[],{});const $e=pe({docNum:s().min(1,"DocNum is required"),vesselId:s().min(1,"Vessel Name is required"),voyage:s().min(1,"Voyage is required"),postingDate:s().min(1,"Posting Date is required"),vesselArrival:s().min(1,"Arrival Date is required"),vesselDeparture:s().min(1,"Departure Date is required"),portOriginId:s().optional(),destinationPortId:s().optional(),deleted:s().optional(),docType:s().optional(),isChange:s().optional(),isLocked:s().optional(),createdBy:s().optional(),docStatus:s().optional(),statusBms:s().optional(),transType:s().min(1,"TransType is required"),concurrencyStamp:s().optional(),jettyId:s().min(1,"Jetty is required"),status:s().optional(),vesselType:s().optional(),shipment:s().optional(),portOrigin:s().optional(),destinationPort:s().optional(),bargeId:s().optional(),asideDate:s().optional(),castOfDate:s().optional()}),Qe=pe({id:s().optional(),itemName:s().nullable().optional(),itemQty:Ee().nullable().optional(),unitQty:s().nullable().optional(),remarks:s().nullable().optional(),tenant:s().nullable().optional(),tenantId:s().nullable().optional(),businessPartner:s().nullable().optional(),businessPartnerId:s().nullable().optional(),letterNo:s().nullable().optional(),letterDate:s().nullable().optional(),status:s().nullable().optional(),concurrencyStamp:s().nullable().optional()});Ce();function ue(t){const n=r=>r?r.slice(0,10):"",d=()=>new Date().toISOString().slice(0,10);return{docNum:t.docNum??"",vesselId:t.vesselId??"",voyage:t.voyage??"",postingDate:t.postingDate??(n(t.vesselArrival)||d()),vesselArrival:t.vesselArrival??"",vesselDeparture:t.vesselDeparture??"",transType:t.transType??"",portOriginId:t.portOriginId??"",destinationPortId:t.destinationPortId??"",jettyId:t.jettyId??"",bargeId:t.bargeId??"",asideDate:t.asideDate??"",castOfDate:t.castOfDate??"",...t.concurrencyStamp?{concurrencyStamp:t.concurrencyStamp}:{}}}const rt=t=>{const{data:n=[],isLoading:d}=X({queryKey:["tenants"],queryFn:()=>H.filterTenants({page:1,maxResultCount:1e3}).then(i=>i.data?.items??[])}),{data:r=[],isLoading:m}=X({queryKey:["businessPartners"],queryFn:()=>H.filterBusinessPartners({page:1,maxResultCount:1e4}).then(i=>i.data?.items??[])});return d||m?e.jsx("div",{children:"Loading data..."}):e.jsx(Ue,{...t,columns:ee(n,r,t.queryClient),tenants:n,businessPartners:r,queryClient:t.queryClient,vesselData:t.vesselData,jettyList:t.jettyList})},Ue=({mode:t,initialHeader:n,initialItems:d,onSubmit:r,headerSchema:m=$e,itemSchema:i=Qe,isSubmitting:l=!1,title:p="Create Local Vessel",tenants:o,businessPartners:g,queryClient:h,vesselData:b,showAddLineButton:j=!0,jettyList:C})=>{const[y,v]=x.useState(d),[q,Y]=x.useState([]),[k,$]=x.useState(new Map),P=x.useRef(null),[A,I]=x.useState(!1),[L,me]=x.useState(""),[R,he]=x.useState(""),[te,ae]=x.useState(null),[se,ne]=x.useState(!1),re=x.useRef(null),f=Be({resolver:_e(m),defaultValues:{...ue(n),docNum:t==="create"?R:n.docNum??""},mode:"onBlur"}),{register:T,handleSubmit:ge,formState:{errors:c},reset:oe,setValue:F}=f,Q=f.watch("vesselArrival")||new Date().toISOString().slice(0,10),{data:U}=X({queryKey:["generateDocNum",Q,t],queryFn:()=>H.generateNextLocalVesselDocNum(Q).then(a=>String(a.data??"")),enabled:t==="create"&&!!Q});x.useEffect(()=>{t==="create"&&U&&he(U)},[U,t]),x.useEffect(()=>{t==="create"&&R&&F("docNum",R)},[R,t,F]);const ye=a=>{me(a),I(!0)},xe=(a,u)=>{$(D=>{const N=new Map(D);return u?N.set(a,!0):N.delete(a),N})},ie=x.useMemo(()=>C.find(a=>a.id===f.watch("jettyId")),[C,f]),be=async()=>{const a=n.id||b?.id||"";if(!a){J({title:"Error",description:"Document ID is missing.",variant:"destructive"});return}ne(!0);try{await Ae({body:{documentId:a,documentType:"Local",notes:""}}),J({title:"Success",description:"Document submitted for approval.",variant:"success"});const u=P.current?.hotInstance?.getSourceData?.()??[],N={...f.getValues(),docStatus:"Waiting"};await r(N,u)}catch(u){let D="Failed to submit for approval.";typeof u=="object"&&u&&"message"in u&&typeof u.message=="string"&&(D=u.message),J({title:"Error",description:D,variant:"destructive"})}finally{ne(!1)}},le=ee(o,g,h,y,ye,k,xe,String(n.id||""),f.getValues(),b);x.useEffect(()=>{const a=n.id??n.docNum;t==="edit"&&a&&re.current!==a&&(oe(ue(n)),v(d.map(u=>({...u,concurrencyStamp:u.concurrencyStamp}))),re.current=a)},[n,d,t,oe]),x.useEffect(()=>{n.concurrencyStamp&&F("concurrencyStamp",n.concurrencyStamp)},[n.concurrencyStamp,F]);const ve=a=>{const u=[];return a.forEach((D,N)=>{const V=i.safeParse(D);V.success?u[N]="":u[N]=Object.values(V.error.flatten().fieldErrors).flat().join(", ")}),Y(u),u.every(D=>!D)},fe=async a=>{const u=P.current?.hotInstance?.getSourceData?.()??[],D={deleted:"",shipment:"",statusBms:"",transType:"",portOrigin:"",vesselType:"",destinationPort:""},N={...D,...a,concurrencyStamp:a.concurrencyStamp},V=u.map(w=>{const{concurrencyStamp:M,...Se}=w,E={...D,...Se,concurrencyStamp:M??void 0};if(w.tenant){const _=o.find(z=>z.name===w.tenant);E.tenantId=_?.id||""}else E.tenantId="";if(w.businessPartner){const _=g.find(z=>z.name===w.businessPartner);E.businessPartnerId=_?.id||""}else E.businessPartnerId="";const K=d.find(_=>_.id===w.id);return K&&K.concurrencyStamp&&(E.concurrencyStamp=K.concurrencyStamp),E});if(ve(V)){ae(null);try{await r(N,V)}catch(w){let M="An error occurred";typeof w=="object"&&w&&"message"in w&&typeof w.message=="string"&&(M=w.message),ae(M)}}},je=()=>{v(a=>[...a,{}])},{toast:J}=De();return e.jsxs("div",{className:"w-full mx-auto",children:[e.jsxs("div",{className:"bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold text-gray-800 dark:text-white",children:p}),e.jsx("div",{className:"h-1 w-16 bg-primary rounded mt-2 mb-4"})]}),n.docStatus&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:"Status:"}),e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:n.docStatus})]})]})}),e.jsx(We,{...f,children:e.jsxs("form",{onSubmit:ge(fe),className:"space-y-6",children:[te&&e.jsx("div",{className:"text-red-500 text-sm mb-2",children:te}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(Z,{showDivider:!1,children:[e.jsxs(S,{label:"DocNum",labelWidth:"100px",children:[e.jsx(O,{...T("docNum"),value:f.watch("docNum"),readOnly:t==="create",disabled:t==="edit"}),c.docNum&&e.jsx("span",{className:"text-red-500 text-xs",children:c.docNum.message})]}),e.jsxs(S,{label:"TransType",labelWidth:"100px",children:[e.jsxs(we,{value:f.watch("transType")??"",onValueChange:a=>F("transType",a),children:[e.jsx(Ne,{size:"sm",className:"w-full",children:e.jsx(Ie,{placeholder:"Select TransType"})}),e.jsxs(Pe,{children:[e.jsx(ce,{value:"IN",children:"IN"}),e.jsx(ce,{value:"OUT",children:"OUT"})]})]}),c.transType&&e.jsx("span",{className:"text-red-500 text-xs",children:c.transType.message})]}),e.jsxs(S,{label:"Vessel Name",labelWidth:"100px",children:[e.jsx(B,{name:"vesselId",control:f.control,render:({field:a})=>e.jsx(de,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select vessel...",disabled:l})}),c.vesselId&&e.jsx("span",{className:"text-red-500 text-xs",children:c.vesselId.message})]}),e.jsxs(S,{label:"Barge Name",labelWidth:"100px",children:[e.jsx(B,{name:"bargeId",control:f.control,render:({field:a})=>e.jsx(de,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select barge...",disabled:l})}),c.bargeId&&e.jsx("span",{className:"text-red-500 text-xs",children:c.bargeId.message})]}),e.jsxs(S,{label:"Voyage",labelWidth:"100px",children:[e.jsx(O,{...T("voyage")}),c.voyage&&e.jsx("span",{className:"text-red-500 text-xs",children:c.voyage.message})]})]}),e.jsxs(Z,{showDivider:!1,children:[e.jsxs(S,{label:"Jetty",labelWidth:"100px",children:[e.jsx(B,{name:"jettyId",control:f.control,render:({field:a})=>e.jsx(Le,{value:a.value,onValueChange:a.onChange,placeholder:"Select jetty...",disabled:l})}),c.jettyId&&e.jsx("span",{className:"text-red-500 text-xs",children:c.jettyId.message})]}),e.jsxs(S,{label:"A/Side Date",labelWidth:"100px",children:[e.jsx(O,{type:"datetime-local",...T("asideDate")}),c.asideDate&&e.jsx("span",{className:"text-red-500 text-xs",children:c.asideDate.message})]}),e.jsxs(S,{label:"Cast Of Date",labelWidth:"100px",children:[e.jsx(O,{type:"datetime-local",...T("castOfDate")}),c.castOfDate&&e.jsx("span",{className:"text-red-500 text-xs",children:c.castOfDate.message})]}),e.jsxs(S,{label:"Arrival Date",labelWidth:"100px",children:[e.jsx(O,{type:"datetime-local",...T("vesselArrival")}),c.vesselArrival&&e.jsx("span",{className:"text-red-500 text-xs",children:c.vesselArrival.message})]}),e.jsxs(S,{label:"Departure Date",labelWidth:"100px",children:[e.jsx(O,{type:"datetime-local",...T("vesselDeparture")}),c.vesselDeparture&&e.jsx("span",{className:"text-red-500 text-xs",children:c.vesselDeparture.message})]})]}),e.jsxs(Z,{showDivider:!1,children:[e.jsxs(S,{label:"Posting Date",labelWidth:"100px",children:[e.jsx(O,{type:"date",...T("postingDate")}),c.postingDate&&e.jsx("span",{className:"text-red-500 text-xs",children:c.postingDate.message})]}),e.jsxs(S,{label:"Port Origin",labelWidth:"100px",children:[e.jsx(B,{name:"portOriginId",control:f.control,render:({field:a})=>e.jsx(Fe,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select port origin...",disabled:l})}),c.portOriginId&&e.jsx("span",{className:"text-red-500 text-xs",children:c.portOriginId.message})]}),e.jsxs(S,{label:"Destination Port",labelWidth:"100px",children:[e.jsx(B,{name:"destinationPortId",control:f.control,render:({field:a})=>e.jsx(Ve,{value:a.value??"",onValueChange:a.onChange,placeholder:"Select destination port...",disabled:l})}),c.destinationPortId&&e.jsx("span",{className:"text-red-500 text-xs",children:c.destinationPortId.message})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block font-medium mb-2",children:"Items"}),e.jsx(Oe,{ref:P,themeName:"ht-theme-main",data:y,columns:le,colHeaders:le.map(a=>typeof a=="object"&&a&&"title"in a?a.title:void 0).filter(a=>typeof a=="string"),rowHeaders:!0,height:"50vh",licenseKey:"non-commercial-and-evaluation",stretchH:"all",contextMenu:!0,manualColumnResize:!0,manualRowResize:!0,autoColumnSize:!1,autoRowSize:!1,startRows:1,dropdownMenu:!0,filters:!0,colWidths:80,hiddenColumns:{copyPasteEnabled:!0,indicators:!0,columns:[0,1,2]},width:"100%",persistentState:!0}),q.some(a=>a)&&e.jsx("div",{className:"mt-2 text-red-500 text-xs",children:q.map((a,u)=>a&&e.jsxs("div",{children:["Row ",u+1,": ",a]},u))})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[j&&e.jsx(G,{type:"button",variant:"outline",onClick:je,disabled:l,children:"+ Add Line"}),typeof ie?.isCustomArea=="boolean"&&ie.isCustomArea&&e.jsx(G,{type:"button",variant:"warning",onClick:be,disabled:l||se||n.docStatus==="Waiting"||n.docStatus==="Approved",children:se?"Submitting...":"Submit Approval"}),e.jsx(G,{type:"submit",disabled:l,children:l?t==="edit"?"Saving...":"Creating...":t==="edit"?"Save Changes":"Create"})]})]})})]}),e.jsx(Te,{isOpen:A,onOpenChange:I,documentSrc:L})]})};export{rt as L};
//# sourceMappingURL=local-vessel-form-B3V1uWrj.js.map
