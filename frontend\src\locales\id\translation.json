{"menu": {"dashboard": "Be<PERSON><PERSON>", "jettyApplication": "Aplikasi Jetty", "newApplication": "Aplikasi Baru", "applicationList": "Daftar Aplikasi", "approvalManagement": "<PERSON><PERSON><PERSON><PERSON>", "incomingApproval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "history": "Riwayat", "customAreaVessel": "Kapal Area Pabean", "exportVessel": "Kapal <PERSON>", "importVessel": "<PERSON><PERSON>", "localVessel": "<PERSON><PERSON>", "nonCustomAreaVessel": "Kapal Area Non Pabean", "jettyOperations": "Operasi Jetty", "masterData": "Data Master", "manageJetty": "<PERSON><PERSON><PERSON>", "manageCargo": "<PERSON><PERSON><PERSON>", "masterReport": "<PERSON><PERSON><PERSON> Master", "reports": "<PERSON><PERSON><PERSON>", "documentTemplate": "Template Dokumen", "approvalTemplate": "Template <PERSON><PERSON><PERSON>"}, "table": {"docNum": "No. Dokumen", "vesselName": "<PERSON><PERSON>", "voyage": "Voyage", "arrivalDate": "Tanggal Kedatangan", "departureDate": "Tanggal Keberangkatan", "jetty": "<PERSON>y", "portOrigin": "<PERSON><PERSON><PERSON><PERSON>", "destinationPort": "<PERSON><PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "docType": "<PERSON><PERSON><PERSON> Dokumen", "bargeName": "<PERSON><PERSON>", "docStatus": "Status Dokumen", "vesselType": "<PERSON><PERSON><PERSON>", "arrival": "Kedatangan", "requestDate": "<PERSON><PERSON>", "status": "Status", "requester": "<PERSON><PERSON><PERSON><PERSON>", "approveRequest": "<PERSON><PERSON><PERSON><PERSON>", "rejectRequest": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>", "approver": "Penyetuju", "statusPending": "Tertunda", "statusApproved": "Disetuju<PERSON>", "statusRejected": "<PERSON><PERSON><PERSON>", "statusCancelled": "Di<PERSON><PERSON><PERSON>", "statusUnknown": "Tidak Diketahui", "agent": "Agent"}, "datagrid": {"customArea": {"localVessel": "Kapal Lokal Area Pabean", "importVessel": "Kapal Impor Area Pabean", "exportVessel": "Kapal Ekspor Area Pabean"}, "nonCustomArea": {"localVessel": "Kapal Lokal Area Non Pabean", "importVessel": "Kapal Impor Area Non Pabean", "exportVessel": "Kapal Ekspor Area Non Pabean"}, "localVessel": "<PERSON><PERSON>", "importVessel": "<PERSON><PERSON>", "exportVessel": "Kapal <PERSON>", "pendingApprovals": "Persetujuan Tertunda", "approvalHistory": "Riwayat Persetujuan"}, "pages": {"vessel": {"create": {"export": "Buat Kapal Ekspor", "import": "<PERSON><PERSON><PERSON>", "local": "<PERSON><PERSON><PERSON>"}, "edit": {"export": "<PERSON>", "import": "<PERSON>", "local": "<PERSON>"}}}, "form": {"labels": {"docNum": "No. Dokumen", "vesselName": "<PERSON><PERSON>", "vesselType": "<PERSON><PERSON><PERSON>", "bargeName": "<PERSON><PERSON>", "voyage": "Voyage", "jetty": "<PERSON>y", "asideDate": "Tanggal A/Side", "castOfDate": "Tanggal Cast Of", "arrivalDate": "Tanggal Kedatangan", "departureDate": "Tanggal Keberangkatan", "postingDate": "Tanggal Posting", "portOrigin": "<PERSON><PERSON><PERSON><PERSON>", "destinationPort": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "alias": "<PERSON><PERSON>", "status": "Status", "flag": "<PERSON><PERSON>", "type": "Tipe", "transType": "Tipe Trans", "grossWeight": "<PERSON><PERSON>", "max": "<PERSON><PERSON><PERSON><PERSON>", "port": "Pelabuhan", "customArea": "Area Pabean"}}}