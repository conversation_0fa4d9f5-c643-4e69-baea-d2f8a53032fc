{"version": 3, "file": "page-DdGFgOv3.js", "sources": ["../../../../../frontend/src/components/applications/jetty-request-create.tsx", "../../../../../frontend/src/pages/application/create/page.tsx"], "sourcesContent": ["import type { CreateUpdateJettyRequestDto, RemoteServiceErrorResponse, VesselHeaderDto } from '@/client';\r\nimport { postApiIdjasJettyRequest } from '@/client/sdk.gen';\r\nimport ApplicationForm from '@/components/applications/application-form';\r\nimport { Button } from '@/components/ui/button';\r\nimport { DocumentPreviewDialog } from '@/components/ui/DocumentPreviewDialog';\r\nimport { formatDateForInput } from '@/lib/date-helper';\r\nimport { useToast } from '@/lib/useToast';\r\nimport { HotTable } from '@handsontable/react-wrapper';\r\nimport { router } from '@inertiajs/react';\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { registerAllModules } from 'handsontable/registry';\r\nimport 'handsontable/styles/handsontable.min.css';\r\nimport 'handsontable/styles/ht-theme-horizon.css';\r\nimport 'handsontable/styles/ht-theme-main.min.css';\r\nimport { useRef, useState } from 'react';\r\nimport { columns, type TableRowData } from './handsontable-column';\r\nimport { renderDeleteButton, renderPreviewButton, renderSubmitButton } from './handsontable-renderer';\r\n\r\nregisterAllModules();\r\n\r\nconst initialData: TableRowData[] = [];\r\n\r\n// Define the correct type for items based on the mapping in setTableData\r\ntype JettyVesselItem = {\r\n  tenant?: { name?: string };\r\n  itemName?: string;\r\n  itemQty?: number;\r\n  unitQty?: string;\r\n  remarks?: string;\r\n  letterNo?: string;\r\n  letterDate?: string;\r\n  id?: string;\r\n};\r\n\r\nexport const JettyRequestCreate = () => {\r\n  const { toast } = useToast()\r\n  const [docNum, setDocNum] = useState('25060001');\r\n  const [vesselType, setVesselType] = useState('');\r\n  const [voyage, setVoyage] = useState('001');\r\n  const [jetty, setJetty] = useState('');\r\n  const [arrivalDate, setArrivalDate] = useState('');\r\n  const [departureDate, setDepartureDate] = useState('');\r\n  const [asideDate, setAsideDate] = useState('');\r\n  const [castOfDate, setCastOfDate] = useState('');\r\n  const [postDate, setPostDate] = useState('');\r\n  const [portOrigin, setPortOrigin] = useState('');\r\n  const [destinationPort, setDestinationPort] = useState('');\r\n  const [barge, setBarge] = useState('');\r\n  const [tableData, setTableData] = useState(initialData);\r\n  const [vessel, setVessel] = useState<(VesselHeaderDto & { items?: JettyVesselItem[] }) | null>(null);\r\n\r\n  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);\r\n  const [previewDocumentSrc, setPreviewDocumentSrc] = useState('');\r\n  const [loadingStates, setLoadingStates] = useState<Map<number, boolean>>(new Map());\r\n\r\n  const hotTableComponent = useRef(null);\r\n\r\n  const queryClient = useQueryClient();\r\n\r\n  // Helper functions for preview dialog\r\n  const handlePreview = (documentSrc: string) => {\r\n    setPreviewDocumentSrc(documentSrc);\r\n    setIsPreviewDialogOpen(true);\r\n  };\r\n\r\n  const setLoadingState = (row: number, loading: boolean) => {\r\n    setLoadingStates(prev => {\r\n      const newMap = new Map(prev);\r\n      if (loading) {\r\n        newMap.set(row, true);\r\n      } else {\r\n        newMap.delete(row);\r\n      }\r\n      return newMap;\r\n    });\r\n  };\r\n\r\n  const createJettyRequestMutation = useMutation({\r\n    mutationFn: async (payload: CreateUpdateJettyRequestDto) =>\r\n      postApiIdjasJettyRequest({ body: payload }),\r\n    onSuccess: (data) => {\r\n      toast({\r\n        title: 'Success',\r\n        description: 'Jetty request saved successfully.',\r\n        variant: 'success',\r\n      });\r\n      setTableData([]);\r\n      setVessel(null);\r\n      setVoyage('');\r\n      setJetty('');\r\n      setArrivalDate('');\r\n      setDepartureDate('');\r\n      setAsideDate('');\r\n      setCastOfDate('');\r\n      setPostDate('');\r\n      if (data && data.data?.id) {\r\n        router.visit(`/application/${data.data?.id}/edit`);\r\n      }\r\n    },\r\n    onError: (err: RemoteServiceErrorResponse) => {\r\n      toast({\r\n        title: err?.error?.message || 'Error',\r\n        description: err?.error?.details || 'Failed to save jetty request.',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  });\r\n\r\n\r\n  const handleSave = () => {\r\n    // Prevent duplicate submissions\r\n    if (createJettyRequestMutation.isPending) {\r\n      return;\r\n    }\r\n\r\n    const payload = {\r\n      docNum: Number(docNum),\r\n      vesselType,\r\n      vesselName: vessel?.vesselName ?? '',\r\n      voyage,\r\n      jetty,\r\n      arrivalDate,\r\n      departureDate,\r\n      asideDate,\r\n      castOfDate,\r\n      postDate,\r\n      portOrigin,\r\n      destinationPort,\r\n      barge,\r\n      referenceId: vessel?.id,\r\n      items: tableData.map(item => ({\r\n        tenantName: item.tenantName,\r\n        itemName: item.itemName,\r\n        qty: Number(item.quantity) || 0,\r\n        uoM: item.uom,\r\n        notes: item.remark,\r\n        letterNo: item.letterNo,\r\n        letterDate: item.letterDate,\r\n        id: item.id,\r\n        status: 1 as const, // Open status\r\n      })),\r\n    };\r\n    createJettyRequestMutation.mutate(payload);\r\n  };\r\n\r\n  const handleVesselChange = (selectedVessel: (VesselHeaderDto & { items?: JettyVesselItem[] }) | null) => {\r\n    setVessel(selectedVessel);\r\n    if (selectedVessel) {\r\n      setVoyage(selectedVessel.voyage || '');\r\n      setJetty(selectedVessel.jetty?.id || '');\r\n      setArrivalDate(formatDateForInput(selectedVessel.vesselArrival));\r\n      setDepartureDate(formatDateForInput(selectedVessel.vesselDeparture));\r\n      setPortOrigin(selectedVessel.portOrigin || '');\r\n      setDestinationPort(selectedVessel.destinationPort || '');\r\n      setBarge(selectedVessel.barge?.name || '');\r\n      if (selectedVessel.items) {\r\n        setTableData(\r\n          selectedVessel.items.map(item => ({\r\n            tenantName: item.tenant?.name ?? '',\r\n            itemName: item.itemName || '',\r\n            quantity: String(item.itemQty ?? ''),\r\n            uom: item.unitQty || '',\r\n            remark: item.remarks || '',\r\n            status: 'Draft',\r\n            letterNo: item.letterNo || '',\r\n            letterDate: item.letterDate || '',\r\n            id: item.id,\r\n            preview: '',\r\n            submit: '',\r\n            delete: '',\r\n          }))\r\n        );\r\n      } else {\r\n        setTableData([]);\r\n      }\r\n    } else {\r\n      setVoyage('');\r\n      setJetty('');\r\n      setArrivalDate('');\r\n      setDepartureDate('');\r\n      setAsideDate('');\r\n      setCastOfDate('');\r\n      setPostDate('');\r\n      setTableData([]);\r\n    }\r\n  };\r\n\r\n  const columnConfig = columns.map(col => {\r\n    if (col.data === 'id') {\r\n      return { ...col, renderer: renderPreviewButton(tableData, handlePreview, loadingStates, setLoadingState) };\r\n    }\r\n    if (col.data === 'submit') {\r\n      return { ...col, renderer: renderSubmitButton(tableData, vesselType, loadingStates, setLoadingState, queryClient, '') };\r\n    }\r\n    if (col.data === 'delete') {\r\n      return { ...col, renderer: renderDeleteButton(tableData, setTableData, loadingStates, setLoadingState, queryClient, '') };\r\n    }\r\n    return col;\r\n  });\r\n\r\n  return (\r\n    <div className=\"container mx-auto\">\r\n      <div className='bg-card text-card-foreground rounded-xl border shadow-sm px-4 py-4'>\r\n        <ApplicationForm\r\n          docNum={docNum}\r\n          vesselType={vesselType}\r\n          vessel={vessel}\r\n          voyage={voyage}\r\n          jetty={jetty}\r\n          arrivalDate={arrivalDate}\r\n          departureDate={departureDate}\r\n          asideDate={asideDate}\r\n          castOfDate={castOfDate}\r\n          postDate={postDate}\r\n          portOrigin={portOrigin}\r\n          destinationPort={destinationPort}\r\n          barge={barge}\r\n          onDocNumChange={setDocNum}\r\n          onVesselTypeChange={setVesselType}\r\n          onVesselChange={handleVesselChange}\r\n          onVoyageChange={setVoyage}\r\n          onJettyChange={setJetty}\r\n          onArrivalDateChange={setArrivalDate}\r\n          onDepartureDateChange={setDepartureDate}\r\n          onAsideDateChange={setAsideDate}\r\n          onCastOfDateChange={setCastOfDate}\r\n          onPostDateChange={setPostDate}\r\n          onPortOriginChange={setPortOrigin}\r\n          onDestinationPortChange={setDestinationPort}\r\n          onBargeChange={setBarge}\r\n        />\r\n        <div style={{ maxWidth: '100%', overflowX: 'auto' }} className=\"mb-8\">\r\n          <HotTable\r\n            ref={hotTableComponent}\r\n            themeName=\"ht-theme-main\"\r\n            data={tableData}\r\n            columns={columnConfig}\r\n            colHeaders={columns.map(col => col.title)}\r\n            rowHeaders={true}\r\n            height=\"50vh\"\r\n            rowHeights={27}\r\n            currentRowClassName=\"currentRow\"\r\n            currentColClassName=\"currentCol\"\r\n            // autoWrapRow={true}\r\n            licenseKey=\"non-commercial-and-evaluation\"\r\n            // stretchH=\"all\"\r\n            contextMenu={true}\r\n            manualColumnResize={true}\r\n            manualRowResize={true}\r\n            autoColumnSize={false}\r\n            autoRowSize={false}\r\n            startRows={1}\r\n            viewportRowRenderingOffset={1000}\r\n            viewportColumnRenderingOffset={100}\r\n            dropdownMenu={true}\r\n            filters={true}\r\n            colWidths={80}\r\n            width=\"100%\"\r\n            persistentState={true}\r\n          />\r\n        </div>\r\n        <div className=\"flex justify-end\">\r\n          <Button\r\n            onClick={handleSave}\r\n            disabled={createJettyRequestMutation.isPending}\r\n            className=\"px-6 py-2 text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n          >\r\n            {createJettyRequestMutation.isPending ? 'Saving...' : 'Save'}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <DocumentPreviewDialog\r\n        isOpen={isPreviewDialogOpen}\r\n        onOpenChange={setIsPreviewDialogOpen}\r\n        documentSrc={previewDocumentSrc}\r\n      />\r\n    </div>\r\n  );\r\n};", "import { JettyRequestCreate } from '@/components/applications/jetty-request-create';\r\nimport AppLayout from '@/layouts/app-layout';\r\nimport { Head } from '@inertiajs/react';\r\n\r\nexport default function CreateApplication() {\r\n  return (\r\n    <AppLayout>\r\n      <Head title=\"Create New Application\" />\r\n      <JettyRequestCreate />\r\n    </AppLayout>\r\n  );\r\n}\r\n"], "names": ["registerAllModules", "initialData", "JettyRequestCreate", "toast", "useToast", "doc<PERSON>um", "setDocNum", "useState", "vesselType", "setVesselType", "voyage", "setVoyage", "jetty", "<PERSON><PERSON><PERSON><PERSON>", "arrivalDate", "setArrivalDate", "departureDate", "setDepartureDate", "asideDate", "setAsideDate", "castOfDate", "setCastOfDate", "postDate", "setPostDate", "port<PERSON>rigin", "set<PERSON>ort<PERSON><PERSON><PERSON>", "destinationPort", "setDestinationPort", "barge", "setBarge", "tableData", "setTableData", "vessel", "<PERSON><PERSON><PERSON><PERSON>", "isPreviewDialogOpen", "setIsPreviewDialogOpen", "previewDocumentSrc", "setPreviewDocumentSrc", "loadingStates", "setLoadingStates", "hotTableComponent", "useRef", "queryClient", "useQueryClient", "handlePreview", "documentSrc", "setLoadingState", "row", "loading", "prev", "newMap", "createJettyRequestMutation", "useMutation", "payload", "postApiIdjasJettyRequest", "data", "router", "err", "handleSave", "item", "handleVesselChange", "<PERSON><PERSON><PERSON><PERSON>", "formatDateForInput", "columnConfig", "columns", "col", "renderPreviewButton", "renderSubmitButton", "renderDeleteButton", "jsxs", "jsx", "ApplicationForm", "HotTable", "<PERSON><PERSON>", "DocumentPreviewDialog", "CreateApplication", "AppLayout", "Head"], "mappings": "y8BAkBAA,GAAmB,EAEnB,MAAMC,GAA8B,CAAC,EAcxBC,GAAqB,IAAM,CAChC,KAAA,CAAE,MAAAC,CAAM,EAAIC,EAAS,EACrB,CAACC,EAAQC,CAAS,EAAIC,EAAAA,SAAS,UAAU,EACzC,CAACC,EAAYC,CAAa,EAAIF,EAAAA,SAAS,EAAE,EACzC,CAACG,EAAQC,CAAS,EAAIJ,EAAAA,SAAS,KAAK,EACpC,CAACK,EAAOC,CAAQ,EAAIN,EAAAA,SAAS,EAAE,EAC/B,CAACO,EAAaC,CAAc,EAAIR,EAAAA,SAAS,EAAE,EAC3C,CAACS,EAAeC,CAAgB,EAAIV,EAAAA,SAAS,EAAE,EAC/C,CAACW,EAAWC,CAAY,EAAIZ,EAAAA,SAAS,EAAE,EACvC,CAACa,EAAYC,CAAa,EAAId,EAAAA,SAAS,EAAE,EACzC,CAACe,EAAUC,CAAW,EAAIhB,EAAAA,SAAS,EAAE,EACrC,CAACiB,EAAYC,CAAa,EAAIlB,EAAAA,SAAS,EAAE,EACzC,CAACmB,EAAiBC,CAAkB,EAAIpB,EAAAA,SAAS,EAAE,EACnD,CAACqB,EAAOC,CAAQ,EAAItB,EAAAA,SAAS,EAAE,EAC/B,CAACuB,EAAWC,CAAY,EAAIxB,EAAAA,SAASN,EAAW,EAChD,CAAC+B,EAAQC,CAAS,EAAI1B,EAAAA,SAAmE,IAAI,EAE7F,CAAC2B,EAAqBC,CAAsB,EAAI5B,EAAAA,SAAS,EAAK,EAC9D,CAAC6B,EAAoBC,CAAqB,EAAI9B,EAAAA,SAAS,EAAE,EACzD,CAAC+B,EAAeC,CAAgB,EAAIhC,EAAAA,SAA+B,IAAI,GAAK,EAE5EiC,EAAoBC,SAAO,IAAI,EAE/BC,EAAcC,EAAe,EAG7BC,EAAiBC,GAAwB,CAC7CR,EAAsBQ,CAAW,EACjCV,EAAuB,EAAI,CAC7B,EAEMW,EAAkB,CAACC,EAAaC,IAAqB,CACzDT,EAAyBU,GAAA,CACjB,MAAAC,EAAS,IAAI,IAAID,CAAI,EAC3B,OAAID,EACKE,EAAA,IAAIH,EAAK,EAAI,EAEpBG,EAAO,OAAOH,CAAG,EAEZG,CAAA,CACR,CACH,EAEMC,EAA6BC,EAAY,CAC7C,WAAY,MAAOC,GACjBC,GAAyB,CAAE,KAAMD,EAAS,EAC5C,UAAYE,GAAS,CACbpD,EAAA,CACJ,MAAO,UACP,YAAa,oCACb,QAAS,SAAA,CACV,EACD4B,EAAa,CAAA,CAAE,EACfE,EAAU,IAAI,EACdtB,EAAU,EAAE,EACZE,EAAS,EAAE,EACXE,EAAe,EAAE,EACjBE,EAAiB,EAAE,EACnBE,EAAa,EAAE,EACfE,EAAc,EAAE,EAChBE,EAAY,EAAE,EACVgC,GAAQA,EAAK,MAAM,IACrBC,GAAO,MAAM,gBAAgBD,EAAK,MAAM,EAAE,OAAO,CAErD,EACA,QAAUE,GAAoC,CACtCtD,EAAA,CACJ,MAAOsD,GAAK,OAAO,SAAW,QAC9B,YAAaA,GAAK,OAAO,SAAW,gCACpC,QAAS,aAAA,CACV,CAAA,CACH,CACD,EAGKC,EAAa,IAAM,CAEvB,GAAIP,EAA2B,UAC7B,OAGF,MAAME,EAAU,CACd,OAAQ,OAAOhD,CAAM,EACrB,WAAAG,EACA,WAAYwB,GAAQ,YAAc,GAClC,OAAAtB,EACA,MAAAE,EACA,YAAAE,EACA,cAAAE,EACA,UAAAE,EACA,WAAAE,EACA,SAAAE,EACA,WAAAE,EACA,gBAAAE,EACA,MAAAE,EACA,YAAaI,GAAQ,GACrB,MAAOF,EAAU,IAAa6B,IAAA,CAC5B,WAAYA,EAAK,WACjB,SAAUA,EAAK,SACf,IAAK,OAAOA,EAAK,QAAQ,GAAK,EAC9B,IAAKA,EAAK,IACV,MAAOA,EAAK,OACZ,SAAUA,EAAK,SACf,WAAYA,EAAK,WACjB,GAAIA,EAAK,GACT,OAAQ,CAAA,EACR,CACJ,EACAR,EAA2B,OAAOE,CAAO,CAC3C,EAEMO,EAAsBC,GAA6E,CACvG5B,EAAU4B,CAAc,EACpBA,GACQlD,EAAAkD,EAAe,QAAU,EAAE,EAC5BhD,EAAAgD,EAAe,OAAO,IAAM,EAAE,EACxB9C,EAAA+C,EAAmBD,EAAe,aAAa,CAAC,EAC9C5C,EAAA6C,EAAmBD,EAAe,eAAe,CAAC,EACrDpC,EAAAoC,EAAe,YAAc,EAAE,EAC1BlC,EAAAkC,EAAe,iBAAmB,EAAE,EAC9ChC,EAAAgC,EAAe,OAAO,MAAQ,EAAE,EACrCA,EAAe,MACjB9B,EACE8B,EAAe,MAAM,IAAaF,IAAA,CAChC,WAAYA,EAAK,QAAQ,MAAQ,GACjC,SAAUA,EAAK,UAAY,GAC3B,SAAU,OAAOA,EAAK,SAAW,EAAE,EACnC,IAAKA,EAAK,SAAW,GACrB,OAAQA,EAAK,SAAW,GACxB,OAAQ,QACR,SAAUA,EAAK,UAAY,GAC3B,WAAYA,EAAK,YAAc,GAC/B,GAAIA,EAAK,GACT,QAAS,GACT,OAAQ,GACR,OAAQ,EAAA,EACR,CACJ,EAEA5B,EAAa,CAAA,CAAE,IAGjBpB,EAAU,EAAE,EACZE,EAAS,EAAE,EACXE,EAAe,EAAE,EACjBE,EAAiB,EAAE,EACnBE,EAAa,EAAE,EACfE,EAAc,EAAE,EAChBE,EAAY,EAAE,EACdQ,EAAa,CAAA,CAAE,EAEnB,EAEMgC,EAAeC,EAAQ,IAAWC,GAClCA,EAAI,OAAS,KACR,CAAE,GAAGA,EAAK,SAAUC,GAAoBpC,EAAWc,EAAeN,EAAeQ,CAAe,CAAE,EAEvGmB,EAAI,OAAS,SACR,CAAE,GAAGA,EAAK,SAAUE,GAAmBrC,EAAWtB,EAAY8B,EAAeQ,EAAiBJ,EAAa,EAAE,CAAE,EAEpHuB,EAAI,OAAS,SACR,CAAE,GAAGA,EAAK,SAAUG,GAAmBtC,EAAWC,EAAcO,EAAeQ,EAAiBJ,EAAa,EAAE,CAAE,EAEnHuB,CACR,EAGC,OAAAI,EAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,qEACb,SAAA,CAAAC,EAAA,IAACC,GAAA,CACC,OAAAlE,EACA,WAAAG,EACA,OAAAwB,EACA,OAAAtB,EACA,MAAAE,EACA,YAAAE,EACA,cAAAE,EACA,UAAAE,EACA,WAAAE,EACA,SAAAE,EACA,WAAAE,EACA,gBAAAE,EACA,MAAAE,EACA,eAAgBtB,EAChB,mBAAoBG,EACpB,eAAgBmD,EAChB,eAAgBjD,EAChB,cAAeE,EACf,oBAAqBE,EACrB,sBAAuBE,EACvB,kBAAmBE,EACnB,mBAAoBE,EACpB,iBAAkBE,EAClB,mBAAoBE,EACpB,wBAAyBE,EACzB,cAAeE,CAAA,CACjB,EACAyC,EAAAA,IAAC,MAAI,CAAA,MAAO,CAAE,SAAU,OAAQ,UAAW,MAAA,EAAU,UAAU,OAC7D,SAAAA,EAAA,IAACE,GAAA,CACC,IAAKhC,EACL,UAAU,gBACV,KAAMV,EACN,QAASiC,EACT,WAAYC,EAAQ,IAAIC,GAAOA,EAAI,KAAK,EACxC,WAAY,GACZ,OAAO,OACP,WAAY,GACZ,oBAAoB,aACpB,oBAAoB,aAEpB,WAAW,gCAEX,YAAa,GACb,mBAAoB,GACpB,gBAAiB,GACjB,eAAgB,GAChB,YAAa,GACb,UAAW,EACX,2BAA4B,IAC5B,8BAA+B,IAC/B,aAAc,GACd,QAAS,GACT,UAAW,GACX,MAAM,OACN,gBAAiB,EAAA,CAAA,EAErB,EACAK,EAAAA,IAAC,MAAI,CAAA,UAAU,mBACb,SAAAA,EAAA,IAACG,GAAA,CACC,QAASf,EACT,SAAUP,EAA2B,UACrC,UAAU,8KAET,SAAAA,EAA2B,UAAY,YAAc,MAAA,CAAA,CAE1D,CAAA,CAAA,EACF,EACAmB,EAAA,IAACI,GAAA,CACC,OAAQxC,EACR,aAAcC,EACd,YAAaC,CAAA,CAAA,CACf,EACF,CAEJ,EClRA,SAAwBuC,IAAoB,CAC1C,cACGC,GACC,CAAA,SAAA,CAACN,EAAAA,IAAAO,GAAA,CAAK,MAAM,wBAAyB,CAAA,QACpC3E,GAAmB,CAAA,CAAA,CAAA,EACtB,CAEJ"}