import {
  IconDashboard,
  IconClipboardCheck,
  IconDatabase,
  IconFileDescription,
  IconClipboardList,
  IconSailboat,
  // IconShip,
  // IconShipOff,
  IconAnchor,
} from '@tabler/icons-react'
import type { Policy } from '@/lib/hooks/useGrantedPolicies'

export const siteConfig = {
  name: 'JETTY APPROVAL',
  baseLinks: {
    login: '/auth/login',
  },
}

export type siteConfig = typeof siteConfig

// export const clientConfig = {
//   url: process.env.NEXT_PUBLIC_API_URL,
//   audience: process.env.NEXT_PUBLIC_API_URL,
//   client_id: process.env.NEXT_PUBLIC_CLIENT_ID,
//   client_secret: process.env.NEXT_PUBLIC_CLIENT_SECRET,
//   scope: process.env.NEXT_PUBLIC_SCOPE,
//   redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}/auth/openiddict`,
//   post_logout_redirect_uri: `${process.env.NEXT_PUBLIC_APP_URL}`,
//   response_type: 'code',
//   grant_type: 'authorization_code',
//   post_login_route: `${process.env.NEXT_PUBLIC_APP_URL}/admin`,
//   code_challenge_method: 'S256',
// }

/**
 * List of menus shown in the Admin layout.
 * Each menu item contains a title, url, icon, and optional permission.
 */
export const AdminMenus: Array<{
  title: string;
  url: string;
  isActive: boolean;
  icon: React.ComponentType;
  items?: Array<{
    title: string;
    url: string;
    permission: Policy;
  }>;
  permission?: Policy;
}> = [
    {
      title: 'menu.dashboard',
      url: '/',
      isActive: false,
      icon: IconDashboard,
    },
    // {
    //   title: 'menu.jettyApplication',
    //   url: '#',
    //   isActive: false,
    //   icon: IconAppWindow,
    //   permission: 'JettyApprovalApp.JettyRequest',
    //   items: [
    //     {
    //       title: 'menu.newApplication',
    //       url: '/application/create',
    //       permission: 'JettyApprovalApp.JettyRequest.Create',
    //     },
    //     {
    //       title: 'menu.applicationList',
    //       url: '/application/list',
    //       permission: 'JettyApprovalApp.JettyRequest.View',
    //     },
    //   ],
    // },
    {
      title: 'menu.approvalManagement',
      url: '#',
      isActive: false,
      icon: IconClipboardCheck,
      permission: 'JettyApprovalApp.ApprovalStages',
      items: [
        {
          title: 'menu.incomingApproval',
          url: '/approval',
          permission: 'JettyApprovalApp.ApprovalStages.View',
        },
        {
          title: 'menu.history',
          url: '/approval/history',
          permission: 'JettyApprovalApp.ApprovalStages.View',
        },
      ],
    },
    // {
    //   title: 'menu.exportVessel',
    //   url: '/custom-area/export',
    //   isActive: false,
    //   icon: IconShip,
    //   permission: 'JettyApprovalApp.ExportVesselCustomArea.View',
    // },
    // {
    //   title: 'menu.importVessel',
    //   url: '/custom-area/import',
    //   isActive: false,
    //   icon: IconSailboat,
    //   permission: 'JettyApprovalApp.ImportVesselCustomArea.View',
    // },
    // {
    //   title: 'menu.localVessel',
    //   url: '/custom-area/local',
    //   isActive: false,
    //   icon: IconShipOff,
    //   permission: 'JettyApprovalApp.LocalVesselCustomArea.View',
    // },
    {
      title: 'menu.customAreaVessel',
      url: '#',
      isActive: false,
      icon: IconAnchor,
      permission: 'JettyApprovalApp.CustomArea',
      items: [
        {
          title: 'menu.exportVessel',
          url: '/custom-area/export',
          permission: 'JettyApprovalApp.ExportVesselCustomArea.View',
        },
        {
          title: 'menu.importVessel',
          url: '/custom-area/import',
          permission: 'JettyApprovalApp.ImportVesselCustomArea.View',
        },
        {
          title: 'menu.localVessel',
          url: '/custom-area/local',
          permission: 'JettyApprovalApp.LocalVesselCustomArea.View',
        },
      ],
    },
    {
      title: 'menu.nonCustomAreaVessel',
      url: '#',
      isActive: false,
      icon: IconSailboat,
      permission: 'JettyApprovalApp.NonCustomArea',
      items: [
        {
          title: 'menu.exportVessel',
          url: '/non-custom-area/export',
          permission: 'JettyApprovalApp.ExportVesselNonCustomArea.View',
        },
        {
          title: 'menu.importVessel',
          url: '/non-custom-area/import',
          permission: 'JettyApprovalApp.ImportVesselNonCustomArea.View',
        },
        {
          title: 'menu.localVessel',
          url: '/non-custom-area/local',
          permission: 'JettyApprovalApp.LocalVesselNonCustomArea.View',
        },
      ],
    },
    // {
    //   title: 'menu.jettyOperations',
    //   url: '#',
    //   isActive: false,
    //   icon: IconShip,
    //   permission: 'JettyApprovalApp.JettyManage',
    //   items: [
    //     {
    //       title: 'menu.customAreaVessel',
    //       url: '/jetty/custom-area',
    //       permission: 'JettyApprovalApp.JettyManage.View',
    //     },
    //     {
    //       title: 'menu.nonCustomAreaVessel',
    //       url: '/jetty/non-custom-area',
    //       permission: 'JettyApprovalApp.JettyManage.View',
    //     },
    //   ],
    // },
    {
      title: 'menu.masterData',
      url: '#',
      isActive: false,
      icon: IconDatabase,
      permission: 'JettyApprovalApp.JettyManage.View',
      items: [
        {
          title: 'menu.manageJetty',
          url: '/manage-jetty',
          permission: 'JettyApprovalApp.JettyManage.View',
        },
        {
          title: 'menu.manageCargo',
          url: '/manage-vessel',
          permission: 'JettyApprovalApp.JettyManage.View',
        },
        // {
        //   title: 'menu.masterReport',
        //   url: '/master-report',
        //   permission: 'JettyApprovalApp.Report.Edit',
        // },
      ],
    },
    // {
    //   title: 'menu.reports',
    //   url: '/report',
    //   isActive: false,
    //   icon: IconReportAnalytics,
    //   permission: 'JettyApprovalApp.Report.Export',
    // },
    {
      title: 'menu.documentTemplate',
      url: '/document-template',
      isActive: false,
      icon: IconFileDescription,
      permission: 'JettyApprovalApp.DocumentTemplate.Create',
    },
    {
      title: 'menu.approvalTemplate',
      url: '/approval-template',
      isActive: false,
      icon: IconClipboardList,
      permission: 'JettyApprovalApp.ApprovalTemplate.Create',
    },
  ];
