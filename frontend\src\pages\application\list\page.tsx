import JettyRequestTable from "@/components/jetty-request-table";
import AppLayout from "@/layouts/app-layout";
import { Head } from "@inertiajs/react";
import React from "react";

const ApplicationListPage: React.FC = () => {
  return (
    <AppLayout>
      <Head title={`Application List`} />
      <div className="flex flex-col space-y-4 p-4">
        <JettyRequestTable />
      </div>
    </AppLayout>
  );
};

export default ApplicationListPage;