{"version": 3, "file": "useJettyDataWithFilter-CK58-c0U.js", "sources": ["../../../../../frontend/src/lib/hooks/useJettyDataWithFilter.ts"], "sourcesContent": ["import { useMutation } from '@tanstack/react-query';\r\nimport { postApiEkbJettyFilter } from '@/client/sdk.gen';\r\nimport { toast } from '@/lib/useToast';\r\nimport type { FilterRequestDto, JettyDto, PagedResultDtoOfJettyDto } from '@/client/types.gen';\r\n\r\nexport const useJettyDataWithFilter = () => {\r\n  return useMutation<JettyDto[], Error, FilterRequestDto>({\r\n    mutationFn: async (filterRequest: FilterRequestDto) => {\r\n      try {\r\n        const response = await postApiEkbJettyFilter({ \r\n          body: filterRequest\r\n        });\r\n        \r\n        // Extract items from the paged result\r\n        const pagedResult = response.data as PagedResultDtoOfJettyDto;\r\n        return pagedResult?.items || [];\r\n      } catch (error: unknown) {\r\n        let message = 'Unknown error occurred while loading Jetty data';\r\n        if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: string }).message === 'string') {\r\n          message = (error as { message?: string }).message ?? message;\r\n        }\r\n        \r\n        console.error('Jetty API Error:', error);\r\n        toast({\r\n          title: 'Error loading Jetty data',\r\n          description: message,\r\n          variant: 'destructive',\r\n        });\r\n        \r\n        throw error; // Re-throw for mutation error handling\r\n      }\r\n    },\r\n    onError: (error: unknown) => {\r\n      console.error('Jetty mutation error:', error);\r\n    }\r\n  });\r\n}; "], "names": ["useJettyDataWithFilter", "useMutation", "filterRequest", "postApiEkbJettyFilter", "error", "message", "toast"], "mappings": "6FAKO,MAAMA,EAAyB,IAC7BC,EAAiD,CACtD,WAAY,MAAOC,GAAoC,CACjD,GAAA,CAOK,OANU,MAAMC,EAAsB,CAC3C,KAAMD,CAAA,CACP,GAG4B,MACT,OAAS,CAAC,QACvBE,EAAgB,CACvB,IAAIC,EAAU,kDACV,MAAA,OAAOD,GAAU,UAAYA,GAAS,YAAaA,GAAS,OAAQA,EAA+B,SAAY,WACjHC,EAAWD,EAA+B,SAAWC,GAIjDC,EAAA,CACJ,MAAO,2BACP,YAAaD,EACb,QAAS,aAAA,CACV,EAEKD,CAAA,CAEV,EACA,QAAUA,GAAmB,CAAA,CAE7B,CACD"}