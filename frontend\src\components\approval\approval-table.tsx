import { postApiApprovalStagesFilterList } from "@/client/sdk.gen";
import type { ApprovalStageDto } from "@/client/types.gen";
import { Button } from "@/components/ui/button";
import { DataGrid } from '@/components/ui/data-grid';
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/useToast";
import { router } from "@inertiajs/react";
import {
  type ColumnDef
} from "@tanstack/react-table";
import { ArrowUpRight, CheckCircle2, XCircle } from "lucide-react";
import React, { useState } from "react";
import ApprovalActions from "./approval-actions";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { ContentCard } from "../layout/content-card";

type ApprovalTableMeta = {
  setApprovalAction: (action: { isOpen: boolean; approvalId: string; action: 'approve' | 'reject'; vessel?: { id?: string; vesselType?: string | null } }) => void;
};

const ApprovalTable: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  // State for approval action modal
  const [approvalAction, setApprovalAction] = useState<{
    isOpen: boolean;
    approvalId: string;
    action: 'approve' | 'reject';
    vessel?: { id?: string; vesselType?: string | null };
  }>({
    isOpen: false,
    approvalId: "",
    action: "approve",
  });

  const handleApprovalAction = (action: { isOpen: boolean; approvalId: string; action: 'approve' | 'reject'; vessel?: { id?: string; vesselType?: string | null } }) => {
    setApprovalAction(action);
  };

  const handleApprovalClose = () => {
    setApprovalAction(prev => ({ ...prev, isOpen: false }));
  };

  const handleApprovalSuccess = () => {
    // The ApprovalActions component will handle invalidating the query
    handleApprovalClose();
  };

  // Define columns with translations
  const approvalColumns: ColumnDef<ApprovalStageDto>[] = [
    {
      accessorKey: "vessel.vesselName",
      header: t('table.vesselName'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.voyage",
      header: t('table.voyage'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.vesselType",
      header: t('table.vesselType'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "vessel.vesselArrival",
      header: t('table.arrival'),
      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleString() : "-",
    },
    {
      accessorKey: "vessel.destinationPort",
      header: t('table.destinationPort'),
      cell: info => info.getValue() ?? "-",
    },
    {
      accessorKey: "requestDate",
      header: t('table.requestDate'),
      cell: info => info.getValue() ? new Date(info.getValue() as string).toLocaleDateString() : "-",
    },
    {
      accessorKey: "status",
      header: t('table.status'),
      cell: info => {
        const status = info.getValue() as string | number;
        let statusText: string;
        let variant: "warning" | "success" | "destructive" | "secondary" | "primary";

        switch (status) {
          case 0:
            statusText = t('table.statusPending');
            variant = "warning";
            break;
          case 1:
            statusText = t('table.statusApproved');
            variant = "success";
            break;
          case 2:
            statusText = t('table.statusRejected');
            variant = "destructive";
            break;
          case 3:
            statusText = t('table.statusCancelled');
            variant = "secondary";
            break;
          default:
            statusText = t('table.statusUnknown');
            variant = "primary";
            break;
        }

        return <Badge variant={variant} size="sm">{statusText}</Badge>;
      },
    },
    {
      accessorKey: "requesterUserName",
      header: t('table.requester'),
      cell: info => info.getValue() ?? "-",
    },
    {
      id: "actions",
      header: t('table.actions'),
      cell: ({ row, table }) => {
        const approval = row.original;
        const isPending = approval.status === 0;
        const vessel = approval.vessel;
        const meta = table.options.meta as ApprovalTableMeta;
        return (
          <div className="flex space-x-1">
            {isPending && (
              <>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-green-600 hover:text-green-700"
                      onClick={() => {
                        meta.setApprovalAction({
                          isOpen: true,
                          approvalId: approval.id || "",
                          action: "approve",
                          vessel: approval.vessel,
                        });
                      }}
                      aria-label="Approve"
                    >
                      <CheckCircle2 className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{t('table.approveRequest')}</TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => {
                        meta.setApprovalAction({
                          isOpen: true,
                          approvalId: approval.id || "",
                          action: "reject",
                          vessel: approval.vessel,
                        });
                      }}
                      aria-label="Reject"
                    >
                      <XCircle className="w-5 h-5" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{t('table.rejectRequest')}</TooltipContent>
                </Tooltip>
              </>
            )}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    if (vessel && vessel.id) {
                      if (vessel.vesselType === 'Import') {
                        router.visit(`/import/edit/${vessel.id}`);
                      } else if (vessel.vesselType === 'Export') {
                        router.visit(`/export/edit/${vessel.id}`);
                      } else {
                        router.visit(`/local/edit/${vessel.id}`);
                      }
                    }
                  }}
                  aria-label="Details"
                >
                  <ArrowUpRight className="w-5 h-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>{t('table.viewDetails')}</TooltipContent>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  return (
    <ContentCard>
      <DataGrid
        columns={approvalColumns}
        title={t('datagrid.pendingApprovals')}
        queryKey={["approval-stages"]}
        manualSorting={true}
        manualFiltering={true}
        autoSizeColumns={true}
        meta={{
          setApprovalAction: handleApprovalAction,
        }}
        queryFn={async ({ pageIndex, pageSize }) => {
          try {
            const response = await postApiApprovalStagesFilterList({
              body: {
                maxResultCount: pageSize,
                skipCount: pageIndex * pageSize,
                filterGroup: {
                  operator: "And",
                  conditions: [
                    {
                      fieldName: "status",
                      operator: "Equals",
                      value: "0"
                    }
                  ]
                }
              }
            });
            if (!response || !response.data) {
              throw new Error("Failed to fetch approvals");
            }
            return {
              items: response.data.items || [],
              totalCount: response.data.totalCount || 0,
            };
          } catch (err) {
            console.error("Error fetching approvals:", err);
            toast({
              title: "Error",
              description: "Failed to load approval list",
              variant: "destructive",
            });
            return { items: [], totalCount: 0 };
          }
        }}
      />

      {/* Approval Actions Modal */}
      <ApprovalActions
        approvalId={approvalAction.approvalId}
        isOpen={approvalAction.isOpen}
        onClose={handleApprovalClose}
        action={approvalAction.action}
        vessel={approvalAction.vessel}
        onSuccess={handleApprovalSuccess}
        queryClient={queryClient}
      />
    </ContentCard>
  );
};

export default ApprovalTable;
