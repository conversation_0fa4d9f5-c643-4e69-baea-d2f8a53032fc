{"version": 3, "file": "import-vessel-form-CB_uPBh9.js", "sources": ["../../../../../frontend/src/components/jetty/vessel/import/import-vessel-columns.ts", "../../../../../frontend/src/components/jetty/vessel/import/import-vessel-header-schema.ts", "../../../../../frontend/src/components/jetty/vessel/import/import-vessel-item-schema.ts", "../../../../../frontend/src/components/jetty/vessel/import/import-vessel-form.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button';\r\nimport type { BusinessPartnerDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport React from 'react';\r\nimport { createRoot } from 'react-dom/client';\r\nimport type { Root } from 'react-dom/client';\r\nimport { AttachmentDialog } from '../attachment-dialog';\r\nimport Handsontable from 'handsontable';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\n\r\n// Type guard for custom property on td\r\nfunction getRootContainer(td: unknown): Root | undefined {\r\n  return typeof td === 'object' && td !== null && '_reactRootContainer' in td\r\n    ? (td as { _reactRootContainer?: Root })._reactRootContainer\r\n    : undefined;\r\n}\r\nfunction setRootContainer(td: unknown, root: Root) {\r\n  if (typeof td === 'object' && td !== null) {\r\n    (td as { _reactRootContainer?: Root })._reactRootContainer = root;\r\n  }\r\n}\r\n\r\n// Renderer factory for attachment button\r\nexport const renderAttachmentButton = (queryClient: QueryClient) => {\r\n  if (!queryClient) throw new Error('queryClient is required for renderAttachmentButton');\r\n  return (\r\n    _instance: Handsontable.Core | undefined,\r\n    td: HTMLTableCellElement,\r\n    _row: number,\r\n    _col: number,\r\n    _prop: string | number,\r\n    _value: unknown,\r\n    _cellProperties: Handsontable.CellProperties\r\n  ) => {\r\n    void _col;\r\n    void _prop;\r\n    void _value;\r\n    void _cellProperties;\r\n    \r\n    // Check if there's already a React root for this td element\r\n    let root = getRootContainer(td);\r\n    \r\n    if (!root) {\r\n      // Only create a new root if one doesn't exist\r\n      root = createRoot(td);\r\n      setRootContainer(td, root);\r\n    }\r\n    \r\n    // Create a wrapper component to handle the dialog state\r\n    const AttachmentButton = () => {\r\n      const [open, setOpen] = React.useState(false);\r\n      // Get the vessel ID to fetch fresh data\r\n      const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);\r\n      const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};\r\n      const vesselId = rowData.vesselId || rowData.id;\r\n      \r\n      // Get fresh data from the query cache first, then fallback to Handsontable data\r\n      const freshData = queryClient.getQueryData(['import-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;\r\n      const freshAttachments = freshData?.items?.find((item) => item.id === rowData.id)?.attachments;\r\n      const attachments = freshAttachments || _instance?.getDataAtRowProp(_row, 'attachments') || [];\r\n      \r\n      const itemName = _instance?.getDataAtRowProp(_row, 'itemName') || '';\r\n      const referenceId = rowData.id ?? '';\r\n      const documentReferenceId = rowData.docEntry ?? 0;\r\n      \r\n      // Callback to refresh the table data after successful upload\r\n      const handleUploadSuccess = () => {\r\n        // Get the current vessel ID from the row data\r\n        const rowDataRaw = _instance?.getSourceDataAtRow?.(_row);\r\n        const rowData = (rowDataRaw && !Array.isArray(rowDataRaw)) ? rowDataRaw : {};\r\n        const vesselId = rowData.vesselId || rowData.id;\r\n        \r\n        // Invalidate the specific query with the vessel ID to refetch the vessel data with updated attachments\r\n        if (vesselId) {\r\n          queryClient.invalidateQueries({ queryKey: ['import-vessel', vesselId] });\r\n        } else {\r\n          // Fallback to invalidate all import-vessel queries\r\n          queryClient.invalidateQueries({ queryKey: ['import-vessel'] });\r\n        }\r\n        \r\n        // Wait for the query to complete and then force re-render\r\n        setTimeout(() => {\r\n          if (_instance) {\r\n            try {\r\n              // Get fresh data from the query cache and update Handsontable immediately\r\n              const freshData = queryClient.getQueryData(['import-vessel', vesselId]) as { items?: Array<{ id: string; attachments?: unknown[] }> } | undefined;\r\n              if (freshData && freshData.items) {\r\n                const freshItems = freshData.items;\r\n                const currentItemId = rowData.id;\r\n                const freshItem = freshItems.find((item) => item.id === currentItemId);\r\n                if (freshItem) {\r\n                  // Update the specific row with fresh data immediately\r\n                  _instance.setDataAtRowProp(_row, 'attachments', freshItem.attachments || []);\r\n                }\r\n              }\r\n              \r\n              // Force Handsontable to re-render the entire table to pick up updated data\r\n              _instance.render();\r\n              \r\n              // Force a complete re-render of the cell to update the attachment count\r\n              setTimeout(() => {\r\n                if (_instance) {\r\n                  try {\r\n                    // Re-render the cell to update the attachment count\r\n                    root.render(React.createElement(AttachmentButton));\r\n                  } catch (error) {\r\n                    console.warn('Error re-rendering cell:', error);\r\n                  }\r\n                }\r\n              }, 50); // Reduced delay since we already updated the data\r\n            } catch (error) {\r\n              console.warn('Error re-rendering Handsontable:', error);\r\n            }\r\n          }\r\n        }, 100); // Reduced delay to ensure query completion\r\n      };\r\n      \r\n      return React.createElement(React.Fragment, null, [\r\n        React.createElement(Button, {\r\n          key: 'button',\r\n          size: 'xs',\r\n          variant: 'success',\r\n          type: 'button',\r\n          onClick: () => setOpen(true),\r\n          'aria-label': 'View Attachments',\r\n          disabled: !attachments || attachments.length === 0,\r\n          children: `Attachment (${attachments?.length || 0})`\r\n        }),\r\n        React.createElement(AttachmentDialog, {\r\n          key: 'dialog',\r\n          open: open,\r\n          onOpenChange: setOpen,\r\n          attachments: attachments || [],\r\n          title: `Attachments - ${itemName || 'Item'}`,\r\n          queryClient,\r\n          referenceId,\r\n          documentReferenceId,\r\n          defaultTabName: 'AGENT',\r\n          docType: 'Export',\r\n          transType: 'ExportDetails',\r\n          tabName: 'AGENT',\r\n          onUploadSuccess: handleUploadSuccess,\r\n        })\r\n      ]);\r\n    };\r\n    \r\n    root.render(React.createElement(AttachmentButton));\r\n    return td;\r\n  };\r\n};\r\n\r\nexport const getImportVesselColumns = (tenants: MasterTenantDto[], businessPartners: BusinessPartnerDto[], queryClient: QueryClient) => {\r\n  // Extract tenant names for autocomplete source\r\n  const tenantNames = tenants.map(t => t.name ?? '').filter(name => name !== '');\r\n  \r\n  // Extract business partner names for autocomplete source\r\n  const businessPartnerNames = businessPartners.map(bp => bp.name ?? '').filter(name => name !== '');\r\n  \r\n  return [\r\n    { data: 'id', title: 'Id', type: 'text', width: 200 },\r\n    { data: 'docEntry', title: 'DocEntry', type: 'text', width: 200 },\r\n    { data: 'concurrencyStamp', title: 'concurrencyStamp', type: 'text', width: 200 },\r\n    { \r\n      data: 'tenant', \r\n      title: 'Tenant', \r\n      type: 'autocomplete', \r\n      width: 140, \r\n      source: tenantNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    { \r\n      data: 'businessPartner', \r\n      title: 'Business Partner', \r\n      type: 'autocomplete', \r\n      width: 290, \r\n      source: businessPartnerNames,\r\n      strict: false,\r\n      allowInvalid: false,\r\n      trimDropdown: false,\r\n      visibleRows: 6,\r\n    },\r\n    { data: 'itemName', title: 'Item Name', wordWrap: false, type: 'text', width: 200 },\r\n    { data: 'itemQty', title: 'Quantity', wordWrap: false, type: 'numeric', width: 80 },\r\n    { data: 'unitQty', title: 'UOM', wordWrap: false, type: 'text', width: 150 },\r\n    { data: 'remarks', title: 'Remark', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'letterNo', title: 'Letter No', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'letterDate', title: 'Letter Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'noBl', title: 'No BL', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'dateBl', title: 'Date BL', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'ajuNo', title: 'AJU No', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'regNo', title: 'Reg No', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'regDate', title: 'Reg Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppbNo', title: 'SPPB No', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'sppbDate', title: 'SPPB Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'sppdNo', title: 'SPPD No', wordWrap: false, type: 'text', width: 120 },\r\n    { data: 'sppdDate', title: 'SPPD Date', wordWrap: false, type: 'date', dateFormat: 'YYYY-MM-DD', width: 120, correctFormat: true },\r\n    { data: 'grossWeight', title: 'Gross Weight', wordWrap: false, type: 'numeric', width: 100 },\r\n    { data: 'unitWeight', title: 'Unit Weight', wordWrap: false, type: 'text', width: 100 },\r\n    { data: 'attachments', title: 'Attachment', width: 100, renderer: renderAttachmentButton(queryClient), readOnly: true, filterable: false },\r\n  ];\r\n}; ", "import * as z from 'zod';\r\n\r\nexport const importVesselHeaderSchema = z.object({\r\n  docNum: z.string().min(1, 'DocNum is required'),\r\n  vesselId: z.string().min(1, 'Vessel Name is required'),\r\n  voyage: z.string().min(1, 'Voyage is required'),\r\n  postingDate: z.string().min(1, 'Posting Date is required'),\r\n  vesselArrival: z.string().min(1, 'Arrival Date is required'),\r\n  vesselDeparture: z.string().min(1, 'Departure Date is required'),\r\n  portOriginId: z.string().optional(),\r\n  destinationPortId: z.string().optional(),\r\n  deleted: z.string().optional(),\r\n  docType: z.string().optional(),\r\n  isChange: z.string().optional(),\r\n  isLocked: z.string().optional(),\r\n  createdBy: z.string().optional(),\r\n  docStatus: z.string().optional(),\r\n  statusBms: z.string().optional(),\r\n  transType: z.string().optional(),\r\n  concurrencyStamp: z.string().optional(),\r\n  jettyId: z.string().min(1, 'Jetty is required'),\r\n  status: z.string().optional(),\r\n  vesselType: z.string().optional(),\r\n  shipment: z.string().optional(),\r\n  portOrigin: z.string().optional(),\r\n  destinationPort: z.string().optional(),\r\n  // Add more fields as needed from CreateUpdateImportVesselDto\r\n  asideDate: z.string().optional(),\r\n  castOfDate: z.string().optional(),\r\n});\r\n\r\nexport type ImportVesselHeaderForm = z.infer<typeof importVesselHeaderSchema>; ", "import * as z from 'zod';\r\nimport type { CreateUpdateVesselItemDto } from '@/clientEkb/types.gen';\r\n\r\nexport const importVesselItemSchema = z.object({\r\n  itemName: z.string().nullable().optional(),\r\n  itemQty: z.number().nullable().optional(),\r\n  unitQty: z.string().nullable().optional(),\r\n  remarks: z.string().nullable().optional(),\r\n  tenant: z.string().nullable().optional(),\r\n  tenantId: z.string().nullable().optional(),\r\n  businessPartner: z.string().nullable().optional(),\r\n  businessPartnerId: z.string().nullable().optional(),\r\n  // Add more fields as needed from CreateUpdateVesselItemDto\r\n});\r\n\r\nexport type ImportVesselItemForm = z.infer<typeof importVesselItemSchema> & Partial<CreateUpdateVesselItemDto>; ", "import { ekbProxyService } from '@/services/ekbProxyService';\r\nimport type { BusinessPartnerDto, CreateUpdateImportVesselDto, CreateUpdateVesselItemDto, MasterTenantDto } from '@/clientEkb/types.gen';\r\nimport { Button } from '@/components/ui/button';\r\nimport { FormField, FormSection } from '@/components/ui/FormField';\r\nimport { Input } from '@/components/ui/input';\r\nimport { HotTable, type HotTableRef } from '@handsontable/react-wrapper';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport type { QueryClient } from '@tanstack/react-query';\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { registerAllModules } from 'handsontable/registry';\r\nimport 'handsontable/styles/handsontable.min.css';\r\nimport 'handsontable/styles/ht-theme-horizon.css';\r\nimport 'handsontable/styles/ht-theme-main.min.css';\r\nimport type { ColumnSettings } from 'node_modules/handsontable/settings';\r\nimport { useEffect, useRef, useState } from 'react';\r\nimport { Controller, FormProvider, useForm } from 'react-hook-form';\r\nimport { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from '../export/async-selects';\r\nimport { getImportVesselColumns, renderAttachmentButton } from './import-vessel-columns';\r\nimport { type ImportVesselHeaderForm, importVesselHeaderSchema } from './import-vessel-header-schema';\r\nimport { type ImportVesselItemForm, importVesselItemSchema } from './import-vessel-item-schema';\r\nregisterAllModules();\r\n\r\nexport type ImportVesselFormProps = {\r\n  mode: 'create' | 'edit';\r\n  initialHeader: Partial<CreateUpdateImportVesselDto>;\r\n  initialItems: CreateUpdateVesselItemDto[];\r\n  onSubmit: (header: ImportVesselHeaderForm, items: ImportVesselItemForm[]) => Promise<void>;\r\n  columns?: ColumnSettings[];\r\n  headerSchema?: typeof importVesselHeaderSchema;\r\n  itemSchema?: typeof importVesselItemSchema;\r\n  isSubmitting?: boolean;\r\n  title?: string;\r\n  tenants: MasterTenantDto[];\r\n  businessPartners: BusinessPartnerDto[];\r\n  queryClient: QueryClient;\r\n  showAddLineButton?: boolean;\r\n};\r\n\r\nfunction toImportVesselHeaderForm(dto: Partial<CreateUpdateImportVesselDto>): ImportVesselHeaderForm {\r\n  return {\r\n    docNum: dto.docNum?.toString() ?? '',\r\n    vesselId: dto.vesselId ?? '',\r\n    voyage: dto.voyage ?? '',\r\n    postingDate: dto.postingDate ?? '',\r\n    vesselArrival: dto.vesselArrival ?? '',\r\n    vesselDeparture: dto.vesselDeparture ?? '',\r\n    portOriginId: dto.portOriginId ?? '',\r\n    destinationPortId: dto.destinationPortId ?? '',\r\n    jettyId: dto.jettyId ?? '',\r\n    deleted: dto.deleted ?? '',\r\n    docType: dto.docType ?? '',\r\n    isChange: dto.isChange ?? '',\r\n    isLocked: dto.isLocked ?? '',\r\n    createdBy: dto.createdBy ?? '',\r\n    docStatus: dto.docStatus ?? '',\r\n    statusBms: dto.statusBms ?? '',\r\n    transType: dto.transType ?? '',\r\n    concurrencyStamp: dto.concurrencyStamp ?? '',\r\n    asideDate: dto.asideDate ?? '',\r\n    castOfDate: dto.castOfDate ?? '',\r\n  };\r\n}\r\n\r\n// Extend ImportVesselItemForm to always include concurrencyStamp and id for form logic\r\ntype ImportVesselItemFormWithConcurrency = ImportVesselItemForm & { concurrencyStamp?: string; id?: string };\r\n\r\n// Wrapper component that handles data fetching\r\nexport const ImportVesselFormWithData: React.FC<Omit<ImportVesselFormProps, 'columns' | 'tenants' | 'businessPartners'> & { queryClient: QueryClient }> = (props) => {\r\n  const { data: tenants = [], isLoading: loadingTenants } = useQuery({\r\n    queryKey: ['tenants'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({\r\n    queryKey: ['businessPartners'],\r\n    queryFn: () =>\r\n      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })\r\n        .then(res => res.data?.items ?? []),\r\n  });\r\n\r\n  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;\r\n\r\n  return (\r\n    <ImportVesselForm\r\n      {...props}\r\n      columns={getImportVesselColumns(tenants, businessPartners, props.queryClient)}\r\n      tenants={tenants}\r\n      businessPartners={businessPartners}\r\n      queryClient={props.queryClient}\r\n    />\r\n  );\r\n};\r\n\r\n// Main form component that receives data as props\r\nconst ImportVesselForm: React.FC<ImportVesselFormProps> = ({\r\n  mode,\r\n  initialHeader,\r\n  initialItems,\r\n  onSubmit,\r\n  columns,\r\n  headerSchema = importVesselHeaderSchema,\r\n  itemSchema = importVesselItemSchema,\r\n  isSubmitting = false,\r\n  title = 'Create Import Vessel',\r\n  tenants,\r\n  businessPartners,\r\n  queryClient,\r\n  showAddLineButton = true,\r\n}) => {\r\n  const resolvedColumns = columns ?? getImportVesselColumns([], [], queryClient);\r\n  const [items, setItems] = useState<ImportVesselItemForm[]>(initialItems as ImportVesselItemForm[]);\r\n  const [itemErrors, setItemErrors] = useState<string[]>([]);\r\n  const hotTableRef = useRef<HotTableRef | null>(null);\r\n  const prevKey = useRef<string | undefined>(null);\r\n\r\n  const methods = useForm<ImportVesselHeaderForm>({\r\n    resolver: zodResolver(headerSchema),\r\n    defaultValues: toImportVesselHeaderForm(initialHeader),\r\n    mode: 'onBlur',\r\n  });\r\n  const { register, handleSubmit, formState: { errors }, reset } = methods;\r\n\r\n  useEffect(() => {\r\n    const key = String(initialHeader.docNum ?? '');\r\n    console.log(\"check key\", key, prevKey.current);\r\n    if (mode === 'edit' && key && prevKey.current !== key) {\r\n      reset(toImportVesselHeaderForm(initialHeader));\r\n      setItems(initialItems.map(i => ({ ...i, concurrencyStamp: i.concurrencyStamp ?? undefined, id: i.id ?? undefined })));\r\n      prevKey.current = key;\r\n    }\r\n  }, [initialHeader, initialItems, mode, reset]);\r\n\r\n  const validateItems = (data: ImportVesselItemForm[]): boolean => {\r\n    const errors: string[] = [];\r\n    data.forEach((item, idx) => {\r\n      const result = itemSchema.safeParse(item);\r\n      if (!result.success) {\r\n        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');\r\n      } else {\r\n        errors[idx] = '';\r\n      }\r\n    });\r\n    setItemErrors(errors);\r\n    return errors.every(e => !e);\r\n  };\r\n\r\n  const onFormSubmit = async (header: ImportVesselHeaderForm) => {\r\n    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as ImportVesselItemForm[];\r\n\r\n    // Transform tenant names to tenant IDs and business partner names to business partner IDs\r\n    const transformedItems = currentItems.map(item => {\r\n      const transformedItem: ImportVesselItemFormWithConcurrency = { ...item, concurrencyStamp: item.concurrencyStamp ?? undefined, id: item.id ?? undefined };\r\n\r\n      if (item.tenant) {\r\n        // Find tenant by name and set tenantId\r\n        const tenant = tenants.find(t => t.name === item.tenant);\r\n        if (tenant) {\r\n          transformedItem.tenantId = tenant.id || '';\r\n        }\r\n      }\r\n\r\n      if (item.businessPartner) {\r\n        // Find business partner by name and set businessPartnerId\r\n        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);\r\n        if (businessPartner) {\r\n          transformedItem.businessPartnerId = businessPartner.id || '';\r\n        }\r\n      }\r\n\r\n      // When preparing the PUT payload, always use the latest concurrencyStamp from initialItems (API data)\r\n      const latest = (initialItems as ImportVesselItemFormWithConcurrency[]).find(i => i.id === item.id);\r\n      if (latest && latest.concurrencyStamp) {\r\n        transformedItem.concurrencyStamp = latest.concurrencyStamp;\r\n      }\r\n\r\n      return transformedItem;\r\n    });\r\n\r\n    if (!validateItems(transformedItems)) return;\r\n    await onSubmit(header, transformedItems);\r\n  };\r\n\r\n  const handleAddLine = () => {\r\n    setItems(prev => [...prev, {} as ImportVesselItemForm]);\r\n  };\r\n\r\n  function handleApproval() {\r\n    // TODO: Implement approval logic\r\n  }\r\n\r\n  // Create column configuration with renderer\r\n  const columnConfig = resolvedColumns.map(col => {\r\n    if (col.data === 'id') {\r\n      return {\r\n        ...col,\r\n        renderer: renderAttachmentButton(queryClient)\r\n      };\r\n    }\r\n    return col;\r\n  });\r\n\r\n  // Get masterJetty from the selected jetty (if available)\r\n  type Jetty = { id: string; isCustomArea?: boolean };\r\n  const jettyList: Jetty[] = []; // TODO: replace with actual jetty list\r\n  const selectedJetty = jettyList.find(j => j.id === methods.watch('jettyId')) || null;\r\n\r\n  return (\r\n    <div className=\"w-full mx-auto\">\r\n      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>\r\n        <div className=\"mb-6\">\r\n          <h2 className=\"text-lg font-bold text-gray-800 dark:text-white\">{title}</h2>\r\n          <div className=\"h-1 w-16 bg-primary rounded mt-2 mb-4\" />\r\n        </div>\r\n        <FormProvider {...methods}>\r\n          <form onSubmit={handleSubmit(onFormSubmit)} className=\"space-y-6\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"DocNum\" labelWidth='100px'>\r\n                  <Input {...register('docNum')} disabled={mode === 'edit'} />\r\n                  {errors.docNum && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.docNum.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Vessel Name\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"vesselId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <VesselSelect\r\n                        value={field.value}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select vessel...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.vesselId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Voyage\" labelWidth='100px'>\r\n                  <Input {...register('voyage')} />\r\n                  {errors.voyage && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.voyage.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Jetty\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"jettyId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <JettySelect\r\n                        value={field.value}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select jetty...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.jettyId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.jettyId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"A/Side Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('asideDate')} />\r\n                  {errors.asideDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.asideDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Cast Of Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('castOfDate')} />\r\n                  {errors.castOfDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.castOfDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Arrival Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselArrival')} />\r\n                  {errors.vesselArrival && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselArrival.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Departure Date\" labelWidth='100px'>\r\n                  <Input type=\"datetime-local\" {...register('vesselDeparture')} />\r\n                  {errors.vesselDeparture && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.vesselDeparture.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n\r\n              <FormSection showDivider={false}>\r\n                <FormField label=\"Posting Date\" labelWidth='100px'>\r\n                  <Input type=\"date\" {...register('postingDate')} />\r\n                  {errors.postingDate && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.postingDate.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Port Origin\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"portOriginId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <PortOfLoadingSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select port origin...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.portOriginId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.portOriginId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n\r\n                <FormField label=\"Destination Port\" labelWidth='100px'>\r\n                  <Controller\r\n                    name=\"destinationPortId\"\r\n                    control={methods.control}\r\n                    render={({ field }) => (\r\n                      <DestinationPortSelect\r\n                        value={field.value ?? ''}\r\n                        onValueChange={field.onChange}\r\n                        placeholder=\"Select destination port...\"\r\n                        disabled={isSubmitting}\r\n                      />\r\n                    )}\r\n                  />\r\n                  {errors.destinationPortId && (\r\n                    <span className=\"text-red-500 text-xs\">{errors.destinationPortId.message as string}</span>\r\n                  )}\r\n                </FormField>\r\n              </FormSection>\r\n            </div>\r\n            <div className=\"mt-6\">\r\n              <label className=\"block font-medium mb-2\">Items</label>\r\n              <HotTable\r\n                ref={hotTableRef}\r\n                themeName=\"ht-theme-main\"\r\n                data={items}\r\n                columns={columnConfig}\r\n                colHeaders={columnConfig.map(col => col.title).filter((t): t is string => typeof t === 'string')}\r\n                rowHeaders={true}\r\n                height=\"50vh\"\r\n                licenseKey=\"non-commercial-and-evaluation\"\r\n                stretchH=\"all\"\r\n                contextMenu={true}\r\n                manualColumnResize={true}\r\n                manualRowResize={true}\r\n                autoColumnSize={false}\r\n                autoRowSize={false}\r\n                startRows={1}\r\n                dropdownMenu={true}\r\n                filters={true}\r\n                colWidths={80}\r\n                hiddenColumns={{\r\n                  copyPasteEnabled: true,\r\n                  indicators: true,\r\n                  columns: [0, 1, 2]\r\n                }}\r\n                width=\"100%\"\r\n                persistentState={true}\r\n              />\r\n              {itemErrors.some(e => e) && (\r\n                <div className=\"mt-2 text-red-500 text-xs\">\r\n                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}\r\n                </div>\r\n              )}\r\n            </div>\r\n            <div className=\"flex justify-end gap-2\">\r\n              {showAddLineButton && (\r\n                <Button type=\"button\" variant=\"outline\" onClick={handleAddLine} disabled={isSubmitting}>\r\n                  + Add Line\r\n                </Button>\r\n              )}\r\n              {/* Approval button logic */}\r\n              {typeof selectedJetty?.isCustomArea === 'boolean' && !selectedJetty.isCustomArea && (\r\n                <Button type=\"button\" variant=\"primary\" onClick={handleApproval} disabled={isSubmitting}>\r\n                  Submit Approval\r\n                </Button>\r\n              )}\r\n              <Button type=\"submit\" disabled={isSubmitting}>\r\n                {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </FormProvider>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ImportVesselForm; "], "names": ["getRootContainer", "td", "setRootContainer", "root", "renderAttachmentButton", "queryClient", "_instance", "_row", "_col", "_prop", "_value", "_cellProperties", "createRoot", "AttachmentButton", "open", "<PERSON><PERSON><PERSON>", "React", "rowDataRaw", "rowData", "vesselId", "attachments", "item", "itemName", "referenceId", "documentReferenceId", "handleUploadSuccess", "freshData", "freshItems", "currentItemId", "freshItem", "<PERSON><PERSON>", "AttachmentDialog", "getImportVesselColumns", "tenants", "businessPartners", "tenantNames", "t", "name", "businessPartnerNames", "bp", "importVesselHeaderSchema", "z.object", "z.string", "importVesselItemSchema", "z.number", "registerAllModules", "toImportVesselHeaderForm", "dto", "ImportVesselFormWithData", "props", "loadingTenants", "useQuery", "ekbProxyService", "res", "loadingBusinessPartners", "jsx", "ImportVesselForm", "mode", "initialHeader", "initialItems", "onSubmit", "columns", "headerSchema", "itemSchema", "isSubmitting", "title", "showAddLineButton", "resolvedColumns", "items", "setItems", "useState", "itemErrors", "setItemErrors", "hotTableRef", "useRef", "prev<PERSON><PERSON>", "methods", "useForm", "zodResolver", "register", "handleSubmit", "errors", "reset", "useEffect", "key", "i", "validateItems", "data", "idx", "result", "e", "onFormSubmit", "header", "transformedItems", "transformedItem", "tenant", "business<PERSON><PERSON>ner", "latest", "handleAddLine", "prev", "handleApproval", "columnConfig", "col", "<PERSON><PERSON><PERSON><PERSON>", "j", "jsxs", "FormProvider", "FormSection", "FormField", "Input", "Controller", "field", "VesselSelect", "JettySelect", "PortOfLoadingSelect", "DestinationPortSelect", "HotTable", "err"], "mappings": "miBAUA,SAASA,GAAiBC,EAA+B,CAChD,OAAA,OAAOA,GAAO,UAAYA,IAAO,MAAQ,wBAAyBA,EACpEA,EAAsC,oBACvC,MACN,CACA,SAASC,GAAiBD,EAAaE,EAAY,CAC7C,OAAOF,GAAO,UAAYA,IAAO,OAClCA,EAAsC,oBAAsBE,EAEjE,CAGa,MAAAC,EAA0BC,GAA6B,CAClE,GAAI,CAACA,EAAmB,MAAA,IAAI,MAAM,oDAAoD,EACtF,MAAO,CACLC,EACAL,EACAM,EACAC,EACAC,EACAC,EACAC,IACG,CAOC,IAAAR,EAAOH,GAAiBC,CAAE,EAEzBE,IAEHA,EAAOS,cAAWX,CAAE,EACpBC,GAAiBD,EAAIE,CAAI,GAI3B,MAAMU,EAAmB,IAAM,CAC7B,KAAM,CAACC,EAAMC,CAAO,EAAIC,EAAM,SAAS,EAAK,EAEtCC,EAAaX,GAAW,qBAAqBC,CAAI,EACjDW,EAAWD,GAAc,CAAC,MAAM,QAAQA,CAAU,EAAKA,EAAa,CAAC,EACrEE,EAAWD,EAAQ,UAAYA,EAAQ,GAKvCE,EAFYf,EAAY,aAAa,CAAC,gBAAiBc,CAAQ,CAAC,GAClC,OAAO,KAAME,GAASA,EAAK,KAAOH,EAAQ,EAAE,GAAG,aAC3CZ,GAAW,iBAAiBC,EAAM,aAAa,GAAK,CAAC,EAEvFe,EAAWhB,GAAW,iBAAiBC,EAAM,UAAU,GAAK,GAC5DgB,EAAcL,EAAQ,IAAM,GAC5BM,EAAsBN,EAAQ,UAAY,EAG1CO,EAAsB,IAAM,CAE1BR,MAAAA,EAAaX,GAAW,qBAAqBC,CAAI,EACjDW,EAAWD,GAAc,CAAC,MAAM,QAAQA,CAAU,EAAKA,EAAa,CAAC,EACrEE,EAAWD,EAAQ,UAAYA,EAAQ,GAGzCC,EACFd,EAAY,kBAAkB,CAAE,SAAU,CAAC,gBAAiBc,CAAQ,EAAG,EAGvEd,EAAY,kBAAkB,CAAE,SAAU,CAAC,eAAe,EAAG,EAI/D,WAAW,IAAM,CACf,GAAIC,EACE,GAAA,CAEF,MAAMoB,EAAYrB,EAAY,aAAa,CAAC,gBAAiBc,CAAQ,CAAC,EAClEO,GAAAA,GAAaA,EAAU,MAAO,CAChC,MAAMC,EAAaD,EAAU,MACvBE,EAAgBV,EAAQ,GACxBW,EAAYF,EAAW,KAAMN,GAASA,EAAK,KAAOO,CAAa,EACjEC,GAEFvB,EAAU,iBAAiBC,EAAM,cAAesB,EAAU,aAAe,EAAE,CAC7E,CAIFvB,EAAU,OAAO,EAGjB,WAAW,IAAM,CACf,GAAIA,EACE,GAAA,CAEFH,EAAK,OAAOa,EAAM,cAAcH,CAAgB,CAAC,OACnC,CAAA,GAIjB,EAAE,OACS,CAAA,GAIjB,GAAG,CACR,EAEA,OAAOG,EAAM,cAAcA,EAAM,SAAU,KAAM,CAC/CA,EAAM,cAAcc,EAAQ,CAC1B,IAAK,SACL,KAAM,KACN,QAAS,UACT,KAAM,SACN,QAAS,IAAMf,EAAQ,EAAI,EAC3B,aAAc,mBACd,SAAU,CAACK,GAAeA,EAAY,SAAW,EACjD,SAAU,eAAeA,GAAa,QAAU,CAAC,GAAA,CAClD,EACDJ,EAAM,cAAce,GAAkB,CACpC,IAAK,SACL,KAAAjB,EACA,aAAcC,EACd,YAAaK,GAAe,CAAC,EAC7B,MAAO,iBAAiBE,GAAY,MAAM,GAC1C,YAAAjB,EACA,YAAAkB,EACA,oBAAAC,EACA,eAAgB,QAChB,QAAS,SACT,UAAW,gBACX,QAAS,QACT,gBAAiBC,CAClB,CAAA,CAAA,CACF,CACH,EAEA,OAAAtB,EAAK,OAAOa,EAAM,cAAcH,CAAgB,CAAC,EAC1CZ,CACT,CACF,EAEa+B,EAAyB,CAACC,EAA4BC,EAAwC7B,IAA6B,CAEhI,MAAA8B,EAAcF,EAAQ,IAASG,GAAAA,EAAE,MAAQ,EAAE,EAAE,OAAeC,GAAAA,IAAS,EAAE,EAGvEC,EAAuBJ,EAAiB,IAAUK,GAAAA,EAAG,MAAQ,EAAE,EAAE,OAAeF,GAAAA,IAAS,EAAE,EAE1F,MAAA,CACL,CAAE,KAAM,KAAM,MAAO,KAAM,KAAM,OAAQ,MAAO,GAAI,EACpD,CAAE,KAAM,WAAY,MAAO,WAAY,KAAM,OAAQ,MAAO,GAAI,EAChE,CAAE,KAAM,mBAAoB,MAAO,mBAAoB,KAAM,OAAQ,MAAO,GAAI,EAChF,CACE,KAAM,SACN,MAAO,SACP,KAAM,eACN,MAAO,IACP,OAAQF,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CACE,KAAM,kBACN,MAAO,mBACP,KAAM,eACN,MAAO,IACP,OAAQG,EACR,OAAQ,GACR,aAAc,GACd,aAAc,GACd,YAAa,CACf,EACA,CAAE,KAAM,WAAY,MAAO,YAAa,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAClF,CAAE,KAAM,UAAW,MAAO,WAAY,SAAU,GAAO,KAAM,UAAW,MAAO,EAAG,EAClF,CAAE,KAAM,UAAW,MAAO,MAAO,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC3E,CAAE,KAAM,UAAW,MAAO,SAAU,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC9E,CAAE,KAAM,WAAY,MAAO,YAAa,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAClF,CAAE,KAAM,aAAc,MAAO,cAAe,SAAU,GAAO,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EACrI,CAAE,KAAM,OAAQ,MAAO,QAAS,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC1E,CAAE,KAAM,SAAU,MAAO,UAAW,SAAU,GAAO,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC7H,CAAE,KAAM,QAAS,MAAO,SAAU,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC5E,CAAE,KAAM,QAAS,MAAO,SAAU,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC5E,CAAE,KAAM,UAAW,MAAO,WAAY,SAAU,GAAO,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EAC/H,CAAE,KAAM,SAAU,MAAO,UAAW,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC9E,CAAE,KAAM,WAAY,MAAO,YAAa,SAAU,GAAO,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EACjI,CAAE,KAAM,SAAU,MAAO,UAAW,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EAC9E,CAAE,KAAM,WAAY,MAAO,YAAa,SAAU,GAAO,KAAM,OAAQ,WAAY,aAAc,MAAO,IAAK,cAAe,EAAK,EACjI,CAAE,KAAM,cAAe,MAAO,eAAgB,SAAU,GAAO,KAAM,UAAW,MAAO,GAAI,EAC3F,CAAE,KAAM,aAAc,MAAO,cAAe,SAAU,GAAO,KAAM,OAAQ,MAAO,GAAI,EACtF,CAAE,KAAM,cAAe,MAAO,aAAc,MAAO,IAAK,SAAUlC,EAAuBC,CAAW,EAAG,SAAU,GAAM,WAAY,EAAM,CAC3I,CACF,ECxMamC,GAA2BC,EAAS,CAC/C,OAAQC,EAAW,EAAA,IAAI,EAAG,oBAAoB,EAC9C,SAAUA,EAAW,EAAA,IAAI,EAAG,yBAAyB,EACrD,OAAQA,EAAW,EAAA,IAAI,EAAG,oBAAoB,EAC9C,YAAaA,EAAW,EAAA,IAAI,EAAG,0BAA0B,EACzD,cAAeA,EAAW,EAAA,IAAI,EAAG,0BAA0B,EAC3D,gBAAiBA,EAAW,EAAA,IAAI,EAAG,4BAA4B,EAC/D,aAAcA,EAAS,EAAE,SAAS,EAClC,kBAAmBA,EAAS,EAAE,SAAS,EACvC,QAASA,EAAS,EAAE,SAAS,EAC7B,QAASA,EAAS,EAAE,SAAS,EAC7B,SAAUA,EAAS,EAAE,SAAS,EAC9B,SAAUA,EAAS,EAAE,SAAS,EAC9B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAS,EAAE,SAAS,EAC/B,UAAWA,EAAS,EAAE,SAAS,EAC/B,iBAAkBA,EAAS,EAAE,SAAS,EACtC,QAASA,EAAW,EAAA,IAAI,EAAG,mBAAmB,EAC9C,OAAQA,EAAS,EAAE,SAAS,EAC5B,WAAYA,EAAS,EAAE,SAAS,EAChC,SAAUA,EAAS,EAAE,SAAS,EAC9B,WAAYA,EAAS,EAAE,SAAS,EAChC,gBAAiBA,EAAS,EAAE,SAAS,EAErC,UAAWA,EAAS,EAAE,SAAS,EAC/B,WAAYA,EAAS,EAAE,SAAS,CAClC,CAAC,EC1BYC,GAAyBF,EAAS,CAC7C,SAAUC,EAAE,EAAS,SAAA,EAAW,SAAS,EACzC,QAASE,GAAE,EAAS,SAAA,EAAW,SAAS,EACxC,QAASF,EAAE,EAAS,SAAA,EAAW,SAAS,EACxC,QAASA,EAAE,EAAS,SAAA,EAAW,SAAS,EACxC,OAAQA,EAAE,EAAS,SAAA,EAAW,SAAS,EACvC,SAAUA,EAAE,EAAS,SAAA,EAAW,SAAS,EACzC,gBAAiBA,EAAE,EAAS,SAAA,EAAW,SAAS,EAChD,kBAAmBA,EAAE,EAAS,SAAA,EAAW,SAAS,CAEpD,CAAC,ECODG,GAAmB,EAkBnB,SAASC,EAAyBC,EAAmE,CAC5F,MAAA,CACL,OAAQA,EAAI,QAAQ,SAAc,GAAA,GAClC,SAAUA,EAAI,UAAY,GAC1B,OAAQA,EAAI,QAAU,GACtB,YAAaA,EAAI,aAAe,GAChC,cAAeA,EAAI,eAAiB,GACpC,gBAAiBA,EAAI,iBAAmB,GACxC,aAAcA,EAAI,cAAgB,GAClC,kBAAmBA,EAAI,mBAAqB,GAC5C,QAASA,EAAI,SAAW,GACxB,QAASA,EAAI,SAAW,GACxB,QAASA,EAAI,SAAW,GACxB,SAAUA,EAAI,UAAY,GAC1B,SAAUA,EAAI,UAAY,GAC1B,UAAWA,EAAI,WAAa,GAC5B,UAAWA,EAAI,WAAa,GAC5B,UAAWA,EAAI,WAAa,GAC5B,UAAWA,EAAI,WAAa,GAC5B,iBAAkBA,EAAI,kBAAoB,GAC1C,UAAWA,EAAI,WAAa,GAC5B,WAAYA,EAAI,YAAc,EAChC,CACF,CAMa,MAAAC,GAA8IC,GAAU,CAC7J,KAAA,CAAE,KAAMhB,EAAU,CAAA,EAAI,UAAWiB,GAAmBC,EAAS,CACjE,SAAU,CAAC,SAAS,EACpB,QAAS,IACPC,EAAgB,cAAc,CAAE,KAAM,EAAG,eAAgB,GAAK,CAAC,EAC5D,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,CAAA,CAAA,CACvC,EAEK,CAAE,KAAMnB,EAAmB,CAAA,EAAI,UAAWoB,GAA4BH,EAAS,CACnF,SAAU,CAAC,kBAAkB,EAC7B,QAAS,IACPC,EAAgB,uBAAuB,CAAE,KAAM,EAAG,eAAgB,GAAM,CAAC,EACtE,KAAKC,GAAOA,EAAI,MAAM,OAAS,CAAE,CAAA,CAAA,CACvC,EAED,OAAIH,GAAkBI,EAAgCC,EAAAA,IAAC,OAAI,SAAe,kBAAA,EAGxEA,EAAA,IAACC,GAAA,CACE,GAAGP,EACJ,QAASjB,EAAuBC,EAASC,EAAkBe,EAAM,WAAW,EAC5E,QAAAhB,EACA,iBAAAC,EACA,YAAae,EAAM,WAAA,CACrB,CAEJ,EAGMO,GAAoD,CAAC,CACzD,KAAAC,EACA,cAAAC,EACA,aAAAC,EACA,SAAAC,EACA,QAAAC,EACA,aAAAC,EAAetB,GACf,WAAAuB,EAAapB,GACb,aAAAqB,EAAe,GACf,MAAAC,EAAQ,uBACR,QAAAhC,EACA,iBAAAC,EACA,YAAA7B,EACA,kBAAA6D,EAAoB,EACtB,IAAM,CACJ,MAAMC,EAAkBN,GAAW7B,EAAuB,CAAA,EAAI,CAAA,EAAI3B,CAAW,EACvE,CAAC+D,EAAOC,CAAQ,EAAIC,EAAAA,SAAiCX,CAAsC,EAC3F,CAACY,EAAYC,CAAa,EAAIF,EAAAA,SAAmB,CAAA,CAAE,EACnDG,EAAcC,SAA2B,IAAI,EAC7CC,EAAUD,SAA2B,IAAI,EAEzCE,EAAUC,GAAgC,CAC9C,SAAUC,GAAYhB,CAAY,EAClC,cAAehB,EAAyBY,CAAa,EACrD,KAAM,QAAA,CACP,EACK,CAAE,SAAAqB,EAAU,aAAAC,EAAc,UAAW,CAAE,OAAAC,CAAO,EAAG,MAAAC,GAAUN,EAEjEO,EAAAA,UAAU,IAAM,CACd,MAAMC,EAAM,OAAO1B,EAAc,QAAU,EAAE,EAEzCD,IAAS,QAAU2B,GAAOT,EAAQ,UAAYS,IAC1CF,EAAApC,EAAyBY,CAAa,CAAC,EAC7CW,EAASV,EAAa,IAAU0B,IAAA,CAAE,GAAGA,EAAG,iBAAkBA,EAAE,kBAAoB,OAAW,GAAIA,EAAE,IAAM,QAAY,CAAC,EACpHV,EAAQ,QAAUS,IAEnB,CAAC1B,EAAeC,EAAcF,EAAMyB,CAAK,CAAC,EAEvC,MAAAI,EAAiBC,GAA0C,CAC/D,MAAMN,EAAmB,CAAC,EACrB,OAAAM,EAAA,QAAQ,CAAClE,EAAMmE,IAAQ,CACpB,MAAAC,EAAS1B,EAAW,UAAU1C,CAAI,EACnCoE,EAAO,QAGVR,EAAOO,CAAG,EAAI,GAFdP,EAAOO,CAAG,EAAI,OAAO,OAAOC,EAAO,MAAM,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,IAAI,CAGlF,CACD,EACDjB,EAAcS,CAAM,EACbA,EAAO,MAAWS,GAAA,CAACA,CAAC,CAC7B,EAEMC,EAAe,MAAOC,GAAmC,CAIvD,MAAAC,GAHgBpB,EAAY,SAAS,aAAa,mBAAqB,CAAC,GAGxC,IAAYpD,GAAA,CAC1C,MAAAyE,EAAuD,CAAE,GAAGzE,EAAM,iBAAkBA,EAAK,kBAAoB,OAAW,GAAIA,EAAK,IAAM,MAAU,EAEvJ,GAAIA,EAAK,OAAQ,CAEf,MAAM0E,EAAS9D,EAAQ,QAAUG,EAAE,OAASf,EAAK,MAAM,EACnD0E,IACcD,EAAA,SAAWC,EAAO,IAAM,GAC1C,CAGF,GAAI1E,EAAK,gBAAiB,CAExB,MAAM2E,EAAkB9D,EAAiB,QAAWK,EAAG,OAASlB,EAAK,eAAe,EAChF2E,IACcF,EAAA,kBAAoBE,EAAgB,IAAM,GAC5D,CAIF,MAAMC,EAAUtC,EAAuD,QAAU0B,EAAE,KAAOhE,EAAK,EAAE,EAC7F,OAAA4E,GAAUA,EAAO,mBACnBH,EAAgB,iBAAmBG,EAAO,kBAGrCH,CAAA,CACR,EAEIR,EAAcO,CAAgB,GAC7B,MAAAjC,EAASgC,EAAQC,CAAgB,CACzC,EAEMK,EAAgB,IAAM,CAC1B7B,KAAiB,CAAC,GAAG8B,EAAM,CAA0B,CAAA,CAAC,CACxD,EAEA,SAASC,GAAiB,CAAA,CAKpB,MAAAC,EAAelC,EAAgB,IAAWmC,GAC1CA,EAAI,OAAS,KACR,CACL,GAAGA,EACH,SAAUlG,EAAuBC,CAAW,CAC9C,EAEKiG,CACR,EAKKC,EADqB,CAAC,EACI,KAAUC,GAAAA,EAAE,KAAO5B,EAAQ,MAAM,SAAS,CAAC,GAAK,KAEhF,aACG,MAAI,CAAA,UAAU,iBACb,SAAC6B,EAAA,KAAA,MAAA,CAAI,UAAU,qEACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAAClD,EAAA,IAAA,KAAA,CAAG,UAAU,kDAAmD,SAAMU,EAAA,EACvEV,EAAAA,IAAC,MAAI,CAAA,UAAU,uCAAwC,CAAA,CAAA,EACzD,EACAA,EAAAA,IAACmD,GAAc,CAAA,GAAG9B,EAChB,SAAA6B,EAAAA,KAAC,OAAK,CAAA,SAAUzB,EAAaW,CAAY,EAAG,UAAU,YACpD,SAAA,CAACc,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAACA,EAAAA,KAAAE,EAAA,CAAY,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,SAAS,WAAW,QACnC,SAAA,CAAArD,MAACsD,GAAO,GAAG9B,EAAS,QAAQ,EAAG,SAAUtB,IAAS,OAAQ,EACzDwB,EAAO,QACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,OAAO,OAAkB,CAAA,CAAA,EAE5E,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,SAAA,CAAArD,EAAA,IAACuD,EAAA,CACC,KAAK,WACL,QAASlC,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAmC,CAAA,IACTxD,EAAA,IAACyD,GAAA,CACC,MAAOD,EAAM,MACb,cAAeA,EAAM,SACrB,YAAY,mBACZ,SAAU/C,CAAA,CAAA,CACZ,CAEJ,EACCiB,EAAO,UACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,SAAS,OAAkB,CAAA,CAAA,EAE9E,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,SAAS,WAAW,QACnC,SAAA,CAAArD,EAAAA,IAACsD,EAAO,CAAA,GAAG9B,EAAS,QAAQ,CAAG,CAAA,EAC9BE,EAAO,QACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,OAAO,OAAkB,CAAA,CAAA,EAE5E,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,QAAQ,WAAW,QAClC,SAAA,CAAArD,EAAA,IAACuD,EAAA,CACC,KAAK,UACL,QAASlC,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAmC,CAAA,IACTxD,EAAA,IAAC0D,GAAA,CACC,MAAOF,EAAM,MACb,cAAeA,EAAM,SACrB,YAAY,kBACZ,SAAU/C,CAAA,CAAA,CACZ,CAEJ,EACCiB,EAAO,SACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,QAAQ,OAAkB,CAAA,CAAA,CAE7E,CAAA,CAAA,EACF,EAEAwB,EAAAA,KAACE,EAAY,CAAA,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,cAAc,WAAW,QACxC,SAAA,CAAArD,EAAA,IAACsD,GAAM,KAAK,iBAAkB,GAAG9B,EAAS,WAAW,EAAG,EACvDE,EAAO,WACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,UAAU,OAAkB,CAAA,CAAA,EAE/E,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,SAAA,CAAArD,EAAA,IAACsD,GAAM,KAAK,iBAAkB,GAAG9B,EAAS,YAAY,EAAG,EACxDE,EAAO,YACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,WAAW,OAAkB,CAAA,CAAA,EAEhF,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,eAAe,WAAW,QACzC,SAAA,CAAArD,EAAA,IAACsD,GAAM,KAAK,iBAAkB,GAAG9B,EAAS,eAAe,EAAG,EAC3DE,EAAO,eACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,cAAc,OAAkB,CAAA,CAAA,EAEnF,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,iBAAiB,WAAW,QAC3C,SAAA,CAAArD,EAAA,IAACsD,GAAM,KAAK,iBAAkB,GAAG9B,EAAS,iBAAiB,EAAG,EAC7DE,EAAO,iBACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,gBAAgB,OAAkB,CAAA,CAAA,CAErF,CAAA,CAAA,EACF,EAEAwB,EAAAA,KAACE,EAAY,CAAA,YAAa,GACxB,SAAA,CAAAF,EAAA,KAACG,EAAU,CAAA,MAAM,eAAe,WAAW,QACzC,SAAA,CAAArD,EAAA,IAACsD,GAAM,KAAK,OAAQ,GAAG9B,EAAS,aAAa,EAAG,EAC/CE,EAAO,aACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,YAAY,OAAkB,CAAA,CAAA,EAEjF,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,cAAc,WAAW,QACxC,SAAA,CAAArD,EAAA,IAACuD,EAAA,CACC,KAAK,eACL,QAASlC,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAmC,CAAA,IACTxD,EAAA,IAAC2D,GAAA,CACC,MAAOH,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,wBACZ,SAAU/C,CAAA,CAAA,CACZ,CAEJ,EACCiB,EAAO,cACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,aAAa,OAAkB,CAAA,CAAA,EAElF,EAECwB,EAAA,KAAAG,EAAA,CAAU,MAAM,mBAAmB,WAAW,QAC7C,SAAA,CAAArD,EAAA,IAACuD,EAAA,CACC,KAAK,oBACL,QAASlC,EAAQ,QACjB,OAAQ,CAAC,CAAE,MAAAmC,CAAA,IACTxD,EAAA,IAAC4D,GAAA,CACC,MAAOJ,EAAM,OAAS,GACtB,cAAeA,EAAM,SACrB,YAAY,6BACZ,SAAU/C,CAAA,CAAA,CACZ,CAEJ,EACCiB,EAAO,mBACL1B,MAAA,OAAA,CAAK,UAAU,uBAAwB,SAAA0B,EAAO,kBAAkB,OAAkB,CAAA,CAAA,CAEvF,CAAA,CAAA,CACF,CAAA,CAAA,EACF,EACAwB,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAAClD,EAAA,IAAA,QAAA,CAAM,UAAU,yBAAyB,SAAK,QAAA,EAC/CA,EAAA,IAAC6D,GAAA,CACC,IAAK3C,EACL,UAAU,gBACV,KAAML,EACN,QAASiC,EACT,WAAYA,EAAa,IAAIC,GAAOA,EAAI,KAAK,EAAE,OAAQlE,GAAmB,OAAOA,GAAM,QAAQ,EAC/F,WAAY,GACZ,OAAO,OACP,WAAW,gCACX,SAAS,MACT,YAAa,GACb,mBAAoB,GACpB,gBAAiB,GACjB,eAAgB,GAChB,YAAa,GACb,UAAW,EACX,aAAc,GACd,QAAS,GACT,UAAW,GACX,cAAe,CACb,iBAAkB,GAClB,WAAY,GACZ,QAAS,CAAC,EAAG,EAAG,CAAC,CACnB,EACA,MAAM,OACN,gBAAiB,EAAA,CACnB,EACCmC,EAAW,KAAKmB,GAAKA,CAAC,SACpB,MAAI,CAAA,UAAU,4BACZ,SAAAnB,EAAW,IAAI,CAAC8C,EAAK7B,IAAQ6B,UAAQ,MAAc,CAAA,SAAA,CAAA,OAAK7B,EAAM,EAAE,KAAG6B,CAAA,CAArB,EAAA7B,CAAyB,CAAM,CAChF,CAAA,CAAA,EAEJ,EACAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,yBACZ,SAAA,CACCvC,GAAAX,EAAAA,IAACzB,EAAO,CAAA,KAAK,SAAS,QAAQ,UAAU,QAASoE,EAAe,SAAUlC,EAAc,SAExF,YAAA,CAAA,EAGD,OAAOuC,GAAe,cAAiB,WAAa,CAACA,EAAc,cAClEhD,EAAAA,IAACzB,EAAO,CAAA,KAAK,SAAS,QAAQ,UAAU,QAASsE,EAAgB,SAAUpC,EAAc,SAEzF,kBAAA,EAEDT,EAAA,IAAAzB,EAAA,CAAO,KAAK,SAAS,SAAUkC,EAC7B,SAAAA,EAAgBP,IAAS,OAAS,YAAc,cAAkBA,IAAS,OAAS,eAAiB,QACxG,CAAA,CAAA,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAEJ"}