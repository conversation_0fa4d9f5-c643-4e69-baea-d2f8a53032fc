{"version": 3, "file": "popover-ChFN9yvN.js", "sources": ["../../../../../frontend/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverAnchor, PopoverContent, PopoverTrigger }\n\n"], "names": ["Popover", "props", "PopoverPrimitive.Root", "PopoverTrigger", "PopoverPrimitive.Trigger", "PopoverC<PERSON>nt", "className", "align", "sideOffset", "jsx", "PopoverPrimitive.Portal", "PopoverPrimitive.Content", "cn"], "mappings": "uJAOA,SAASA,EAAQ,CACf,GAAGC,CACL,EAAuD,CACrD,aAAQC,EAAA,CAAsB,YAAU,UAAW,GAAGD,EAAO,CAC/D,CAEA,SAASE,EAAe,CACtB,GAAGF,CACL,EAA0D,CACxD,aAAQG,EAAA,CAAyB,YAAU,kBAAmB,GAAGH,EAAO,CAC1E,CAEA,SAASI,EAAe,CACtB,UAAAC,EACA,MAAAC,EAAQ,SACR,WAAAC,EAAa,EACb,GAAGP,CACL,EAA0D,CAEtD,OAAAQ,MAACC,EAAA,CACC,SAAAD,EAAA,IAACE,EAAA,CACC,YAAU,kBACV,MAAAJ,EACA,WAAAC,EACA,UAAWI,EACT,ieACAN,CACF,EACC,GAAGL,CAAA,CAAA,EAER,CAEJ"}