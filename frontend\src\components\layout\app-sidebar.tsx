'use client';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail
} from '@/components/ui/sidebar';
import { AdminMenus, siteConfig } from "@/config";
import { useMediaQuery } from '@/hooks/use-media-query';
import { useCurrentUser } from '@/lib/hooks/useCurrentUser';
import { useGrantedPolicies } from '@/lib/hooks/useGrantedPolicies';
import { Link, usePage } from '@inertiajs/react';
import {
  IconChevronRight,
  IconInnerShadowTop,
} from '@tabler/icons-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { NavUser } from '../nav-user';

export default function AppSidebar() {
  const { can } = useGrantedPolicies();
  const currentUser = useCurrentUser();
  const { url } = usePage();
  const pathname = url;
  const { isOpen } = useMediaQuery();
  const { t } = useTranslation();

  React.useEffect(() => {
    // Side effects based on sidebar state changes
  }, [isOpen]);

  return (
    <Sidebar collapsible='icon'>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="#">
                <IconInnerShadowTop className="!size-5" />
                <span className="text-base font-semibold">{siteConfig.name}</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className='overflow-x-hidden'>
        <SidebarGroup>
          <SidebarGroupLabel>{t('menu.dashboard')}</SidebarGroupLabel>
          <SidebarMenu>
            {AdminMenus.map((item) => {
              const isParentActive = item.items?.some(subItem => pathname.startsWith(subItem.url));
              return item?.items && item?.items?.length > 0 && (!item.permission || can(item.permission)) ? (
                <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={isParentActive || item.isActive}
                  className='group/collapsible'
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        tooltip={t(item.title)}
                        isActive={pathname === item.url}
                      >
                        {item.icon && <item.icon />}
                        <span>{t(item.title)}</span>
                        <IconChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub>
                        {item.items?.map((subItem) => (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={pathname === subItem.url}
                            >
                              <Link href={subItem.url}>
                                <span>{t(subItem.title)}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ) : (
                (!item.permission || can(item.permission)) && (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      tooltip={t(item.title)}
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <item.icon />
                        <span>{t(item.title)}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              );
            })}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        {currentUser && <NavUser user={currentUser} />}
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
