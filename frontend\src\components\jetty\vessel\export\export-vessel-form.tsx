import { ekbProxyService } from '@/services/ekbProxyService';
import type { BusinessPartnerDto, CreateUpdateExportVesselDto, CreateUpdateVesselItemDto, GenerateDocNumDto, MasterTenantDto } from '@/clientEkb/types.gen';
import { Button } from '@/components/ui/button';
import { FormField, FormSection } from '@/components/ui/FormField';
import { Input } from '@/components/ui/input';
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from '@inertiajs/react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { registerAllModules } from 'handsontable/registry';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-horizon.css';
import 'handsontable/styles/ht-theme-main.min.css';
import type { ColumnSettings } from 'node_modules/handsontable/settings';
import { useEffect, useRef, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { DestinationPortSelect, JettySelect, PortOfLoadingSelect, VesselSelect } from './async-selects';
import { getExportVesselColumns } from './export-vessel-columns';
import { type ExportVesselHeaderForm, exportVesselHeaderSchema } from './export-vessel-header-schema';
import { type ExportVesselItemForm, exportVesselItemSchema } from './export-vessel-item-schema';
import { useCurrentUser } from '@/lib/hooks/useCurrentUser';
registerAllModules();

export type ExportVesselFormProps = {
  mode: 'create' | 'edit';
  initialHeader: Partial<Record<keyof ExportVesselHeaderForm, string>>;
  initialItems: ExportVesselItemForm[];
  onSubmit: (header: ExportVesselHeaderForm, items: ExportVesselItemForm[]) => Promise<void>;
  columns?: ColumnSettings[];
  headerSchema?: typeof exportVesselHeaderSchema;
  itemSchema?: typeof exportVesselItemSchema;
  isSubmitting?: boolean;
  tenants: MasterTenantDto[];
  businessPartners: BusinessPartnerDto[];
  loadingTenants: boolean;
  loadingBusinessPartners: boolean;
  title?: string;
  showAddLineButton?: boolean;
};

function toExportVesselHeaderForm(dto: Partial<Record<keyof ExportVesselHeaderForm, string>>): ExportVesselHeaderForm {
  return {
    docNum: dto.docNum ?? '',
    vesselId: dto.vesselId ?? '',
    voyage: dto.voyage ?? '',
    postingDate: dto.postingDate ?? '',
    vesselArrival: dto.vesselArrival ?? '',
    vesselDeparture: dto.vesselDeparture ?? '',
    portOriginId: dto.portOriginId ?? '',
    destinationPortId: dto.destinationPortId ?? '',
    jettyId: dto.jettyId ?? '',
    asideDate: dto.asideDate ?? '',
    castOfDate: dto.castOfDate ?? '',
  };
}

function handleApproval() {
  // TODO: Implement approval logic
}

// Extend ExportVesselItemForm to always include concurrencyStamp and id for form logic
type ExportVesselItemFormWithConcurrency = ExportVesselItemForm & { concurrencyStamp?: string; id?: string };

// Wrapper component that handles data fetching
export const ExportVesselFormWithData: React.FC<Omit<ExportVesselFormProps, 'tenants' | 'businessPartners' | 'loadingTenants' | 'loadingBusinessPartners'>> = (props) => {
  const { data: tenants = [], isLoading: loadingTenants } = useQuery({
    queryKey: ['tenants'],
    queryFn: () =>
      ekbProxyService.filterTenants({ page: 1, maxResultCount: 1000 })
        .then(res => res.data?.items ?? []),
  });

  const { data: businessPartners = [], isLoading: loadingBusinessPartners } = useQuery({
    queryKey: ['businessPartners'],
    queryFn: () =>
      ekbProxyService.filterBusinessPartners({ page: 1, maxResultCount: 10000 })
        .then(res => res.data?.items ?? []),
  });

  if (loadingTenants || loadingBusinessPartners) return <div>Loading data...</div>;

  return (
    <ExportVesselForm
      {...props}
      tenants={tenants}
      businessPartners={businessPartners}
      loadingTenants={loadingTenants}
      loadingBusinessPartners={loadingBusinessPartners}
    />
  );
};

// Main form component that receives data as props
const ExportVesselForm: React.FC<ExportVesselFormProps> = ({
  mode,
  initialHeader,
  initialItems,
  onSubmit,
  headerSchema = exportVesselHeaderSchema,
  itemSchema = exportVesselItemSchema,
  isSubmitting = false,
  tenants,
  businessPartners,
  title = 'Create Export Vessel',
  showAddLineButton = true,
}) => {
  const [items, setItems] = useState<ExportVesselItemForm[]>(initialItems);
  const [itemErrors, setItemErrors] = useState<string[]>([]);
  const hotTableRef = useRef<HotTableRef | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [docNum, setDocNum] = useState<string>('');
  const prevHeaderString = useRef<string | undefined>(undefined);
  const prevItemsString = useRef<string | undefined>(undefined);
  const currentUser = useCurrentUser();
  const { t } = useTranslation();

  const methods = useForm<ExportVesselHeaderForm>({
    resolver: zodResolver(headerSchema),
    defaultValues: {
      ...toExportVesselHeaderForm(initialHeader),
      docNum: mode === 'create' ? docNum : initialHeader.docNum ?? '',
    },
    mode: 'onBlur',
  });
  const { register, handleSubmit, formState: { errors }, reset, setValue } = methods;

  // Get postDate from form or use current date
  const watchedPostDate = methods.watch('vesselArrival');
  const getCurrentDate = () => {
    const d = new Date();
    return d.toISOString().slice(0, 10);
  };
  const postDate = watchedPostDate || getCurrentDate();

  // Fetch docNum only in create mode, and when postDate changes
  const { data: generatedDocNum } = useQuery<GenerateDocNumDto, Error>({
    queryKey: ['generateDocNum', postDate, mode],
    queryFn: () => ekbProxyService.generateNextExportVesselDocNum(postDate).then(res => res.data ?? {}),
    enabled: mode === 'create' && !!postDate,
  });

  useEffect(() => {
    if (mode === 'create' && generatedDocNum) {
      setDocNum(generatedDocNum.docNum ?? '');
    }
  }, [generatedDocNum, mode]);

  // Keep docNum in sync with fetched value in create mode
  useEffect(() => {
    if (mode === 'create' && docNum) {
      setValue('docNum', docNum);
    }
  }, [docNum, mode, setValue]);

  // Auto-select destination port for create mode
  const { data: destinationPorts } = useQuery({
    queryKey: ['destination-ports-for-auto-select'],
    queryFn: async () => {
      const filterRequest = {
        maxResultCount: 1000, // Get all ports to find the specific one
        skipCount: 0,
        filterGroup: {
          operator: 'And' as const,
          conditions: [
            { fieldName: 'name', operator: 'Equals' as const, value: 'PT. BINTANGDELAPAN MINERAL' }
          ]
        }
      };
      const res = await ekbProxyService.filterDestinationPorts(filterRequest);
      return res.data?.items ?? [];
    },
    enabled: mode === 'create', // Only fetch when in create mode
  });

  // Auto-set destination port when in create mode and port is found
  useEffect(() => {
    if (mode === 'create' && destinationPorts && destinationPorts.length > 0) {
      const targetPort = destinationPorts.find(port => port.name === 'PT. BINTANGDELAPAN MINERAL');
      if (targetPort && targetPort.id && !methods.watch('destinationPortId')) {
        // Only set if destination port is not already set
        setValue('destinationPortId', targetPort.id);
      }
    }
  }, [mode, destinationPorts, setValue, methods]);

  // Auto-set posting date to today when in create mode
  useEffect(() => {
    if (mode === 'create' && !methods.watch('postingDate')) {
      const today = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
      setValue('postingDate', today);
    }
  }, [mode, setValue, methods]);

  useEffect(() => {
    const headerString = JSON.stringify(initialHeader);
    const itemsString = JSON.stringify(initialItems);
    if (
      mode === 'edit' &&
      ((headerString && prevHeaderString.current !== headerString) ||
        (itemsString && prevItemsString.current !== itemsString))
    ) {
      reset(toExportVesselHeaderForm(initialHeader));
      setItems(
        initialItems.map(i => ({
          ...i,
          concurrencyStamp: (i as ExportVesselItemFormWithConcurrency).concurrencyStamp ?? undefined,
          id: (i as ExportVesselItemFormWithConcurrency).id ?? undefined,
        })) as ExportVesselItemFormWithConcurrency[]
      );
      prevHeaderString.current = headerString;
      prevItemsString.current = itemsString;
    }
  }, [initialHeader, initialItems, mode, reset]);

  // Cleanup effect to prevent accessing destroyed Handsontable instance
  useEffect(() => {
    return () => {
      // Cleanup any pending timeouts or operations when component unmounts
      if (hotTableRef.current?.hotInstance) {
        try {
          // The Handsontable instance will be automatically destroyed by the React wrapper
          // but we can clear any references to prevent memory leaks
          hotTableRef.current = null;
        } catch (error) {
          console.warn('Error during Handsontable cleanup:', error);
        }
      }
    };
  }, []);

  const validateItems = (data: ExportVesselItemForm[]): boolean => {
    const errors: string[] = [];
    data.forEach((item, idx) => {
      const result = itemSchema.safeParse(item);
      if (!result.success) {
        errors[idx] = Object.values(result.error.flatten().fieldErrors).flat().join(', ');
      } else {
        errors[idx] = '';
      }
    });
    setItemErrors(errors);
    return errors.every(e => !e);
  };

  // Utility to map nulls/undefined to empty string for required string fields
  const mapRequiredFields = <T extends Record<string, unknown>>(obj: T, requiredKeys: string[]): T => {
    const result: Record<string, unknown> = { ...obj };
    for (const key of requiredKeys) {
      if (result[key] === undefined || result[key] === null) {
        result[key] = '';
      }
    }
    // For all string | null fields, set to '' if null
    for (const key in result) {
      if (typeof result[key] === 'string' || result[key] === null) {
        result[key] = result[key] ?? '';
      }
    }
    return result as T;
  };

  // Type guard for result with id
  function hasId(obj: unknown): obj is { id: string | number } {
    return typeof obj === 'object' && obj !== null && 'id' in obj && (typeof (obj as { id?: unknown }).id === 'string' || typeof (obj as { id?: unknown }).id === 'number');
  }

  const onFormSubmit = async (header: ExportVesselHeaderForm) => {
    const currentItems = (hotTableRef.current?.hotInstance?.getSourceData?.() ?? []) as ExportVesselItemForm[];

    // Backend-required fields for items and header
    const requiredKeys = [
      'deleted', 'docType', 'shipment', 'docStatus', 'statusBms', 'portOrigin', 'concurrencyStamp',
      'tenantId', 'businessPartnerId', 'itemName', 'unitQty', 'remarks'
    ];
    const requiredFields: Record<string, string> = {
      deleted: 'N',
      docType: '',
      shipment: '',
      docStatus: '',
      statusBms: '',
      portOrigin: '',
      concurrencyStamp: '',
      tenantId: '',
      businessPartnerId: '',
      itemName: '',
      unitQty: '',
      remarks: '',
    };

    // Transform tenant names to tenant IDs and business partner names to business partner IDs
    const transformedItems: CreateUpdateVesselItemDto[] = currentItems.map(item => {
      const transformedItem = { ...item } as ExportVesselItemFormWithConcurrency;

      if (item.tenant) {
        const tenant = tenants.find(t => t.name === item.tenant);
        transformedItem.tenantId = tenant?.id || '';
      } else {
        transformedItem.tenantId = '';
      }

      if (item.businessPartner) {
        const businessPartner = businessPartners.find(bp => bp.name === item.businessPartner);
        transformedItem.businessPartnerId = businessPartner?.id || '';
      } else {
        transformedItem.businessPartnerId = '';
      }
      // Set date fields to undefined if falsy or empty string (for backend compatibility)
      if (!transformedItem.shippingInstructionDate) transformedItem.shippingInstructionDate = undefined;
      if (!transformedItem.letterDate) transformedItem.letterDate = undefined;
      if (!transformedItem.regDate) transformedItem.regDate = undefined;
      const itemWithRequired = {
        ...requiredFields,
        ...transformedItem,
      };
      const latest = (initialItems as ExportVesselItemFormWithConcurrency[]).find(i => i.id === transformedItem.id);
      if (latest && latest.concurrencyStamp) {
        transformedItem.concurrencyStamp = latest.concurrencyStamp;
      }
      return mapRequiredFields(itemWithRequired, requiredKeys) as CreateUpdateVesselItemDto;
    });

    // Ensure docNum is string
    const headerWithRequiredFields: CreateUpdateExportVesselDto = mapRequiredFields({
      ...requiredFields,
      ...header,
      docNum: String(header.docNum ?? ''),
    }, requiredKeys);

    if (!validateItems(transformedItems as ExportVesselItemForm[])) return;
    setSubmitError(null);
    try {
      // Await the result and expect the created ID in the result
      type SubmitResult = { id?: string | number } | void;
      const result: SubmitResult = await onSubmit(headerWithRequiredFields as ExportVesselHeaderForm, transformedItems as ExportVesselItemForm[]);
      // Only reset after success
      methods.reset();
      // Redirect to edit page if result has id
      if (hasId(result)) {
        router.visit(`/export-vessel/edit/${result.id}`);
      }
    } catch (error: unknown) {
      let message = 'An error occurred';
      if (typeof error === 'object' && error && 'message' in error && typeof (error as { message?: unknown }).message === 'string') {
        message = (error as { message: string }).message;
      }
      setSubmitError(message);
      // Do not reset form
    }
  };

  const handleAddLine = () => {
    setItems(prev => [...prev, {} as ExportVesselItemForm]);
  };

  const queryClient = useQueryClient();
  const columns = getExportVesselColumns(tenants, businessPartners, queryClient);

  // Define a minimal Jetty type and get the jetty list (replace with your actual jetty list source)
  type Jetty = { id: string; isCustomArea?: boolean };
  const jettyList: Jetty[] = []; // TODO: replace with actual jetty list
  const selectedJetty = jettyList.find(j => j.id === methods.watch('jettyId')) || null;

  return (
    <div className="w-full mx-auto">
      <div className='bg-card text-card-foreground rounded-lg border shadow-sm px-4 py-4'>
        <div className="mb-6">
          <h2 className="text-lg font-bold text-gray-800 dark:text-white">{title}</h2>
          <div className="h-1 w-16 bg-primary rounded mt-2 mb-4" />
        </div>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
            {submitError && (
              <div className="text-red-500 text-sm mb-2">{submitError}</div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormSection showDivider={false}>
                <FormField label={t('form.labels.docNum')} labelWidth='100px'>
                  <Input
                    {...register('docNum')}
                    value={methods.watch('docNum')}
                    readOnly={mode === 'create'}
                    disabled={mode === 'edit'}
                  />
                  {errors.docNum && (
                    <span className="text-red-500 text-xs">{errors.docNum.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.vesselName')} labelWidth='100px'>
                  <Controller
                    name="vesselId"
                    control={methods.control}
                    render={({ field }) => (
                      <VesselSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select vessel..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.vesselId && (
                    <span className="text-red-500 text-xs">{errors.vesselId.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.voyage')} labelWidth='100px'>
                  <Input {...register('voyage')} />
                  {errors.voyage && (
                    <span className="text-red-500 text-xs">{errors.voyage.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.jetty')} labelWidth='100px'>
                  <Controller
                    name="jettyId"
                    control={methods.control}
                    render={({ field }) => (
                      <JettySelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select jetty..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.jettyId && (
                    <span className="text-red-500 text-xs">{errors.jettyId.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label={t('form.labels.asideDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('asideDate')} />
                  {errors.asideDate && (
                    <span className="text-red-500 text-xs">{errors.asideDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.castOfDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('castOfDate')} />
                  {errors.castOfDate && (
                    <span className="text-red-500 text-xs">{errors.castOfDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.arrivalDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselArrival')} />
                  {errors.vesselArrival && (
                    <span className="text-red-500 text-xs">{errors.vesselArrival.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.departureDate')} labelWidth='100px'>
                  <Input type="datetime-local" {...register('vesselDeparture')} />
                  {errors.vesselDeparture && (
                    <span className="text-red-500 text-xs">{errors.vesselDeparture.message as string}</span>
                  )}
                </FormField>
              </FormSection>

              <FormSection showDivider={false}>
                <FormField label={t('form.labels.postingDate')} labelWidth='100px'>
                  <Input type="date" {...register('postingDate')} />
                  {errors.postingDate && (
                    <span className="text-red-500 text-xs">{errors.postingDate.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.portOrigin')} labelWidth='100px'>
                  <Controller
                    name="portOriginId"
                    control={methods.control}
                    render={({ field }) => (
                      <PortOfLoadingSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select port origin..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.portOriginId && (
                    <span className="text-red-500 text-xs">{errors.portOriginId.message as string}</span>
                  )}
                </FormField>

                <FormField label={t('form.labels.destinationPort')} labelWidth='100px'>
                  <Controller
                    name="destinationPortId"
                    control={methods.control}
                    render={({ field }) => (
                      <DestinationPortSelect
                        value={field.value ?? ''}
                        onValueChange={field.onChange}
                        placeholder="Select destination port..."
                        disabled={isSubmitting}
                      />
                    )}
                  />
                  {errors.destinationPortId && (
                    <span className="text-red-500 text-xs">{errors.destinationPortId.message as string}</span>
                  )}
                </FormField>
              </FormSection>
            </div>
            {/* {currentUser && currentUser?.roles?.includes('Jetty Approval Show Items') && ( */}
            <div className="mt-2">
              <label className="block font-medium mb-2">Items</label>
              <HotTable
                ref={hotTableRef}
                themeName="ht-theme-main"
                data={items}
                columns={columns}
                colHeaders={columns.map((col: { title?: string }) => col.title).filter((t: string | undefined): t is string => typeof t === 'string')}
                rowHeaders={true}
                height="50vh"
                licenseKey="non-commercial-and-evaluation"
                stretchH="all"
                contextMenu={true}
                manualColumnResize={true}
                manualRowResize={true}
                autoColumnSize={false}
                autoRowSize={false}
                startRows={1}
                dropdownMenu={true}
                filters={true}
                colWidths={80}
                hiddenColumns={{
                  copyPasteEnabled: true,
                  indicators: true,
                  columns: [0, 1, 2]
                }}
                width="100%"
                persistentState={true}
              />
              {itemErrors.some(e => e) && (
                <div className="mt-2 text-red-500 text-xs">
                  {itemErrors.map((err, idx) => err && <div key={idx}>Row {idx + 1}: {err}</div>)}
                </div>
              )}
            </div>
            {/* )} */}
            <div className="flex justify-end gap-2">
              {currentUser && ['Jetty Approval Admin', 'Jetty Approval Shipping', 'admin'].some(role => currentUser?.roles?.includes(role)) && (
                <>
                  {showAddLineButton && (
                    <Button type="button" variant="outline" onClick={handleAddLine} disabled={isSubmitting}>
                      + Add Line
                    </Button>
                  )}
                  {typeof selectedJetty?.isCustomArea === 'boolean' && !selectedJetty.isCustomArea && (
                    <Button type="button" variant="primary" onClick={handleApproval} disabled={isSubmitting}>
                      Submit Approval
                    </Button>
                  )}
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? (mode === 'edit' ? 'Saving...' : 'Creating...') : (mode === 'edit' ? 'Save Changes' : 'Create')}
                  </Button>
                </>
              )}
            </div>
          </form>
        </FormProvider>

      </div>
    </div>
  );
};

export default ExportVesselForm;