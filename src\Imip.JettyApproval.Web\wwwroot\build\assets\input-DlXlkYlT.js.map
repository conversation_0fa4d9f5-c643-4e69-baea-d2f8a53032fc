{"version": 3, "file": "input-DlXlkYlT.js", "sources": ["../../../../../frontend/src/components/ui/input.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport * as React from 'react';\n\n// Define input size variants\nconst inputVariants = cva(\n  `\n    flex w-full bg-background border border-input shadow-xs shadow-black/5 transition-[color,box-shadow] text-foreground placeholder:text-muted-foreground/80 \n    focus-visible:ring-ring/30  focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px]     \n    disabled:cursor-not-allowed disabled:opacity-60 \n    [&[readonly]]:bg-muted/80 [&[readonly]]:cursor-not-allowed\n    file:h-full [&[type=file]]:py-0 file:border-solid file:border-input file:bg-transparent \n    file:font-medium file:not-italic file:text-foreground file:p-0 file:border-0 file:border-e\n    aria-invalid:border-destructive/60 aria-invalid:ring-destructive/10 dark:aria-invalid:border-destructive dark:aria-invalid:ring-destructive/20\n  `,\n  {\n    variants: {\n      variant: {\n        lg: 'h-10 px-4 text-sm rounded-md file:pe-4 file:me-4',\n        md: 'h-8 px-3 text-[0.8125rem] leading-(--text-sm--line-height) rounded-md file:pe-3 file:me-3',\n        sm: 'h-7 px-2.5 py-2.5 text-xs rounded file:pe-2.5 file:me-2.5',\n      },\n    },\n    defaultVariants: {\n      variant: 'sm',\n    },\n  },\n);\n\nconst inputAddonVariants = cva(\n  'flex items-center shrink-0 justify-center bg-muted border border-input shadow-xs shadow-[rgba(0,0,0,0.05)] text-secondary-foreground [&_svg]:text-secondary-foreground/60',\n  {\n    variants: {\n      variant: {\n        sm: 'rounded h-7 min-w-7 text-xs px-2.5 [&_svg:not([class*=size-])]:size-3.5',\n        md: 'rounded-md h-8.5 min-w-8.5 px-3 text-[0.8125rem] leading-(--text-sm--line-height) [&_svg:not([class*=size-])]:size-4.5',\n        lg: 'rounded-md h-10 min-w-10 px-4 text-sm [&_svg:not([class*=size-])]:size-4.5',\n      },\n      mode: {\n        default: '',\n        icon: 'px-0 justify-center',\n      },\n    },\n    defaultVariants: {\n      variant: 'md',\n      mode: 'default',\n    },\n  },\n);\n\nconst inputGroupVariants = cva(\n  `\n    flex items-stretch\n    [&_[data-slot=input]]:grow\n    [&_[data-slot=input-addon]:has(+[data-slot=input])]:rounded-e-none [&_[data-slot=input-addon]:has(+[data-slot=input])]:border-e-0\n    [&_[data-slot=input-addon]:has(+[data-slot=datefield])]:rounded-e-none [&_[data-slot=input-addon]:has(+[data-slot=datefield])]:border-e-0 \n    [&_[data-slot=input]+[data-slot=input-addon]]:rounded-s-none [&_[data-slot=input]+[data-slot=input-addon]]:border-s-0\n    [&_[data-slot=input-addon]:has(+[data-slot=button])]:rounded-e-none\n    [&_[data-slot=input]+[data-slot=button]]:rounded-s-none\n    [&_[data-slot=button]+[data-slot=input]]:rounded-s-none\n    [&_[data-slot=input-addon]+[data-slot=input]]:rounded-s-none\n    [&_[data-slot=input-addon]+[data-slot=datefield]]:[&_[data-slot=input]]:rounded-s-none\n    [&_[data-slot=datefield]:has(+[data-slot=input-addon])]:[&_[data-slot=input]]:rounded-e-none\n    [&_[data-slot=input]:has(+[data-slot=button])]:rounded-e-none\n    [&_[data-slot=input]:has(+[data-slot=input-addon])]:rounded-e-none\n    [&_[data-slot=datefield]]:grow\n    [&_[data-slot=datefield]+[data-slot=input-addon]]:rounded-s-none [&_[data-slot=datefield]+[data-slot=input-addon]]:border-s-0\n  `,\n  {\n    variants: {},\n    defaultVariants: {},\n  },\n);\n\nconst inputWrapperVariants = cva(\n  `\n    flex items-center gap-1.5\n    has-[:focus-visible]:ring-ring/30 \n    has-[:focus-visible]:border-ring\n    has-[:focus-visible]:outline-none \n    has-[:focus-visible]:ring-[3px]\n\n    [&_[data-slot=datefield]]:grow \n    [&_[data-slot=input]]:data-focus-within:ring-transparent  \n    [&_[data-slot=input]]:data-focus-within:ring-0 \n    [&_[data-slot=input]]:data-focus-within:border-0 \n    [&_[data-slot=input]]:flex \n    [&_[data-slot=input]]:w-full \n    [&_[data-slot=input]]:outline-none \n    [&_[data-slot=input]]:transition-colors \n    [&_[data-slot=input]]:text-foreground\n    [&_[data-slot=input]]:placeholder:text-muted-foreground \n    [&_[data-slot=input]]:border-0 \n    [&_[data-slot=input]]:bg-transparent \n    [&_[data-slot=input]]:p-0\n    [&_[data-slot=input]]:shadow-none \n    [&_[data-slot=input]]:focus-visible:ring-0 \n    [&_[data-slot=input]]:h-auto \n    [&_[data-slot=input]]:disabled:cursor-not-allowed\n    [&_[data-slot=input]]:disabled:opacity-50    \n\n    [&_svg]:text-muted-foreground \n  `,\n  {\n    variants: {\n      variant: {\n        sm: 'gap-1.25 [&_svg:not([class*=size-])]:size-3.5',\n        md: 'gap-1.5 [&_svg:not([class*=size-])]:size-4',\n        lg: 'gap-1.5 [&_svg:not([class*=size-])]:size-4',\n      },\n    },\n    defaultVariants: {\n      variant: 'md',\n    },\n  },\n);\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground\",\n        \"flex w-full min-w-0 rounded border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none\",\n        \"file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium\",\n        \"disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50\",\n        \"md:text-sm\",\n        // Border and background colors\n        \"border-input dark:bg-input/30\",\n        // Focus states (theme-aware)\n        \"focus-visible:border-primary focus-visible:ring-primary/20 dark:focus-visible:ring-primary/40 focus-visible:ring-[3px]\",\n        // Invalid states\n        \"aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40\",\n        // Hover states (theme-aware)\n        \"hover:border-primary/50 dark:hover:border-primary/30\",\n        // Active states (theme-aware)\n        \"active:border-primary/70 dark:active:border-primary/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction InputAddon({\n  className,\n  variant,\n  mode,\n  ...props\n}: React.ComponentProps<'div'> & VariantProps<typeof inputAddonVariants>) {\n  return <div data-slot=\"input-addon\" className={cn(inputAddonVariants({ variant, mode }), className)} {...props} />;\n}\n\nfunction InputGroup({ className, ...props }: React.ComponentProps<'div'> & VariantProps<typeof inputGroupVariants>) {\n  return <div data-slot=\"input-group\" className={cn(inputGroupVariants(), className)} {...props} />;\n}\n\nfunction InputWrapper({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<'div'> & VariantProps<typeof inputWrapperVariants>) {\n  return (\n    <div\n      data-slot=\"input-wrapper\"\n      className={cn(inputVariants({ variant }), inputWrapperVariants({ variant }), className)}\n      {...props}\n    />\n  );\n}\n\nexport { Input, InputAddon, inputAddonVariants, InputGroup, inputVariants, InputWrapper };\n\n"], "names": ["Input", "className", "type", "props", "jsx", "cn"], "mappings": "sFAqHA,SAASA,EAAM,CAAE,UAAAC,EAAW,KAAAC,EAAM,GAAGC,GAAwC,CAEzE,OAAAC,EAAA,IAAC,QAAA,CACC,KAAAF,EACA,YAAU,QACV,UAAWG,EACT,gHACA,6HACA,4FACA,+EACA,aAEA,gCAEA,yHAEA,yGAEA,uDAEA,yDACAJ,CACF,EACC,GAAGE,CAAA,CACN,CAEJ"}